<?php

namespace pages\backoffice\reports\servicing\classes;

use models\cypher;
use models\Database2;
use models\lendingwise\db\tblFile2_db;
use models\lendingwise\db\tblFile_db;
use models\lendingwise\db\tblFileCalculatedValues_db;
use models\lendingwise\db\tblFileHMLOBusinessEntity_db;
use models\lendingwise\db\tblFileHMLONewLoanInfo_db;
use models\lendingwise\db\tblFileHMLOPropInfo_db;
use models\lendingwise\db\tblFileResponse_db;
use models\lendingwise\db\tblFileServicing_db;
use models\lendingwise\db\tblProcessingCompany_db;
use models\lendingwise\db\tblProperties_db;
use models\pops\exportClientFiles\DataExporter;
use models\standard\Dates;

class ServicingExporter extends DataExporter
{
    // require explicit properties on result set
    public static ?bool $enforceStrongType = true;

    public const COLUMN_FILE_ID = 'File ID';
    public const COLUMN_LMRRESPONSEID = tblFileResponse_db::COLUMN_LMRRESPONSEID;
    public const COLUMN_FBRID = tblFile_db::COLUMN_FBRID;
    public const COLUMN_LOAN_NUMBER = 'Loan Number';
    public const COLUMN_BORROWER_FIRST_NAME = 'Borrower First Name';
    public const COLUMN_BORROWER_LAST_NAME = 'Borrower Last Name';
    public const COLUMN_BORROWER_PHONE = 'Borrower Phone Number';
    public const COLUMN_BORROWER_CELL = 'Borrower Cell Number';
    public const COLUMN_BORROWER_FAX = 'Borrower Fax Number';
    public const COLUMN_BORROWER_EMAIL = 'Borrower Email';
    public const COLUMN_BORROWER_SECONDARY_EMAIL = 'Borrower Secondary Email';
    public const COLUMN_BORROWER_SSN = 'Client SSN Last 4';
    public const COLUMN_BORROWER_DOB = 'Borrower Date Of Birth';
    public const COLUMN_BORROWER_ADDRESS = 'Borrower Address';
    public const COLUMN_BORROWER_CITY = 'Borrower City';
    public const COLUMN_BORROWER_STATE = 'Borrower State';
    public const COLUMN_BORROWER_ZIP = 'Borrower Zip';
    public const COLUMN_BORROWER_MAILING_ADDRESS = 'Mailing Address';
    public const COLUMN_BORROWER_MAILING_CITY = 'Mailing City';
    public const COLUMN_BORROWER_MAILING_STATE = 'Mailing State';
    public const COLUMN_BORROWER_MAILING_ZIP = 'Mailing Zip';
    public const COLUMN_PROPERTY_OWNERSHIP = 'Property Ownership';
    public const COLUMN_PAYMENT_FREQUENCY = 'Payment Frequency';
    public const COLUMN_MONTHLY_PAYMENT = 'Monthly Payment';
    public const COLUMN_TOTAL_LOAN_PAYMENT = 'Total Loan Amount';
    public const COLUMN_PRINCIPAL_PAYMENT = 'Principal Payment';
    public const COLUMN_INTEREST_PAYMENT = 'Interest Payment';
    public const COLUMN_LOAN_TERM = 'Loan Term';
    public const COLUMN_CURRENT_LOAN_BALANCE = 'Current Loan Balance';
    public const COLUMN_CURRENT_ESCROW_BALANCE = 'Current Escrow Balance';
    public const COLUMN_PREPAID_INTEREST_RESERVE = 'Pre-paid Interest Reserve';
    public const COLUMN_TOTAL_DRAWS_FUNDED = 'Total Draws Funded';
    public const COLUMN_AMORTIZATION = 'Amortization';
    public const COLUMN_SERVICING_NUMBER = 'Servicing Number';
    public const COLUMN_PROPERTY_ADDRESS = 'Property Address';
    public const COLUMN_PROPERTY_CITY = 'Property City';
    public const COLUMN_PROPERTY_STATE = 'Property State';
    public const COLUMN_PROPERTY_ZIP = 'Property Zip';
    public const COLUMN_PROPERTY_COUNTY = 'Property County';
    public const COLUMN_PROPERTY_TYPE = 'Property Type';
    public const COLUMN_PROCESSING_COMPANY = 'Processing Company';
    public const COLUMN_INITIAL_LOAN_AMOUNT = 'Initial Loan Amount';
    public const COLUMN_IS_VALID_FOR_SERVICING = 'Is Valid for Servicing';
    public const COLUMN_NEXT_DUE_DATE = 'Next Due Date';
    public const COLUMN_LAST_PAYMENT_DATE = 'Last Payment Date';
    public const COLUMN_FLAGGED_FOR_ISSUES = tblFileServicing_db::COLUMN_FLAGFORISSUES;


    public ?string $Processing_Company = null;
    public ?string $File_ID = null;
    public ?string $Loan_Number = null;
    public ?string $FBRID = null;
    public ?string $LMRResponseId = null;
    public ?string $Borrower_First_Name = null;
    public ?string $Borrower_Last_Name = null;
    public ?string $Borrower_Phone_Number = null;
    public ?string $Borrower_Cell_Number = null;
    public ?string $Borrower_Fax_Number = null;
    public ?string $Borrower_Email = null;
    public ?string $Borrower_Secondary_Email = null;
    public ?string $Client_SSN_Last_4 = null;
    public ?string $Borrower_Date_Of_Birth = null;
    public ?string $Borrower_Address = null;
    public ?string $Borrower_City = null;
    public ?string $Borrower_State = null;
    public ?string $Borrower_Zip = null;
    public ?string $Mailing_Address = null;
    public ?string $Mailing_City = null;
    public ?string $Mailing_State = null;
    public ?string $Mailing_Zip = null;
    public ?string $Property_Ownership = null;
    public ?string $Payment_Frequency = null;
    public ?string $Monthly_Payment = null;
    public ?string $Total_Loan_Amount = null;
    public ?string $Principal_Payment = null;
    public ?string $Interest_Payment = null;
    public ?string $Loan_Term = null;
    public ?string $Current_Loan_Balance = null;
    public ?string $Current_Escrow_Balance = null;
    public ?string $Pre_paid_Interest_Reserve = null;
    public ?string $Total_Draws_Funded = null;
    public ?string $Amortization = null;
    public ?string $Servicing_Number = null;
    public ?string $Property_Address = null;
    public ?string $Property_City = null;
    public ?string $Property_State = null;
    public ?string $Property_Zip = null;
    public ?string $Property_County = null;
    public ?string $Property_Type = null;
    public ?float $Initial_Loan_Amount = null;
    public ?int $Is_Valid_for_Servicing = null;
    public ?string $Next_Due_Date = null;
    public ?string $Last_Payment_Date = null;
    public ?string $flagForIssues = null;

    public static array $headers = [

        'File ID' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_LMRID,
            'format' => Database2::DATATYPE_NUMBER,
        ],

        'Loan Number' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_LOANNUMBER,
            'format' => Database2::DATATYPE_NUMBER,
        ],

        tblFileServicing_db::COLUMN_FLAGFORISSUES => [
            'table'  => tblFileServicing_db::TABLE,
            'column' => tblFileServicing_db::COLUMN_FLAGFORISSUES,
            'format' => Database2::DATATYPE_NUMBER,
        ],

        tblFileResponse_db::COLUMN_LMRRESPONSEID => [
            'table'  => tblFileResponse_db::TABLE,
            'column' => tblFileResponse_db::COLUMN_LMRRESPONSEID, // 'trialPaymentDate1',
            'format' => Database2::DATATYPE_NUMBER,
        ],

        tblFile_db::COLUMN_FBRID => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_FBRID, // 'trialPaymentDate1',
            'format' => Database2::DATATYPE_NUMBER,
        ],

        'Processing Company' => [
            'table'  => tblProcessingCompany_db::TABLE,
            'column' => tblProcessingCompany_db::COLUMN_PROCESSINGCOMPANYNAME,
            'format' => Database2::DATATYPE_STRING
        ],

        'Borrower First Name' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_BORROWERNAME,
            'format' => Database2::DATATYPE_STRING
        ],

        'Borrower Last Name' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_BORROWERLNAME,
            'format' => Database2::DATATYPE_STRING
        ],

        'Borrower Phone Number' => [
            'table'  => tblFile_db::TABLE,
            'column' => 'phoneNumber AS BorrowerPhoneNumber', // 'phoneNumber AS BorrowerPhoneNumber',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Borrower Cell Number' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_CELLNUMBER,
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Borrower Fax Number' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_FAX,
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Borrower Email' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_BORROWEREMAIL,
            'format' => Database2::DATATYPE_EMAIL
        ],

        'Borrower Secondary Email' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_BORROWERSECONDARYEMAIL,
            'format' => Database2::DATATYPE_EMAIL
        ],

        'Client SSN Last 4' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_SSNNUMBER,
            'format' => Database2::DATATYPE_SSN4
        ],

        'Borrower Date Of Birth' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_BORROWERDOB,
            'format' => Database2::DATATYPE_DATE
        ],

        'Borrower Address' => [
            'table'  => tblFile2_db::TABLE,
            'column' => tblFile2_db::COLUMN_PRESENTADDRESS, // 'presentAddress',
            'format' => Database2::DATATYPE_STRING,
        ],

        'Borrower City' => [
            'table'  => tblFile2_db::TABLE,
            'column' => tblFile2_db::COLUMN_PRESENTCITY, // 'presentCity',
            'format' => Database2::DATATYPE_STRING,
        ],

        'Borrower State' => [
            'table'  => tblFile2_db::TABLE,
            'column' => tblFile2_db::COLUMN_PRESENTSTATE, // 'presentState',
            'format' => Database2::DATATYPE_STATE,
        ],

        'Borrower Zip' => [
            'table'  => tblFile2_db::TABLE,
            'column' => tblFile2_db::COLUMN_PRESENTZIP, // 'presentZip',
            'format' => Database2::DATATYPE_ZIP,
        ],

        'Mailing Address' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_MAILINGADDRESS, // 'mailingAddress',
            'format' => Database2::DATATYPE_STRING,
        ],

        'Mailing City' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_MAILINGCITY, // 'mailingCity',
            'format' => Database2::DATATYPE_STRING,
        ],

        'Mailing State' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_MAILINGSTATE, // 'mailingState',
            'format' => Database2::DATATYPE_STATE,
        ],

        'Mailing Zip' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_MAILINGZIP, // 'mailingZip',
            'format' => Database2::DATATYPE_ZIP,
        ],

        'Property Ownership' => [
            'table'  => tblFileHMLOBusinessEntity_db::TABLE,
            'column' => tblFileHMLOBusinessEntity_db::COLUMN_ENTITYPROPERTYOWNERSHIP, // 'entityPropertyOwnerShip',
            'format' => null
        ],

        'Payment Frequency' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_LOANTERMEXPIREDATE, // 'loanTermExpireDate',
            'format' => Database2::DATATYPE_DATE,
        ],

        'Monthly Payment' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_LIEN1PAYMENT, // 'lien1Payment',
            'format' => null
        ],

        'Is Valid for Servicing' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_ISVALIDFORSERVICING, // 'TotalLoanAmount',
            'format' => null
        ],

        'Next Due Date' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_PAYMENTNEXTDUEDATE, // 'TotalLoanAmount',
            'format' => null
        ],

        'Last Payment Date' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_PAYMENTLASTPAYMENTDATE, // 'TotalLoanAmount',
            'format' => null
        ],


        'Total Loan Amount' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_TOTALLOANAMOUNT, // 'TotalLoanAmount',
            'format' => Database2::DATATYPE_MONEY,
        ],

        'Principal Payment' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_PAYMENTPRINCIPAL,
            'format' => Database2::DATATYPE_MONEY,
        ],

        'Interest Payment' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_PAYMENTINTEREST,
            'format' => Database2::DATATYPE_MONEY,
        ],

        'Loan Term' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_LOANTERM, // 'loanTerm',
            'format' => null
        ],

        'Current Loan Balance' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_CURRENTLOANBALANCE, // 'currentLoanBalance',
            'format' => Database2::DATATYPE_MONEY,
        ],

        'Current Escrow Balance' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_CURRENTESCROWBALANCE, // 'CurrentEscrowBalance',
            'format' => Database2::DATATYPE_MONEY,
        ],

        'Pre-paid Interest Reserve' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_PREPAIDINTERESTRESERVE, // 'prepaidInterestReserve',
            'format' => Database2::DATATYPE_MONEY,
        ],

        'Total Draws Funded' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_TOTALDRAWSFUNDED, // 'TotalDrawsFunded',
            'format' => Database2::DATATYPE_MONEY,
        ],

        'Amortization' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_AMORTIZATIONTYPE, // 'amortizationType',
            'format' => null
        ],

        'Servicing Number' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_SERVICINGNUMBER, // 'servicingNumber',
            'format' => null
        ],

        'Property Address' => [
            'table'  => tblProperties_db::TABLE,
            'column' => tblProperties_db::COLUMN_PROPERTYADDRESS, // 'propertyAddress',
            'format' => null
        ],

        'Property City' => [
            'table'  => tblProperties_db::TABLE,
            'column' => tblProperties_db::COLUMN_PROPERTYCITY, // 'propertyCity',
            'format' => null
        ],

        'Property State' => [
            'table'  => tblProperties_db::TABLE,
            'column' => tblProperties_db::COLUMN_PROPERTYSTATE, // 'propertyState',
            'format' => null
        ],

        'Property Zip' => [
            'table'  => tblProperties_db::TABLE,
            'column' => tblProperties_db::COLUMN_PROPERTYZIPCODE, // 'propertyZipCode',
            'format' => null
        ],

        'Property County' => [
            'table'  => tblProperties_db::TABLE,
            'column' => tblProperties_db::COLUMN_PROPERTYCOUNTY, // 'propertyCounty',
            'format' => null
        ],

        'Property Type' => [
            'table'    => null,
            'column'   => 'propertyType', // 'propertyType',
            'function' => '_PropertyType',
            'format'   => null
        ],

        'Initial Loan Amount' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_INITIALLOANAMOUNT, // 'InitialLoanAmount',
            'format' => Database2::DATATYPE_MONEY
        ],
    ];

    public function getURL(): string
    {
//        /backoffice/LMRequest.php
//        eId - 5551 - FBRID
//        lId - 7463955 - LMRId
//        rId - 10028873 LMRResponseId
//        op - edit


        $eId = cypher::myEncryption($this->FBRID);
        $lId = cypher::myEncryption($this->File_ID);
        $rId = cypher::myEncryption($this->LMRResponseId);
        $op = cypher::myEncryption('edit');

        return '/backoffice/LMRequest.php?eId=' . $eId . '&lId=' . $lId . '&rId=' . $rId . '&op=' . $op . '&tabOpt=SER2';
    }

    public static function getStyle($header): string
    {
        $settings =  self::$headers[$header] ?? null;
        if(!$settings) {
            return '';
        }

        switch($settings['format']) {
            case Database2::DATATYPE_DATE:
            case Database2::DATATYPE_NUMBER:
            case Database2::DATATYPE_MONEY:
                return 'text-align: right;';
        }

        return 'text-align: left;';
    }

    public static function getFormat(?string $header, $value): ?string
    {
        $settings =  self::$headers[$header] ?? null;
        if(!$settings) {
            return $value;
        }

        switch($settings['format']) {
            case Database2::DATATYPE_DATE:
                return Dates::StandardDate($value, '');

            case Database2::DATATYPE_NUMBER:
                return number_format($value ?? 0);

            case Database2::DATATYPE_MONEY:
                return number_format($value ?? 0, 2);
        }

        return $value;
    }

    public function daysLate(): int
    {
        if(!$this->Next_Due_Date) {
            return 0;
        }

        $diff = Dates::DaysDiff($this->Next_Due_Date, time());

        return max($diff, 0);
    }
}