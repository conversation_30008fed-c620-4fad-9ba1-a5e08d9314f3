<?php

namespace pages\backoffice\api_v2\draw_management\loanFile;

use pages\backoffice\api_v2\draw_management\base\DrawManagementApiBase;
use models\composite\oDrawManagement\dto\request\SaveDrawRequestRequest;
use models\lendingwise\tblDrawRequests_h;
use models\lendingwise\db\tblDrawRequests_h_db;

/**
 * Class LoanFile
 *
 * API endpoint for updating and fetching draw request data
 *
 * @package pages\backoffice\api_v2\draw_management
 */
class LoanFile extends DrawManagementApiBase
{
    /**
     * Handle POST requests to update draw request data
     *
     * @return void
     */
    public static function Post(): void
    {
        parent::Init();

        $postData = static::parseJsonInput();
        if (!$postData) return;

        if (isset($postData['LMRId'])) {
            $postData['LMRId'] = static::getLMRId($postData['LMRId']);
        }

        if (isset($postData['historyData'])) {
            $historyData = $postData['historyData'];
            $historyId = $historyData['id'];
            unset($historyData['id']);
            static::updateDrawHistory($historyId, $historyData);
        }

        static::handlePostRequest(
            SaveDrawRequestRequest::class,
            $postData,
            function($dto) {
                $LMRId = $dto->LMRId;

                static::validateLoanFile($LMRId);
                $drawRequestManager = static::getDrawRequestManager($LMRId);
                $requestData = static::dtoToArray($dto);

                $result = $drawRequestManager->saveDrawRequestData($requestData);

                if (!$result) {
                    return ['success' => false, 'data' => null];
                }

                $updatedData = $drawRequestManager->getDrawRequestDataArray();
                return ['success' => true, 'data' => $updatedData];
            },
            'Draw request data saved successfully',
            'Failed to save draw request data'
        );
    }

    private static function updateDrawHistory(int $historyId, array $historyData): void
    {
        $history = tblDrawRequests_h::Get([
            tblDrawRequests_h_db::COLUMN_ID => $historyId,
            tblDrawRequests_h_db::COLUMN_ISDRAWREQUEST => 1
        ]);
        if (!$history) return;
        $history->rehabPercentFinanced = $historyData['rehabPercentFinanced'] ?? $history->rehabPercentFinanced;
        $history->drawFee = $historyData['drawFee'] ?? $history->drawFee;
        $history->save();
    }

}
