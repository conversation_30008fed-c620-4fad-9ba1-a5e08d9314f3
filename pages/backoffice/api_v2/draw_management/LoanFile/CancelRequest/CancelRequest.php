<?php

namespace pages\backoffice\api_v2\draw_management\LoanFile\CancelRequest;

use pages\backoffice\api_v2\draw_management\base\DrawManagementApiBase;

/**
 * API endpoint for canceling draw requests and SOW revision requests
 *
 * Handles POST requests to cancel either:
 * - SOW revision requests (type: "revision")
 * - Draw requests (type: "drawrequest")
 *
 * @package pages\backoffice\api_v2\draw_management\LoanFile\CancelRequest
 */
class CancelRequest extends DrawManagementApiBase
{
    /**
     * Handle POST requests to cancel draw requests or SOW revision requests
     *
     * Expected POST parameters:
     * - type: "revision" or "drawrequest"
     * - LMRId: The draw request ID (encrypted or numeric)
     *
     * @return void
     */
    public static function Post(): void
    {
        parent::Init();

        static::executeWithErrorHandling(function() {
            $postData = static::parseJsonInput();
            if (!$postData) return;

            $type = $postData['type'] ?? null;
            $LMRId = $postData['LMRId'] ?? null;

            if (!$type || !$LMRId) {
                static::sendErrorResponse('Missing required parameters: type and LMRId');
                return;
            }

            if (!in_array($type, ['revision', 'drawrequest'])) {
                static::sendErrorResponse('Invalid type parameter. Must be "revision" or "drawrequest"');
                return;
            }

            $LMRId = static::getLMRId($LMRId);
            if (!$LMRId) {
                static::sendErrorResponse('Invalid LMRId or draw request not found');
                return;
            }

            static::validateLoanFile($LMRId);
            $drawRequestManager = static::getDrawRequestManager($LMRId);

            $success = false;
            $message = '';

            if ($type === 'revision') {
                $success = $drawRequestManager->cancelSowRevisionRequest();
                $message = $success ? 'SOW revision request canceled successfully' : 'Failed to cancel SOW revision request';
            } else {
                $success = $drawRequestManager->cancelDrawRequest();
                $message = $success ? 'Draw request canceled successfully' : 'Failed to cancel draw request';
            }

            if ($success) {
                static::sendSuccessResponse([
                    'type' => $type,
                    'LMRId' => $LMRId
                ], $message);
            } else {
                static::sendErrorResponse($message);
            }
        });
    }
}
