<?php

namespace pages\backoffice\api_v2\draw_management\borrower\SowLineItems;

use pages\backoffice\api_v2\draw_management\base\DrawManagementApiBase;
use models\composite\oDrawManagement\dto\request\SaveLineItemsRequest;

/**
 * Class SowLineItems
 *
 * API endpoint for updating and fetching SOW line items
 *
 * @package pages\backoffice\api_v2\draw_management
 */
class SowLineItems extends DrawManagementApiBase
{
    /**
     * Handle GET requests to fetch SOW line items
     *
     * @return void
     */
    public static function Get(): void
    {
        parent::Init();

        $LMRId = static::getLMRId($_GET['LMRId'] ?? null);

        static::handleGetRequest(
            $LMRId,
            function($LMRId) {
                static::validateLoanFile($LMRId);
                $drawRequestManager = static::getDrawRequestManager($LMRId);
                return $drawRequestManager->getDrawRequestDataArray();
            },
            'Line items fetched successfully',
            'No line items found'
        );
    }

    /**
     * Handle POST requests to update SOW line items
     *
     * @return void
     */
    public static function Post(): void
    {
        parent::Init();

        $postData = static::parseJsonInput();
        if (!$postData) return;

        if (isset($postData['LMRId'])) {
            $postData['LMRId'] = static::getLMRId($postData['LMRId']);
        }

        static::handlePostRequest(
            SaveLineItemsRequest::class,
            $postData,
            function($dto) {
                $LMRId = $dto->LMRId;
                static::validateLoanFile($LMRId);
                $drawRequestManager = static::getDrawRequestManager($LMRId);

                $lineItemsData = [];
                foreach ($dto->lineItems as $categoryId => $categoryLineItems) {
                    $lineItemsData[$categoryId] = [];
                    foreach ($categoryLineItems as $lineItem) {
                        $lineItemsData[$categoryId][] = $lineItem->toArray();
                    }
                }

                $success = $drawRequestManager->saveLineItems($lineItemsData, $dto->isDraft);

                if (!$success) {
                    return ['success' => false, 'data' => null];
                }

                $updatedData = $drawRequestManager->getDrawRequestDataArray();
                return ['success' => true, 'data' => $updatedData];
            },
            'Line items saved successfully',
            'Failed to save line items'
        );
    }
}
