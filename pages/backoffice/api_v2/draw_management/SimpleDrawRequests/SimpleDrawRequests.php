<?php

namespace pages\backoffice\api_v2\draw_management\SimpleDrawRequests;

use models\lendingwise\tblFileSimpleDrawRequests;
use pages\backoffice\api_v2\draw_management\base\DrawManagementApiBase;
use models\composite\oDrawManagement\DrawSummaryCalculatedValues;
use models\Database2;

/**
 * Class SimpleDrawRequests
 *
 * API endpoint for managing simple draw requests
 *
 * @package pages\backoffice\api_v2\draw_management\SimpleDrawRequests
 */
class SimpleDrawRequests extends DrawManagementApiBase
{
    /**
     * Handle POST requests to save simple draw requests
     *
     * @return void
     */
    public static function Post(): void
    {
        parent::Init();

        $postData = static::parseJsonInput();
        if (!$postData) return;

        static::executeWithErrorHandling(function() use ($postData) {
            if (!isset($postData['LMRId']) || !isset($postData['drawRequests'])) {
                static::sendErrorResponse('Missing required fields: LMRId and drawRequests');
                return;
            }

            $LMRId = static::getLMRId($postData['LMRId']);
            $drawRequests = $postData['drawRequests'];
            $idsToDelete = $postData['idsToDelete'] ?? [];

            static::validateLoanFile($LMRId);

            if (!is_array($drawRequests)) {
                static::sendErrorResponse('Draw requests must be an array');
                return;
            }
            $conn = Database2::getInstance();
            $conn->beginTransaction();
            try {
                static::deleteDrawRequest($idsToDelete);

                $result = static::saveSimpleDrawRequests($LMRId, $drawRequests);
                $result2 = static::updateDrawSummaryCalculatedValues($LMRId);

                if ($result['success'] && $result2['success']) {
                    $conn->commit();
                    static::sendSuccessResponse($result['data'], 'Simple draw requests saved successfully');
                } else {
                    $conn->rollBack();
                    static::sendErrorResponse($result['message'] ?? 'Failed to save simple draw requests');
                }
            } catch (\Exception $e) {
                $conn->rollBack();
                static::sendErrorResponse('Failed to save simple draw requests: ' . $e->getMessage());
            }
        });
    }

    /**
     * Handle DELETE requests to remove simple draw requests
     *
     * @return void
     */
    private static function deleteDrawRequest(array $ids): void
    {
        foreach ($ids as $id) {
            $drawRequest = tblFileSimpleDrawRequests::Get(['id' => $id]);
            if ($drawRequest) $drawRequest->Delete();
        }
    }

    /**
     * Save simple draw requests to database
     *
     * @param int $LMRId
     * @param array $drawRequests
     * @return array
     */
    private static function saveSimpleDrawRequests(int $LMRId, array $drawRequests): array
    {
        try {
            $savedRequests = [];
            $errors = [];

            foreach ($drawRequests as $index => $request) {
                $result = static::saveIndividualDrawRequest($LMRId, $request);

                if ($result['success']) {
                    $savedRequests[] = $result['data'];
                } else {
                    $errors[] = "Row " . ($index + 1) . ": " . $result['message'];
                }
            }

            if (!empty($errors)) {
                return [
                    'success' => false,
                    'message' => 'Some requests failed to save: ' . implode(', ', $errors),
                    'data' => $savedRequests
                ];
            }

            return [
                'success' => true,
                'data' => $savedRequests
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Database error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Save individual draw request
     *
     * @param int $LMRId
     * @param array $request
     * @return array
     */
    private static function saveIndividualDrawRequest(int $LMRId, array $request): array
    {
        try {
            $requiredFields = ['status', 'submittedAt', 'requestedAmount'];
            foreach ($requiredFields as $field) {
                if (!isset($request[$field]) || $request[$field] === '') {
                    return [
                        'success' => false,
                        'message' => "Missing required field: {$field}"
                    ];
                }
            }

            if (!empty($request['id']) && $request['id'] !== 'new') {
                $drawRequest = tblFileSimpleDrawRequests::Get(['id' => (int)$request['id']]);
                if (!$drawRequest) {
                    return [
                        'success' => false,
                        'message' => 'Record not found for update'
                    ];
                }
            } else {
                $drawRequest = new tblFileSimpleDrawRequests();
                $drawRequest->LMRId = $LMRId;
            }

            $drawRequest->status = $request['status'];
            $drawRequest->submittedAt = static::formatDateTime($request['submittedAt']);
            $drawRequest->requestedAmount = (float)($request['requestedAmount'] ?? 0);
            $drawRequest->approvedAmount = (float)($request['approvedAmount'] ?? 0);
            $drawRequest->rehabPercentFinanced = (float)($request['rehabPercentFinanced'] ?? 0);
            $drawRequest->drawFee = (float)($request['drawFee'] ?? 0);
            $drawRequest->wireAmount = !empty($request['wireAmount']) ? (float)$request['wireAmount'] : null;
            $drawRequest->wireSentDate = !empty($request['wireSentDate']) ? static::formatDate($request['wireSentDate']) : null;

            $result = $drawRequest->Save();

            if (!$result['error']) {
                return [
                    'success' => true,
                    'data' => $drawRequest
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Database operation failed: ' . ($result['error_msg'] ?? 'Unknown error')
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error saving request: ' . $e->getMessage()
            ];
        }
    }

    private static function updateDrawSummaryCalculatedValues(int $LMRId): array
    {
        try {
            $drawSummaryCalculatedValues = new drawSummaryCalculatedValues($LMRId);
            $drawSummaryCalculatedValues->updateValues();
            return [
                'success' => true,
                'data' => $drawSummaryCalculatedValues
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error updating draw summary calculated values: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Format date string for database
     *
     * @param string $dateString
     * @return string|null
     */
    private static function formatDate(string $dateString): ?string
    {
        if (empty($dateString)) {
            return null;
        }

        try {
            $date = \DateTime::createFromFormat('m/d/Y', $dateString);
            if ($date) {
                return $date->format('Y-m-d');
            }

            $date = \DateTime::createFromFormat('Y-m-d', $dateString);
            if ($date) {
                return $date->format('Y-m-d');
            }

            return null;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Format datetime string for database
     *
     * @param string $dateTimeString
     * @return string|null
     */
    private static function formatDateTime(string $dateTimeString): ?string
    {
        if (empty($dateTimeString)) {
            return null;
        }

        try {
            $date = \DateTime::createFromFormat('m/d/Y', $dateTimeString);
            if ($date) {
                return $date->format('Y-m-d H:i:s');
            }

            $date = \DateTime::createFromFormat('Y-m-d H:i:s', $dateTimeString);
            if ($date) {
                return $date->format('Y-m-d H:i:s');
            }

            $date = \DateTime::createFromFormat('Y-m-d', $dateTimeString);
            if ($date) {
                return $date->format('Y-m-d 00:00:00');
            }

            return date('Y-m-d H:i:s');
        } catch (\Exception $e) {
            return date('Y-m-d H:i:s');
        }
    }
}
