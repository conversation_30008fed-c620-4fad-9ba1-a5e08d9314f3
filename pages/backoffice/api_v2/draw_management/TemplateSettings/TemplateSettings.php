<?php
namespace pages\backoffice\api_v2\draw_management\TemplateSettings;

use models\standard\HTTP;
use models\composite\oDrawManagement\SowTemplateManager;
use models\cypher;
use pages\backoffice\api_v2\draw_management\base\DrawManagementApiBase;

class TemplateSettings extends DrawManagementApiBase
{
    public static function Post(): void
    {
        try {
            parent::Init();
            $postData = static::parseJsonInput();
            $pcId = $postData['pcid'];
            if ($pcId && !is_numeric($pcId)) $pcId = cypher::decrypt($pcId);
            $pcId = (int)$pcId;

            $settingsData = [
                'allowBorrowersAddEditCategories' => $postData['allowBorrowersAddEditCategories'],
                'allowBorrowersDeleteCategories' => $postData['allowBorrowersDeleteCategories'],
                'allowBorrowersAddEditLineItems' => $postData['allowBorrowersAddEditLineItems'],
                'allowBorrowersDeleteLineItems' => $postData['allowBorrowersDeleteLineItems'],
                'allowBorrowersSOWRevisions' => $postData['allowBorrowersSOWRevisions'],
                'allowBorrowersExceedFinancedRehabCostOnRevision' => $postData['allowBorrowersExceedFinancedRehabCostOnRevision'],
                'allowUsersDeleteUploads' => $postData['allowUsersDeleteUploads'],
                'enableSimpleMode' => $postData['enableSimpleMode'],
                'drawFee' => $postData['drawFee']
            ];

            $sowTemplateManager = SowTemplateManager::forProcessingCompany($pcId);
            $sowTemplateManager->saveSettings($settingsData);

            // Return JSON response
            HTTP::ExitJSON(["success" => true, "message" => "Settings saved successfully."]);
        } catch (\Exception $e) {
            HTTP::ExitJSON(["success" => false, "message" => "An error occurred: " . $e->getMessage()]);
        }
    }
}
