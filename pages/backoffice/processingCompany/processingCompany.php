<?php

namespace pages\backoffice\processingCompany;

use models\cypher;
use models\lendingwise\db\tblProcessingCompany_db;
use models\lendingwise\tblProcessingCompany;
use models\PageVariables;
use models\portals\BackofficePage;
use models\Request;
use models\standard\HTTP;

class processingCompany extends BackofficePage
{
    public static function Post()
    {
        $pcid = PageVariables::$PCID ?: (Request::GetClean('PCID') ?? null);

        if (is_numeric($pcid) && !PageVariables::$PCID) {
            HTTP::ExitJSON(['error' => 'Invalid Request'], HTTP::HTTP_STATUS_FORBIDDEN);
        }

        if (is_string($pcid)) {
            $pcid = intval(cypher::myDecryption($pcid));
        }

        $table = tblProcessingCompany::Get([
            tblProcessingCompany_db::COLUMN_PCID => $pcid
        ]);

        if (!$table) {
            HTTP::ExitJSON(['error' => 'PC Not Found'], HTTP::HTTP_STATUS_FORBIDDEN);
        }
        HTTP::ExitJSON([
            'legalEntityIdentifier' => $table->legalEntityIdentifier
        ]);
    }
}