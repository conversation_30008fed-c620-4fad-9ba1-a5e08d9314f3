<?php
namespace pages\backoffice\settings\custom_fields\loan_tab;

use models\Controllers\backoffice\createProcessingCompany\quickAppCustomFormFieldsV2;
use models\Controllers\loanForm;
use models\CustomField;
use models\lendingwise\tblCustomFieldType;

?>
<form method="post" id="custom_field_form">
    <input type="hidden" name="a" value="edit_custom_field"/>
    <input type="hidden" name="id" value="<?php echo loan_tab::$selected->id; ?>"/>
    <input type="hidden" name="Section" value="<?php echo loan_tab::$sectionID; ?>"/>

    <div class="row">
        <div class="col-6">
            <label for="DisplayOrder">Display Order</label>
            <input type="number"
                   name="DisplayOrder"
                   id="DisplayOrder"
                   value="<?php echo loan_tab::$selected->DisplayOrder ?: ''; ?>"
                   class="form-control validate"/>

            <label for="Width">Width</label><small>(1-12, default 3)</small>
            <input type="number"
                   name="Width"
                   id="Width"
                   value="<?php echo loan_tab::$selected->Width ?: ''; ?>"
                   class="form-control validate"/>

            <label for="Label">Label</label>
            <input type="text"
                   maxlength="64"
                   name="Label"
                   id="Label"
                   value="<?php echo htmlentities(loan_tab::$selected->Label); ?>"
                   class="form-control validate"/>

            <label for="LabelWidth">Label Width (0-12, 0 means above field)</label>
            <input type="number"
                   name="LabelWidth"
                   id="LabelWidth"
                   value="<?php echo loan_tab::$selected->LabelWidth ?: 0; ?>"
                   class="form-control validate"/>

            <label for="tblCustomFieldTypeId">Field Type</label>
            <select name="tblCustomFieldTypeId" id="tblCustomFieldTypeId" class="form-control validate">
                <option value="">Select One...</option>
                <?php foreach (CustomField::$Types as $type) { ?>
                    <option value="<?php echo $type->id; ?>"
                        <?php if ($type->id == loan_tab::$selected->tblCustomFieldTypeId) echo 'selected="selected"'; ?>

                    ><?php echo $type->type; ?></option>
                <?php } ?>
            </select>

            <label for="ToolTip">Tool Tip</label>
            <input type="text"
                   maxlength="255"
                   name="ToolTip"
                   id="ToolTip"
                   value="<?php echo htmlentities(loan_tab::$selected->ToolTip); ?>"
                   class="form-control"/>

            <div id="OptionsDiv">
                <label for="Options">Multi Value Options (separate with semicolons)</label>
                <textarea
                        name="Options"
                        id="Options"
                        class="form-control"><?php $options = json_decode(loan_tab::$selected->Options);
                    echo is_array($options) ? implode('; ', $options) : ''; ?></textarea>
            </div>

            <div id="AddressDiv">
                <?php foreach (tblCustomFieldType::$AddressParts as $part) { ?>
                    <div>
                        <label for="address_<?php echo $part; ?>"><?php echo ucfirst($part); ?></label>
                        <?php echo loanForm::simpleSelect(
                            'address[' . $part . ']',
                            true,
                            0,
                            loan_tab::$selected->dataAddress()[$part] ?? '',
                            loan_tab::$address_options,
                            '',
                            '',
                            'Select One...',
                        ); ?>
                    </div>
                <?php } ?>
            </div>
        </div>
        <div class="col-3">
            <h4>File Types</h4>

            <label>
                <input type="checkbox" onchange="CheckAll(this,'file-type');"/>
                Toggle All
            </label>
            <hr/>
            <div style="max-height: 300px; overflow-y: scroll">
                <?php
                foreach (quickAppCustomFormFieldsV2::$fileTypes as $moduleCode => $module) {
                    if (!isset(quickAppCustomFormFieldsV2::$customModules[$moduleCode])
                        || (!$module->activeStatus && !in_array($moduleCode, loan_tab::$selected->_fileTypes()))) {
                        continue;
                    }
                    $label = $module->moduleName;
                    if (!$module->activeStatus) {
                        $label = '<i style="color: #600;" data-info="module not active">' . $label . ' (Inactive)</i>';
                    }

                    echo loanForm::simpleCheckbox(
                        'fileType[]',
                        'file-type',
                        '',
                        '',
                        in_array($moduleCode, loan_tab::$selected->_fileTypes()),
                        $moduleCode,
                        $label
                    ); ?><br/>
                <?php } ?>
            </div>
        </div>
        <div class="col-3">
            <h4>Loan Programs</h4>
            <label>
                <input type="checkbox" onchange="CheckAll(this,'loan-program');"/>
                Toggle All
            </label>
            <hr/>
            <div style="max-height: 300px; overflow-y: scroll">
                <?php
                foreach (quickAppCustomFormFieldsV2::$customModules as $moduleCode => $moduleInfo) {
                    if (!quickAppCustomFormFieldsV2::$fileTypes[$moduleCode]->activeStatus) {
                        continue;
                    }
                    ?>
                    <div class="loanProgramHeader"><?php echo quickAppCustomFormFieldsV2::$fileTypes[$moduleCode]->moduleName; ?></div>
                    <?php
                    $programs = quickAppCustomFormFieldsV2::$loanPrograms[$moduleCode];
                    foreach ($programs as $program) {
                        if ((!$program->activeStatus || !$program->PCActiveStatus || !$program->isAvailable)
                            && (!in_array($program->STCode, loan_tab::$selected->_loanPrograms()))) {
                            continue;
                        }
                        $label = $program->serviceType;
                        if (!$program->activeStatus) {
                            $label = '<span style="color: #600;" data-info="program not active">' . $label . ' (Inactive)</span>';
                        }
                        if (!$program->PCActiveStatus) {
                            $label = '<span style="color: #600;" data-info="program not active for PC">' . $label . ' (PC Inactive)</span>';
                        }
                        if ($program->internalLoanProgram) {
                            $label = '<span style="color: #060;" data-info="internal loan program">' . $label . ' (Internal)</span>';
                        }
                        if (!$program->isAvailable) {
                            $label = '<span style="color: #060;" data-info="unavailable loan program">' . $label . ' (Unavailable)</span>';
                        }
                        echo loanForm::simpleCheckbox(
                            'loanProgram[]',
                            'loan-program',
                            '',
                            '',
                            in_array($program->STCode, loan_tab::$selected->_loanPrograms()),
                            $program->STCode,
                            $label
                        ); ?><br/>
                    <?php } ?>
                    <?php //} ?>
                <?php } ?>
            </div>
        </div>
    </div>


    <div class="row">
        <table>
            <thead>
            <tr>
                <th colspan="6"></th>
                <th colspan="3" style="text-align: center;">Display</th>
                <th colspan="3" style="text-align: center;">Mandatory</th>
            </tr>
            <tr>
                <th><label for="isActive" style="padding: 1.0em;">Active</label></th>
                <th><label for="ShowOnExcel" style="padding: 1.0em;">Export to Excel</label></th>
                <th><label for="ShowOnMergeTag" style="padding: 1.0em;">Enable Merge Tag</label></th>
                <th><label for="displayFA" style="padding: 1.0em;">Full App</label></th>
                <th><label for="displayQA" style="padding: 1.0em;">Quick App</label></th>
                <th><label for="displayBO" style="padding: 1.0em;">Back Office</label></th>
                <th><label for="mandatoryFA" style="padding: 1.0em;">Full App</label></th>
                <th><label for="mandatoryQA" style="padding: 1.0em;">Quick App</label></th>
                <th><label for="mandatoryBO" style="padding: 1.0em;">Back Office</label></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><input type="checkbox"
                           name="isActive"
                           id="isActive"
                           value="1"
                        <?php if (loan_tab::$selected->isActive) echo 'checked="checked"'; ?>
                           class="form-control"/></td>              
                <td><input type="checkbox"
                           name="ShowOnExcel"
                           id="ShowOnExcel"
                           value="1"
                        <?php if (loan_tab::$selected->ShowOnExcel) echo 'checked="checked"'; ?>
                           class="form-control"/></td>
                <td><input type="checkbox"
                           name="ShowOnMergeTag"
                           id="ShowOnMergeTag"
                           value="1"
                        <?php if (loan_tab::$selected->ShowOnMergeTag) echo 'checked="checked"'; ?>
                           class="form-control"/></td>
                <td><input type="checkbox"
                           name="displayFA"
                           id="displayFA"
                           value="1"
                        <?php if (loan_tab::$selected->displayFA) echo 'checked="checked"'; ?>
                           class="form-control"/></td>
                <td><input type="checkbox"
                           name="displayQA"
                           id="displayQA"
                           value="1"
                        <?php if (loan_tab::$selected->displayQA) echo 'checked="checked"'; ?>
                           class="form-control"/></td>
                <td><input type="checkbox"
                           name="displayBO"
                           id="displayBO"
                           value="1"
                        <?php if (loan_tab::$selected->displayBO) echo 'checked="checked"'; ?>
                           class="form-control"/></td>

                <td><input type="checkbox"
                           name="mandatoryFA"
                           id="mandatoryFA"
                           value="1"
                        <?php if (loan_tab::$selected->mandatoryFA) echo 'checked="checked"'; ?>
                           class="form-control"/></td>
                <td><input type="checkbox"
                           name="mandatoryQA"
                           id="mandatoryQA"
                           value="1"
                        <?php if (loan_tab::$selected->mandatoryQA) echo 'checked="checked"'; ?>
                           class="form-control"/></td>
                <td><input type="checkbox"
                           name="mandatoryBO"
                           id="mandatoryBO"
                           value="1"
                        <?php if (loan_tab::$selected->mandatoryBO) echo 'checked="checked"'; ?>
                           class="form-control"/></td>
            </tr>
            </tbody>
        </table>
    </div>

    <input type="submit" class="btn btn-primary" value="Save"/>
</form>
