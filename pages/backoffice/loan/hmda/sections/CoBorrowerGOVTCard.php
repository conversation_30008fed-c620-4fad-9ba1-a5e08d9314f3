<?php

namespace pages\backoffice\loan\hmda;

use models\constants\EthnicityRaceGenderVeteran;
use models\standard\Arrays;
use models\standard\Strings;
use pages\backoffice\loan\hmda\classes\HMDAController;

?>

<div class="card card-custom CoBorrowerGOVTCard">
    <!-- Deal Analysis Calculator Start -->
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label">
                CoBorrower
            </h3>
        </div>
        <div class="card-toolbar">
            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none"
               data-card-tool="toggle"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </a>
            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none"
               data-card-tool="reload"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </a>
            <a href="javascript:void(0);"
               class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
               data-card-tool="toggle"
               data-section="CoBorrowerGOVTCard"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </a>
        </div>
    </div>
    <div class="card-body CoBorrowerGOVTCard_body">
        <div class="row">
            <div class="col-lg-12">
                <div class="form-group row">
                    <label class="font-weight-bold col-md-5" for="coBorrowerRaceOfApplicant">
                        Race of Co-Applicant or Co-Borrower - American Indian or Alaska Native Enrolled or
                        Principal Tribe
                    </label>
                    <div class="col-md-7">
                        <input type="text"
                               class="form-control input-sm"
                               name="coBorrowerRaceOfApplicant"
                               id="coBorrowerRaceOfApplicant"
                               value="<?php echo htmlentities(HMDAController::$coBorrowerRaceOfApplicant); ?>">
                    </div>
                </div>
                <div class="form-group row">
                    <label class="font-weight-bold col-md-5" for="coBorrowerAgeOfApplicant">
                        Age of Co-Applicant or Co-Borrower
                    </label>
                    <div class="col-md-7">
                        <input type="text"
                               class="form-control input-sm"
                               name="coBorrowerAgeOfApplicant"
                               id="coBorrowerAgeOfApplicant"
                               value="<?php echo HMDAController::$coBorrowerAgeOfApplicant; ?>">
                    </div>
                </div>
                <div class="form-group row">
                    <label class="font-weight-bold col-md-5" for="coBorrowerCreditScoreOfApplicant">
                        Credit Score of Co-Applicant or Co-Borrower
                    </label>
                    <div class="col-md-7">
                        <input type="text"
                               class="form-control input-sm"
                               name="coBorrowerCreditScoreOfApplicant"
                               id="coBorrowerCreditScoreOfApplicant"
                               value="<?php echo HMDAController::$coBorrowerCreditScoreOfApplicant; ?>">
                    </div>
                </div>
                <div class="form-group row">
                    <label class="font-weight-bold col-md-5" for="coBorrowerCreditScoringModelOfApplicant">
                        Co-Applicant or Co-Borrower, Name and Version of Credit Scoring Model
                    </label>
                    <div class="col-md-7">
                        <select class="form-control input-sm creditScoringModel"
                                name="coBorrowerCreditScoringModelOfApplicant"
                                id="coBorrowerCreditScoringModelOfApplicant">
                            <option value="">-Select-</option>
                            <?php foreach (HMDAController::$creditScoringModelArray as $coBoCreditScoringModelKey => $coBoCreditScoringModelValue) { ?>
                                <option value="<?php echo $coBoCreditScoringModelKey; ?>" <?php echo Arrays::isSelected($coBoCreditScoringModelKey, HMDAController::$coBorrowerCreditScoringModelOfApplicant); ?> ><?php echo $coBoCreditScoringModelValue; ?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
                <div class="form-group row <?php if (HMDAController::$coBorrowerCreditScoringModelOfApplicant != 8) {
                    echo 'hide';
                } ?> " id="coBorrowerCreditScoringModelOfApplicantOtherDiv">
                    <label class="font-weight-bold col-md-5"
                           for="coBorrowerCreditScoringModelOfApplicantOther">
                        Other
                    </label>
                    <div class="col-md-7">
                        <input type="text" class="form-control input-sm"
                               name="coBorrowerCreditScoringModelOfApplicantOther"
                               id="coBorrowerCreditScoringModelOfApplicantOther"
                               value="<?php echo HMDAController::$coBorrowerCreditScoringModelOfApplicantOther; ?>"
                        >
                    </div>
                </div>
                <div class="form-group row">
                    <label class="font-weight-bold col-md-5"
                           for="coBorrowerCreditScoringModelConditionalFreeOfApplicant">
                        Co-Applicant or Co-Borrower, Name and Version of Credit Scoring Model: Conditional
                        Free Form Text Field For Code 8
                    </label>
                    <div class="col-md-7">
                        <input
                            type="text"
                            class="form-control input-sm"
                            name="coBorrowerCreditScoringModelConditionalFreeOfApplicant"
                            id="coBorrowerCreditScoringModelConditionalFreeOfApplicant"
                            value="<?php echo htmlentities(HMDAController::$coBorrowerCreditScoringModelConditionalFreeOfApplicant); ?>">
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <table width="100%"
                   class="table table-hover  table-condensed table-sm table-vertical-center">
                <tr>
                    <td class="font-weight-bold">Do you wish to furnish this information?</td>
                    <td>
                        <?php
                        if (hmda::$allowToEdit) {
                            ?>
                            <div class="radio-inline">
                                <label class="radio radio-solid font-weight-bold" for="CoBrYes">
                                    <input type="radio" name="PublishCBInfo" value="2" class="CoBrYes"
                                           id="CoBrYes"
                                           tabindex="<?php echo HMDAController::$tabIndexNo++; ?>" <?php echo Strings::isChecked('2', HMDAController::$PublishCBInfo); ?>
                                           onclick="showAndHideQADivCB(this.value, 'coBorrower');"><span></span>
                                    Yes
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="CoBrNo">
                                    <input type="radio" name="PublishCBInfo" value="1" id="CoBrNo"
                                           tabindex="<?php echo HMDAController::$tabIndexNo++; ?>" <?php echo Strings::isChecked('1', HMDAController::$PublishCBInfo); ?>
                                           onclick="showAndHideQADivCB(this.value, 'coBorrower');"><span></span>
                                    No
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="CoBrNo3">
                                    <input type="radio" name="PublishCBInfo" value="3" id="CoBrNo3"
                                           tabindex="<?php echo HMDAController::$tabIndexNo++; ?>" <?php echo Strings::isChecked('3', HMDAController::$PublishCBInfo); ?>
                                           onclick="showAndHideQADivCB(this.value, 'coBorrower');"><span></span>
                                    N/A
                                </label>
                            </div>
                            <?php
                        } else {
                            if (HMDAController::$PublishCBInfo == 2) {
                                echo '<h5>Yes</h5>';
                            } elseif (HMDAController::$PublishCBInfo == 1) {
                                echo '<h5>No</h5>';
                            } else {
                                echo '<h5>N/A</h5>';
                            }
                        }
                        ?>
                    </td>
                </tr>
                <tr class="even">
                    <td class="font-weight-bold" width="50%">What is your Ethnicity:</td>
                    <td width="50%">
                        <?php
                        if (hmda::$allowToEdit) { ?>
                            <div class="radio-list">
                                <label class="radio radio-solid font-weight-bold" for="CBEthnicity2">
                                    <input type="radio" name="CBEthnicity" id="CBEthnicity2"
                                           value="2" <?php echo Strings::isChecked('2', HMDAController::$CBEthnicity); ?>
                                           tabindex="<?php echo HMDAController::$tabIndexNo++; ?>"
                                           class="CoBorChildRadio CBHispanic coBorrower"><span></span>
                                    <?php echo EthnicityRaceGenderVeteran::$Ethnicity[2]; ?>
                                </label>
                                <div class="col-md-12 px-4 <?php if (HMDAController::$CBEthnicity != '2') {
                                    echo 'hidden';
                                } ?>" id="CBHispanic">
                                    <div class="radio-list">
                                        <label class="radio radio-solid font-weight-bold"
                                               for="CBEthnicitySub1">
                                            <input type="radio" name="CBEthnicitySub" id="CBEthnicitySub1"
                                                   class="CoBorChildRadio CBHispanicPrintOrigin"
                                                   value="1" <?php echo Strings::isChecked('1', HMDAController::$CBEthnicitySub); ?> >
                                            <span></span>
                                            <?php echo EthnicityRaceGenderVeteran::$EthnicitySub[1]; ?>
                                        </label>
                                        <label class="radio radio-solid font-weight-bold"
                                               for="CBEthnicitySub2">
                                            <input type="radio" name="CBEthnicitySub" id="CBEthnicitySub2"
                                                   class="CoBorChildRadio CBHispanicPrintOrigin"
                                                   value="2" <?php echo Strings::isChecked('2', HMDAController::$CBEthnicitySub); ?> >
                                            <span></span>
                                            <?php echo EthnicityRaceGenderVeteran::$EthnicitySub[2]; ?>
                                        </label>
                                        <label class="radio radio-solid font-weight-bold"
                                               for="CBEthnicitySub3">
                                            <input type="radio" name="CBEthnicitySub" id="CBEthnicitySub3"
                                                   class="CoBorChildRadio CBHispanicPrintOrigin"
                                                   value="3" <?php echo Strings::isChecked('3', HMDAController::$CBEthnicitySub); ?> ><span></span>
                                            <?php echo EthnicityRaceGenderVeteran::$EthnicitySub[3]; ?>
                                        </label>
                                        <label class="radio radio-solid font-weight-bold"
                                               for="CBEthnicitySub4">
                                            <input type="radio" name="CBEthnicitySub" id="CBEthnicitySub4"
                                                   class="CoBorChildRadio CBHispanicPrintOrigin"
                                                   value="4" <?php echo Strings::isChecked('4', HMDAController::$CBEthnicitySub); ?> >
                                            <span></span>
                                            <?php echo EthnicityRaceGenderVeteran::$EthnicitySub[4]; ?>
                                        </label>
                                        <div class="col-md-12  <?php if (HMDAController::$CBEthnicitySub != 4) echo 'hidden'; ?> "
                                             id="CBHispanicPrintOriginDiv">
                                            <label class="col-md-6" for="CBEthnicitySubOtherTxt">
                                                Print Origin</label>
                                            <div class="col-md-6"><input type="text"
                                                                         autocomplete="off"
                                                                         name="CBEthnicitySubOther"
                                                                         id="CBEthnicitySubOtherTxt"
                                                                         class="form-control input-sm"
                                                                         value="<?php echo htmlspecialchars(HMDAController::$CBEthnicitySubOther); ?>">
                                                <span class="text-muted">
                                                                For example: Argentinean, Colombian, Dominican, Nicaraguan, Salvadoran, Spaniard, and so on</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <label class="radio radio-solid font-weight-bold" for="CBEthnicity1">
                                    <input type="radio" name="CBEthnicity" id="CBEthnicity1"
                                           value="1" <?php echo Strings::isChecked('1', HMDAController::$CBEthnicity); ?>
                                           tabindex="<?php echo HMDAController::$tabIndexNo++; ?>"
                                           class="CoBorChildRadio CBHispanic coBorrower"><span></span>
                                    <?php echo EthnicityRaceGenderVeteran::$Ethnicity[1]; ?>
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="CBEthnicity3">
                                    <input type="radio" name="CBEthnicity" id="CBEthnicity3"
                                           value="3" <?php echo Strings::isChecked('3', HMDAController::$CBEthnicity); ?>
                                           tabindex="<?php echo HMDAController::$tabIndexNo++; ?>"
                                           class="CoBorChildRadio CBHispanic coBorrower"><span></span>
                                    <?php echo EthnicityRaceGenderVeteran::$Ethnicity[3]; ?>
                                </label>
                            </div>
                        <?php } else { ?>
                            <input type="radio" name="CBEthnicity" disabled
                                   value="2" <?php echo Strings::isChecked('2', HMDAController::$CBEthnicity); ?>
                                   tabindex="<?php echo HMDAController::$tabIndexNo++; ?>">
                            <?php echo EthnicityRaceGenderVeteran::$Ethnicity[2]; ?>
                            <br>
                            <div class="col-md-12 <?php if (HMDAController::$CBEthnicity != '2') {
                                echo 'hidden';
                            } ?>" id="CBHispanic">
                                <input type="radio" name="CBEthnicitySub" id="CBEthnicitySub" class=""
                                       disabled
                                       value="1" <?php echo Strings::isChecked('1', HMDAController::$CBEthnicitySub); ?> >
                                <?php echo EthnicityRaceGenderVeteran::$EthnicitySub[1]; ?>
                                <br>
                                <input type="radio" name="CBEthnicitySub" id="CBEthnicitySub" class=""
                                       disabled
                                       value="2" <?php echo Strings::isChecked('2', HMDAController::$CBEthnicitySub); ?> >
                                <?php echo EthnicityRaceGenderVeteran::$EthnicitySub[2]; ?>
                                <br>
                                <input type="radio" name="CBEthnicitySub" id="CBEthnicitySub" class=""
                                       disabled
                                       value="3" <?php echo Strings::isChecked('3', HMDAController::$CBEthnicitySub); ?> >
                                <?php echo EthnicityRaceGenderVeteran::$EthnicitySub[3]; ?>
                                <br>
                                <input type="radio" name="CBEthnicitySub" id="CBEthnicitySub" class=""
                                       disabled
                                       value="4" <?php echo Strings::isChecked('4', HMDAController::$CBEthnicitySub); ?> >
                                <?php echo EthnicityRaceGenderVeteran::$EthnicitySub[4]; ?>
                                <div class="col-md-12 no-padding <?php if (HMDAController::$CBEthnicitySub != 4) echo 'hidden'; ?> "
                                     id="CBHispanicPrintOriginDiv">
                                    <div class="col-md-12">
                                        Print Origin
                                        <input type="text" name="CBEthnicitySubOther"
                                               id="CBEthnicitySubOther" autocomplete="off"
                                               class="form-control input-sm" disabled
                                               value="<?php echo htmlspecialchars(HMDAController::$CBEthnicitySubOther); ?>">
                                        <i>For example: Argentinean, Colombian, Dominican, Nicaraguan,
                                            Salvadoran, Spaniard, and so on</i>
                                    </div>
                                </div>
                            </div>
                            <input type="radio" name="CBEthnicity" disabled
                                   value="1" <?php echo Strings::isChecked('1', HMDAController::$CBEthnicity); ?>
                                   tabindex="<?php echo HMDAController::$tabIndexNo++; ?>"><?php echo EthnicityRaceGenderVeteran::$Ethnicity[1]; ?>
                            <br>
                            <input type="radio" name="CBEthnicity" disabled
                                   value="3" <?php echo Strings::isChecked('3', HMDAController::$CBEthnicity); ?>
                                   tabindex="<?php echo HMDAController::$tabIndexNo++; ?>"><?php echo EthnicityRaceGenderVeteran::$Ethnicity[3]; ?>
                            <?php
                        }
                        ?>
                    </td>
                </tr>
                <tr>
                    <td class="font-weight-bold"> What is your Race:</td>
                    <td>
                        <?php
                        if (hmda::$allowToEdit) { ?>
                            <div class="radio-list">
                                <label class="radio radio-solid font-weight-bold" for="CBRace1">
                                    <input type="radio" name="CBRace" id="CBRace1"
                                           value="1" <?php echo Strings::isChecked(HMDAController::$CBRace, '1'); ?>
                                           tabindex="<?php echo HMDAController::$tabIndexNo++; ?>"
                                           class="CoBorChildRadio CBRace coBorrower"><span></span>
                                    <?php echo EthnicityRaceGenderVeteran::$Race[1]; ?>
                                </label>
                                <div class="col-md-12 no-padding <?php if (HMDAController::$CBRace != '1') {
                                    echo 'hide';
                                } ?> " id="coBorEnrolledPrincipalTribeDiv">
                                    <div class="col-md-12">
                                        <label for="coBorEnrolledPrincipalTribe">
                                            Enrolled or Principal Tribe
                                        </label>
                                        <input type="text"
                                               class="form-control input-sm"
                                               name="coBorEnrolledPrincipalTribe"
                                               id="coBorEnrolledPrincipalTribe"
                                               value="<?php echo htmlentities(HMDAController::$coBorEnrolledPrincipalTribe); ?>">
                                    </div>
                                </div>
                                <label class="radio radio-solid font-weight-bold" for="CBRace2">
                                    <input type="radio" name="CBRace" id="CBRace2"
                                           value="2" <?php echo Strings::isChecked(HMDAController::$CBRace, '2'); ?>
                                           tabindex="<?php echo HMDAController::$tabIndexNo++; ?>"
                                           class="CoBorChildRadio CBRace coBorrower"><span></span>
                                    <?php echo EthnicityRaceGenderVeteran::$Race[2]; ?>
                                </label>
                                <div class="col-md-12 px-4 <?php if (HMDAController::$CBRace != '2') {
                                    echo 'hidden';
                                } ?>" id="CBAsian">
                                    <div class="radio-list">
                                        <label class="radio radio-solid font-weight-bold" for="CBRaceSub1">
                                            <input type="radio" name="CBRaceSub" id="CBRaceSub1"
                                                   class="CoBorChildRadio CBAsian"
                                                   value="1" <?php echo Strings::isChecked('1', HMDAController::$CBRaceSub); ?> ><span></span>
                                            <?php echo EthnicityRaceGenderVeteran::$RaceSub[1]; ?>

                                        </label>
                                        <label class="radio radio-solid font-weight-bold" for="CBRaceSub2">
                                            <input type="radio" name="CBRaceSub" id="CBRaceSub2"
                                                   class="CoBorChildRadio CBAsian"
                                                   value="2" <?php echo Strings::isChecked('2', HMDAController::$CBRaceSub); ?> ><span></span>

                                            <?php echo EthnicityRaceGenderVeteran::$RaceSub[2]; ?>
                                        </label>
                                        <label class="radio radio-solid font-weight-bold" for="CBRaceSub3">
                                            <input type="radio" name="CBRaceSub" id="CBRaceSub3"
                                                   class="CoBorChildRadio CBAsian"
                                                   value="3" <?php echo Strings::isChecked('3', HMDAController::$CBRaceSub); ?> >
                                            <span></span>
                                            <?php echo EthnicityRaceGenderVeteran::$RaceSub[3]; ?>

                                        </label>
                                        <label class="radio radio-solid font-weight-bold" for="CBRaceSub4">
                                            <input type="radio" name="CBRaceSub" id="CBRaceSub4"
                                                   class="CoBorChildRadio CBAsian"
                                                   value="4" <?php echo Strings::isChecked('4', HMDAController::$CBRaceSub); ?> ><span></span>
                                            <?php echo EthnicityRaceGenderVeteran::$RaceSub[4]; ?>
                                        </label>
                                        <label class="radio radio-solid font-weight-bold" for="CBRaceSub5">
                                            <input type="radio" name="CBRaceSub" id="CBRaceSub5"
                                                   class="CoBorChildRadio CBAsian"
                                                   value="5" <?php echo Strings::isChecked('5', HMDAController::$CBRaceSub); ?> ><span></span>
                                            <?php echo EthnicityRaceGenderVeteran::$RaceSub[5]; ?>

                                        </label>
                                        <label class="radio radio-solid font-weight-bold" for="CBRaceSub6">
                                            <input type="radio" name="CBRaceSub" id="CBRaceSub6"
                                                   class="CoBorChildRadio CBAsian"
                                                   value="6" <?php echo Strings::isChecked('6', HMDAController::$CBRaceSub); ?> >
                                            <span></span>
                                            <?php echo EthnicityRaceGenderVeteran::$RaceSub[6]; ?>

                                        </label>
                                        <label class="radio radio-solid font-weight-bold" for="CBRaceSub7">
                                            <input type="radio" name="CBRaceSub" id="CBRaceSub7"
                                                   class="CoBorChildRadio CBAsian"
                                                   value="7" <?php echo Strings::isChecked('7', HMDAController::$CBRaceSub); ?> ><span></span>
                                            <?php echo EthnicityRaceGenderVeteran::$RaceSub[7]; ?>

                                        </label>
                                        <div class="col-md-12 px-4 <?php if (HMDAController::$CBRaceSub != 7) echo 'hidden'; ?>"
                                             id="CBAsianDiv">
                                            <label class="col-md-6 font-weight-bold">
                                                Print Race
                                            </label>
                                            <span class="col-md-6"><input type="text"
                                                                          class="form-control input-sm"
                                                                          name="CBRaceAsianOther"
                                                                          id="CBRaceAsianOther"
                                                                          value="<?php echo htmlspecialchars(HMDAController::$CBRaceAsianOther); ?>">
                                                        <span class="text-muted">
                                                            For example: Hmong, Laotian, Thai, Pakistani, Cambodian, and so on.</span>
                                                    </span>

                                        </div>
                                    </div>
                                </div>
                                <label class="radio radio-solid font-weight-bold" for="CBRace3">
                                    <input type="radio" name="CBRace" id="CBRace3"
                                           value="3" <?php echo Strings::isChecked(HMDAController::$CBRace, '3'); ?>
                                           tabindex="<?php echo HMDAController::$tabIndexNo++; ?>"
                                           class="CoBorChildRadio CBRace coBorrower"><span></span>
                                    <?php echo EthnicityRaceGenderVeteran::$Race[3]; ?>
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="CBRace4">
                                    <input type="radio" name="CBRace" id="CBRace4"
                                           value="4" <?php echo Strings::isChecked(HMDAController::$CBRace, '4'); ?>
                                           tabindex="<?php echo HMDAController::$tabIndexNo++; ?>"
                                           class="CoBorChildRadio CBRace coBorrower"><span></span>
                                    <?php echo EthnicityRaceGenderVeteran::$Race[4]; ?>

                                </label>
                                <div class="col-md-12 px-4 <?php if (HMDAController::$CBRace != '4') {
                                    echo 'hidden';
                                } ?>" id="CBNative">
                                    <div class="radio-list">
                                        <label class="radio radio-solid font-weight-bold" for="CBRaceSub8">
                                            <input type="radio" name="CBRaceSub" id="CBRaceSub8"
                                                   class="CoBorChildRadio CBAsian"
                                                   value="8" <?php echo Strings::isChecked('8', HMDAController::$CBRaceSub); ?> ><span></span>
                                            <?php echo EthnicityRaceGenderVeteran::$RaceSub[8]; ?>

                                        </label>
                                        <label class="radio radio-solid font-weight-bold" for="CBRaceSub9">
                                            <input type="radio" name="CBRaceSub" id="CBRaceSub9"
                                                   class="CoBorChildRadio CBAsian"
                                                   value="9" <?php echo Strings::isChecked('9', HMDAController::$CBRaceSub); ?> >
                                            <span></span>
                                            <?php echo EthnicityRaceGenderVeteran::$RaceSub[9]; ?>

                                        </label>
                                        <label class="radio radio-solid font-weight-bold" for="CBRaceSub10">
                                            <input type="radio" name="CBRaceSub" id="CBRaceSub10"
                                                   class="CoBorChildRadio CBAsian"
                                                   value="10" <?php echo Strings::isChecked('10', HMDAController::$CBRaceSub); ?> ><span></span>
                                            <?php echo EthnicityRaceGenderVeteran::$RaceSub[10]; ?>

                                        </label>
                                        <label class="radio radio-solid font-weight-bold" for="CBRaceSub11">
                                            <input type="radio" name="CBRaceSub" id="CBRaceSub11"
                                                   class="CoBorChildRadio CBAsian"
                                                   value="11" <?php echo Strings::isChecked('11', HMDAController::$CBRaceSub); ?> ><span></span>
                                            <?php echo EthnicityRaceGenderVeteran::$RaceSub[11]; ?>

                                        </label>
                                        <div class="col-md-12 no-padding <?php if (HMDAController::$CBRaceSub != 11) echo 'hidden'; ?>"
                                             id="CBPacificDiv">
                                            <label class="col-md-6 font-weight-bold">
                                                Print Race</label>
                                            <span class="col-md-6">    <input type="text"
                                                                              autocomplete="off"
                                                                              class="form-control input-sm"
                                                                              name="CBRacePacificOther"
                                                                              id="CBRacePacificOther"
                                                                              value="<?php echo htmlspecialchars(HMDAController::$CBRacePacificOther); ?>">
                                                            <span class="text-muted">For example: Fijian, Tongan, and so on.</span>
                                                        </span>
                                        </div>
                                    </div>
                                </div>
                                <label class="radio radio-solid font-weight-bold" for="CBRace5">
                                    <input type="radio" name="CBRace" id="CBRace5"
                                           value="5" <?php echo Strings::isChecked(HMDAController::$CBRace, '5'); ?>
                                           tabindex="<?php echo HMDAController::$tabIndexNo++; ?>"
                                           class="CoBorChildRadio CBRace coBorrower"><span></span>
                                    <?php echo EthnicityRaceGenderVeteran::$Race[5]; ?>
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="CBRace6">
                                    <input type="radio" name="CBRace" id="CBRace6"
                                           value="6" <?php echo Strings::isChecked(HMDAController::$CBRace, '6'); ?>
                                           tabindex="<?php echo HMDAController::$tabIndexNo++; ?>"
                                           class="CoBorChildRadio CBRace coBorrower"><span></span>
                                    <?php echo EthnicityRaceGenderVeteran::$Race[6]; ?>

                                </label>
                            </div>
                        <?php } else { ?>
                            <input type="radio" name="CBRace" value="1"
                                   disabled <?php echo Strings::isChecked(HMDAController::$CBRace, '1'); ?>
                                   tabindex="<?php echo HMDAController::$tabIndexNo++; ?>">&nbsp;

                            <?php echo EthnicityRaceGenderVeteran::$Race[1]; ?>
                            <br>
                            <?php if (HMDAController::$CBRace) { ?>
                                <div class="col-md-12 no-padding" id="coBorEnrolledPrincipalTribeDiv">
                                    <div class="col-md-12">
                                        <label for="coBorEnrolledPrincipalTribe">
                                            Enrolled or Principal Tribe
                                        </label>
                                        <input type="text" readonly
                                               class="form-control input-sm"
                                               name="coBorEnrolledPrincipalTribe"
                                               id="coBorEnrolledPrincipalTribe"
                                               value="<?php echo HMDAController::$coBorEnrolledPrincipalTribe; ?>">
                                    </div>
                                </div>
                            <?php } ?>
                            <input type="radio" name="CBRace" value="2"
                                   disabled <?php echo Strings::isChecked(HMDAController::$CBRace, '2'); ?>
                                   tabindex="<?php echo HMDAController::$tabIndexNo++; ?>">&nbsp;Asian<br>
                            <div class="col-md-12 <?php if (HMDAController::$CBRace != '2') {
                                echo 'hidden';
                            } ?>" id="CBAsian">
                                <input type="radio" name="CBRaceSub" id="CBRaceSub" class="" disabled
                                       value="1" <?php echo Strings::isChecked('1', HMDAController::$CBRaceSub); ?> >
                                <?php echo EthnicityRaceGenderVeteran::$RaceSub[1]; ?>
                                <br>
                                <input type="radio" name="CBRaceSub" id="CBRaceSub" class="" disabled
                                       value="2" <?php echo Strings::isChecked('2', HMDAController::$CBRaceSub); ?> >
                                <?php echo EthnicityRaceGenderVeteran::$RaceSub[2]; ?>
                                <br>
                                <input type="radio" name="CBRaceSub" id="CBRaceSub" class="" disabled
                                       value="3" <?php echo Strings::isChecked('3', HMDAController::$CBRaceSub); ?> >
                                <?php echo EthnicityRaceGenderVeteran::$RaceSub[3]; ?>
                                <br>
                                <input type="radio" name="CBRaceSub" id="CBRaceSub" class="" disabled
                                       value="4" <?php echo Strings::isChecked('4', HMDAController::$CBRaceSub); ?> >
                                <?php echo EthnicityRaceGenderVeteran::$RaceSub[4]; ?>
                                <br>
                                <input type="radio" name="CBRaceSub" id="CBRaceSub" class="" disabled
                                       value="5" <?php echo Strings::isChecked('5', HMDAController::$CBRaceSub); ?> >
                                <?php echo EthnicityRaceGenderVeteran::$RaceSub[5]; ?>
                                <br>
                                <input type="radio" name="CBRaceSub" id="CBRaceSub" class="" disabled
                                       value="6" <?php echo Strings::isChecked('6', HMDAController::$CBRaceSub); ?> >
                                <?php echo EthnicityRaceGenderVeteran::$RaceSub[6]; ?>
                                <br>
                                <input type="radio" name="CBRaceSub" id="CBRaceSub" class="" disabled
                                       value="7" <?php echo Strings::isChecked('7', HMDAController::$CBRaceSub); ?> >
                                <?php echo EthnicityRaceGenderVeteran::$RaceSub[7]; ?>
                                <div class="col-md-12 no-padding <?php if (HMDAController::$CBRaceSub != 7) echo 'hidden'; ?>"
                                     id="CBAsianDiv">
                                    <div class="col-md-12">
                                        Print Race
                                        <input type="text" class="form-control input-sm" disabled
                                               name="CBRaceAsianOther" id="CBRaceAsianOther"
                                               value="<?php echo HMDAController::$CBRaceAsianOther; ?>">
                                        <i>For example: Hmong, Laotian, Thai, Pakistani, Cambodian, and
                                            so
                                            on.</i>
                                    </div>
                                </div>
                            </div>
                            <input type="radio" name="CBRace" value="3"
                                   disabled <?php echo Strings::isChecked(HMDAController::$CBRace, '3'); ?>
                                   tabindex="<?php echo HMDAController::$tabIndexNo++; ?>">&nbsp;<?php echo EthnicityRaceGenderVeteran::$Race[3]; ?>

                            <br>
                            <input type="radio" name="CBRace" value="4"
                                   disabled <?php echo Strings::isChecked(HMDAController::$CBRace, '4'); ?>
                                   tabindex="<?php echo HMDAController::$tabIndexNo++; ?>">&nbsp;<?php echo EthnicityRaceGenderVeteran::$Race[4]; ?>
                            <br>
                            <input type="radio" name="CBRace" value="5"
                                   disabled <?php echo Strings::isChecked(HMDAController::$CBRace, '5'); ?>
                                   tabindex="<?php echo HMDAController::$tabIndexNo++; ?>">&nbsp;<?php echo EthnicityRaceGenderVeteran::$Race[5]; ?><br>
                            <input type="radio" name="CBRace" value="6"
                                   disabled <?php echo Strings::isChecked(HMDAController::$CBRace, '6'); ?>
                                   tabindex="<?php echo HMDAController::$tabIndexNo++; ?>">&nbsp;<?php echo EthnicityRaceGenderVeteran::$Race[6]; ?>
                            <?php
                        }
                        ?>
                        <div class="clsAnswer"></div>
                    </td>
                </tr>
                <tr class="even">
                    <td class="font-weight-bold">Sex:</td>
                    <td>
                        <?php
                        if (hmda::$allowToEdit) { ?>
                            <div class="radio-inline">
                                <label class="radio radio-solid font-weight-bold" for="CBGender1">
                                    <input type="radio" name="CBGender" id="CBGender1"
                                           value="2" <?php echo Strings::isChecked('2', HMDAController::$CBGender); ?>
                                           class="CoBorChildRadio coBorrower"
                                           tabindex="<?php echo HMDAController::$tabIndexNo++; ?>"><span></span>
                                    <?php echo EthnicityRaceGenderVeteran::$Gender[2]; ?>
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="CBGender2">
                                    <input type="radio" name="CBGender" id="CBGender2"
                                           value="1" <?php echo Strings::isChecked('1', HMDAController::$CBGender); ?>
                                           class="CoBorChildRadio coBorrower"
                                           tabindex="<?php echo HMDAController::$tabIndexNo++; ?>"><span></span>
                                    <?php echo EthnicityRaceGenderVeteran::$Gender[1]; ?>
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="CBGender3">
                                    <input type="radio" name="CBGender" id="CBGender3"
                                           value="3" <?php echo Strings::isChecked('3', HMDAController::$CBGender); ?>
                                           class="CoBorChildRadio coBorrower"
                                           tabindex="<?php echo HMDAController::$tabIndexNo++; ?>"><span></span>
                                    <?php echo EthnicityRaceGenderVeteran::$Gender[3]; ?>
                                </label>
                            </div>
                        <?php } else { ?>
                            <input type="radio" name="CBGender" disabled
                                   value="2" <?php echo Strings::isChecked('2', HMDAController::$CBGender); ?>
                                   tabindex="<?php echo HMDAController::$tabIndexNo++; ?>"> <?php echo EthnicityRaceGenderVeteran::$Gender[2]; ?>
                            <input type="radio" name="CBGender" disabled
                                   value="1" <?php echo Strings::isChecked('1', HMDAController::$CBGender); ?>
                                   tabindex="<?php echo HMDAController::$tabIndexNo++; ?>"> <?php echo EthnicityRaceGenderVeteran::$Gender[1]; ?>
                            <input type="radio" name="CBGender" disabled
                                   value="3" <?php echo Strings::isChecked('3', HMDAController::$CBGender); ?>
                                   tabindex="<?php echo HMDAController::$tabIndexNo++; ?>"> <?php echo EthnicityRaceGenderVeteran::$Gender[3]; ?>
                            <?php
                        }
                        ?>
                    </td>
                </tr>
                <tr>
                    <td class="font-weight-bold">Veteran</td>
                    <td>
                        <?php if (hmda::$allowToEdit) { ?>
                            <div class="radio-list">
                                <label class="radio radio-solid font-weight-bold" for="CBVeteran1">
                                    <input type="radio" name="CBVeteran" id="CBVeteran1"
                                           value="1" <?php echo Strings::isChecked('1', HMDAController::$CBVeteran); ?>
                                           class="CoBorChildRadio coBorrower"
                                           tabindex="<?php echo HMDAController::$tabIndexNo++; ?>"><span></span>
                                    <?php echo EthnicityRaceGenderVeteran::$Veteran[1]; ?>
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="CBVeteran2">
                                    <input type="radio" name="CBVeteran" id="CBVeteran2"
                                           value="2" <?php echo Strings::isChecked('2', HMDAController::$CBVeteran); ?>
                                           class="CoBorChildRadio coBorrower"
                                           tabindex="<?php echo HMDAController::$tabIndexNo++; ?>"><span></span>
                                    <?php echo EthnicityRaceGenderVeteran::$Veteran[2]; ?>
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="CBVeteran3">
                                    <input type="radio" name="CBVeteran" id="CBVeteran3"
                                           value="3" <?php echo Strings::isChecked('3', HMDAController::$CBVeteran); ?>
                                           class="CoBorChildRadio coBorrower"
                                           tabindex="<?php echo HMDAController::$tabIndexNo++; ?>"><span></span>
                                    <?php echo EthnicityRaceGenderVeteran::$Veteran[3]; ?>
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="CBVeteran4">
                                    <input type="radio" name="CBVeteran" id="CBVeteran4"
                                           value="4" <?php echo Strings::isChecked('4', HMDAController::$CBVeteran); ?>
                                           class="CoBorChildRadio coBorrower"
                                           tabindex="<?php echo HMDAController::$tabIndexNo++; ?>"><span></span>
                                    <?php echo EthnicityRaceGenderVeteran::$Veteran[4]; ?>
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="CBVeteran5">
                                    <input type="radio" name="CBVeteran" id="CBVeteran5"
                                           value="5" <?php echo Strings::isChecked('5', HMDAController::$CBVeteran); ?>
                                           class="CoBorChildRadio coBorrower"
                                           tabindex="<?php echo HMDAController::$tabIndexNo++; ?>"><span></span>
                                    <?php echo EthnicityRaceGenderVeteran::$Veteran[5]; ?>
                                </label>
                            </div>
                        <?php } else { ?>
                            <input type="radio" disabled name="CBVeteran" id="CBVeteran"
                                   value="1" <?php echo Strings::isChecked('1', HMDAController::$CBVeteran); ?>
                                   class="coBorrower"
                                   tabindex="<?php echo HMDAController::$tabIndexNo++; ?>"> <?php echo EthnicityRaceGenderVeteran::$Veteran[1]; ?><br>
                            <input type="radio" disabled name="CBVeteran" id="CBVeteran"
                                   value="2" <?php echo Strings::isChecked('2', HMDAController::$CBVeteran); ?>
                                   class="coBorrower"
                                   tabindex="<?php echo HMDAController::$tabIndexNo++; ?>"> <?php echo EthnicityRaceGenderVeteran::$Veteran[2]; ?><br>
                            <input type="radio" disabled name="CBVeteran" id="CBVeteran"
                                   value="3" <?php echo Strings::isChecked('3', HMDAController::$CBVeteran); ?>
                                   class="coBorrower"
                                   tabindex="<?php echo HMDAController::$tabIndexNo++; ?>"> <?php echo EthnicityRaceGenderVeteran::$Veteran[3]; ?><br>
                            <input type="radio" disabled name="CBVeteran" id="CBVeteran"
                                   value="4" <?php echo Strings::isChecked('4', HMDAController::$CBVeteran); ?>
                                   class="coBorrower"
                                   tabindex="<?php echo HMDAController::$tabIndexNo++; ?>"> <?php echo EthnicityRaceGenderVeteran::$Veteran[4]; ?><br>
                            <input type="radio" disabled name="CBVeteran" id="CBVeteran"
                                   value="5" <?php echo Strings::isChecked('5', HMDAController::$CBVeteran); ?>
                                   class="coBorrower"
                                   tabindex="<?php echo HMDAController::$tabIndexNo++; ?>"> <?php echo EthnicityRaceGenderVeteran::$Veteran[5]; ?>
                        <?php } ?>
                    </td>
                </tr>
            </table>

            <div class="form-group row col-lg-12 m-0 mb-4 px-0" id="cbFiTitle">
                <label class="bg-secondary  py-4  col-lg-12"><b>To Be Completed by Financial
                        Institution (for application taken in person)</b></label>
            </div>

            <?php if (hmda::$allowToEdit) { ?>
                <table width="100%"
                       class="table table-hover  table-bordered table-condensed table-sm table-vertical-center">
                    <tr>
                        <td width="80%" class="font-weight-bold">Was the ethnicity of the Co-Borrower
                            collected on the basis of
                            visual
                            observation or surname?
                        </td>
                        <td width="20%">
                            <div class="radio-inline">
                                <label class="radio radio-solid font-weight-bold" for="CBFiEthnicityYes">
                                    <input type="radio" name="CBFiEthnicity" id="CBFiEthnicityYes" class=""
                                           value="Yes" <?php echo Strings::isChecked('Yes', HMDAController::$CBFiEthnicity); ?>><span></span>
                                    Yes
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="CBFiEthnicityNo">
                                    <input type="radio" name="CBFiEthnicity" id="CBFiEthnicityNo" class=""
                                           value="No" <?php echo Strings::isChecked('No', HMDAController::$CBFiEthnicity); ?>><span></span>
                                    No
                                </label>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td width="80%" class="font-weight-bold">Was the sex of the Co-Borrower collected on
                            the basis of visual
                            observation
                            or surname?
                        </td>
                        <td width="20%">
                            <div class="radio-inline">
                                <label class="radio radio-solid font-weight-bold" for="CBFiGenderYes">
                                    <input type="radio" name="CBFiGender" id="CBFiGenderYes" class=""
                                           value="Yes" <?php echo Strings::isChecked('Yes', HMDAController::$CBFiGender); ?>><span></span>
                                    Yes
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="CBFiGenderNo">
                                    <input type="radio" name="CBFiGender" id="CBFiGenderNo" class=""
                                           value="No" <?php echo Strings::isChecked('No', HMDAController::$CBFiGender); ?>><span></span>
                                    No
                                </label>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td width="80%" class="font-weight-bold">Was the race of the Co-Borrower collected
                            on the basis of visual
                            observation
                            or surname?
                        </td>
                        <td width="20%">
                            <div class="radio-inline">
                                <label class="radio radio-solid font-weight-bold" for="CBFiRaceYes">
                                    <input type="radio" name="CBFiRace" id="CBFiRaceYes" class=""
                                           value="Yes" <?php echo Strings::isChecked('Yes', HMDAController::$CBFiRace); ?>>
                                    <span></span>Yes
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="CBFiRaceNo">
                                    <input type="radio" name="CBFiRace" id="CBFiRaceNo" class=""
                                           value="No" <?php echo Strings::isChecked('No', HMDAController::$CBFiRace); ?>><span></span>
                                    No
                                </label>
                            </div>
                        </td>
                    </tr>
                </table>
            <?php } else { ?>
                <table>
                    <tr>
                        <td width="80%">Was the ethnicity of the Co-Borrower collected on the basis of
                            visual
                            observation or surname?
                        </td>
                        <td width="20%">
                            <input type="radio" name="CBFiEthnicity" id="CBFiEthnicity" class="" disabled
                                   value="Yes" <?php echo Strings::isChecked('Yes', HMDAController::$CBFiEthnicity); ?>> Yes
                            <input type="radio" name="CBFiEthnicity" id="CBFiEthnicity" class="" disabled
                                   value="No" <?php echo Strings::isChecked('No', HMDAController::$CBFiEthnicity); ?>> No
                        </td>
                    </tr>
                    <tr>
                        <td width="80%">Was the sex of the Co-Borrower collected on the basis of visual
                            observation
                            or surname?
                        </td>
                        <td width="20%">
                            <input type="radio" name="CBFiGender" id="CBFiGender" class="" disabled
                                   value="Yes" <?php echo Strings::isChecked('Yes', HMDAController::$CBFiGender); ?>> Yes
                            <input type="radio" name="CBFiGender" id="CBFiGender" class="" disabled
                                   value="No" <?php echo Strings::isChecked('No', HMDAController::$CBFiGender); ?>> No
                        </td>
                    </tr>
                    <tr>
                        <td width="80%">Was the race of the Co-Borrower collected on the basis of visual
                            observation
                            or surname?
                        </td>
                        <td width="20%">
                            <input type="radio" name="CBFiRace" id="CBFiRace" class="" disabled
                                   value="Yes" <?php echo Strings::isChecked('Yes', HMDAController::$CBFiRace); ?>> Yes
                            <input type="radio" name="CBFiRace" id="CBFiRace" class="" disabled
                                   value="No" <?php echo Strings::isChecked('No', HMDAController::$CBFiRace); ?>> No
                        </td>
                    </tr>
                </table>
            <?php } ?>


            <div class="form-group row col-lg-12 m-0 mb-4 px-0" id="cbFiTitle">
                <label class="bg-secondary  py-4  col-lg-12"><b>The Demographic Information was
                        provided through</b></label>
            </div>

            <?php if (hmda::$allowToEdit) { ?>
                <table width="100%"
                       class="table   table-bordered table-condensed table-sm table-vertical-center">
                    <tr>
                        <td>
                            <div class="radio-list">
                                <label class="radio radio-solid font-weight-bold" for="CBDDemoInfo">
                                    <input type="radio" name="CBDDemoInfo" id="CBDDemoInfo"
                                           value="1" <?php echo Strings::isChecked('1', HMDAController::$CBDDemoInfo); ?>><span></span>
                                    Face-to-Face Interview (includes Electronic Media w/ Video Component)
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="CBDDemoInfo2">
                                    <input type="radio" name="CBDDemoInfo" id="CBDDemoInfo2"
                                           value="2" <?php echo Strings::isChecked('2', HMDAController::$CBDDemoInfo); ?>><span></span>
                                    Telephone Interview
                                </label>
                                <label class="radio radio-solid font-weight-bold" for="CBDDemoInfo3">
                                    <input type="radio" name="CBDDemoInfo" id="CBDDemoInfo3"
                                           value="3" <?php echo Strings::isChecked('3', HMDAController::$CBDDemoInfo); ?>><span></span>
                                    Fax or Mail
                                </label>

                                <label class="radio radio-solid font-weight-bold" for="CBDDemoInfo4">
                                    <input type="radio" name="CBDDemoInfo" id="CBDDemoInfo4"
                                           value="4" <?php echo Strings::isChecked('4', HMDAController::$CBDDemoInfo); ?>><span></span>
                                    Email or Internet
                                </label>
                            </div>
                        </td>
                    </tr>
                </table>
            <?php } else { ?>
            <?php } ?>
        </div>
    </div>
</div>
