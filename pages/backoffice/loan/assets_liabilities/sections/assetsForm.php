<?php
namespace pages\backoffice\loan\assets_liabilities;

use models\Controllers\backoffice\LMRequest;
use models\Controllers\loanForm;
use models\CustomField;
use models\lendingwise\tblFile;
use models\PageVariables;
use models\standard\BaseHTML;
use models\standard\Currency;
use pages\backoffice\loan\assets_liabilities\classes\AssetsLiabilitiesController;


?>
<div class="card card-custom  HMLOLoanInfoSections isClientInfo Assets AssetsCard">

    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <?php echo loanForm::toggleSectionV2(
            assets_liabilities::$sId,
            true,
            true,
        ); ?>
    </div>
    <div class="card-body assets AssetsCard_body">
        <div class="row">
            <div class="col-md-12">
                <div class="form-group">
                    <div class="row">
                        <div class="col-4 font-weight-boldest">Type</div>
                        <div class="col-4 font-weight-boldest">Estimated Value ($)</div>
                        <div class="col-4 font-weight-boldest">Amount Owed ($)</div>
                    </div>
                </div>
                <div class="form-group <?php echo loanForm::showField('assetCash'); ?>">
                    <div class="row">
                        <div class="col-md-4">
                            <?php echo loanForm::label2('assetCash'); ?>
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                'assetCash',
                                assets_liabilities::$allowToEdit,
                                assets_liabilities::$tabIndex++,
                                AssetsLiabilitiesController::$assetCash,
                                null,
                                'calculateTotalAssets(this.value);'
                            ); ?>
                        </div>
                        <div class="col-4">
                        </div>
                    </div>
                </div>

                <div
                    class="form-group  <?php echo loanForm::showField('vestedInterest'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label2('vestedInterest'); ?>
                        </div>
                        <div class="col-4">
                            <?php echo loanForm::currency(
                                'vestedInterest',
                                assets_liabilities::$allowToEdit,
                                assets_liabilities::$tabIndex++,
                                AssetsLiabilitiesController::$vestedInterest,
                                null,
                                'calculateTotalSumOfAll(this.value);calculateTotalAssets(this.value);'
                            ); ?>
                        </div>
                        <div class="col-4">
                        </div>
                    </div>
                </div>

                <div
                    class="form-group  <?php echo loanForm::showField('assetCheckingAccounts'); ?> ">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetCheckingAccounts'); ?>
                        </div>
                        <div class="col-4">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetCheckingAccounts', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                        type="text" name="assetCheckingAccounts" id="assetCheckingAccounts"
                                        onblur="currencyConverter(this, this.value);"
                                        onchange="calculateTotalAssets(this.value);"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetCheckingAccounts) ?>"
                                        size="30"
                                        maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" autocomplete="off"
                                        placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'assetCheckingAccounts', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>
                                    $ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetCheckingAccounts) ?></label>
                            <?php } ?>
                        </div>
                        <div class="col-4">
                            &nbsp;
                        </div>
                    </div>
                </div>

                <div
                    class="form-group assetSavingMoneyMarket_disp <?php echo loanForm::showField('assetSavingMoneyMarket'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetSavingMoneyMarket'); ?>
                        </div>
                        <div class="col-4">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetSavingMoneyMarket', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                        type="text" name="assetSavingMoneyMarket" id="assetSavingMoneyMarket"
                                        onchange="calculateTotalAssets(this.value);currencyConverter(this, this.value);"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetSavingMoneyMarket) ?>"
                                        maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" autocomplete="off"
                                        placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'assetSavingMoneyMarket', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetSavingMoneyMarket) ?></label>
                            <?php } ?>
                        </div>
                        <div class="col-4">
                            &nbsp;
                        </div>
                    </div>
                </div>

                <div
                    class="form-group networthOfBusinessOwned_disp <?php echo loanForm::showField('networthOfBusinessOwned'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('networthOfBusinessOwned'); ?>
                        </div>
                        <div class="col-4">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'networthOfBusinessOwned', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                        type="text" name="networthOfBusinessOwned" id="networthOfBusinessOwned"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$networthOfBusinessOwned) ?>"
                                        maxlength="30" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" autocomplete="off"
                                        onchange="calculateTotalAssets(this.value);currencyConverter(this, this.value);"
                                        placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'networthOfBusinessOwned', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$networthOfBusinessOwned) ?></label>
                            <?php } ?>
                        </div>
                        <div class="col-4">
                            &nbsp;
                        </div>
                    </div>
                </div>

                <!-- Accounts & Notes Receivable -->
                <div
                    class="form-group assetAccount_disp <?php echo loanForm::showField('assetAccount'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetAccount'); ?>
                        </div>
                        <div class="col-4">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetAccount', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                        type="text" name="assetAccount" id="assetAccount"
                                        onblur="currencyConverter(this, this.value);"
                                        onchange="<?php if (AssetsLiabilitiesController::$isLO == 1) { ?> calculateTotalSumOfAll(this.value); <?php } else { ?> calculateTotalAssets(this.value);<?php } ?>"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetAccount); ?>"
                                        size="30"
                                        maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" autocomplete="off"
                                        placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'assetAccount', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetAccount); ?></label>
                            <?php } ?>
                        </div>
                        <div class="col-4">
                            <div
                                class="assetAccountOwd_disp <?php echo loanForm::showField('assetAccountOwd'); ?>">
                                <?php echo loanForm::label('assetAccountOwd', 'hidden'); ?>
                                <?php if (assets_liabilities::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input
                                            class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetAccountOwd', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                            type="text" name="assetAccountOwd" id="assetAccountOwd"
                                            onblur="currencyConverter(this, this.value);"
                                            onchange="<?php if (AssetsLiabilitiesController::$isLO == 1) { ?> calculateTotalSumOfAll(this.value); <?php } else { ?> calculateTotalAssetsOwed(this.value,'loanModForm'); <?php } ?>"
                                            value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetAccountOwd); ?>"
                                            size="30"
                                            maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" autocomplete="off"
                                            placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'assetAccountOwd', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                    </div>
                                <?php } else { ?>
                                    <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetAccountOwd); ?></label>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- // Accounts & Notes Receivable // -->

                <!-- Assets Stocks-->
                <div
                    class="form-group assetStocks_disp <?php echo loanForm::showField('assetStocks'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetStocks'); ?>
                        </div>
                        <div class="col-4">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetStocks', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                        type="text" name="assetStocks" id="assetStocks"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetStocks); ?>"
                                        size="30"
                                        maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>"
                                        onchange="<?php if (AssetsLiabilitiesController::$isLO == 1) { ?> calculateTotalSumOfAll(this.value);currencyConverter(this, this.value); <?php } else { ?>  calculateTotalAssets(this.value);currencyConverter(this, this.value);<?php } ?>"
                                        autocomplete="off"
                                        placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'assetStocks', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetStocks) ?></label>
                            <?php } ?>
                        </div>
                        <div class="col-4">
                            <div
                                class="assetStocksOwed_disp <?php echo loanForm::showField('assetStocksOwed'); ?>">
                                <?php echo loanForm::label('assetStocksOwed', 'hidden'); ?>
                                <?php if (assets_liabilities::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input
                                            class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetStocksOwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                            type="text" name="assetStocksOwed" id="assetStocksOwed"
                                            value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetStocksOwed); ?>"
                                            size="30"
                                            maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>"
                                            onchange="<?php if (AssetsLiabilitiesController::$isLO == 1) { ?> calculateTotalSumOfAll(this.value);currencyConverter(this, this.value); <?php } else { ?>  calculateTotalAssetsOwed(this.value, 'loanModForm');currencyConverter(this, this.value);<?php } ?>"
                                            autocomplete="off"
                                            placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'assetStocks', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                    </div>
                                <?php } else { ?>
                                    <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetStocksOwed); ?></label>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- // Assets Stocks // -->

                <!-- Non-Marketable Securities -->
                <div
                    class="form-group assetNonMarketableSecurities_disp <?php echo loanForm::showField('assetNonMarketableSecurities'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetNonMarketableSecurities'); ?>
                        </div>
                        <div class="col-4">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetNonMarketableSecurities', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                        type="text" name="assetNonMarketableSecurities"
                                        id="assetNonMarketableSecurities"
                                        onblur="currencyConverter(this, this.value);"
                                        onchange="<?php if (AssetsLiabilitiesController::$isLO == 1) { ?> calculateTotalSumOfAll(this.value); <?php } else { ?> calculateTotalAssets(this.value);<?php } ?>"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetNonMarketableSecurities); ?>"
                                        size="30" maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>"
                                        autocomplete="off"
                                        placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'assetNonMarketableSecurities', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetNonMarketableSecurities); ?></label>
                            <?php } ?>
                        </div>
                        <div class="col-4">
                            <div
                                class="assetNonMarketableSecuritiesOwd_disp <?php echo loanForm::showField('assetNonMarketableSecuritiesOwd'); ?>">
                                <?php echo loanForm::label('assetNonMarketableSecuritiesOwd', 'hidden'); ?>
                                <?php if (assets_liabilities::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input
                                            class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetNonMarketableSecuritiesOwd', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                            type="text" name="assetNonMarketableSecuritiesOwd"
                                            id="assetNonMarketableSecuritiesOwd"
                                            onblur="currencyConverter(this, this.value);"
                                            onchange="<?php if (AssetsLiabilitiesController::$isLO == 1) { ?> calculateTotalSumOfAll(this.value); <?php } else { ?> calculateTotalAssetsOwed(this.value,'loanModForm'); <?php } ?>"
                                            value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetNonMarketableSecuritiesOwd); ?>"
                                            size="30" maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>"
                                            autocomplete="off"
                                            placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'assetNonMarketableSecuritiesOwd', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                    </div>
                                <?php } else { ?>
                                    <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetNonMarketableSecuritiesOwd); ?></label>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- // Non Marketable Securities // -->

                <div
                    class="form-group assetIRAAccounts_disp <?php echo loanForm::showField('assetIRAAccounts'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetIRAAccounts'); ?>
                        </div>
                        <div class="col-4">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetIRAAccounts', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                        type="text" name="assetIRAAccounts" id="assetIRAAccounts"
                                        onblur="currencyConverter(this, this.value);"
                                        onchange="calculateTotalAssets(this.value);"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetIRAAccounts) ?>"
                                        size="30"
                                        maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" autocomplete="off"
                                        placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'assetIRAAccounts', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetIRAAccounts); ?></label>
                            <?php } ?>
                        </div>
                        <div class="col-4">
                            <div
                                class="assetIRAAccountsOwed_disp <?php echo loanForm::showField('assetIRAAccountsOwed'); ?>">
                                <?php echo loanForm::label('assetIRAAccountsOwed', 'hidden'); ?>
                                <?php if (assets_liabilities::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input
                                            class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetIRAAccountsOwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                            type="text" name="assetIRAAccountsOwed" id="assetIRAAccountsOwed"
                                            onblur="currencyConverter(this, this.value);"
                                            onchange="calculateTotalAssetsOwed(this.value, 'loanModForm');"
                                            value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetIRAAccountsOwed) ?>"
                                            size="30"
                                            maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" autocomplete="off"
                                            placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'assetIRAAccountsOwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                    </div>
                                <?php } else { ?>
                                    <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetIRAAccountsOwed) ?></label>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div
                    class="form-group assetESPOAccounts_disp <?php echo loanForm::showField('assetESPOAccounts'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetESPOAccounts'); ?>
                        </div>
                        <div class="col-4">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetESPOAccounts', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                        type="text" name="assetESPOAccounts" id="assetESPOAccounts"
                                        onblur="currencyConverter(this, this.value);"
                                        onchange="calculateTotalAssets(this.value);"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetESPOAccounts) ?>"
                                        size="30"
                                        maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" autocomplete="off"
                                        placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'assetESPOAccounts', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetESPOAccounts); ?></label>
                            <?php } ?>
                        </div>
                        <div class="col-4">
                            <div
                                class="assetESPOAccountsOwed_disp <?php echo loanForm::showField('assetESPOAccountsOwed'); ?> ">
                                <?php echo loanForm::label('assetESPOAccountsOwed', 'hidden'); ?>
                                <?php if (assets_liabilities::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input
                                            class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetESPOAccountsOwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                            type="text" name="assetESPOAccountsOwed" id="assetESPOAccountsOwed"
                                            onblur="currencyConverter(this, this.value);"
                                            onchange="calculateTotalAssetsOwed(this.value, 'loanModForm');"
                                            value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetESPOAccountsOwed); ?>"
                                            maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" autocomplete="off"
                                            placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'assetESPOAccountsOwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                    </div>
                                <?php } else { ?>
                                    <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetESPOAccountsOwed); ?></label>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div
                    class="form-group assetLifeInsurance_disp <?php echo loanForm::showField('assetLifeInsurance'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetLifeInsurance'); ?>
                        </div>
                        <div class="col-4">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetLifeInsurance', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                        type="text" name="assetLifeInsurance" id="assetLifeInsurance"
                                        onchange="<?php if (AssetsLiabilitiesController::$isLO == 1) { ?> calculateTotalSumOfAll(this.value);currencyConverter(this, this.value); <?php } else { ?> calculateTotalAssets(this.value);currencyConverter(this, this.value);<?php } ?>"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetLifeInsurance) ?>"
                                        size="30"
                                        maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" autocomplete="off"
                                        placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'assetLifeInsurance', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetLifeInsurance); ?></label>
                            <?php } ?>
                        </div>
                        <div class="col-4">
                            <div
                                class="assetLifeInsuranceOwed_disp assetLifeInsuranceOwed <?php echo loanForm::showField('assetLifeInsuranceOwed'); ?> ">
                                <?php echo loanForm::label('assetLifeInsuranceOwed', 'hidden'); ?>
                                <?php if (assets_liabilities::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input
                                            class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetLifeInsuranceOwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                            type="text" name="assetLifeInsuranceOwed" id="assetLifeInsuranceOwed"
                                            onchange="<?php if (AssetsLiabilitiesController::$isLO == 1) { ?> calculateTotalSumOfAll(this.value);currencyConverter(this, this.value); <?php } else { ?> calculateTotalAssetsOwed(this.value, 'loanModForm'); currencyConverter(this, this.value);<?php } ?>"
                                            value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetLifeInsuranceOwed); ?>"
                                            maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" autocomplete="off"
                                            placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'assetLifeInsuranceOwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                    </div>
                                <?php } else { ?>
                                    <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetLifeInsuranceOwed); ?></label>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div
                    class="form-group assetTotalCashBankAcc_disp <?php echo loanForm::showField('assetTotalCashBankAcc'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetTotalCashBankAcc'); ?>
                        </div>
                        <div class="col-4">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetTotalCashBankAcc', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                        type="text" name="assetTotalCashBankAcc" id="assetTotalCashBankAcc"
                                        onblur="currencyConverter(this, this.value);"
                                        onchange="calculateTotalAssets(this.value);"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetTotalCashBankAcc) ?>"
                                        maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" autocomplete="off"
                                        placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'assetTotalCashBankAcc', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label><?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetTotalCashBankAcc); ?></label>
                            <?php } ?>
                        </div>
                        <div class="col-4">
                            &nbsp;
                        </div>
                    </div>
                </div>

                <div
                    class="form-group assetTotalRetirementValue_disp <?php echo loanForm::showField('assetTotalRetirementValue'); ?> ">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetTotalRetirementValue'); ?>
                        </div>
                        <div class="col-4">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetTotalRetirementValue', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                        type="text" name="assetTotalRetirementValue" id="assetTotalRetirementValue"
                                        onblur="currencyConverter(this, this.value);"
                                        onchange="calculateTotalAssets(this.value);"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetTotalRetirementValue) ?>"
                                        maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" autocomplete="off"
                                        placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'assetTotalRetirementValue', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetTotalRetirementValue) ?></label>
                            <?php } ?>
                        </div>
                        <div class="col-4">
                            &nbsp;
                        </div>
                    </div>
                </div>

                <div
                    class="form-group assetAvailabilityLinesCredit_disp <?php echo loanForm::showField('assetAvailabilityLinesCredit'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetAvailabilityLinesCredit'); ?>
                        </div>
                        <div class="col-4">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetAvailabilityLinesCredit', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                        type="text" name="assetAvailabilityLinesCredit"
                                        id="assetAvailabilityLinesCredit"
                                        onblur="currencyConverter(this, this.value);"
                                        onchange="calculateTotalAssets(this.value);"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetAvailabilityLinesCredit) ?>"
                                        size="14" maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>"
                                        autocomplete="off"
                                        placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'assetAvailabilityLinesCredit', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label><?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetAvailabilityLinesCredit) ?></label>
                            <?php } ?>
                        </div>
                        <div class="col-4">
                            <div
                                class="assetAvailabilityLinesCreditOwed_disp <?php echo loanForm::showField('assetAvailabilityLinesCreditOwed'); ?>">
                                <?php echo loanForm::label('assetAvailabilityLinesCreditOwed', 'hidden'); ?>
                                <?php if (assets_liabilities::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input
                                            class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetAvailabilityLinesCreditOwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                            type="text" name="assetAvailabilityLinesCreditOwed"
                                            id="assetAvailabilityLinesCreditOwed"
                                            onblur="currencyConverter(this, this.value);"
                                            onchange="calculateTotalAssetsOwed(this.value, 'loanModForm');"
                                            value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetAvailabilityLinesCreditOwed); ?>"
                                            size="14" maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>"
                                            autocomplete="off"
                                            placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'assetAvailabilityLinesCreditOwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                    </div>
                                <?php } else { ?>
                                    <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetAvailabilityLinesCreditOwed); ?></label>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div
                    class="form-group assetHome_disp <?php echo loanForm::showField('assetHome'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetHome'); ?>
                        </div>
                        <div class="col-4">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetHome', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                        type="text" name="assetHome" id="assetHome"
                                        onblur="currencyConverter(this, this.value);"
                                        onchange="calculateTotalAssets(this.value);"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetHome) ?>"
                                        size="30"
                                        maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" autocomplete="off"
                                        placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'assetHome', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetHome); ?></label>
                            <?php } ?>
                        </div>
                        <div class="col-4">
                            <div
                                class="assetHomeOwed_disp <?php echo loanForm::showField('assetHomeOwed'); ?>">
                                <?php echo loanForm::label('assetHomeOwed', 'hidden'); ?>
                                <?php if (assets_liabilities::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input
                                            class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetHomeOwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                            type="text" name="assetHomeOwed" id="assetHomeOwed"
                                            onblur="currencyConverter(this, this.value);"
                                            onchange="calculateTotalAssetsOwed(this.value, 'loanModForm');"
                                            value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetHomeOwed) ?>"
                                            size="30"
                                            maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" autocomplete="off"
                                            placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'assetHomeOwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                    </div>
                                <?php } else { ?>
                                    <label><?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetHomeOwed); ?></label>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div
                    class="form-group assetSR_disp <?php echo loanForm::showField('assetSR'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetSR'); ?>
                        </div>
                        <div class="col-4">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetSR', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                        type="text" name="assetSR" id="assetSR"
                                        onblur="currencyConverter(this, this.value);"
                                        onchange="calculateTotalAssets(this.value);"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetSR) ?>"
                                        size="14"
                                        maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" autocomplete="off"
                                        placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'assetSR', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetSR); ?></label>
                            <?php } ?>
                        </div>
                        <div class="col-4">
                            <div
                                class="assetSROwed_disp <?php echo loanForm::showField('assetSROwed'); ?>">
                                <?php echo loanForm::label('assetSROwed', 'hidden'); ?>
                                <?php if (assets_liabilities::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input
                                            class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetSROwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                            type="text" name="assetSROwed" id="assetSROwed"
                                            onblur="currencyConverter(this, this.value);"
                                            onchange="calculateTotalAssetsOwed(this.value,'loanModForm');"
                                            value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetSROwed) ?>"
                                            size="18"
                                            maxlength="40" autocomplete="off" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>"
                                            placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'assetSROwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                    </div>
                                <?php } else { ?>
                                    <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetSROwed); ?></label>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div
                    class="form-group SORESection assetORE_disp <?php echo loanForm::showField('assetORE'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetORE'); ?>
                        </div>
                        <div class="col-4">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetORE', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                        type="text" name="assetORE" id="assetORE"
                                        onblur="currencyConverter(this, this.value);"
                                        onchange="<?php if (AssetsLiabilitiesController::$isLO == 1) { ?> calculateTotalSumOfAll(this.value); <?php } else { ?> calculateTotalAssets(this.value);<?php } ?>"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetORE) ?>"
                                        size="30"
                                        maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" autocomplete="off"
                                        placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'assetORE', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetORE); ?></label>
                            <?php } ?>
                        </div>
                        <div class="col-4">
                            <div
                                class="assetOREOwed_disp <?php echo loanForm::showField('assetOREOwed'); ?>">
                                <?php echo loanForm::label('assetOREOwed', 'hidden'); ?>
                                <?php if (assets_liabilities::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input
                                            class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetOREOwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                            type="text" name="assetOREOwed" id="assetOREOwed"
                                            onblur="currencyConverter(this, this.value);"
                                            onchange="calculateTotalAssetsOwed(this.value,'loanModForm');"
                                            value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetOREOwed) ?>"
                                            size="30"
                                            maxlength="40" autocomplete="off" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>"
                                            placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'assetOREOwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>>
                                    </div>
                                <?php } else { ?>
                                    <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetOREOwed); ?></label>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div
                    class="form-group assetCars_disp <?php echo loanForm::showField('assetCars'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetCars'); ?>
                        </div>
                        <div class="col-4">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetCars', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                        type="text" name="assetCars" id="assetCars"
                                        onblur="currencyConverter(this, this.value);"
                                        onchange="<?php if (AssetsLiabilitiesController::$isLO == 1) { ?> calculateTotalAutomobiles(this.value); calculateTotalSumOfAll(this.value); <?php } else { ?> calculateTotalAssets(this.value);<?php } ?>"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetCars) ?>"
                                        size="30"
                                        maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" autocomplete="off"
                                        placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'assetCars', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetCars); ?></label>
                            <?php } ?>
                        </div>
                        <div class="col-4">
                            <div
                                class="assetCarsOwed_disp <?php echo loanForm::showField('assetCarsOwed'); ?> ">
                                <?php echo loanForm::label('assetCarsOwed', 'hidden'); ?>
                                <?php if (assets_liabilities::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input
                                            class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetCarsOwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                            type="text" name="assetCarsOwed" id="assetCarsOwed"
                                            onblur="currencyConverter(this, this.value);"
                                            onchange="<?php if (AssetsLiabilitiesController::$isLO == 1) { ?> calculateTotalAutomobiles(this.value); calculateTotalSumOfAll(this.value); <?php } else { ?> calculateTotalAssetsOwed(this.value,'loanModForm');<?php } ?>"
                                            value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetCarsOwed) ?>"
                                            size="30"
                                            maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" autocomplete="off"
                                            placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'assetCarsOwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                    </div>
                                <?php } else { ?>
                                    <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetCarsOwed); ?></label>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div
                    class="form-group automobilesOwned3x_disp <?php echo loanForm::showField('automobilesOwned3x'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('automobilesOwned3x'); ?>
                        </div>
                        <div class="col-4">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'automobilesOwned3x', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                        type="text" name="automobilesOwned3x" id="automobilesOwned3x"
                                        onblur="currencyConverter(this, this.value);"
                                        onchange="<?php if (AssetsLiabilitiesController::$isLO == 1) { ?> calculateTotalAutomobiles(this.value); calculateTotalSumOfAll(this.value); <?php } else { ?> calculateTotalAssets(this.value);<?php } ?>"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$automobilesOwned3x) ?>"
                                        size="30"
                                        maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" autocomplete="off"
                                        placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'automobilesOwned3x', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>

                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$automobilesOwned3x); ?></label>
                            <?php } ?>
                        </div>
                        <div class="col-4">
                            <div
                                class="automobilesOwned3x1_disp <?php echo loanForm::showField('automobilesOwned3x1'); ?> ">
                                <?php echo loanForm::label('automobilesOwned3x1', 'hidden'); ?>
                                <?php if (assets_liabilities::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input
                                            class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'automobilesOwned3x1', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                            type="text" name="automobilesOwned3x1" id="automobilesOwned3x1"
                                            onblur="currencyConverter(this, this.value);"
                                            onchange="calculateTotalAssetsOwed(this.value,'loanModForm');"
                                            value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$automobilesOwned3x1) ?>"
                                            size="30"
                                            maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" autocomplete="off"
                                            placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'automobilesOwned3x1', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                    </div>
                                <?php } else { ?>
                                    <label>$ <?php echo AssetsLiabilitiesController::$automobilesOwned3x1; ?></label>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div
                    class="form-group assetOther_disp <?php echo loanForm::showField('assetOther'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetOther'); ?>
                        </div>
                        <div class="col-4">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetOther', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                        type="text" name="assetOther" id="assetOther"
                                        onblur="currencyConverter(this, this.value);"
                                        onchange="<?php if (AssetsLiabilitiesController::$isLO == 1) { ?> calculateTotalSumOfAll(this.value); <?php } else { ?> calculateTotalAssets(this.value);<?php } ?>"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetOther) ?>"
                                        size="30"
                                        maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" autocomplete="off"
                                        placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'assetOther', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetOther); ?></label>
                            <?php } ?>
                        </div>
                        <div class="col-4">
                            <div
                                class="otherAmtOwed_disp <?php echo loanForm::showField('otherAmtOwed'); ?>">
                                <?php echo loanForm::label('otherAmtOwed', 'hidden'); ?>
                                <?php if (assets_liabilities::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input
                                            class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'otherAmtOwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                            type="text" name="otherAmtOwed" id="otherAmtOwed"
                                            onblur="currencyConverter(this, this.value);"
                                            onchange="calculateTotalAssetsOwed(this.value,'loanModForm');"
                                            value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$otherAmtOwed) ?>"
                                            size="30"
                                            maxlength="40" autocomplete="off" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>"
                                            placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'otherAmtOwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                    </div>
                                <?php } else { ?>
                                    <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$otherAmtOwed); ?></label>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div
                    class="form-group otherAssets_disp <?php echo loanForm::showField('otherAssets'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('otherAssets'); ?>
                        </div>
                        <div class="col-4">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'otherAssets', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                        type="text" name="otherAssets" id="otherAssets"
                                        placeholder="0.00"
                                        onblur="currencyConverter(this, this.value);"
                                        onchange="calculateTotalAssets(this.value);calculateTotalSumOfAll(this.value);"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$otherAssets) ?>"
                                        size="30"
                                        tabindex="<?php echo assets_liabilities::$tabIndex++; ?>"
                                        autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'otherAssets', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?> />
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$otherAssets); ?></label>
                            <?php } ?>
                        </div>
                        <div class="col-4">
                            &nbsp;
                        </div>
                    </div>
                </div>
                <!-- Notes Payable to Banks & Others -->
                <div
                    class="form-group notesPayableToBanksOthers_disp <?php echo loanForm::showField('notesPayableToBanksOthers'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('notesPayableToBanksOthers', '', '', '', 'Owed'); ?>
                        </div>
                        <div class="col-4">
                            &nbsp;
                        </div>
                        <div class="col-4">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'notesPayableToBanksOthersOwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                        type="text" name="notesPayableToBanksOthersOwed"
                                        id="notesPayableToBanksOthersOwed"
                                        onblur="currencyConverter(this, this.value);"
                                        onchange="<?php if (AssetsLiabilitiesController::$isLO == 1) { ?> calculateTotalSumOfAll(this.value); <?php } else { ?> calculateTotalAssetsOwed(this.value,'loanModForm'); <?php } ?>"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$notesPayableToBanksOthersOwed); ?>"
                                        size="30" maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>"
                                        autocomplete="off"
                                        placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'notesPayableToBanksOthersOwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$notesPayableToBanksOthersOwed); ?></label>
                            <?php } ?>
                        </div>
                    </div>
                </div>
                <!-- // Notes Payable to Banks & Others // -->
                <!-- Installment Account(s) -->
                <div
                    class="form-group installmentAccount_disp <?php echo loanForm::showField('installmentAccount'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('installmentAccount', '', '', '', 'Owed'); ?>
                        </div>
                        <div class="col-4">
                            &nbsp;
                        </div>
                        <div class="col-4">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'installmentAccountOwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                        type="text" name="installmentAccountOwed" id="installmentAccountOwed"
                                        onblur="currencyConverter(this, this.value);"
                                        onchange="<?php if (AssetsLiabilitiesController::$isLO == 1) { ?> calculateTotalSumOfAll(this.value); <?php } else { ?> calculateTotalAssetsOwed(this.value,'loanModForm'); <?php } ?>"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$installmentAccountOwed); ?>"
                                        maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" autocomplete="off"
                                        placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'installmentAccountOwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$installmentAccountOwed); ?></label>
                            <?php } ?>
                        </div>
                    </div>
                </div>
                <!-- // Installment Account(s) // -->
                <!-- Revolving Debt -->
                <div
                    class="form-group revolvingDebt_disp <?php echo loanForm::showField('revolvingDebt'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('revolvingDebt', '', '', '', 'Owed'); ?>
                        </div>
                        <div class="col-4">
                            &nbsp;
                        </div>
                        <div class="col-4">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'revolvingDebtOwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                        type="text" name="revolvingDebtOwed" id="revolvingDebtOwed"
                                        onblur="currencyConverter(this, this.value);"
                                        onchange="<?php if (AssetsLiabilitiesController::$isLO == 1) { ?> calculateTotalSumOfAll(this.value); <?php } else { ?> calculateTotalAssetsOwed(this.value,'loanModForm'); <?php } ?>"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$revolvingDebtOwed); ?>"
                                        size="30"
                                        maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" autocomplete="off"
                                        placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'revolvingDebtOwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$revolvingDebtOwed); ?></label>
                            <?php } ?>
                        </div>
                    </div>
                </div>
                <!-- // Revolving Debt // -->

                <!-- Unpaid/Payable Taxes -->
                <div
                    class="form-group unpaidPayableTaxes_disp <?php echo loanForm::showField('unpaidPayableTaxes'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('unpaidPayableTaxes', '', '', '', 'Owed'); ?>
                        </div>
                        <div class="col-4">
                            &nbsp;
                        </div>
                        <div class="col-4">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'unpaidPayableTaxesOwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                        type="text" name="unpaidPayableTaxesOwed" id="unpaidPayableTaxesOwed"
                                        onblur="currencyConverter(this, this.value);"
                                        onchange="<?php if (AssetsLiabilitiesController::$isLO == 1) { ?> calculateTotalSumOfAll(this.value); <?php } else { ?> calculateTotalAssetsOwed(this.value,'loanModForm'); <?php } ?>"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$unpaidPayableTaxesOwed); ?>"
                                        maxlength="40" tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" autocomplete="off"
                                        placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'unpaidPayableTaxesOwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$unpaidPayableTaxesOwed); ?></label>
                            <?php } ?>
                        </div>
                    </div>
                </div>
                <!-- // Unpaid/Payable Taxes // -->


                <div
                    class="form-group assetSecNotesOwd_disp <?php echo loanForm::showField('assetSecNotesOwd'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetSecNotesOwd'); ?>
                        </div>
                        <div class="col-4">
                            &nbsp;
                        </div>
                        <div class="col-4">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetSecNotesOwd', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                        type="text"
                                        name="assetSecNotesOwd"
                                        id="assetSecNotesOwd"
                                        onblur="currencyConverter(this, this.value);"
                                        onchange="<?php if (AssetsLiabilitiesController::$isLO == 1) { ?> calculateTotalSumOfAll(this.value); <?php } else { ?> calculateTotalAssetsOwed(this.value,'loanModForm'); <?php } ?>"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetSecNotesOwd); ?>"
                                        maxlength="40"
                                        tabindex="<?php echo assets_liabilities::$tabIndex++; ?>"
                                        autocomplete="off"
                                        placeholder="0.00"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'assetSecNotesOwd', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetSecNotesOwd); ?></label>
                            <?php } ?>
                        </div>
                    </div>
                </div>


                <div
                    class="form-group assetUnsecNotesOwd_disp <?php echo loanForm::showField('assetUnsecNotesOwd'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetUnsecNotesOwd'); ?>
                        </div>
                        <div class="col-4">
                            &nbsp;
                        </div>
                        <div class="col-4">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetUnsecNotesOwd', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                        type="text"
                                        name="assetUnsecNotesOwd"
                                        id="assetUnsecNotesOwd"
                                        onblur="currencyConverter(this, this.value);"
                                        onchange="<?php if (AssetsLiabilitiesController::$isLO == 1) { ?> calculateTotalSumOfAll(this.value); <?php } else { ?> calculateTotalAssetsOwed(this.value,'loanModForm'); <?php } ?>"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetUnsecNotesOwd); ?>"
                                        maxlength="40"
                                        tabindex="<?php echo assets_liabilities::$tabIndex++; ?>"
                                        autocomplete="off"
                                        placeholder="0.00"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'assetUnsecNotesOwd', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetUnsecNotesOwd); ?></label>
                            <?php } ?>
                        </div>
                    </div>
                </div>

                <div
                    class="form-group assetAcctPayableOwd_disp <?php echo loanForm::showField('assetAcctPayableOwd'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetAcctPayableOwd'); ?>
                        </div>
                        <div class="col-4">
                            &nbsp;
                        </div>
                        <div class="col-4">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetAcctPayableOwd', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                        type="text"
                                        name="assetAcctPayableOwd"
                                        id="assetAcctPayableOwd"
                                        onblur="currencyConverter(this, this.value);"
                                        onchange="<?php if (AssetsLiabilitiesController::$isLO == 1) { ?> calculateTotalSumOfAll(this.value); <?php } else { ?> calculateTotalAssetsOwed(this.value,'loanModForm'); <?php } ?>"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetAcctPayableOwd); ?>"
                                        maxlength="40"
                                        tabindex="<?php echo assets_liabilities::$tabIndex++; ?>"
                                        autocomplete="off"
                                        placeholder="0.00"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'assetAcctPayableOwd', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetAcctPayableOwd); ?></label>
                            <?php } ?>
                        </div>
                    </div>
                </div>
                <div
                    class="form-group assetMarginOwd_disp <?php echo loanForm::showField('assetMarginOwd'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('assetMarginOwd'); ?>
                        </div>
                        <div class="col-4">
                            &nbsp;
                        </div>
                        <div class="col-4">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input
                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assetMarginOwd', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                        type="text"
                                        name="assetMarginOwd"
                                        id="assetMarginOwd"
                                        onblur="currencyConverter(this, this.value);"
                                        onchange="<?php if (AssetsLiabilitiesController::$isLO == 1) { ?> calculateTotalSumOfAll(this.value); <?php } else { ?> calculateTotalAssetsOwed(this.value,'loanModForm'); <?php } ?>"
                                        value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetMarginOwd); ?>"
                                        maxlength="40"
                                        tabindex="<?php echo assets_liabilities::$tabIndex++; ?>"
                                        autocomplete="off"
                                        placeholder="0.00"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'assetMarginOwd', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$assetMarginOwd); ?></label>
                            <?php } ?>
                        </div>
                    </div>
                </div>


                <!-- Other Liabilities -->
                <div
                    class="form-group otherLiabilitiesOwed_disp <?php echo loanForm::showField('otherLiabilitiesOwed'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('otherLiabilitiesOwed'); ?>
                        </div>
                        <div class="col-4">&nbsp;</div>
                        <div class="col-4">
                            <div
                                class="otherLiabilitiesOwed_disp <?php echo loanForm::showField('otherLiabilitiesOwed'); ?>">
                                <?php if (assets_liabilities::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input
                                            class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'otherLiabilitiesOwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                            type="text" name="otherLiabilitiesOwed" id="otherLiabilitiesOwed"
                                            onblur="currencyConverter(this, this.value);"
                                            onchange="<?php if (AssetsLiabilitiesController::$isLO == 1) { ?> calculateTotalSumOfAll(this.value); <?php } else { ?> calculateTotalAssetsOwed(this.value,'loanModForm'); <?php } ?>"
                                            value="<?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$otherLiabilitiesOwed); ?>"
                                            size="30"
                                            tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" autocomplete="off"
                                            placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'otherLiabilitiesOwed', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>/>
                                    </div>
                                <?php } else { ?>
                                    <label>$ <?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$otherLiabilitiesOwed); ?></label>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- // Other Liabilities // -->

                <!-- Other Assets Details -->
                <div
                    class="form-group otherDesc_disp <?php echo loanForm::showField('otherDesc'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('otherDesc'); ?>
                        </div>
                        <div class="col-8">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <textarea
                                    class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'otherDesc', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                    name="otherDesc" id="otherDesc"
                                    tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'otherDesc', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>><?php echo AssetsLiabilitiesController::$otherDescription ?></textarea>
                            <?php } else { ?>
                                <label><?php echo AssetsLiabilitiesController::$otherDescription; ?></label>
                            <?php } ?>
                        </div>
                    </div>
                </div>
                <!-- // Other Assets Details // -->
                <!-- Other Liability Details Desc -->
                <div
                    class="form-group otherLiabilityDetails_disp <?php echo loanForm::showField('otherLiabilityDetails'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('otherLiabilityDetails'); ?>
                        </div>
                        <div class="col-8">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <textarea
                                    class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'otherLiabilityDetails', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                    name="otherLiabilityDetails" id="otherLiabilityDetails"
                                    tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'otherLiabilityDetails', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>><?php echo AssetsLiabilitiesController::$otherLiabilityDetails; ?></textarea>
                            <?php } else { ?>
                                <label><?php echo AssetsLiabilitiesController::$otherLiabilityDetails; ?></label>
                            <?php } ?>
                        </div>
                    </div>
                </div>
                <!-- // Other Liability Details Desc // -->
                <!-- Unpaid Payable Taxes Desc -->
                <div
                    class="form-group unpaidPayableTaxesDesc_disp <?php echo loanForm::showField('unpaidPayableTaxesDesc'); ?>">
                    <div class="row">
                        <div class="col-4">
                            <?php echo loanForm::label('unpaidPayableTaxesDesc'); ?>
                        </div>
                        <div class="col-8">
                            <?php if (assets_liabilities::$allowToEdit) { ?>
                                <textarea
                                    class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'unpaidPayableTaxesDesc', 'sArr' => assets_liabilities::$secArr, 'opt' => 'M']); ?>"
                                    name="unpaidPayableTaxesDesc" id="unpaidPayableTaxesDesc"
                                    tabindex="<?php echo assets_liabilities::$tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'unpaidPayableTaxesDesc', 'sArr' => assets_liabilities::$secArr, 'opt' => 'I']); ?>><?php echo AssetsLiabilitiesController::$unpaidPayableTaxesDesc; ?></textarea>
                            <?php } else { ?>
                                <label><?php echo AssetsLiabilitiesController::$unpaidPayableTaxesDesc; ?></label>
                            <?php } ?>
                        </div>
                    </div>
                </div>
                <!-- // Unpaid Payable Taxes Desc // -->

                <div class="form-group py-2 bg-success-o-40">
                    <div class="row">
                        <div class="col-4 font-weight-boldest text-right"><h4>Total: </h4></div>
                        <div class="col-4 font-weight-boldest">
                            <h4>$
                                <span
                                    id="totalAssets"><?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$totalAssets); ?></span>
                            </h4>
                        </div>
                        <div class="col-4 font-weight-boldest">
                            <h4>$
                                <span
                                    id="totalAssetsOwed"><?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$totalAssetsOwed); ?></span>
                            </h4>
                        </div>
                    </div>
                </div>
                <div class="form-group py-2 bg-primary-o-40">
                    <div class="row">
                        <div class="col-4 font-weight-boldest"><h4 class="pl-2">Total Est. Value</h4></div>
                        <div class="col-4 font-weight-boldest"><h4>- Total Amt Owed</h4></div>
                        <div class="col-4 font-weight-boldest"><h4>= Total Net Worth</h4></div>
                    </div>
                </div>
                <div class="form-group py-2 bg-warning-o-40">
                    <div class="row">
                        <div class="col-4 font-weight-boldest">
                            <h4 class="pl-2">$ <span
                                    id="totalAssetsDisp"><?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$totalAssets); ?></span>
                            </h4>
                        </div>
                        <div class="col-4 font-weight-boldest">
                            <h4>- $ <span
                                    id="totalAssetsOwedDisp"><?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$totalAssetsOwed); ?></span>
                            </h4>
                        </div>
                        <div class="col-4 font-weight-boldest">
                            <h4>= $ <span
                                    id="totalAssetNetValue"><?php echo Currency::formatDollarAmountWithDecimal(AssetsLiabilitiesController::$totalAssetsNetValue); ?></span>
                            </h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php echo CustomField::RenderForTabSection(
            PageVariables::$PCID,
            tblFile::class,
            LMRequest::$LMRId,
            assets_liabilities::$sId,
            assets_liabilities::$fileTab,
            assets_liabilities::$activeTab,
            LMRequest::myFileInfo()->getFileTypes(),
            LMRequest::myFileInfo()->getLoanPrograms()
        ); ?>

    </div>
</div>
