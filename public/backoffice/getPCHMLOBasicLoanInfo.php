<?php

use models\composite\oPC\getPCHMLOBasicLoanInfoForFileLevel;
use models\constants\gl\glEntityTypeArray;
use models\constants\gl\glHMLOAmortization;
use models\constants\gl\glHMLOCreditScoreRange;
use models\constants\gl\glHMLOExitStrategy;
use models\constants\gl\glHMLOExtensionOption;
use models\constants\gl\glHMLOHouseType;
use models\constants\gl\glHMLOLoanTerms;
use models\constants\gl\glRateLockPeriod;
use models\constants\gl\gltypeOfHMLOLoanRequesting;
use models\constants\GpropertyTypeNumbArray;
use models\servicing\LoanTerms;
use models\standard\Arrays;
use models\standard\HTTP;

session_start();
require '../includes/util.php';

// http://dev.theloanpost.com/backoffice/getPCHMLOBasicLoanInfo.php?PCID=3198&loanPgm=BRL

$glHMLOHouseType = glHMLOHouseType::$glHMLOHouseType;
$glHMLOCreditScoreRange = glHMLOCreditScoreRange::$glHMLOCreditScoreRange;
$glHMLOLoanTerms = glHMLOLoanTerms::$glHMLOLoanTerms;
$glHMLOExtensionOption = glHMLOExtensionOption::$glHMLOExtensionOption;
$gltypeOfHMLOLoanRequesting = gltypeOfHMLOLoanRequesting::$gltypeOfHMLOLoanRequesting;
$glEntityTypeArray = glEntityTypeArray::$glEntityTypeArray;

$loanPgm = '';
$PCID = '';
$resultArray = $filetypes = [];
$stateArray = [];

$loanPgm = $_REQUEST['loanPgm'] ?? null;
$PCID = $_REQUEST['PCID'] ?? null;

$stateArray = Arrays::fetchStates();
/** Fetch all States **/
if ($loanPgm) {
    $resultArray = getPCHMLOBasicLoanInfoForFileLevel::getReport([
        'loanPgm' => $loanPgm,
        'PCID'    => $PCID,
    ]);
}
$glHMLOCreditScoreRange = glHMLOCreditScoreRange::getCreditScoreRange($PCID);
$filetypes = explode(' ', $_POST['ft']);
if (in_array('loc', $filetypes)) {
    $gltypeOfHMLOLoanRequesting[] = 'Equipment Financing';
}
$glHMLOAmortization = glHMLOAmortization::$glHMLOAmortization; // needs to be set to default
$glRateLockPeriod = glRateLockPeriod::$glRateLockPeriod;
if (in_array('EF', $filetypes) || in_array('loc', $filetypes)) {
    $glHMLOAmortization = [
        LoanTerms::INTEREST_ONLY,
        '1 Years',
        '2 Years', '3 Years', '4 Years', '5 Years', '6 Years',
        '7 Years', '8 Years', '9 Years', '10 Years', '15 Years',
        '20 Years', '25 Years', '30 Years', '40 Years'];
}


if (count($resultArray) > 0) {
    $resultArray['glHMLOExtensionOption'] = $glHMLOExtensionOption;
    $resultArray['propertyType'] = GpropertyTypeNumbArray::$GpropertyTypeNumbArray;
    $resultArray['useGLCS'] = 0;
    $resultArray['useGLEO'] = 0;
    $resultArray['useGLTT'] = 0;
    $resultArray['useGLLT'] = 0;
    $resultArray['useGLO'] = 0;
    $resultArray['usePROST'] = 0;
    $resultArray['usePTY'] = 0;
    $resultArray['useGLAmortization'] = 0;
    $resultArray['useGLRateLockPeriod'] = 0;
    $eachExitStrategyArray = [];

    $resultArray['customLoanGuidelinesExitStrategy'] = !empty($resultArray['customLoanGuidelinesExitStrategy']) ? array_combine(array_column($resultArray['customLoanGuidelinesExitStrategy'], 'exitStrategy'),array_column($resultArray['customLoanGuidelinesExitStrategy'], 'exitStrategy')) : glHMLOExitStrategy::getExitStrategy($PCID, $loanPgm);

    if (!array_key_exists('HMLOPCTransactionType', $resultArray)) {
        $resultArray['useGLTT'] = 1;
        $resultArray['HMLOPCTransactionType'] = $gltypeOfHMLOLoanRequesting;
    }
    if (!array_key_exists('HMLOPCOccupancy', $resultArray)) {
        $resultArray['useGLO'] = 1;
        $resultArray['HMLOPCOccupancy'] = $glHMLOHouseType;
    }
    if (!array_key_exists('HMLOPCExtnOption', $resultArray)) {
        $resultArray['useGLEO'] = 1;
        $resultArray['HMLOPCExtnOption'] = $glHMLOExtensionOption;
    }
    if (!array_key_exists('HMLOPCAmortization', $resultArray)) {
        $resultArray['useGLAmortization'] = 1;
        $resultArray['HMLOPCAmortization'] = $glHMLOAmortization;
    }
    if (!array_key_exists('HMLOPCBasicRateLockPeriodInfo', $resultArray)) {
        $resultArray['useGLRateLockPeriod'] = 1;
        $resultArray['HMLOPCBasicRateLockPeriodInfo'] = $glRateLockPeriod;
    }
    if (!array_key_exists('HMLOPCLoanTerm', $resultArray)) {
        $resultArray['useGLLT'] = 1;
        $resultArray['HMLOPCLoanTerm'] = $glHMLOLoanTerms;
    }
    if (!array_key_exists('HMLOPCState', $resultArray)) {
        $resultArray['HMLOPCState'] = $stateArray;
    }
    if (!array_key_exists('HMLOPCPropertyType', $resultArray)) {
        $resultArray['usePTY'] = 1;
        $resultArray['HMLOPCPropertyType'] = GpropertyTypeNumbArray::$GpropertyTypeNumbArray2;
    }
    if (!array_key_exists('HMLOPCEntityType', $resultArray)) {
        $resultArray['useGLET'] = 1;
        $resultArray['HMLOPCEntityType'] = $glEntityTypeArray;
    } else {
        $temp = $resultArray['HMLOPCEntityType'];
        $resultArray['HMLOPCEntityType'] = [];
        foreach ($temp as $item) {
            $resultArray['HMLOPCEntityType'][] = $item['entityType'];
        }
    }

    if (trim($resultArray['HMLOPCBasicLoanInfo'][0]['PCBorrCreditScoreRange']) == '') {
        $resultArray['useGLCS'] = 1;
        $resultArray['glHMLOCreditScore'] = $glHMLOCreditScoreRange;
    }

    if (trim($resultArray['HMLOPCBasicLoanInfo'][0]['rehabCostPercentageFinanced']) == '') {   //if Rehab/Construction % Financed in loan guide is empty then it should be 100 : card447
        $resultArray['HMLOPCBasicLoanInfo'][0]['rehabCostPercentageFinanced'] = 100;
    }
    $resultArray['alternate'] = 0;

    if(!empty($resultArray['HMLOPCFuturePropertyType'])) {
        foreach($resultArray['HMLOPCFuturePropertyType'] as $key => $value) {
            $futureType[$value['futurePropertyType']] = GpropertyTypeNumbArray::GpropertyTypeNumbArray[$value['futurePropertyType']];
        }
    } else {
        $futureType = GpropertyTypeNumbArray::$GpropertyTypeNumbArray2;
    }
    $resultArray['customLoanGuidelinesFuturePropertyType'] =  $futureType;
    unset($resultArray['HMLOPCFuturePropertyType']);

} else {
    $resultArray['customLoanGuidelinesFuturePropertyType'] = GpropertyTypeNumbArray::GpropertyTypeNumbArray;

    $resultArray['HMLOPCExtnOption'] = $glHMLOExtensionOption;
    $resultArray['HMLOPCTransactionType'] = $gltypeOfHMLOLoanRequesting;
    $resultArray['HMLOPCLoanTerm'] = $glHMLOLoanTerms;
    $resultArray['HMLOPCOccupancy'] = $glHMLOHouseType;
    $resultArray['HMLOPCState'] = $stateArray;
    $resultArray['HMLOPCPropertyType'] = GpropertyTypeNumbArray::$GpropertyTypeNumbArray2;
    $resultArray['glHMLOCreditScore'] = $glHMLOCreditScoreRange;
    $resultArray['HMLOPCAmortization'] = $glHMLOAmortization;
    $resultArray['HMLOPCEntityType'] = $glEntityTypeArray;
    $resultArray['useGLAmortization'] = 1;
    $resultArray['useGLRateLockPeriod'] = 1;
    $resultArray['HMLOPCBasicRateLockPeriodInfo'] = $glRateLockPeriod;
    $resultArray['useGLCS'] = 1;
    $resultArray['useGLEO'] = 1;
    $resultArray['useGLTT'] = 1;
    $resultArray['useGLLT'] = 1;
    $resultArray['useGLO'] = 1;
    $resultArray['usePROST'] = 1;
    $resultArray['usePTY'] = 1;
    $resultArray['useGLET'] = 1;
    $resultArray['alternate'] = 1;
    $resultArray['customLoanGuidelinesExitStrategy'] = glHMLOExitStrategy::getExitStrategy($PCID, $loanPgm);
}
$resultArray['fileType'] = $_POST['ft'];

HTTP::ExitJSON($resultArray);

