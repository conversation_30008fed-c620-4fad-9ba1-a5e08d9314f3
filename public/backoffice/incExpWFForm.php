<?php
global $myFileInfo, $incomeInfo, $LMRId, $isHMLO, $totalHouseHoldIncome, $totalHouseHoldExpenses,
       $totalGrossMonthlyHouseHoldIncome, $netSocialSecurity1, $socialSecurity1, $netPensionOrRetirement1,
       $pensionOrRetirement1, $netDisability1, $disability1, $netUnemployment1, $unemployment1, $netRental1,
       $rental1, $netEarnedInterest1, $earnedInterest1, $netRoomRental1, $roomRental1, $netSecondJobIncome1,
       $secondJobIncome1, $netSocialSecurity2, $socialSecurity2, $netPensionOrRetirement2, $pensionOrRetirement2,
       $netDisability2, $disability2, $netUnemployment2, $unemployment2, $netRental2, $rental2, $netEarnedInterest2,
       $earnedInterest2, $netRoomRental2, $roomRental2,
       $netSecondJobIncome2, $secondJobIncome2, $assetId, $isLO, $HMLOLoanInfoSectionsDisp, $fileTab, $fieldsInfo,
       $tabIndex, $allowToEdit, $coBorDisp, $capitalGains1, $partnership1,
       $otherIncomeDescription1, $primTotalNetHouseHoldIncome, $capitalGains2, $partnership2, $otherIncomeDescription2,
       $coTotalNetHouseHoldIncome, $HMLORealEstateTaxes, $activeTab, $HOAFees1, $totalInsurance, $otherMortgage1,
       $otherMortgageBalance1, $unsecuredLoans1, $unsecuredLoanBalance1, $childSupportOrAlimonyMonthly1,
       $childSupportOrAlimonyMonthlyBalance1, $other1, $creditCards1, $creditCardsBalance1, $otherBalance1,
       $primTotalHouseHoldExpenses, $otherMortgage2, $otherMortgageBalance2, $unsecuredLoanBalance2, $creditCards2,
       $studentLoans1, $studentLoansBalance1, $unsecuredLoans2, $creditCardsBalance2, $studentLoans2, $studentLoansBalance2,
       $childSupportOrAlimonyMonthly2, $childSupportOrAlimonyMonthlyBalance2, $other2, $otherBalance2, $coTotalHouseHoldExpenses;
global $stateArray, $op, $expFedTax, $expStateTax, $expRentalPay, $expMortgPayResi, $expMortgPayInvest, $expPropTaxResi,
       $expPropTaxInvest, $expLoanPayments, $expIns, $expInvestments, $expTuition, $expOtherLiving, $expMedical, $fileMC;

global $expFedTaxOwed, $expStateTaxOwed, $expRentalPayOwed, $expMortgPayResiOwed, $expMortgPayInvestOwed;
global $expPropTaxResiOwed, $expPropTaxInvestOwed, $expLoanPaymentsOwed, $expInsOwed, $expInvestmentsOwed;
global $expTuitionOwed, $expOtherLivingOwed, $expMedicalOwed;

use models\composite\proposalFormula;
use models\constants\employedInfo1Array;
use models\constants\gl\glCountryArray;
use models\constants\gl\glIncomeFromOtherSource;
use models\constants\hide1003FieldsArray;
use models\constants\states;
use models\Controllers\loanForm;
use models\cypher;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;

$employedInfo1Array = employedInfo1Array::$employedInfo1Array;
$hide1003FieldsArray = hide1003FieldsArray::$hide1003FieldsArray;
$glCountryArray = glCountryArray::$glCountryArray;
$glIncomeFromOtherSource = glIncomeFromOtherSource::$glIncomeFromOtherSource;

$netDefaultText = '- Net -';
$grossDefaultText = '- Gross -';
$lien1Payment = '';
$lien2Payment = '';
$paidOften = '';
$paidOften1 = '';
$taxes1 = 0;
$isTaxesInsEscrowed = '';
$mortgageDelinquencyAmount = '';
$QAInfo = [];
$borEmploymentInfo = [];
$coBEmploymentInfo = [];
$fileLOExpensesInfo = [];
$YRF4506TDate1 = '';
$YRF4506TDate2 = '';
$YRF4506TDate3 = '';
$YRF4506TDate4 = '';

$emptypeshare1 = '';
$empmonthlyincome1 = '';

$emptypeshare2 = '';
$empmonthlyincome2 = '';
$employedByOtherParty = 0;
$ownerOrSelfEmpoyed = 0;

if (count($myFileInfo) > 0) {
    $lien1Payment = Strings::showField('lien1Payment', 'LMRInfo');
    $lien2Payment = Strings::showField('lien2Payment', 'LMRInfo');
    $paidOften = Strings::showField('paidOften', 'incomeInfo');
    $paidOften1 = Strings::showField('paidOften1', 'incomeInfo');
    $floodInsurance1 = Strings::showField('floodInsurance1', 'incomeInfo');
    $taxes1 = Strings::showField('taxes1', 'incomeInfo');
    $isTaxesInsEscrowed = Strings::showField('isTaxesInsEscrowed', 'fileHMLONewLoanInfo');

    if (array_key_exists('QAInfo', $myFileInfo)) $QAInfo = $myFileInfo['QAInfo'];
    /** Fetch Q+A info **/

    if (array_key_exists('borEmploymentInfo', $myFileInfo)) $borEmploymentInfo = $myFileInfo['borEmploymentInfo'];

    if (array_key_exists('coBEmploymentInfo', $myFileInfo)) $coBEmploymentInfo = $myFileInfo['coBEmploymentInfo'];

    if (array_key_exists('fileLOExpensesInfo', $myFileInfo)) $fileLOExpensesInfo = $myFileInfo['fileLOExpensesInfo'];

    $employedByOtherParty = Strings::showField('employedByOtherParty', 'incomeInfo');
    $ownerOrSelfEmpoyed = Strings::showField('ownerOrSelfEmpoyed', 'incomeInfo');
}

if (count($myFileInfo) > 0) {
    $creditorInfo = $myFileInfo['creditorInfo'];
    $assetsInfo = $myFileInfo['AssetsInfo'];
}

require 'incExpCalculation.php';
/** Income & expenses calculation for a file **/


if (count($incomeInfo) > 0) {
    $houseHoldExpensesNotes = trim($incomeInfo['houseHoldExpensesNotes']);
    $borrowerHireDate = trim($incomeInfo['borrowerHireDate']);
    $coBorrowerHireDate = trim($incomeInfo['coBorrowerHireDate']);
    $unemploymentStDate1 = trim($incomeInfo['unemploymentStDate1']);
    $secondJobStDate1 = trim($incomeInfo['secondJobStDate1']);
    $unemploymentStDate2 = trim($incomeInfo['unemploymentStDate2']);
    $secondJobStDate2 = trim($incomeInfo['secondJobStDate2']);

    $borSocialSecurity = trim($incomeInfo['borSocialSecurity']);
    $coBSocialSecurity = trim($incomeInfo['coBSocialSecurity']);

    $emptypeshare1 = trim($incomeInfo['emptypeshare1']);
    $empmonthlyincome1 = trim($incomeInfo['empmonthlyincome1']);
    $emptypeshare2 = trim($incomeInfo['emptypeshare2']);
    $empmonthlyincome2 = trim($incomeInfo['empmonthlyincome2']);


    if ($borrowerHireDate == NULL || $borrowerHireDate == '' || $borrowerHireDate == '0000-00-00') {
        $borrowerHireDate = '';
    } else {
        $borrowerHireDate = Dates::formatDateWithRE($borrowerHireDate, 'YMD', 'm/d/Y');
    }
    if ($coBorrowerHireDate == NULL || $coBorrowerHireDate == '' || $coBorrowerHireDate == '0000-00-00') {
        $coBorrowerHireDate = '';
    } else {
        $coBorrowerHireDate = Dates::formatDateWithRE($coBorrowerHireDate, 'YMD', 'm/d/Y');
    }
    if ($unemploymentStDate1 == NULL || $unemploymentStDate1 == '' || $unemploymentStDate1 == '0000-00-00') {
        $unemploymentStDate1 = '';
    } else {
        $unemploymentStDate1 = Dates::formatDateWithRE($unemploymentStDate1, 'YMD', 'm/d/Y');
    }
    if ($secondJobStDate1 == NULL || $secondJobStDate1 == '' || $secondJobStDate1 == '0000-00-00') {
        $secondJobStDate1 = '';
    } else {
        $secondJobStDate1 = Dates::formatDateWithRE($secondJobStDate1, 'YMD', 'm/d/Y');
    }

    if ($unemploymentStDate2 == NULL || $unemploymentStDate2 == '' || $unemploymentStDate2 == '0000-00-00') {
        $unemploymentStDate2 = '';
    } else {
        $unemploymentStDate2 = Dates::formatDateWithRE($unemploymentStDate2, 'YMD', 'm/d/Y');
    }
    if ($secondJobStDate2 == NULL || $secondJobStDate2 == '' || $secondJobStDate2 == '0000-00-00') {
        $secondJobStDate2 = '';
    } else {
        $secondJobStDate2 = Dates::formatDateWithRE($secondJobStDate2, 'YMD', 'm/d/Y');
    }

    $employer1Phone = Strings::showField('employer1Phone', 'incomeInfo');
    $employer2Phone = Strings::showField('employer2Phone', 'incomeInfo');
    $employer1PhoneArray = Strings::splitPhoneNumber($employer1Phone);
    $employer2PhoneArray = Strings::splitPhoneNumber($employer2Phone);
    if (count($employer1PhoneArray) > 0) {
        $employer1Phone1 = trim($employer1PhoneArray['No1']);
        $employer1Phone2 = trim($employer1PhoneArray['No2']);
        $employer1Phone3 = trim($employer1PhoneArray['No3']);
        $employer1PhoneExt = trim($employer1PhoneArray['Ext']);
    }
    if (count($employer2PhoneArray) > 0) {
        $employer2Phone1 = trim($employer2PhoneArray['No1']);
        $employer2Phone2 = trim($employer2PhoneArray['No2']);
        $employer2Phone3 = trim($employer2PhoneArray['No3']);
        $employer2PhoneExt = trim($employer2PhoneArray['Ext']);
    }
}


if ($LMRId > 0) {

    $lien1DTI = '';
    $lien2DTI = '';
    $mortgageOwner1 = '';
    $mortgageOwner1 = Strings::showField('mortgageOwner1', 'LMRInfo');

    /*** Remove  the mortgage insurance from calculating DTI - on Sep 03, 2015 ***/
    if ($mortgageOwner1 == '3') {
        /*** Include the mortgage insurance while calculating DTI if the mortgage type/owner is FHA - on May 04, 2016 ***/
        $tempLien1PaymentPITIA = proposalFormula::calculatePaymentPI(Strings::showField('lien1Payment', 'LMRInfo'), Strings::showField('taxes1', 'incomeInfo'), Strings::showField('insurance1', 'incomeInfo'), Strings::showField('HOAFees1', 'incomeInfo'), Strings::showField('mortgageInsurance1', 'incomeInfo'), Strings::showField('floodInsurance1', 'incomeInfo'));
    } else {
        $tempLien1PaymentPITIA = proposalFormula::calculatePaymentPI(Strings::showField('lien1Payment', 'LMRInfo'), Strings::showField('taxes1', 'incomeInfo'), Strings::showField('insurance1', 'incomeInfo'), Strings::showField('HOAFees1', 'incomeInfo'), 0, Strings::showField('floodInsurance1', 'incomeInfo'));
    }
    if ($isHMLO == 1) { /* Calculate separate DTI for HMLO */
        $lien1DTI = proposalFormula::calculateDTIForHMLO($totalHouseHoldIncome, $totalHouseHoldExpenses);
    } else {
        $lien1DTI = proposalFormula::calculateDTI($tempLien1PaymentPITIA, 0, $totalGrossMonthlyHouseHoldIncome);
    }
}

$presentRent = '';
$proposedRent = '';
$presentFirstMortgage = '';
$proposedFirstMortgage = '';
$presentOtherFinancing = '';
$proposedOtherFinancing = '';
$presentHazardInsurance = '';
$proposedHazardInsurance = '';
$presentRealEstateTaxes = '';
$proposedRealEstateTaxes = '';
$presentMortgageInsurance = '';
$proposedMortgageInsurance = '';
$presentHomeownerAssnDues = '';
$proposedHomeownerAssnDues = '';
$presentOther = '';
$proposedOther = '';

if (count($fileLOExpensesInfo) > 0) {
    $presentRent = trim($fileLOExpensesInfo['presentRent']);
    $proposedRent = trim($fileLOExpensesInfo['proposedRent']);
    $presentFirstMortgage = trim($fileLOExpensesInfo['presentFirstMortgage']);
    $proposedFirstMortgage = trim($fileLOExpensesInfo['proposedFirstMortgage']);
    $presentOtherFinancing = trim($fileLOExpensesInfo['presentOtherFinancing']);
    $proposedOtherFinancing = trim($fileLOExpensesInfo['proposedOtherFinancing']);

    $presentHazardInsurance = trim($fileLOExpensesInfo['presentHazardInsurance']);
    $proposedHazardInsurance = trim($fileLOExpensesInfo['proposedHazardInsurance']);
    $presentRealEstateTaxes = trim($fileLOExpensesInfo['presentRealEstateTaxes']);
    $proposedRealEstateTaxes = trim($fileLOExpensesInfo['proposedRealEstateTaxes']);
    $presentMortgageInsurance = trim($fileLOExpensesInfo['presentMortgageInsurance']);
    $proposedMortgageInsurance = trim($fileLOExpensesInfo['proposedMortgageInsurance']);
    $presentHomeownerAssnDues = trim($fileLOExpensesInfo['presentHomeownerAssnDues']);
    $proposedHomeownerAssnDues = trim($fileLOExpensesInfo['proposedHomeownerAssnDues']);
    $presentOther = trim($fileLOExpensesInfo['presentOther']);
    $proposedOther = trim($fileLOExpensesInfo['proposedOther']);
}


if (count($QAInfo) > 0) {
    $mortgageDelinquencyAmount = Strings::showField('mortgageDelinquencyAmount', 'QAInfo');
    $YRF4506TDate1 = Strings::showField('YRF4506TDate1', 'QAInfo'); // YRF - Year requested For 4506T
    $YRF4506TDate2 = Strings::showField('YRF4506TDate2', 'QAInfo');
    $YRF4506TDate3 = Strings::showField('YRF4506TDate3', 'QAInfo');
    $YRF4506TDate4 = Strings::showField('YRF4506TDate4', 'QAInfo');

    if ($YRF4506TDate1 == NULL || $YRF4506TDate1 == '' || $YRF4506TDate1 == '0000-00-00') {
        $YRF4506TDate1 = '';
    } else {
        $YRF4506TDate1 = Dates::formatDateWithRE($YRF4506TDate1, 'YMD', 'm/d/Y');
    }
    if ($YRF4506TDate2 == NULL || $YRF4506TDate2 == '' || $YRF4506TDate2 == '0000-00-00') {
        $YRF4506TDate2 = '';
    } else {
        $YRF4506TDate2 = Dates::formatDateWithRE($YRF4506TDate2, 'YMD', 'm/d/Y');
    }
    if ($YRF4506TDate3 == NULL || $YRF4506TDate3 == '' || $YRF4506TDate3 == '0000-00-00') {
        $YRF4506TDate3 = '';
    } else {
        $YRF4506TDate3 = Dates::formatDateWithRE($YRF4506TDate3, 'YMD', 'm/d/Y');
    }
    if ($YRF4506TDate4 == NULL || $YRF4506TDate4 == '' || $YRF4506TDate4 == '0000-00-00') {
        $YRF4506TDate4 = '';
    } else {
        $YRF4506TDate4 = Dates::formatDateWithRE($YRF4506TDate4, 'YMD', 'm/d/Y');
    }
}
if (trim($netSocialSecurity1) == '') $netSocialSecurity1 = $netDefaultText;
if (trim($socialSecurity1) == '') $socialSecurity1 = $grossDefaultText;
if (trim($netPensionOrRetirement1) == '') $netPensionOrRetirement1 = $netDefaultText;
if (trim($pensionOrRetirement1) == '') $pensionOrRetirement1 = $grossDefaultText;
if (trim($netDisability1) == '') $netDisability1 = $netDefaultText;
if (trim($disability1) == '') $disability1 = $grossDefaultText;
if (trim($netUnemployment1) == '') $netUnemployment1 = $netDefaultText;
if (trim($unemployment1) == '') $unemployment1 = $unemployment1;
if (trim($netRental1) == '') $netRental1 = $netDefaultText;
if (trim($rental1) == '') $rental1 = $grossDefaultText;
if (trim($netEarnedInterest1) == '') $netEarnedInterest1 = $netDefaultText;
if (trim($earnedInterest1) == '') $earnedInterest1 = $grossDefaultText;
if (trim($netRoomRental1) == '') $netRoomRental1 = $netDefaultText;
if (trim($roomRental1) == '') $roomRental1 = $grossDefaultText;
if (trim($netSecondJobIncome1) == '') $netSecondJobIncome1 = $netDefaultText;
if (trim($secondJobIncome1) == '') $secondJobIncome1 = $grossDefaultText;

if (trim($netSocialSecurity2) == '') $netSocialSecurity2 = $netDefaultText;
if (trim($socialSecurity2) == '') $socialSecurity2 = $grossDefaultText;
if (trim($netPensionOrRetirement2) == '') $netPensionOrRetirement2 = $netDefaultText;
if (trim($pensionOrRetirement2) == '') $pensionOrRetirement2 = $grossDefaultText;
if (trim($netDisability2) == '') $netDisability2 = $netDefaultText;
if (trim($disability2) == '') $disability2 = $grossDefaultText;
if (trim($netUnemployment2) == '') $netUnemployment2 = $netDefaultText;
if (trim($unemployment2) == '') $unemployment2 = $unemployment2;
if (trim($netRental2) == '') $netRental2 = $netDefaultText;
if (trim($rental2) == '') $rental2 = $grossDefaultText;
if (trim($netEarnedInterest2) == '') $netEarnedInterest2 = $netDefaultText;
if (trim($earnedInterest2) == '') $earnedInterest2 = $grossDefaultText;
if (trim($netRoomRental2) == '') $netRoomRental2 = $netDefaultText;
if (trim($roomRental2) == '') $roomRental2 = $grossDefaultText;
if (trim($netSecondJobIncome2) == '') $netSecondJobIncome2 = $netDefaultText;
if (trim($secondJobIncome2) == '') $secondJobIncome2 = $grossDefaultText;

$employer2State = Strings::showField('employer2State', 'incomeInfo');
?>
</script>
<input type="hidden" name="grossText" value="<?php echo $grossDefaultText ?>"/>
<input type="hidden" name="netText" value="<?php echo $netDefaultText ?>">
<input type="hidden" name="isCoBorrowerExists" id="isCoBorrowerExists"
       value="<?php echo Strings::showField('isCoBorrower', 'LMRInfo') ?>"/>
<input type="hidden" name="mortgage1MonthlyPayment" id="mortgage1MonthlyPayment"
       value="<?php echo Strings::showField('lien1Payment', 'LMRInfo') ?>"/>
<input type="hidden" name="mortgage2MonthlyPayment" id="mortgage2MonthlyPayment"
       value="<?php echo Strings::showField('lien2Payment', 'LMRInfo') ?>"/>
<input type="hidden" name="assetId" value="<?php echo $assetId ?>"/>
<input type="hidden" name="lien1PaymentPITIAValue" value="<?php echo $tempLien1PaymentPITIA ?>"/>
<input type="hidden" name="isLOOpt" value="<?php echo $isLO ?>"/>
<!--<input type="hidden" name="isHMLOOpt" id="isHMLOOpt" value="--><?php //echo $isHMLO ?><!--"/>-->
<input type="hidden" name="additionalEmpCnt" value="<?php echo count($borEmploymentInfo) ?>"/>
<input type="hidden" name="additionalCoBEmpCnt" value="<?php echo count($coBEmploymentInfo) ?>"/>


<?php
$secArr = BaseHTML::sectionAccess2(['sId' => 'BEI', 'opt' => $fileTab, 'activeTab' => $activeTab]); // Get Active Fields only...
loanForm::pushSectionID('BEI');

if ($activeTab == '1003') {  /* story-31529 hide some fields based on the requirement mentioned in story*/
    foreach ($hide1003FieldsArray['BEI'] as $hideFieldkey => $hideFieldValue) {
        if (array_key_exists($hideFieldValue, $secArr)) {
            if(is_array($secArr[$hideFieldValue])) {
                $secArr[$hideFieldValue]['fieldDisplay'] = 0;
                loanForm::hideField('BEI', $hideFieldValue);
            }
        }
    }
    foreach ($hide1003FieldsArray['CBEI'] as $hideFieldkey => $hideFieldValue) {
        if (array_key_exists($hideFieldValue, $secArr)) {
            if(is_array($secArr[$hideFieldValue])) {
                $secArr[$hideFieldValue]['fieldDisplay'] = 0;
                loanForm::hideField('CBEI', $hideFieldValue);
            }
        }
    }
}
?>
<!-- removed class "BEI" to avoid the popup on clicks -->
<div class="card card-custom HMLOLoanInfoSections  BEI <?php if (count(Arrays::getValueFromArray('BEI', $fieldsInfo, $activeTab)) <= 0) {
    echo 'secHide';
} ?>" style="<?php echo $HMLOLoanInfoSectionsDisp; ?>">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label">
                <?php echo BaseHTML::getSectionHeading('BEI'); ?>
            </h3>
            <?php if (trim(BaseHTML::getSectionTooltip('BEI')) != '') { ?>
                <i class="popoverClass fas fa-info-circle text-primary ml-2 "
                   data-html="true"
                   data-content="<?php echo BaseHTML::getSectionTooltip('BEI'); ?>"></i>
            <?php } ?>
        </div>
        <div class="card-toolbar">
                <span class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                      data-card-tool="toggle"
                      data-section="GOVTCBCard"
                      data-toggle="tooltip"
                      data-placement="top" title="Toggle Card">
                    <i class="ki ki-arrow-down icon-nm"></i>
                </span>
            <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none"
                  data-card-tool="reload"
                  data-toggle="tooltip"
                  data-placement="top"
                  title="Reload Card">
                    <i class="ki ki-reload icon-nm"></i>
                </span>
            <span class="btn btn-icon btn-sm btn-hover-light-primary  d-none"
                  data-card-tool="remove"
                  data-toggle="tooltip"
                  data-placement="top"
                  title="Remove Card">
                    <i class="ki ki-close icon-nm"></i>
                </span>
        </div>
    </div>
    <div class="card-body ">
        <div class="row ">
            <?php if ($hideThisField) { ?>
                <div class=" col-md-3 occupation1_disp <?php echo loanForm::showField('occupation1'); ?>">
                    <div class="row form-group">
                        <?php echo loanForm::label('occupation1', 'col-md-12 '); ?>
                        <div class="col-md-12">
                            <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'occupation1', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   type="text"
                                   name="occupation1"
                                   id="occupation1"
                                   value="<?php echo htmlentities(Strings::showField('occupation1', 'incomeInfo')); ?>"
                                   maxlength="30"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'occupation1', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                        </div>
                    </div>
                </div>
            <?php } ?>
            <div class=" col-md-3 employedInfo1_disp <?php echo loanForm::showField('employedInfo1'); ?>">
                <div class="row form-group">
                    <?php echo loanForm::label('employedInfo1', 'col-md-12 '); ?>
                    <div class="col-md-12">
                        <select name="employedInfo1"
                                id="employedInfo1"
                                TABINDEX="<?php echo $tabIndex++; ?>"
                                class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'employedInfo1', 'sArr' => $secArr, 'opt' => 'M']); ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'employedInfo1', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                            <option value=""> - Select -</option>
                            <?php
                            for ($em1 = 1; $em1 <= count($employedInfo1Array); $em1++) {
                                $optSel = '';
                                $optSel = Arrays::isSelected($employedInfo1Array[$em1], Strings::showField('employedInfo1', 'incomeInfo'));
                                echo "<option value=\"$employedInfo1Array[$em1]\"" . $optSel . '>' . $employedInfo1Array[$em1] . "</option>\n";
                            }
                            ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class=" col-md-3 borrowerHireDate_disp <?php echo loanForm::showField('borrowerHireDate'); ?>">
                <div class="row form-group">
                    <?php echo loanForm::label('borrowerHireDate', 'col-md-12 '); ?>
                    <div class="col-md-12">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary"></i>
                                    </span>
                            </div>
                            <input class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'borrowerHireDate', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass"
                                   name="borrowerHireDate"
                                   id="borrowerHireDate"
                                   value="<?php echo $borrowerHireDate ?>"
                                   placeholder="MM/DD/YYYY" type="text"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'borrowerHireDate', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                   TABINDEX="<?php echo $tabIndex++; ?>"/>
                        </div>
                    </div>
                </div>
            </div>
            <?php if ($hideThisField) { ?>
                <div class=" col-md-3 borEmpBusiness_disp <?php echo loanForm::showField('borEmpBusiness'); ?>">
                    <div class="row form-group">
                        <?php echo loanForm::label('borEmpBusiness', 'col-md-12 '); ?>
                        <div class="col-md-12">
                            <textarea
                                    class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'borEmpBusiness', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    type="text"
                                    name="borEmpBusiness"
                                    id="borEmpBusiness"
                                    TABINDEX="<?php echo $tabIndex++; ?>"
                                    maxlength="128"
                                    autocomplete="off"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'borEmpBusiness', 'sArr' => $secArr, 'opt' => 'I']); ?>><?php echo Strings::showField('borEmpBusiness', 'incomeInfo') ?></textarea>
                        </div>
                    </div>
                </div>
            <?php } ?>
        </div>

        <!-- borrower self-employed start -->

        <div class="row form-group  borpg_employmentType_Disp  <?php echo loanForm::showField('employedInfo1'); ?>">

            <div class="col-md-6 <?php echo loanForm::showField('ownershipShare'); ?>">
                <div class="radio-inline">
                    <label class="radio radio-solid font-weight-bold">
                        <input type="radio"
                               name="emptypeshare1"
                               id="emptypeshare1"
                               value="lessthan25"
                            <?php echo Strings::isChecked('lessthan25', $emptypeshare1); ?> >
                        <span></span>I
                        have
                        ownership share of less than 25%
                    </label>
                    <label class="radio radio-solid font-weight-bold">
                        <input type="radio"
                               name="emptypeshare1"
                               id="emptypeshare1more"
                               value="eqmorethan25"
                            <?php echo Strings::isChecked('eqmorethan25', $emptypeshare1); ?> >
                        <span></span>I
                        have
                        ownership share of 25% or more
                    </label>
                </div>
            </div>

            <div class=" col-md-6 <?php echo loanForm::showField('empmonthlyincome1'); ?>">
                <div class="row">
                    <?php echo loanForm::label('empmonthlyincome1', 'col-md-5 '); ?>
                    <div class="col-md-6">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="number"
                                   name="empmonthlyincome1"
                                   id="empmonthlyincome1"
                                   onkeyup='return numericValues(this)'
                                   class="form-control input-sm"
                                   value="<?php echo $empmonthlyincome1; ?>"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- borrower self-employed end -->

        <div class="row form-group paidOften_disp <?php echo loanForm::showField('paidOften'); ?>">
            <?php echo loanForm::label('paidOften', 'col-md-3 '); ?>
            <div class="col-md-9">
                <div class="radio-inline">
                    <label class="radio radio-solid font-weight-bold"
                           for="paidOftenWeekly">
                        <input type="radio"
                               name="paidOften"
                               value="weekly"
                               id="paidOftenWeekly"
                            <?php echo Strings::isChecked('weekly', $paidOften); ?>
                               class=" <?php echo BaseHTML::fieldAccess(['fNm' => 'paidOften', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'paidOften', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                        <span></span>
                        Weekly
                    </label>
                    <label class="radio radio-solid font-weight-bold"
                           for="paidOftenmonthly">
                        <input type="radio"
                               name="paidOften"
                               value="monthly"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               id="paidOftenmonthly"
                            <?php echo Strings::isChecked('monthly', $paidOften); ?>
                               class=" <?php echo BaseHTML::fieldAccess(['fNm' => 'paidOften', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'paidOften', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                        <span></span>
                        Monthly
                    </label>
                    <label class="radio radio-solid font-weight-bold"
                           for="paidOftenevery2Weeks">
                        <input type="radio"
                               name="paidOften"
                               value="every2Weeks"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               id="paidOftenevery2Weeks"
                            <?php echo Strings::isChecked('every2Weeks', $paidOften); ?>
                               class=" <?php echo BaseHTML::fieldAccess(['fNm' => 'paidOften', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'paidOften', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                        <span></span>
                        Every 2 weeks
                    </label>
                    <label class="radio radio-solid font-weight-bold" for="paidOftentwiceMonth">
                        <input type="radio"
                               name="paidOften"
                               value="twiceMonth"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               id="paidOftentwiceMonth"
                            <?php echo Strings::isChecked('twiceMonth', $paidOften); ?>
                               class=" <?php echo BaseHTML::fieldAccess(['fNm' => 'paidOften', 'sArr' => $secArr, 'opt' => 'M']); ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'paidOften', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                        <span></span>
                        Twice a month
                    </label>
                </div>
            </div>
        </div>


        <div class="row form-group employerInfo_disp <?php echo loanForm::showField('employerInfo'); ?>">
            <div class=" col-lg-12 m-0 px-0">
                <?php echo loanForm::label('employerInfo', 'bg-secondary  py-2  col-lg-12 font-weight-bolder'); ?>
            </div>
        </div>

        <div class="row">
            <?php if ($hideThisField) { ?>
                <div class=" col-md-6 employer1_disp <?php echo loanForm::showField('employer1'); ?>">
                    <div class="row form-group">
                        <?php echo loanForm::label('employer1', 'col-md-12 '); ?>
                        <div class="col-md-12">
                            <input class="form-control input-sm<?php echo BaseHTML::fieldAccess(['fNm' => 'employer1', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   type="text"
                                   name="employer1"
                                   id="employer1"
                                   value="<?php echo htmlentities(Strings::showField('employer1', 'incomeInfo')); ?>"
                                   maxlength="30"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'employer1', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                        </div>
                    </div>
                </div>
            <?php } ?>

            <div class=" col-md-6 yearsAtJob1_disp <?php echo loanForm::showField('yearsAtJob1'); ?>">
                <div class="row form-group">
                    <?php echo loanForm::label('yearsAtJob1', 'col-md-12 '); ?>
                    <div class="col-md-12">
                        <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'yearsAtJob1', 'sArr' => $secArr, 'opt' => 'M']); ?>>"
                               type="number"
                               name="yearsAtJob1"
                               id="yearsAtJob1"
                               value="<?php echo Strings::showField('yearsAtJob1', 'incomeInfo') ?>" maxlength="30"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'yearsAtJob1', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                    </div>
                </div>
            </div>

            <?php if ($hideThisField) { ?>
                <div class=" col-md-6 employer1Add_disp <?php echo loanForm::showField('employer1Add'); ?>">
                    <div class="row form-group">
                        <?php echo loanForm::label('employer1Add', 'col-md-12 '); ?>
                        <div class="col-md-12">
                            <textarea
                                    class="form-control input-sm validateMaxLength <?php echo BaseHTML::fieldAccess(['fNm' => 'employer1Add', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    name="employer1Add"
                                    id="employer1Add"
                                    TABINDEX="<?php echo $tabIndex++; ?>"
                                    rows="2"
                                    cols="20"
                                    maxlength="<?php echo loanForm::getFieldLength('employer1Add', 'tblIncomeInfo'); ?>"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'employer1Add', 'sArr' => $secArr, 'opt' => 'I']); ?>><?php echo Strings::showField('employer1Add', 'incomeInfo') ?></textarea>
                        </div>
                    </div>
                </div>
            <?php } ?>

            <?php if ($hideThisField) { ?>
                <div class=" col-md-6  <?php echo loanForm::showField('employer1City'); ?>">
                    <div class="row form-group">
                        <?php echo loanForm::label('employer1City', 'col-md-12 '); ?>
                        <div class="col-md-12">
                            <?php echo loanForm::text(
                                'employer1City',
                                true,
                                $tabIndex++,
                                Strings::showField('employer1City', 'incomeInfo')
                            ); ?>
                        </div>
                    </div>
                </div>
            <?php } ?>

            <div class=" col-md-6  <?php echo loanForm::showField('employer1State'); ?>">
                <div class="row form-group">
                    <?php echo loanForm::label('employer1State', 'col-md-12 '); ?>
                    <div class="col-md-12">
                        <?php echo loanForm::select(
                            'employer1State',
                            true,
                            $tabIndex++,
                            Strings::showField('employer1State', 'incomeInfo'),
                            states::getOptions()
                        ); ?>
                    </div>
                </div>
            </div>

            <div class=" col-md-6  <?php echo loanForm::showField('employer1Zip'); ?>">
                <div class="row form-group">
                    <?php echo loanForm::label('employer1Zip', 'col-md-12 '); ?>
                    <div class="col-md-12">
                        <?php echo loanForm::text(
                            'employer1Zip',
                            true,
                            $tabIndex++,
                            Strings::showField('employer1Zip', 'incomeInfo'),
                            'zipCode'
                        ); ?>
                    </div>
                </div>
            </div>

            <div class=" col-md-6 employer1Country_disp <?php echo loanForm::showField('employer1Country'); ?>">
                <div class="row form-group">
                    <?php echo loanForm::label('employer1Country', 'col-md-12 '); ?>
                    <div class="col-md-12">
                        <?php echo loanForm::select(
                            'employer1Country',
                            true,
                            $tabIndex++,
                            Strings::showField('employer1Country', 'incomeInfo'),
                            glCountryArray::$options
                        ); ?>
                    </div>
                </div>
            </div>
            <div class=" col-md-6 employedByOtherParty_disp <?php echo loanForm::showField('employedByOtherParty'); ?>">
                <div class="checkbox-inline pt-10">
                    <label class="checkbox">
                        <input type="checkbox"
                               name="employedByOtherParty"
                               id="employedByOtherParty"
                               value="1"
                               class="employedByOtherParty <?php echo BaseHTML::fieldAccess(['fNm' => 'employedByOtherParty', 'sArr' => $secArr, 'opt' => 'M']); ?>" <?php if ($employedByOtherParty == 1) {
                            echo 'checked=checked';
                        } ?> >
                        <span></span>
                        <?php echo BaseHTML::fieldAccess(['fNm' => 'employedByOtherParty', 'sArr' => $secArr, 'opt' => 'L']); ?>
                    </label>
                </div>
            </div>
            <div class=" col-md-6 ownerOrSelfEmpoyed_disp <?php echo loanForm::showField('ownerOrSelfEmpoyed'); ?>">
                <div class="checkbox-inline pt-10">
                    <label class="checkbox">
                        <input type="checkbox"
                               name="ownerOrSelfEmpoyed"
                               id="ownerOrSelfEmpoyed"
                               value="1"
                               class="ownerOrSelfEmpoyed <?php echo BaseHTML::fieldAccess(['fNm' => 'ownerOrSelfEmpoyed', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                            <?php if ($ownerOrSelfEmpoyed == 1) {
                                echo 'checked=checked';
                            } ?> >
                        <span></span>
                        <?php echo BaseHTML::fieldAccess(['fNm' => 'ownerOrSelfEmpoyed', 'sArr' => $secArr, 'opt' => 'L']); ?>
                    </label>
                </div>
            </div>
            <?php if ($hideThisField) { ?>
                <div class=" col-md-6 employmentHistory_disp <?php echo loanForm::showField('employmentHistory'); ?>">
                    <div class="row form-group">
                        <?php echo loanForm::label('employmentHistory', 'col-md-12 '); ?>
                        <div class="col-md-12">
                            <textarea
                                    class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'employmentHistory', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    name="employmentHistory"
                                    id="employmentHistory"
                                    TABINDEX="<?php echo $tabIndex++; ?>"
                                    rows="2"
                                    cols="20"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'employmentHistory', 'sArr' => $secArr, 'opt' => 'I']); ?>><?php echo Strings::showField('employmentHistory', 'incomeInfo') ?></textarea>
                        </div>
                    </div>
                </div>
            <?php } ?>
            <?php if ($hideThisField) { ?>
                <div class=" col-md-6 employer1Phone_disp <?php echo loanForm::showField('employer1Phone'); ?>">
                    <div class="row form-group">
                        <?php echo loanForm::label('employer1Phone', 'col-md-12 '); ?>
                        <div class="col-md-12">
                            <input class="form-control input-sm mask_phone <?php echo BaseHTML::fieldAccess(['fNm' => 'employer1Phone', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   type="text"
                                   name="employerPhone1"
                                   id="employerPhone1"
                                   value="<?php echo Strings::formatPhoneNumber($employer1Phone) ?>"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'employer1Phone', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                        </div>
                    </div>
                </div>
            <?php } ?>
            <div class="col-md-6 contactAtWork1_disp <?php echo loanForm::showField('contactAtWork1'); ?>">
                <div class="row form-group">
                    <?php echo loanForm::label('contactAtWork1', 'col-md-12 '); ?>
                    <div class="col-md-2">
                            <span class="switch switch-icon <?php echo BaseHTML::fieldAccess(['fNm' => 'contactAtWork1', 'sArr' => $secArr, 'opt' => 'M']); ?> ">
                                <label class="font-weight-bold">
                                    <input class="form-control"
                                        <?php if (Strings::showField('contactAtWork1', 'incomeInfo') == '1') { ?>
                                            checked="checked"
                                        <?php } ?>
                                           id="contactWork1"
                                           type="checkbox"
                                           <?php if ($allowToEdit) { ?>
                                               onchange="toggleSwitch('contactWork1', 'contactAtWork1', '1', '0' );"
                                           <?php } ?>>
                                     <input type="hidden"
                                            name="contactAtWork1"
                                            id="contactAtWork1"
                                            value="<?php echo Strings::showField('contactAtWork1', 'incomeInfo') ?>"
                                         <?php echo BaseHTML::fieldAccess(['fNm' => 'contactAtWork1', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                    <span></span>
                                </label>
                            </span>
                    </div>
                </div>
            </div>


            <div class=" col-md-6 borLineOfWorkProfession_disp <?php echo loanForm::showField('borLineOfWorkProfession'); ?>">
                <div class="row form-group">
                    <?php echo loanForm::label('borLineOfWorkProfession', 'col-md-12 '); ?>
                    <div class="col-md-12">
                        <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'borLineOfWorkProfession', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               type="number"
                               name="borLineOfWorkProfession"
                               id="borLineOfWorkProfession"
                               value="<?php echo Strings::showField('borLineOfWorkProfession', 'incomeInfo') ?>"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'borLineOfWorkProfession', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                    </div>
                </div>
            </div>
        </div>
        <div class=" row addiEmpInfo_disp <?php echo loanForm::showField('addiEmpInfo'); ?> ">
            <div class="col-md-12">
                <div class="card card-custom">
                    <div class="card-header bg-primary-o-40">
                        <div class="card-title">
                            <h3 class="card-label">
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'addiEmpInfo', 'sArr' => $secArr, 'opt' => 'L']); ?>
                            </h3>
                        </div>
                        <?php if ($allowToEdit) { ?>
                            <div class="card-toolbar"
                                 id="hideAddnlEmpInfo" <?php
                            if (count($borEmploymentInfo) >= 2) { ?>
                                style="display:none;"
                            <?php } ?>>
                                <a data-href="<?php echo CONST_URL_POPS; ?>addBoEmploymentInfo.php"
                                   data-id='LMRID=<?php echo cypher::myEncryption($LMRId) ?>'
                                   data-name="Employment Information"
                                   data-toggle='modal'
                                   data-target='#exampleModal1'
                                   data-wsize='modal-xl'
                                   class="btn btn-sm btn-success btn-text-primary  btn-icon ml-2 tooltipClass d-none"
                                   title="Click to add  Employment Information">
                                    <i class=" icon-md fas fa-plus "></i>
                                </a>
                                <a href="javascript:void(0);"
                                   onclick="cloneFormBorAddEmpInfo('borEmplAdditional','icrementSec')"
                                   class="btn btn-sm btn-success btn-text-primary  btn-icon ml-2 tooltipClass cloneFormButton "
                                   title="Click to add new row">
                                    <i class=" fas fa-plus  icon-md tooltipClass"
                                       title="Click to Add New Rent Roll"></i>
                                </a>
                            </div>
                        <?php } ?>
                    </div>
                    <div class="card-body p-2" id="showEmploymentInfo">
                        <?php
                        $borEmploymentInfoCnt = count($borEmploymentInfo);
                        if ($borEmploymentInfoCnt == 0) {
                            $borEmploymentInfoCnt = 1;
                        }
                        $borForCnt = 1;
                        $encLMRId = cypher::myEncryption($LMRId);

                        for ($borEmpCnt = 0; $borEmpCnt < $borEmploymentInfoCnt; $borEmpCnt++) {

                            $nameOfEmployer = trim($borEmploymentInfo[$borEmpCnt]['nameOfEmployer']);
                            $addrOfEmployer = trim($borEmploymentInfo[$borEmpCnt]['addrOfEmployer']);
                            $cityOfEmployer = trim($borEmploymentInfo[$borEmpCnt]['cityOfEmployer']);
                            $stateOfEmployer = trim($borEmploymentInfo[$borEmpCnt]['stateOfEmployer']);
                            $zipOfEmployer = trim($borEmploymentInfo[$borEmpCnt]['zipOfEmployer']);
                            $countryOfEmployer = trim($borEmploymentInfo[$borEmpCnt]['countryOfEmployer']);
                            $addOrPrevJob = trim($borEmploymentInfo[$borEmpCnt]['addOrPrevJob']);

                            $employmentType = trim($borEmploymentInfo[$borEmpCnt]['employmentType']);
                            $employedFrom = trim($borEmploymentInfo[$borEmpCnt]['employedFrom']);
                            $employedTo = trim($borEmploymentInfo[$borEmpCnt]['employedTo']);
                            $monthlyIncome = trim($borEmploymentInfo[$borEmpCnt]['monthlyIncome']);
                            $yrsEmployed = trim($borEmploymentInfo[$borEmpCnt]['yrsEmployed']);
                            $employedByOtherParty = trim($borEmploymentInfo[$borEmpCnt]['employedByOtherParty']);
                            $ownerOrSelfEmpoyed = trim($borEmploymentInfo[$borEmpCnt]['ownerOrSelfEmpoyed']);
                            $overtime = trim($borEmploymentInfo[$borEmpCnt]['overtime']);
                            $commissionOrBonus = trim($borEmploymentInfo[$borEmpCnt]['commissionOrBonus']);
                            $militaryIncome = trim($borEmploymentInfo[$borEmpCnt]['militaryIncome']);
                            $otherHouseHold = trim($borEmploymentInfo[$borEmpCnt]['otherHouseHold']);
                            $otherSourcesIncome = trim($borEmploymentInfo[$borEmpCnt]['otherSourcesIncome']);
                            $otherIncomeSources = explode(',', trim($borEmploymentInfo[$borEmpCnt]['otherIncomeSources']));
                            $position = trim($borEmploymentInfo[$borEmpCnt]['position']);
                            $businessPhone = trim($borEmploymentInfo[$borEmpCnt]['businessPhone']);
                            $emptypeshare = trim($borEmploymentInfo[$borEmpCnt]['emptypeshare']);
                            $empmonthlyincome = trim($borEmploymentInfo[$borEmpCnt]['empmonthlyincome']);

                            if ($employedFrom == NULL || $employedFrom == '' || $employedFrom == '0000-00-00') {
                                $employedFrom = '';
                            } else {
                                $employedFrom = Dates::formatDateWithRE($employedFrom, 'YMD', 'm/d/Y');
                            }

                            if ($employedTo == NULL || $employedTo == '' || $employedTo == '0000-00-00') {
                                $employedTo = '';
                            } else {
                                $employedTo = Dates::formatDateWithRE($employedTo, 'YMD', 'm/d/Y');
                            }
                            $employedByOtherPartyChk = $ownerOrSelfEmpoyedChk = '';
                            if ($employedByOtherParty == 1) {
                                $employedByOtherPartyChk = ' checked ';
                            }
                            if ($ownerOrSelfEmpoyed == 1) {
                                $ownerOrSelfEmpoyedChk = ' checked ';
                            }
                            $additionalSel = $previousSel = '';
                            if ($addOrPrevJob == 'additional') {
                                $additionalSel = ' selected ';
                            } else if ($addOrPrevJob == 'previous') {
                                $previousSel = ' selected ';
                            }

                            $businessPhone = Strings::formatPhoneNumber($businessPhone);
                            // $LMRId = $borEmploymentInfo[$borEmpCnt]['fileID'];
                            $LOBEID = $borEmploymentInfo[$borEmpCnt]['LOBEID'];

                            $encLID = cypher::myEncryption($LOBEID);
                            ?>
                            <div class="card card-custom mb-2 borEmplAdditional borEmplAdditionalDiv<?php echo $borForCnt; ?>"
                                 data-sN="<?php echo trim($borForCnt); ?>"
                                 id="borEmpl_<?php echo trim($borForCnt); ?>_div">
                                <div class="card-header bg-primary-o-20">
                                    <div class="card-title">
                                        <h3 class="card-label entityInfoCnt">
                                            Additional Employment Information
                                            :
                                            <span class="icrementSec"><?php echo trim($borForCnt); ?></span>
                                        </h3>
                                    </div>
                                    <div class="card-toolbar">
                                        <?php if ($op != 'view') { ?>
                                            <a href="javascript:void(0);"
                                               data-lmrid="<?php echo $encLMRId; ?>"
                                               data-lobeid="<?php echo $encLID; ?>"
                                               data-sectionnum="<?php echo $borForCnt; ?>"
                                               class=" btn btn-danger btn-xs  btn-text-primary btn-hover-danger btn-icon ml-2 tooltipClass deleteEmployementInfoCls"
                                               data-toggle="tooltip" data-placement="top"
                                               title="Click to delete">
                                                <i class="flaticon2-trash"></i>
                                            </a>
                                        <?php } ?>
                                        <a href="javascript:void(0);"
                                           class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                                           data-card-tool="toggle"
                                           data-section="body_borEmpl<?php echo trim($borForCnt); ?>_body"
                                           data-toggle="tooltip" data-placement="top" title="Toggle Card">
                                            <i class="ki ki-arrow-down icon-nm"></i>
                                        </a>
                                    </div>
                                </div>
                                <div class="card-body body_borEmpl<?php echo trim($borForCnt); ?>_body ">
                                    <input type="hidden"
                                           name="AddiontalEmplInfo[<?php echo $borForCnt; ?>][LOBEID]"
                                           id="AddiontalEmplInfo_<?php echo $borForCnt; ?>_LOBEID"
                                           value="<?php echo $encLID; ?>">
                                    <div class="row">
                                        <div class="form-group col-md-6">
                                            <label class="font-weight-bold"
                                                   for="AddiontalEmplInfo_<?php echo $borForCnt; ?>_addOrPrevJob">Is
                                                this job an
                                                additional or
                                                previous
                                                Job?</label>
                                            <select name="AddiontalEmplInfo[<?php echo $borForCnt; ?>][addOrPrevJob]"
                                                    id="AddiontalEmplInfo_<?php echo $borForCnt; ?>_addOrPrevJob"
                                                    data-placeholder="Please Select"
                                                    class="form-control chzn-select <?php echo BaseHTML::fieldAccess(['fNm' => 'addiEmpInfo', 'sArr' => $secArr, 'opt' => 'M']); ?>">
                                                <option value=""></option>
                                                <option value="additional" <?php echo $additionalSel; ?>>
                                                    Additional
                                                </option>
                                                <option value="previous" <?php echo $previousSel; ?>>
                                                    Previous
                                                </option>
                                            </select>
                                        </div>
                                        <?php if ($hideThisField) { ?>
                                            <div class="form-group col-md-6">
                                                <label class="font-weight-bold"
                                                       for="AddiontalEmplInfo_<?php echo $borForCnt; ?>_nameOfEmployer">Name</label>
                                                <input type="text"
                                                       name="AddiontalEmplInfo[<?php echo $borForCnt; ?>][nameOfEmployer]"
                                                       id="AddiontalEmplInfo_<?php echo $borForCnt; ?>_nameOfEmployer"
                                                       value="<?php echo htmlentities($nameOfEmployer); ?>"
                                                       size="40"
                                                       class="form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'addiEmpInfo', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       autocomplete="off">
                                            </div>
                                        <?php } ?>
                                        <?php if ($hideThisField) { ?>
                                            <div class="form-group col-md-6">
                                                <script>
                                                    $(document).ready(function() {
                                                        $('#AddiontalEmplInfo_<?php echo $borForCnt; ?>_addrOfEmployer').on('input', function() {
                                                            address_lookup.InitLegacy($(this));
                                                        });
                                                    });
                                                </script>
                                                <label class="font-weight-bold"
                                                       for="AddiontalEmplInfo_<?php echo $borForCnt; ?>_addrOfEmployer">
                                                    Address of Employer
                                                </label>
                                                <input type="text"
                                                       data-address="AddiontalEmplInfo_<?php echo $borForCnt; ?>_addrOfEmployer"
                                                       data-city="AddiontalEmplInfo_<?php echo $borForCnt; ?>_cityOfEmployer"
                                                       data-state="AddiontalEmplInfo_<?php echo $borForCnt; ?>_stateOfEmployer"
                                                       data-zip="AddiontalEmplInfo_<?php echo $borForCnt; ?>_zipOfEmployer"
                                                       name="AddiontalEmplInfo[<?php echo $borForCnt; ?>][addrOfEmployer]"
                                                       class="form-control"
                                                       id="AddiontalEmplInfo_<?php echo $borForCnt; ?>_addrOfEmployer"
                                                       autocomplete="off"
                                                       value="<?php echo htmlentities($addrOfEmployer); ?>"
                                                />
                                            </div>
                                        <?php } ?>
                                        <?php if ($hideThisField) { ?>
                                            <div class="form-group col-md-6">
                                                <label class="font-weight-bold"
                                                       for="AddiontalEmplInfo_<?php echo $borForCnt; ?>_cityOfEmployer">
                                                    Employer City
                                                </label>
                                                <input type="text"
                                                       name="AddiontalEmplInfo[<?php echo $borForCnt; ?>][cityOfEmployer]"
                                                       id="AddiontalEmplInfo_<?php echo $borForCnt; ?>_cityOfEmployer"
                                                       value="<?php echo htmlentities($cityOfEmployer); ?>"
                                                       size="30"
                                                       class="form-control "
                                                       autocomplete="off">
                                            </div>
                                        <?php } ?>
                                        <div class="form-group col-md-6">
                                            <label class="font-weight-bold"
                                                   for="AddiontalEmplInfo_<?php echo $borForCnt; ?>_stateOfEmployer">
                                                Employer State
                                            </label>
                                            <select name="AddiontalEmplInfo[<?php echo $borForCnt; ?>][stateOfEmployer]"
                                                    class="form-control chzn-select"
                                                    data-placeholder="Please Select State"
                                                    id="AddiontalEmplInfo_<?php echo $borForCnt; ?>_stateOfEmployer"
                                                    autocomplete="off">
                                                <option value=""></option>
                                                <?php
                                                for ($j = 0; $j < count($stateArray); $j++) {
                                                    $empStateCode = trim($stateArray[$j]['stateCode']);
                                                    $empStateName = trim($stateArray[$j]['stateName']);
                                                    $sOpt = '';
                                                    $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), $stateOfEmployer);
                                                    ?>
                                                    <option value="<?php echo $empStateCode; ?>" <?php echo $sOpt; ?> ><?php echo $empStateName; ?></option>
                                                <?php }
                                                ?>
                                            </select>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label class="font-weight-bold"
                                                   for="AddiontalEmplInfo_<?php echo $borForCnt; ?>_zipOfEmployer">
                                                Employer Zip
                                            </label>
                                            <input type="text"
                                                   name="AddiontalEmplInfo[<?php echo $borForCnt; ?>][zipOfEmployer]"
                                                   id="AddiontalEmplInfo_<?php echo $borForCnt; ?>_zipOfEmployer"
                                                   value="<?php echo htmlentities($zipOfEmployer); ?>" size="30"
                                                   class="form-control zipCode"
                                                   autocomplete="off">
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label class="font-weight-bold"
                                                   for="AddiontalEmplInfo_<?php echo $borForCnt; ?>_countryOfEmployer">Employer
                                                Country</label>
                                            <select name="AddiontalEmplInfo[<?php echo $borForCnt; ?>][countryOfEmployer]"
                                                    class="form-control chzn-select"
                                                    data-placeholder="Please Select Employer Country"
                                                    id="AddiontalEmplInfo_<?php echo $borForCnt; ?>_countryOfEmployer"
                                                    autocomplete="off">
                                                <option value=""></option>
                                                <?php
                                                foreach ($glCountryArray as $empCountryName => $empCountryCode) {
                                                    $sOpt = '';
                                                    $sOpt = Arrays::isSelected($empCountryCode, $countryOfEmployer);
                                                    ?>
                                                    <option value="<?php echo $empCountryCode; ?>" <?php echo $sOpt; ?>><?php echo $empCountryName; ?></option>
                                                    <?php
                                                } ?>
                                            </select>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label class="font-weight-bold"
                                                   for="AddiontalEmplInfo_<?php echo $borForCnt; ?>_employmentType">Employment
                                                Type</label>
                                            <select name="AddiontalEmplInfo[<?php echo $borForCnt; ?>][employmentType]"
                                                    class="form-control employmentType"
                                                    id="AddiontalEmplInfo_<?php echo $borForCnt; ?>_employmentType"
                                                    autocomplete="off">
                                                <option value=""> - Select -</option>
                                                <?php
                                                for ($o = 1; $o <= count($employedInfo1Array); $o++) {
                                                    $empType = trim($employedInfo1Array[$o]);
                                                    $sOpt = Arrays::isSelected($empType, $employmentType);
                                                    ?>
                                                    <option value="<?php echo $empType; ?>" <?php echo $sOpt; ?>><?php echo $empType; ?>
                                                    </option>
                                                    <?php
                                                }
                                                ?>
                                            </select>
                                        </div>
                                        <div class="employmentTypecls employmentType_<?php echo $borForCnt; ?>_Disp form-group col-md-6 <?php if ($employmentType != 'Self-Employed') {
                                            echo 'd-none';
                                        } ?>">
                                            <div class="radio-inline">
                                                <?php $empShareChk1 = Strings::isChecked($emptypeshare, 'lessthan25'); ?>
                                                <?php $empShareChk2 = Strings::isChecked($emptypeshare, 'eqmorethan25'); ?>
                                                <label class="radio radio-solid font-weight-bold"
                                                       for="AddiontalEmplInfo_<?php echo $borForCnt; ?>_lessthan25">
                                                    <input type="radio"
                                                        <?php echo $empShareChk1; ?>
                                                           name="AddiontalEmplInfo[<?php echo $borForCnt; ?>][emptypeshare]"
                                                           id="AddiontalEmplInfo_<?php echo $borForCnt; ?>_lessthan25"
                                                           value="lessthan25">
                                                    <span></span>
                                                    I have ownership share of less than 25%
                                                </label>
                                                <label class="radio radio-solid font-weight-bold"
                                                       for="AddiontalEmplInfo_<?php echo $borForCnt; ?>_eqmorethan25">
                                                    <input type="radio"
                                                        <?php echo $empShareChk2; ?>
                                                           name="AddiontalEmplInfo[<?php echo $borForCnt; ?>][emptypeshare]"
                                                           id="AddiontalEmplInfo_<?php echo $borForCnt; ?>_eqmorethan25"
                                                           value="eqmorethan25">
                                                    <span></span>
                                                    I have ownership share of 25% or more
                                                </label>
                                            </div>
                                        </div>
                                        <div class="employmentTypecls employmentType_<?php echo $borForCnt; ?>_Disp form-group col-md-6 <?php if ($employmentType != 'Self-Employed') {
                                            echo 'd-none';
                                        } ?>">
                                            <label class="font-weight-bold"
                                                   for="AddiontalEmplInfo_<?php echo $borForCnt; ?>_empmonthlyincome">Monthly
                                                Income/Loss $</label>
                                            <input type="text"
                                                   placeholder="0.00"
                                                   name="AddiontalEmplInfo[<?php echo $borForCnt; ?>][empmonthlyincome]"
                                                   class="form-control"
                                                   id="AddiontalEmplInfo_<?php echo $borForCnt; ?>_empmonthlyincome"
                                                   value="<?php echo $empmonthlyincome; ?>"/>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label class="font-weight-bold"
                                                   for="AddiontalEmplInfo_<?php echo $borForCnt; ?>_employedFrom">Employed
                                                From</label>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                                <span class="input-group-text employedFrom tooltipClass"
                                                                      title="Click to open the calender">
                                                                    <i class="fa fa-calendar text-primary"></i>
                                                                </span>
                                                </div>
                                                <input type="text" class="form-control input-sm dateNewClass"
                                                       name="AddiontalEmplInfo[<?php echo $borForCnt; ?>][employedFrom]"
                                                       id="AddiontalEmplInfo_<?php echo $borForCnt; ?>_employedFrom"
                                                       value="<?php echo $employedFrom; ?>"
                                                       placeholder="MM/DD/YYYY"
                                                       size="15" maxlength="10" autocomplete="off">
                                            </div>
                                        </div>

                                        <div class="form-group col-md-6">
                                            <label class="font-weight-bold"
                                                   for="AddiontalEmplInfo_<?php echo $borForCnt; ?>_employedTo">Employed
                                                To</label>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                                <span class="input-group-text employedTo tooltipClass"
                                                                      title="Click to open the calender">
                                                                    <i class="fa fa-calendar text-primary"></i>
                                                                </span>
                                                </div>
                                                <input type="text" class="form-control input-sm dateNewClass"
                                                       name="AddiontalEmplInfo[<?php echo $borForCnt; ?>][employedTo]"
                                                       id="AddiontalEmplInfo_<?php echo $borForCnt; ?>_employedTo"
                                                       value="<?php echo $employedTo; ?>"
                                                       placeholder="MM/DD/YYYY"
                                                       size="15" maxlength="10" autocomplete="off">
                                            </div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label class="font-weight-bold"
                                                   for="AddiontalEmplInfo_<?php echo $borForCnt; ?>_yrsEmployed">Yrs
                                                employed in this
                                                line of
                                                work /
                                                profession</label>
                                            <input type="number"
                                                   name="AddiontalEmplInfo[<?php echo $borForCnt; ?>][yrsEmployed]"
                                                   id="AddiontalEmplInfo_<?php echo $borForCnt; ?>_yrsEmployed"
                                                   onkeyup='return isNumberOnly(event)'
                                                   class="form-control"
                                                   value="<?php echo $yrsEmployed; ?>" size="14"
                                                   maxlength="28"
                                                   autocomplete="off">
                                        </div>
                                        <div class="form-group col-md-6">
                                            <div class="checkbox-inline pt-4">
                                                <label class="checkbox">
                                                    <input type="checkbox"
                                                           value="1"
                                                           name="AddiontalEmplInfo[<?php echo $borForCnt; ?>][employedByOtherParty]"
                                                           id="AddiontalEmplInfo_<?php echo $borForCnt; ?>_employedByOtherParty"
                                                        <?php echo $employedByOtherPartyChk; ?>><span></span>
                                                    Employed
                                                    by a family
                                                    member, property seller, real estate agent, or other
                                                    party to the
                                                    transaction?
                                                </label>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <div class="checkbox-inline pt-4">
                                                <label class="checkbox">
                                                    <input type="checkbox"
                                                           value="1"
                                                           name="AddiontalEmplInfo[<?php echo $borForCnt; ?>][ownerOrSelfEmpoyed]"
                                                           id="AddiontalEmplInfo_<?php echo $borForCnt; ?>_ownerOrSelfEmpoyed"
                                                        <?php echo $ownerOrSelfEmpoyedChk; ?>>
                                                    <span></span>
                                                    Are you the
                                                    business
                                                    Owner
                                                    or Self-Employed?
                                                </label>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label class="font-weight-bold"
                                                   for="AddiontalEmplInfo_<?php echo $borForCnt; ?>_monthlyIncome">Monthly
                                                Income</label>
                                            <input type="text"
                                                   placeholder="0.00"

                                                   name="AddiontalEmplInfo[<?php echo $borForCnt; ?>][monthlyIncome]"
                                                   id="AddiontalEmplInfo_<?php echo $borForCnt; ?>_monthlyIncome"
                                                   onkeyup='return numericValues(this,event)'
                                                   class="form-control"
                                                   onblur="currencyConverter(this,this.value)"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimalZeros($monthlyIncome); ?>"
                                                   size="14"
                                                   maxlength="28"
                                                   autocomplete="off">
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label class="font-weight-bold"
                                                   for="AddiontalEmplInfo_<?php echo $borForCnt; ?>_overtime">Overtime</label>
                                            <input type="text"
                                                   placeholder="0.00"
                                                   name="AddiontalEmplInfo[<?php echo $borForCnt; ?>][overtime]"
                                                   id="AddiontalEmplInfo_<?php echo $borForCnt; ?>_overtime"
                                                   class="form-control"
                                                   onblur="currencyConverter(this,this.value)"
                                                   onkeyup='return numericValues(this,event)'
                                                   value="<?php echo Currency::formatDollarAmountWithDecimalZeros($overtime); ?>"
                                                   size="14"
                                                   maxlength="28" autocomplete="off">
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label class="font-weight-bold"
                                                   for="AddiontalEmplInfo_<?php echo $borForCnt; ?>_commissionOrBonus">Commission/Bonus</label>
                                            <input type="text"
                                                   placeholder="0.00"
                                                   name="AddiontalEmplInfo[<?php echo $borForCnt; ?>][commissionOrBonus]"
                                                   id="AddiontalEmplInfo_<?php echo $borForCnt; ?>_commissionOrBonus"
                                                   onkeyup='return numericValues(this,event)'
                                                   onblur="currencyConverter(this,this.value)"
                                                   class="form-control"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimalZeros($commissionOrBonus); ?>"
                                                   size="14"
                                                   maxlength="28"
                                                   autocomplete="off">
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label class="font-weight-bold"
                                                   for="AddiontalEmplInfo_<?php echo $borForCnt; ?>_militaryIncome">Military
                                                Entitlements</label>
                                            <input type="text"
                                                   placeholder="0.00"
                                                   name="AddiontalEmplInfo[<?php echo $borForCnt; ?>][militaryIncome]"
                                                   id="AddiontalEmplInfo_<?php echo $borForCnt; ?>_militaryIncome"
                                                   onkeyup='return numericValues(this,event)'
                                                   onblur="currencyConverter(this,this.value)"
                                                   class="form-control"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimalZeros($militaryIncome); ?>"
                                                   size="14"
                                                   maxlength="28"
                                                   autocomplete="off">
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label class="font-weight-bold"
                                                   for="AddiontalEmplInfo_<?php echo $borForCnt; ?>_otherHouseHold">Other</label>
                                            <input type="text"
                                                   placeholder="0.00"
                                                   name="AddiontalEmplInfo[<?php echo $borForCnt; ?>][otherHouseHold]"
                                                   id="AddiontalEmplInfo_<?php echo $borForCnt; ?>_otherHouseHold"
                                                   onkeyup='return numericValues(this,event)'
                                                   onblur="currencyConverter(this,this.value)"
                                                   class="form-control"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimalZeros($otherHouseHold); ?>"
                                                   size="14"
                                                   maxlength="28"
                                                   autocomplete="off">
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label class="font-weight-bold"
                                                   for="AddiontalEmplInfo_<?php echo $borForCnt; ?>_otherIncomeSources">Income
                                                from other
                                                sources</label>
                                            <select name="AddiontalEmplInfo[<?php echo $borForCnt; ?>][otherIncomeSources][]"
                                                    id="AddiontalEmplInfo_<?php echo $borForCnt; ?>_otherIncomeSources"
                                                    data-placeholder="Please Select Income From Other "
                                                    class="chzn-select form-control" multiple=""
                                                    data-live-search="true"
                                                    data-size="5" data-actions-box="true">
                                                <?php
                                                for ($i = 0; $i < count($glIncomeFromOtherSource); $i++) {
                                                    $incomeFromOtherSourceName = $glIncomeFromOtherSource[$i];
                                                    $sOpt = '';
                                                    $sOpt = in_array($incomeFromOtherSourceName, $otherIncomeSources) ? 'selected' : '';
                                                    ?>
                                                    <option value="<?php echo $incomeFromOtherSourceName; ?>" <?php echo $sOpt; ?>><?php echo $incomeFromOtherSourceName; ?></option>
                                                    <?php
                                                }
                                                ?>
                                            </select>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label class="font-weight-bold"
                                                   for="AddiontalEmplInfo_<?php echo $borForCnt; ?>_otherSourcesIncome">Total
                                                Monthly
                                                Income from Other
                                                Sources</label>
                                            <input type="text"
                                                   placeholder="0.00"
                                                   name="AddiontalEmplInfo[<?php echo $borForCnt; ?>][otherSourcesIncome]"
                                                   onkeyup='return numericValues(this,event)'
                                                   id="AddiontalEmplInfo_<?php echo $borForCnt; ?>_otherSourcesIncome"
                                                   class="form-control"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimalZeros($otherSourcesIncome); ?>"
                                                   onblur="currencyConverter(this,this.value)"
                                                   size="14"
                                                   maxlength="28"
                                                   autocomplete="off">
                                        </div>
                                        <?php if ($hideThisField) { ?>
                                            <div class="form-group col-md-6">
                                                <label class="font-weight-bold"
                                                       for="AddiontalEmplInfo_<?php echo $borForCnt; ?>_position">Position
                                                    / Title / Type
                                                    of
                                                    Business</label>
                                                <input type="text"
                                                       name="AddiontalEmplInfo[<?php echo $borForCnt; ?>][position]"
                                                       id="AddiontalEmplInfo_<?php echo $borForCnt; ?>_position"
                                                       value="<?php echo htmlentities($position); ?>"
                                                       class="form-control" size="30" maxlength="28"
                                                       autocomplete="off">
                                            </div>
                                        <?php } ?>
                                        <div class="form-group col-md-6">
                                            <label class="font-weight-bold"
                                                   for="AddiontalEmplInfo_<?php echo $borForCnt; ?>_businessPhone">Employer
                                                Phone</label>
                                            <input type="text"
                                                   name="AddiontalEmplInfo[<?php echo $borForCnt; ?>][businessPhone]"
                                                   id="AddiontalEmplInfo_<?php echo $borForCnt; ?>_businessPhone"
                                                   maxlength="31"
                                                   size="3" value="<?php echo $businessPhone; ?>"
                                                   autocomplete="off"
                                                   class="form-control mask_phone"/>
                                        </div>

                                    </div>
                                </div>
                            </div>

                            <?php
                            $borForCnt++;
                        } ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$secArr = BaseHTML::sectionAccess2(['sId' => 'CBEI', 'opt' => $fileTab]); // Get Active Fields only....
loanForm::pushSectionID('CBEI');

?>
<div class="card card-custom HMLOLoanInfoSections coBorrowerSections CBEI CBEICard <?php if (count(Arrays::getValueFromArray('CBEI', $fieldsInfo)) <= 0) {
    echo 'secHide';
} ?>" style="<?php echo $coBorDisp; ?>">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label">
                <?php echo BaseHTML::getSectionHeading('CBEI'); ?>
            </h3>
            <?php if (trim(BaseHTML::getSectionTooltip('CBEI')) != '') { ?>&nbsp;
                <i class="popoverClass fas fa-info-circle text-primary "
                   data-html="true"
                   data-content="<?php echo BaseHTML::getSectionTooltip('CBEI'); ?>"></i>
            <?php } ?>
        </div>
        <div class="card-toolbar">
                    <span
                            class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none"
                            data-card-tool="toggle"
                            data-toggle="tooltip"
                            data-placement="top"
                            title="Toggle Card">
                        <i class="ki ki-arrow-down icon-nm"></i>
                    </span>
            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none" data-card-tool="reload"
               data-toggle="tooltip" data-placement="top" title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </a>
            <a href="javascript:void(0);"
               class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
               data-card-tool="toggle"
               data-section="CBEICard"
               data-toggle="tooltip" data-placement="top" title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </a>
        </div>
    </div>
    <div class="card-body CBEICard_body">
        <div class="row col-md-12">
            <?php if ($hideThisField) { ?>
                <div class="form-group col-md-3 occupation2_disp <?php echo loanForm::showField('occupation2'); ?>">
                    <?php echo loanForm::label('occupation2', 'col-md-12 '); ?>
                    <div class="col-md-12">
                        <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'occupation2', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               type="text" name="occupation2" id="occupation2"
                               value="<?php echo htmlentities(Strings::showField('occupation2', 'incomeInfo')); ?>"
                               maxlength="30"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'occupation2', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                    </div>
                </div>
            <?php } ?>
            <div class="form-group col-md-3 employedInfo2_disp <?php echo loanForm::showField('employedInfo2'); ?>">
                <?php echo loanForm::label('employedInfo2', 'col-md-12 '); ?>
                <div class="col-md-12">
                    <select name="employedInfo2" id="employedInfo2" TABINDEX="<?php echo $tabIndex++; ?>"
                            class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'employedInfo2', 'sArr' => $secArr, 'opt' => 'M']); ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'employedInfo2', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                        <option value=""> - Select -</option>
                        <?php
                        for ($em1 = 1; $em1 <= count($employedInfo1Array); $em1++) {
                            $optSel = '';
                            $optSel = Arrays::isSelected($employedInfo1Array[$em1], Strings::showField('employedInfo2', 'incomeInfo'));
                            echo "<option value=\"$employedInfo1Array[$em1]\"" . $optSel . '>' . $employedInfo1Array[$em1] . "</option>\n";
                        }
                        ?>
                    </select>
                </div>
            </div>
            <div class="form-group col-md-3 coBorrowerHireDate_disp <?php echo loanForm::showField('coBorrowerHireDate'); ?>">
                <?php echo loanForm::label('coBorrowerHireDate', 'col-md-12 '); ?>
                <div class="col-md-12">
                    <div class="input-group">
                        <div class="input-group-prepend">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary"></i>
                                    </span>
                        </div>
                        <input class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorrowerHireDate', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass"
                               name="coBorrowerHireDate" id="coBorrowerHireDate"
                               value="<?php echo $coBorrowerHireDate ?>" placeholder="MM/DD/YYYY"
                               type="text"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'coBorrowerHireDate', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                    </div>
                </div>
            </div>
            <?php if ($hideThisField) { ?>
                <div class=" col-md-3 coborEmpbusiness_disp <?php echo loanForm::showField('coborEmpbusiness'); ?>">
                    <div class="row form-group">
                        <?php echo loanForm::label('coborEmpbusiness', 'col-md-12 '); ?>
                        <div class="col-md-12">
                            <textarea
                                    class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'coborEmpbusiness', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    type="text" name="coborEmpbusiness" id="coborEmpbusiness"
                                    TABINDEX="<?php echo $tabIndex++; ?>" maxlength="128"
                                    autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'coborEmpbusiness', 'sArr' => $secArr, 'opt' => 'I']); ?>><?php echo Strings::showField('coborEmpbusiness', 'incomeInfo') ?></textarea>
                        </div>
                    </div>
                </div>
            <?php } ?>
            <!-- co-borrower self-employed start -->
            <div class="clearfix"></div>
            <div class="form-group col-md-12 coborpg_employmentType2_Disp  <?php echo loanForm::showField('employedInfo2'); ?>">
                <div class="row">
                    <div class="form-group col-md-7 ">
                        <div class="radio-inline">
                            <label class="radio radio-solid font-weight-bold">
                                <input type="radio" name="emptypeshare2" id="emptypeshare2"
                                       value="lessthan25" <?php echo Strings::isChecked('lessthan25', $emptypeshare2); ?> ><span></span>
                                I
                                have
                                ownership
                                share of less than 25%
                            </label>
                            <label class="radio radio-solid font-weight-bold">
                                <input type="radio" name="emptypeshare2" id="emptypeshare2"
                                       value="eqmorethan25" <?php echo Strings::isChecked('eqmorethan25', $emptypeshare2); ?> ><span></span>
                                I
                                have
                                ownership share of 25% or more
                            </label>
                        </div>
                    </div>
                    <div class="form-group col-md-5">
                        <div class="row">
                            <label class="col-md-5 font-weight-bold" for="empmonthlyincome2">
                                Monthly Income/Loss $
                            </label>
                            <div class="col-md-7">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input type="text" name="empmonthlyincome2" id="empmonthlyincome2"
                                           class="form-control input-sm"
                                           value="<?php echo $empmonthlyincome2; ?>"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- co-borrower self-employed end -->
            <div class="form-group col-md-12 paidOften1_disp <?php echo loanForm::showField('paidOften1'); ?>">
                <?php echo loanForm::label('paidOften1', 'col-md-3 '); ?>
                <div class="col-md-9">
                    <div class="radio-inline">
                        <label class="radio radio-solid font-weight-bold" for="paidOften1weekly">
                            <input type="radio" name="paidOften1"
                                   class=" <?php echo BaseHTML::fieldAccess(['fNm' => 'paidOften1', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   value="weekly"
                                   id="paidOften1weekly" <?php echo Strings::isChecked('weekly', $paidOften1); ?>  <?php echo BaseHTML::fieldAccess(['fNm' => 'paidOften1', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>Weekly
                        </label>
                        <label class="radio radio-solid font-weight-bold" for="paidOften1monthly">
                            <input type="radio" name="paidOften1"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   class=" <?php echo BaseHTML::fieldAccess(['fNm' => 'paidOften1', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   value="monthly"
                                   id="paidOften1monthly" <?php echo Strings::isChecked('monthly', $paidOften1); ?>><span></span>Monthly
                        </label>
                        <label class="radio radio-solid font-weight-bold" for="paidOften1Weeks">
                            <input type="radio" name="paidOften1"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   class=" <?php echo BaseHTML::fieldAccess(['fNm' => 'paidOften1', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   value="every2Weeks"
                                   id="paidOften1Weeks" <?php echo Strings::isChecked('every2Weeks', $paidOften1); ?>><span></span>Every
                            2
                            weeks
                        </label>
                        <label class="radio radio-solid font-weight-bold" for="paidOften1Month">
                            <input
                                    type="radio" name="paidOften1"
                                    TABINDEX="<?php echo $tabIndex++; ?>"
                                    class=" <?php echo BaseHTML::fieldAccess(['fNm' => 'paidOften1', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    value="twiceMonth"
                                    id="paidOften1Month" <?php echo Strings::isChecked('twiceMonth', $paidOften1); ?>><span></span>Twice
                            a month
                        </label>
                    </div>
                </div>
            </div>
            <div class="form-group col-md-12 bbg coEmployerInfo_disp <?php echo loanForm::showField('coEmployerInfo'); ?>">
                <div class="form-group row col-lg-12 m-0 px-0">
                    <?php echo loanForm::label('coEmployerInfo', 'bg-secondary  py-2  col-lg-12 font-weight-bolder'); ?>
                </div>
            </div>
            <?php if ($hideThisField) { ?>
                <div class="form-group col-md-6 employer2_disp <?php echo loanForm::showField('employer2'); ?>">
                    <?php echo loanForm::label('employer2', 'col-md-12 '); ?>
                    <div class="col-md-12">
                        <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'employer2', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               type="text" name="employer2" id="employer2"
                               value="<?php echo htmlentities(Strings::showField('employer2', 'incomeInfo')); ?>"
                               maxlength="30"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'employer2', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                    </div>
                </div>
            <?php } ?>
            <div class="form-group col-md-6 yearsAtJob2_disp <?php echo loanForm::showField('yearsAtJob2'); ?>">
                <?php echo loanForm::label('yearsAtJob2', 'col-md-12 '); ?>
                <div class="col-md-12">
                    <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'yearsAtJob2', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                           type="text" name="yearsAtJob2" id="yearsAtJob2"
                           value="<?php echo Strings::showField('yearsAtJob2', 'incomeInfo') ?>"
                           maxlength="30"
                           TABINDEX="<?php echo $tabIndex++; ?>"
                           autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'yearsAtJob2', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                </div>
            </div>
            <?php if ($hideThisField) { ?>
                <div class="form-group col-md-6 employer2Add_disp <?php echo loanForm::showField('employer2Add'); ?>">
                    <?php echo loanForm::label('employer2Add', 'col-md-12 '); ?>
                    <div class="col-md-12">
                        <textarea
                                class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'employer2Add', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                name="employer2Add" id="employer2Add" TABINDEX="<?php echo $tabIndex++; ?>" rows="2"
                                cols="20" <?php echo BaseHTML::fieldAccess(['fNm' => 'employer2Add', 'sArr' => $secArr, 'opt' => 'I']); ?>><?php echo Strings::showField('employer2Add', 'incomeInfo') ?></textarea>
                    </div>
                </div>
            <?php } ?>
            <?php if ($hideThisField) { ?>
                <div class="form-group col-md-6 employer2City_disp <?php echo loanForm::showField('employer2City'); ?>">
                    <?php echo loanForm::label('employer2City', 'col-md-12 ', '', '', '', 'Employer City'); ?>
                    <div class="col-md-12">
                        <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'employer2City', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               type="text" name="employer2City" id="employer2City"
                               value="<?php echo Strings::showField('employer2City', 'incomeInfo'); ?>"
                               maxlength="30" autocomplete="off" tabIndex="<?php echo $tabIndex++; ?>">
                    </div>
                </div>
            <?php } ?>
            <div class="form-group col-md-6 employer2State_disp <?php echo loanForm::showField('employer2State'); ?>">
                <?php echo loanForm::label('employer2State', 'col-md-12 ', '', '', '', 'Employer State'); ?>
                <div class="col-md-12">
                    <select name="employer2State" id="employer2State"
                            class="form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'employer2State', 'sArr' => $secArr, 'opt' => 'M']); ?>">
                        <option value="">Select</option>
                        <?php foreach ($stateArray as $coBorState) { ?>
                            <option value="<?php echo $coBorState['stateCode']; ?>" <?php echo Arrays::isSelected($coBorState['stateCode'], $employer2State); ?> ><?php echo $coBorState['stateName']; ?></option>
                        <?php } ?>
                    </select>
                </div>
            </div>
            <div class="form-group col-md-6 employer2Zip_disp <?php echo loanForm::showField('employer2Zip'); ?>">
                <?php echo loanForm::label('employer2Zip', 'col-md-12 ', '', '', '', 'Employer Zip'); ?>
                <div class="col-md-12">
                    <div class="col-md-12">
                        <input class="form-control input-sm zipCode <?php echo BaseHTML::fieldAccess(['fNm' => 'employer2Zip', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               type="test" name="employer2Zip" id="employer2Zip"
                               value="<?php echo Strings::showField('employer2Zip', 'incomeInfo'); ?>"
                               maxlength="8" autocomplete="off" tabIndex="<?php echo $tabIndex++; ?>">
                    </div>
                </div>
            </div>
            <div class="clearfix"></div>
            <?php if ($hideThisField) { ?>
                <div class="form-group col-md-6 employer2Phone_disp <?php echo loanForm::showField('employer2Phone'); ?>">
                    <?php echo loanForm::label('employer2Phone', 'col-md-12 '); ?>
                    <div class="col-md-12">
                        <input class="form-control input-sm mask_phone <?php echo BaseHTML::fieldAccess(['fNm' => 'employer2Phone', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               type="text" name="employerPhone2" id="employerPhone2"
                               value="<?php echo Strings::formatPhoneNumber($employer2Phone) ?>"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'employer2Phone', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                    </div>
                </div>
            <?php } ?>
            <div class="form-group col-md-6 contactWork2_disp <?php echo loanForm::showField('contactWork2'); ?>">
                <?php echo loanForm::label('contactWork2', 'col-md-12 '); ?>
                <div class="col-md-2">
                            <span class="switch switch-icon <?php echo BaseHTML::fieldAccess(['fNm' => 'contactWork2', 'sArr' => $secArr, 'opt' => 'M']); ?> ">
                                <label class="font-weight-bold">
                                    <input class="form-control" <?php if (Strings::showField('contactAtWork2', 'incomeInfo') == '1') { ?> checked="checked" <?php } ?>
                                           id="contactAtWork2_switch" type="checkbox"
                                           <?php if ($allowToEdit) { ?> onchange="toggleSwitch('contactAtWork2_switch', 'contactAtWork2', '1', '0' );"
                                           <?php } ?>>
                                     <input type="hidden" name="contactAtWork2" id="contactAtWork2"
                                            value="<?php echo Strings::showField('contactAtWork2', 'incomeInfo') ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'contactWork2', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                    <span></span>
                                </label>
                            </span>
                </div>
            </div>
            <div class="form-group col-md-12 cobLineOfWorkProfession_disp <?php echo loanForm::showField('cobLineOfWorkProfession'); ?>">
                <?php echo loanForm::label('cobLineOfWorkProfession', 'col-md-6 '); ?>
                <div class="col-md-6">
                    <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'cobLineOfWorkProfession', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                           type="text" name="cobLineOfWorkProfession" id="cobLineOfWorkProfession"
                           value="<?php echo Strings::showField('cobLineOfWorkProfession', 'incomeInfo') ?>"
                           TABINDEX="<?php echo $tabIndex++; ?>"
                           autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'cobLineOfWorkProfession', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                </div>
            </div>
            <div class=" row coAddiEmpInfo_disp <?php echo loanForm::showField('coAddiEmpInfo'); ?> ">
                <div class="col-md-12">
                    <div class="card card-custom">
                        <div class="card-header bg-primary-o-40">
                            <div class="card-title">
                                <h3 class="card-label">
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'coAddiEmpInfo', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                </h3>
                            </div>
                            <?php if ($allowToEdit) { ?>
                                <div class="card-toolbar"
                                     id="hideCoBorAddnlEmpInfo" <?php
                                if (count($coBEmploymentInfo) >= 2) { ?> style="display:none;" <?php } else { ?><?php } ?>>
                                    <a href="javascript:void(0);"
                                       onclick="cloneFormBorAddEmpInfo('coborEmplAdditional','cbicrementSec')"
                                       class="btn btn-sm btn-success btn-text-primary  btn-icon ml-2 tooltipClass cloneFormButton "
                                       title="Click to add new row">
                                        <i class=" fas fa-plus  icon-md tooltipClass"
                                           title="Click to Add New Rent Roll"></i>
                                    </a>
                                </div>
                            <?php } ?>
                        </div>
                        <div class="card-body p-2" id="showCoBEmploymentInfo">
                            <?php
                            $coBEmploymentInfoCnt = count($coBEmploymentInfo);
                            if ($coBEmploymentInfoCnt == 0) {
                                $coBEmploymentInfoCnt = 1;
                            }
                            $borForCnt = 1;
                            $encLMRId = cypher::myEncryption($LMRId);

                            for ($borEmpCnt = 0; $borEmpCnt < $coBEmploymentInfoCnt; $borEmpCnt++) {

                                $nameOfEmployer = trim($coBEmploymentInfo[$borEmpCnt]['nameOfEmployer']);
                                $addrOfEmployer = trim($coBEmploymentInfo[$borEmpCnt]['addrOfEmployer']);

                                $employmentType = trim($coBEmploymentInfo[$borEmpCnt]['employmentType']);
                                $employedFrom = trim($coBEmploymentInfo[$borEmpCnt]['employedFrom']);
                                $employedTo = trim($coBEmploymentInfo[$borEmpCnt]['employedTo']);
                                $monthlyIncome = trim($coBEmploymentInfo[$borEmpCnt]['monthlyIncome']);
                                $position = trim($coBEmploymentInfo[$borEmpCnt]['position']);
                                $businessPhone = trim($coBEmploymentInfo[$borEmpCnt]['businessPhone']);
                                $emptypeshare = trim($coBEmploymentInfo[$borEmpCnt]['coboremptypeshare']);
                                $empmonthlyincome = trim($coBEmploymentInfo[$borEmpCnt]['coborpopempmonthlyincome']);
                                $coBorrowerOwnerOrSelfEmployed = trim($coBEmploymentInfo[$borEmpCnt]['coBorrowerOwnerOrSelfEmployed']);

                                if ($employedFrom == NULL || $employedFrom == '' || $employedFrom == '0000-00-00') {
                                    $employedFrom = '';
                                } else {
                                    $employedFrom = Dates::formatDateWithRE($employedFrom, 'YMD', 'm/d/Y');
                                }

                                if ($employedTo == NULL || $employedTo == '' || $employedTo == '0000-00-00') {
                                    $employedTo = '';
                                } else {
                                    $employedTo = Dates::formatDateWithRE($employedTo, 'YMD', 'm/d/Y');
                                }


                                $businessPhone = Strings::formatPhoneNumber($businessPhone);
                                // $LMRId = $coBEmploymentInfo[$borEmpCnt]['fileID'];
                                $LOCBID = $coBEmploymentInfo[$borEmpCnt]['LOCBID'];

                                $encLID = cypher::myEncryption($LOCBID);
                                $coBorrowerOwnerOrSelfEmployedChk = '';
                                if ($coBorrowerOwnerOrSelfEmployed) {
                                    $coBorrowerOwnerOrSelfEmployedChk = ' checked ';
                                }
                                ?>
                                <div class="card card-custom mb-2 coborEmplAdditional coborEmplAdditionalDiv<?php echo $borForCnt; ?>"
                                     data-sN="<?php echo trim($borForCnt); ?>"
                                     id="borEmpl_<?php echo trim($borForCnt); ?>_div">
                                    <div class="card-header bg-primary-o-20">
                                        <div class="card-title">
                                            <h3 class="card-label entityInfoCnt">
                                                Additional Employment Information
                                                :
                                                <span class="cbicrementSec"><?php echo trim($borForCnt); ?></span>
                                            </h3>
                                        </div>
                                        <div class="card-toolbar">
                                            <?php if ($op != 'view') { ?>
                                                <a href="javascript:void(0);"
                                                   data-lmrid="<?php echo $encLMRId; ?>"
                                                   data-lobeid="<?php echo $encLID; ?>"
                                                   data-sectionnum="<?php echo $borForCnt; ?>"
                                                   class=" btn btn-danger btn-xs  btn-text-primary btn-hover-danger btn-icon ml-2 tooltipClass deleteCoBorEmployementInfoCls"
                                                   data-toggle="tooltip" data-placement="top"
                                                   title="Click to delete">
                                                    <i class="flaticon2-trash"></i>
                                                </a>
                                            <?php } ?>
                                            <a href="javascript:void(0);"
                                               class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                                               data-card-tool="toggle"
                                               data-section="body_coborEmpl<?php echo trim($borForCnt); ?>_body"
                                               data-toggle="tooltip" data-placement="top" title="Toggle Card">
                                                <i class="ki ki-arrow-down icon-nm"></i>
                                            </a>
                                        </div>
                                    </div>
                                    <div class="card-body body_coborEmpl<?php echo trim($borForCnt); ?>_body ">
                                        <input type="hidden"
                                               name="AdditionalCoBorEmplInfo[<?php echo $borForCnt; ?>][LOCBID]"
                                               id="AdditionalCoBorEmplInfo_<?php echo $borForCnt; ?>_LOCBID"
                                               value="<?php echo $encLID; ?>">
                                        <div class="row">
                                            <?php if ($hideThisField) { ?>
                                                <div class="form-group col-md-6">
                                                    <label class="font-weight-bold"
                                                           for="AdditionalCoBorEmplInfo_<?php echo $borForCnt; ?>_nameOfEmployer">Name</label>
                                                    <input type="text"
                                                           name="AdditionalCoBorEmplInfo[<?php echo $borForCnt; ?>][nameOfEmployer]"
                                                           id="AdditionalCoBorEmplInfo_<?php echo $borForCnt; ?>_nameOfEmployer"
                                                           value="<?php echo htmlentities($nameOfEmployer); ?>"
                                                           size="40"
                                                           class="form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'addiEmpInfo', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                           autocomplete="off">
                                                </div>
                                            <?php } ?>
                                            <?php if ($hideThisField) { ?>
                                                <div class="form-group col-md-6">
                                                    <label class="font-weight-bold"
                                                           for="AdditionalCoBorEmplInfo<?php echo $borForCnt; ?>_addrOfEmployer">Address
                                                        of
                                                        Employer</label>
                                                    <textarea
                                                            name="AdditionalCoBorEmplInfo[<?php echo $borForCnt; ?>][addrOfEmployer]"
                                                            class="form-control"
                                                            id="AdditionalCoBorEmplInfo_<?php echo $borForCnt; ?>_addrOfEmployer"
                                                            autocomplete="off"><?php echo $addrOfEmployer; ?></textarea>
                                                </div>
                                            <?php } ?>
                                            <div class="form-group col-md-6">
                                                <label class="font-weight-bold"
                                                       for="AdditionalCoBorEmplInfo_<?php echo $borForCnt; ?>_employmentType">Employment
                                                    Type</label>
                                                <select name="AdditionalCoBorEmplInfo[<?php echo $borForCnt; ?>][employmentType]"
                                                        class="form-control coboremploymentType"
                                                        id="AdditionalCoBorEmplInfo_<?php echo $borForCnt; ?>_employmentType"
                                                        autocomplete="off">
                                                    <option value=""> - Select -</option>
                                                    <?php
                                                    for ($o = 1; $o <= count($employedInfo1Array); $o++) {
                                                        $empType = trim($employedInfo1Array[$o]);
                                                        $sOpt = Arrays::isSelected($empType, $employmentType);
                                                        ?>
                                                        <option value="<?php echo $empType; ?>" <?php echo $sOpt; ?>><?php echo $empType; ?>
                                                        </option>
                                                        <?php
                                                    }
                                                    ?>
                                                </select>
                                            </div>
                                            <div class="employmentTypecls coboremploymentType_<?php echo $borForCnt; ?>_Disp form-group col-md-6
                                                    <?php if ($employmentType != 'Self-Employed') {
                                                echo 'd-none';
                                            } ?>">
                                                <div class="radio-inline">
                                                    <?php $empShareChk1 = Strings::isChecked($emptypeshare, 'lessthan25'); ?>
                                                    <?php $empShareChk2 = Strings::isChecked($emptypeshare, 'eqmorethan25'); ?>
                                                    <label class="radio radio-solid font-weight-bold"
                                                           for="AdditionalCoBorEmplInfo_<?php echo $borForCnt; ?>_lessthan25">
                                                        <input type="radio"
                                                            <?php echo $empShareChk1; ?>
                                                               name="AdditionalCoBorEmplInfo[<?php echo $borForCnt; ?>][emptypeshare]"
                                                               id="AdditionalCoBorEmplInfo_<?php echo $borForCnt; ?>_lessthan25"
                                                               value="lessthan25">
                                                        <span></span>
                                                        I have ownership share of less than 25%
                                                    </label>
                                                    <label class="radio radio-solid font-weight-bold"
                                                           for="AdditionalCoBorEmplInfo_<?php echo $borForCnt; ?>_eqmorethan25">
                                                        <input type="radio"
                                                            <?php echo $empShareChk2; ?>
                                                               name="AdditionalCoBorEmplInfo[<?php echo $borForCnt; ?>][emptypeshare]"
                                                               id="AdditionalCoBorEmplInfo_<?php echo $borForCnt; ?>_eqmorethan25"
                                                               value="eqmorethan25">
                                                        <span></span>
                                                        I have ownership share of 25% or more
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="employmentTypecls coboremploymentType_<?php echo $borForCnt; ?>_Disp form-group col-md-6 <?php if ($employmentType != 'Self-Employed') {
                                                echo 'd-none';
                                            } ?>">
                                                <label class="font-weight-bold"
                                                       for="AdditionalCoBorEmplInfo_<?php echo $borForCnt; ?>_empmonthlyincome">Monthly
                                                    Income/Loss $</label>
                                                <input type="text"
                                                       placeholder="0.00"
                                                       name="AdditionalCoBorEmplInfo[<?php echo $borForCnt; ?>][empmonthlyincome]"
                                                       class="form-control"
                                                       id="AdditionalCoBorEmplInfo_<?php echo $borForCnt; ?>_empmonthlyincome"
                                                       value="<?php echo $empmonthlyincome; ?>"/>
                                            </div>
                                            <div class="form-group col-md-6">
                                                <label class="font-weight-bold"
                                                       for="AdditionalCoBorEmplInfo_<?php echo $borForCnt; ?>_employedFrom">Employed
                                                    From</label>
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                                <span class="input-group-text employedFrom tooltipClass"
                                                                      title="Click to open the calender">
                                                                    <i class="fa fa-calendar text-primary"></i>
                                                                </span>
                                                    </div>
                                                    <input type="text" class="form-control input-sm dateNewClass"
                                                           name="AdditionalCoBorEmplInfo[<?php echo $borForCnt; ?>][employedFrom]"
                                                           id="AdditionalCoBorEmplInfo_<?php echo $borForCnt; ?>_employedFrom"
                                                           value="<?php echo $employedFrom; ?>"
                                                           placeholder="MM/DD/YYYY"
                                                           size="15" maxlength="10" autocomplete="off">
                                                </div>
                                            </div>

                                            <div class="form-group col-md-6">
                                                <label class="font-weight-bold"
                                                       for="AdditionalCoBorEmplInfo_<?php echo $borForCnt; ?>_employedTo">Employed
                                                    To</label>
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                                <span class="input-group-text employedTo tooltipClass"
                                                                      title="Click to open the calender">
                                                                    <i class="fa fa-calendar text-primary"></i>
                                                                </span>
                                                    </div>
                                                    <input type="text" class="form-control input-sm dateNewClass"
                                                           name="AdditionalCoBorEmplInfo[<?php echo $borForCnt; ?>][employedTo]"
                                                           id="AdditionalCoBorEmplInfo_<?php echo $borForCnt; ?>_employedTo"
                                                           value="<?php echo $employedTo; ?>"
                                                           placeholder="MM/DD/YYYY"
                                                           size="15" maxlength="10" autocomplete="off">
                                                </div>
                                            </div>
                                            <div class="form-group col-md-6">
                                                <div class="checkbox-inline pt-4">
                                                    <label class="checkbox">
                                                        <input type="checkbox"
                                                               value="1"
                                                               name="AdditionalCoBorEmplInfo[<?php echo $borForCnt; ?>][coBorrowerOwnerOrSelfEmployed]"
                                                               id="AdditionalCoBorEmplInfo_<?php echo $borForCnt; ?>_coBorrowerOwnerOrSelfEmployed"
                                                            <?php echo $coBorrowerOwnerOrSelfEmployedChk; ?>>
                                                        <span></span>
                                                        Are you the
                                                        business
                                                        Owner
                                                        or Self-Employed?
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="form-group col-md-6">
                                                <label class="font-weight-bold"
                                                       for="AdditionalCoBorEmplInfo_<?php echo $borForCnt; ?>_monthlyIncome">Monthly
                                                    Income</label>
                                                <input type="text"
                                                       placeholder="0.00"

                                                       name="AdditionalCoBorEmplInfo[<?php echo $borForCnt; ?>][monthlyIncome]"
                                                       id="AdditionalCoBorEmplInfo_<?php echo $borForCnt; ?>_monthlyIncome"
                                                       onkeyup='return numericValues(this,event)'
                                                       class="form-control"
                                                       value="<?php echo $monthlyIncome; ?>" size="14"
                                                       maxlength="28"
                                                       autocomplete="off">
                                            </div>
                                            <?php if ($hideThisField) { ?>
                                                <div class="form-group col-md-6">
                                                    <label class="font-weight-bold"
                                                           for="AdditionalCoBorEmplInfo_<?php echo $borForCnt; ?>_position">Position
                                                        / Title / Type
                                                        of
                                                        Business</label>
                                                    <input type="text"
                                                           name="AdditionalCoBorEmplInfo[<?php echo $borForCnt; ?>][position]"
                                                           id="AdditionalCoBorEmplInfo_<?php echo $borForCnt; ?>_position"
                                                           value="<?php echo htmlentities($position); ?>"
                                                           class="form-control" size="30" maxlength="28"
                                                           autocomplete="off">
                                                </div>
                                            <?php } ?>
                                            <?php if ($hideThisField) { ?>
                                                <div class="form-group col-md-6">
                                                    <label class="font-weight-bold"
                                                           for="AdditionalCoBorEmplInfo_<?php echo $borForCnt; ?>_businessPhone">Employer
                                                        Phone</label>
                                                    <input type="text"
                                                           name="AdditionalCoBorEmplInfo[<?php echo $borForCnt; ?>][businessPhone]"
                                                           id="AdditionalCoBorEmplInfo_<?php echo $borForCnt; ?>_businessPhone"
                                                           maxlength="31"
                                                           size="3" value="<?php echo $businessPhone; ?>"
                                                           autocomplete="off"
                                                           class="form-control mask_phone"/>
                                                </div>
                                            <?php } ?>
                                        </div>
                                    </div>
                                </div>

                                <?php
                                $borForCnt++;
                            } ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<?php

$secArr = BaseHTML::sectionAccess2(['sId' => 'BMI', 'opt' => $fileTab, 'activeTab' => $activeTab]); // Get Active Fields only...
loanForm::pushSectionID('BMI');

if ($activeTab == '1003') {  /* story-31529 hide some fields based on the requirement mentioned in story*/
    foreach ($hide1003FieldsArray['BMI'] as $hideFieldkey => $hideFieldValue) {
        if (array_key_exists($hideFieldValue, $secArr)) {
            if(is_array($secArr[$hideFieldValue])) {
                $secArr[$hideFieldValue]['fieldDisplay'] = 0;
                loanForm::hideField('BMI', $hideFieldValue);
            }
        }
    }

}
?>
<div class="card card-custom HMLOLoanInfoSections BMI BMICard <?php if (count(Arrays::getValueFromArray('BMI', $fieldsInfo, $activeTab)) <= 0) {
    echo 'secHide';
} ?>" style="<?php echo $HMLOLoanInfoSectionsDisp; ?>">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label">
                <?php echo BaseHTML::getSectionHeading('BMI'); ?>
            </h3>
            <?php if (trim(BaseHTML::getSectionTooltip('BMI')) != '') { ?>&nbsp;
                <i class="popoverClass fas fa-info-circle text-primary "
                   data-html="true"
                   data-content="<?php echo BaseHTML::getSectionTooltip('BMI'); ?>"></i>
            <?php } ?>
        </div>
        <div class="card-toolbar">
            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none" data-card-tool="toggle"
               data-toggle="tooltip" data-placement="top" title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </a>
            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none" data-card-tool="reload"
               data-toggle="tooltip" data-placement="top" title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </a>
            <a href="javascript:void(0);"
               class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
               data-card-tool="toggle"
               data-section="BMICard"
               data-toggle="tooltip" data-placement="top" title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </a>
        </div>
    </div>
    <div class="card-body BMICard_body">
        <div class="row">
            <div class="form-group col-md-6 grossIncome1_disp <?php echo loanForm::showField('grossIncome1'); ?>">
                <?php echo loanForm::label('grossIncome1', 'col-md-12 '); ?>
                <div class="col-md-12">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input type="text"
                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'grossIncome1', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               placeholder="0.00"
                               name="grossIncome1"
                               id="grossIncome1"
                               value="<?php echo Currency::formatDollarAmountWithDecimalZeros(Strings::showField('grossIncome1', 'incomeInfo')) ?>"
                               maxlength="12"
                               onblur="currencyConverter(this, this.value);calculateHMLOBorrowerNetHouseholdIncome('loanModForm',  'primTotalNetHouseHoldIncome');"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'grossIncome1', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>
            </div>
            <div class="form-group col-md-6 commissionOrBonus1_disp <?php echo loanForm::showField('commissionOrBonus1'); ?>">
                <?php echo loanForm::label('commissionOrBonus1', 'col-md-12 '); ?>
                <div class="col-md-12">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input type="text"
                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'commissionOrBonus1', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               name="commissionOrBonus1" id="commissionOrBonus1"
                               placeholder="0.00"
                               value="<?php echo Currency::formatDollarAmountWithDecimalZeros(Strings::showField('commissionOrBonus1', 'incomeInfo')) ?>"
                               size="20" maxlength="12"
                               onblur="currencyConverter(this, this.value);calculateHMLOBorrowerNetHouseholdIncome('loanModForm',  'primTotalNetHouseHoldIncome');"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'commissionOrBonus1', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>
            </div>
            <div class="form-group col-md-6 militaryIncome1_disp <?php echo loanForm::showField('militaryIncome1'); ?>">
                <?php echo loanForm::label('militaryIncome1', 'col-md-12 '); ?>
                <div class="col-md-12">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input type="text"
                               placeholder="0.00"
                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'militaryIncome1', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               name="militaryIncome1" id="militaryIncome1"
                               value="<?php echo Currency::formatDollarAmountWithDecimalZeros(Strings::showField('militaryIncome1', 'incomeInfo')) ?>"
                               maxlength="12" onkeyup='return numericValues(this,event)'
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               onblur="currencyConverter(this, this.value);calculateHMLOBorrowerNetHouseholdIncome('loanModForm',  'primTotalNetHouseHoldIncome');"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'militaryIncome1', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>
            </div>
            <div class="form-group col-md-6 overtime1_disp <?php echo loanForm::showField('overtime1'); ?>">
                <?php echo loanForm::label('overtime1', 'col-md-12 '); ?>
                <div class="col-md-12">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input type="text"
                               placeholder="0.00"
                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'overtime1', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               name="overtime1" id="overtime1"
                               value="<?php echo Currency::formatDollarAmountWithDecimalZeros(Strings::showField('overtime1', 'incomeInfo')) ?>"
                               size="20" maxlength="12"
                               onblur="currencyConverter(this, this.value);calculateHMLOBorrowerNetHouseholdIncome('loanModForm',  'primTotalNetHouseHoldIncome');"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'overtime1', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>
            </div>
            <div class="form-group col-md-6 netRental1_disp <?php echo loanForm::showField('netRental1'); ?>">
                <?php echo loanForm::label('netRental1', 'col-md-12 '); ?>
                <div class="col-md-12">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input type="text"
                               placeholder="0.00"
                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'netRental1', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               name="netRental1" id="netRental1"
                               value="<?php echo Currency::formatDollarAmountWithDecimalZeros(Strings::showField('netRental1', 'incomeInfo')) ?>"
                               size="20" maxlength="12"
                               onclick="clearMyMsg('loanModForm','netRental1','<?php echo $netDefaultText ?>');"
                               onfocus="clearMyMsg('loanModForm','netRental1','<?php echo $netDefaultText ?>');"
                               onblur="currencyConverter(this, this.value);putMyMsg('loanModForm','netRental1','<?php echo $netDefaultText ?>'), calculateHMLOBorrowerNetHouseholdIncome('loanModForm',  'primTotalNetHouseHoldIncome');"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'netRental1', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>
            </div>
            <div class="form-group col-md-6 netEarnedInterest1_disp <?php echo loanForm::showField('netEarnedInterest1'); ?>">
                <?php echo loanForm::label('netEarnedInterest1', 'col-md-12 '); ?>
                <div class="col-md-12">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input type="text"
                               placeholder="0.00"
                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'netEarnedInterest1', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               name="netEarnedInterest1" id="netEarnedInterest1"
                               value="<?php echo Currency::formatDollarAmountWithDecimalZeros(Strings::showField('netEarnedInterest1', 'incomeInfo')) ?>"
                               maxlength="12"
                               onclick="clearMyMsg('loanModForm','netEarnedInterest1','<?php echo $netDefaultText ?>');"
                               onfocus="clearMyMsg('loanModForm','netEarnedInterest1','<?php echo $netDefaultText ?>');"
                               onblur="currencyConverter(this, this.value);putMyMsg('loanModForm','netEarnedInterest1','<?php echo $netDefaultText ?>');  calculateHMLOBorrowerNetHouseholdIncome('loanModForm',  'primTotalNetHouseHoldIncome');"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'netEarnedInterest1', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>
            </div>
            <!-- Capital Gains / (Losses) -->
            <div class="form-group col-md-6 capitalGains1_disp <?php echo loanForm::showField('capitalGains1'); ?>">
                <?php echo loanForm::label('capitalGains1', 'col-md-12 '); ?>
                <div class="col-md-12">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input type="text" id="capitalGains1" name="capitalGains1"
                               placeholder="0.00"
                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'capitalGains1', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               value="<?php echo $capitalGains1; ?>" autocomplete="off"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'capitalGains1', 'sArr' => $secArr, 'opt' => 'I']); ?>
                               onblur="currencyConverter(this, this.value);calculateHMLOBorrowerNetHouseholdIncome('loanModForm',  'primTotalNetHouseHoldIncome');"
                               TABINDEX="<?php echo $tabIndex++; ?>"/>
                    </div>
                </div>
            </div>
            <!-- //Capital Gains / (Losses)// -->
            <!-- Partnership / Subchapter S Income -->
            <div class="form-group col-md-6 partnership1_disp <?php echo loanForm::showField('partnership1'); ?>">
                <?php echo loanForm::label('partnership1', 'col-md-12 '); ?>
                <div class="col-md-12">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input type="text" id="partnership1" name="partnership1"
                               placeholder="0.00"
                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'partnership1', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               value="<?php echo $partnership1; ?>" autocomplete="off"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'partnership1', 'sArr' => $secArr, 'opt' => 'I']); ?>
                               onblur="currencyConverter(this, this.value);calculateHMLOBorrowerNetHouseholdIncome('loanModForm',  'primTotalNetHouseHoldIncome');"
                               TABINDEX="<?php echo $tabIndex++; ?>"/>
                    </div>
                </div>
            </div>
            <!-- //Partnership / Subchapter S Income// -->
            <div class="form-group col-md-6 otherHouseHold1_disp <?php echo loanForm::showField('otherHouseHold1'); ?>">
                <?php echo loanForm::label('otherHouseHold1', 'col-md-12 '); ?>
                <div class="col-md-12">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input type="text"
                               placeholder="0.00"
                               class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'otherHouseHold1', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                               name="otherHouseHold1" id="otherHouseHold1"
                               value="<?php echo Currency::formatDollarAmountWithDecimalZeros(Strings::showField('otherHouseHold1', 'incomeInfo')) ?>"
                               size="20" maxlength="12"
                               onblur="currencyConverter(this, this.value);calculateHMLOBorrowerNetHouseholdIncome('loanModForm',  'primTotalNetHouseHoldIncome');"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'otherHouseHold1', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>
            </div>
            <div class="clearfix"></div>
            <!-- Other Income Description 1 -->
            <div class="form-group col-md-6 otherIncomeDescription1_disp <?php echo loanForm::showField('otherIncomeDescription1'); ?>">
                <?php echo loanForm::label('otherIncomeDescription1', 'col-md-12 '); ?>
                <div class="col-md-12">
                        <textarea id="otherIncomeDescription1"
                                  name="otherIncomeDescription1"
                                  class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'otherIncomeDescription1', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'otherIncomeDescription1', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                  TABINDEX="<?php echo $tabIndex++; ?>"><?php echo $otherIncomeDescription1; ?></textarea>
                </div>
            </div>
            <!-- //Other Income Description 1// -->
            <?php
            $borTotalExp = 1;
            ?>
            <div class="form-group row col-md-12 primTotalNetHouseHoldIncome_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'primTotalNetHouseHoldIncome', 'sArr' => $secArr, 'opt' => 'D', 'dpF' => '']); ?>">
                <?php echo loanForm::label('primTotalNetHouseHoldIncome', 'col-md-8 '); ?>
                <div class="col-md-4">
                    <h3>$
                        <span id="primTotalNetHouseHoldIncome"><?php echo Currency::formatDollarAmountWithDecimalZeros($primTotalNetHouseHoldIncome) ?></span>
                    </h3>(Borrower)
                </div>
            </div>
        </div>
    </div>
</div>
<?php
if ($activeTab != '1003') {
    $secArr = BaseHTML::sectionAccess2(['sId' => 'CBMI', 'opt' => $fileTab]); // Get Active Fields only...
    loanForm::pushSectionID('CBMI');

    ?>
    <div class="card card-custom HMLOLoanInfoSections coBorrowerSections CBMI CBMICard <?php if (count(Arrays::getValueFromArray('CBMI', $fieldsInfo)) <= 0) {
        echo 'secHide';
    } ?>" style="<?php echo $coBorDisp; ?>">
        <div class="card-header card-header-tabs-line bg-gray-100  ">
            <div class="card-title">
                <h3 class="card-label">
                    <?php echo BaseHTML::getSectionHeading('CBMI'); ?>
                </h3>
                <?php if (trim(BaseHTML::getSectionTooltip('CBMI')) != '') { ?>&nbsp;
                    <i class="popoverClass fas fa-info-circle text-primary "
                       data-html="true"
                       data-content="<?php echo BaseHTML::getSectionTooltip('CBMI'); ?>"></i>
                <?php } ?>
            </div>
            <div class="card-toolbar">
                <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none" data-card-tool="toggle"
                   data-toggle="tooltip" data-placement="top" title="Toggle Card">
                    <i class="ki ki-arrow-down icon-nm"></i>
                </a>
                <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none" data-card-tool="reload"
                   data-toggle="tooltip" data-placement="top" title="Reload Card">
                    <i class="ki ki-reload icon-nm"></i>
                </a>
                <a href="javascript:void(0);"
                   class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                   data-card-tool="toggle"
                   data-section="CBMICard"
                   data-toggle="tooltip" data-placement="top" title="Toggle Card">
                    <i class="ki ki-arrow-down icon-nm"></i>
                </a>
            </div>
        </div>
        <div class="card-body CBMICard_body">
            <div class="row">
                <div class="form-group col-md-6 grossIncome2_disp <?php echo loanForm::showField('grossIncome2'); ?>">
                    <?php echo loanForm::label('grossIncome2', 'col-md-12 '); ?>
                    <div class="col-md-12">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'grossIncome2', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="grossIncome2" id="grossIncome2"
                                   value="<?php echo Currency::formatDollarAmountWithDecimalZeros(Strings::showField('grossIncome2', 'incomeInfo')) ?>"
                                   maxlength="12"
                                   onblur="currencyConverter(this, this.value);calculateHMLOCoBorrowerNetHouseholdIncome('loanModForm',  'coTotalNetHouseHoldIncome');"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'grossIncome2', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                </div>
                <div class="form-group col-md-6 commissionOrBonus2_disp <?php echo loanForm::showField('commissionOrBonus2'); ?>">
                    <?php echo loanForm::label('commissionOrBonus2', 'col-md-12 '); ?>
                    <div class="col-md-12">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'grossIncome2', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="commissionOrBonus2" id="commissionOrBonus2"
                                   value="<?php echo Currency::formatDollarAmountWithDecimalZeros(Strings::showField('commissionOrBonus2', 'incomeInfo')) ?>"
                                   maxlength="12"
                                   onblur="currencyConverter(this, this.value);calculateHMLOCoBorrowerNetHouseholdIncome('loanModForm',  'coTotalNetHouseHoldIncome');"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'grossIncome2', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                        </div>
                    </div>
                </div>
                <div class="form-group col-md-6 overtime2_disp <?php echo loanForm::showField('overtime2'); ?>">
                    <?php echo loanForm::label('overtime2', 'col-md-12 '); ?>
                    <div class="col-md-12">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'overtime2', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="overtime2" id="overtime2"
                                   value="<?php echo Currency::formatDollarAmountWithDecimalZeros(Strings::showField('overtime2', 'incomeInfo')) ?>"
                                   maxlength="12"
                                   onblur="currencyConverter(this, this.value);calculateHMLOCoBorrowerNetHouseholdIncome('loanModForm',  'coTotalNetHouseHoldIncome');"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'overtime2', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                </div>
                <div class="form-group col-md-6 netRental2_disp <?php echo loanForm::showField('netRental2'); ?>">
                    <?php echo loanForm::label('netRental2', 'col-md-12 '); ?>
                    <div class="col-md-12">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'netRental2', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="netRental2" id="netRental2"
                                   value="<?php echo Currency::formatDollarAmountWithDecimalZeros(Strings::showField('netRental2', 'incomeInfo')) ?>"
                                   maxlength="12"
                                   onclick="clearMyMsg('loanModForm','netRental2','<?php echo $netDefaultText ?>');"
                                   onfocus="clearMyMsg('loanModForm','netRental2','<?php echo $netDefaultText ?>');"
                                   onblur="currencyConverter(this, this.value);putMyMsg('loanModForm','netRental2','<?php echo $netDefaultText ?>'), calculateHMLOCoBorrowerNetHouseholdIncome('loanModForm',  'coTotalNetHouseHoldIncome');"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'netRental2', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                </div>
                <div class="form-group col-md-6 netEarnedInterest2_disp <?php echo loanForm::showField('netEarnedInterest2'); ?>">
                    <?php echo loanForm::label('netEarnedInterest2', 'col-md-12 '); ?>
                    <div class="col-md-12">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'netEarnedInterest2', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="netEarnedInterest2" id="netEarnedInterest2"
                                   value="<?php echo Currency::formatDollarAmountWithDecimalZeros(Strings::showField('netEarnedInterest2', 'incomeInfo')) ?>"
                                   maxlength="12"
                                   onclick="clearMyMsg('loanModForm','netEarnedInterest2','<?php echo $netDefaultText ?>');"
                                   onfocus="clearMyMsg('loanModForm','netEarnedInterest2','<?php echo $netDefaultText ?>');"
                                   onblur="currencyConverter(this, this.value);putMyMsg('loanModForm','netEarnedInterest2','<?php echo $netDefaultText ?>'), calculateHMLOCoBorrowerNetHouseholdIncome('loanModForm',  'coTotalNetHouseHoldIncome');"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'netEarnedInterest2', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                </div>
                <!-- Capital Gains / (Losses) -->
                <div class="form-group col-md-6 capitalGains2_disp <?php echo loanForm::showField('capitalGains2'); ?>">
                    <?php echo loanForm::label('capitalGains2', 'col-md-12 '); ?>
                    <div class="col-md-12">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text" id="capitalGains2" name="capitalGains2"
                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'capitalGains2', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   value="<?php echo $capitalGains2; ?>" autocomplete="off"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'capitalGains2', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                   onblur="currencyConverter(this, this.value);calculateHMLOBorrowerNetHouseholdIncome('loanModForm',  'primTotalNetHouseHoldIncome');"
                                   TABINDEX="<?php echo $tabIndex++; ?>"/>
                        </div>
                    </div>
                </div>
                <!-- //Capital Gains / (Losses)// -->
                <!-- Partnership / Subchapter S Income -->
                <div class="form-group col-md-6 partnership2_disp <?php echo loanForm::showField('partnership2'); ?>">
                    <?php echo loanForm::label('partnership2', 'col-md-12 '); ?>
                    <div class="col-md-12">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text" id="partnership2" name="partnership2"
                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'partnership2', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   value="<?php echo $partnership2; ?>" autocomplete="off"
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'partnership2', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                   onblur="currencyConverter(this, this.value);calculateHMLOBorrowerNetHouseholdIncome('loanModForm',  'primTotalNetHouseHoldIncome');"
                                   TABINDEX="<?php echo $tabIndex++; ?>"/>
                        </div>
                    </div>
                </div>
                <!-- //Partnership / Subchapter S Income// -->
                <div class="form-group col-md-6 otherHouseHold2_disp <?php echo loanForm::showField('otherHouseHold2'); ?>">
                    <?php echo loanForm::label('otherHouseHold2', 'col-md-12 '); ?>
                    <div class="col-md-12">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'otherHouseHold2', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="otherHouseHold2" id="otherHouseHold2"
                                   value="<?php echo Currency::formatDollarAmountWithDecimalZeros(Strings::showField('otherHouseHold2', 'incomeInfo')) ?>"
                                   maxlength="12"
                                   onblur="currencyConverter(this, this.value);calculateHMLOCoBorrowerNetHouseholdIncome('loanModForm',  'coTotalNetHouseHoldIncome');"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'otherHouseHold2', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                </div>
                <div class="clearfix"></div>
                <!-- Other Income Description 2 -->
                <div class="form-group col-md-6 otherIncomeDescription2_disp <?php echo loanForm::showField('otherIncomeDescription2'); ?>">
                    <?php echo loanForm::label('otherIncomeDescription2', 'col-md-12 '); ?>
                    <div class="col-md-12">
                        <textarea id="otherIncomeDescription2" name="otherIncomeDescription2"
                                  class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'otherIncomeDescription2', 'sArr' => $secArr, 'opt' => 'M']); ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'otherIncomeDescription2', 'sArr' => $secArr, 'opt' => 'I']); ?> TABINDEX="<?php echo $tabIndex++; ?>"><?php echo $otherIncomeDescription2; ?></textarea>
                    </div>
                </div>
                <!-- //Other Income Description 2// -->
                <div class="clearfix"></div>
                <div class="row col-md-12">
                    <div class="form-group col-md-6">
                        <div class="col-md-12">
                            <div class="left">
                                <h3>$
                                    <span id="coTotalNetHouseHoldIncome"><?php echo Currency::formatDollarAmountWithDecimalZeros($coTotalNetHouseHoldIncome) ?></span>
                                </h3>(Co-Borrower)
                            </div>
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <div class="col-md-12">
                            <div class="left">
                                <h3>$
                                    <span id="subTotalNetHouseHoldIncome"><?php echo Currency::formatDollarAmountWithDecimalZeros($totalHouseHoldIncome) ?></span>
                                </h3>(Borr + Cobor)
                                <span id="totalHouseHoldIncome"
                                      style="display: none;"><?php echo Currency::formatDollarAmountWithDecimalZeros($totalHouseHoldIncome) ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php } ?>

<?php if ($activeTab != '1003') { ?>
    <?php

    $secArr = BaseHTML::sectionAccess2(['sId' => 'BME', 'opt' => $fileTab]); // Get Active Fields only...
    loanForm::pushSectionID('BME');

    ?>
    <div class="card card-custom HMLOLoanInfoSections BME BMECard <?php if (count(Arrays::getValueFromArray('BME', $fieldsInfo)) <= 0) {
        echo 'secHide';
    } ?>" style="<?php echo $HMLOLoanInfoSectionsDisp; ?>">

        <div class="card-header card-header-tabs-line bg-gray-100  ">
            <div class="card-title">
                <h3 class="card-label">
                    <?php echo BaseHTML::getSectionHeading('BME'); ?>
                </h3>
                <?php if (trim(BaseHTML::getSectionTooltip('BME')) != '') { ?>&nbsp;
                    <i class="popoverClass fas fa-info-circle text-primary "
                       data-html="true"
                       data-content="<?php echo BaseHTML::getSectionTooltip('BME'); ?>"></i>
                <?php } ?>
            </div>
            <div class="card-toolbar">
                <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none" data-card-tool="toggle"
                   data-toggle="tooltip" data-placement="top" title="Toggle Card">
                    <i class="ki ki-arrow-down icon-nm"></i>
                </a>
                <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none" data-card-tool="reload"
                   data-toggle="tooltip" data-placement="top" title="Reload Card">
                    <i class="ki ki-reload icon-nm"></i>
                </a>
                <a href="javascript:void(0);"
                   class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                   data-card-tool="toggle"
                   data-section="BMECard"
                   data-toggle="tooltip" data-placement="top" title="Toggle Card">
                    <i class="ki ki-arrow-down icon-nm"></i>
                </a>
            </div>
        </div>
        <div class="card-body BMECard_body">
            <div class="row col-md-12">
                <div class="form-group row col-md-12">
                    <div class="col-md-4"></div>
                    <div class="col-md-5"><h4>Monthly Payment</h4></div>
                    <div class="col-md-3"><h4>Bal Owed</h4></div>
                </div>
                <div class="form-group row col-md-12 lien1PaymentExp_disp <?php echo loanForm::showField('lien1PaymentExp'); ?>">
                    <?php echo loanForm::label('lien1PaymentExp', 'col-md-4 '); ?>
                    <div class="col-md-5"><h5>
                            $ <?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('lien1Payment', 'LMRInfo')) ?></h5>
                    </div>
                    <div class="col-md-3"><h5>
                            $ <?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('approvedLoanAmt', 'fileHMLOPropertyInfo')) ?></h5>
                    </div>
                </div>
                <div class="form-group row col-md-12 HMLORealEstateTaxes_disp <?php echo loanForm::showField('HMLORealEstateTaxes'); ?>">
                    <?php echo loanForm::label('HMLORealEstateTaxes', 'col-md-4 '); ?>
                    <div class="col-md-5"><h5>
                            $ <?php echo Currency::formatDollarAmountWithDecimal($HMLORealEstateTaxes); ?></h5>
                    </div>
                    <div class="col-md-3"></div>
                    <input type="hidden" name="HMLORealEstateTaxes" id="HMLORealEstateTaxes"
                           value="<?php echo $HMLORealEstateTaxes ?>"/>
                    <?php if ($activeTab != 'LI' && $activeTab != 'QAPP') { ?>
                        <input type="hidden" name="taxes1" id="taxes1"
                               value="<?php echo Strings::showField('taxes1', 'incomeInfo') ?>"/>
                    <?php } ?>
                </div>
                <div class="form-group row col-md-12 HOAFees1_disp <?php echo loanForm::showField('HOAFees1'); ?>">
                    <label class="col-md-4 font-weight-bold"> <?php echo BaseHTML::fieldAccess(['fNm' => 'HOAFees1', 'sArr' => $secArr, 'opt' => 'L']); ?></label>
                    <div class="col-md-5"><h5>
                            $ <?php echo Currency::formatDollarAmountWithDecimal($HOAFees1); ?></h5></div>
                    <div class="col-md-3"></div>
                    <input type="hidden" name="HOAFees1" id="HOAFees1" value="<?php echo $HOAFees1 ?>">
                </div>
                <div class="form-group row col-md-12 totalInsurance_disp <?php echo loanForm::showField('totalInsurance'); ?>">
                    <?php echo loanForm::label('totalInsurance', 'col-md-4 '); ?>
                    <div class="col-md-5"><h5>
                            $ <?php echo Currency::formatDollarAmountWithDecimal($totalInsurance) ?></h5>
                    </div>
                    <div class="col-md-3"></div>
                    <input type="hidden" name="totalInsurance" id="totalInsurance"
                           value="<?php echo $totalInsurance ?>">
                </div>
                <div class="form-group row col-md-12 otherMortgage1_disp <?php echo loanForm::showField('otherMortgage1'); ?>">
                    <?php echo loanForm::label('otherMortgage1', 'col-md-4 '); ?>
                    <div class="col-md-5">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'otherMortgage1', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   placeholder="0.00"
                                   name="otherMortgage1" id="otherMortgage1"
                                   onblur="currencyConverter(this, this.value);calculatePrimaryTotalHouseHoldExpenses(this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($otherMortgage1) ?>"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'otherMortgage1', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control input-sm" name="otherMortgageBalance1"
                               placeholder="0.00"
                               id="otherMortgageBalance1"
                               onblur="currencyConverter(this, this.value);"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($otherMortgageBalance1) ?>"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'otherMortgage1', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>
                <div class="form-group row col-md-12 unsecuredLoans1_disp  <?php echo loanForm::showField('unsecuredLoans1'); ?>">
                    <?php echo loanForm::label('unsecuredLoans1', 'col-md-4 '); ?>
                    <div class="col-md-5">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'unsecuredLoans1', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   placeholder="0.00"
                                   name="unsecuredLoans1" id="unsecuredLoans1"
                                   onblur="currencyConverter(this, this.value);calculatePrimaryTotalHouseHoldExpenses(this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($unsecuredLoans1) ?>"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'unsecuredLoans1', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control input-sm" name="unsecuredLoanBalance1"
                               id="unsecuredLoanBalance1"
                               onblur="currencyConverter(this, this.value);"
                               placeholder="0.00"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($unsecuredLoanBalance1) ?>"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'unsecuredLoans1', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>
                <div class="form-group row col-md-12 creditCards1_disp <?php echo loanForm::showField('creditCards1'); ?>">
                    <?php echo loanForm::label('creditCards1', 'col-md-4 '); ?>
                    <div class="col-md-5">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'creditCards1', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   placeholder="0.00"
                                   name="creditCards1" id="creditCards1"
                                   onblur="currencyConverter(this, this.value);calculatePrimaryTotalHouseHoldExpenses(this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($creditCards1) ?>"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'creditCards1', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control input-sm" name="creditCardsBalance1"
                               id="creditCardsBalance1"
                               onblur="currencyConverter(this, this.value);"
                               placeholder="0.00"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($creditCardsBalance1) ?>"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'creditCards1', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>
                <div class="form-group row col-md-12 studentLoans1_disp <?php echo loanForm::showField('studentLoans1'); ?>">
                    <?php echo loanForm::label('studentLoans1', 'col-md-4 '); ?>
                    <div class="col-md-5">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'studentLoans1', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   placeholder="0.00"
                                   name="studentLoans1" id="studentLoans1"
                                   onblur="currencyConverter(this, this.value);calculatePrimaryTotalHouseHoldExpenses(this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($studentLoans1) ?>"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'studentLoans1', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control input-sm" name="studentLoansBalance1"
                               id="studentLoansBalance1"
                               onblur="currencyConverter(this, this.value);"
                               placeholder="0.00"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($studentLoansBalance1) ?>"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'studentLoans1', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>


                <div class="form-group row col-md-12 childSupportOrAlimonyMonthly1_disp  <?php echo loanForm::showField('childSupportOrAlimonyMonthly1'); ?>">
                    <?php echo loanForm::label('childSupportOrAlimonyMonthly1', 'col-md-4 '); ?>
                    <div class="col-md-5">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'childSupportOrAlimonyMonthly1', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   placeholder="0.00"
                                   name="childSupportOrAlimonyMonthly1" id="childSupportOrAlimonyMonthly1"
                                   onblur="currencyConverter(this, this.value);calculatePrimaryTotalHouseHoldExpenses(this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($childSupportOrAlimonyMonthly1) ?>"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'childSupportOrAlimonyMonthly1', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control input-sm"
                               name="childSupportOrAlimonyMonthlyBalance1"
                               id="childSupportOrAlimonyMonthlyBalance1"
                               placeholder="0.00"
                               onblur="currencyConverter(this, this.value);"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($childSupportOrAlimonyMonthlyBalance1) ?>"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'childSupportOrAlimonyMonthly1', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>

                <!---- Start ********* -->

                <div class="form-group row col-md-12 expFedTax_disp  <?php echo loanForm::showField('expFedTax'); ?>">
                    <?php echo loanForm::label('expFedTax', 'col-md-4 '); ?>
                    <div class="col-md-5">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'expFedTax', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   placeholder="0.00"
                                   name="expFedTax" id="expFedTax"
                                   onblur="currencyConverter(this, this.value);calculatePrimaryTotalHouseHoldExpenses(this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($expFedTax) ?>"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'expFedTax', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control input-sm"
                               name="expFedTaxOwed"
                               id="expFedTaxOwed"
                               placeholder="0.00"
                               onblur="currencyConverter(this, this.value);"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($expFedTaxOwed) ?>"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'expFedTax', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>


                <div class="form-group row col-md-12 expStateTax_disp  <?php echo loanForm::showField('expStateTax'); ?>">
                    <?php echo loanForm::label('expStateTax', 'col-md-4 '); ?>
                    <div class="col-md-5">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'expStateTax', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   placeholder="0.00"
                                   name="expStateTax" id="expStateTax"
                                   onblur="currencyConverter(this, this.value);calculatePrimaryTotalHouseHoldExpenses(this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($expStateTax) ?>"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'expStateTax', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control input-sm"
                               name="expStateTaxOwed"
                               id="expStateTaxOwed"
                               placeholder="0.00"
                               onblur="currencyConverter(this, this.value);"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($expStateTaxOwed) ?>"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'expStateTax', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>


                <div class="form-group row col-md-12 expRentalPay_disp  <?php echo loanForm::showField('expRentalPay'); ?>">
                    <?php echo loanForm::label('expRentalPay', 'col-md-4 '); ?>
                    <div class="col-md-5">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'expRentalPay', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   placeholder="0.00"
                                   name="expRentalPay" id="expRentalPay"
                                   onblur="currencyConverter(this, this.value);calculatePrimaryTotalHouseHoldExpenses(this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($expRentalPay) ?>"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'expRentalPay', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control input-sm"
                               name="expRentalPayOwed"
                               id="expRentalPayOwed"
                               placeholder="0.00"
                               onblur="currencyConverter(this, this.value);"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($expRentalPayOwed) ?>"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'expRentalPay', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>


                <div class="form-group row col-md-12 expMortgPayResi_disp  <?php echo loanForm::showField('expMortgPayResi'); ?>">
                    <?php echo loanForm::label('expMortgPayResi', 'col-md-4 '); ?>
                    <div class="col-md-5">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'expMortgPayResi', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   placeholder="0.00"
                                   name="expMortgPayResi" id="expMortgPayResi"
                                   onblur="currencyConverter(this, this.value);calculatePrimaryTotalHouseHoldExpenses(this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($expMortgPayResi) ?>"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'expMortgPayResi', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control input-sm"
                               name="expMortgPayResiOwed"
                               id="expMortgPayResiOwed"
                               placeholder="0.00"
                               onblur="currencyConverter(this, this.value);"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($expMortgPayResiOwed) ?>"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'expMortgPayResi', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>


                <div class="form-group row col-md-12 expMortgPayInvest_disp  <?php echo loanForm::showField('expMortgPayInvest'); ?>">
                    <?php echo loanForm::label('expMortgPayInvest', 'col-md-4 '); ?>
                    <div class="col-md-5">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'expMortgPayInvest', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   placeholder="0.00"
                                   name="expMortgPayInvest" id="expMortgPayInvest"
                                   onblur="currencyConverter(this, this.value);calculatePrimaryTotalHouseHoldExpenses(this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($expMortgPayInvest) ?>"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'expMortgPayInvest', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control input-sm"
                               name="expMortgPayInvestOwed"
                               id="expMortgPayInvestOwed"
                               placeholder="0.00"
                               onblur="currencyConverter(this, this.value);"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($expMortgPayInvestOwed) ?>"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'expMortgPayInvest', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>


                <div class="form-group row col-md-12 expPropTaxResi_disp  <?php echo loanForm::showField('expPropTaxResi'); ?>">
                    <?php echo loanForm::label('expPropTaxResi', 'col-md-4 '); ?>
                    <div class="col-md-5">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'expPropTaxResi', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   placeholder="0.00"
                                   name="expPropTaxResi" id="expPropTaxResi"
                                   onblur="currencyConverter(this, this.value);calculatePrimaryTotalHouseHoldExpenses(this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($expPropTaxResi) ?>"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'expPropTaxResi', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control input-sm"
                               name="expPropTaxResiOwed"
                               id="expPropTaxResiOwed"
                               placeholder="0.00"
                               onblur="currencyConverter(this, this.value);"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($expPropTaxResiOwed) ?>"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'expPropTaxResi', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>


                <div class="form-group row col-md-12 expPropTaxInvest_disp  <?php echo loanForm::showField('expPropTaxInvest'); ?>">
                    <?php echo loanForm::label('expPropTaxInvest', 'col-md-4 '); ?>
                    <div class="col-md-5">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'expPropTaxInvest', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   placeholder="0.00"
                                   name="expPropTaxInvest" id="expPropTaxInvest"
                                   onblur="currencyConverter(this, this.value);calculatePrimaryTotalHouseHoldExpenses(this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($expPropTaxInvest) ?>"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'expPropTaxInvest', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control input-sm"
                               name="expPropTaxInvestOwed"
                               id="expPropTaxInvestOwed"
                               placeholder="0.00"
                               onblur="currencyConverter(this, this.value);"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($expPropTaxInvestOwed) ?>"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'expPropTaxInvest', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>

                <div class="form-group row col-md-12 expLoanPayments_disp  <?php echo loanForm::showField('expLoanPayments'); ?>">
                    <?php echo loanForm::label('expLoanPayments', 'col-md-4 '); ?>
                    <div class="col-md-5">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'expLoanPayments', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   placeholder="0.00"
                                   name="expLoanPayments" id="expLoanPayments"
                                   onblur="currencyConverter(this, this.value);calculatePrimaryTotalHouseHoldExpenses(this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($expLoanPayments) ?>"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'expLoanPayments', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control input-sm"
                               name="expLoanPaymentsOwed"
                               id="expLoanPaymentsOwed"
                               placeholder="0.00"
                               onblur="currencyConverter(this, this.value);"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($expLoanPaymentsOwed) ?>"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'expLoanPayments', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>


                <div class="form-group row col-md-12 expIns_disp  <?php echo loanForm::showField('expIns'); ?>">
                    <?php echo loanForm::label('expIns', 'col-md-4 '); ?>
                    <div class="col-md-5">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'expIns', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   placeholder="0.00"
                                   name="expIns" id="expIns"
                                   onblur="currencyConverter(this, this.value);calculatePrimaryTotalHouseHoldExpenses(this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($expIns) ?>"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'expIns', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control input-sm"
                               name="expInsOwed"
                               id="expInsOwed"
                               placeholder="0.00"
                               onblur="currencyConverter(this, this.value);"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($expInsOwed) ?>"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'expIns', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>


                <div class="form-group row col-md-12 expInvestments_disp  <?php echo loanForm::showField('expInvestments'); ?>">
                    <?php echo loanForm::label('expInvestments', 'col-md-4 '); ?>
                    <div class="col-md-5">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'expInvestments', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   placeholder="0.00"
                                   name="expInvestments" id="expInvestments"
                                   onblur="currencyConverter(this, this.value);calculatePrimaryTotalHouseHoldExpenses(this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($expInvestments) ?>"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'expInvestments', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control input-sm"
                               name="expInvestmentsOwed"
                               id="expInvestmentsOwed"
                               placeholder="0.00"
                               onblur="currencyConverter(this, this.value);"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($expInvestmentsOwed) ?>"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'expInvestments', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>


                <div class="form-group row col-md-12 expTuition_disp  <?php echo loanForm::showField('expTuition'); ?>">
                    <?php echo loanForm::label('expTuition', 'col-md-4 '); ?>
                    <div class="col-md-5">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'expTuition', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   placeholder="0.00"
                                   name="expTuition" id="expTuition"
                                   onblur="currencyConverter(this, this.value);calculatePrimaryTotalHouseHoldExpenses(this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($expTuition) ?>"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'expTuition', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control input-sm"
                               name="expTuitionOwed"
                               id="expTuitionOwed"
                               placeholder="0.00"
                               onblur="currencyConverter(this, this.value);"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($expTuitionOwed) ?>"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'expTuition', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>


                <div class="form-group row col-md-12 expOtherLiving_disp  <?php echo loanForm::showField('expOtherLiving'); ?>">
                    <?php echo loanForm::label('expOtherLiving', 'col-md-4 '); ?>
                    <div class="col-md-5">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'expOtherLiving', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   placeholder="0.00"
                                   name="expOtherLiving" id="expOtherLiving"
                                   onblur="currencyConverter(this, this.value);calculatePrimaryTotalHouseHoldExpenses(this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($expOtherLiving) ?>"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'expOtherLiving', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control input-sm"
                               name="expOtherLivingOwed"
                               id="expOtherLivingOwed"
                               placeholder="0.00"
                               onblur="currencyConverter(this, this.value);"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($expOtherLivingOwed) ?>"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'expOtherLiving', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>


                <div class="form-group row col-md-12 expMedical_disp  <?php echo loanForm::showField('expMedical'); ?>">
                    <?php echo loanForm::label('expMedical', 'col-md-4 '); ?>
                    <div class="col-md-5">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'expMedical', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   placeholder="0.00"
                                   name="expMedical" id="expMedical"
                                   onblur="currencyConverter(this, this.value);calculatePrimaryTotalHouseHoldExpenses(this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($expMedical) ?>"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'expMedical', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control input-sm"
                               name="expMedicalOwed"
                               id="expMedicalOwed"
                               placeholder="0.00"
                               onblur="currencyConverter(this, this.value);"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($expMedicalOwed) ?>"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'expMedical', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>


                <!---- end ********* -->

                <div class="form-group row col-md-12 other1_disp <?php echo loanForm::showField('other1'); ?>">
                    <?php echo loanForm::label('other1', 'col-md-4 '); ?>
                    <div class="col-md-5">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   placeholder="0.00"
                                   class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'other1', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="other1" size="20" id="other1"
                                   onblur="currencyConverter(this, this.value);calculatePrimaryTotalHouseHoldExpenses(this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($other1) ?>"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'other1', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control input-sm" name="otherBalance1" id="otherBalance1"
                               placeholder="0.00"
                               onblur="currencyConverter(this, this.value);"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($otherBalance1) ?>"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'other1', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>
                <?php
                $coBorTotalExp = 1;
                ?>
                <div class="form-group row col-md-12 primTotalHouseHoldExpenses_disp <?php echo loanForm::showField('primTotalHouseHoldExpenses'); ?>">
                    <?php echo loanForm::label('primTotalHouseHoldExpenses', 'col-md-4 font-weight-bold h3'); ?>
                    <div class="col-md-5">
                        <div class="left">
                            <h3>$
                                <span id="primTotalHouseHoldExpenses"><?php echo Currency::formatDollarAmountWithDecimal($primTotalHouseHoldExpenses) ?></span>
                            </h3>(Borrower)
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
    $secArr = BaseHTML::sectionAccess2(['sId' => 'CBME', 'opt' => $fileTab]); // Get Active Fields only...
    loanForm::pushSectionID('CBME');

    ?>
    <div class="card card-custom HMLOLoanInfoSections coBorrowerSections CBME CBMECard <?php if (count(Arrays::getValueFromArray('CBME', $fieldsInfo)) <= 0) {
        echo 'secHide';
    } ?>" style="<?php echo $coBorDisp; ?><?php echo $HMLOLoanInfoSectionsDisp; ?>">
        <div class="card-header card-header-tabs-line bg-gray-100  ">
            <div class="card-title">
                <h3 class="card-label">
                    <?php echo BaseHTML::getSectionHeading('CBME'); ?>
                </h3>
                <?php if (trim(BaseHTML::getSectionTooltip('CBME')) != '') { ?>&nbsp;
                    <i class="popoverClass fas fa-info-circle text-primary "
                       data-html="true"
                       data-content="<?php echo BaseHTML::getSectionTooltip('CBME'); ?>"></i>
                <?php } ?>
            </div>
            <div class="card-toolbar">
                <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none" data-card-tool="toggle"
                   data-toggle="tooltip" data-placement="top" title="Toggle Card">
                    <i class="ki ki-arrow-down icon-nm"></i>
                </a>
                <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none" data-card-tool="reload"
                   data-toggle="tooltip" data-placement="top" title="Reload Card">
                    <i class="ki ki-reload icon-nm"></i>
                </a>
                <a href="javascript:void(0);"
                   class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                   data-card-tool="toggle"
                   data-section="CBMECard"
                   data-toggle="tooltip" data-placement="top" title="Toggle Card">
                    <i class="ki ki-arrow-down icon-nm"></i>
                </a>
            </div>
        </div>
        <div class="card-body CBMECard_body">
            <div class="row col-md-12">
                <div class="form-group row col-md-12">
                    <div class="col-md-4"></div>
                    <div class="col-md-5"><h4>Monthly Payment</h4></div>
                    <div class="col-md-3"><h4>Bal Owed</h4></div>
                </div>
                <div class="form-group row col-md-12 otherMortgage2_disp <?php echo loanForm::showField('otherMortgage2'); ?>">
                    <?php echo loanForm::label('otherMortgage2', 'col-md-4 '); ?>
                    <div class="col-md-5">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'otherMortgage2', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="otherMortgage2" id="otherMortgage2"
                                   onblur="currencyConverter(this, this.value);calculateCoBorrowerTotalHouseHoldExpenses(this.value)"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($otherMortgage2) ?>"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'otherMortgage2', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control input-sm" name="otherMortgageBalance2"
                               id="otherMortgageBalance2"
                               onblur="currencyConverter(this, this.value);calculateCoBorrowerTotalHouseHoldExpenses(this.value)"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($otherMortgageBalance2) ?>"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'otherMortgage2', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>
                <div class="form-group row col-md-12 unsecuredLoans2_disp <?php echo loanForm::showField('unsecuredLoans2'); ?>">
                    <?php echo loanForm::label('unsecuredLoans2', 'col-md-4 '); ?>
                    <div class="col-md-5">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'unsecuredLoans2', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="unsecuredLoans2" id="unsecuredLoans2"
                                   onblur="currencyConverter(this, this.value);calculateCoBorrowerTotalHouseHoldExpenses(this.value)"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($unsecuredLoans2) ?>"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'unsecuredLoans2', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control input-sm" name="unsecuredLoanBalance2"
                               id="unsecuredLoanBalance2"
                               onblur="currencyConverter(this, this.value);calculateCoBorrowerTotalHouseHoldExpenses(this.value)"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($unsecuredLoanBalance2) ?>"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'unsecuredLoans2', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>
                <div class="form-group row col-md-12 creditCards2_disp <?php echo loanForm::showField('creditCards2'); ?>">
                    <?php echo loanForm::label('creditCards2', 'col-md-4 '); ?>
                    <div class="col-md-5">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'creditCards2', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="creditCards2" id="creditCards2"
                                   onblur="currencyConverter(this, this.value);calculateCoBorrowerTotalHouseHoldExpenses(this.value)"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($creditCards2) ?>"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'creditCards2', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control input-sm" name="creditCardsBalance2"
                               id="creditCardsBalance2"
                               onblur="currencyConverter(this, this.value);calculateCoBorrowerTotalHouseHoldExpenses(this.value)"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($creditCardsBalance2) ?>"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'creditCards2', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>
                <div class="form-group row col-md-12 studentLoans2_disp <?php echo loanForm::showField('studentLoans2'); ?>">
                    <?php echo loanForm::label('studentLoans2', 'col-md-4 '); ?>
                    <div class="col-md-5">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'studentLoans2', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="studentLoans2" id="studentLoans2"
                                   onblur="currencyConverter(this, this.value);calculateCoBorrowerTotalHouseHoldExpenses(this.value)"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($studentLoans2) ?>"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'studentLoans2', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control input-sm" name="studentLoansBalance2"
                               id="studentLoansBalance2"
                               onblur="currencyConverter(this, this.value);calculateCoBorrowerTotalHouseHoldExpenses(this.value)"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($studentLoansBalance2) ?>"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'studentLoans2', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>
                <div class="form-group row col-md-12 childSupportOrAlimonyMonthly2_disp <?php echo loanForm::showField('childSupportOrAlimonyMonthly2'); ?>">
                    <?php echo loanForm::label('childSupportOrAlimonyMonthly2', 'col-md-4 '); ?>
                    <div class="col-md-5">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'childSupportOrAlimonyMonthly2', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="childSupportOrAlimonyMonthly2" id="childSupportOrAlimonyMonthly2"
                                   onblur="currencyConverter(this, this.value);calculateCoBorrowerTotalHouseHoldExpenses(this.value)"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($childSupportOrAlimonyMonthly2) ?>"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'childSupportOrAlimonyMonthly2', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control input-sm"
                               name="childSupportOrAlimonyMonthlyBalance2"
                               id="childSupportOrAlimonyMonthlyBalance2"
                               onblur="currencyConverter(this, this.value);calculateCoBorrowerTotalHouseHoldExpenses(this.value)"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($childSupportOrAlimonyMonthlyBalance2) ?>"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'childSupportOrAlimonyMonthly2', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>
                <div class="form-group row col-md-12 other2_disp <?php echo loanForm::showField('other2'); ?>">
                    <?php echo loanForm::label('other2', 'col-md-4 '); ?>
                    <div class="col-md-5">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'other2', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                   name="other2" id="other2"
                                   onblur="currencyConverter(this, this.value);calculateCoBorrowerTotalHouseHoldExpenses(this.value)"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal($other2) ?>"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'other2', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <input type="text"
                               class="form-control input-sm"
                               name="otherBalance2"
                               id="otherBalance2"
                               onblur="currencyConverter(this, this.value);calculateCoBorrowerTotalHouseHoldExpenses(this.value)"
                               value="<?php echo Currency::formatDollarAmountWithDecimal($otherBalance2) ?>"
                               TABINDEX="<?php echo $tabIndex++; ?>"
                               autocomplete="off"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'other2', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                    </div>
                </div>

                <div class="form-group row col-md-12 coTotalHouseHoldExpenses_disp <?php echo loanForm::showField('coTotalHouseHoldExpenses'); ?>">
                    <?php echo loanForm::label('coTotalHouseHoldExpenses', 'col-md-4 font-weight-bold h3'); ?>
                    <div class="col-md-5">
                        <div class="left">
                            <h3>$
                                <span id="coTotalHouseHoldExpenses"><?php echo Currency::formatDollarAmountWithDecimal($coTotalHouseHoldExpenses) ?></span>
                            </h3>(Co-Borrower)
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="left">
                            <h3>$
                                <span id="subTotalHouseHoldExpenses"><?php echo Currency::formatDollarAmountWithDecimal($totalHouseHoldExpenses) ?></span>
                            </h3>(Borr + Cobor)
                        </div>
                    </div>
                </div>
                <?php //} ?>
            </div>
        </div>
    </div>

<?php } ?>

<?php
if (!$allowToEdit) { ?>
    <script type="text/javascript">
        $(document).ready(function () {
            disableFormFields('loanModForm');

            function disableFormFields(oForm) {
//		   document.getElementById(oForm).className = 'grey';
                var frm_elements = document.getElementById(oForm).elements;
                for (i = 0; i < frm_elements.length; i++) {
                    field_type = frm_elements[i].type.toLowerCase();
                    frm_elements[i].disabled = true;
                }
            }

        });
    </script>
<?php } ?>
<script type="text/javascript">
    /* borrower self-employed start script */
    var dropdown = $('#employedInfo1').val();
    if (dropdown === 'Self-Employed') {
        $(".borpg_employmentType_Disp").show();
    } else {
        $(".borpg_employmentType_Disp").hide();
    }
    $(document).on('change', '#employedInfo1', function () {
        var empType = $(this).val();
        if (empType === 'Self-Employed') {
            $(".borpg_employmentType_Disp").show();
        } else { //reset values hide the rows
            $("input[type='radio'][name='emptypeshare1']").prop('checked', false);
            $('#empmonthlyincome1').val('');
            $(".borpg_employmentType_Disp").hide();
        }
    });
    /* borrower self-employed end script */

    /* co-borrower self-employed start script */
    var dropdown2 = $('#employedInfo2').val();
    if (dropdown2 === 'Self-Employed') {
        $(".coborpg_employmentType2_Disp").show();
    } else {
        $(".coborpg_employmentType2_Disp").hide();
    }
    $(document).on('change', '#employedInfo2', function () {
        var empType2 = $(this).val();
        if (empType2 === 'Self-Employed') {
            $(".coborpg_employmentType2_Disp").show();
        } else { //reset values hide the rows
            $("input[type='radio'][name='emptypeshare2']").prop('checked', false);
            $('#empmonthlyincome2').val('');
            $(".coborpg_employmentType2_Disp").hide();
        }
    });

    $(document).on('change', '.coboremploymentType', function () {
        let coborEmpType = $(this).val();
        let idArray = [];
        idArray = (this.id).split('_');
        if (coborEmpType === 'Self-Employed') {
            $(".coboremploymentType_" + idArray[1] + "_Disp").removeClass('d-none');
        } else { //reset values hide the rows
            $("input[name='AdditionalCoBorEmplInfo[" + idArray[1] + "][emptypeshare]']").prop('checked', false);
            $('#AdditionalCoBorEmplInfo_' + idArray[1] + '_empmonthlyincome').val('');
            $(".coboremploymentType_" + idArray[1] + "_Disp").addClass('d-none');
        }
    });

    $(document).on('change', '.employmentType', function () {
        let borEmpType = $(this).val();
        let idArray = [];
        idArray = (this.id).split('_');
        if (borEmpType === 'Self-Employed') {
            $(".employmentType_" + idArray[1] + "_Disp").removeClass('d-none');
        } else { //reset values hide the rows
            $("input[name='AdditionalBorEmplInfo[" + idArray[1] + "][emptypeshare]']").prop('checked', false);
            $('#AdditionalBorEmplInfo_' + idArray[1] + '_empmonthlyincome').val('');
            $(".employmentType_" + idArray[1] + "_Disp").addClass('d-none');
        }
    });

    /* co-borrower self-employed end script */
</script>

<!-- incExpWFForm.php -->
