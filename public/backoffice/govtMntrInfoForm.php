<?php
global $myFileInfo, $allowToEdit, $publicUser;

use models\constants\EthnicityRaceGenderVeteran;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glHMLOLienPosition;
use models\constants\HMDAActionTaken;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\loanForm;
use models\cypher;
use models\lendingwise\db\tblFileHMLOPropInfo_db;
use models\lendingwise\db\tblFilePropertyInfo_db;
use models\lendingwise\tblFileHMLOPropInfo;
use models\lendingwise\tblFilePropertyInfo;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;
use pages\backoffice\loan\adverse_action\classes\AdverseActionController;
use pages\backoffice\loan\web_form\HMLO\classes\HMLOController;

$QAInfo = [];
$LMRInfo = [];
$tabIndexNo = 1;
$isCoBorrower = 0;
$PublishBInfo = 3;
$PublishCBInfo = '3';
$BEthnicity = [];
$CBEthnicity = [];
$BGender = '';
$CBGender = '';
$BRace = [];
$CBRace = [];
$BVeteran = '';
$bFiEthnicity = '';
$bFiEthnicitySub = [];
$bFiEthnicitySubOther = '';
$bFiSex = '';
$bFiRace = '';
$bFiRaceSub = [];
$bFiRaceAsianOther = '';
$bFiRacePacificOther = '';
$bDemoInfo = '';

/*HMDA - (Co-Borrower)*/
$CBVeteran = '';
$CBFiEthnicity = '';
$CBFiGender = '';
$CBFiRace = '';
$CBDDemoInfo = '';
$CBEthnicitySub = [];
$CBEthnicitySubOther = '';
$CBRaceSub = [];
$CBRaceAsianOther = '';
$CBRacePacificOther = '';
/*HMDA - (Co-Borrower)*/

if (count($secArr = BaseHTML::sectionAccess2(['sId' => 'GOVT-GI', 'opt' => HMLOController::$fileTab])) > 0) { // Get Active Fields only...
    loanForm::pushSectionID('GOVT-GI');
}

if (count($myFileInfo) > 0) {
    if (array_key_exists('LMRInfo', $myFileInfo)) $LMRInfo = $myFileInfo['LMRInfo'];
    if (array_key_exists('QAInfo', $myFileInfo)) $QAInfo = $myFileInfo['QAInfo'];
    /** Fetch Q+A info **/
}

if (count($LMRInfo) > 0) {
    $isCoBorrower = Strings::showField('isCoBorrower', 'LMRInfo');
    //$recordDate = $LMRInfo['recordDate'] ? Dates::formatDateWithRE($LMRInfo['recordDate'], 'YMD', 'm/d/Y') : '';
}
if (count($QAInfo) > 0) {
    $PublishBInfo = Strings::showField('PublishBInfo', 'QAInfo');
    $BEthnicity = Strings::showField('BEthnicity', 'QAInfo');
    $BEthnicity = $BEthnicity != '' ? explode(',', $BEthnicity) : [];
    $BRace = Strings::showField('BRace', 'QAInfo');
    $BRace = $BRace != '' ? explode(',', $BRace) : [];
    $BGender = Strings::showField('BGender', 'QAInfo');
    $BVeteran = Strings::showField('BVeteran', 'QAInfo');
    $bFiEthnicity = Strings::showField('bFiEthnicity', 'QAInfo');
    $bFiEthnicitySub = Strings::showField('bFiEthnicitySub', 'QAInfo');
    $bFiEthnicitySub = $bFiEthnicitySub != '' ? explode(',', $bFiEthnicitySub) : [];
    $bFiEthnicitySubOther = Strings::showField('bFiEthnicitySubOther', 'QAInfo');
    $bFiSex = Strings::showField('bFiSex', 'QAInfo');
    $bFiRace = Strings::showField('bFiRace', 'QAInfo');
    $bFiRaceSub = Strings::showField('bFiRaceSub', 'QAInfo');
    $bFiRaceSub = $bFiRaceSub != '' ? explode(',', $bFiRaceSub) : [];
    $bFiRaceAsianOther = Strings::showField('bFiRaceAsianOther', 'QAInfo');
    $bFiRacePacificOther = Strings::showField('bFiRacePacificOther', 'QAInfo');
    $bDemoInfo = Strings::showField('bDemoInfo', 'QAInfo');
    $PublishCBInfo = Strings::showField('PublishCBInfo', 'QAInfo');
    $CBEthnicity = Strings::showField('CBEthnicity', 'QAInfo');
    $CBEthnicity = $CBEthnicity != '' ? explode(',', $CBEthnicity) : [];
    $CBEthnicitySub = Strings::showField('CBEthnicitySub', 'QAInfo');
    $CBEthnicitySub = $CBEthnicitySub != '' ? explode(',', $CBEthnicitySub) : [];
    $CBEthnicitySubOther = Strings::showField('CBEthnicitySubOther', 'QAInfo');
    $CBRace = Strings::showField('CBRace', 'QAInfo');
    $CBRace = $CBRace != '' ? explode(',', $CBRace) : [];
    $CBRaceSub = Strings::showField('CBRaceSub', 'QAInfo');
    $CBRaceSub = $CBRaceSub != '' ? explode(',', $CBRaceSub) : [];
    $CBRaceAsianOther = Strings::showField('CBRaceAsianOther', 'QAInfo');
    $CBRacePacificOther = Strings::showField('CBRacePacificOther', 'QAInfo');
    $CBGender = Strings::showField('CBGender', 'QAInfo');
    $CBVeteran = Strings::showField('CBVeteran', 'QAInfo');
    $CBFiEthnicity = Strings::showField('CBFiEthnicity', 'QAInfo');
    $CBFiGender = Strings::showField('CBFiGender', 'QAInfo');
    $CBFiRace = Strings::showField('CBFiRace', 'QAInfo');
    $CBDDemoInfo = Strings::showField('CBDDemoInfo', 'QAInfo');
}


//New Fields added

$HMDAActionTakenArray = HMDAActionTaken::$getHMDAActionTaken;
$typeOfPurchaserArray = HMDAActionTaken::$typeOfPurchaser;
$reasonForDenialArray = HMDAActionTaken::$reasonForDenial;
$creditScoringModelArray = HMDAActionTaken::$creditScoringModel;
$HMDALoanPurposeArray = glCustomJobForProcessingCompany::isPC_CV3(PageVariables::$PCID)
    ? HMDAActionTaken::$HMDALoanPurpose_CV3
    : HMDAActionTaken::$HMDALoanPurpose;

$excludeFromHMDAReport = $QAInfo['excludeFromHMDAReport'] ?? '';
$legalEntityIdentifier = $QAInfo['legalEntityIdentifier'] ?? '';
$introductoryRatePeriod = $QAInfo['introductoryRatePeriod'] ?? '';
$universalLoanIdentifier = $QAInfo['universalLoanIdentifier'] ?? '';
$interestOnlyPayment = Currency::formatDollarAmountWithDecimalZeros($QAInfo['interestOnlyPayment']) ?? '';
$actionTaken = $QAInfo['actionTaken'] ?? '';
$propertyValue = Currency::formatDollarAmountWithDecimalZeros($QAInfo['propertyValue']) ?? '';
$typeOfPurchaser = $QAInfo['typeOfPurchaser'] ?? '';
$totalUnits = $QAInfo['totalUnits'] ?? '';
$reasonForDenial = explode('~', $QAInfo['reasonForDenial'] ?? '') ?? '';
$reasonForDenialOther = $QAInfo['reasonForDenialOther'] ?? '';
$submissionOfApplicationFrom = $QAInfo['submissionOfApplicationFrom'] ?? '';
$combinedLoanToValueRatio = $QAInfo['combinedLoanToValueRatio'] ?? '';
$censusTract = $QAInfo['censusTract'] ?? '';
$HMDALoanPurpose = $QAInfo['HMDALoanPurpose'] ?? 0;
$HMDALoanAmount = $QAInfo['HMDALoanAmount'] ?? '';

$borrowerRaceOfApplicant = $QAInfo['borrowerRaceOfApplicant'] ?? '';
$borrowerAgeOfApplicant = $QAInfo['borrowerAgeOfApplicant'] ?? '';
$borrowerCreditScoreOfApplicant = $QAInfo['borrowerCreditScoreOfApplicant'] ?? '';
$borrowerCreditScoringModelOfApplicant = $QAInfo['borrowerCreditScoringModelOfApplicant'] ?? '';
$borrowerCreditScoringModelOfApplicantOther = $QAInfo['borrowerCreditScoringModelOfApplicantOther'] ?? '';
$borrowerCreditScoringModelConditionalFreeOfApplicant = $QAInfo['borrowerCreditScoringModelConditionalFreeOfApplicant'] ?? '';

$coBorrowerRaceOfApplicant = $QAInfo['coBorrowerRaceOfApplicant'] ?? '';
$coBorrowerAgeOfApplicant = $QAInfo['coBorrowerAgeOfApplicant'] ?? '';
$coBorrowerCreditScoreOfApplicant = $QAInfo['coBorrowerCreditScoreOfApplicant'] ?? '';
$coBorrowerCreditScoringModelOfApplicant = $QAInfo['coBorrowerCreditScoringModelOfApplicant'] ?? '';
$coBorrowerCreditScoringModelOfApplicantOther = $QAInfo['coBorrowerCreditScoringModelOfApplicantOther'] ?? '';
$coBorrowerCreditScoringModelConditionalFreeOfApplicant = $QAInfo['coBorrowerCreditScoringModelConditionalFreeOfApplicant'] ?? '';

$borEnrolledPrincipalTribe = $QAInfo['borEnrolledPrincipalTribe'] ?? '';
$coBorEnrolledPrincipalTribe = $QAInfo['coBorEnrolledPrincipalTribe'] ?? '';
AdverseActionController::Init();

/**
 ** Description    : HMLO Client File Tab Order
 ** Authors        : Viji, Venkatesh, Suresh
 ** Developer    : Suresh
 ** Pivotal #    : 150647571
 ** Date            : Sept 14, 2017
 **/
LMRequest::setLMRId(LMRequest::$LMRId);
$glHMLOLienPosition = glHMLOLienPosition::getLienPosition(PageVariables::$PCID);
//
$automatedUnderwritingSystem = explode(',', LMRequest::File()->getTblQAInfo_by_LMRId()[0]->automatedUnderwritingSystem ?? '');
$universalLoanIdentifierFileId = $QAInfo['legalEntityIdentifier'] ?? '';
if ($universalLoanIdentifierFileId) {
    $universalLoanIdentifierFileId .= LMRequest::File()->LMRId;
}
?>

<div class="form-group row col-lg-12 m-0 mb-4 px-0" id="inforForGov">
    <label class="bg-secondary  py-2  col-lg-12"><b>Information for Government Monitoring Purposes</b></label>
</div>
<div class="form-group row">
    <div class="col-lg-12">
        <div class="col-lg-6">
            <label class="font-weight-bold checkbox checkbox-lg">
                <input type="checkbox" name="excludeFromHMDAReport" id="excludeFromHMDAReport" value="Yes"
                    <?php echo Strings::isChecked('Yes', $excludeFromHMDAReport); ?> >
                <span></span>&nbsp; Exclude from HMDA Reporting</label>
        </div>
    </div>
</div>
<div class="form-group row col-lg-12 m-0 mb-4 px-0">
    <label class="bg-secondary py-2 col-lg-12">
        <b>General Information</b>
    </label>
</div>
<div class="form-group">
    <div class="col-lg-12">
        <div class="row">
            <div class="col-lg-6 <?php echo loanForm::showField('legalEntityIdentifier'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('legalEntityIdentifier', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <div class="input-group">
                            <span class="input-group-text cursor-pointer"
                                  data-toggle="tooltip"
                                  data-trigger="hover"
                                  data-html="true"
                                  title=""
                                  data-original-title="Click to populate the data from Company Information"
                                  onclick="HMDA.getLegalEntityIdentifier('<?php echo cypher::myEncryption(PageVariables::$PCID); ?>', 'LEI')">
                                <i class="flaticon2-reload text-primary icon-lg"></i>
                            </span>
                        <input
                                type="text"
                                class="form-control input-sm"
                                name="legalEntityIdentifier"
                                autocomplete="off"
                                id="legalEntityIdentifier"
                                value="<?php echo htmlentities($legalEntityIdentifier); ?>">
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('introductoryRatePeriod'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('introductoryRatePeriod', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <input
                                type="text"
                                class="form-control input-sm"
                                name="introductoryRatePeriod"
                                autocomplete="off"
                                id="introductoryRatePeriod"
                                maxlength="3"
                                value="<?php echo $introductoryRatePeriod; ?>">
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('universalLoanIdentifierFileId'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('universalLoanIdentifierFileId', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <div class="input-group">
                        <span class="input-group-text cursor-pointer"
                              data-toggle="tooltip"
                              data-trigger="hover"
                              data-html="true"
                              title=""
                              data-original-title="Click to generate LEI + File Id"
                              onclick="HMDA.getLegalEntityIdentifier('<?php echo cypher::myEncryption(PageVariables::$PCID); ?>', 'ULI');">
                            <i class="flaticon2-reload text-primary icon-lg"></i>
                        </span>
                            <input
                                    type="text"
                                    class="form-control input-sm"
                                    name="universalLoanIdentifierFileId"
                                    autocomplete="off"
                                    id="universalLoanIdentifierFileId"
                                    value="<?php echo $universalLoanIdentifierFileId; ?>">
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('universalLoanIdentifier'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('universalLoanIdentifier', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <div class="input-group">
                            <span class="input-group-text cursor-pointer"
                                  data-toggle="tooltip"
                                  data-trigger="hover"
                                  data-html="true"
                                  title=""
                                  data-original-title="Click to generate ULI (LEI + File Id + Check Digits)"
                                  onclick="HMDA.generateLegalEntityIdentifier('<?php echo cypher::myEncryption(PageVariables::$PCID); ?>', 'ULI');">
                                <i class="flaticon2-reload text-primary icon-lg"></i>
                            </span>
                        <input
                                type="text"
                                class="form-control input-sm"
                                name="universalLoanIdentifier"
                                autocomplete="off"
                                id="universalLoanIdentifier"
                                value="<?php echo $universalLoanIdentifier; ?>">
                            <span class="input-group-text cursor-pointer"
                                  data-toggle="tooltip"
                                  data-trigger="hover"
                                  data-html="true"
                                  title=""
                                  data-original-title="Click to validate ULI (LEI + File Id + Check Digits)"
                                  onclick="HMDA.validateUniversalLoanIdentifier();">
                                <i class="fas fa-check text-primary icon-lg"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('interestOnlyPayment'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('interestOnlyPayment', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input
                                    type="text"
                                    class="form-control input-sm"
                                    name="interestOnlyPayment"
                                    id="interestOnlyPayment"
                                    value="<?php echo Currency::formatDollarAmountWithDecimalZeros($interestOnlyPayment); ?>"
                                    onblur="currencyConverter(this, this.value);"
                            >
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('HMDALoanPurpose'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('HMDALoanPurpose', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <select class="form-control input-sm" name="HMDALoanPurpose" id="HMDALoanPurpose">
                            <option value="">--Select--</option>
                            <?php foreach ($HMDALoanPurposeArray as $HMDALoanPurposeKey => $HMDALoanPurposeValue) { ?>
                                <option value="<?php echo $HMDALoanPurposeKey; ?>" <?php echo Arrays::isSelected($HMDALoanPurposeKey, $HMDALoanPurpose); ?> ><?php echo $HMDALoanPurposeValue; ?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('HMDALoanAmount'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('HMDALoanAmount', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input
                                    type="text"
                                    class="form-control input-sm"
                                    name="HMDALoanAmount"
                                    id="HMDALoanAmount"
                                    onblur="fileCommon.numberWithTwoDecimal(this)"
                                    value="<?php echo Currency::formatDollarAmountWithDecimalZeros($HMDALoanAmount); ?>"
                            >
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('actionTaken'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('actionTaken', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <select class="form-control input-sm" name="actionTaken" id="actionTaken">
                            <option value="">--Select--</option>
                            <?php
                            foreach ($HMDAActionTakenArray as $HMDAActionTakenKey => $HMDAActionTakenValue) { ?>
                                <option value="<?php echo $HMDAActionTakenKey; ?>" <?php echo Arrays::isSelected($HMDAActionTakenKey, $actionTaken); ?>><?php echo $HMDAActionTakenValue; ?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('actionDate'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('actionDate', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <div class="input-group">
                            <div class="input-group-prepend actionDate">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary icon-lg"></i>
                                    </span>
                            </div>
                            <input class="form-control input-sm dateNewClass" type="text" name="actionDate" placeholder="MM/DD/YYYY"
                                   id="actionDate" value="<?php echo AdverseActionController::$actionDate;?>" maxlength="10" autocomplete="off">
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('propertyValue'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('propertyValue', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <input
                                type="text"
                                class="form-control input-sm"
                                name="propertyValue"
                                id="propertyValue"
                                value="<?php echo $propertyValue; ?>"
                        >
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('typeOfPurchaser'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('typeOfPurchaser', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <select class="form-control input-sm" name="typeOfPurchaser" id="typeOfPurchaser">
                            <option value="-1">--Select--</option>
                            <?php
                            foreach ($typeOfPurchaserArray as $typeOfPurchaserKey => $typeOfPurchaserValue) { ?>
                                <option value="<?php echo $typeOfPurchaserKey; ?>" <?php echo Arrays::isSelected($typeOfPurchaserKey, $typeOfPurchaser); ?> ><?php echo $typeOfPurchaserValue; ?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('totalUnits'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('totalUnits', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <input
                                type="text"
                                class="form-control input-sm"
                                name="totalUnits"
                                id="totalUnits"
                                maxlength="4"
                                value="<?php echo $totalUnits; ?>">
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('reasonForDenial'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('reasonForDenial', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <select name="reasonForDenial[]" id="reasonForDenial" class="form-control chzn-select" multiple>
                            <?php foreach ($reasonForDenialArray as $reasonForDenialKey => $reasonForDenialValue) { ?>
                                <option value="<?php echo $reasonForDenialKey; ?>" <?php echo Arrays::isSelectedArray($reasonForDenial, $reasonForDenialKey); ?> ><?php echo $reasonForDenialValue; ?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 <?php if (!in_array(9, $reasonForDenial)) {
                echo 'hide';
            } ?>" id="reasonForDenialOtherDiv">
                <div class="form-group row">
                    <label class="font-weight-bold col-md-5" for="reasonForDenialOther">
                        Reason for Denial Free Form Text Field for Code 9
                    </label>
                    <div class="col-md-7">
                        <input type="text"
                               name="reasonForDenialOther"
                               id="reasonForDenialOther"
                               class="form-control input-sm"
                               maxlength="255"
                               value="<?php echo htmlentities($reasonForDenialOther); ?>">
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('submissionOfApplicationFrom'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('submissionOfApplicationFrom', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <select class="form-control input-sm" name="submissionOfApplicationFrom"
                                id="submissionOfApplicationFrom">
                            <option value="">--Select--</option>
                            <option value="Retail" <?php echo Arrays::isSelected('Retail', $submissionOfApplicationFrom); ?> >
                                Retail
                            </option>
                            <option value="Whole Sale" <?php echo Arrays::isSelected('Whole Sale', $submissionOfApplicationFrom); ?> >
                                Wholesale
                            </option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('combinedLoanToValueRatio'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('combinedLoanToValueRatio', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <input
                                type="text"
                                class="form-control input-sm"
                                name="combinedLoanToValueRatio"
                                id="combinedLoanToValueRatio"
                                value="<?php echo $combinedLoanToValueRatio; ?>">
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('censusTract'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('censusTract', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <div class="input-group">
                            <span class="input-group-text cursor-pointer"
                                  onclick="HMDA.getCensusTract('<?php echo LMRequest::myFileInfo()->getPrimaryProperty()->realestateapi_id; ?>');">
                                <i class="flaticon2-reload text-primary icon-lg"></i>
                            </span>
                        <input
                                type="text"
                                class="form-control input-sm"
                                name="censusTract"
                                id="censusTract"
                                value="<?php echo intval($censusTract); ?>">
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('receivedDate'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('receivedDate', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <div class="input-group">
                            <div class="input-group-prepend receivedDate">
                                <span class="input-group-text">
                                    <i class="fa fa-calendar text-primary icon-lg"></i>
                                </span>
                            </div>
                            <input class="form-control input-sm"
                                   type="text"
                                   placeholder="MM/DD/YYYY"
                                   id="receivedDate"
                                   value="<?php echo Dates::formatDateWithRE(LMRequest::myFileInfo()->tblFile()->receivedDate, 'YMD', 'm/d/Y');?>"
                                   maxlength="10"
                                   readonly
                                   autocomplete="off">
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('manufacturedHomeSecuredPropertyType'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('manufacturedHomeSecuredPropertyType', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <select class="form-control input-sm" name="manufacturedHomeSecuredPropertyType"
                                id="manufacturedHomeSecuredPropertyType">
                            <option value="">--Select--</option>
                            <?php foreach (HMDAActionTaken::$HMDAManufacturedHomeSecuredPropertyType
                                           as $ManufacturedHomeSecuredPropertyTypeKey
                            => $ManufacturedHomeSecuredPropertyTypeValue) { ?>
                                <option value="<?php echo $ManufacturedHomeSecuredPropertyTypeKey; ?>" <?php echo Arrays::isSelected($ManufacturedHomeSecuredPropertyTypeKey, LMRequest::File()->getTblQAInfo_by_LMRId()[0]->manufacturedHomeSecuredPropertyType); ?>>
                                    <?php echo $ManufacturedHomeSecuredPropertyTypeValue; ?>
                                </option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('loanType'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('loanType', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <select class="form-control input-sm" name="HMDALoanType"
                                id="loanType">
                            <option value="">--Select--</option>
                            <?php foreach (HMDAActionTaken::$HMDALoanType as $LoanTypeKey => $LoanTypeValue) { ?>
                                <option value="<?php echo $LoanTypeKey; ?>" <?php echo Arrays::isSelected($LoanTypeKey, LMRequest::File()->getTblQAInfo_by_LMRId()[0]->HMDALoanType); ?>>
                                    <?php echo $LoanTypeValue; ?>
                                </option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('manufacturedHomeLandPropertyInterest'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('manufacturedHomeLandPropertyInterest', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <select class="form-control input-sm"
                                name="manufacturedHomeLandPropertyInterest"
                                id="manufacturedHomeLandPropertyInterest">
                            <option value="">--Select--</option>
                            <?php foreach (HMDAActionTaken::$HMDAManufacturedHomeLandPropertyInterest
                                           as $ManufacturedHomeLandPropertyInterestKey
                            => $ManufacturedHomeLandPropertyInterestValue) { ?>
                                <option value="<?php echo $ManufacturedHomeLandPropertyInterestKey; ?>" <?php echo Arrays::isSelected($ManufacturedHomeLandPropertyInterestKey, LMRequest::File()->getTblQAInfo_by_LMRId()[0]->manufacturedHomeLandPropertyInterest); ?>>
                                    <?php echo $ManufacturedHomeLandPropertyInterestValue; ?>
                                </option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('preapproval'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('preapproval', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <select class="form-control input-sm"
                                name="preapproval"
                                id="preapproval">
                            <option value="">--Select--</option>
                            <?php foreach (HMDAActionTaken::$HMDAPreapproval as $PreapprovalKey => $PreapprovalValue) { ?>
                                <option value="<?php echo $PreapprovalKey; ?>" <?php echo Arrays::isSelected($PreapprovalKey, LMRequest::File()->getTblQAInfo_by_LMRId()[0]->preapproval); ?>>
                                    <?php echo $PreapprovalValue; ?>
                                </option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('multifamilyAffordableUnits'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('multifamilyAffordableUnits', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <input type="text"
                               class="form-control input-sm"
                               name="multifamilyAffordableUnits"
                               id="multifamilyAffordableUnits"
                               value="<?php echo LMRequest::File()->getTblQAInfo_by_LMRId()[0]->multifamilyAffordableUnits; ?>"
                        >
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('constructionMethod'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('constructionMethod', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <select class="form-control input-sm"
                                name="constructionMethod"
                                id="constructionMethod">
                            <option value="">--Select--</option>
                            <?php foreach (HMDAActionTaken::$HMDAConstructionMethod as $ConstructionMethodKey => $ConstructionMethodValue) { ?>
                                <option value="<?php echo $ConstructionMethodKey; ?>" <?php echo Arrays::isSelected($ConstructionMethodKey, LMRequest::File()->getTblQAInfo_by_LMRId()[0]->constructionMethod); ?>>
                                    <?php echo $ConstructionMethodValue; ?>
                                </option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('applicationChannel'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('applicationChannel', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <select class="form-control input-sm"
                                name="applicationChannel"
                                id="applicationChannel">
                            <option value="">--Select--</option>
                            <?php foreach (HMDAActionTaken::$HMDAApplicationChannel as $ApplicationChannelKey => $ApplicationChannelValue) { ?>
                                <option value="<?php echo $ApplicationChannelKey; ?>" <?php echo Arrays::isSelected($ApplicationChannelKey, LMRequest::File()->getTblQAInfo_by_LMRId()[0]->applicationChannel); ?>>
                                    <?php echo $ApplicationChannelValue; ?>
                                </option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('borrowerOccupancy'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('borrowerOccupancy', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <select class="form-control input-sm" disabled
                                id="borrowerOccupancy">
                            <option value="">--Select--</option>
                            <?php if (LMRequest::myFileInfo()->FilePropInfo()->isHouseProperty) { ?>
                                <option value="<?php echo LMRequest::myFileInfo()->FilePropInfo()->isHouseProperty; ?>" selected><?php echo LMRequest::myFileInfo()->FilePropInfo()->isHouseProperty; ?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('initiallyPayableToYourInstitution'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('initiallyPayableToYourInstitution', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <select class="form-control input-sm"
                                name="initiallyPayableToYourInstitution"
                                id="initiallyPayableToYourInstitution">
                            <option value="">--Select--</option>
                            <?php foreach (HMDAActionTaken::$HMDAInitiallyPayableToYourInstitution as $InitiallyPayableToYourInstitutionKey => $InitiallyPayableToYourInstitutionValue) { ?>
                                <option value="<?php echo $InitiallyPayableToYourInstitutionKey; ?>" <?php echo Arrays::isSelected($InitiallyPayableToYourInstitutionKey, LMRequest::File()->getTblQAInfo_by_LMRId()[0]->initiallyPayableToYourInstitution); ?>>
                                    <?php echo $InitiallyPayableToYourInstitutionValue; ?>
                                </option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('rateSpread'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('rateSpread', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <input type="text"
                               class="form-control input-sm"
                               name="rateSpread"
                               id="rateSpread"
                               value="<?php echo LMRequest::File()->getTblQAInfo_by_LMRId()[0]->rateSpread; ?>"
                        >
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('mortgageLoanOriginatorNMLSIdentifier'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('mortgageLoanOriginatorNMLSIdentifier', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <input type="text"
                               class="form-control input-sm"
                               readonly
                               id="mortgageLoanOriginatorNMLSIdentifier"
                               value="<?php echo LMRequest::myFileInfo()->SecondaryBrokerInfo()->personalNMLSLicense;?>"
                        >
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('HOEPAStatus'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('HOEPAStatus', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <select class="form-control input-sm"
                                name="HOEPAStatus"
                                id="HOEPAStatus">
                            <option value="">--Select--</option>
                            <?php foreach (HMDAActionTaken::$HMDAHoepaStatus as $HOEPAStatusKey => $HOEPAStatusValue) { ?>
                                <option value="<?php echo $HOEPAStatusKey; ?>" <?php echo Arrays::isSelected($HOEPAStatusKey, LMRequest::File()->getTblQAInfo_by_LMRId()[0]->HOEPAStatus); ?>>
                                    <?php echo $HOEPAStatusValue; ?>
                                </option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('lienStatus'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('lienStatus', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <select class="form-control input-sm" disabled
                                id="lienStatus">
                            <option value="">--Select--</option>
                            <?php if (LMRequest::myFileInfo()->fileHMLOPropertyInfo()->lienPosition) { ?>
                                <option value="<?php echo LMRequest::myFileInfo()->fileHMLOPropertyInfo()->lienPosition; ?>" selected>
                                    <?php echo $glHMLOLienPosition[LMRequest::myFileInfo()->fileHMLOPropertyInfo()->lienPosition]; ?>
                                </option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('totalLoanCostOrTotalPointsAndFees'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('totalLoanCostOrTotalPointsAndFees', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm"
                                   name="totalLoanCostOrTotalPointsAndFees"
                                   id="totalLoanCostOrTotalPointsAndFees"
                                   onblur="currencyConverter(this, this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimalZeros(LMRequest::File()->getTblQAInfo_by_LMRId()[0]->totalLoanCostOrTotalPointsAndFees); ?>"
                            >
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('automatedUnderwritingSystem'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('automatedUnderwritingSystem', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <select name="automatedUnderwritingSystem[]"
                                id="automatedUnderwritingSystem"
                                class="form-control chzn-select"
                                multiple>
                            <option value="">--Select--</option>
                            <?php foreach (HMDAActionTaken::$HMDAAutomatedUnderwritingSystem as $automatedUnderwritingSystemKey => $automatedUnderwritingSystemValue) { ?>
                                <option value="<?php echo $automatedUnderwritingSystemKey; ?>" <?php echo Arrays::isSelectedArray($automatedUnderwritingSystem, $automatedUnderwritingSystemKey); ?> >
                                    <?php echo $automatedUnderwritingSystemValue; ?>
                                </option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('originationCharges'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('originationCharges', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm"
                                   name="originationCharges"
                                   id="originationCharges"
                                   onblur="currencyConverter(this, this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimalZeros(LMRequest::File()->getTblQAInfo_by_LMRId()[0]->originationCharges); ?>"
                            >
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('AUSFreeFormTextFieldForCode5'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('AUSFreeFormTextFieldForCode5', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <input type="text"
                               class="form-control input-sm"
                               name="AUSFreeFormTextFieldForCode5"
                               id="AUSFreeFormTextFieldForCode5"
                               value="<?php echo LMRequest::File()->getTblQAInfo_by_LMRId()[0]->AUSFreeFormTextFieldForCode5; ?>"
                        >
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('discountPoints'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('discountPoints', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm"
                                   name="discountPoints"
                                   id="discountPoints"
                                   onblur="currencyConverter(this, this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimalZeros(LMRequest::File()->getTblQAInfo_by_LMRId()[0]->discountPoints); ?>"
                            >
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('automatedUnderwritingSystemResult'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('automatedUnderwritingSystemResult', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <select class="form-control input-sm"
                                name="automatedUnderwritingSystemResult"
                                id="automatedUnderwritingSystemResult">
                            <option value="">--Select--</option>
                            <?php foreach (HMDAActionTaken::$HMDAAutomatedUnderwritingSystemResult as $AutomatedUnderwritingSystemResultKey => $AutomatedUnderwritingSystemResultValue) { ?>
                                <option value="<?php echo $AutomatedUnderwritingSystemResultKey; ?>"
                                    <?php echo Arrays::isSelected($AutomatedUnderwritingSystemResultKey, LMRequest::File()->getTblQAInfo_by_LMRId()[0]->automatedUnderwritingSystemResult); ?>>
                                    <?php echo $AutomatedUnderwritingSystemResultValue; ?>
                                </option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('lenderCredits'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('lenderCredits', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text"
                                   class="form-control input-sm"
                                   name="lenderCredits"
                                   id="lenderCredits"
                                   onblur="currencyConverter(this, this.value);"
                                   value="<?php echo Currency::formatDollarAmountWithDecimalZeros(LMRequest::File()->getTblQAInfo_by_LMRId()[0]->lenderCredits); ?>"
                            >
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('AUSResultFreeFormTextFieldForCode16'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('AUSResultFreeFormTextFieldForCode16', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <input type="text"
                               class="form-control input-sm"
                               name="AUSResultFreeFormTextFieldForCode16"
                               id="AUSResultFreeFormTextFieldForCode16"
                               value="<?php echo LMRequest::File()->getTblQAInfo_by_LMRId()[0]->AUSResultFreeFormTextFieldForCode16; ?>"
                        >
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('lien1Rate'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('lien1Rate', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <input type="text"
                               class="form-control input-sm"
                               readonly
                               id="interestRate"
                               value="<?php echo LMRequest::myFileInfo()->tblFile()->lien1Rate; ?>">
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('reverseMortgage'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('reverseMortgage', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <select class="form-control input-sm"
                                name="reverseMortgage"
                                id="reverseMortgage">
                            <option value="">--Select--</option>
                            <?php foreach (HMDAActionTaken::$HMDAReverseMortgage as $ReverseMortgageKey => $ReverseMortgageValue) { ?>
                                <option value="<?php echo $ReverseMortgageKey; ?>"
                                    <?php echo Arrays::isSelected($ReverseMortgageKey, LMRequest::File()->getTblQAInfo_by_LMRId()[0]->reverseMortgage); ?>>
                                    <?php echo $ReverseMortgageValue; ?>
                                </option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('HMDAPrepaymentPenalty'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('HMDAPrepaymentPenalty', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <input type="text"
                               class="form-control input-sm"
                               name="HMDAPrepaymentPenalty"
                               id="HMDAPrepaymentPenalty"
                               value="<?php echo LMRequest::File()->getTblQAInfo_by_LMRId()[0]->HMDAPrepaymentPenalty; ?>"
                        >
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('openEndLineOfCredit'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('openEndLineOfCredit', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <select class="form-control input-sm"
                                name="openEndLineOfCredit"
                                id="openEndLineOfCredit">
                            <option value="">--Select--</option>
                            <?php foreach (HMDAActionTaken::$HMDAOpenEndLineOfCredit as $OpenEndLineOfCreditKey => $OpenEndLineOfCreditValue) { ?>
                                <option value="<?php echo $OpenEndLineOfCreditKey; ?>"
                                    <?php echo Arrays::isSelected($OpenEndLineOfCreditKey, LMRequest::File()->getTblQAInfo_by_LMRId()[0]->openEndLineOfCredit); ?>>
                                    <?php echo $OpenEndLineOfCreditValue; ?>
                                </option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('debtToIncomeRatio'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('debtToIncomeRatio', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <input type="text"
                               class="form-control input-sm"
                               name="debtToIncomeRatio"
                               id="debtToIncomeRatio"
                               value="<?php echo LMRequest::File()->getTblQAInfo_by_LMRId()[0]->debtToIncomeRatio; ?>"
                        >
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('businessOrCommercialPurpose'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('businessOrCommercialPurpose', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <select class="form-control input-sm"
                                name="businessOrCommercialPurpose"
                                id="businessOrCommercialPurpose">
                            <option value="">--Select--</option>
                            <?php foreach (HMDAActionTaken::$HMDABusinessOrCommercialPurpose as $BusinessOrCommercialPurposeKey => $BusinessOrCommercialPurposeValue) { ?>
                                <option value="<?php echo $BusinessOrCommercialPurposeKey; ?>"
                                    <?php echo Arrays::isSelected($BusinessOrCommercialPurposeKey, LMRequest::File()->getTblQAInfo_by_LMRId()[0]->businessOrCommercialPurpose); ?>>
                                    <?php echo $BusinessOrCommercialPurposeValue; ?>
                                </option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('loanTerm'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('loanTerm', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <select class="form-control input-sm"
                                disabled
                                id="loanTerm">
                            <option value="">--Select--</option>
                            <?php if(LMRequest::myFileInfo()->fileHMLOPropertyInfo()->loanTerm) { ?>
                                <option value="<?php echo LMRequest::myFileInfo()->fileHMLOPropertyInfo()->loanTerm; ?>" selected>
                                    <?php echo LMRequest::myFileInfo()->fileHMLOPropertyInfo()->loanTerm; ?>
                                </option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('balloonPayment'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('balloonPayment', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <select class="form-control input-sm"
                                name="balloonPayment"
                                id="balloonPayment">
                            <option value="">--Select--</option>
                            <?php foreach (HMDAActionTaken::$HMDABalloonPayment as $BalloonPaymentKey => $BalloonPaymentValue) { ?>
                                <option value="<?php echo $BalloonPaymentKey; ?>" <?php echo Arrays::isSelected($BalloonPaymentKey, LMRequest::File()->getTblQAInfo_by_LMRId()[0]->balloonPayment); ?>>
                                    <?php echo $BalloonPaymentValue; ?>
                                </option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('areThereInterestOnlyPayment'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('areThereInterestOnlyPayment', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <select class="form-control input-sm"
                                name="areThereInterestOnlyPayment"
                                id="areThereInterestOnlyPayment">
                            <option value="">--Select--</option>
                            <?php foreach (HMDAActionTaken::$HMDAInterestOnlyPayments as $InterestOnlyPaymentsKey => $InterestOnlyPaymentsValue) { ?>
                                <option value="<?php echo $InterestOnlyPaymentsKey; ?>"
                                    <?php echo Arrays::isSelected($InterestOnlyPaymentsKey, LMRequest::File()->getTblQAInfo_by_LMRId()[0]->areThereInterestOnlyPayment); ?>>
                                    <?php echo $InterestOnlyPaymentsValue; ?>
                                </option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('negativeAmortization'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('negativeAmortization', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <select class="form-control input-sm"
                                name="negativeAmortization"
                                id="negativeAmortization">
                            <option value="">--Select--</option>
                            <?php foreach (HMDAActionTaken::$HMDANegativeAmortization as $NegativeAmortizationKey => $NegativeAmortizationValue) { ?>
                                <option value="<?php echo $NegativeAmortizationKey; ?>"
                                    <?php echo Arrays::isSelected($NegativeAmortizationKey, LMRequest::File()->getTblQAInfo_by_LMRId()[0]->negativeAmortization); ?>>
                                    <?php echo $NegativeAmortizationValue; ?>
                                </option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 <?php echo loanForm::showField('otherNonAmortizingFeatures'); ?>">
                <div class="form-group row">
                    <?php echo loanForm::label('otherNonAmortizingFeatures', 'col-md-5'); ?>
                    <div class="col-md-7">
                        <select class="form-control input-sm"
                                name="otherNonAmortizingFeatures"
                                id="otherNonAmortizingFeatures">
                            <option value="">--Select--</option>
                            <?php foreach (HMDAActionTaken::$HMDAOtherNonAmortizingFeatures as $OtherNonAmortizingFeaturesKey => $OtherNonAmortizingFeaturesValue) { ?>
                                <option value="<?php echo $OtherNonAmortizingFeaturesKey; ?>"
                                    <?php echo Arrays::isSelected($OtherNonAmortizingFeaturesKey, LMRequest::File()->getTblQAInfo_by_LMRId()[0]->otherNonAmortizingFeatures); ?>>
                                    <?php echo $OtherNonAmortizingFeaturesValue; ?>
                                </option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php
//Hide for CV3
if (glCustomJobForProcessingCompany::hideShowHMDAFields(PageVariables::$PCID)) { ?>
<div class="row mb-10">
    <div class="col-md-12 mb-2">
        <div class="card card-custom">
            <div class="card-header card-header-tabs-line bg-gray-100">
                <div class="card-title">
                    <h3 class="card-label">
                        Property Location
                    </h3>
                </div>
                <div class="card-toolbar">
                    <a href="javascript:void(0);"
                       class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                       data-card-tool="toggle"
                       data-section="HMDA_PropertyInfoCard"
                       data-toggle="tooltip"
                       data-placement="top"
                       title=""
                       data-original-title="Toggle Card">
                        <i class="ki ki-arrow-down icon-nm"></i>
                    </a>
                </div>
            </div>
            <div class="card-body HMDA_PropertyInfoCard_body">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <label class="font-weight-bold col-md-5" for="">Subject Address</label>
                                    <div class="col-md-7">
                                        <input
                                            type="text"
                                            class="form-control input-sm"
                                            id=""
                                            readonly
                                            value="<?php echo LMRequest::myFileInfo()->getPrimaryProperty()->propertyAddress;?>">
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <label class="font-weight-bold col-md-5" for="">Subject Unit#</label>
                                    <div class="col-md-7">
                                        <input
                                                type="text"
                                                class="form-control input-sm"
                                                id=""
                                                readonly
                                                value="<?php echo LMRequest::myFileInfo()->getPrimaryProperty()->propertyUnit;?>">
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <label class="font-weight-bold col-md-5" for="">Subject City</label>
                                    <div class="col-md-7">
                                        <input
                                                type="text"
                                                class="form-control input-sm"
                                                id=""
                                                readonly
                                                value="<?php echo LMRequest::myFileInfo()->getPrimaryProperty()->propertyCity;?>">
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <label class="font-weight-bold col-md-5" for="">Subject State</label>
                                    <div class="col-md-7">
                                        <select class="form-control input-sm" disabled
                                        id="">
                                            <option value="">-Select-</option>
                                            <?php if(Strings::convertState(LMRequest::myFileInfo()->getPrimaryProperty()->propertyState)) { ?>
                                                <option value="<?php echo Strings::convertState(LMRequest::myFileInfo()->getPrimaryProperty()->propertyState); ?>"
                                                        selected><?php echo Strings::convertState(LMRequest::myFileInfo()->getPrimaryProperty()->propertyState); ?>
                                                </option>
                                            <?php } ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <label class="font-weight-bold col-md-5" for="">Subject Zip Code</label>
                                    <div class="col-md-7">
                                        <input type="text"
                                        class="form-control input-sm"
                                        id=""
                                        readonly
                                        value="<?php echo LMRequest::myFileInfo()->getPrimaryProperty()->propertyZipCode; ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <label class="font-weight-bold col-md-5" for="">County</label>
                                    <div class="col-md-7">
                                        <select class="form-control input-sm" disabled
                                                name=""
                                                id="">
                                            <option value="">-Select-</option>
                                            <?php if(LMRequest::myFileInfo()->getPrimaryProperty()->propertyCounty) { ?>
                                                <option value="<?php echo LMRequest::myFileInfo()->getPrimaryProperty()->propertyCounty; ?>" selected>
                                                    <?php echo LMRequest::myFileInfo()->getPrimaryProperty()->propertyCounty; ?>
                                                </option>
                                            <?php } ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group row">
                                    <label class="font-weight-bold col-md-5" for="">County Code</label>
                                    <div class="col-md-7">
                                        <div class="input-group">
                                            <span class="input-group-text cursor-pointer" onclick="HMDA.getCountyCode('<?php echo LMRequest::myFileInfo()->getPrimaryProperty()->realestateapi_id; ?>')">
                                                <i class="flaticon2-reload text-primary icon-lg"></i>
                                            </span>
                                            <input type="text"
                                                class="form-control input-sm"
                                                name="countyCode"
                                                id="countyCode"
                                                value="<?php echo LMRequest::File()->getTblQAInfo_by_LMRId()[0]->countyCode;?>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php } ?>

<?php
loanForm::pushSectionID('GOVT');
?>

<div class="row mb-10">
    <div class="col-md-6 mb-2">
        <div class="card card-custom BorrowerGOVTCard">
            <div class="card-header card-header-tabs-line bg-gray-100  ">
                <div class="card-title">
                    <h3 class="card-label">
                        Borrower
                    </h3>
                </div>
                <div class="card-toolbar">
                    <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none" data-card-tool="toggle"
                       data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                        <i class="ki ki-arrow-down icon-nm"></i>
                    </a>
                    <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none" data-card-tool="reload"
                       data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                        <i class="ki ki-reload icon-nm"></i>
                    </a>
                    <a href="javascript:void(0);"
                       class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                       data-card-tool="toggle"
                       data-section="BorrowerGOVTCard"
                       data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                        <i class="ki ki-arrow-down icon-nm"></i>
                    </a>
                </div>
            </div>
            <div class="card-body BorrowerGOVTCard_body">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="form-group row">
                            <label class="font-weight-bold col-md-5" for="borrowerCreditScoreOfApplicant">
                                Do you wish to furnish this information?
                            </label>
                            <div class="col-md-7">
                                <?php
                                if ($allowToEdit) {
                                    ?>
                                    <div class="radio-inline">
                                        <label class="radio radio-solid font-weight-bold" for="PublishBInfoYes">
                                            <input type="radio" name="PublishBInfo" value="2" class="BrYes"
                                                   id="PublishBInfoYes"
                                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('2', $PublishBInfo); ?>
                                                   onclick="showAndHideQADiv16(this.value, 'borrowerSelector');"><span></span>
                                            Yes
                                        </label>
                                        <label class="radio radio-solid font-weight-bold" for="PublishBInfoNo">
                                            <input type="radio" name="PublishBInfo" value="1" id="PublishBInfoNo"
                                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('1', $PublishBInfo); ?>
                                                   onclick="showAndHideQADiv16(this.value, 'borrowerSelector');"><span></span>
                                            No
                                        </label>
                                        <label class="radio radio-solid font-weight-bold" for="PublishBInfo3">
                                            <input type="radio" name="PublishBInfo" value="3" id="PublishBInfo3"
                                                   tabindex="<?php echo $tabIndexNo++; ?>"
                                                   <?php echo Strings::isChecked('3', $PublishBInfo); ?>onclick="showAndHideQADiv16(this.value, 'borrowerSelector');"><span></span>
                                            N/A
                                        </label></div>
                                    <?php
                                } else {
                                    if ($PublishBInfo == 2) {
                                        echo '<h5>Yes</h5>';
                                    } elseif ($PublishBInfo == 1) {
                                        echo '<h5>No</h5>';
                                    } else {
                                        echo '<h5>N/A</h5>';
                                    }
                                }
                                ?>
                            </div>
                        </div>
                        <div class="form-group row <?php echo loanForm::showField('incomeBorrowerCoBorrower'); ?> ">
                            <?php echo loanForm::label('incomeBorrowerCoBorrower', 'col-md-5'); ?>
                            <div class="col-md-7">
                                <?php echo loanForm::currency(
                                    'incomeBorrowerCoBorrower',
                                    LMRequest::$allowToEdit,
                                    $tabIndexNo++,
                                    LMRequest::File()->getTblQAInfo_by_LMRId()[0]->incomeBorrowerCoBorrower,
                                    'input-sm',
                                    'currencyConverter(this, this.value);'
                                ); ?>
                            </div>
                        </div>
                        <?php if (glCustomJobForProcessingCompany::hideShowHMDAFields(PageVariables::$PCID)) { ?>
                        <?php if (0) { //Hide for now ?>
                            <div class="form-group row">
                                <label class="font-weight-bold col-md-5" for="borrowerRaceOfApplicant">
                                    Race of Applicant or Borrower - American Indian or Alaska Native Enrolled or Principal
                                    Tribe
                                </label>
                                <div class="col-md-7">
                                    <input
                                            type="text"
                                            class="form-control input-sm"
                                            name="borrowerRaceOfApplicant"
                                            id="borrowerRaceOfApplicant"
                                            value="<?php echo htmlentities($borrowerRaceOfApplicant); ?>">
                                </div>
                            </div>
                        <?php } ?>
                        <div class="form-group row">
                            <label class="font-weight-bold col-md-5" for="borrowerAgeOfApplicant">
                                Age of Applicant or Borrower
                            </label>
                            <div class="col-md-7">
                                <input
                                        type="text"
                                        class="form-control input-sm"
                                        name="borrowerAgeOfApplicant"
                                        id="borrowerAgeOfApplicant"
                                        value="<?php echo $borrowerAgeOfApplicant; ?>">
                            </div>
                        </div>
                        <?php } ?>
                        <div class="form-group row">
                            <label class="font-weight-bold col-md-5" for="borrowerCreditScoreOfApplicant">
                                Credit Score of Applicant or Borrower
                            </label>
                            <div class="col-md-7">
                                <input
                                        type="text"
                                        class="form-control input-sm"
                                        name="borrowerCreditScoreOfApplicant"
                                        id="borrowerCreditScoreOfApplicant"
                                        value="<?php echo $borrowerCreditScoreOfApplicant; ?>">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="font-weight-bold col-md-5" for="borrowerCreditScoringModelOfApplicant">
                                Applicant or Borrower, Name and Version of Credit Scoring Model
                            </label>
                            <div class="col-md-7">
                                <select class="form-control input-sm creditScoringModel"
                                        name="borrowerCreditScoringModelOfApplicant"
                                        id="borrowerCreditScoringModelOfApplicant">
                                    <option value="">-Select-</option>
                                    <?php foreach ($creditScoringModelArray as $boCreditScoringModelKey => $boCreditScoringModelValue) { ?>
                                        <option value="<?php echo $boCreditScoringModelKey; ?>" <?php echo Arrays::isSelected($boCreditScoringModelKey, $borrowerCreditScoringModelOfApplicant); ?> ><?php echo $boCreditScoringModelValue; ?></option>
                                    <?php } ?>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row <?php if ($borrowerCreditScoringModelOfApplicant != 8) {
                            echo 'hide';
                        } ?> " id="borrowerCreditScoringModelOfApplicantOtherDiv">
                            <label class="font-weight-bold col-md-5" for="borrowerCreditScoringModelOfApplicantOther">
                                Other
                            </label>
                            <div class="col-md-7">
                                <input type="text" class="form-control input-sm"
                                       name="borrowerCreditScoringModelOfApplicantOther"
                                       id="borrowerCreditScoringModelOfApplicantOther"
                                       value="<?php echo $borrowerCreditScoringModelOfApplicantOther; ?>"
                                >
                            </div>
                        </div>
                        <?php if (glCustomJobForProcessingCompany::hideShowHMDAFields(PageVariables::$PCID)) { ?>
                        <div class="form-group row">
                            <label class="font-weight-bold col-md-5"
                                   for="borrowerCreditScoringModelConditionalFreeOfApplicant">
                                Applicant or Borrower, Name and Version of Credit Scoring Model: Conditional Free Form
                                Text Field For Code 8
                            </label>
                            <div class="col-md-7">
                                <input type="text"
                                       class="form-control input-sm"
                                       name="borrowerCreditScoringModelConditionalFreeOfApplicant"
                                       id="borrowerCreditScoringModelConditionalFreeOfApplicant"
                                       value="<?php echo htmlentities($borrowerCreditScoringModelConditionalFreeOfApplicant); ?>">
                            </div>
                        </div>
                        <?php } ?>
                    </div>
                </div>


                <div class="row">
                    <table class="table table-hover table-condensed table-sm table-vertical-center">
                        <tr>
                            <td class="font-weight-bold">What is your Ethnicity:</td>
                            <td>
                                <?php
                                if ($allowToEdit) {
                                    ?>
                                    <div class="checkbox-list">
                                        <label class="checkbox checkbox-solid font-weight-bold" for="BEthnicityHis">
                                            <input type="checkbox" name="BEthnicity2" id="BEthnicityHis"
                                                   value="2" <?php echo Arrays::inArrayCheck('2', $BEthnicity); ?>
                                                   tabindex="<?php echo $tabIndexNo++; ?>"
                                                   class="BorChildRadio borrowerSelector Hispanic"><span></span>
                                            <?php echo EthnicityRaceGenderVeteran::$Ethnicity[2]; ?>
                                        </label>
                                        <div class="col-md-12 px-4" id="Hispanic">
                                            <div class="checkbox-list">
                                                <label class="checkbox checkbox-solid font-weight-bold"
                                                       for="bFiEthnicitySubMex">
                                                    <input type="checkbox" name="bFiEthnicitySub1" id="bFiEthnicitySubMex"
                                                           class="BorChildRadio borrowerSelector HispanicPrintOrigin"
                                                           value="1" <?php echo Arrays::inArrayCheck('1', $bFiEthnicitySub); ?> >
                                                    <span></span>
                                                    <?php echo EthnicityRaceGenderVeteran::$EthnicitySub[1]; ?>

                                                </label>
                                                <label class="checkbox checkbox-solid font-weight-bold"
                                                       for="bFiEthnicitySubPu">
                                                    <input type="checkbox" name="bFiEthnicitySub2" id="bFiEthnicitySubPu"
                                                           class="BorChildRadio borrowerSelector HispanicPrintOrigin"
                                                           value="2" <?php echo Arrays::inArrayCheck('2', $bFiEthnicitySub); ?> ><span></span>
                                                    <?php echo EthnicityRaceGenderVeteran::$EthnicitySub[2]; ?>
                                                </label>
                                                <label class="checkbox checkbox-solid font-weight-bold"
                                                       for="bFiEthnicityCuban">
                                                    <input type="checkbox" name="bFiEthnicitySub3" id="bFiEthnicityCuban"
                                                           class="BorChildRadio borrowerSelector HispanicPrintOrigin"
                                                           value="3" <?php echo Arrays::inArrayCheck('3', $bFiEthnicitySub); ?> ><span></span>
                                                    <?php echo EthnicityRaceGenderVeteran::$EthnicitySub[3]; ?>
                                                </label>
                                                <label class="checkbox radio-solid font-weight-bold"
                                                       for="bFiEthnicitySubOther">
                                                    <input type="checkbox" name="bFiEthnicitySub4" id="bFiEthnicitySubOther"
                                                           class="BorChildRadio borrowerSelector HispanicPrintOrigin"
                                                           value="4" <?php echo Arrays::inArrayCheck('4', $bFiEthnicitySub); ?> ><span></span>
                                                    <?php echo EthnicityRaceGenderVeteran::$EthnicitySub[4]; ?>
                                                </label>
                                                <div class="col-md-12 px-4"
                                                     id="HispanicPrintOriginDiv">

                                                    <label class="col-md-6 font-weight-bold"
                                                           for="bFiEthnicitySubOtherTxt">Print Origin</label>
                                                    <span class="col-md-6"><input type="text"
                                                                                  autocomplete="off"
                                                                                  name="bFiEthnicitySubOther"
                                                                                  id="bFiEthnicitySubOtherTxt"
                                                                                  class="form-control input-sm"
                                                                                  value="<?php echo htmlspecialchars($bFiEthnicitySubOther); ?>">
                                                        <span class="text-muted">For example: Argentinean, Colombian, Dominican,
                                                            Nicaraguan,Salvadoran,
                                                            Spaniard, and so on</span>
                                                   </span>
                                                </div>
                                            </div>
                                        </div>

                                        <label class="checkbox checkbox-solid font-weight-bold" for="BEthnicityNotHis">
                                            <input type="checkbox" name="BEthnicity1" id="BEthnicityNotHis"
                                                   value="1" <?php echo Arrays::inArrayCheck('1', $BEthnicity); ?>
                                                   tabindex="<?php echo $tabIndexNo++; ?>"
                                                   class="BorChildRadio borrowerSelector Hispanic"><span></span>
                                            <?php echo EthnicityRaceGenderVeteran::$Ethnicity[1]; ?>
                                        </label>
                                        <label class="checkbox checkbox-solid font-weight-bold" for="BEthnicityNotDis">
                                            <input type="checkbox" name="BEthnicity3" id="BEthnicityNotDis"
                                                   value="3" <?php echo Arrays::inArrayCheck('3', $BEthnicity); ?>
                                                   tabindex="<?php echo $tabIndexNo++; ?>"
                                                   class="BorChildRadio borrowerSelector Hispanic"><span></span>
                                            <?php echo EthnicityRaceGenderVeteran::$Ethnicity[3]; ?>
                                        </label>
                                    </div>
                                    <?php
                                } else {
                                    ?>
                                    <input type="checkbox" name="BEthnicity2" value="2"
                                           disabled <?php echo Arrays::inArrayCheck('2', $BEthnicity); ?>
                                           tabindex="<?php echo $tabIndexNo++; ?>">Hispanic or Latino
                                    <div class="col-md-12" id="Hispanic">
                                        <input type="checkbox" name="bFiEthnicitySub1" id="bFiEthnicitySub1" class=""
                                               disabled
                                               value="1" <?php echo Arrays::inArrayCheck('1', $bFiEthnicitySub); ?> >
                                        <?php echo EthnicityRaceGenderVeteran::$EthnicitySub[1]; ?>
                                        <br>
                                        <input type="checkbox" name="bFiEthnicitySub2" id="bFiEthnicitySub2" class=""
                                               disabled
                                               value="2" <?php echo Arrays::inArrayCheck('2', $bFiEthnicitySub); ?> >
                                        <?php echo EthnicityRaceGenderVeteran::$EthnicitySub[2]; ?>
                                        <br>
                                        <input type="checkbox" name="bFiEthnicitySub3" id="bFiEthnicitySub3" class=""
                                               disabled
                                               value="3" <?php echo Arrays::inArrayCheck('3', $bFiEthnicitySub); ?> >
                                        <?php echo EthnicityRaceGenderVeteran::$EthnicitySub[3]; ?>
                                        <br>
                                        <input type="checkbox" name="bFiEthnicitySub4" id="bFiEthnicitySub4" class=""
                                               disabled
                                               value="4" <?php echo Arrays::inArrayCheck('4', $bFiEthnicitySub); ?> >
                                        <?php echo EthnicityRaceGenderVeteran::$EthnicitySub[4]; ?>
                                    </div>
                                    <br>
                                    <input type="checkbox" name="BEthnicity1" value="1"
                                           disabled <?php echo Arrays::inArrayCheck('1', $BEthnicity); ?>
                                           tabindex="<?php echo $tabIndexNo++; ?>">
                                    <?php echo EthnicityRaceGenderVeteran::$Ethnicity[1]; ?>
                                    <br>
                                    <input type="checkbox" name="BEthnicity" value="3"
                                           disabled <?php echo Arrays::inArrayCheck('3', $BEthnicity); ?>
                                           tabindex="<?php echo $tabIndexNo++; ?>">
                                    <?php echo EthnicityRaceGenderVeteran::$Ethnicity[2]; ?>
                                    <?php
                                }
                                ?>
                            </td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold"> What is your Race:</td>
                            <td>
                                <?php
                                if ($allowToEdit) {
                                    ?>
                                    <div class="checkbox-list">
                                        <label class="checkbox checkbox-solid font-weight-bold" for="BRaceAme">
                                            <input type="radio" name="BRace1"
                                                   value="1" <?php echo Arrays::inArrayCheck('1', $BRace); ?>
                                                   tabindex="<?php echo $tabIndexNo++; ?>" id="BRaceAme"
                                                   class="BorChildRadio borrowerSelector Race"><span></span>
                                            <?php echo EthnicityRaceGenderVeteran::$Race[1]; ?>
                                        </label>
                                        <div class="col-md-12 no-padding" id="borEnrolledPrincipalTribeDiv">
                                            <div class="col-md-12">
                                                <label for="borEnrolledPrincipalTribe">
                                                    Enrolled or Principal Tribe
                                                </label>
                                                <input type="text"
                                                       class="form-control input-sm"
                                                       name="borEnrolledPrincipalTribe"
                                                       id="borEnrolledPrincipalTribe"
                                                       value="<?php echo htmlentities($borEnrolledPrincipalTribe); ?>">
                                            </div>
                                        </div>
                                        <label class="checkbox checkbox-solid font-weight-bold" for="BRaceAme2">
                                            <input type="checkbox" name="BRace2"
                                                   value="2" <?php echo Arrays::inArrayCheck('2', $BRace); ?>
                                                   tabindex="<?php echo $tabIndexNo++; ?>" id="BRaceAme2"
                                                   class="BorChildRadio borrowerSelector Race"><span></span>
                                            <?php echo EthnicityRaceGenderVeteran::$Race[2]; ?>
                                        </label>
                                        <div class="col-md-12 px-4" id="Asian">
                                            <div class="checkbox-list">
                                                <label class="checkbox checkbox-solid font-weight-bold" for="bFiRaceSubBor">
                                                    <input type="checkbox" name="bFiRaceSub1" id="bFiRaceSubBor"
                                                           class="BorChildRadio borrowerSelector Asian"
                                                           value="1" <?php echo Arrays::inArrayCheck('1', $bFiRaceSub); ?> ><span></span>
                                                    <?php echo EthnicityRaceGenderVeteran::$RaceSub[1]; ?>
                                                </label>
                                                <label class="checkbox checkbox-solid font-weight-bold" for="bFiRaceSubChin">
                                                    <input type="checkbox" name="bFiRaceSub2" id="bFiRaceSubChin"
                                                           class="BorChildRadio borrowerSelector Asian"
                                                           value="2" <?php echo Arrays::inArrayCheck('2', $bFiRaceSub); ?> ><span></span>
                                                    <?php echo EthnicityRaceGenderVeteran::$RaceSub[2]; ?>
                                                </label>
                                                <label class="checkbox checkbox-solid font-weight-bold" for="bFiRaceSub3">
                                                    <input type="checkbox" name="bFiRaceSub3" id="bFiRaceSub3"
                                                           class="BorChildRadio borrowerSelector Asian"
                                                           value="3" <?php echo Arrays::inArrayCheck('3', $bFiRaceSub); ?> ><span></span>
                                                    <?php echo EthnicityRaceGenderVeteran::$RaceSub[3]; ?>
                                                </label>
                                                <label class="checkbox checkbox-solid font-weight-bold" for="bFiRaceSub4">
                                                    <input type="checkbox" name="bFiRaceSub4" id="bFiRaceSub4"
                                                           class="BorChildRadio borrowerSelector Asian"
                                                           value="4" <?php echo Arrays::inArrayCheck('4', $bFiRaceSub); ?> ><span></span>
                                                    <?php echo EthnicityRaceGenderVeteran::$RaceSub[4]; ?>
                                                </label>
                                                <label class="checkbox checkbox-solid font-weight-bold" for="bFiRaceSub5">
                                                    <input type="checkbox" name="bFiRaceSub5" id="bFiRaceSub5"
                                                           class="BorChildRadio borrowerSelector Asian"
                                                           value="5" <?php echo Arrays::inArrayCheck('5', $bFiRaceSub); ?> ><span></span>
                                                    <?php echo EthnicityRaceGenderVeteran::$RaceSub[5]; ?>
                                                </label>
                                                <label class="checkbox checkbox-solid font-weight-bold" for="bFiRaceSub6">
                                                    <input type="checkbox" name="bFiRaceSub6" id="bFiRaceSub6"
                                                           class="BorChildRadio borrowerSelector Asian"
                                                           value="6" <?php echo Arrays::inArrayCheck('6', $bFiRaceSub); ?> ><span></span>
                                                    <?php echo EthnicityRaceGenderVeteran::$RaceSub[6]; ?>
                                                </label>
                                                <label class="checkbox checkbox-solid font-weight-bold" for="bFiRaceSub7">
                                                    <input type="checkbox" name="bFiRaceSub7" id="bFiRaceSub7"
                                                           class="BorChildRadio borrowerSelector Asian"
                                                           value="7" <?php echo Arrays::inArrayCheck('7', $bFiRaceSub); ?> ><span></span>
                                                    <?php echo EthnicityRaceGenderVeteran::$RaceSub[7]; ?>
                                                </label>
                                                <div class="col-md-12 no-padding" id="AsianDiv">
                                                    <label class="col-md-6 font-weight-bold">
                                                        Print Race</label>
                                                    <span class="col-md-6">   <input type="text"
                                                                                     autocomplete="off"
                                                                                     class="form-control input-sm"
                                                                                     name="bFiRaceAsianOther"
                                                                                     id="bFiRaceAsianOther"
                                                                                     value="<?php echo htmlspecialchars($bFiRaceAsianOther); ?>">
                                                        <span class="text-muted">For example: Hmong, Laotian, Thai, Pakistani, Cambodian, and
                                                            so
                                                            on.</span>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <label class="checkbox checkbox-solid font-weight-bold" for="BRaceAmeBlack">
                                            <input type="checkbox" name="BRace3"
                                                   value="3" <?php echo Arrays::inArrayCheck('3', $BRace); ?>
                                                   tabindex="<?php echo $tabIndexNo++; ?>" id="BRaceAmeBlack"
                                                   class="BorChildRadio borrowerSelector Race"><span></span>
                                            <?php echo EthnicityRaceGenderVeteran::$Race[3]; ?>
                                        </label>
                                        <label class="checkbox checkbox-solid font-weight-bold" for="BRaceAmeNat">
                                            <input type="checkbox" name="BRace4"
                                                   value="4" <?php echo Arrays::inArrayCheck('4', $BRace); ?>
                                                   tabindex="<?php echo $tabIndexNo++; ?>" id="BRaceAmeNat"
                                                   class="BorChildRadio borrowerSelector Race"><span></span>
                                            <?php echo EthnicityRaceGenderVeteran::$Race[4]; ?>
                                        </label>
                                        <div class="col-md-12" id="Native">
                                            <div class="checkbox-list">
                                                <label class="checkbox checkbox-solid font-weight-bold" for="bFiRaceSub8">
                                                    <input type="checkbox" name="bFiRaceSub8" id="bFiRaceSub8"
                                                           class="BorChildRadio borrowerSelector Asian"
                                                           value="8" <?php echo Arrays::inArrayCheck('8', $bFiRaceSub); ?> ><span></span>
                                                    <?php echo EthnicityRaceGenderVeteran::$RaceSub[8]; ?>
                                                </label>
                                                <label class="checkbox checkbox-solid font-weight-bold" for="bFiRaceSub9">
                                                    <input type="checkbox" name="bFiRaceSub9" id="bFiRaceSub9"
                                                           class="BorChildRadio borrowerSelector Asian"
                                                           value="9" <?php echo Arrays::inArrayCheck('9', $bFiRaceSub); ?> ><span></span>
                                                    <?php echo EthnicityRaceGenderVeteran::$RaceSub[9]; ?>
                                                </label>
                                                <label class="checkbox checkbox-solid font-weight-bold" for="bFiRaceSub10">
                                                    <input type="checkbox" name="bFiRaceSub10" id="bFiRaceSub10"
                                                           class="BorChildRadio borrowerSelector Asian"
                                                           value="10" <?php echo Arrays::inArrayCheck('10', $bFiRaceSub); ?> ><span></span>
                                                    <?php echo EthnicityRaceGenderVeteran::$RaceSub[10]; ?>
                                                </label>
                                                <label class="checkbox checkbox-solid font-weight-bold" for="bFiRaceSub11">
                                                    <input type="checkbox" name="bFiRaceSub11" id="bFiRaceSub11"
                                                           class="BorChildRadio borrowerSelector Asian"
                                                           value="11" <?php echo Arrays::inArrayCheck('11', $bFiRaceSub); ?> ><span></span>
                                                    <?php echo EthnicityRaceGenderVeteran::$RaceSub[11]; ?>
                                                </label>
                                                <div class="col-md-12 no-padding" id="PacificDiv">
                                                    <div class="col-md-12">
                                                        Print Race
                                                        <input type="text" class="form-control input-sm"
                                                               name="bFiRacePacificOther"
                                                               id="bFiRacePacificOther"
                                                               value="<?php echo htmlspecialchars($bFiRacePacificOther); ?>">
                                                        <span class="text-muted">For example: Fijian, Tongan, and so on.</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <label class="checkbox checkbox-solid font-weight-bold" for="BRace5">
                                            <input type="checkbox" name="BRace5"
                                                   value="5" <?php echo Arrays::inArrayCheck('5', $BRace); ?>
                                                   tabindex="<?php echo $tabIndexNo++; ?>" id="BRace5"
                                                   class="BorChildRadio borrowerSelector Race"><span></span>
                                            <?php echo EthnicityRaceGenderVeteran::$Race[5]; ?>
                                        </label>
                                        <label class="checkbox checkbox-solid font-weight-bold" for="BRace6">
                                            <input type="checkbox" name="BRace6" id="BRace6"
                                                   value="6" <?php echo Arrays::inArrayCheck( '6',$BRace); ?>
                                                   tabindex="<?php echo $tabIndexNo++; ?>"
                                                   class="BorChildRadio borrowerSelector Race"><span></span>
                                            <?php echo EthnicityRaceGenderVeteran::$Race[6]; ?>
                                        </label>
                                    </div>
                                    <div class="clsAnswer"></div>
                                <?php } else { ?>
                                    <input type="checkbox" name="BRace1" value="1"
                                           disabled <?php echo Arrays::inArrayCheck('1', $BRace); ?>
                                           tabindex="<?php echo $tabIndexNo++; ?>">&nbsp;
                                    <?php echo EthnicityRaceGenderVeteran::$Race[1]; ?>

                                    <br>
                                    <?php if ($BRace) { ?>
                                        <div class="col-md-12 no-padding hide" id="borEnrolledPrincipalTribeDiv">
                                            <div class="col-md-12">
                                                <label for="borEnrolledPrincipalTribe">
                                                    Enrolled or Principal Tribe
                                                </label>
                                                <input
                                                        type="text" readonly
                                                        class="form-control input-sm"
                                                        name="borEnrolledPrincipalTribe"
                                                        id="borEnrolledPrincipalTribe"
                                                        value="<?php echo $borEnrolledPrincipalTribe; ?>">
                                            </div>
                                        </div>
                                    <?php } ?>
                                    <input type="checkbox" name="BRace2" value="2"
                                           disabled <?php echo Arrays::inArrayCheck($BRace, '2'); ?>
                                           tabindex="<?php echo $tabIndexNo++; ?>">&nbsp;
                                    <?php echo EthnicityRaceGenderVeteran::$Race[2]; ?>
                                    <br>
                                    <div class="col-md-12" id="Asian">
                                        <input type="checkbox" name="bFiRaceSub1" id="bFiRaceSub1" class="" disabled
                                               value="1" <?php echo Arrays::inArrayCheck('1', $bFiRaceSub); ?> >
                                        <?php echo EthnicityRaceGenderVeteran::$RaceSub[1]; ?>
                                        <br>
                                        <input type="checkbox" name="bFiRaceSub2" id="bFiRaceSub2" class="" disabled
                                               value="2" <?php echo Arrays::inArrayCheck('2', $bFiRaceSub); ?> > <?php echo EthnicityRaceGenderVeteran::$RaceSub[2]; ?>
                                        <br>
                                        <input type="checkbox" name="bFiRaceSub3" id="bFiRaceSub3" class="" disabled
                                               value="3" <?php echo Arrays::inArrayCheck('3', $bFiRaceSub); ?> > <?php echo EthnicityRaceGenderVeteran::$RaceSub[3]; ?>
                                        <br>
                                        <input type="checkbox" name="bFiRaceSub4" id="bFiRaceSub4" class="" disabled
                                               value="4" <?php echo Arrays::inArrayCheck('4', $bFiRaceSub); ?> > <?php echo EthnicityRaceGenderVeteran::$RaceSub[4]; ?>
                                        <br>
                                        <input type="checkbox" name="bFiRaceSub5" id="bFiRaceSub5" class="" disabled
                                               value="5" <?php echo Arrays::inArrayCheck('5', $bFiRaceSub); ?> > <?php echo EthnicityRaceGenderVeteran::$RaceSub[5]; ?>
                                        <br>
                                        <input type="checkbox" name="bFiRaceSub6" id="bFiRaceSub6" class="" disabled
                                               value="6" <?php echo Arrays::inArrayCheck('6', $bFiRaceSub); ?> > <?php echo EthnicityRaceGenderVeteran::$RaceSub[6]; ?>
                                        <br>
                                        <input type="checkbox" name="bFiRaceSub7" id="bFiRaceSub7" class="" disabled
                                               value="7" <?php echo Arrays::inArrayCheck('7', $bFiRaceSub); ?> > <?php echo EthnicityRaceGenderVeteran::$RaceSub[7]; ?>
                                    </div>
                                    <input type="checkbox" name="BRace3" value="3"
                                           disabled <?php echo Arrays::inArrayCheck('3', $BRace); ?>
                                           tabindex="<?php echo $tabIndexNo++; ?>">&nbsp;<?php echo EthnicityRaceGenderVeteran::$Race[3]; ?>
                                    <br>
                                    <input type="checkbox" name="BRace4" value="4"
                                           disabled <?php echo Arrays::inArrayCheck('4', $BRace); ?>
                                           tabindex="<?php echo $tabIndexNo++; ?>">&nbsp;<?php echo EthnicityRaceGenderVeteran::$Race[4]; ?>
                                    <div class="col-md-12" id="Native">
                                        <input type="checkbox" name="bFiRaceSub8" id="bFiRaceSub8" class=""
                                               value="8" <?php echo Arrays::inArrayCheck('8', $bFiRaceSub); ?> >
                                        <?php echo EthnicityRaceGenderVeteran::$RaceSub[8]; ?>
                                        <br>
                                        <input type="checkbox" name="bFiRaceSub9" id="bFiRaceSub9" class=""
                                               value="9" <?php echo Arrays::inArrayCheck('9', $bFiRaceSub); ?> >
                                        <?php echo EthnicityRaceGenderVeteran::$RaceSub[9]; ?>
                                        <br>
                                        <input type="checkbox" name="bFiRaceSub10" id="bFiRaceSub10" class=""
                                               value="10" <?php echo Arrays::inArrayCheck('10', $bFiRaceSub); ?> >
                                        <?php echo EthnicityRaceGenderVeteran::$RaceSub[10]; ?>
                                        <br>
                                        <input type="checkbox" name="bFiRaceSub11" id="bFiRaceSub11" class=""
                                               value="11" <?php echo Arrays::inArrayCheck('11', $bFiRaceSub); ?> >
                                        <?php echo EthnicityRaceGenderVeteran::$RaceSub[11]; ?>
                                    </div>
                                    <br>
                                    <input type="checkbox" name="BRace5" value="5"
                                           disabled <?php echo Arrays::inArrayCheck('5', $BRace); ?>
                                           tabindex="<?php echo $tabIndexNo++; ?>">&nbsp;<?php echo EthnicityRaceGenderVeteran::$Race[5]; ?>
                                    <br>
                                    <input type="checkbox" name="BRace6" value="6"
                                           disabled <?php echo Arrays::inArrayCheck('6', $BRace); ?>
                                           tabindex="<?php echo $tabIndexNo++; ?>">&nbsp;<?php echo EthnicityRaceGenderVeteran::$Race[6]; ?>
                                    <?php
                                }
                                ?>
                            </td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">Sex:</td>
                            <td>
                                <?php
                                if ($allowToEdit) {
                                    ?>
                                    <div class="radio-inline">
                                        <label class="radio radio-solid font-weight-bold" for="BGenderM">
                                            <input type="radio" name="BGender" id="BGenderM"
                                                   value="2" <?php echo Strings::isChecked('2', $BGender); ?>
                                                   class="BorChildRadio borrowerSelector"
                                                   tabindex="<?php echo $tabIndexNo++; ?>"><span></span> <?php echo EthnicityRaceGenderVeteran::$Gender[2]; ?>
                                        </label>
                                        <label class="radio radio-solid font-weight-bold" for="BGenderF">
                                            <input type="radio" name="BGender" id="BGenderF"
                                                   value="1" <?php echo Strings::isChecked('1', $BGender); ?>
                                                   class="BorChildRadio borrowerSelector"
                                                   tabindex="<?php echo $tabIndexNo++; ?>"><span></span> <?php echo EthnicityRaceGenderVeteran::$Gender[1]; ?>
                                        </label>
                                        <label class="radio radio-solid font-weight-bold" for="BGenderNotDis">
                                            <input type="radio" name="BGender" id="BGenderNotDis"
                                                   value="3" <?php echo Strings::isChecked('3', $BGender); ?>
                                                   class="BorChildRadio borrowerSelector"
                                                   tabindex="<?php echo $tabIndexNo++; ?>"><span></span> <?php echo EthnicityRaceGenderVeteran::$Gender[3]; ?>
                                        </label>
                                    </div>
                                <?php } else { ?>
                                    <input type="radio" name="BGender" value="2"
                                           disabled <?php echo Strings::isChecked('2', $BGender); ?>
                                           tabindex="<?php echo $tabIndexNo++; ?>"><?php echo EthnicityRaceGenderVeteran::$Gender[2]; ?>
                                    <input type="radio" name="BGender" value="1"
                                           disabled <?php echo Strings::isChecked('1', $BGender); ?>
                                           tabindex="<?php echo $tabIndexNo++; ?>"><?php echo EthnicityRaceGenderVeteran::$Gender[1]; ?>
                                    <input type="radio" name="BGender" value="3"
                                           disabled <?php echo Strings::isChecked('3', $BGender); ?>
                                           tabindex="<?php echo $tabIndexNo++; ?>"><?php echo EthnicityRaceGenderVeteran::$Gender[3]; ?>
                                    <?php
                                }
                                ?>
                            </td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">Veteran</td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <div class="radio-list">
                                        <label class="radio radio-solid font-weight-bold" for="BVeteran1">
                                            <input type="radio" name="BVeteran" id="BVeteran1"
                                                   class="BorChildRadio borrowerSelector"
                                                   value="1" <?php echo Strings::isChecked('1', $BVeteran); ?>
                                                   tabindex="<?php echo $tabIndexNo++; ?>"> <span></span><?php echo EthnicityRaceGenderVeteran::$Veteran[1]; ?>
                                        </label>
                                        <label class="radio radio-solid font-weight-bold" for="BVeteran2">
                                            <input type="radio" name="BVeteran" id="BVeteran2"
                                                   class="BorChildRadio borrowerSelector"
                                                   value="2" <?php echo Strings::isChecked('2', $BVeteran); ?>
                                                   tabindex="<?php echo $tabIndexNo++; ?>"><span></span> <?php echo EthnicityRaceGenderVeteran::$Veteran[2]; ?>
                                        </label>
                                        <label class="radio radio-solid font-weight-bold" for="BVeteran3">
                                            <input type="radio" name="BVeteran" id="BVeteran3"
                                                   class="BorChildRadio borrowerSelector"
                                                   value="3" <?php echo Strings::isChecked('3', $BVeteran); ?>
                                                   tabindex="<?php echo $tabIndexNo++; ?>"> <span></span>
                                            <?php echo EthnicityRaceGenderVeteran::$Veteran[3]; ?>
                                        </label>
                                        <label class="radio radio-solid font-weight-bold" for="BVeteran4">
                                            <input type="radio" name="BVeteran" id="BVeteran4"
                                                   class="BorChildRadio borrowerSelector"
                                                   value="4" <?php echo Strings::isChecked('4', $BVeteran); ?>
                                                   tabindex="<?php echo $tabIndexNo++; ?>"> <span></span>
                                            <?php echo EthnicityRaceGenderVeteran::$Veteran[4]; ?>
                                        </label>
                                        <label class="radio radio-solid font-weight-bold" for="BVeteran5">
                                            <input type="radio" name="BVeteran" id="BVeteran5"
                                                   class="BorChildRadio borrowerSelector"
                                                   value="5" <?php echo Strings::isChecked('5', $BVeteran); ?>
                                                   tabindex="<?php echo $tabIndexNo++; ?>"><span></span>
                                            <?php echo EthnicityRaceGenderVeteran::$Veteran[5]; ?>
                                        </label>
                                    </div>
                                <?php } else { ?>
                                    <input type="radio" name="BVeteran" id="BVeteran"
                                           value="1" <?php echo Strings::isChecked('1', $BVeteran); ?>
                                           disabled tabindex="<?php echo $tabIndexNo++; ?>"> <?php echo EthnicityRaceGenderVeteran::$Veteran[1]; ?>
                                    <br>
                                    <input type="radio" name="BVeteran" id="BVeteran"
                                           value="2" <?php echo Strings::isChecked('2', $BVeteran); ?>
                                           disabled tabindex="<?php echo $tabIndexNo++; ?>"> <?php echo EthnicityRaceGenderVeteran::$Veteran[2]; ?>
                                    <br>
                                    <input type="radio" name="BVeteran" id="BVeteran"
                                           value="3" <?php echo Strings::isChecked('3', $BVeteran); ?>
                                           disabled tabindex="<?php echo $tabIndexNo++; ?>"> <?php echo EthnicityRaceGenderVeteran::$Veteran[3]; ?>
                                    <br>
                                    <input type="radio" name="BVeteran" id="BVeteran"
                                           value="4" <?php echo Strings::isChecked('4', $BVeteran); ?>
                                           disabled tabindex="<?php echo $tabIndexNo++; ?>"> <?php echo EthnicityRaceGenderVeteran::$Veteran[4]; ?>
                                    <br>
                                    <input type="radio" name="BVeteran" id="BVeteran"
                                           value="5" <?php echo Strings::isChecked('5', $BVeteran); ?>
                                           disabled tabindex="<?php echo $tabIndexNo++; ?>"> <?php echo EthnicityRaceGenderVeteran::$Veteran[5]; ?>
                                <?php } ?>
                            </td>
                        </tr>
                    </table>
                    <div class="clearfix"></div>

                    <?php if (glCustomJobForProcessingCompany::hideShowHMDAFields(PageVariables::$PCID)) { ?>
                    <div class="form-group row col-lg-12 m-0 mb-4 px-0" id="bFiTitle">
                        <label class="bg-secondary  py-4  col-lg-12"><b>To Be Completed by Financial
                                Institution
                                (for application taken in person)</b></label>
                    </div>


                    <div class="clearfix"></div>
                    <?php if ($allowToEdit) { ?>
                        <table width="100%"
                               class="table   table-bordered table-condensed table-sm table-vertical-center">
                            <tr>
                                <td width="80%" class="font-weight-bold">Was the ethnicity of the Borrower collected on
                                    the basis of visual
                                    observation
                                    or surname?
                                </td>
                                <td width="20%">
                                    <div class="radio-inline">
                                        <label class="radio radio-solid font-weight-bold" for="bFiEthnicityYes">
                                            <input type="radio" name="bFiEthnicity" id="bFiEthnicityYes" class=""
                                                   value="Yes" <?php echo Strings::isChecked('Yes', $bFiEthnicity); ?>><span></span>
                                            Yes
                                        </label>
                                        <label class="radio radio-solid font-weight-bold" for="bFiEthnicityNo">
                                            <input type="radio" name="bFiEthnicity" id="bFiEthnicityNo" class=""
                                                   value="No" <?php echo Strings::isChecked('No', $bFiEthnicity); ?>><span></span>
                                            No
                                        </label>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td width="80%" class="font-weight-bold">Was the sex of the Borrower collected on the
                                    basis of visual observation
                                    or
                                    surname?
                                </td>
                                <td width="20%">
                                    <div class="radio-inline">
                                        <label class="radio radio-solid font-weight-bold" for="bFiSexYes">
                                            <input type="radio" name="bFiSex" id="bFiSexYes" class=""
                                                   value="Yes" <?php echo Strings::isChecked('Yes', $bFiSex); ?>><span></span>
                                            Yes
                                        </label>
                                        <label class="radio radio-solid font-weight-bold" for="bFiSexNo">

                                            <input type="radio" name="bFiSex" id="bFiSexNo" class=""
                                                   value="No" <?php echo Strings::isChecked('No', $bFiSex); ?>>
                                            <span></span>No
                                        </label>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td width="80%" class="font-weight-bold">Was the race of the Borrower collected on the
                                    basis of visual
                                    observation or
                                    surname?
                                </td>
                                <td width="20%">
                                    <div class="radio-inline">
                                        <label class="radio radio-solid font-weight-bold" for="bFiRaceYes">
                                            <input type="radio" name="bFiRace" id="bFiRaceYes" class=""
                                                   value="Yes" <?php echo Strings::isChecked('Yes', $bFiRace); ?>><span></span>
                                            Yes
                                        </label>
                                        <label class="radio radio-solid font-weight-bold" for="bFiRaceNo">
                                            <input type="radio" name="bFiRace" id="bFiRaceNo" class=""
                                                   value="No" <?php echo Strings::isChecked('No', $bFiRace); ?>><span></span>
                                            No
                                        </label>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    <?php } else { ?>
                        <table>
                            <tr>
                                <td width="80%">Was the ethnicity of the Borrower collected on the basis of visual
                                    observation
                                    or surname?
                                </td>
                                <td width="20%">
                                    <input type="radio" name="bFiEthnicity" id="bFiEthnicity" class="" value="Yes"
                                           disabled <?php echo Strings::isChecked('Yes', $bFiEthnicity); ?>> Yes
                                    <input type="radio" name="bFiEthnicity" id="bFiEthnicity" class="" value="No"
                                           disabled <?php echo Strings::isChecked('No', $bFiEthnicity); ?>> No
                                </td>
                            </tr>
                            <tr>
                                <td width="80%">Was the sex of the Borrower collected on the basis of visual observation
                                    or
                                    surname?
                                </td>
                                <td width="20%">
                                    <input type="radio" name="bFiSex" id="bFiSex" class="" value="Yes"
                                           disabled <?php echo Strings::isChecked('Yes', $bFiSex); ?>> Yes
                                    <input type="radio" name="bFiSex" id="bFiSex" class="" value="No"
                                           disabled <?php echo Strings::isChecked('No', $bFiSex); ?>> No
                                </td>
                            </tr>
                            <tr>
                                <td width="80%">Was the race of the Borrower collected on the basis of visual
                                    observation or
                                    surname?
                                </td>
                                <td width="20%">
                                    <input type="radio" name="bFiRace" id="bFiRace" class="" value="Yes"
                                           disabled <?php echo Strings::isChecked('Yes', $bFiRace); ?>> Yes
                                    <input type="radio" name="bFiRace" id="bFiRace" class="" value="No"
                                           disabled <?php echo Strings::isChecked('No', $bFiRace); ?>> No
                                </td>
                            </tr>
                        </table>
                    <?php } ?>


                    <div class="form-group row col-lg-12 m-0 my-4 px-0" id="bFiTitle">
                        <label class="bg-secondary  py-4  col-lg-12"><b>The Demographic Information was
                                provided
                                through</b></label>
                    </div>


                    <?php if ($allowToEdit) { ?>
                        <table width="100%"
                               class="table   table-bordered table-condensed table-sm table-vertical-center">
                            <tr>
                                <td>
                                    <div class="radio-list">
                                        <label class="radio radio-solid font-weight-bold" for="bDemoInfo1">
                                            <input type="radio" name="bDemoInfo" id="bDemoInfo1"
                                                   value="1" <?php echo Strings::isChecked('1', $bDemoInfo); ?>>
                                            <span></span>Face-to-Face Interview (includes Electronic Media w/ Video
                                            Component)

                                        </label>

                                        <label class="radio radio-solid font-weight-bold" for="bDemoInfo2">
                                            <input type="radio" name="bDemoInfo" id="bDemoInfo2"
                                                   value="2" <?php echo Strings::isChecked('2', $bDemoInfo); ?>><span></span>
                                            Telephone Interview
                                        </label>

                                        <label class="radio radio-solid font-weight-bold" for="bDemoInfo3">
                                            <input type="radio" name="bDemoInfo" id="bDemoInfo3"
                                                   value="3" <?php echo Strings::isChecked('3', $bDemoInfo); ?>><span></span>
                                            Fax or Mail
                                        </label>

                                        <label class="radio radio-solid font-weight-bold" for="bDemoInfo4">
                                            <input type="radio" name="bDemoInfo" id="bDemoInfo4"
                                                   value="4" <?php echo Strings::isChecked('4', $bDemoInfo); ?>><span></span>
                                            Email or Internet

                                        </label>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    <?php } else { ?>
                        <table>
                            <tr>
                                <td>
                                    <input type="radio" name="bDemoInfo" id="bDemoInfo" value="1"
                                           disabled <?php echo Strings::isChecked('1', $bDemoInfo); ?>>
                                    Face-to-Face Interview (includes Electronic Media w/ Video Component)
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <input type="radio" name="bDemoInfo" id="bDemoInfo" value="2"
                                           disabled <?php echo Strings::isChecked('2', $bDemoInfo); ?>>
                                    Telephone Interview
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <input type="radio" name="bDemoInfo" id="bDemoInfo" value="3"
                                           disabled <?php echo Strings::isChecked('3', $bDemoInfo); ?>>
                                    Fax or Mail
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <input type="radio" name="bDemoInfo" id="bDemoInfo" value="4"
                                           disabled <?php echo Strings::isChecked('4', $bDemoInfo); ?>>
                                    Email or Internet
                                </td>
                            </tr>
                        </table>
                    <?php } ?>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>
    <?php
    if ($isCoBorrower == 1 || $publicUser == 1) {
        ?>
        <div class="col-md-6 mb-2 coBorrowerSections">
            <div class="card card-custom CoBorrowerGOVTCard">
                <!-- Deal Analysis Calculator Start -->
                <div class="card-header card-header-tabs-line bg-gray-100  ">
                    <div class="card-title">
                        <h3 class="card-label">
                            CoBorrower
                        </h3>
                    </div>
                    <div class="card-toolbar">
                        <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none"
                           data-card-tool="toggle"
                           data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                            <i class="ki ki-arrow-down icon-nm"></i>
                        </a>
                        <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none"
                           data-card-tool="reload"
                           data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                            <i class="ki ki-reload icon-nm"></i>
                        </a>
                        <a href="javascript:void(0);"
                           class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                           data-card-tool="toggle"
                           data-section="CoBorrowerGOVTCard"
                           data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                            <i class="ki ki-arrow-down icon-nm"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body CoBorrowerGOVTCard_body">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="form-group row">
                                <label class="font-weight-bold col-md-5" for="coBorrowerCreditScoreOfApplicant">
                                    Do you wish to furnish this information?
                                </label>
                                <div class="col-md-7">
                                    <?php
                                    if ($allowToEdit) {
                                        ?>
                                        <div class="radio-inline">
                                            <label class="radio radio-solid font-weight-bold" for="CoBrYes">
                                                <input type="radio" name="PublishCBInfo" value="2" class="CoBrYes"
                                                       id="CoBrYes"
                                                       tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('2', $PublishCBInfo); ?>
                                                       onclick="showAndHideQADivCB(this.value, 'coBorrower');"><span></span>
                                                Yes
                                            </label>
                                            <label class="radio radio-solid font-weight-bold" for="CoBrNo">
                                                <input type="radio" name="PublishCBInfo" value="1" id="CoBrNo"
                                                       tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('1', $PublishCBInfo); ?>
                                                       onclick="showAndHideQADivCB(this.value, 'coBorrower');"><span></span>
                                                No
                                            </label>
                                            <label class="radio radio-solid font-weight-bold" for="CoBrNo3">
                                                <input type="radio" name="PublishCBInfo" value="3" id="CoBrNo3"
                                                       tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('3', $PublishCBInfo); ?>
                                                       onclick="showAndHideQADivCB(this.value, 'coBorrower');"><span></span>
                                                N/A
                                            </label>
                                        </div>
                                        <?php
                                    } else {
                                        if ($PublishCBInfo == 2) {
                                            echo '<h5>Yes</h5>';
                                        } elseif ($PublishCBInfo == 1) {
                                            echo '<h5>No</h5>';
                                        } else {
                                            echo '<h5>N/A</h5>';
                                        }
                                    }
                                    ?>
                                </div>
                            </div>
                            <?php if (glCustomJobForProcessingCompany::hideShowHMDAFields(PageVariables::$PCID)) { ?>
                                <?php if(0) { //hide for now?>
                                    <div class="form-group row">
                                        <label class="font-weight-bold col-md-5" for="coBorrowerRaceOfApplicant">
                                            Race of Co-Applicant or Co-Borrower - American Indian or Alaska Native Enrolled or
                                            Principal Tribe
                                        </label>
                                        <div class="col-md-7">
                                            <input type="text"
                                                   class="form-control input-sm"
                                                   name="coBorrowerRaceOfApplicant"
                                                   id="coBorrowerRaceOfApplicant"
                                                   value="<?php echo htmlentities($coBorrowerRaceOfApplicant); ?>">
                                        </div>
                                    </div>
                                <?php } ?>
                            <div class="form-group row">
                                <label class="font-weight-bold col-md-5" for="coBorrowerAgeOfApplicant">
                                    Age of Co-Applicant or Co-Borrower
                                </label>
                                <div class="col-md-7">
                                    <input type="text"
                                           class="form-control input-sm"
                                           name="coBorrowerAgeOfApplicant"
                                           id="coBorrowerAgeOfApplicant"
                                           value="<?php echo $coBorrowerAgeOfApplicant; ?>">
                                </div>
                            </div>
                            <?php } ?>
                            <div class="form-group row">
                                <label class="font-weight-bold col-md-5" for="coBorrowerCreditScoreOfApplicant">
                                    Credit Score of Co-Applicant or Co-Borrower
                                </label>
                                <div class="col-md-7">
                                    <input type="text"
                                           class="form-control input-sm"
                                           name="coBorrowerCreditScoreOfApplicant"
                                           id="coBorrowerCreditScoreOfApplicant"
                                           value="<?php echo $coBorrowerCreditScoreOfApplicant; ?>">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="font-weight-bold col-md-5" for="coBorrowerCreditScoringModelOfApplicant">
                                    Co-Applicant or Co-Borrower, Name and Version of Credit Scoring Model
                                </label>
                                <div class="col-md-7">
                                    <select class="form-control input-sm creditScoringModel"
                                            name="coBorrowerCreditScoringModelOfApplicant"
                                            id="coBorrowerCreditScoringModelOfApplicant">
                                        <option value="">-Select-</option>
                                        <?php foreach ($creditScoringModelArray as $coBoCreditScoringModelKey => $coBoCreditScoringModelValue) { ?>
                                            <option value="<?php echo $coBoCreditScoringModelKey; ?>" <?php echo Arrays::isSelected($coBoCreditScoringModelKey, $coBorrowerCreditScoringModelOfApplicant); ?> ><?php echo $coBoCreditScoringModelValue; ?></option>
                                        <?php } ?>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row <?php if ($coBorrowerCreditScoringModelOfApplicant != 8) {
                                echo 'hide';
                            } ?> " id="coBorrowerCreditScoringModelOfApplicantOtherDiv">
                                <label class="font-weight-bold col-md-5"
                                       for="coBorrowerCreditScoringModelOfApplicantOther">
                                    Other
                                </label>
                                <div class="col-md-7">
                                    <input type="text" class="form-control input-sm"
                                           name="coBorrowerCreditScoringModelOfApplicantOther"
                                           id="coBorrowerCreditScoringModelOfApplicantOther"
                                           value="<?php echo $coBorrowerCreditScoringModelOfApplicantOther; ?>"
                                    >
                                </div>
                            </div>
                            <?php if (glCustomJobForProcessingCompany::hideShowHMDAFields(PageVariables::$PCID)) { ?>
                            <div class="form-group row">
                                <label class="font-weight-bold col-md-5"
                                       for="coBorrowerCreditScoringModelConditionalFreeOfApplicant">
                                    Co-Applicant or Co-Borrower, Name and Version of Credit Scoring Model: Conditional
                                    Free Form Text Field For Code 8
                                </label>
                                <div class="col-md-7">
                                    <input
                                            type="text"
                                            class="form-control input-sm"
                                            name="coBorrowerCreditScoringModelConditionalFreeOfApplicant"
                                            id="coBorrowerCreditScoringModelConditionalFreeOfApplicant"
                                            value="<?php echo htmlentities($coBorrowerCreditScoringModelConditionalFreeOfApplicant); ?>">
                                </div>
                            </div>
                            <?php } ?>
                        </div>
                    </div>
                    <div class="row">
                        <table width="100%"
                               class="table table-hover  table-condensed table-sm table-vertical-center">
                            <tr>
                                <td class="font-weight-bold" width="50%">What is your Ethnicity:</td>
                                <td width="50%">
                                    <?php
                                    if ($allowToEdit) { ?>
                                        <div class="checkbox-list">
                                            <label class="checkbox checkbox-solid font-weight-bold" for="CBEthnicity2">
                                                <input type="checkbox" name="CBEthnicity2" id="CBEthnicity2"
                                                       value="2" <?php echo Arrays::inArrayCheck('2', $CBEthnicity); ?>
                                                       tabindex="<?php echo $tabIndexNo++; ?>"
                                                       class="CoBorChildRadio CBHispanic coBorrower"><span></span>
                                                <?php echo EthnicityRaceGenderVeteran::$Ethnicity[2]; ?>
                                            </label>
                                            <div class="col-md-12 px-4" id="CBHispanic">
                                                <div class="checkbox-list">
                                                    <label class="checkbox checkbox-solid font-weight-bold"
                                                           for="CBEthnicitySub1">
                                                        <input type="checkbox" name="CBEthnicitySub1" id="CBEthnicitySub1"
                                                               class="CoBorChildRadio CBHispanicPrintOrigin"
                                                               value="1" <?php echo Arrays::inArrayCheck('1', $CBEthnicitySub); ?> >
                                                        <span></span>
                                                        <?php echo EthnicityRaceGenderVeteran::$EthnicitySub[1]; ?>
                                                    </label>
                                                    <label class="checkbox checkbox-solid font-weight-bold"
                                                           for="CBEthnicitySub2">
                                                        <input type="checkbox" name="CBEthnicitySub2" id="CBEthnicitySub2"
                                                               class="CoBorChildRadio CBHispanicPrintOrigin"
                                                               value="2" <?php echo Arrays::inArrayCheck('2', $CBEthnicitySub); ?> >
                                                        <span></span>
                                                        <?php echo EthnicityRaceGenderVeteran::$EthnicitySub[2]; ?>
                                                    </label>
                                                    <label class="checkbox checkbox-solid font-weight-bold"
                                                           for="CBEthnicitySub3">
                                                        <input type="checkbox" name="CBEthnicitySub3" id="CBEthnicitySub3"
                                                               class="CoBorChildRadio CBHispanicPrintOrigin"
                                                               value="3" <?php echo Arrays::inArrayCheck('3', $CBEthnicitySub); ?> ><span></span>
                                                        <?php echo EthnicityRaceGenderVeteran::$EthnicitySub[3]; ?>
                                                    </label>
                                                    <label class="checkbox checkbox-solid font-weight-bold"
                                                           for="CBEthnicitySub4">
                                                        <input type="checkbox" name="CBEthnicitySub4" id="CBEthnicitySub4"
                                                               class="CoBorChildRadio CBHispanicPrintOrigin"
                                                               value="4" <?php echo Arrays::inArrayCheck('4', $CBEthnicitySub); ?> >
                                                        <span></span>
                                                        <?php echo EthnicityRaceGenderVeteran::$EthnicitySub[4]; ?>
                                                    </label>
                                                    <div class="col-md-12"
                                                         id="CBHispanicPrintOriginDiv">
                                                        <label class="col-md-12 px-0" for="CBEthnicitySubOtherTxt">
                                                            Print Origin</label>
                                                        <div class="col-md-12 px-0">
                                                            <input type="text"
                                                                 autocomplete="off"
                                                                 name="CBEthnicitySubOther"
                                                                 id="CBEthnicitySubOtherTxt"
                                                                 class="form-control input-sm"
                                                                 value="<?php echo htmlspecialchars($CBEthnicitySubOther); ?>">
                                                            <span class="text-muted">
                                                                For example: Argentinean, Colombian, Dominican, Nicaraguan, Salvadoran, Spaniard, and so on</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <label class="checkbox checkbox-solid font-weight-bold" for="CBEthnicity1">
                                                <input type="checkbox" name="CBEthnicity1" id="CBEthnicity1"
                                                       value="1" <?php echo Arrays::inArrayCheck('1', $CBEthnicity); ?>
                                                       tabindex="<?php echo $tabIndexNo++; ?>"
                                                       class="CoBorChildRadio CBHispanic coBorrower"><span></span>
                                                <?php echo EthnicityRaceGenderVeteran::$Ethnicity[1]; ?>
                                            </label>
                                            <label class="checkbox checkbox-solid font-weight-bold" for="CBEthnicity3">
                                                <input type="checkbox" name="CBEthnicity3" id="CBEthnicity3"
                                                       value="3" <?php echo Arrays::inArrayCheck('3', $CBEthnicity); ?>
                                                       tabindex="<?php echo $tabIndexNo++; ?>"
                                                       class="CoBorChildRadio CBHispanic coBorrower"><span></span>
                                                <?php echo EthnicityRaceGenderVeteran::$Ethnicity[3]; ?>
                                            </label>
                                        </div>
                                    <?php } else { ?>
                                        <input type="checkbox" name="CBEthnicity2" disabled
                                               value="2" <?php echo Arrays::inArrayCheck('2', $CBEthnicity); ?>
                                               tabindex="<?php echo $tabIndexNo++; ?>">
                                        <?php echo EthnicityRaceGenderVeteran::$Ethnicity[2]; ?>
                                        <br>
                                        <div class="col-md-12" id="CBHispanic">
                                            <input type="checkbox" name="CBEthnicitySub1" id="CBEthnicitySub1" class=""
                                                   disabled
                                                   value="1" <?php echo Arrays::inArrayCheck('1', $CBEthnicitySub); ?> >
                                            <?php echo EthnicityRaceGenderVeteran::$EthnicitySub[1]; ?>
                                            <br>
                                            <input type="checkbox" name="CBEthnicitySub2" id="CBEthnicitySub2" class=""
                                                   disabled
                                                   value="2" <?php echo Arrays::inArrayCheck('2', $CBEthnicitySub); ?> >
                                            <?php echo EthnicityRaceGenderVeteran::$EthnicitySub[2]; ?>
                                            <br>
                                            <input type="checkbox" name="CBEthnicitySub3" id="CBEthnicitySub3" class=""
                                                   disabled
                                                   value="3" <?php echo Arrays::inArrayCheck('3', $CBEthnicitySub); ?> >
                                            <?php echo EthnicityRaceGenderVeteran::$EthnicitySub[3]; ?>
                                            <br>
                                            <input type="checkbox" name="CBEthnicitySub4" id="CBEthnicitySub4" class=""
                                                   disabled
                                                   value="4" <?php echo Arrays::inArrayCheck('4', $CBEthnicitySub); ?> >
                                            <?php echo EthnicityRaceGenderVeteran::$EthnicitySub[4]; ?>
                                            <div class="col-md-12 no-padding"
                                                 id="CBHispanicPrintOriginDiv">
                                                <div class="col-md-12">
                                                    Print Origin
                                                    <input type="text" name="CBEthnicitySubOther"
                                                           id="CBEthnicitySubOther" autocomplete="off"
                                                           class="form-control input-sm" disabled
                                                           value="<?php echo htmlspecialchars($CBEthnicitySubOther); ?>">
                                                    <i>For example: Argentinean, Colombian, Dominican, Nicaraguan,
                                                        Salvadoran, Spaniard, and so on</i>
                                                </div>
                                            </div>
                                        </div>
                                        <input type="checkbox" name="CBEthnicity1" disabled
                                               value="1" <?php echo Arrays::inArrayCheck('1', $CBEthnicity); ?>
                                               tabindex="<?php echo $tabIndexNo++; ?>"><?php echo EthnicityRaceGenderVeteran::$Ethnicity[1]; ?>
                                        <br>
                                        <input type="checkbox" name="CBEthnicity3" disabled
                                               value="3" <?php echo Arrays::inArrayCheck('3', $CBEthnicity); ?>
                                               tabindex="<?php echo $tabIndexNo++; ?>"><?php echo EthnicityRaceGenderVeteran::$Ethnicity[3]; ?>
                                        <?php
                                    }
                                    ?>
                                </td>
                            </tr>
                            <tr>
                                <td class="font-weight-bold"> What is your Race:</td>
                                <td>
                                    <?php
                                    if ($allowToEdit) { ?>
                                        <div class="checkbox-list">
                                            <label class="checkbox checkbox-solid font-weight-bold" for="CBRace1">
                                                <input type="checkbox" name="CBRace1" id="CBRace1"
                                                       value="1" <?php echo Arrays::inArrayCheck('1', $CBRace); ?>
                                                       tabindex="<?php echo $tabIndexNo++; ?>"
                                                       class="CoBorChildRadio CBRace coBorrower"><span></span>
                                                <?php echo EthnicityRaceGenderVeteran::$Race[1]; ?>
                                            </label>
                                            <div class="col-md-12 no-padding" id="coBorEnrolledPrincipalTribeDiv">
                                                <div class="col-md-12">
                                                    <label for="coBorEnrolledPrincipalTribe">
                                                        Enrolled or Principal Tribe
                                                    </label>
                                                    <input type="text"
                                                           class="form-control input-sm"
                                                           name="coBorEnrolledPrincipalTribe"
                                                           id="coBorEnrolledPrincipalTribe"
                                                           value="<?php echo htmlentities($coBorEnrolledPrincipalTribe); ?>">
                                                </div>
                                            </div>
                                            <label class="checkbox checkbox-solid font-weight-bold" for="CBRace2">
                                                <input type="checkbox" name="CBRace2" id="CBRace2"
                                                       value="2" <?php echo Arrays::inArrayCheck('2', $CBRace); ?>
                                                       tabindex="<?php echo $tabIndexNo++; ?>"
                                                       class="CoBorChildRadio CBRace coBorrower"><span></span>
                                                <?php echo EthnicityRaceGenderVeteran::$Race[2]; ?>
                                            </label>
                                            <div class="col-md-12 px-4" id="CBAsian">
                                                <div class="checkbox-list">
                                                    <label class="checkbox checkbox-solid font-weight-bold" for="CBRaceSub1">
                                                        <input type="checkbox" name="CBRaceSub1" id="CBRaceSub1"
                                                               class="CoBorChildRadio CBAsian"
                                                               value="1" <?php echo Arrays::inArrayCheck('1', $CBRaceSub); ?> ><span></span>
                                                        <?php echo EthnicityRaceGenderVeteran::$RaceSub[1]; ?>

                                                    </label>
                                                    <label class="checkbox checkbox-solid font-weight-bold" for="CBRaceSub2">
                                                        <input type="checkbox" name="CBRaceSub2" id="CBRaceSub2"
                                                               class="CoBorChildRadio CBAsian"
                                                               value="2" <?php echo Arrays::inArrayCheck('2', $CBRaceSub); ?> ><span></span>

                                                        <?php echo EthnicityRaceGenderVeteran::$RaceSub[2]; ?>
                                                    </label>
                                                    <label class="checkbox checkbox-solid font-weight-bold" for="CBRaceSub3">
                                                        <input type="checkbox" name="CBRaceSub3" id="CBRaceSub3"
                                                               class="CoBorChildRadio CBAsian"
                                                               value="3" <?php echo Arrays::inArrayCheck('3', $CBRaceSub); ?> >
                                                        <span></span>
                                                        <?php echo EthnicityRaceGenderVeteran::$RaceSub[3]; ?>

                                                    </label>
                                                    <label class="checkbox checkbox-solid font-weight-bold" for="CBRaceSub4">
                                                        <input type="checkbox" name="CBRaceSub4" id="CBRaceSub4"
                                                               class="CoBorChildRadio CBAsian"
                                                               value="4" <?php echo Arrays::inArrayCheck('4', $CBRaceSub); ?> ><span></span>
                                                        <?php echo EthnicityRaceGenderVeteran::$RaceSub[4]; ?>
                                                    </label>
                                                    <label class="checkbox checkbox-solid font-weight-bold" for="CBRaceSub5">
                                                        <input type="checkbox" name="CBRaceSub5" id="CBRaceSub5"
                                                               class="CoBorChildRadio CBAsian"
                                                               value="5" <?php echo Arrays::inArrayCheck('5', $CBRaceSub); ?> ><span></span>
                                                        <?php echo EthnicityRaceGenderVeteran::$RaceSub[5]; ?>

                                                    </label>
                                                    <label class="checkbox checkbox-solid font-weight-bold" for="CBRaceSub6">
                                                        <input type="checkbox" name="CBRaceSub6" id="CBRaceSub6"
                                                               class="CoBorChildRadio CBAsian"
                                                               value="6" <?php echo Arrays::inArrayCheck('6', $CBRaceSub); ?> >
                                                        <span></span>
                                                        <?php echo EthnicityRaceGenderVeteran::$RaceSub[6]; ?>

                                                    </label>
                                                    <label class="checkbox checkbox-solid font-weight-bold" for="CBRaceSub7">
                                                        <input type="checkbox" name="CBRaceSub7" id="CBRaceSub7"
                                                               class="CoBorChildRadio CBAsian"
                                                               value="7" <?php echo Arrays::inArrayCheck('7', $CBRaceSub); ?> ><span></span>
                                                        <?php echo EthnicityRaceGenderVeteran::$RaceSub[7]; ?>

                                                    </label>
                                                    <div class="col-md-12 px-4" id="CBAsianDiv">
                                                        <label class="col-md-6 font-weight-bold">
                                                            Print Race
                                                        </label>
                                                        <span class="col-md-6"><input type="text"
                                                                                      class="form-control input-sm"
                                                                                      name="CBRaceAsianOther"
                                                                                      id="CBRaceAsianOther"
                                                                                      value="<?php echo htmlspecialchars($CBRaceAsianOther); ?>">
                                                        <span class="text-muted">
                                                            For example: Hmong, Laotian, Thai, Pakistani, Cambodian, and so on.</span>
                                                    </span>

                                                    </div>
                                                </div>
                                            </div>
                                            <label class="checkbox checkbox-solid font-weight-bold" for="CBRace3">
                                                <input type="checkbox" name="CBRace3" id="CBRace3"
                                                       value="3" <?php echo Arrays::inArrayCheck('3', $CBRace); ?>
                                                       tabindex="<?php echo $tabIndexNo++; ?>"
                                                       class="CoBorChildRadio CBRace coBorrower"><span></span>
                                                <?php echo EthnicityRaceGenderVeteran::$Race[3]; ?>
                                            </label>
                                            <label class="checkbox checkbox-solid font-weight-bold" for="CBRace4">
                                                <input type="checkbox" name="CBRace4" id="CBRace4"
                                                       value="4" <?php echo Arrays::inArrayCheck('4', $CBRace); ?>
                                                       tabindex="<?php echo $tabIndexNo++; ?>"
                                                       class="CoBorChildRadio CBRace coBorrower"><span></span>
                                                <?php echo EthnicityRaceGenderVeteran::$Race[4]; ?>

                                            </label>
                                            <div class="col-md-12 px-4" id="CBNative">
                                                <div class="checkbox-list">
                                                    <label class="checkbox radio-solid font-weight-bold" for="CBRaceSub8">
                                                        <input type="checkbox" name="CBRaceSub8" id="CBRaceSub8"
                                                               class="CoBorChildRadio CBAsian"
                                                               value="8" <?php echo Arrays::inArrayCheck('8', $CBRaceSub); ?> ><span></span>
                                                        <?php echo EthnicityRaceGenderVeteran::$RaceSub[8]; ?>

                                                    </label>
                                                    <label class="checkbox checkbox-solid font-weight-bold" for="CBRaceSub9">
                                                        <input type="checkbox" name="CBRaceSub9" id="CBRaceSub9"
                                                               class="CoBorChildRadio CBAsian"
                                                               value="9" <?php echo Arrays::inArrayCheck('9', $CBRaceSub); ?> >
                                                        <span></span>
                                                        <?php echo EthnicityRaceGenderVeteran::$RaceSub[9]; ?>

                                                    </label>
                                                    <label class="checkbox checkbox-solid font-weight-bold" for="CBRaceSub10">
                                                        <input type="checkbox" name="CBRaceSub10" id="CBRaceSub10"
                                                               class="CoBorChildRadio CBAsian"
                                                               value="10" <?php echo Arrays::inArrayCheck('10', $CBRaceSub); ?> ><span></span>
                                                        <?php echo EthnicityRaceGenderVeteran::$RaceSub[10]; ?>

                                                    </label>
                                                    <label class="checkbox checkbox-solid font-weight-bold" for="CBRaceSub11">
                                                        <input type="checkbox" name="CBRaceSub11" id="CBRaceSub11"
                                                               class="CoBorChildRadio CBAsian"
                                                               value="11" <?php echo Arrays::inArrayCheck('11', $CBRaceSub); ?> ><span></span>
                                                        <?php echo EthnicityRaceGenderVeteran::$RaceSub[11]; ?>

                                                    </label>
                                                    <div class="col-md-12 no-padding" id="CBPacificDiv">
                                                        <label class="col-md-6 font-weight-bold">
                                                            Print Race</label>
                                                        <span class="col-md-6">    <input type="text"
                                                                                          autocomplete="off"
                                                                                          class="form-control input-sm"
                                                                                          name="CBRacePacificOther"
                                                                                          id="CBRacePacificOther"
                                                                                          value="<?php echo htmlspecialchars($CBRacePacificOther); ?>">
                                                            <span class="text-muted">For example: Fijian, Tongan, and so on.</span>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <label class="checkbox checkbox-solid font-weight-bold" for="CBRace5">
                                                <input type="checkbox" name="CBRace5" id="CBRace5"
                                                       value="5" <?php echo Arrays::inArrayCheck('5', $CBRace); ?>
                                                       tabindex="<?php echo $tabIndexNo++; ?>"
                                                       class="CoBorChildRadio CBRace coBorrower"><span></span>
                                                <?php echo EthnicityRaceGenderVeteran::$Race[5]; ?>
                                            </label>
                                            <label class="checkbox checkbox-solid font-weight-bold" for="CBRace6">
                                                <input type="checkbox" name="CBRace6" id="CBRace6"
                                                       value="6" <?php echo Arrays::inArrayCheck('6', $CBRace); ?>
                                                       tabindex="<?php echo $tabIndexNo++; ?>"
                                                       class="CoBorChildRadio CBRace coBorrower"><span></span>
                                                <?php echo EthnicityRaceGenderVeteran::$Race[6]; ?>

                                            </label>
                                        </div>
                                    <?php } else { ?>
                                        <input type="checkbox" name="CBRace1" value="1"
                                               disabled <?php echo Arrays::inArrayCheck('1', $CBRace); ?>
                                               tabindex="<?php echo $tabIndexNo++; ?>">&nbsp;

                                        <?php echo EthnicityRaceGenderVeteran::$Race[1]; ?>
                                        <br>
                                        <?php if ($CBRace) { ?>
                                            <div class="col-md-12 no-padding" id="coBorEnrolledPrincipalTribeDiv">
                                                <div class="col-md-12">
                                                    <label for="coBorEnrolledPrincipalTribe">
                                                        Enrolled or Principal Tribe
                                                    </label>
                                                    <input type="text" readonly
                                                           class="form-control input-sm"
                                                           name="coBorEnrolledPrincipalTribe"
                                                           id="coBorEnrolledPrincipalTribe"
                                                           value="<?php echo $coBorEnrolledPrincipalTribe; ?>">
                                                </div>
                                            </div>
                                        <?php } ?>
                                        <input type="checkbox" name="CBRace2" value="2"
                                               disabled <?php echo Arrays::inArrayCheck('2', $CBRace); ?>
                                               tabindex="<?php echo $tabIndexNo++; ?>">&nbsp;Asian<br>
                                        <div class="col-md-12" id="CBAsian">
                                            <input type="checkbox" name="CBRaceSub1" id="CBRaceSub1" class="" disabled
                                                   value="1" <?php echo Arrays::inArrayCheck('1', $CBRaceSub); ?> >
                                            <?php echo EthnicityRaceGenderVeteran::$RaceSub[1]; ?>
                                            <br>
                                            <input type="checkbox" name="CBRaceSub2" id="CBRaceSub2" class="" disabled
                                                   value="2" <?php echo Arrays::inArrayCheck('2', $CBRaceSub); ?> >
                                            <?php echo EthnicityRaceGenderVeteran::$RaceSub[2]; ?>
                                            <br>
                                            <input type="checkbox" name="CBRaceSub3" id="CBRaceSub3" class="" disabled
                                                   value="3" <?php echo Arrays::inArrayCheck('3', $CBRaceSub); ?> >
                                            <?php echo EthnicityRaceGenderVeteran::$RaceSub[3]; ?>
                                            <br>
                                            <input type="checkbox" name="CBRaceSub4" id="CBRaceSub4" class="" disabled
                                                   value="4" <?php echo Arrays::inArrayCheck('4', $CBRaceSub); ?> >
                                            <?php echo EthnicityRaceGenderVeteran::$RaceSub[4]; ?>
                                            <br>
                                            <input type="checkbox" name="CBRaceSub5" id="CBRaceSub5" class="" disabled
                                                   value="5" <?php echo Arrays::inArrayCheck('5', $CBRaceSub); ?> >
                                            <?php echo EthnicityRaceGenderVeteran::$RaceSub[5]; ?>
                                            <br>
                                            <input type="checkbox" name="CBRaceSub6" id="CBRaceSub6" class="" disabled
                                                   value="6" <?php echo Arrays::inArrayCheck('6', $CBRaceSub); ?> >
                                            <?php echo EthnicityRaceGenderVeteran::$RaceSub[6]; ?>
                                            <br>
                                            <input type="checkbox" name="CBRaceSub7" id="CBRaceSub7" class="" disabled
                                                   value="7" <?php echo Arrays::inArrayCheck('7', $CBRaceSub); ?> >
                                            <?php echo EthnicityRaceGenderVeteran::$RaceSub[7]; ?>
                                            <div class="col-md-12 no-padding"
                                                 id="CBAsianDiv">
                                                <div class="col-md-12">
                                                    Print Race
                                                    <input type="text" class="form-control input-sm" disabled
                                                           name="CBRaceAsianOther" id="CBRaceAsianOther"
                                                           value="<?php echo $CBRaceAsianOther; ?>">
                                                    <i>For example: Hmong, Laotian, Thai, Pakistani, Cambodian, and
                                                        so
                                                        on.</i>
                                                </div>
                                            </div>
                                        </div>
                                        <input type="checkbox" name="CBRace3" value="3"
                                               disabled <?php echo Arrays::inArrayCheck('3', $CBRace); ?>
                                               tabindex="<?php echo $tabIndexNo++; ?>">&nbsp;<?php echo EthnicityRaceGenderVeteran::$Race[3]; ?>

                                        <br>
                                        <input type="checkbox" name="CBRace4" value="4"
                                               disabled <?php echo Arrays::inArrayCheck('4', $CBRace); ?>
                                               tabindex="<?php echo $tabIndexNo++; ?>">&nbsp;<?php echo EthnicityRaceGenderVeteran::$Race[4]; ?>
                                        <br>
                                        <input type="checkbox" name="CBRace" value="5"
                                               disabled <?php echo Arrays::inArrayCheck('5', $CBRace); ?>
                                               tabindex="<?php echo $tabIndexNo++; ?>">&nbsp;<?php echo EthnicityRaceGenderVeteran::$Race[5]; ?><br>
                                        <input type="checkbox" name="CBRace6" value="6"
                                               disabled <?php echo Arrays::inArrayCheck('6', $CBRace); ?>
                                               tabindex="<?php echo $tabIndexNo++; ?>">&nbsp;<?php echo EthnicityRaceGenderVeteran::$Race[6]; ?>
                                        <?php
                                    }
                                    ?>
                                    <div class="clsAnswer"></div>
                                </td>
                            </tr>
                            <tr>
                                <td class="font-weight-bold">Sex:</td>
                                <td>
                                    <?php
                                    if ($allowToEdit) { ?>
                                        <div class="radio-inline">
                                            <label class="radio radio-solid font-weight-bold" for="CBGender1">
                                                <input type="radio" name="CBGender" id="CBGender1"
                                                       value="2" <?php echo Strings::isChecked('2', $CBGender); ?>
                                                       class="CoBorChildRadio coBorrower"
                                                       tabindex="<?php echo $tabIndexNo++; ?>"><span></span>
                                                <?php echo EthnicityRaceGenderVeteran::$Gender[2]; ?>
                                            </label>
                                            <label class="radio radio-solid font-weight-bold" for="CBGender2">
                                                <input type="radio" name="CBGender" id="CBGender2"
                                                       value="1" <?php echo Strings::isChecked('1', $CBGender); ?>
                                                       class="CoBorChildRadio coBorrower"
                                                       tabindex="<?php echo $tabIndexNo++; ?>"><span></span>
                                                <?php echo EthnicityRaceGenderVeteran::$Gender[1]; ?>
                                            </label>
                                            <label class="radio radio-solid font-weight-bold" for="CBGender3">
                                                <input type="radio" name="CBGender" id="CBGender3"
                                                       value="3" <?php echo Strings::isChecked('3', $CBGender); ?>
                                                       class="CoBorChildRadio coBorrower"
                                                       tabindex="<?php echo $tabIndexNo++; ?>"><span></span>
                                                <?php echo EthnicityRaceGenderVeteran::$Gender[3]; ?>
                                            </label>
                                        </div>
                                    <?php } else { ?>
                                        <input type="radio" name="CBGender" disabled
                                               value="2" <?php echo Strings::isChecked('2', $CBGender); ?>
                                               tabindex="<?php echo $tabIndexNo++; ?>"> <?php echo EthnicityRaceGenderVeteran::$Gender[2]; ?>
                                        <input type="radio" name="CBGender" disabled
                                               value="1" <?php echo Strings::isChecked('1', $CBGender); ?>
                                               tabindex="<?php echo $tabIndexNo++; ?>"> <?php echo EthnicityRaceGenderVeteran::$Gender[1]; ?>
                                        <input type="radio" name="CBGender" disabled
                                               value="3" <?php echo Strings::isChecked('3', $CBGender); ?>
                                               tabindex="<?php echo $tabIndexNo++; ?>"> <?php echo EthnicityRaceGenderVeteran::$Gender[3]; ?>
                                        <?php
                                    }
                                    ?>
                                </td>
                            </tr>
                            <tr>
                                <td class="font-weight-bold">Veteran</td>
                                <td>
                                    <?php if ($allowToEdit) { ?>
                                        <div class="radio-list">
                                            <label class="radio radio-solid font-weight-bold" for="CBVeteran1">
                                                <input type="radio" name="CBVeteran" id="CBVeteran1"
                                                       value="1" <?php echo Strings::isChecked('1', $CBVeteran); ?>
                                                       class="CoBorChildRadio coBorrower"
                                                       tabindex="<?php echo $tabIndexNo++; ?>"><span></span>
                                                <?php echo EthnicityRaceGenderVeteran::$Veteran[1]; ?>
                                            </label>
                                            <label class="radio radio-solid font-weight-bold" for="CBVeteran2">
                                                <input type="radio" name="CBVeteran" id="CBVeteran2"
                                                       value="2" <?php echo Strings::isChecked('2', $CBVeteran); ?>
                                                       class="CoBorChildRadio coBorrower"
                                                       tabindex="<?php echo $tabIndexNo++; ?>"><span></span>
                                                <?php echo EthnicityRaceGenderVeteran::$Veteran[2]; ?>
                                            </label>
                                            <label class="radio radio-solid font-weight-bold" for="CBVeteran3">
                                                <input type="radio" name="CBVeteran" id="CBVeteran3"
                                                       value="3" <?php echo Strings::isChecked('3', $CBVeteran); ?>
                                                       class="CoBorChildRadio coBorrower"
                                                       tabindex="<?php echo $tabIndexNo++; ?>"><span></span>
                                                <?php echo EthnicityRaceGenderVeteran::$Veteran[3]; ?>
                                            </label>
                                            <label class="radio radio-solid font-weight-bold" for="CBVeteran4">
                                                <input type="radio" name="CBVeteran" id="CBVeteran4"
                                                       value="4" <?php echo Strings::isChecked('4', $CBVeteran); ?>
                                                       class="CoBorChildRadio coBorrower"
                                                       tabindex="<?php echo $tabIndexNo++; ?>"><span></span>
                                                <?php echo EthnicityRaceGenderVeteran::$Veteran[4]; ?>
                                            </label>
                                            <label class="radio radio-solid font-weight-bold" for="CBVeteran5">
                                                <input type="radio" name="CBVeteran" id="CBVeteran5"
                                                       value="5" <?php echo Strings::isChecked('5', $CBVeteran); ?>
                                                       class="CoBorChildRadio coBorrower"
                                                       tabindex="<?php echo $tabIndexNo++; ?>"><span></span>
                                                <?php echo EthnicityRaceGenderVeteran::$Veteran[5]; ?>
                                            </label>
                                        </div>
                                    <?php } else { ?>
                                        <input type="radio" disabled name="CBVeteran" id="CBVeteran"
                                               value="1" <?php echo Strings::isChecked('1', $CBVeteran); ?>
                                               class="coBorrower"
                                               tabindex="<?php echo $tabIndexNo++; ?>"> <?php echo EthnicityRaceGenderVeteran::$Veteran[1]; ?><br>
                                        <input type="radio" disabled name="CBVeteran" id="CBVeteran"
                                               value="2" <?php echo Strings::isChecked('2', $CBVeteran); ?>
                                               class="coBorrower"
                                               tabindex="<?php echo $tabIndexNo++; ?>"> <?php echo EthnicityRaceGenderVeteran::$Veteran[2]; ?><br>
                                        <input type="radio" disabled name="CBVeteran" id="CBVeteran"
                                               value="3" <?php echo Strings::isChecked('3', $CBVeteran); ?>
                                               class="coBorrower"
                                               tabindex="<?php echo $tabIndexNo++; ?>"> <?php echo EthnicityRaceGenderVeteran::$Veteran[3]; ?><br>
                                        <input type="radio" disabled name="CBVeteran" id="CBVeteran"
                                               value="4" <?php echo Strings::isChecked('4', $CBVeteran); ?>
                                               class="coBorrower"
                                               tabindex="<?php echo $tabIndexNo++; ?>"> <?php echo EthnicityRaceGenderVeteran::$Veteran[4]; ?><br>
                                        <input type="radio" disabled name="CBVeteran" id="CBVeteran"
                                               value="5" <?php echo Strings::isChecked('5', $CBVeteran); ?>
                                               class="coBorrower"
                                               tabindex="<?php echo $tabIndexNo++; ?>"> <?php echo EthnicityRaceGenderVeteran::$Veteran[5]; ?>
                                    <?php } ?>
                                </td>
                            </tr>
                        </table>
                        <?php if (glCustomJobForProcessingCompany::hideShowHMDAFields(PageVariables::$PCID)) { ?>
                        <div class="form-group row col-lg-12 m-0 mb-4 px-0" id="cbFiTitle">
                            <label class="bg-secondary  py-4  col-lg-12"><b>To Be Completed by Financial
                                    Institution (for application taken in person)</b></label>
                        </div>

                        <?php if ($allowToEdit) { ?>
                            <table width="100%"
                                   class="table table-hover  table-bordered table-condensed table-sm table-vertical-center">
                                <tr>
                                    <td width="80%" class="font-weight-bold">Was the ethnicity of the Co-Borrower
                                        collected on the basis of
                                        visual
                                        observation or surname?
                                    </td>
                                    <td width="20%">
                                        <div class="radio-inline">
                                            <label class="radio radio-solid font-weight-bold" for="CBFiEthnicityYes">
                                                <input type="radio" name="CBFiEthnicity" id="CBFiEthnicityYes" class=""
                                                       value="Yes" <?php echo Strings::isChecked('Yes', $CBFiEthnicity); ?>><span></span>
                                                Yes
                                            </label>
                                            <label class="radio radio-solid font-weight-bold" for="CBFiEthnicityNo">
                                                <input type="radio" name="CBFiEthnicity" id="CBFiEthnicityNo" class=""
                                                       value="No" <?php echo Strings::isChecked('No', $CBFiEthnicity); ?>><span></span>
                                                No
                                            </label>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td width="80%" class="font-weight-bold">Was the sex of the Co-Borrower collected on
                                        the basis of visual
                                        observation
                                        or surname?
                                    </td>
                                    <td width="20%">
                                        <div class="radio-inline">
                                            <label class="radio radio-solid font-weight-bold" for="CBFiGenderYes">
                                                <input type="radio" name="CBFiGender" id="CBFiGenderYes" class=""
                                                       value="Yes" <?php echo Strings::isChecked('Yes', $CBFiGender); ?>><span></span>
                                                Yes
                                            </label>
                                            <label class="radio radio-solid font-weight-bold" for="CBFiGenderNo">
                                                <input type="radio" name="CBFiGender" id="CBFiGenderNo" class=""
                                                       value="No" <?php echo Strings::isChecked('No', $CBFiGender); ?>><span></span>
                                                No
                                            </label>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td width="80%" class="font-weight-bold">Was the race of the Co-Borrower collected
                                        on the basis of visual
                                        observation
                                        or surname?
                                    </td>
                                    <td width="20%">
                                        <div class="radio-inline">
                                            <label class="radio radio-solid font-weight-bold" for="CBFiRaceYes">
                                                <input type="radio" name="CBFiRace" id="CBFiRaceYes" class=""
                                                       value="Yes" <?php echo Strings::isChecked('Yes', $CBFiRace); ?>>
                                                <span></span>Yes
                                            </label>
                                            <label class="radio radio-solid font-weight-bold" for="CBFiRaceNo">
                                                <input type="radio" name="CBFiRace" id="CBFiRaceNo" class=""
                                                       value="No" <?php echo Strings::isChecked('No', $CBFiRace); ?>><span></span>
                                                No
                                            </label>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        <?php } else { ?>
                            <table>
                                <tr>
                                    <td width="80%">Was the ethnicity of the Co-Borrower collected on the basis of
                                        visual
                                        observation or surname?
                                    </td>
                                    <td width="20%">
                                        <input type="radio" name="CBFiEthnicity" id="CBFiEthnicity" class="" disabled
                                               value="Yes" <?php echo Strings::isChecked('Yes', $CBFiEthnicity); ?>> Yes
                                        <input type="radio" name="CBFiEthnicity" id="CBFiEthnicity" class="" disabled
                                               value="No" <?php echo Strings::isChecked('No', $CBFiEthnicity); ?>> No
                                    </td>
                                </tr>
                                <tr>
                                    <td width="80%">Was the sex of the Co-Borrower collected on the basis of visual
                                        observation
                                        or surname?
                                    </td>
                                    <td width="20%">
                                        <input type="radio" name="CBFiGender" id="CBFiGender" class="" disabled
                                               value="Yes" <?php echo Strings::isChecked('Yes', $CBFiGender); ?>> Yes
                                        <input type="radio" name="CBFiGender" id="CBFiGender" class="" disabled
                                               value="No" <?php echo Strings::isChecked('No', $CBFiGender); ?>> No
                                    </td>
                                </tr>
                                <tr>
                                    <td width="80%">Was the race of the Co-Borrower collected on the basis of visual
                                        observation
                                        or surname?
                                    </td>
                                    <td width="20%">
                                        <input type="radio" name="CBFiRace" id="CBFiRace" class="" disabled
                                               value="Yes" <?php echo Strings::isChecked('Yes', $CBFiRace); ?>> Yes
                                        <input type="radio" name="CBFiRace" id="CBFiRace" class="" disabled
                                               value="No" <?php echo Strings::isChecked('No', $CBFiRace); ?>> No
                                    </td>
                                </tr>
                            </table>
                        <?php } ?>


                        <div class="form-group row col-lg-12 m-0 mb-4 px-0" id="cbFiTitle">
                            <label class="bg-secondary  py-4  col-lg-12"><b>The Demographic Information was
                                    provided through</b></label>
                        </div>

                        <?php if ($allowToEdit) { ?>
                            <table width="100%"
                                   class="table   table-bordered table-condensed table-sm table-vertical-center">
                                <tr>
                                    <td>
                                        <div class="radio-list">
                                            <label class="radio radio-solid font-weight-bold" for="CBDDemoInfo">
                                                <input type="radio" name="CBDDemoInfo" id="CBDDemoInfo"
                                                       value="1" <?php echo Strings::isChecked('1', $CBDDemoInfo); ?>><span></span>
                                                Face-to-Face Interview (includes Electronic Media w/ Video Component)
                                            </label>
                                            <label class="radio radio-solid font-weight-bold" for="CBDDemoInfo2">
                                                <input type="radio" name="CBDDemoInfo" id="CBDDemoInfo2"
                                                       value="2" <?php echo Strings::isChecked('2', $CBDDemoInfo); ?>><span></span>
                                                Telephone Interview
                                            </label>
                                            <label class="radio radio-solid font-weight-bold" for="CBDDemoInfo3">
                                                <input type="radio" name="CBDDemoInfo" id="CBDDemoInfo3"
                                                       value="3" <?php echo Strings::isChecked('3', $CBDDemoInfo); ?>><span></span>
                                                Fax or Mail
                                            </label>

                                            <label class="radio radio-solid font-weight-bold" for="CBDDemoInfo4">
                                                <input type="radio" name="CBDDemoInfo" id="CBDDemoInfo4"
                                                       value="4" <?php echo Strings::isChecked('4', $CBDDemoInfo); ?>><span></span>
                                                Email or Internet
                                            </label>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        <?php } else { ?>
                        <?php } ?>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>
    <?php } ?>
</div>
<?php
if ($publicUser != 1) {
    ?>
    <?php
    if ($allowToEdit) {
        ?>

        <div style="text-align: center">
            <input type="submit" class="btn btn-primary btnSave" id="saveBtn" name="btnSave" value="Save"
                   tabindex="<?php echo $tabIndexNo++; ?>">
            <input type="submit" class="btn btn-primary btnSave" d="saveNextBtn" name="btnSave" value="Save & Next"
                   tabindex="<?php echo $tabIndexNo++; ?>"
                   onclick="if(this.disabled===false) {return true;} else {return false;}">
        </div>
        <script>
            $(document).ready(function () {
                //HMDA - (Borrower)
                //If child options (Ethnicity, Race, Sex, Veteran) are selected
                // make parent (Do you wish to furnish this info) as Yes
                $('.BorChildRadio').change(function () {
                    $('.BrYes').prop('checked', true);
                });
                //HMDA - (Co-Borrower)
                //If child options (Ethnicity, Race, Sex, Veteran) are selected
                // make parent (Do you wish to furnish this info) as Yes
                $('.CoBorChildRadio').change(function () {
                    $('.CoBrYes').prop('checked', true);
                });


                $(document).on('change', '#reasonForDenial', function () {
                    let data = $(this).val();
                    let selector = $('#reasonForDenialOtherDiv');
                    selector.hide();
                    if (data.length) {
                        let valSplit = data.toString().split(",");
                        if ($.inArray('9', valSplit) !== -1) {
                            selector.show();
                        }
                    }
                });

                $(document).on('change', '.creditScoringModel', function () {
                    let attrId = $(this).attr('id');
                    let selector = $('#' + attrId + 'OtherDiv');
                    let _thisValue = $(this).val();
                    selector.removeClass('hmdaFlexShow');
                    selector.addClass('hmdaFlexHide');
                    if (_thisValue === '8') {
                        selector.removeClass('hmdaFlexHide');
                        selector.addClass('hmdaFlexShow');
                    }
                });
            });

            $('#introductoryRatePeriod').inputmask({
                mask: "999",
                placeholder: '',
            });
            $('#totalUnits').inputmask({
                mask: "9999",
                placeholder: '',
            });
            $('#combinedLoanToValueRatio').inputmask({
                mask: "9[9][9][.99]",
                placeholder: '',
            });
            /*$('#censusTract').inputmask({
                'alias': 'decimal',
                rightAlign: false,
                'groupSeparator': '.',
                'autoGroup': true,
                placeholder: ''
            });*/
            $('#borrowerAgeOfApplicant,#coBorrowerAgeOfApplicant').inputmask({
                mask: "99",
                placeholder: '',
            });
            $('#borrowerCreditScoreOfApplicant,#coBorrowerCreditScoreOfApplicant').inputmask({
                mask: "999",
                placeholder: '',
            });

            $('#propertyValue').inputmask({
                'alias': 'numeric',
                rightAlign: false,
            });

            //copied from the QAForm.js
            function showAndHideQADiv16(fldValue, clsName) {
                //disable Ethnicity/Race/Sex/Veteran for furnish this information = No or N/A
                if (fldValue === '1' || fldValue === '3') {
                    $("." + clsName).prop("checked", false);
                    //empty other fields
                    $('#bFiEthnicitySubOther').val('');
                    $('#bFiRaceAsianOther').val('');
                    $('#bFiRacePacificOther').val('');
                    //$('#Asian, #Native, #Hispanic, #HispanicPrintOriginDiv, #AsianDiv, #PacificDiv').addClass('hidden');
                }
            }

            /* for co-borrower*/
            function showAndHideQADivCB(fldValue, clsName) {
                //disable Ethnicity/Race/Sex/Veteran for furnish this information = No or N/A
                if (fldValue === '1' || fldValue === '3') {
                    $("." + clsName).prop("checked", false);
                    //empty other text fields
                    $('#CBEthnicitySubOther').val('');
                    $('#CBRaceAsianOther').val('');
                    $('#CBRacePacificOther').val('');
                    //$('#CBAsian, #CBNative, #CBHispanic, #CBHispanicPrintOriginDiv, #CBAsianDiv, #CBPacificDiv').addClass('hidden');
                }
            }
        </script>
        <?php
    }
}
?>
<style type="text/css">
    .hmdaFlexHide {
        display: none;
    }

    .hmdaFlexShow {
        display: flex;
    }

</style>

<?php
$scriptArray = [
        '/assets/js/HMDA.js',
];
Strings::includeMyScript($scriptArray);
?>
<script>
    HMDA.LMRId = '<?php echo LMRequest::$LMRId; ?>';
</script>
