<?php

use models\composite\oDrawManagement\DrawRequestManager;
use models\composite\oDrawManagement\SowTemplateManager;
use models\standard\Strings;

global $LMRId, $PCID;

Strings::includeMyCSS([
    '/backoffice/drawManagement/loanFile/css/drawSummary.css',
]);

SowTemplateManager::setUserType(SowTemplateManager::USER_LENDER);

$viewMode = DrawRequestManager::getViewMode($LMRId);
require "views/{$viewMode}-view.php";

?>
