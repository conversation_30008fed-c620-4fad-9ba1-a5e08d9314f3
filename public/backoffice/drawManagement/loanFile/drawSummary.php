<?php

use models\composite\oDrawManagement\DrawSummaryManager;
use models\composite\oDrawManagement\SowTemplateManager;
use models\composite\oDrawManagement\DrawRequestManager;

DrawSummaryManager::initialize($LMRId, $drawRequestManager);
$templateSettings = SowTemplateManager::forProcessingCompany($PCID)->getTemplate();
$isInitialSow = $drawRequestManager ? $drawRequestManager->isInitialScopeOfWork() : false;
$showAllFields = $userType == SowTemplateManager::USER_LENDER || !$isInitialSow;
?>

<div class="card card-custom card-stretch d-flex p-0 mt-4 drawSummaryCard">
    <div class="card-header card-header-tabs-line bg-gray-100">
        <div class="card-title">
            <h3 class="card-label">
                Summary
            </h3>
        </div>
        <div class="card-toolbar">
            <a href="javascript:void(0);"
                class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                data-card-tool="toggle" data-section="drawSummaryCard" data-toggle="tooltip" data-placement="top"
                title="" data-original-title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </a>
        </div>
    </div>

    <div class="card-body p-2">
        <div class="summary-section">
            <div class="col-12 summary-grid">
                <!-- Row 1: Address Information -->
                <div class="row">
                    <div class="col-md-3">
                        <div class="summary-item">
                            <div class="summary-label">Address</div>
                            <div class="summary-value"><?= htmlspecialchars(DrawSummaryManager::$address); ?></div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="summary-item">
                            <div class="summary-label">City</div>
                            <div class="summary-value"><?= htmlspecialchars(DrawSummaryManager::$city); ?></div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="summary-item">
                            <div class="summary-label">State</div>
                            <div class="summary-value"><?= htmlspecialchars(DrawSummaryManager::$state); ?></div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="summary-item">
                            <div class="summary-label">Zip</div>
                            <div class="summary-value"><?= htmlspecialchars(DrawSummaryManager::$zip); ?></div>
                        </div>
                    </div>
                </div>
                <?php if ($showAllFields): ?>
                <!-- Row 2: Loan Information -->
                <div class="row">
                    <div class="col-md-3">
                        <div class="summary-item">
                            <div class="summary-label">Initial Loan</div>
                            <div class="summary-value">$<?= number_format(DrawSummaryManager::$initialLoan); ?></div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="summary-item">
                            <div class="summary-label">Rehab Cost Financed</div>
                            <div class="summary-value">$<?= number_format(DrawSummaryManager::$rehabCostFinanced); ?></div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="summary-item">
                            <div class="summary-label">Total Loan Amount</div>
                            <div class="summary-value">$<?= number_format(DrawSummaryManager::$totalLoanAmount); ?></div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="summary-item">
                            <div class="summary-label">
                                Current Loan Balance
                                <i type="button" class="tooltipClass fas fa-info-circle text-primary" data-original-title="Balance at Funding + Total Draws Funded"></i>
                            </div>
                            </div>
                            <div class="summary-value">$<?= number_format(DrawSummaryManager::$currentLoanBalance); ?></div>
                        </div>
                    </div>
                </div>

                <!-- Row 3: Additional Loan Information -->
                <div class="row">
                    <div class="col-md-3">
                        <div class="summary-item">
                            <div class="summary-label">Rehab Cost</div>
                            <div class="summary-value">$<?= number_format(DrawSummaryManager::$rehabCost); ?></div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="summary-item">
                            <div class="summary-label">Rehab% Financed</div>
                            <div class="summary-value"><?= number_format(DrawSummaryManager::$rehabCostPercentageFinanced, 2); ?>%</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="summary-item">
                            <div class="summary-label">ARV</div>
                            <div class="summary-value">$<?= number_format(DrawSummaryManager::$arv); ?></div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="summary-item">
                            <div class="summary-label">
                                Total Draws Funded
                                <div class="popup-container mt-1 ml-2 draft-info">
                                    <i type="button" class="tooltipClass fas fa-info-circle text-primary" data-original-title="Sum total of all Amounts Approved"></i>
                                    <div class="popover">Sum total of all Amounts Approved.</div>
                                </div>
                            </div>
                            <div class="summary-value">$<?= htmlspecialchars(DrawSummaryManager::getFormattedTotalDrawsFunded()); ?></div>
                        </div>
                    </div>
                </div>

                <!-- Row 4: Date Information -->
                <div class="row">
                    <div class="col-md-3">
                        <div class="summary-item">
                            <div class="summary-label">
                                Holdback Remaining
                                <div class="popup-container mt-1 ml-2 draft-info">
                                    <i type="button" class="tooltipClass fas fa-info-circle text-primary" data-original-title="Rehab Cost Financed - Total Draws Funded + Any Adjustments to Holdback Remaining"></i>
                                    <div class="popover">Rehab Cost Financed - Total Draws Funded + Any Adjustments to Holdback Remaining.</div>
                                </div>
                            </div>
                            <div class="summary-value">$<?= htmlspecialchars(DrawSummaryManager::getFormattedHoldbackRemaining()); ?></div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="summary-item">
                            <div class="summary-label">Closing Date</div>
                            <div class="summary-value"><?= htmlspecialchars(DrawSummaryManager::$closingDate); ?></div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="summary-item">
                            <div class="summary-label">Maturity Date</div>
                            <div class="summary-value"><?= htmlspecialchars(DrawSummaryManager::$maturityDate); ?></div>
                        </div>
                    </div>
                    <?php if (!$templateSettings->enableSimpleMode): ?>
                        <div class="col-md-3">
                            <div class="summary-item">
                                <div class="summary-label">Date of Last Draw</div>
                                <div class="summary-value"><?= htmlspecialchars(DrawSummaryManager::$dateOfLastDraw); ?></div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                <?php if (!$templateSettings->enableSimpleMode): ?>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="summary-item">
                                <div class="summary-label">Date of Current Draw</div>
                                <div class="summary-value"><?= htmlspecialchars(DrawSummaryManager::$dateOfCurrentDraw); ?></div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
