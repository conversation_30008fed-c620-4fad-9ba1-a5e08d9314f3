<?php
use models\standard\Currency;
use models\standard\Strings;

// Calculate total budget and remaining budget
$totalRehabBudget = $templateData['rehabCost'] ?? 0;

// Calculate used budget from existing line items
$usedBudget = 0;
if (isset($templateData['categories']) && is_array($templateData['categories'])) {
    foreach ($templateData['categories'] as $category) {
        if (isset($category['lineItems']) && is_array($category['lineItems'])) {
            foreach ($category['lineItems'] as $lineItem) {
                $usedBudget += floatval($lineItem['cost'] ?? 0);
            }
        }
    }
}

Strings::includeMyScript([ '/backoffice/drawManagement/js/budgetSidebar.js']);

$remainingBudget = $totalRehabBudget - $usedBudget;
?>

<div class="budget-sidebar" id="budgetSidebar">
    <div class="budget-sidebar-content">
        <div class="budget-header d-flex justify-content-between align-items-center">
            <h5 class="mb-3">
                Budget Summary
            </h5>
            <button type="button" class="btn btn-sm btn-outline-secondary d-lg-none" id="closeBudgetSidebar">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="budget-item">
            <label class="budget-label">Total Rehab Budget:</label>
            <div class="budget-value total-budget">
                <?= Currency::formatDollarAmountWithDecimal($totalRehabBudget) ?>
            </div>
        </div>

        <div class="budget-item">
            <label class="budget-label">Remaining Budget:</label>
            <div class="budget-value remaining-budget" id="remainingBudgetValue">
                <?= Currency::formatDollarAmountWithDecimal($remainingBudget) ?>
            </div>
        </div>

        <div class="budget-progress mt-3">
            <div class="progress">
                <div class="progress-bar bg-primary" role="progressbar"
                     style="width: <?= $totalRehabBudget > 0 ? (($usedBudget / $totalRehabBudget) * 100) : 0 ?>%"
                     aria-valuenow="<?= $usedBudget ?>"
                     aria-valuemin="0"
                     aria-valuemax="<?= $totalRehabBudget ?>">
                </div>
            </div>
            <small class="text-muted mt-1 d-block">
                <?= $totalRehabBudget > 0 ? number_format((($usedBudget / $totalRehabBudget) * 100), 1) : 0 ?>% Used
            </small>
        </div>
    </div>
</div>
