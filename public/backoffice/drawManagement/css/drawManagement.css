.category-details {
    flex-grow: 1;
    flex-shrink: 1;
    min-width: 0;
    overflow: hidden;
    margin-right: 8px;
}

.category-details h5 {
    margin-bottom: 0.25rem;
    font-size: 1rem;
    font-weight: 600;
}
.category-details p {
    margin-bottom: 0;
    font-size: 0.875rem;
}
.actions a {
    margin-left: 0.75rem;
}
.actions a:hover {
    color: #007bff;
}

.categories-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

.category-item {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 8px;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    overflow: hidden;
    align-items: flex-start;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
              0 2px 4px -2px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.2s ease-in-out;
}

.category-item:hover, .editable-line-item:hover  {
    background-color: #f3f5f7;
}

.category-item:hover {
    cursor: move;
}

.category-item:hover {
  box-shadow: 0 8px 10px -2px rgba(0, 0, 0, 0.12), /* Increased shadow on hover */
              0 3px 6px -3px rgba(0, 0, 0, 0.12);
}

.category-item .actions {
    display: flex;
    flex-shrink: 0;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease-in-out, visibility 0s 0.2s;
}

.category-item:hover .actions {
  opacity: 1;
  visibility: visible;
  transition-delay: 0s;
}

.category-item .actions a {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.2em;
    padding: 5px;
}

.category-details .category-name {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 4px;
}

.category-details .category-description {
  display: -webkit-box;
  line-clamp: 1;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}

.category-name, .line-item-name-display {
    text-transform: capitalize;
}

.line-item-table {
    table-layout: fixed;
    width: 100%;
    border-collapse: collapse;
}

i.fas:hover {
    color: #181c32;
}
i.fa-trash-alt:hover {
    color: #dc3545;
}

@media (max-width: 768px) {
    .categories-container {
        grid-template-columns: 1fr;
    }
}

/* Stepper Styling */
.stepper-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    position: relative;
}

.stepper-container::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    border-top: 1px solid #ccc;
    z-index: 0;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    z-index: 1;
    background: white;
    padding: 0 10px;
}

.step-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #ccc;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    margin-bottom: 5px;
}

.step-label {
    font-size: 0.9em;
    color: #555;
}

.step.active .step-icon {
    background-color: #3699ff;
}

.step.active .step-label {
    font-weight: bold;
    color: #000;
}

.sortable-ghost {
    opacity: 0.4;
    background-color: #e9ecef;
    border: 2px dashed #adb5bd;
}

/* Drag handle styling */
.drag-handle {
    cursor: grab;
    opacity: 0.6;
    transition: opacity 0.2s ease;
}

.drag-handle:hover {
    opacity: 1;
    color: #181c32 !important;
}

.drag-handle:active {
    cursor: grabbing;
}

.drag-handle-cell {
    padding: 8px 4px !important;
    vertical-align: middle;
}

.add-line-item-row {
    background-color: #fffbe6;
    cursor: pointer;
}

.add-line-item-row:hover {
    background-color: #fffacd;
}

.add-line-item-row td {
    font-style: italic;
}

.line-item-category-header{
    cursor: pointer;
}

.line-item-category-header[aria-expanded="true"] .collapse-icon {
    transform: rotate(180deg);
    transition: transform 0.3s ease;
}
.line-item-category-header[aria-expanded="false"] .collapse-icon {
    transform: rotate(0deg);
    transition: transform 0.3s ease;
}

.line-item-display {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.line-item-display::after {
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    content: "\f303";
    margin-left: 10px;
    transform: translateY(-50%);
    color: #b5b5c3;
    pointer-events: none;
}

.line-item-display:not(:empty)::after {
    display: none;
}

.line-item-display:not(:empty):hover::after {
    display: inline;
}

.invalid-feedback {
    display: none;
    width: 100%;
    margin-top: .25rem;
    font-size: .875em;
}

.form-control.is-invalid ~ .invalid-feedback {
    display: block;
}

.card.card-custom>.card-header .card-title {
    margin: 0;
}

.popover {
    max-width: 300px;
    background-color: #f8f9fa;
    border: 1px solid rgba(0,0,0,.2);
    border-radius: 0.3rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0,0,0,.1);
}

.popover .arrow::before {
    border-top-color: rgba(0,0,0,.2);
}

.popover .popover-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0,0,0,.1);
    font-size: 0.875rem;
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    margin: 0;
}

.popover .popover-body {
    padding: 0.5rem 0.75rem;
    color: #212529;
    font-size: 0.875rem;
}

/* Base styling */
html {
    font-size: 12px;
}

body {
    background-color: #f8f9fa;
    font-size: 1.2rem;
}

/* Table styling */
.table th {
    font-size: 1rem;
}

.table th,
.table td {
    vertical-align: middle;
    border: none !important;
}

.table td {
    border-bottom: 1px solid #dee2e6 !important;
}

.table td:first-child {
    white-space: nowrap;
}

.table td:first-child > * {
    display: inline-block;
    vertical-align: middle;
}

tr > th {
    background: #e1f0ff;
}

.category-header td {
    font-weight: 600;
}

tr.rejected {
    background-color: #f2dede;
    border-left: 2px solid red;
    border-bottom: 2px solid #fff;
}

/* Card styling */
.card,
.line-item-table {
    -webkit-box-shadow: 0 0 30px 0 rgba(82, 63, 105, .05);
    box-shadow: 0 0 30px 0 rgba(82, 63, 105, .05);
    border: 0;
}

.card {
    padding: 0 !important;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #f1f1f1;
}

/* Typography and colors */
.text-success {
    color: #1bc5bd !important;
}

.text-muted {
    color: #ccc !important;
}

.font-weight-bold {
    font-weight: 400 !important;
}

.categories-container i,
.content-step-line-items i {
    color: #b5b5c3;
}

.line-item-display {
    font-size: 1.1rem;
    padding-left: 5px;
}

.percentage {
    padding: 0.5rem;
    font-weight: 600;
    font-size: 0.9rem;
    color: #fff;
    text-align: center;
    text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.5);
}

/* Notes and popover system */
.note-container,
.popup-container {
    position: relative;
    display: inline-block;
}

.note-btn {
    color: #3699ff;
    border: none;
    background: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
}

.note-btn[data-note=""] {
    color: #ccc !important;
}

.note-btn:hover {
    transform: scale(1.1);
}

.note-btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.popover {
    position: absolute;
    width: max-content;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    margin-left: 10px;
    background-color: #fff;
    color: #212529;
    border: 1px solid #d3d3d3;
    border-radius: 0.25rem;
    padding: 0.5rem 0.75rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    z-index: 1060;
    display: none;
    font-size: 0.875rem;
    white-space: pre-line;
}

.popover::before {
    content: "";
    position: absolute;
    top: 50%;
    right: 100%;
    transform: translateY(-50%);
    border-width: 0.4rem;
    border-style: solid;
    border-color: transparent #fff transparent transparent;
}

.popover::after {
    content: "";
    position: absolute;
    top: 50%;
    right: 100%;
    transform: translateY(-50%);
    border-width: 0.4rem;
    border-style: solid;
    border-color: transparent #d3d3d3 transparent transparent;
    margin-right: 1px;
    z-index: -1;
}

/* Show popover on hover */
.note-container:hover .popover,
.popup-container:hover .popover {
    display: block;
}

/* Hide popover if empty */
.note-container .popover:empty,
.popup-container .popover:empty {
    display: none;
}

/* Form validation */
.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.validation-message {
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Toast positioning */
#toast-container {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Hide number input spinners/steppers */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
input[type="number"] {
    -moz-appearance: textfield;
    appearance: textfield;
}

.card-toolbar {
    display: none;
}

/* Budget Sidebar Styles */
.budget-sidebar {
    position: fixed;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    z-index: 1050;
    width: 280px;
    max-height: 80vh;
    overflow-y: auto;
    display: none;
}

.budget-sidebar-content {
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
}

.budget-header h5 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
}

.budget-item {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 0.375rem;
    border-left: 4px solid #007bff;
}

.budget-label {
    font-weight: 600;
    color: #6c757d;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
    display: block;
}

.budget-value {
    font-size: 1.125rem;
    font-weight: 700;
    color: #212529;
}

.budget-value.total-budget {
    color: #007bff;
}

.budget-value.remaining-budget {
    color: #28a745;
}

.budget-value.remaining-budget.low {
    color: #dc3545;
}

.budget-progress .progress-bar.bg-danger {
    background-color: #dc3545 !important;
}

.budget-progress .progress-bar.bg-warning {
    background-color: #ffc107 !important;
}

.budget-sidebar.budget-exceeded .budget-sidebar-content {
    border-left: 4px solid #dc3545;
}

.budget-sidebar.budget-low .budget-sidebar-content {
    border-left: 4px solid #ffc107;
}

.budget-progress .progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
}

.budget-progress .progress-bar {
    border-radius: 4px;
    transition: width 0.3s ease;
}

.drag-handle {
    display: none;
    cursor: move;
    position: absolute;
    transform: translateY(-50%);
}

.editable-line-item:hover .drag-handle {
    display: block;
    color: #6c757d;
}

.tooltip .tooltip-inner {
    -webkit-box-shadow: 0 0 20px 0 rgba(0, 0, 0, .15);
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, .15);
}
.tooltip-inner {
    max-width: 200px;
    padding: .75rem 1rem;
    color: #3f4254;
    text-align: center;
    background-color: #fff;
    border-radius: 3px;
}

.tooltip .arrow {
    position: absolute;
    display: block;
    width: .8rem;
    height: .4rem;
}

.tooltip .arrow::before {
    position: absolute;
    content: "";
    border-color: transparent;
    border-style: solid;
}

/* Tooltip arrow positioning for different directions */
.tooltip.bs-tooltip-top .arrow {
    bottom: 0;
}

.tooltip.bs-tooltip-top .arrow::before {
    top: 0;
    border-width: .4rem .4rem 0;
    border-top-color: #000;
}

.tooltip.bs-tooltip-right .arrow {
    left: 0;
    width: .4rem;
    height: .8rem;
}

.tooltip.bs-tooltip-right .arrow::before {
    right: 0;
    border-width: .4rem .4rem .4rem 0;
    border-right-color: #000;
}

.tooltip.bs-tooltip-bottom .arrow {
    top: 0;
}

.tooltip.bs-tooltip-bottom .arrow::before {
    bottom: 0;
    border-width: 0 .4rem .4rem;
    border-bottom-color: #000;
}

.tooltip.bs-tooltip-left .arrow {
    right: 0;
    width: .4rem;
    height: .8rem;
}

.tooltip.bs-tooltip-left .arrow::before {
    left: 0;
    border-width: .4rem 0 .4rem .4rem;
    border-left-color: #000;
}

/* Responsive design for smaller screens */
@media (max-width: 1200px) {
    .budget-sidebar {
        position: relative;
        right: auto;
        top: auto;
        transform: none;
        width: 100%;
        margin-bottom: 2rem;
        order: -1;
    }
}

@media (max-width: 768px) {
    .budget-sidebar-content {
        padding: 1rem;
    }

    .budget-sidebar {
        width: 100%;
        margin-bottom: 1rem;
    }
}
