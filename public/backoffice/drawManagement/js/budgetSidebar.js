/**
 * Budget Sidebar Management
 * Handles dynamic updates to the budget sidebar when line items are modified
 */
class BudgetSidebar {
    constructor() {
        this.totalBudget = 0;
        this.usedBudget = 0;
        this.remainingBudget = 0;
        this.init();
    }

    init() {
        if (typeof window.templateData !== 'undefined' && window.templateData.rehabCost) {
            this.totalBudget = parseFloat(window.templateData.rehabCost) || 0;
        }
        this.setupEventListeners();
        this.calculateBudgets();
    }

    setupEventListeners() {
        $(document).on('input change', '.line-item-cost-input, .line-item-amount-input, [data-cost-field]', () => {
            this.calculateBudgets();
        });

        $(document).on('DOMNodeInserted DOMNodeRemoved', '.line-items-container, .categories-container', () => {
            setTimeout(() => this.calculateBudgets(), 100);
        });

        $(document).on('budgetUpdate', () => {
            this.calculateBudgets();
        });

        $(document).on('click', '#closeBudgetSidebar', () => {
            $('#budgetSidebar').hide();
        });

        $(document).on('click', '#showBudgetSidebar', () => {
            $('#budgetSidebar').show();
        });
    }

    calculateBudgets() {
        this.usedBudget = 0;
        this.usedBudget = $('input[name="cost"]').toArray().reduce((sum, el) => sum + (parseFloat(el.value) || 0), 0);
        this.remainingBudget = this.totalBudget - this.usedBudget;
        this.updateDisplay();
    }

    updateDisplay() {
        const remainingElement = $('#remainingBudgetValue');
        const sidebarElement = $('#budgetSidebar');

        if (remainingElement.length) {
            remainingElement.text(this.formatCurrency(this.remainingBudget));

            remainingElement.removeClass('low');
            sidebarElement.removeClass('budget-exceeded budget-low');

            if (this.remainingBudget < 0) {
                remainingElement.addClass('low');
                sidebarElement.addClass('budget-exceeded');
            } else if (this.remainingBudget < (this.totalBudget * 0.1)) {
                sidebarElement.addClass('budget-low');
            }
        }

        const progressBar = $('.budget-sidebar .progress-bar');
        if (progressBar.length && this.totalBudget > 0) {
            const percentage = (this.usedBudget / this.totalBudget) * 100;
            const cappedPercentage = Math.min(percentage, 100);

            progressBar.css('width', cappedPercentage + '%');
            progressBar.attr('aria-valuenow', this.usedBudget);

            progressBar.removeClass('bg-primary bg-warning bg-danger');
            if (percentage > 100) {
                progressBar.addClass('bg-danger');
            } else if (percentage === 100) {
                progressBar.addClass('bg-success');
            } else if (percentage > 90) {
                progressBar.addClass('bg-warning');
            } else {
                progressBar.addClass('bg-primary');
            }

            const percentageText = progressBar.closest('.budget-progress').find('small');
            percentageText.text(percentage.toFixed(1) + '% Used');
        }

        $(document).trigger('budgetDisplayUpdated', {
            total: this.totalBudget,
            used: this.usedBudget,
            remaining: this.remainingBudget
        });
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount);
    }

    // Public methods for external updates
    updateTotalBudget(newTotal) {
        this.totalBudget = parseFloat(newTotal) || 0;
        this.calculateBudgets();
    }

    addToBudget(amount) {
        this.usedBudget += parseFloat(amount) || 0;
        this.updateDisplay();
    }

    removeFromBudget(amount) {
        this.usedBudget -= parseFloat(amount) || 0;
        this.updateDisplay();
    }

    refresh() {
        this.calculateBudgets();
    }
}

$(document).ready(function() {
    if ($('#budgetSidebar').length > 0) {
        window.budgetSidebar = new BudgetSidebar();
    }
});
