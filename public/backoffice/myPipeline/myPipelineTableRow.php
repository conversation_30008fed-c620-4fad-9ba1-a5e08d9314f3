<?php

use models\composite\proposalFormula;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\Controllers\backoffice\myPipeline\myPipelineRow;
use models\Controllers\backoffice\myPipelineColumns;
use models\cypher;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\Currency;
use models\standard\Strings;

global $rowCls, $r, $fileType, $stateArray, $allowToCFPBSubmitForPC,
       $allowCFPBAuditing, $executiveId, $brokerNumb, $userSeeBilling,
       $allowEmailCampaign, $sendMarketingEmail, $recNumbStart,
       $PCID, $glShowMoneyIconForPaidStatus, $userGroup, $PCModuleInfoKeys,
       $activeFile, $clsColor, $userRole, $borrowerLink, $myPipelineRow, $allowToAccessInternalLoanProgram;

/* @var models\Controllers\backoffice\myPipeline\myPipelineRow $myPipelineRow */

?>

<tr class="<?php echo $rowCls ?> " id="row-<?php echo $r ?>">
    <td
            data-title="row1td1"
            class="text-center countMyPipelineTable">
        <span id="sno-<?php echo $r ?>"
              class="font-weight-bold px-2"><?php echo myPipelineRow::$serialNo + 1 + $recNumbStart; ?></span>
    </td>

    <?php if ($fileType != 4 && $fileType != 'LA') { ?>
        <td data-title="row1td2"
            id="td-folder-<?php echo $r ?>"
            class="accordion-headerClick"
            data-fileidlmr="<?php echo cypher::myEncryption($myPipelineRow->LMRId) ?>">
            <div id="op-row-<?php echo $r ?>">&nbsp;</div>
            <a class="text-danger tooltipClass folderIconClass"
               id="<?php echo cypher::myEncryption($myPipelineRow->LMRId) ?>"
               data-lmrid="<?php echo $myPipelineRow->LMRId; ?>"
            >
                <i class="far fa-folder  icon-2x text-success  mar_for_icon"
                   title="Click to Show/ Hide more info"
                   id="<?php echo 'Folder_Icon_' . cypher::myEncryption($myPipelineRow->LMRId) ?>"></i></a>
        </td>
    <?php } ?>

    <td data-title="row1td3">
        <div class="">
            <?php require __DIR__ . '/myLinksMain.php'; ?>
        </div>
    </td>

    <td class="tdClassNamePropAddress" title="Name & Prop. Addr" data-title="row1td44">
        <div class="<?php echo $clsColor ?>"

            <?php if (($fileType == 2 && $userRole == 'Super')
                || (($fileType == 4) && $userRole == 'Super')
                || $userRole == 'REST') { ?> style="width: 160px;" <?php } ?> >
            <?php echo $myPipelineRow->borrowerLink;

            if ($userRole == 'REST' || ($fileType == 2 && ($userRole == 'Super' || $userRole == 'Sales')) || (($fileType == 4 || $fileType == 'LA') && ($userRole == 'Super' || $userRole == 'Sales'))) {
                echo '<br>' . $myPipelineRow->borPhone;
            }
            ?></div>
    </td>

    <?php if (myPipelineColumns::isColumnActive('fileId')) { ?>
        <td class="tdClassFileId" data-title="row1td4" id="fileId_<?php echo myPipelineRow::$serialNo + 1 ?>">
            <div><?php echo $myPipelineRow->LMRId; ?></div>
        </td>
    <?php }
    if (myPipelineColumns::isColumnActive('recordDate') || $fileType == 2 || $fileType == 4 || $fileType == 'LA' || $userRole == 'REST') { ?>
        <td class="tdClassRecordDate" data-title="row1td5" id="recordDate_<?php echo myPipelineRow::$serialNo + 1 ?>">
            <div><?php
                if ($userRole == 'REST' || $fileType == 2 || $fileType == 4 || $fileType == 'LA') {
                    echo $myPipelineRow->SubmissionDate;
                } else {
                    echo $myPipelineRow->recordDate;
                }
                ?></div>
        </td>
    <?php }

    if (myPipelineColumns::isColumnActive('MERSID')) {
        echo myPipelineColumns::showColumnValue('MERSID', $myPipelineRow->MERSID, myPipelineRow::$serialNo);
    }

    if (myPipelineColumns::isColumnActive('createdDateWithTimestamp')) {
        echo myPipelineColumns::showColumnValue('createdDateWithTimestamp', $myPipelineRow->createdDateWithTimestamp, myPipelineRow::$serialNo);
    }

    if (myPipelineColumns::isColumnActive('lastUpdatedDate')) {
        echo myPipelineColumns::showColumnValue('lastUpdatedDate', $myPipelineRow->lastUpdatedDate, myPipelineRow::$serialNo);
    }

    if (myPipelineColumns::isColumnActive('entityName')) {
        echo myPipelineColumns::showColumnValue('entityName', $myPipelineRow->entityLink, myPipelineRow::$serialNo, 'Entity Name');
    }

    if (myPipelineColumns::isColumnActive('phoneNumber') || $fileType == 2 || $fileType == 4) {
        echo myPipelineColumns::showColumnValue('phoneNumber', '<nobr>' . str_replace('Ext', '<br/>Ext: ', $myPipelineRow->borPhone) . '</nobr>', myPipelineRow::$serialNo, 'Borrower Phone Number');
    }

    if (myPipelineColumns::isColumnActive('cellNumber')) {
        echo myPipelineColumns::showColumnValue('cellNumber', str_replace('Ext', '<br/>Ext: ', $myPipelineRow->borrowercellNumber), myPipelineRow::$serialNo, 'Borrower Cell Number');
    }

    if (myPipelineColumns::isColumnActive('workNumber')) {
        echo myPipelineColumns::showColumnValue('workNumber', $myPipelineRow->workNumber, myPipelineRow::$serialNo, 'Borrower Work Number');
    }

    if (myPipelineColumns::isColumnActive('borrowerEmail')) {
        echo myPipelineColumns::showColumnValue('borrowerEmail', '<a href="mailto:' . $myPipelineRow->borrowerEmail . '">' . $myPipelineRow->borrowerEmail . '</a>', myPipelineRow::$serialNo, 'Borrower Email');
    }

    if (myPipelineColumns::isColumnActive('city')) {
        echo myPipelineColumns::showColumnValue('city', $myPipelineRow->presentCity, myPipelineRow::$serialNo, 'Borrower City');
    }

    if (myPipelineColumns::isColumnActive('state')) {
        echo myPipelineColumns::showColumnValue('state', $myPipelineRow->presentState, myPipelineRow::$serialNo, 'Borrower State');
    }

    if (myPipelineColumns::isColumnActive('assetTotalCashBankAcc')) {
        echo myPipelineColumns::showColumnValue('assetTotalCashBankAcc', Currency::formatDollarAmountWithDecimal($myPipelineRow->assetTotalCashBankAcc), myPipelineRow::$serialNo, 'Cash In Bank');
    }

    if (myPipelineColumns::isColumnActive('primaryStatus') || $fileType == 2 || $fileType == 4 || $fileType == 'LA' || $fileType == 'CFPB' || $userRole == 'REST') {
        echo myPipelineColumns::showColumnValue('primaryStatus', '
            <div class="d-flex justify-content-start">
                <span class="d-inline mr-2">' . $myPipelineRow->statusLink . '</span>
                <span class="d-inline">' . wordwrap($myPipelineRow->filePrimaryStatus, 15, '<br>', 1) . '</span>
            </div>
        ', myPipelineRow::$serialNo, 'Primary Status');
    }

    if (myPipelineColumns::isColumnActive('subStatus')) {
        echo myPipelineColumns::showColumnValue('subStatus', $myPipelineRow->subStatus, myPipelineRow::$serialNo, 'Sub-Status');
    }
    if (myPipelineColumns::isColumnActive('LMRInternalLoanProgram') &&
        ($userGroup == 'Employee' || ($userGroup == 'Agent' && $allowToAccessInternalLoanProgram))) {
        echo myPipelineColumns::showColumnValue('LMRInternalLoanProgram', $myPipelineRow->LMRInternalLoanProgram, myPipelineRow::$serialNo, 'Internal Loan Program');
    }
    if (myPipelineColumns::isColumnActive('seviceTypes')) {
        echo myPipelineColumns::showColumnValue('seviceTypes', $myPipelineRow->serviceReq, myPipelineRow::$serialNo);
    }
    if (myPipelineColumns::isColumnActive('propertyType')) {
        echo myPipelineColumns::showColumnValue('propertyType', $myPipelineRow->propertyType, myPipelineRow::$serialNo, 'Property Type');
    }
    if (myPipelineColumns::isColumnActive('propertyCity')) {
        echo myPipelineColumns::showColumnValue('propertyCity', $myPipelineRow->propertyCity, myPipelineRow::$serialNo, 'Property City');
    }
    if (myPipelineColumns::isColumnActive('propertyState')) {
        echo myPipelineColumns::showColumnValue('propertyState', $myPipelineRow->propertyState ? Strings::getStateFullName($stateArray, $myPipelineRow->propertyState) : '', myPipelineRow::$serialNo, 'Property State');
    }
    if (myPipelineColumns::isColumnActive('desiredClosingDate')) {
        echo myPipelineColumns::showColumnValue('desiredClosingDate', $myPipelineRow->desiredClosingDate, myPipelineRow::$serialNo, 'Desired Closing Date');
    }

    if (myPipelineColumns::isColumnActive('hearingDate')) {
        echo myPipelineColumns::showColumnValue('hearingDate', $myPipelineRow->hearingDate, myPipelineRow::$serialNo, 'Hearing Date');
    }

    if (myPipelineColumns::isColumnActive('CFPBSubmittedDate') || $fileType == 'CFPB' || $userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager') {
        echo myPipelineColumns::showColumnValue('CFPBSubmittedDate', $myPipelineRow->CFPBSubmittedDate, myPipelineRow::$serialNo);
    }
    $textColour = '';
    if (myPipelineColumns::isColumnActive('salesDate') && (array_key_exists('LM', myPipelineColumns::$PCModuleInfo) || array_key_exists('SS', myPipelineColumns::$PCModuleInfo))) {
        if (glCustomJobForProcessingCompany::colorSalesdate($PCID)) {
            $textColour = " style='color: red;'";
        }
        echo myPipelineColumns::showColumnValue('salesDate', $myPipelineRow->salesDate, myPipelineRow::$serialNo, 'Sales Date', $textColour);
    }
    if (myPipelineColumns::isColumnActive('disclosureSentDate')) {
        echo myPipelineColumns::showColumnValue('disclosureSentDate', $myPipelineRow->disclosureSentDate, myPipelineRow::$serialNo, 'Disclosure Sent Date');
    }
    if (myPipelineColumns::isColumnActive('receivedDate')) {
        echo myPipelineColumns::showColumnValue('receivedDate', $myPipelineRow->receivedDate, myPipelineRow::$serialNo, 'Received Date');
    }
    if (myPipelineColumns::isColumnActive('lenderSubmission')) {
        echo myPipelineColumns::showColumnValue('lenderSubmission', $myPipelineRow->lenderSubmissionDate, myPipelineRow::$serialNo, 'Lender Submission Date');
    }
    if (myPipelineColumns::isColumnActive('closedDate')) {
        echo myPipelineColumns::showColumnValue('closedDate', $myPipelineRow->closedDate, myPipelineRow::$serialNo, 'Closed Date');
    }
    if (myPipelineColumns::isColumnActive('closingDate')) {
        echo myPipelineColumns::showColumnValue('closingDate', $myPipelineRow->closingDate, myPipelineRow::$serialNo, 'Closing Date');
    }
    if (myPipelineColumns::isColumnActive('targetClosingDate')) {
        echo myPipelineColumns::showColumnValue('targetClosingDate', $myPipelineRow->targetClosingDate, myPipelineRow::$serialNo, 'Target Closing Date');
    }
    if (myPipelineColumns::isColumnActive('borrowerCallBack')) {
        echo myPipelineColumns::showColumnValue('borrowerCallBack', $myPipelineRow->borrowerCallBack, myPipelineRow::$serialNo, 'Client Call Back Date');
    }
    if (myPipelineColumns::isColumnActive('lenderCallBack')) {
        echo myPipelineColumns::showColumnValue('lenderCallBack', $myPipelineRow->lenderCallBack, myPipelineRow::$serialNo, 'Lender Call Back');
    }
    if (myPipelineColumns::isColumnActive('HAFADate')) {
        echo myPipelineColumns::showColumnValue('HAFADate', $myPipelineRow->HAFADate, myPipelineRow::$serialNo, 'HAFA Date');
    }

    if (!($userRole == 'REST' || ($fileType == 2 && ($userRole == 'Super' || $userRole == 'Sales')) || (($fileType == 4 || $fileType == 'LA') && ($userRole == 'Super' || $userRole == 'Sales')))) {
        if ($fileType == 2) {
            echo myPipelineColumns::showColumnValue('orderStatus', $myPipelineRow->orderStatus, myPipelineRow::$serialNo, 'Order Status');
            echo myPipelineColumns::showColumnValue('paymentStatus', $myPipelineRow->paymentStatus, myPipelineRow::$serialNo, 'Payment Status');
        }
    }

    if ($fileType == 'LA') {
        echo myPipelineColumns::showColumnValue('LMRId', $myPipelineRow->LMRId, myPipelineRow::$serialNo);
    }

    if ($fileType == 4) {
        echo myPipelineColumns::showColumnValue('LMRId', $myPipelineRow->LMRId, myPipelineRow::$serialNo);
    } elseif ($userRole == 'REST' || ($fileType == 2 && ($userRole == 'Super' || $userRole == 'Sales'))) {
        echo myPipelineColumns::showColumnValue('CaseFileId', trim($myPipelineRow->tempRespArray['CaseFileId']), myPipelineRow::$serialNo, 'Case #');
    } else {
        if (myPipelineColumns::isColumnActive('fileVelocity') || $userRole == 'Auditor') { ?>
            <td class="tdClassDaysInPrimaryFileStatus" id="fileVelocity_<?php echo myPipelineRow::$serialNo + 1 ?>"
                title="Days in Primary File Status" data-title="row1td23"
            >
                <div class="d-flex justify-content-center">

                    <span
                            class="btn btn-sm btn-secondary btn-text-primary btn-hover-primary btn-icon mr-4"><?php echo $myPipelineRow->fileVelocity ?></span>

                    <?php
                    $iniialSt = 0;
                    $processorCommentsNotes = '<tr>
                <th>Status</th><th>No Of Days</th> </tr>';

                    foreach ($myPipelineRow->thisFileStatusHistory as $k => $v) {
                        $processorCommentsNotes .= '<tr><td>' . htmlspecialchars($v['primaryStatus'], ENT_QUOTES) . '</td><td>';
                        if ($iniialSt == 0) {
                            $processorCommentsNotes .= $myPipelineRow->fileVelocity;
                        } else {
                            $processorCommentsNotes .= $v['noOfDays'];
                        }
                        $processorCommentsNotes .= '</td></tr>';
                        $iniialSt++;
                    }

                    $processorCommentsNotesList = '<table class="table rounded table-striped">' . ($processorCommentsNotes) . '</table>';
                    $processorCommentsNotesFinal = '<div class="card card-custom">
                                <div class="card-header">
                                    <div class="card-title"><h3 class="card-label">File Status Changed History</h3></div>
                                </div>
                                <div class="card-body p-0">' . $processorCommentsNotesList . '</div>
                            </div>'; ?>
                    <span
                            class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon  manualPopover "
                            data-html="true"
                            data-trigger="focus"
                            data-placement="left"
                            data-content='<?php echo($processorCommentsNotesFinal); ?>'> <i
                                class="icon-md fa fa-history text-success"></i>
                            </span>
                </div>
            </td>
        <?php }

        if (myPipelineColumns::isColumnActive('HMLOTotalLoanAmt')) {
            echo myPipelineColumns::showColumnValue('HMLOTotalLoanAmt', Currency::formatDollarAmountWithDecimal($myPipelineRow->totalLoanAmount), myPipelineRow::$serialNo, 'Total Loan Amount');
        }
        if (myPipelineColumns::isColumnActive('initialLoanAmount')) {
            echo myPipelineColumns::showColumnValue('initialLoanAmount', Currency::formatDollarAmountWithDecimal($myPipelineRow->initialLoanAmount), myPipelineRow::$serialNo, 'Initial Loan Amount');
        }
        if (myPipelineColumns::isColumnActive('brokerProcessingFee')) {
            echo myPipelineColumns::showColumnValue('brokerProcessingFee', Currency::formatDollarAmountWithDecimal($myPipelineRow->brokerProcessingFee), myPipelineRow::$serialNo, 'Broker Processing Fee');
        }
        if (myPipelineColumns::isColumnActive('yieldSpread')) {
            echo myPipelineColumns::showColumnValue('yieldSpread', $myPipelineRow->yieldSpread, myPipelineRow::$serialNo, 'Yield Spread');
        }

        if (myPipelineColumns::isColumnActive('costOfCapital')) {
            echo myPipelineColumns::showColumnValue('costOfCapital', $myPipelineRow->costOfCapital, myPipelineRow::$serialNo, 'Cost Of Capital');
        }
        if (myPipelineColumns::isColumnActive('monthlyLoanAmount')) {
            echo myPipelineColumns::showColumnValue('monthlyLoanAmount', Currency::formatDollarAmountWithDecimal($myPipelineRow->payrollLoanAmount), myPipelineRow::$serialNo, 'Payroll Loan');
        }
        if (myPipelineColumns::isColumnActive('loanAuditProduct') || $userRole == 'Auditor' || $fileType == 'LA') {
            echo myPipelineColumns::showColumnValue('loanAuditProduct', $myPipelineRow->loanAuditProduct, myPipelineRow::$serialNo, 'Loan Audit Product');
        }
        if (myPipelineColumns::isColumnActive('fraudLevel') || $userRole == 'Auditor') {
            echo myPipelineColumns::showColumnValue('fraudLevel', $myPipelineRow->fraudLevel, myPipelineRow::$serialNo, 'Fraud Level Warning');
        }
    }

    if ($userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager' || ($fileType == 'CFPB' && $allowCFPBAuditing == 1) || ($fileType == 'CFPB' && $userRole == 'Super')) {
        echo myPipelineColumns::showColumnValue('CFPBAuditSubmitterInfo', '
            ' . (trim($myPipelineRow->CFPBAuditSubmitterInfo) ? '<i class="fa fa-info-circle fa-2x" title="' . htmlentities($myPipelineRow->CFPBAuditSubmitterInfo) . '">&nbsp;</i>' : '') . '
            <div>' . $myPipelineRow->CFPBAuditSubmittedBy . '</div>
        ', myPipelineRow::$serialNo, 'Submitted By');
    }

    if (myPipelineColumns::isColumnActive('entity')) {
        echo myPipelineColumns::showColumnValue('entity', $myPipelineRow->entityInfo, myPipelineRow::$serialNo, 'Entity Info');
    }

    if (myPipelineColumns::isColumnActive('entityType')) {
        echo myPipelineColumns::showColumnValue('entityType', $myPipelineRow->entityType, myPipelineRow::$serialNo, 'Entity Type');
    }

    if (myPipelineColumns::isColumnActive('stateOfFormation')) {
        echo myPipelineColumns::showColumnValue('stateOfFormation', $myPipelineRow->entityStateOfFormation, myPipelineRow::$serialNo, 'State Of Formation');
    }

    if (myPipelineColumns::isColumnActive('entityState')) {
        echo myPipelineColumns::showColumnValue('entityState', $myPipelineRow->entityState, myPipelineRow::$serialNo, 'Entity State');
    }

    if (myPipelineColumns::isColumnActive('entityCity')) {
        echo myPipelineColumns::showColumnValue('entityCity', $myPipelineRow->entityCity, myPipelineRow::$serialNo, 'Entity City');
    }

    if (myPipelineColumns::isColumnActive('propertyOwnership')) {
        echo myPipelineColumns::showColumnValue('propertyOwnership', $myPipelineRow->entityPropertyOwnerShip, myPipelineRow::$serialNo, 'Prop. Ownership');
    }

    if (myPipelineColumns::isColumnActive('ofEmployees')) {
        echo myPipelineColumns::showColumnValue('ofEmployees', $myPipelineRow->noOfEmployees, myPipelineRow::$serialNo, '# Of Employees');
    }

    if (myPipelineColumns::isColumnActive('avgMonthlyCreditCardSales')) {
        echo myPipelineColumns::showColumnValue('avgMonthlyCreditCardSales', $myPipelineRow->avgMonthlyCreditcardSale, myPipelineRow::$serialNo, 'Avg Monthly Credit Card Sales');
    }

    if (myPipelineColumns::isColumnActive('avgTotalMonthlySales')) {
        echo myPipelineColumns::showColumnValue('avgTotalMonthlySales', $myPipelineRow->avgTotalMonthlySale, myPipelineRow::$serialNo, 'Avg Total Monthly Sales');
    }

    if (myPipelineColumns::isColumnActive('annualGrossProfit')) {
        echo myPipelineColumns::showColumnValue('annualGrossProfit', Currency::formatDollarAmountWithDecimal($myPipelineRow->annualGrossProfit), myPipelineRow::$serialNo, 'Annual Gross Profit');
    }

    if (myPipelineColumns::isColumnActive('netBusinessIncome')) {
        echo myPipelineColumns::showColumnValue('netBusinessIncome', Currency::formatDollarAmountWithDecimal($myPipelineRow->ordinaryBusinessIncome), myPipelineRow::$serialNo, 'Net Business Income');
    }

    if (myPipelineColumns::isColumnActive('grossSocialSecurity')) {
        echo myPipelineColumns::showColumnValue('grossSocialSecurity', Currency::formatDollarAmountWithDecimal($myPipelineRow->grossSocialSecurity), myPipelineRow::$serialNo, 'Gross SSI');
    }

    if (myPipelineColumns::isColumnActive('totalGrossIncome')) {
        echo myPipelineColumns::showColumnValue('totalGrossIncome', Currency::formatDollarAmountWithDecimal($myPipelineRow->totalGrossMonthlyHouseHoldIncome), myPipelineRow::$serialNo, 'Total Gross Income');
    }

    if (myPipelineColumns::isColumnActive('totalNetIncome')) {
        echo myPipelineColumns::showColumnValue('totalNetIncome', $myPipelineRow->totalHouseHoldIncome, myPipelineRow::$serialNo, 'Total Net Income');
    }

    if (myPipelineColumns::isColumnActive('totalExpenses')) {
        echo myPipelineColumns::showColumnValue('totalExpenses', Currency::formatDollarAmountWithDecimal($myPipelineRow->totalHouseHoldExpenses), myPipelineRow::$serialNo, 'Total Expenses');
    }

    if (myPipelineColumns::isColumnActive('disposableIncome')) {
        echo myPipelineColumns::showColumnValue('disposableIncome', Currency::formatDollarAmountWithDecimal($myPipelineRow->disposableIncome), myPipelineRow::$serialNo, 'Disposable Income');
    }

    if (myPipelineColumns::isColumnActive('currentDTI')) {
        echo myPipelineColumns::showColumnValue('currentDTI', $myPipelineRow->lien1DTI . '%', myPipelineRow::$serialNo, 'Current DTI');
    }

    if (myPipelineColumns::isColumnActive('totalAssets')) {
        echo myPipelineColumns::showColumnValue('totalAssets', Currency::formatDollarAmountWithDecimal($myPipelineRow->totalAssets), myPipelineRow::$serialNo, 'Total Assets');
    }

    if (myPipelineColumns::isColumnActive('servicer1') && (array_key_exists('LM', myPipelineColumns::$PCModuleInfo) || array_key_exists('SS', myPipelineColumns::$PCModuleInfo))) {
        echo myPipelineColumns::showColumnValue('servicer1', '
        ' . (trim($myPipelineRow->lender1Info) ? '<div class="pad2 with-children-tip"><i class="fa fa-info-circle fa-2x" title="' . htmlentities($myPipelineRow->executiveIdlender1Info) . '"></i></div>' : '') . '
        <div>' . $myPipelineRow->lender1Name . '</div>
        ', myPipelineRow::$serialNo);
    }

    if (myPipelineColumns::isColumnActive('servicer2') || $fileType == 2 || $fileType == 4 || $userRole == 'REST') {
        echo myPipelineColumns::showColumnValue('servicer2', $myPipelineRow->lender2Name, myPipelineRow::$serialNo, '2nd Lien Servicer');
    }

    if (myPipelineColumns::isColumnActive('mortgageOwner1')) {
        echo myPipelineColumns::showColumnValue('mortgageOwner1', $myPipelineRow->mortgageOwner1, myPipelineRow::$serialNo, 'Mortgage Type');
    }

    if (myPipelineColumns::isColumnActive('mortgageOwner2')) {
        echo myPipelineColumns::showColumnValue('mortgageOwner2', $myPipelineRow->mortgageOwner2, myPipelineRow::$serialNo, '2nd Lien Mortgage Type');
    }

    if (myPipelineColumns::isColumnActive('loanType')) {
        echo myPipelineColumns::showColumnValue('loanType', $myPipelineRow->loanType, myPipelineRow::$serialNo, 'Loan Type');
    }

    if (myPipelineColumns::isColumnActive('loanType2')) {
        echo myPipelineColumns::showColumnValue('loanType2', $myPipelineRow->loanType2, myPipelineRow::$serialNo, '2nd Lien Loan Type');
    }

    if (myPipelineColumns::isColumnActive('currentBalance')) {
        echo myPipelineColumns::showColumnValue('currentBalance', $myPipelineRow->lien1Amount, myPipelineRow::$serialNo, 'Current Balance');
    }

    if (myPipelineColumns::isColumnActive('currentBalance2')) {
        echo myPipelineColumns::showColumnValue('currentBalance2', $myPipelineRow->lien2Amount, myPipelineRow::$serialNo, 'Lien Current Balance');
    }

    if (myPipelineColumns::isColumnActive('loanNumber')) {
        echo myPipelineColumns::showColumnValue('loanNumber', $myPipelineRow->loanNumber, myPipelineRow::$serialNo, 'Loan #');
    }

    if (myPipelineColumns::isColumnActive('loanNumber2')) {
        echo myPipelineColumns::showColumnValue('loanNumber2', $myPipelineRow->loanNumber2, myPipelineRow::$serialNo, '2nd Lien Loan #');
    }

    if (myPipelineColumns::isColumnActive('lien1Rate')) {
        echo myPipelineColumns::showColumnValue('lien1Rate', proposalFormula::convertToAbsoluteValueWithNewFormatForPercent($myPipelineRow->lien1Rate), myPipelineRow::$serialNo, 'Rate');
    }

    if (myPipelineColumns::isColumnActive('lien2Rate')) {
        echo myPipelineColumns::showColumnValue('lien2Rate', proposalFormula::convertToAbsoluteValueWithNewFormatForPercent($myPipelineRow->lien2Rate), myPipelineRow::$serialNo, '2nd Lien Rate');
    }

    if (myPipelineColumns::isColumnActive('lien1Payment')) {
        echo myPipelineColumns::showColumnValue('lien1Payment', $myPipelineRow->lien1Payment, myPipelineRow::$serialNo, 'Payment- P+I');
    }

    if (myPipelineColumns::isColumnActive('lien2Payment')) {
        echo myPipelineColumns::showColumnValue('lien2Payment', $myPipelineRow->lien2Payment, myPipelineRow::$serialNo, 'Lien Payment');
    }

    if (myPipelineColumns::isColumnActive('PITIA')) {
        echo myPipelineColumns::showColumnValue('PITIA', $myPipelineRow->tempLien1PITIA, myPipelineRow::$serialNo, 'PITIA');
    }

    if (myPipelineColumns::isColumnActive('lien1LPMade')) {
        echo myPipelineColumns::showColumnValue('lien1LPMade', $myPipelineRow->lien1LPMade, myPipelineRow::$serialNo, 'Last Payment Made');
    }

    if (myPipelineColumns::isColumnActive('lien2LPMade')) {
        echo myPipelineColumns::showColumnValue('lien2LPMade', $myPipelineRow->lien2LPMade, myPipelineRow::$serialNo, 'Lien Last Payment Made');
    }

    if (myPipelineColumns::isColumnActive('noOfMonthsBehind1') && (array_key_exists('LM', myPipelineColumns::$PCModuleInfo) || array_key_exists('SS', myPipelineColumns::$PCModuleInfo))) {
        echo myPipelineColumns::showColumnValue('noOfMonthsBehind1', $myPipelineRow->noOfMonthsBehind1, myPipelineRow::$serialNo, '# Months Behind');
    }

    if (myPipelineColumns::isColumnActive('noOfMonthsBehind2')) {
        echo myPipelineColumns::showColumnValue('noOfMonthsBehind2', $myPipelineRow->noOfMonthsBehind2, myPipelineRow::$serialNo, '2nd Lien # Months Behind');
    }

    if (myPipelineColumns::isColumnActive('lien1BalanceDue')) {
        echo myPipelineColumns::showColumnValue('lien1BalanceDue', $myPipelineRow->lien1BalanceDue, myPipelineRow::$serialNo, 'Past Due');
    }

    if (myPipelineColumns::isColumnActive('loanOriginationDate')) {
        echo myPipelineColumns::showColumnValue('loanOriginationDate', $myPipelineRow->loanOriginationDate, myPipelineRow::$serialNo, 'Loan Origination Date');
    }

    if (myPipelineColumns::isColumnActive('lien2BalanceDue')) {
        echo myPipelineColumns::showColumnValue('lien2BalanceDue', $myPipelineRow->lien2BalanceDue, myPipelineRow::$serialNo, '2nd Lien Past Due');
    }

    if (myPipelineColumns::isColumnActive('occupancy')) {
        echo myPipelineColumns::showColumnValue('occupancy', $myPipelineRow->occupancy, myPipelineRow::$serialNo, 'Occupancy');
    }

    if (myPipelineColumns::isColumnActive('homeValue')) {
        echo myPipelineColumns::showColumnValue('homeValue', $myPipelineRow->homeValue, myPipelineRow::$serialNo, 'Property Value (As-Is)');
    }

    if (myPipelineColumns::isColumnActive('costBasis')) {
        echo myPipelineColumns::showColumnValue('costBasis', $myPipelineRow->costBasis, myPipelineRow::$serialNo, 'Cost Basis');
    }

    if (myPipelineColumns::isColumnActive('appraiser1')) {
        echo myPipelineColumns::showColumnValue('appraiser1', $myPipelineRow->appraiser1, myPipelineRow::$serialNo, 'Appraisal 1');
    }

    if (myPipelineColumns::isColumnActive('BPO1')) {
        echo myPipelineColumns::showColumnValue('BPO1', $myPipelineRow->BPO1, myPipelineRow::$serialNo);
    }

    if (myPipelineColumns::isColumnActive('rehabValue')) {
        echo myPipelineColumns::showColumnValue('rehabValue', $myPipelineRow->rehabValue, myPipelineRow::$serialNo, 'Rehabbed Value');
    }

    if (myPipelineColumns::isColumnActive('bedrooms')) {
        echo myPipelineColumns::showColumnValue('bedrooms', $myPipelineRow->bedrooms, myPipelineRow::$serialNo, '# of Bedrooms');
    }

    if (myPipelineColumns::isColumnActive('bathrooms')) {
        echo myPipelineColumns::showColumnValue('bathrooms', $myPipelineRow->bathrooms, myPipelineRow::$serialNo, '# of Baths');
    }

    if (myPipelineColumns::isColumnActive('yearBuilt')) {
        echo myPipelineColumns::showColumnValue('yearBuilt', $myPipelineRow->yearBuilt, myPipelineRow::$serialNo, 'Year Built');
    }

    if (myPipelineColumns::isColumnActive('fileNumber')) {
        echo myPipelineColumns::showColumnValue('HMLO', $myPipelineRow->fileNumber, myPipelineRow::$serialNo, 'File #');
    }

    // assignedEmployee - used to be clickable, sort out later - 2023-02-03 BDK
    //            <span
    //                data-toggle="tooltip"
    //                data-html="true"
    //                title="' . htmlentities('<table class="table table-hover  table-bordered table-condensed table-sm table-vertical-center">' . base64_decode($myPipelineRow->assignedEmpInfo) . '</table>') . '"
    //                data-original-title="' . htmlentities('<table class="table table-hover  table-bordered table-condensed table-sm table-vertical-center">' . base64_decode($myPipelineRow->assignedEmpInfo) . '</table>') . '"
    //            ><a
    //                class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1"
    //                data-href="/pops/assignEmployee.php"
    //                data-name=""
    //                data-id="LMRId=cf022beaf6846c1f&amp;PCID=a021e187c53f86cf&amp;BRID=c195b49e7a99c1a2"
    //                data-toggle="modal"
    //                data-target="#exampleModal1"
    //            ><i class="fas fa-users-cog"></i></a>
    //        </span>

    if (myPipelineColumns::isColumnActive('assignedEmployee')) {
        echo myPipelineColumns::showColumnValue('assignedEmployee', '
        <span
           class="manualPopover btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1"
           data-title="Assigned Employee(s)"
           data-content="' . htmlentities('<table class="table table-hover  table-bordered table-condensed table-sm table-vertical-center">' . base64_decode($myPipelineRow->assignedEmpInfo) . '</table>') . '"
           data-html="true">
           <i class="fas fa-users-cog"></i>
       </span>
                               
        ', myPipelineRow::$serialNo, 'Assigned Employee');
    }
    if (myPipelineColumns::isColumnActive('PCName')) {
        echo myPipelineColumns::showColumnValue('PCName',
            ($userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager' || ($fileType == 'CFPB' && $allowCFPBAuditing == 1) ? '<b>Co :</b> ' . $myPipelineRow->PCName . '<br><b>Branch : </b> ' . $myPipelineRow->branchName : $myPipelineRow->PCName)
            , myPipelineRow::$serialNo, 'Processing Company');
    }

    if (($fileType == 'CFPB' && $allowCFPBAuditing == 1) || $userRole == 'Auditor Manager' || ($fileType == 'CFPB' && $userRole == 'Super')) {
        echo myPipelineColumns::showColumnValue('assignedCFPBAuditors', '
        ' . ($myPipelineRow->assignedCFPBAuditors ? '<table>' . $myPipelineRow->assignedCFPBAuditors . '</table>' : '') . '
        ', myPipelineRow::$serialNo, 'Assigned Auditor(s)');
    }

    if (myPipelineColumns::isColumnActive('leadSource')) {
        echo myPipelineColumns::showColumnValue('leadSource', $myPipelineRow->leadSource, myPipelineRow::$serialNo, 'Lead Source');
    }

    if (myPipelineColumns::isColumnActive('insuranceCompName')) {
        echo myPipelineColumns::showColumnValue('insuranceCompName', $myPipelineRow->insuranceCompName, myPipelineRow::$serialNo, 'Insurance Co. Name');
    }

    if (myPipelineColumns::isColumnActive('listingAgentName')) {
        echo myPipelineColumns::showColumnValue('listingAgentName', $myPipelineRow->listingAgentName, myPipelineRow::$serialNo, 'Listing Loan Officer/Broker');
    }

    if (myPipelineColumns::isColumnActive('priorityLevel')) {
        $indicator = '';
        switch (strtolower($myPipelineRow->priorityLevel)) {
            case 'high':
                $indicator .= '<i class="fa fa-fire text-danger ml-1"></i>';
            case 'medium':
                $indicator .= '<i class="fa fa-fire text-danger ml-1"></i>';
            case 'low':
                $indicator .= '<i class="fa fa-fire text-danger ml-1"></i>';
                break;
        }
        echo myPipelineColumns::showColumnValue('priorityLevel', $indicator, myPipelineRow::$serialNo, 'Priority Level');
    }

    /** HMLO Module START **/
    if (myPipelineColumns::isColumnActive('HMLOLoanType')) {
        echo myPipelineColumns::showColumnValue('HMLOLoanType', $myPipelineRow->typeOfHMLOLoanRequesting, myPipelineRow::$serialNo, 'Transaction Type');
    }
    if (myPipelineColumns::isColumnActive('trialPaymentDate1')) {
        echo myPipelineColumns::showColumnValue('trialPaymentDate1', $myPipelineRow->trialPaymentDate1, myPipelineRow::$serialNo, 'First Payment Due');
    }
    if (myPipelineColumns::isColumnActive('proInsPolicyExpDate')) {
        echo myPipelineColumns::showColumnValue('proInsPolicyExpDate', $myPipelineRow->proInsPolicyExpDate, myPipelineRow::$serialNo, 'Any Insurance Expiration Date');
    }
    if (myPipelineColumns::isColumnActive('networthOfBusinessOwned')) {
        echo myPipelineColumns::showColumnValue('networthOfBusinessOwned', $myPipelineRow->networthOfBusinessOwned, myPipelineRow::$serialNo, 'Net Worth');
    }
    if (myPipelineColumns::isColumnActive('borCreditScoreRange')) {
        echo myPipelineColumns::showColumnValue('borCreditScoreRange', $myPipelineRow->borCreditScoreRange, myPipelineRow::$serialNo, 'Credit Score Range');
    }
    if (myPipelineColumns::isColumnActive('midFico')) {
        echo myPipelineColumns::showColumnValue('midFico', $myPipelineRow->midFico, myPipelineRow::$serialNo, 'MID FICO');
    }
    if (myPipelineColumns::isColumnActive('borExperianScore')) {
        echo myPipelineColumns::showColumnValue('borExperianScore', Currency::formatDollarAmountWithDecimal($myPipelineRow->borExperianScore), myPipelineRow::$serialNo, 'Experian');
    }
    if (myPipelineColumns::isColumnActive('borEquifaxScore')) {
        echo myPipelineColumns::showColumnValue('borEquifaxScore', Currency::formatDollarAmountWithDecimal($myPipelineRow->borEquifaxScore), myPipelineRow::$serialNo, 'Equifax');
    }
    if (myPipelineColumns::isColumnActive('borTransunionScore')) {
        echo myPipelineColumns::showColumnValue('borTransunionScore', Currency::formatDollarAmountWithDecimal($myPipelineRow->borTransunionScore), myPipelineRow::$serialNo, 'Transunion');
    }
    if (myPipelineColumns::isColumnActive('borNoOfREPropertiesCompleted')) {
        echo myPipelineColumns::showColumnValue('borNoOfREPropertiesCompleted', $myPipelineRow->borNoOfREPropertiesCompleted, myPipelineRow::$serialNo, '# of Properties Completed');
    }
    if (myPipelineColumns::isColumnActive('borRehabPropCompleted')) {
        echo myPipelineColumns::showColumnValue('borRehabPropCompleted', $myPipelineRow->borRehabPropCompleted, myPipelineRow::$serialNo, '# of New Construction Deals Done');
    }
    if (myPipelineColumns::isColumnActive('borNoOfOwnProp')) {
        echo myPipelineColumns::showColumnValue('borNoOfOwnProp', $myPipelineRow->borNoOfOwnProp, myPipelineRow::$serialNo, '# of Investment Properties');
    }
    if (myPipelineColumns::isColumnActive('isBorUSCitizen')) {
        echo myPipelineColumns::showColumnValue('isBorUSCitizen', $myPipelineRow->isBorUSCitizen, myPipelineRow::$serialNo, 'US Citizen');
    }
    if (myPipelineColumns::isColumnActive('HMLOLender')) {
        echo myPipelineColumns::showColumnValue('HMLOLender', $myPipelineRow->HMLOLender, myPipelineRow::$serialNo, 'Lender');
    }
    if (myPipelineColumns::isColumnActive('totalProjectCost')) {
        echo myPipelineColumns::showColumnValue('totalProjectCost', Currency::formatDollarAmountWithDecimal($myPipelineRow->totalProjectCost), myPipelineRow::$serialNo, 'Total Project Cost');
    }
    if (myPipelineColumns::isColumnActive('rehabCostFinanced')) {
        echo myPipelineColumns::showColumnValue('rehabCostFinanced', $myPipelineRow->rehabCostFinanced, myPipelineRow::$serialNo, 'Rehab Cost Financed');
    }
    if (myPipelineColumns::isColumnActive('LTC')) {
        echo myPipelineColumns::showColumnValue('LTC', $myPipelineRow->LTC ? $myPipelineRow->LTC . '%' : '', myPipelineRow::$serialNo, 'Loan-to-Cost');
    }
    if (myPipelineColumns::isColumnActive('ARV')) {
        echo myPipelineColumns::showColumnValue('ARV', $myPipelineRow->ARV ? $myPipelineRow->ARV . '%' : '', myPipelineRow::$serialNo, 'ARV %');
    }
    if (myPipelineColumns::isColumnActive('rehabConstructionCost')) {
        echo myPipelineColumns::showColumnValue('rehabConstructionCost', Currency::formatDollarAmountWithDecimal($myPipelineRow->rehabConstructionCost), myPipelineRow::$serialNo, 'Rehab/Construction Cost');
    }
    if (myPipelineColumns::isColumnActive('acquisitionLTV')) {
        echo myPipelineColumns::showColumnValue('acquisitionLTV', $myPipelineRow->acquisitionLTV ? $myPipelineRow->acquisitionLTV . '%' : '', myPipelineRow::$serialNo, 'Acquisition LTV');
    }
    if (myPipelineColumns::isColumnActive('marketLTV')) {
        echo myPipelineColumns::showColumnValue('marketLTV', $myPipelineRow->marketLTV ? $myPipelineRow->marketLTV . '%' : '', myPipelineRow::$serialNo, 'Market LTV');
    }
    if (myPipelineColumns::isColumnActive('perRehabCostFinanced')) {
        echo myPipelineColumns::showColumnValue('perRehabCostFinanced', $myPipelineRow->perRehabCostFinanced ? $myPipelineRow->perRehabCostFinanced . '%' : '', myPipelineRow::$serialNo, '% of Rehab Cost Financed');
    }
    if (myPipelineColumns::isColumnActive('exitStrategy')) {
        echo myPipelineColumns::showColumnValue('exitStrategy', $myPipelineRow->exitStrategy, myPipelineRow::$serialNo, 'Exit Strategy');
    }
    if (myPipelineColumns::isColumnActive('isHouseProperty')) {
        echo myPipelineColumns::showColumnValue('isHouseProperty', $myPipelineRow->isHouseProperty, myPipelineRow::$serialNo, 'Borrower Occupancy');
    }
    if (myPipelineColumns::isColumnActive('propertyNeedRehab')) {
        echo myPipelineColumns::showColumnValue('propertyNeedRehab', $myPipelineRow->propertyNeedRehab, myPipelineRow::$serialNo, 'Rehab Required');
    }
    if (myPipelineColumns::isColumnActive('propertyConstructionLevel')) {
        echo myPipelineColumns::showColumnValue('propertyConstructionLevel', $myPipelineRow->propertyConstructionLevel, myPipelineRow::$serialNo, 'Property Construction Level');
    }
    if (myPipelineColumns::isColumnActive('lienPosition')) {
        echo myPipelineColumns::showColumnValue('lienPosition', $myPipelineRow->lienPosition ? $myPipelineRow->lienPosition . ' lien' : '', myPipelineRow::$serialNo, 'Lien Position');
    }
    if (myPipelineColumns::isColumnActive('propertyCounty')) {
        echo myPipelineColumns::showColumnValue('propertyCounty', $myPipelineRow->propertyCounty, myPipelineRow::$serialNo, 'Property County');
    }
    if (myPipelineColumns::isColumnActive('propertySqFt')) {
        echo myPipelineColumns::showColumnValue('propertySqFt', $myPipelineRow->propertySqFt, myPipelineRow::$serialNo, 'Sq Ft');
    }
    if (myPipelineColumns::isColumnActive('propertyURLLink')) {
        echo myPipelineColumns::showColumnValue('propertyURLLink', $myPipelineRow->propertyURLLink, myPipelineRow::$serialNo, 'URL Link To Property');
    }
    if (myPipelineColumns::isColumnActive('taxes1')) {
        echo myPipelineColumns::showColumnValue('taxes1', $myPipelineRow->taxes1, myPipelineRow::$serialNo, 'Annual Property Tax');
    }
    if (myPipelineColumns::isColumnActive('workflowEvents')) {
        echo myPipelineColumns::showColumnValue('workflowEvents', $myPipelineRow->workflowEvents, myPipelineRow::$serialNo, 'WorkFlow(s)');
    }
    if (myPipelineColumns::isColumnActive('HMLOLoanTerm')) {
        echo myPipelineColumns::showColumnValue('HMLOLoanTerm', $myPipelineRow->HMLOLoanTerm, myPipelineRow::$serialNo, 'Loan Term');
    }
    if (myPipelineColumns::isColumnActive('purchasePrice')) {
        echo myPipelineColumns::showColumnValue('purchasePrice', Currency::formatDollarAmountWithDecimal($myPipelineRow->costBasis), myPipelineRow::$serialNo, 'Purchase Price');
    }
    if (myPipelineColumns::isColumnActive('HMLOTotalPayment')) {
        echo myPipelineColumns::showColumnValue('HMLOTotalPayment', Currency::formatDollarAmountWithDecimal($myPipelineRow->lien1Payment), myPipelineRow::$serialNo, 'Monthly Payment');
    }
    if (myPipelineColumns::isColumnActive('netMonthlyPayment')) {
        echo myPipelineColumns::showColumnValue('netMonthlyPayment', Currency::formatDollarAmountWithDecimal($myPipelineRow->netMonthlyPayment), myPipelineRow::$serialNo, 'Monthly Payment(PITIA) - Loan Info');
    }
    if (myPipelineColumns::isColumnActive('totalPropertiesPITIA') && glCustomJobForProcessingCompany::showLoanInfoV2($PCID)) {
        echo myPipelineColumns::showColumnValue('totalPropertiesPITIA', $myPipelineRow->totalPropertiesPITIA, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('totalPropertiesPITIA'));
    }
    if (myPipelineColumns::isColumnActive('ASISValue')) {
        echo myPipelineColumns::showColumnValue('ASISValue', Currency::formatDollarAmountWithDecimal($myPipelineRow->homeValue), myPipelineRow::$serialNo, 'As-Is Value');
    }
    if (myPipelineColumns::isColumnActive('presentOccupancyStatus')) {
        echo myPipelineColumns::showColumnValue('presentOccupancyStatus', $myPipelineRow->presentOccupancyStatus, myPipelineRow::$serialNo, 'Present Occupancy Status');
    }
    if (myPipelineColumns::isColumnActive('propertyCondition')) {
        echo myPipelineColumns::showColumnValue('propertyCondition', $myPipelineRow->propertyCondition, myPipelineRow::$serialNo, 'Condition');
    }
    if (myPipelineColumns::isColumnActive('appraiser1Value')) {
        echo myPipelineColumns::showColumnValue('appraiser1Value', $myPipelineRow->appraiser1Value, myPipelineRow::$serialNo, 'Appraisal 1 Value');
    }
    if (myPipelineColumns::isColumnActive('AVM1')) {
        echo myPipelineColumns::showColumnValue('AVM1', $myPipelineRow->AVM1, myPipelineRow::$serialNo, 'AVM 1 Value');
    }
    if (myPipelineColumns::isColumnActive('dateObtained')) {
        echo myPipelineColumns::showColumnValue('dateObtained', $myPipelineRow->dateObtained, myPipelineRow::$serialNo, 'Appraisal Date');
    }
    if (myPipelineColumns::isColumnActive('appraisal1OrderDate')) {
        echo myPipelineColumns::showColumnValue('appraisal1OrderDate', $myPipelineRow->appraisal1OrderDate, myPipelineRow::$serialNo, 'Appraisal Date');
    }
    if (myPipelineColumns::isColumnActive('totalRehabCost')) {
        echo myPipelineColumns::showColumnValue('totalRehabCost', $myPipelineRow->totalRehabCost, myPipelineRow::$serialNo, 'Total Est. Rehab Cost');
    }
    if (myPipelineColumns::isColumnActive('assessedValue')) {
        echo myPipelineColumns::showColumnValue('assessedValue', Currency::formatDollarAmountWithDecimal($myPipelineRow->assessedValue), myPipelineRow::$serialNo, 'After Repair Value');
    }
    if (myPipelineColumns::isColumnActive('titleCompanyInfo')) {
        echo myPipelineColumns::showColumnValue('titleCompanyInfo', $myPipelineRow->titleContacts, myPipelineRow::$serialNo, 'Title Company Info');
    }
    if (myPipelineColumns::isColumnActive('typeOfSale')) {
        echo myPipelineColumns::showColumnValue('typeOfSale', $myPipelineRow->typeOfSale, myPipelineRow::$serialNo, 'Type of Purchase');
    }
    if (myPipelineColumns::isColumnActive('projectName')) {
        echo myPipelineColumns::showColumnValue('projectName', Strings::processString($myPipelineRow->projectName), myPipelineRow::$serialNo, 'Project Name');
    }
    if (myPipelineColumns::isColumnActive('MFLoanTermsInfoCnt')) {
        echo myPipelineColumns::showColumnValue('MFLoanTermsInfoCnt', $myPipelineRow->MFLoanTermsInfoCnt, myPipelineRow::$serialNo, 'Applications Submitted');
    }
    if (myPipelineColumns::isColumnActive('noOfApprovedStatusCnt')) {
        echo myPipelineColumns::showColumnValue('noOfApprovedStatusCnt', $myPipelineRow->noOfApprovedStatusCnt, myPipelineRow::$serialNo, 'Applications Approved');
    }
    if (myPipelineColumns::isColumnActive('totalApprovedLoanAmt')) {
        echo myPipelineColumns::showColumnValue('totalApprovedLoanAmt', $myPipelineRow->totalApprovedLoanAmt, myPipelineRow::$serialNo, 'Total Amount Approved');
    }
    if (myPipelineColumns::isColumnActive('bankNumber')) {
        echo myPipelineColumns::showColumnValue('bankNumber', Currency::formatDollarAmountWithDecimal($myPipelineRow->bankNumber), myPipelineRow::$serialNo, 'Bank Number');
    }
    if (myPipelineColumns::isColumnActive('lienAmount')) {
        echo myPipelineColumns::showColumnValue('lienAmount', Currency::formatDollarAmountWithDecimal($myPipelineRow->lienAmount), myPipelineRow::$serialNo, 'Lien Amt');
    }
    if (myPipelineColumns::isColumnActive('referringParty')) {
        echo myPipelineColumns::showColumnValue('referringParty', $myPipelineRow->referringParty, myPipelineRow::$serialNo, 'Broker Referring Party');
    }
    if (myPipelineColumns::isColumnActive('availableBudget')) {
        echo myPipelineColumns::showColumnValue('availableBudget', $myPipelineRow->availableBudget1, myPipelineRow::$serialNo, 'Current Escrow Balance');
    }
    if (myPipelineColumns::isColumnActive('currentLoanBalance')) {
        echo myPipelineColumns::showColumnValue('currentLoanBalance', '$ ' . Currency::formatDollarAmountWithDecimal($myPipelineRow->currentLoanBalance), myPipelineRow::$serialNo, 'Current Loan Balance');
    }
    if (myPipelineColumns::isColumnActive('maturityDate')) {
        echo myPipelineColumns::showColumnValue('maturityDate', $myPipelineRow->maturityDate, myPipelineRow::$serialNo, 'Maturity Date');
    }
    if (myPipelineColumns::isColumnActive('servicingStatus')) {
        echo myPipelineColumns::showColumnValue('servicingStatus', $myPipelineRow->servicingStatus, myPipelineRow::$serialNo, 'Servicing Status');
    }
    if (myPipelineColumns::isColumnActive('brokerPointsRate')) {
        $formattedRate = proposalFormula::convertToAbsoluteValueWithNewFormatForPercent($myPipelineRow->brokerPointsRate);
        echo myPipelineColumns::showColumnValue('brokerPointsRate', $formattedRate, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('brokerPointsRate'));
    }
    if (myPipelineColumns::isColumnActive('brokerPointsValue')) {
        echo myPipelineColumns::showColumnValue('brokerPointsValue', Currency::formatDollarAmountWithDecimal($myPipelineRow->brokerPointsValue), myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('brokerPointsValue'));
    }
    if (myPipelineColumns::isColumnActive('originationPointsRate')) {
        $formattedRate = proposalFormula::convertToAbsoluteValueWithNewFormatForPercent($myPipelineRow->originationPointsRate);
        echo myPipelineColumns::showColumnValue('originationPointsRate', $formattedRate, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('originationPointsRate'));
    }
    if (myPipelineColumns::isColumnActive('originationPointsValue')) {
        echo myPipelineColumns::showColumnValue('originationPointsValue', Currency::formatDollarAmountWithDecimal($myPipelineRow->originationPointsValue), myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('originationPointsValue'));
    }
    if (myPipelineColumns::isColumnActive('payOffDate')) {
        echo myPipelineColumns::showColumnValue('payOffDate', $myPipelineRow->payOffDate, myPipelineRow::$serialNo, 'Pay Off Date');
    }
    if (myPipelineColumns::isColumnActive('loanSaleDate')) {
        echo myPipelineColumns::showColumnValue('loanSaleDate', $myPipelineRow->loanSaleDate, myPipelineRow::$serialNo, 'Loan Sale Agreement');
    }

    if ($userRole == 'REST' || ($fileType == 2 && ($userRole == 'Super' || $userRole == 'Sales')) || (($fileType == 4 || $fileType == 'LA') && ($userRole == 'Super' || $userRole == 'Sales'))) {
        echo myPipelineColumns::showColumnValue('oldPCName', $myPipelineRow->PCLink . (trim($myPipelineRow->filePCID) != $myPipelineRow->oldFPCID ? '<br/>(' . $myPipelineRow->oldPCName . ')' : ''), myPipelineRow::$serialNo, 'PC Name');
        echo myPipelineColumns::showColumnValue('BranchLink', $myPipelineRow->BranchLink, myPipelineRow::$serialNo, 'Branch');
    }

    if (myPipelineColumns::isColumnActive('agent') || $fileType == 2 || $fileType == 4 || $fileType == 'LA' || $userRole == 'REST') {
        echo myPipelineColumns::showColumnValue('agent', $myPipelineRow->brokerName, myPipelineRow::$serialNo, 'Broker');
    }

    if (myPipelineColumns::isColumnActive('lender')) {
        echo myPipelineColumns::showColumnValue('lender', $myPipelineRow->lenderName, myPipelineRow::$serialNo, 'Lender Name');
    }

    if (myPipelineColumns::isColumnActive('servicer')) {
        echo myPipelineColumns::showColumnValue('servicer', $myPipelineRow->servicerName, myPipelineRow::$serialNo, 'Servicer Name');
    }

    if (myPipelineColumns::isColumnActive('trustee')) {
        echo myPipelineColumns::showColumnValue('trustee', $myPipelineRow->trusteeName, myPipelineRow::$serialNo, 'Trustee Name');
    }

    if (myPipelineColumns::isColumnActive('investor')) {
        echo myPipelineColumns::showColumnValue('investor', $myPipelineRow->investorContactDetails, myPipelineRow::$serialNo, 'Investor Name');
    }

    if (myPipelineColumns::isColumnActive('loanofficer') || $fileType == 2 || $fileType == 4 || $fileType == 'LA' || $userRole == 'REST') {
        echo myPipelineColumns::showColumnValue('loanofficer', $myPipelineRow->secondaryBrokerName, myPipelineRow::$serialNo, 'Loan Officer');
    }

    if (myPipelineColumns::isColumnActive('branch') || $fileType == 4) {
        echo myPipelineColumns::showColumnValue('branch', $myPipelineRow->branchName, myPipelineRow::$serialNo, 'Branch Name');
    }

    if ($userRole == 'REST' || ($fileType == 2 && ($userRole == 'Super' || $userRole == 'Sales'))) {
        echo myPipelineColumns::showColumnValue('orderStatus', $myPipelineRow->orderStatus, myPipelineRow::$serialNo, 'Order Status');
        echo myPipelineColumns::showColumnValue('price', $myPipelineRow->price, myPipelineRow::$serialNo, 'Price');
        echo myPipelineColumns::showColumnValue('paymentStatus', $myPipelineRow->paymentStatus, myPipelineRow::$serialNo, 'Payment Status');
    }

    if ((($fileType == 4 || $fileType == 'LA') && ($userRole == 'Super' || $userRole == 'Sales')) || (($fileType == 4) && $userGroup == 'Employee' && $userSeeBilling == 1) || $userRole == 'Auditor') {
        echo myPipelineColumns::showColumnValue('price', $myPipelineRow->price, myPipelineRow::$serialNo, 'Price');
        echo myPipelineColumns::showColumnValue('paymentStatus', $myPipelineRow->paymentStatus, myPipelineRow::$serialNo, 'Payment Status');
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalAsIsValue')) {
        echo myPipelineColumns::showColumnValue('propertyAppraisalAsIsValue', Currency::formatDollarAmountWithDecimal($myPipelineRow->propertyAppraisalAsIsValue), myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('propertyAppraisalAsIsValue'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalRehabbedValue')) {
        echo myPipelineColumns::showColumnValue('propertyAppraisalRehabbedValue', Currency::formatDollarAmountWithDecimal($myPipelineRow->propertyAppraisalRehabbedValue), myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('propertyAppraisalRehabbedValue'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalMonthlyRent')) {
        echo myPipelineColumns::showColumnValue('propertyAppraisalMonthlyRent', Currency::formatDollarAmountWithDecimal($myPipelineRow->propertyAppraisalMonthlyRent), myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('propertyAppraisalMonthlyRent'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalJobTypes')) {
        echo myPipelineColumns::showColumnValue('propertyAppraisalJobTypes', str_replace(',', ', ', $myPipelineRow->propertyAppraisalJobTypes), myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('propertyAppraisalJobTypes'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalDateObtained')) {
        echo myPipelineColumns::showColumnValue('propertyAppraisalDateObtained', $myPipelineRow->propertyAppraisalDateObtained, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('propertyAppraisalDateObtained'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalRequestedReturnDate')) {
        echo myPipelineColumns::showColumnValue('propertyAppraisalRequestedReturnDate', $myPipelineRow->propertyAppraisalRequestedReturnDate, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('propertyAppraisalRequestedReturnDate'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalIsRushOrder')) {
        echo myPipelineColumns::showColumnValue('propertyAppraisalIsRushOrder', $myPipelineRow->propertyAppraisalIsRushOrder, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('propertyAppraisalIsRushOrder'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalOrderDate')) {
        echo myPipelineColumns::showColumnValue('propertyAppraisalOrderDate', $myPipelineRow->propertyAppraisalOrderDate, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('propertyAppraisalOrderDate'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalComments')) {
        echo myPipelineColumns::showColumnValue('propertyAppraisalComments', $myPipelineRow->propertyAppraisalComments, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('propertyAppraisalComments'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalEffectiveDate')) {
        echo myPipelineColumns::showColumnValue('propertyAppraisalEffectiveDate', $myPipelineRow->propertyAppraisalEffectiveDate, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('propertyAppraisalEffectiveDate'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalInspectionDate')) {
        echo myPipelineColumns::showColumnValue('propertyAppraisalInspectionDate', $myPipelineRow->propertyAppraisalInspectionDate, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('propertyAppraisalInspectionDate'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalExpectedDeliveryDate')) {
        echo myPipelineColumns::showColumnValue('propertyAppraisalExpectedDeliveryDate', $myPipelineRow->propertyAppraisalExpectedDeliveryDate, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('propertyAppraisalExpectedDeliveryDate'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalExpectedDeliveryDelay')) {
        echo myPipelineColumns::showColumnValue('propertyAppraisalExpectedDeliveryDelay', $myPipelineRow->propertyAppraisalExpectedDeliveryDelay, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('propertyAppraisalExpectedDeliveryDelay'));
    }
    if (myPipelineColumns::isColumnActive('coBorrowerName')) {
        echo myPipelineColumns::showColumnValue('coBorrowerName', $myPipelineRow->coBorrowerName, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('coBorrowerName'));
    }
    if (myPipelineColumns::isColumnActive('fundingDate')) {
        echo myPipelineColumns::showColumnValue('fundingDate', $myPipelineRow->fundingDate, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('fundingDate'));
    }
    if (myPipelineColumns::isColumnActive('clearToCloseBy')) {
        echo myPipelineColumns::showColumnValue('clearToCloseBy', $myPipelineRow->clearToCloseBy, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('clearToCloseBy'));
    }
    if (myPipelineColumns::isColumnActive('propertyFloodZone')) {
        echo myPipelineColumns::showColumnValue('propertyFloodZone', $myPipelineRow->propertyFloodZone, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('propertyFloodZone'));
    }
    if (myPipelineColumns::isColumnActive('requiredInsurance')) {
        echo myPipelineColumns::showColumnValue('requiredInsurance', $myPipelineRow->requiredInsurance, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('requiredInsurance'));
    }
    if (myPipelineColumns::isColumnActive('HMDAActionTaken')) {
        echo myPipelineColumns::showColumnValue('HMDAActionTaken', $myPipelineRow->HMDAActionTaken, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('HMDAActionTaken'));
    }
    if (myPipelineColumns::isColumnActive('noOfPropertiesAcquiring')) {
        echo myPipelineColumns::showColumnValue('noOfPropertiesAcquiring', $myPipelineRow->noOfPropertiesAcquiring, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('noOfPropertiesAcquiring'));
    }
    if (myPipelineColumns::isColumnActive('titleOrderDate')) {
        echo myPipelineColumns::showColumnValue('titleOrderDate', $myPipelineRow->titleOrderDate, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('titleOrderDate'));
    }
    if (myPipelineColumns::isColumnActive('lenderInternalNotes')) {
        echo myPipelineColumns::showColumnValue('lenderInternalNotes', $myPipelineRow->lenderInternalNotes, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('lenderInternalNotes'));
    }
    if (myPipelineColumns::isColumnActive('referralPoints')) {
        echo myPipelineColumns::showColumnValue('referralPoints', $myPipelineRow->referralPoints, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('lenderInternalNotes'));
    }
    if (myPipelineColumns::isColumnActive('warehouseInvestor')) {
        echo myPipelineColumns::showColumnValue('warehouseInvestor', $myPipelineRow->warehouseInvestor, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('lenderInternalNotes'));
    }
    if (myPipelineColumns::isColumnActive('wireAmountSent')) {
        echo myPipelineColumns::showColumnValue('wireAmountSent', $myPipelineRow->wireAmountSent, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('lenderInternalNotes'));
    }
    if (myPipelineColumns::isColumnActive('eCoaWaiverStatus')) {
        echo myPipelineColumns::showColumnValue('eCoaWaiverStatus', $myPipelineRow->eCoaWaiverStatus, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('eCoaWaiverStatus'));
    }
    if (myPipelineColumns::isColumnActive('rateLockPeriod')) {
        echo myPipelineColumns::showColumnValue('rateLockPeriod', $myPipelineRow->rateLockPeriod, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('rateLockPeriod'));
    }
    if (myPipelineColumns::isColumnActive('rateLockDate')) {
        echo myPipelineColumns::showColumnValue('rateLockDate', $myPipelineRow->rateLockDate, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('rateLockDate'));
    }
    if (myPipelineColumns::isColumnActive('rateLockExpirationDate')) {
        echo myPipelineColumns::showColumnValue('rateLockExpirationDate', $myPipelineRow->rateLockExpirationDate, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('rateLockExpirationDate'));
    }
    if (myPipelineColumns::isColumnActive('rateLockExtension')) {
        echo myPipelineColumns::showColumnValue('rateLockExtension', $myPipelineRow->rateLockExtension, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('rateLockExtension'));
    }
    if (myPipelineColumns::isColumnActive('rateLockNotes')) {
        echo myPipelineColumns::showColumnValue('rateLockNotes', $myPipelineRow->rateLockNotes, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('rateLockNotes'));
    }


    if (myPipelineColumns::isColumnActive('primaryAppraisalEcoaDeliveryDate')) {
        echo myPipelineColumns::showColumnValue('primaryAppraisalEcoaDeliveryDate', $myPipelineRow->primaryAppraisalEcoaDeliveryDate, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('primaryAppraisalEcoaDeliveryDate'));
    }

    if (myPipelineColumns::isColumnActive('propertyAppraisalSupplementalProductFormType1')) {
        echo myPipelineColumns::showColumnValue('propertyAppraisalSupplementalProductFormType1', $myPipelineRow->propertyAppraisalSupplementalProductFormType1, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('propertyAppraisalSupplementalProductFormType1'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalSupplementalProductFormType2')) {
        echo myPipelineColumns::showColumnValue('propertyAppraisalSupplementalProductFormType2', $myPipelineRow->propertyAppraisalSupplementalProductFormType2, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('propertyAppraisalSupplementalProductFormType2'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalSupplementalProductFormType3')) {
        echo myPipelineColumns::showColumnValue('propertyAppraisalSupplementalProductFormType3', $myPipelineRow->propertyAppraisalSupplementalProductFormType3, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('propertyAppraisalSupplementalProductFormType3'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalSupplementalProductEffectiveDate1')) {
        echo myPipelineColumns::showColumnValue('propertyAppraisalSupplementalProductEffectiveDate1', $myPipelineRow->propertyAppraisalSupplementalProductEffectiveDate1, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('propertyAppraisalSupplementalProductEffectiveDate1'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalSupplementalProductEffectiveDate2')) {
        echo myPipelineColumns::showColumnValue('propertyAppraisalSupplementalProductEffectiveDate2', $myPipelineRow->propertyAppraisalSupplementalProductEffectiveDate2, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('propertyAppraisalSupplementalProductEffectiveDate2'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalSupplementalProductEffectiveDate3')) {
        echo myPipelineColumns::showColumnValue('propertyAppraisalSupplementalProductEffectiveDate3', $myPipelineRow->propertyAppraisalSupplementalProductEffectiveDate3, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('propertyAppraisalSupplementalProductEffectiveDate3'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalSupplementalProductEcoaADeliveryDate1')) {
        echo myPipelineColumns::showColumnValue('propertyAppraisalSupplementalProductEcoaADeliveryDate1', $myPipelineRow->propertyAppraisalSupplementalProductEcoaADeliveryDate1, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('propertyAppraisalSupplementalProductEcoaADeliveryDate1'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalSupplementalProductEcoaADeliveryDate2')) {
        echo myPipelineColumns::showColumnValue('propertyAppraisalSupplementalProductEcoaADeliveryDate2', $myPipelineRow->propertyAppraisalSupplementalProductEcoaADeliveryDate2, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('propertyAppraisalSupplementalProductEcoaADeliveryDate2'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalSupplementalProductEcoaADeliveryDate3')) {
        echo myPipelineColumns::showColumnValue('propertyAppraisalSupplementalProductEcoaADeliveryDate3', $myPipelineRow->propertyAppraisalSupplementalProductEcoaADeliveryDate3, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('propertyAppraisalSupplementalProductEcoaADeliveryDate3'));
    }

    if (myPipelineColumns::isColumnActive('brokerPartnerType')) {
        echo myPipelineColumns::showColumnValue('brokerPartnerType', $myPipelineRow->brokerPartnerType, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('brokerPartnerType'));
    }
    if (myPipelineColumns::isColumnActive('totalPropertiesLoanAmount') && glCustomJobForProcessingCompany::showLoanInfoV2($PCID)) {
        echo myPipelineColumns::showColumnValue('totalPropertiesLoanAmount', $myPipelineRow->totalPropertiesLoanAmount, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('totalPropertiesLoanAmount'));
    }
    if (myPipelineColumns::isColumnActive('PSAClosingDate')) {
        echo myPipelineColumns::showColumnValue('PSAClosingDate', $myPipelineRow->PSAClosingDate, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('PSAClosingDate'));
    }
    if (myPipelineColumns::isColumnActive('buildingAnalysisOutstanding')) {
        echo myPipelineColumns::showColumnValue('buildingAnalysisOutstanding', $myPipelineRow->buildingAnalysisOutstanding, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('buildingAnalysisOutstanding'));
    }
    if (myPipelineColumns::isColumnActive('buildingAnalysisNeed')) {
        echo myPipelineColumns::showColumnValue('buildingAnalysisNeed', $myPipelineRow->buildingAnalysisNeed, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('buildingAnalysisNeed'));
    }
   if (myPipelineColumns::isColumnActive('buildingAnalysisDueDate')) {
        echo myPipelineColumns::showColumnValue('buildingAnalysisDueDate', $myPipelineRow->buildingAnalysisDueDate, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('buildingAnalysisDueDate'));
    }
   if (myPipelineColumns::isColumnActive('targetSubmissionDate')) {
        echo myPipelineColumns::showColumnValue('targetSubmissionDate', $myPipelineRow->targetSubmissionDate, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('targetSubmissionDate'));
    }
   if (myPipelineColumns::isColumnActive('authorizationStatus')) {
        echo myPipelineColumns::showColumnValue('authorizationStatus', $myPipelineRow->authorizationStatus, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('authorizationStatus'));
    }
   if (myPipelineColumns::isColumnActive('VOMStatus')) {
        echo myPipelineColumns::showColumnValue('VOMStatus', $myPipelineRow->VOMStatus, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('VOMStatus'));
    }
   if (myPipelineColumns::isColumnActive('payoffStatus')) {
        echo myPipelineColumns::showColumnValue('payoffStatus', $myPipelineRow->payoffStatus, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('payoffStatus'));
    }
    if (myPipelineColumns::isColumnActive('trackRecord')) {
        echo myPipelineColumns::showColumnValue('trackRecord', $myPipelineRow->trackRecord, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('trackRecord'));
    }
    if (myPipelineColumns::isColumnActive('welcomeCallStatus')) {
        echo myPipelineColumns::showColumnValue('welcomeCallStatus', $myPipelineRow->welcomeCallStatus, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('welcomeCallStatus'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalStatus')) {
        echo myPipelineColumns::showColumnValue('propertyAppraisalStatus', $myPipelineRow->propertyAppraisalStatus, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('propertyAppraisalStatus'));
    }
    if (myPipelineColumns::isColumnActive('LOISentDate')) {
        echo myPipelineColumns::showColumnValue('LOISentDate', $myPipelineRow->LOISentDate, myPipelineRow::$serialNo, myPipelineColumns::getFieldLabel('LOISentDate'));
    }

    /* Paid Status - Synergy Attorney Services - Sep 6, 2016 */

    if (!($fileType == 2 || $fileType == 'CFPB' || $userGroup == 'CFPB Auditor' || $userGroup == 'Auditor Manager')) {
        if (($userRole == 'Super' && ($PCID > 0 || sizeof(Arrays::explodeIntVals($executiveId)) || sizeof(Arrays::explodeIntVals($brokerNumb))))
            || $userRole == 'Manager' || ($userRole == 'Super' && ($fileType == 4 || $fileType == 'LA')) ||
            (PageVariables::$allowToMassUpdate)) { ?>
            <td class="tdClassMultiSelect text-center">
                <div class="checkbox-list d-inline-block">
                    <label class="checkbox checkbox-outline checkbox-square"
                           for="fileIDMultiSelect_<?php echo $r ?>"><input
                                class="newCheck singleCheckFile" type="checkbox" name="fileID"
                                id="fileIDMultiSelect_<?php echo $r ?>"
                                value="<?php echo $myPipelineRow->LMRResponseId ?>"><span></span></label></div>
            </td>
            <?php
        }
        if (myPipelineColumns::$allowEmailCampaignPC
            && PageVariables::$allowEmailCampaign == 1
            && $myPipelineRow->activeFile == 1) {
            if ($myPipelineRow->sendMarketingEmail == 1) { ?>
                <td class="tdClassMailSend text-center ">
                    <div class="checkbox-inline d-inline-block">
                        <label class="checkbox checkbox-outline checkbox-square "
                               for="fileIDMailSend_<?php echo $r ?>"><input
                                    class="newCheck fileIdsCheckbox"
                                    type="checkbox"
                                    name="fileID1"
                                    id="fileIDMailSend_<?php echo $r ?>"
                                    value="<?php echo $myPipelineRow->LMRResponseId ?>"><span></span></label>
                    </div>
                </td>
                <?php
            } else { ?>
            <td data-allowEmailCampaign="<?php echo $allowEmailCampaign; ?>"
                data-activeFile="<?php echo $myPipelineRow->activeFile; ?>"
                data-sendMarketingEmail="<?php echo $myPipelineRow->sendMarketingEmail; ?>"></td><?php
            }
        }
    } ?>
</tr> 
