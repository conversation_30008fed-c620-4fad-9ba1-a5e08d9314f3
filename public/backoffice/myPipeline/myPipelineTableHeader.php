<?php
global $fileType, $PCID, $glShowMoneyIconForPaidStatus, $sortOpt, $orderBy, $userRole, $pipelineHeader;
global $userGroup, $executiveId, $brokerNumb, $allECount, $PCModuleInfoKeys, $allowToCFPBSubmitForPC;
global $allowEmailCampaign, $activeFile, $noOfRecordsPerPage, $allowCFPBAuditing, $userSeeBilling, $allowToAccessInternalLoanProgram;

use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glPCID;
use models\Controllers\backoffice\myPipelineColumns;
use models\PageVariables;
use models\standard\Arrays;

?>
<thead class="thead-light">
<tr class="">
    <?php
    if (!$sortOpt) {
        $sortOpt = 'dateAndUpt';
        $orderBy = 'desc';
    }
    $headerCount = 1;
    myPipelineColumns::$columnOrder['sno'] = $headerCount++;
    echo myPipelineColumns::showNonSortableColumn('#', '#'); ?>

    <?php
    if ($fileType != 4 && $fileType != 'LA') {
        myPipelineColumns::$columnOrder['folder'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('', '');
    }

    myPipelineColumns::$columnOrder['actions'] = $headerCount++;
    echo myPipelineColumns::showNonSortableColumn('Actions', 'Actions');

    myPipelineColumns::$columnOrder['namePropAddrs'] = $headerCount++;
    echo myPipelineColumns::showSortableColumn('borFN', $sortOpt, $orderBy, 'Name &amp; Prop. Addr', true);

    if (myPipelineColumns::isColumnActive('fileId')) {
        myPipelineColumns::$columnOrder['fileId'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('fileId', 'File ID');

    }

    if (myPipelineColumns::isColumnActive('recordDate') || $fileType == 2 || $fileType == 4 || $fileType == 'LA' || $userRole == 'REST') {
        myPipelineColumns::$columnOrder['recordDate'] = $headerCount++; ?>
        <th id="recordDate_0" class="text-nowrap thClassRecordDate text-nowrap">
            <?php
            if ($userRole == 'REST'
                || ($fileType == 2
                    && ($userRole == 'Super' || $userRole == 'Sales'))
                || (($fileType == 4
                        || $fileType == 'LA')
                    && ($userRole == 'Super'
                        || $userRole == 'Sales'))
            ) {
                $recordDateText = 'Submitted Date';
            } else if ($fileType == 2) {
                $recordDateText = 'Ordered Date';
            } else {
                $recordDateText = 'Date Entered';
            } ?>
            <?php if ($fileType != 4) {
                $_sortOpt = ($userRole == 'REST' || $fileType == 2) ? 'restDate' : (($fileType == 'LA') ? 'LASubmissionDate' : 'date');
                echo myPipelineColumns::showSortableColumn($_sortOpt, $sortOpt, $orderBy, $recordDateText);
            } ?>
        </th>
        <?php
    }

    if (myPipelineColumns::isColumnActive('MERSID')) {
        myPipelineColumns::$columnOrder['MERSID'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('MERSID', $sortOpt, $orderBy, 'MERS ID', true);
    }

    if (myPipelineColumns::isColumnActive('createdDateWithTimestamp')) {
        myPipelineColumns::$columnOrder['createdDateWithTimestamp'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('createdDateWithTimestamp', $sortOpt, $orderBy, 'Created Date With Timestamp', true);
    }

    if (myPipelineColumns::isColumnActive('lastUpdatedDate')) {
        myPipelineColumns::$columnOrder['lastUpdatedDate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('dateAndUpt', $sortOpt, $orderBy, 'Last Action', true);
    }

    if (myPipelineColumns::isColumnActive('entityName')) {
        myPipelineColumns::$columnOrder['entityName'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('entityName', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('entityName'), true);
    }

    // borrower
    if (myPipelineColumns::isColumnActive('phoneNumber') || $fileType == 2 || $fileType == 4) {
        myPipelineColumns::$columnOrder['phoneNumber'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('phoneNumber', 'Phone');
    }

    if (myPipelineColumns::isColumnActive('cellNumber')) {
        myPipelineColumns::$columnOrder['cellNumber'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('cellNumber', 'Cell #');
    }

    if (myPipelineColumns::isColumnActive('workNumber')) {
        myPipelineColumns::$columnOrder['workNumber'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('workNumber', 'Work #');
    }

    if (myPipelineColumns::isColumnActive('borrowerEmail')) {
        myPipelineColumns::$columnOrder['borrowerEmail'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('borrowerEmail', 'Email');
    }

    if (myPipelineColumns::isColumnActive('city')) {
        myPipelineColumns::$columnOrder['city'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('city', 'City');
    }

    if (myPipelineColumns::isColumnActive('state')) {
        myPipelineColumns::$columnOrder['state'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('state', 'State');
    }

    if (myPipelineColumns::isColumnActive('assetTotalCashBankAcc')) {
        myPipelineColumns::$columnOrder['assetTotalCashBankAcc'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('assetTotalCashBankAcc', myPipelineColumns::getFieldLabel('assetTotalCashBankAcc'));
    }

    if (myPipelineColumns::isColumnActive('primaryStatus') || $fileType == 2 || $fileType == 4 || $fileType == 'LA' || $userRole == 'REST') {
        myPipelineColumns::$columnOrder['primaryStatus'] = $headerCount++;
        ?>
        <th class="text-nowrap thClassStatus" id="primaryStatus_0">
            <?php
            $statusText = '';
            if ($userRole == 'Auditor' || $fileType == 'LA') {
                $statusText = 'Loan Audit ';
            } else if ($userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager' || $fileType == 'CFPB') {
                $statusText = 'CFPB Audit ';
            }
            $statusText .= 'Status';
            echo myPipelineColumns::showSortableColumn('primeStatus', $sortOpt, $orderBy, $statusText); ?>
        </th>
    <?php }

    if (myPipelineColumns::isColumnActive('subStatus')) {
        myPipelineColumns::$columnOrder['subStatus'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('subStatus', 'Sub-Status');
    }

    if (myPipelineColumns::isColumnActive('LMRInternalLoanProgram') &&
        ($userGroup == 'Employee' || ($userGroup == 'Agent' && $allowToAccessInternalLoanProgram))) {
        myPipelineColumns::$columnOrder['LMRInternalLoanProgram'] = $headerCount++;
        //echo myPipelineColumns::showNonSortableColumn('LMRInternalLoanProgram', 'Internal Loan Programs');
        echo myPipelineColumns::showSortableColumn('LMRInternalLoanProgram', $sortOpt, $orderBy, 'Internal Loan Programs', true);
    }


    if (myPipelineColumns::isColumnActive('seviceTypes')) {
        myPipelineColumns::$columnOrder['seviceTypes'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('seviceTypes', $sortOpt, $orderBy, in_array('HMLO', $PCModuleInfoKeys) ? 'Loan Programs' : 'Services<br/>Offered', true);
    }

    if (myPipelineColumns::isColumnActive('propertyType')) {
        myPipelineColumns::$columnOrder['propertyType'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('propertyType', 'Property Type');
    }

    if (myPipelineColumns::isColumnActive('propertyCity')) {
        myPipelineColumns::$columnOrder['propertyCity'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('propertyCity', $sortOpt, $orderBy, 'Property City', true);
    }

    if (myPipelineColumns::isColumnActive('propertyState')) {
        myPipelineColumns::$columnOrder['propertyState'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('propertyState', $sortOpt, $orderBy, 'Property State', true);
    }

    if (myPipelineColumns::isColumnActive('desiredClosingDate')) {
        myPipelineColumns::$columnOrder['desiredClosingDate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('desiredClosingDate', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('desiredClosingDate'), true);
    }

    if (myPipelineColumns::isColumnActive('hearingDate')) {
        myPipelineColumns::$columnOrder['hearingDate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('hearingDate', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('hearingDate'), true);
    }

    if (myPipelineColumns::isColumnActive('CFPBSubmittedDate') || $fileType == 'CFPB' || $userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager') {
        myPipelineColumns::$columnOrder['CFPBSubmittedDate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('CFPBSubmittedDate', $sortOpt, $orderBy, 'CFPB Submitted', true);
    }

    if (myPipelineColumns::isColumnActive('salesDate') && (array_key_exists('LM', myPipelineColumns::$PCModuleInfo) || array_key_exists('SS', myPipelineColumns::$PCModuleInfo))) {
        myPipelineColumns::$columnOrder['salesDate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('salesDate', $sortOpt, $orderBy, 'Sale Date', true, $PCID);
    }
    if (myPipelineColumns::isColumnActive('disclosureSentDate')) {
        myPipelineColumns::$columnOrder['disclosureSentDate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('disclosureSentDate', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('disclosureSentDate'), true, $PCID);
    }


    if (myPipelineColumns::isColumnActive('receivedDate')) {
        myPipelineColumns::$columnOrder['receivedDate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('receivedDate', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('receivedDate'), true);
    }
    if (myPipelineColumns::isColumnActive('lenderSubmission')) {
        myPipelineColumns::$columnOrder['lenderSubmission'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('lenderSubmission', $sortOpt, $orderBy, 'Lender Submission Date', true);
    }
    if (myPipelineColumns::isColumnActive('closedDate')) {
        myPipelineColumns::$columnOrder['closedDate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('closedDate', $sortOpt, $orderBy, 'Closed Date', true);
    }

    if (myPipelineColumns::isColumnActive('closingDate')) {
        myPipelineColumns::$columnOrder['closingDate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('targetClosingDate', $sortOpt, $orderBy, 'Actual Closing Date', true);
    }

    if (myPipelineColumns::isColumnActive('targetClosingDate')) {
        myPipelineColumns::$columnOrder['targetClosingDate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('adminTargetClosingDate', $sortOpt, $orderBy, 'Target Closing Date', true);
    }

    if (myPipelineColumns::isColumnActive('borrowerCallBack')) {
        myPipelineColumns::$columnOrder['borrowerCallBack'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('borrowerCallBack', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('borrowerCallBack'), true);
    }

    if (myPipelineColumns::isColumnActive('lenderCallBack')) {
        myPipelineColumns::$columnOrder['lenderCallBack'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('lenderCallBack', $sortOpt, $orderBy, 'Lender Call Back Date', true);
    }

    if (myPipelineColumns::isColumnActive('HAFADate')) {
        myPipelineColumns::$columnOrder['HAFADate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('HAFADate', $sortOpt, $orderBy, 'HAFA Date', true);
    }

    if ($userRole == 'REST' || ($fileType == 2 && ($userRole == 'Super' || $userRole == 'Sales')) || ($fileType == 4 && ($userRole == 'Super' || $userRole == 'Sales'))) {
        doNothing();
    } else if ($fileType == 2) {
        myPipelineColumns::$columnOrder['orderStatus'] = $headerCount++;
        myPipelineColumns::$columnOrder['paymentStatus'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('orderStatus', 'Order Status');
        echo myPipelineColumns::showNonSortableColumn('paymentStatus', 'Payment Status');
    }
    if ($fileType == 'LA') {
        myPipelineColumns::$columnOrder['reportId'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('FileID', $sortOpt, $orderBy, 'Report ID #', true);
    }
    if ($fileType == 4) {
        myPipelineColumns::$columnOrder['reportId'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('FileID', $sortOpt, $orderBy, 'Report ID #', true);
    } else if ($userRole == 'REST' || ($fileType == 2 && ($userRole == 'Super' || $userRole == 'Sales'))) {
        myPipelineColumns::$columnOrder['caseNum'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('CaseFile', $sortOpt, $orderBy, 'Case #', true);
    } else {
        if (myPipelineColumns::isColumnActive('fileVelocity') || $userRole == 'Auditor') {
            myPipelineColumns::$columnOrder['fileVelocity'] = $headerCount++;
            echo myPipelineColumns::showSortableColumn('daysInStatus', $sortOpt, $orderBy, 'Days in Primary File Status', true);
        }

        if (myPipelineColumns::isColumnActive('HMLOTotalLoanAmt')) {
            myPipelineColumns::$columnOrder['HMLOTotalLoanAmt'] = $headerCount++;
            echo myPipelineColumns::showSortableColumn('HMLOTotalLoanAmt', $sortOpt, $orderBy, 'Loan Amount', true);
        }

        if (myPipelineColumns::isColumnActive('initialLoanAmount')) {
            myPipelineColumns::$columnOrder['initialLoanAmount'] = $headerCount++;
            echo myPipelineColumns::showNonSortableColumn('initialLoanAmount', 'Initial Loan Amount');
        }
        if (myPipelineColumns::isColumnActive('brokerProcessingFee')) {
            myPipelineColumns::$columnOrder['brokerProcessingFee'] = $headerCount++;
            echo myPipelineColumns::showSortableColumn('brokerProcessingFee', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('brokerProcessingFee'), true);
        }

        if (myPipelineColumns::isColumnActive('yieldSpread')) {
            myPipelineColumns::$columnOrder['yieldSpread'] = $headerCount++;
            echo myPipelineColumns::showNonSortableColumn('yieldSpread', myPipelineColumns::getFieldLabel('yieldSpread'));
        }

        if (myPipelineColumns::isColumnActive('costOfCapital')) {
            myPipelineColumns::$columnOrder['costOfCapital'] = $headerCount++;
            echo myPipelineColumns::showNonSortableColumn('costOfCapital', myPipelineColumns::getFieldLabel('costOfCapital'));
        }

        if (myPipelineColumns::isColumnActive('monthlyLoanAmount')) {
            myPipelineColumns::$columnOrder['monthlyLoanAmount'] = $headerCount++;
            echo myPipelineColumns::showSortableColumn('monthlyLoanAmount', $sortOpt, $orderBy, 'Payroll Loan', true);
        }

        if (myPipelineColumns::isColumnActive('loanAuditProduct') || $userRole == 'Auditor' || $fileType == 'LA') {
            myPipelineColumns::$columnOrder['loanAuditProduct'] = $headerCount++;
            ?>
            <th class="text-nowrap thClassLoanAudit" id="loanAuditProduct_0">Loan Audit Product
            </th>
        <?php }

        if (myPipelineColumns::isColumnActive('fraudLevel') || $userRole == 'Auditor') {
            myPipelineColumns::$columnOrder['fraudLevel'] = $headerCount++;
            echo myPipelineColumns::showNonSortableColumn('fraudLevel', 'Fraud Level Warning');
        }
    }

    if ($userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager' || ($fileType == 'CFPB' && $allowCFPBAuditing == 1) || ($fileType == 'CFPB' && $userRole == 'Super')) {
        myPipelineColumns::$columnOrder['submittedBy'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('submittedBy', 'Submitted By');
    }
    if (myPipelineColumns::isColumnActive('entity')) {
        myPipelineColumns::$columnOrder['entity'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('entity', $sortOpt, $orderBy, 'Entity Info', true);
    }

    if (myPipelineColumns::isColumnActive('entityType')) {
        myPipelineColumns::$columnOrder['entityType'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('entityType', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('entityType'), true);
    }

    if (myPipelineColumns::isColumnActive('stateOfFormation')) {
        myPipelineColumns::$columnOrder['stateOfFormation'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('stateOfFormation', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('entityStateOfFormation'), true);
    }

    if (myPipelineColumns::isColumnActive('entityState')) {
        myPipelineColumns::$columnOrder['entityState'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('entityState', $sortOpt, $orderBy, 'Entity State', true);
    }

    if (myPipelineColumns::isColumnActive('entityCity')) {
        myPipelineColumns::$columnOrder['entityCity'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('entityCity', $sortOpt, $orderBy, 'Entity City', true);
    }

    if (myPipelineColumns::isColumnActive('propertyOwnership')) {
        myPipelineColumns::$columnOrder['propertyOwnership'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('propertyOwnership', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('entityPropertyOwnerShip'), true);
    }
    if (myPipelineColumns::isColumnActive('ofEmployees')) {
        myPipelineColumns::$columnOrder['ofEmployees'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('ofEmployees', $sortOpt, $orderBy, '# Of Employees', true);
    }

    if (myPipelineColumns::isColumnActive('avgMonthlyCreditCardSales')) {
        myPipelineColumns::$columnOrder['avgMonthlyCreditCardSales'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('avgMonthlyCreditCardSales', $sortOpt, $orderBy, 'Avg Monthly Credit Card Sales', true);
    }

    if (myPipelineColumns::isColumnActive('avgTotalMonthlySales')) {
        myPipelineColumns::$columnOrder['avgTotalMonthlySales'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('avgTotalMonthlySales', $sortOpt, $orderBy, 'Avg Total Monthly Sales', true);
    }

    if (myPipelineColumns::isColumnActive('annualGrossProfit')) {
        myPipelineColumns::$columnOrder['annualGrossProfit'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('annualGrossProfit', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('annualGrossProfit'), true);
    }
    if (myPipelineColumns::isColumnActive('netBusinessIncome')) {
        myPipelineColumns::$columnOrder['netBusinessIncome'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('netBusinessIncome', $sortOpt, $orderBy, 'Net Business Income', true);
    }

    if (myPipelineColumns::isColumnActive('grossSocialSecurity')) {
        myPipelineColumns::$columnOrder['grossSocialSecurity'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('grossSocialSecurity', 'Gross SSI<br>(Bor + CoBor)');
    }

    if (myPipelineColumns::isColumnActive('totalGrossIncome')) {
        myPipelineColumns::$columnOrder['totalGrossIncome'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('totalGrossIncome', 'Total Gross<br>Income');
    }
    if (myPipelineColumns::isColumnActive('totalNetIncome')) {
        myPipelineColumns::$columnOrder['totalNetIncome'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('totalNetIncome', 'Total Net Inc.', 'Total Monthly Income (Borrower + CoBorrower)');
    }
    if (myPipelineColumns::isColumnActive('totalExpenses')) {
        myPipelineColumns::$columnOrder['totalExpenses'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('totalExpenses', 'Total Expenses');
    }
    if (myPipelineColumns::isColumnActive('disposableIncome')) {
        myPipelineColumns::$columnOrder['disposableIncome'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('disposableIncome', 'Disposable Income');
    }
    if (myPipelineColumns::isColumnActive('currentDTI')) {
        myPipelineColumns::$columnOrder['currentDTI'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('currentDTI', 'Current DTI');
    }

    if (myPipelineColumns::isColumnActive('totalAssets')) {
        myPipelineColumns::$columnOrder['totalAssets'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('totalAssets', 'Total Assets');
    }

    if ((myPipelineColumns::isColumnActive('servicer1') && (array_key_exists('LM', myPipelineColumns::$PCModuleInfo) || array_key_exists('SS', myPipelineColumns::$PCModuleInfo))) || $fileType == 2 || $fileType == 4) {
        myPipelineColumns::$columnOrder['servicer1'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('servicer', $sortOpt, $orderBy, 'Servicer', true);
    }

    if (myPipelineColumns::isColumnActive('servicer2') || $fileType == 2 || $fileType == 4) {
        myPipelineColumns::$columnOrder['servicer2'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('servicer2', $sortOpt, $orderBy, '2nd Lien Servicer', true);
    }

    if (myPipelineColumns::isColumnActive('mortgageOwner1')) {
        myPipelineColumns::$columnOrder['mortgageOwner1'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('mortgageOwner1', 'Mortgage Type');
    }

    if (myPipelineColumns::isColumnActive('mortgageOwner2')) {
        myPipelineColumns::$columnOrder['mortgageOwner2'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('mortgageOwner2', '2nd Lien Mortgage Type');
    }

    if (myPipelineColumns::isColumnActive('loanType')) {
        myPipelineColumns::$columnOrder['loanType'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('loanType', $sortOpt, $orderBy, 'Loan Type', true);
    }

    if (myPipelineColumns::isColumnActive('loanType2')) {
        myPipelineColumns::$columnOrder['loanType2'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('loanType2', $sortOpt, $orderBy, '2nd Lien Loan Type', true);
    }

    if (myPipelineColumns::isColumnActive('currentBalance')) {
        myPipelineColumns::$columnOrder['currentBalance'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('currentBalance', $sortOpt, $orderBy, 'Current Balance', true);
    }

    if (myPipelineColumns::isColumnActive('currentBalance2')) {
        myPipelineColumns::$columnOrder['currentBalance2'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('currentBalance2', $sortOpt, $orderBy, 'Lien Current Balance', true);
    }

    if (myPipelineColumns::isColumnActive('loanNumber')) {
        myPipelineColumns::$columnOrder['loanNumber'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('loanNumber', $sortOpt, $orderBy, 'Loan #', true);
    }

    if (myPipelineColumns::isColumnActive('loanNumber2')) {
        myPipelineColumns::$columnOrder['loanNumber2'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('loanNumber2', $sortOpt, $orderBy, '2nd Lien Loan #', true);
    }

    if (myPipelineColumns::isColumnActive('lien1Rate')) {
        myPipelineColumns::$columnOrder['lien1Rate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('lien1Rate', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('lien1Rate'), true);
    }

    if (myPipelineColumns::isColumnActive('lien2Rate')) {
        myPipelineColumns::$columnOrder['lien2Rate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('lien2Rate', $sortOpt, $orderBy, '2nd Lien Rate', true);
    }

    if (myPipelineColumns::isColumnActive('lien1Payment')) {
        myPipelineColumns::$columnOrder['lien1Payment'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('lien1Payment', $sortOpt, $orderBy, 'Payment- P+I', true);
    }

    if (myPipelineColumns::isColumnActive('lien2Payment')) {
        myPipelineColumns::$columnOrder['lien2Payment'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('lien2Payment', $sortOpt, $orderBy, 'Lien Payment', true);
    }

    if (myPipelineColumns::isColumnActive('PITIA')) {
        myPipelineColumns::$columnOrder['PITIA'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('PITIA', 'PITIA');
    }

    if (myPipelineColumns::isColumnActive('lien1LPMade')) {
        myPipelineColumns::$columnOrder['lien1LPMade'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('lien1LPMade', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('lien1LPMade'), true);
    }

    if (myPipelineColumns::isColumnActive('lien2LPMade')) {
        myPipelineColumns::$columnOrder['lien2LPMade'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('lien2LPMade', $sortOpt, $orderBy, 'Lien Last Payment Made', true);
    }

    if (myPipelineColumns::isColumnActive('noOfMonthsBehind1') && (array_key_exists('LM', myPipelineColumns::$PCModuleInfo) || array_key_exists('SS', myPipelineColumns::$PCModuleInfo))) {
        myPipelineColumns::$columnOrder['noOfMonthsBehind1'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('noOfMonthsBehind1', $sortOpt, $orderBy, '# Months Behind', true);
    }

    if (myPipelineColumns::isColumnActive('noOfMonthsBehind2')) {
        myPipelineColumns::$columnOrder['noOfMonthsBehind2'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('noOfMonthsBehind2', $sortOpt, $orderBy, '2nd Lien # Months Behind', true);
    }

    if (myPipelineColumns::isColumnActive('lien1BalanceDue')) {
        myPipelineColumns::$columnOrder['lien1BalanceDue'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('lien1BalanceDue', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('lien1BalanceDue'), true);
    }

    if (myPipelineColumns::isColumnActive('loanOriginationDate')) {
        myPipelineColumns::$columnOrder['loanOriginationDate'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('loanOriginationDate', myPipelineColumns::getFieldLabel('loanOriginationDate'));
    }

    if (myPipelineColumns::isColumnActive('lien2BalanceDue')) {
        myPipelineColumns::$columnOrder['lien2BalanceDue'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('lien2BalanceDue', $sortOpt, $orderBy, '2nd Lien Past Due', true);
    }

    if (myPipelineColumns::isColumnActive('occupancy')) {
        myPipelineColumns::$columnOrder['occupancy'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('occupancy', $sortOpt, $orderBy, 'Occupancy', true);
    }

    if (myPipelineColumns::isColumnActive('homeValue')) {
        myPipelineColumns::$columnOrder['homeValue'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('homeValue', $sortOpt, $orderBy, 'Property Value (As-Is)', true);
    }

    if (myPipelineColumns::isColumnActive('costBasis')) {
        myPipelineColumns::$columnOrder['costBasis'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('costBasis', 'Cost Basis');
    }

    if (myPipelineColumns::isColumnActive('appraiser1')) {
        myPipelineColumns::$columnOrder['appraiser1'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('appraiser1', 'Appraisal 1');
    }

    if (myPipelineColumns::isColumnActive('BPO1')) {
        myPipelineColumns::$columnOrder['BPO1'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('BPO1', in_array('HMLO', $PCModuleInfoKeys) ? 'Realtor 1 Value' : 'BPO-1<br>As is Value');
    }

    if (myPipelineColumns::isColumnActive('rehabValue')) {
        myPipelineColumns::$columnOrder['rehabValue'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('rehabValue', myPipelineColumns::getFieldLabel('rehabValue'));
    }

    if (myPipelineColumns::isColumnActive('bedrooms')) {
        myPipelineColumns::$columnOrder['bedrooms'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('bedrooms', $sortOpt, $orderBy, '# of Bedrooms', true);
    }

    if (myPipelineColumns::isColumnActive('bathrooms')) {
        myPipelineColumns::$columnOrder['bathrooms'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('bathrooms', $sortOpt, $orderBy, '# of Baths', true);
    }

    if (myPipelineColumns::isColumnActive('yearBuilt')) {
        myPipelineColumns::$columnOrder['yearBuilt'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('yearBuilt', myPipelineColumns::getFieldLabel('yearBuilt'));
    }

    if (myPipelineColumns::isColumnActive('fileNumber')) {
        myPipelineColumns::$columnOrder['fileNumber'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('fileNumber', $sortOpt, $orderBy, 'File #', true);
    }

    if (myPipelineColumns::isColumnActive('assignedEmployee')) {
        myPipelineColumns::$columnOrder['assignedEmployee'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('assignedEmployee', 'Assigned Employee');
    }

    if (myPipelineColumns::isColumnActive('PCName')) {
        myPipelineColumns::$columnOrder['PCName'] = $headerCount++;
        ?>
        <th class="text-nowrap thClassPCName" id="PCName_0">
            <?php
            $pcCompanyText = '';
            if ($userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager' || ($fileType == 'CFPB' && $allowCFPBAuditing == 1)) {
                $pcCompanyText .= 'Company & Branch ';
            } else {
                $pcCompanyText .= 'PC Name ';
            }
            if ($userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager' || $userRole == 'Super') {
                echo myPipelineColumns::showSortableColumn('PC', $sortOpt, $orderBy, $pcCompanyText);
            } ?>
        </th>
    <?php }

    if (($fileType == 'CFPB' && $allowCFPBAuditing == 1) || $userRole == 'Auditor Manager' || ($fileType == 'CFPB' && $userRole == 'Super')) {
        myPipelineColumns::$columnOrder['AssignedAudit'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('AssignedAudit', 'Assigned Auditor(s)');
    }

    if (myPipelineColumns::isColumnActive('leadSource')) {
        myPipelineColumns::$columnOrder['leadSource'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('leadSource', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('leadSource'), true);
    }

    if (myPipelineColumns::isColumnActive('insuranceCompName')) {
        myPipelineColumns::$columnOrder['insuranceCompName'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('insuranceCompName', 'Insurance Co. Name');
    }

    if (myPipelineColumns::isColumnActive('listingAgentName')) {
        myPipelineColumns::$columnOrder['listingAgentName'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('listingAgentName', 'Listing Loan Officer/Broker');
    }

    if (myPipelineColumns::isColumnActive('priorityLevel')) {
        myPipelineColumns::$columnOrder['priorityLevel'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('priorityLevel', $sortOpt, $orderBy, 'Priority Level', true);
    }

    if (myPipelineColumns::isColumnActive('HMLOLoanType')) {
        myPipelineColumns::$columnOrder['HMLOLoanType'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('HMLOLoanType', $sortOpt, $orderBy, 'Transaction Type', true);
    }

    if (myPipelineColumns::isColumnActive('trialPaymentDate1')) {
        myPipelineColumns::$columnOrder['trialPaymentDate1'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('trialPaymentDate1', $sortOpt, $orderBy, 'First Payment Due', true);
    }

    if (myPipelineColumns::isColumnActive('proInsPolicyExpDate')) {
        myPipelineColumns::$columnOrder['proInsPolicyExpDate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('proInsPolicyExpDate', $sortOpt, $orderBy, 'Any Insurance Expiration Date', true);
    }

    if (myPipelineColumns::isColumnActive('networthOfBusinessOwned')) {
        myPipelineColumns::$columnOrder['networthOfBusinessOwned'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('networthOfBusinessOwned', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('networthOfBusinessOwned'), true);
    }

    if (myPipelineColumns::isColumnActive('borCreditScoreRange')) {
        myPipelineColumns::$columnOrder['borCreditScoreRange'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('borCreditScoreRange', myPipelineColumns::getFieldLabel('borCreditScoreRange'));
    }

    if (myPipelineColumns::isColumnActive('midFico')) {
        myPipelineColumns::$columnOrder['midFico'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('midFico', myPipelineColumns::getFieldLabel('midFicoScore'));
    }

    if (myPipelineColumns::isColumnActive('borExperianScore')) {
        myPipelineColumns::$columnOrder['borExperianScore'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('borExperianScore', myPipelineColumns::getFieldLabel('borExperianScore'));
    }

    if (myPipelineColumns::isColumnActive('borEquifaxScore')) {
        myPipelineColumns::$columnOrder['borEquifaxScore'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('borEquifaxScore', myPipelineColumns::getFieldLabel('borEquifaxScore'));
    }
    if (myPipelineColumns::isColumnActive('borTransunionScore')) {
        myPipelineColumns::$columnOrder['borTransunionScore'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('borTransunionScore', myPipelineColumns::getFieldLabel('borTransunionScore'));
    }

    if (myPipelineColumns::isColumnActive('borNoOfREPropertiesCompleted')) {
        myPipelineColumns::$columnOrder['borNoOfREPropertiesCompleted'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('borNoOfREPropertiesCompleted', $sortOpt, $orderBy, '# of Properties Completed', true);
    }

    if (myPipelineColumns::isColumnActive('borRehabPropCompleted')) {
        myPipelineColumns::$columnOrder['borRehabPropCompleted'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('borRehabPropCompleted', $sortOpt, $orderBy, '# of New Construction Deals Done', true);
    }

    if (myPipelineColumns::isColumnActive('borNoOfOwnProp')) {
        myPipelineColumns::$columnOrder['borNoOfOwnProp'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('borNoOfOwnProp', $sortOpt, $orderBy, '# of Investment Properties', true);
    }

    if (myPipelineColumns::isColumnActive('isBorUSCitizen')) {
        myPipelineColumns::$columnOrder['isBorUSCitizen'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('isBorUSCitizen', $sortOpt, $orderBy, 'US Citizen', true);
    }

    if (myPipelineColumns::isColumnActive('HMLOLender')) {
        myPipelineColumns::$columnOrder['HMLOLender'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('HMLOLender', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('HMLOLender'), true);
    }

    if (myPipelineColumns::isColumnActive('totalProjectCost')) {
        myPipelineColumns::$columnOrder['totalProjectCost'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('totalProjectCost', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('totalProjectCost'), true);
    }

    if (myPipelineColumns::isColumnActive('rehabCostFinanced')) {
        myPipelineColumns::$columnOrder['rehabCostFinanced'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('rehabCostFinanced', myPipelineColumns::getFieldLabel('rehabCostFinanced'));
    }

    if (myPipelineColumns::isColumnActive('LTC')) {
        myPipelineColumns::$columnOrder['LTC'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('LTC', $sortOpt, $orderBy, 'Loan-to-Cost', true);
    }

    if (myPipelineColumns::isColumnActive('ARV')) {
        myPipelineColumns::$columnOrder['ARV'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('ARV', $sortOpt, $orderBy, 'ARV %', true);
    }

    if (myPipelineColumns::isColumnActive('rehabConstructionCost')) {
        myPipelineColumns::$columnOrder['rehabConstructionCost'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('rehabConstructionCost', $sortOpt, $orderBy, 'Rehab/Construction<br>Cost', true);
    }

    if (myPipelineColumns::isColumnActive('acquisitionLTV')) {
        myPipelineColumns::$columnOrder['acquisitionLTV'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('acquisitionLTV', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('acquisitionLTV'), true);
    }

    if (myPipelineColumns::isColumnActive('marketLTV')) {
        myPipelineColumns::$columnOrder['marketLTV'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('marketLTV', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('marketLTV'), true);
    }

    if (myPipelineColumns::isColumnActive('perRehabCostFinanced')) {
        myPipelineColumns::$columnOrder['perRehabCostFinanced'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('perRehabCostFinanced', myPipelineColumns::getFieldLabel('perRehabCostFinanced'));
    }

    if (myPipelineColumns::isColumnActive('exitStrategy')) {
        myPipelineColumns::$columnOrder['exitStrategy'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('perRehabCostFinanced', 'Exit Strategy');
    }

    if (myPipelineColumns::isColumnActive('isHouseProperty')) {
        myPipelineColumns::$columnOrder['isHouseProperty'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('isHouseProperty', myPipelineColumns::getFieldLabel('isHouseProperty'));
    }

    if (myPipelineColumns::isColumnActive('propertyNeedRehab')) {
        myPipelineColumns::$columnOrder['propertyNeedRehab'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('propertyNeedRehab', 'Rehab Required');
    }

    if (myPipelineColumns::isColumnActive('propertyConstructionLevel')) {
        myPipelineColumns::$columnOrder['propertyConstructionLevel'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('propertyConstructionLevel', myPipelineColumns::getFieldLabel('propertyConstructionLevel'));
    }

    if (myPipelineColumns::isColumnActive('lienPosition')) {
        myPipelineColumns::$columnOrder['lienPosition'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('lienPosition', myPipelineColumns::getFieldLabel('lienPosition'));
    }

    if (myPipelineColumns::isColumnActive('propertyCounty')) {
        myPipelineColumns::$columnOrder['propertyCounty'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('propertyCounty', $sortOpt, $orderBy, 'Property County', true);
    }

    if (myPipelineColumns::isColumnActive('propertySqFt')) {
        myPipelineColumns::$columnOrder['propertySqFt'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('propertySqFt', 'Sq Ft');
    }

    if (myPipelineColumns::isColumnActive('propertyURLLink')) {
        myPipelineColumns::$columnOrder['propertyURLLink'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('propertyURLLink', 'URL link to Property');
    }

    if (myPipelineColumns::isColumnActive('taxes1')) {
        myPipelineColumns::$columnOrder['taxes1'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('taxes1', myPipelineColumns::getFieldLabel('taxes1'));
    }

    if (myPipelineColumns::isColumnActive('workflowEvents')) {
        myPipelineColumns::$columnOrder['workflowEvents'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('workflowEvents', 'WorkFlow(s)');
    }

    if (myPipelineColumns::isColumnActive('HMLOLoanTerm')) {
        myPipelineColumns::$columnOrder['HMLOLoanTerm'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('HMLOLoanTerm', 'Loan Term');
    }

    if (myPipelineColumns::isColumnActive('purchasePrice')) {
        myPipelineColumns::$columnOrder['purchasePrice'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('purchasePrice', $sortOpt, $orderBy, 'Purchase Price', true);
    }

    if (myPipelineColumns::isColumnActive('HMLOTotalPayment')) {
        myPipelineColumns::$columnOrder['HMLOTotalPayment'] = $headerCount++;
        ?>
        <th class="text-nowrap thClassHMLOTotalPayment">
            <span class="d-inline p-0">Monthly Payment</span>
        </th>
    <?php }
    if (myPipelineColumns::isColumnActive('netMonthlyPayment')) {
        myPipelineColumns::$columnOrder['netMonthlyPayment'] = $headerCount++;
        ?>
        <th class="text-nowrap thClassNetMonthlyPayment">
            <span class="d-inline p-0">Monthly Payment(PITIA)<br>Loan Info</span>
        </th>
    <?php }

    if (myPipelineColumns::isColumnActive('totalPropertiesPITIA') && glCustomJobForProcessingCompany::showLoanInfoV2($PCID)) {
        myPipelineColumns::$columnOrder['totalPropertiesPITIA'] = $headerCount++;
        ?>
        <th class="text-nowrap thClassTotalPropertiesPITIA">
            <span class="d-inline p-0">PITIA-Loan Info 2</span>
        </th>
        <?php
    }

    if (myPipelineColumns::isColumnActive('ASISValue')) {
        myPipelineColumns::$columnOrder['ASISValue'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('ASISValue', $sortOpt, $orderBy, 'As-Is Value', true);
    }

    if (myPipelineColumns::isColumnActive('presentOccupancyStatus')) {
        myPipelineColumns::$columnOrder['presentOccupancyStatus'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('presentOccupancyStatus', 'Present Occupancy Status');
    }

    if (myPipelineColumns::isColumnActive('propertyCondition')) {
        myPipelineColumns::$columnOrder['propertyCondition'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('propertyCondition', 'Condition');
    }

    if (myPipelineColumns::isColumnActive('appraiser1Value')) {
        myPipelineColumns::$columnOrder['appraiser1Value'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('appraiser1Value', 'Appraisal 1 Value');
    }

    if (myPipelineColumns::isColumnActive('AVM1')) {
        myPipelineColumns::$columnOrder['AVM1'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('AVM1', 'AVM 1 Value');
    }

    if (myPipelineColumns::isColumnActive('dateObtained')) {
        myPipelineColumns::$columnOrder['dateObtained'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('dateObtained', $sortOpt, $orderBy, 'Appraisal 1 Received Date', true);
    }

    if (myPipelineColumns::isColumnActive('appraisal1OrderDate')) {
        myPipelineColumns::$columnOrder['appraisal1OrderDate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('appraisal1OrderDate', $sortOpt, $orderBy, 'Appraisal 1 Order Date', true);
    }

    if (myPipelineColumns::isColumnActive('totalRehabCost')) {
        myPipelineColumns::$columnOrder['totalRehabCost'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('totalRehabCost', $sortOpt, $orderBy, 'Total Est. Rehab Cost', true);
    }

    if (myPipelineColumns::isColumnActive('assessedValue')) {
        myPipelineColumns::$columnOrder['assessedValue'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('assessedValue', 'After Repair Value');
    }

    if (myPipelineColumns::isColumnActive('titleCompanyInfo')) {
        myPipelineColumns::$columnOrder['titleCompanyInfo'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('titleCompanyInfo', 'Title Company Info');
    }

    if (myPipelineColumns::isColumnActive('typeOfSale')) {
        myPipelineColumns::$columnOrder['typeOfSale'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('typeOfSale', 'Type of Sale');
    }

    if (myPipelineColumns::isColumnActive('projectName')) {
        myPipelineColumns::$columnOrder['projectName'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('projectName', myPipelineColumns::getFieldLabel('projectName'));
    }
    if (myPipelineColumns::isColumnActive('MFLoanTermsInfoCnt')) {
        myPipelineColumns::$columnOrder['MFLoanTermsInfoCnt'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('MFLoanTermsInfoCnt', 'Applications Submitted');
    }

    if (myPipelineColumns::isColumnActive('noOfApprovedStatusCnt')) {
        myPipelineColumns::$columnOrder['noOfApprovedStatusCnt'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('noOfApprovedStatusCnt', 'Applications Approved');
    }
    if (myPipelineColumns::isColumnActive('totalApprovedLoanAmt')) {
        myPipelineColumns::$columnOrder['totalApprovedLoanAmt'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('totalApprovedLoanAmt', 'Total Amount Approved');
    }

    if (myPipelineColumns::isColumnActive('bankNumber')) {
        myPipelineColumns::$columnOrder['bankNumber'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('bankNumber', 'Bank Number');
    }

    if (myPipelineColumns::isColumnActive('lienAmount')) {
        myPipelineColumns::$columnOrder['lienAmount'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('lienAmount', 'Lien Amount');
    }
    if (myPipelineColumns::isColumnActive('referringParty')) {
        myPipelineColumns::$columnOrder['referringParty'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('referringParty', $sortOpt, $orderBy, 'Broker Referring Party', true);
    }

    if (myPipelineColumns::isColumnActive('availableBudget')) {
        myPipelineColumns::$columnOrder['availableBudget'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('availableBudget', 'Current Escrow Balance');
    }
    if (myPipelineColumns::isColumnActive('currentLoanBalance')) {
        myPipelineColumns::$columnOrder['currentLoanBalance'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('currentLoanBalance', myPipelineColumns::getFieldLabel('currentLoanBalance'));
    }

    if (myPipelineColumns::isColumnActive('maturityDate')) {
        myPipelineColumns::$columnOrder['maturityDate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('maturityDate', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('maturityDate'), true);
    }

    if (myPipelineColumns::isColumnActive('servicingStatus')) {
        myPipelineColumns::$columnOrder['servicingStatus'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('servicingStatus', $sortOpt, $orderBy, 'Servicing Status', true);
    }
    if (myPipelineColumns::isColumnActive('brokerPointsRate')) {
        myPipelineColumns::$columnOrder['brokerPointsRate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('brokerPointsRate', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('brokerPointsRate'), true);
    }
    if (myPipelineColumns::isColumnActive('brokerPointsValue')) {
        myPipelineColumns::$columnOrder['brokerPointsValue'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('brokerPointsValue', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('brokerPointsValue'), true);
    }
    if (myPipelineColumns::isColumnActive('originationPointsRate')) {
        myPipelineColumns::$columnOrder['originationPointsRate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('originationPointsRate', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('originationPointsRate'), true);
    }
    if (myPipelineColumns::isColumnActive('originationPointsValue')) {
        myPipelineColumns::$columnOrder['originationPointsValue'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('originationPointsValue', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('originationPointsValue'), true);
    }
    if (myPipelineColumns::isColumnActive('payOffDate')) {
        myPipelineColumns::$columnOrder['payOffDate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('payOffDate', $sortOpt, $orderBy, 'Pay Off Date', true);
    }

    if (myPipelineColumns::isColumnActive('loanSaleDate')) {
        myPipelineColumns::$columnOrder['loanSaleDate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('loanSaleDate', $sortOpt, $orderBy, 'Loan Sale Agreement', true);
    }

    if ($userRole == 'REST' || ($fileType == 2 && ($userRole == 'Super' || $userRole == 'Sales')) || (($fileType == 4 || $fileType == 'LA') && ($userRole == 'Super' || $userRole == 'Sales'))) {
        myPipelineColumns::$columnOrder['PC'] = $headerCount++;
        myPipelineColumns::$columnOrder['Branch'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('PC', $sortOpt, $orderBy, 'PC', true);
        echo myPipelineColumns::showSortableColumn('branch', $sortOpt, $orderBy, 'Branch', true);
    }

    if (myPipelineColumns::isColumnActive('agent') || $fileType == 2 || $fileType == 4 || $fileType == 'LA' || $userRole == 'REST') {
        myPipelineColumns::$columnOrder['agent'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('name', $sortOpt, $orderBy, 'Broker', true);
    }

    if (myPipelineColumns::isColumnActive('lender')) {
        myPipelineColumns::$columnOrder['lender'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('lender', 'Lender Contact Details');
    }
    if (myPipelineColumns::isColumnActive('servicer')) {
        myPipelineColumns::$columnOrder['servicer'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('servicer', 'Servicer Contact Details');
    }
    if (myPipelineColumns::isColumnActive('trustee')) {
        myPipelineColumns::$columnOrder['trustee'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('trustee', 'Trustee Contact Details');
    }
    if (myPipelineColumns::isColumnActive('investor')) {
        myPipelineColumns::$columnOrder['investor'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('investor', 'Investor Contact Details');
    }
    if (myPipelineColumns::isColumnActive('loanofficer') || $fileType == 2 || $fileType == 4 || $fileType == 'LA' || $userRole == 'REST') {
        myPipelineColumns::$columnOrder['loanofficer'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('loanOfficerName', $sortOpt, $orderBy, 'Loan Officer', true);
    }

    if (myPipelineColumns::isColumnActive('branch') || $fileType == 4) {
        myPipelineColumns::$columnOrder['branch'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('branchName', $sortOpt, $orderBy, 'Branch', true);
    }

    if ($userRole == 'REST' || ($fileType == 2 && ($userRole == 'Super' || $userRole == 'Sales'))) {
        myPipelineColumns::$columnOrder['orderStatus'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('orderStatus', 'Order Status');
    }
    if ($userRole == 'REST' || $userRole == 'Auditor' || (($fileType == 2 || $fileType == 'LA') && ($userRole == 'Super' || $userRole == 'Sales'))) {
        myPipelineColumns::$columnOrder['price'] = $headerCount++;
        myPipelineColumns::$columnOrder['paymentStatus'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('price', 'Price');
        echo myPipelineColumns::showNonSortableColumn('paymentStatus', 'Payment Status');
    }
    if (($fileType == 4 && ($userRole == 'Super' || $userRole == 'Sales')) || ($fileType == 4 && $userGroup == 'Employee' && $userSeeBilling == 1)) {
        myPipelineColumns::$columnOrder['price'] = $headerCount++;
        myPipelineColumns::$columnOrder['paymentStatus'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('price', 'Price');
        echo myPipelineColumns::showNonSortableColumn('paymentStatus', 'Payment status');
    }

    if (myPipelineColumns::isColumnActive('propertyAppraisalAsIsValue')) {
        myPipelineColumns::$columnOrder['propertyAppraisalAsIsValue'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('propertyAppraisalAsIsValue', myPipelineColumns::getFieldLabel('propertyAppraisalAsIsValue'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalRehabbedValue')) {
        myPipelineColumns::$columnOrder['propertyAppraisalRehabbedValue'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('propertyAppraisalRehabbedValue', myPipelineColumns::getFieldLabel('propertyAppraisalRehabbedValue'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalMonthlyRent')) {
        myPipelineColumns::$columnOrder['propertyAppraisalMonthlyRent'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('propertyAppraisalMonthlyRent', myPipelineColumns::getFieldLabel('propertyAppraisalMonthlyRent'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalJobTypes')) {
        myPipelineColumns::$columnOrder['propertyAppraisalJobTypes'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('propertyAppraisalJobTypes', myPipelineColumns::getFieldLabel('propertyAppraisalJobTypes'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalDateObtained')) {
        myPipelineColumns::$columnOrder['propertyAppraisalDateObtained'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('propertyAppraisalDateObtained', myPipelineColumns::getFieldLabel('propertyAppraisalDateObtained'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalRequestedReturnDate')) {
        myPipelineColumns::$columnOrder['propertyAppraisalRequestedReturnDate'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('propertyAppraisalRequestedReturnDate', myPipelineColumns::getFieldLabel('propertyAppraisalRequestedReturnDate'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalIsRushOrder')) {
        myPipelineColumns::$columnOrder['propertyAppraisalIsRushOrder'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('propertyAppraisalIsRushOrder', myPipelineColumns::getFieldLabel('propertyAppraisalIsRushOrder'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalOrderDate')) {
        myPipelineColumns::$columnOrder['propertyAppraisalOrderDate'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('propertyAppraisalOrderDate', myPipelineColumns::getFieldLabel('propertyAppraisalOrderDate'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalComments')) {
        myPipelineColumns::$columnOrder['propertyAppraisalComments'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('propertyAppraisalComments', myPipelineColumns::getFieldLabel('propertyAppraisalComments'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalEffectiveDate')) {
        myPipelineColumns::$columnOrder['propertyAppraisalEffectiveDate'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('propertyAppraisalEffectiveDate', myPipelineColumns::getFieldLabel('propertyAppraisalEffectiveDate'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalInspectionDate')) {
        myPipelineColumns::$columnOrder['propertyAppraisalInspectionDate'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('propertyAppraisalInspectionDate', myPipelineColumns::getFieldLabel('propertyAppraisalInspectionDate'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalExpectedDeliveryDate')) {
        myPipelineColumns::$columnOrder['propertyAppraisalExpectedDeliveryDate'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('propertyAppraisalExpectedDeliveryDate', myPipelineColumns::getFieldLabel('propertyAppraisalExpectedDeliveryDate'));
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalExpectedDeliveryDelay')) {
        myPipelineColumns::$columnOrder['propertyAppraisalExpectedDeliveryDelay'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('propertyAppraisalExpectedDeliveryDelay', myPipelineColumns::getFieldLabel('propertyAppraisalExpectedDeliveryDelay'));
    }

    if (myPipelineColumns::isColumnActive('coBorrowerName')) {
        myPipelineColumns::$columnOrder['coBorrowerName'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('coBorrowerName', $sortOpt, $orderBy, 'Co-Borrower Full Name', true);
    }
    if (myPipelineColumns::isColumnActive('fundingDate')) {
        myPipelineColumns::$columnOrder['fundingDate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('fundingDate', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('fundingDate'), true);
    }
    if (myPipelineColumns::isColumnActive('clearToCloseBy')) {
        myPipelineColumns::$columnOrder['clearToCloseBy'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('clearToCloseBy', $sortOpt, $orderBy, 'Clear To Close By', true);
    }
    if (myPipelineColumns::isColumnActive('propertyFloodZone')) {
        myPipelineColumns::$columnOrder['propertyFloodZone'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('propertyFloodZone', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('propertyFloodZone'), true);
    }
    if (myPipelineColumns::isColumnActive('requiredInsurance')) {
        myPipelineColumns::$columnOrder['requiredInsurance'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('requiredInsurance', $sortOpt, $orderBy, 'Required Insurance', true);
    }
    if (myPipelineColumns::isColumnActive('HMDAActionTaken')) {
        myPipelineColumns::$columnOrder['HMDAActionTaken'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('HMDAActionTaken', $sortOpt, $orderBy, 'HMDA Action Taken', true);
    }
    if (myPipelineColumns::isColumnActive('noOfPropertiesAcquiring')) {
        myPipelineColumns::$columnOrder['noOfPropertiesAcquiring'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('noOfPropertiesAcquiring', $sortOpt, $orderBy, 'Cross Properties', true);
    }
    if (myPipelineColumns::isColumnActive('titleOrderDate')) {
        myPipelineColumns::$columnOrder['titleOrderDate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('titleOrderDate', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('titleOrderedDate'), true);
    }
    if (myPipelineColumns::isColumnActive('lenderInternalNotes')) {
        myPipelineColumns::$columnOrder['lenderInternalNotes'] = $headerCount++;
        if ($PCID == glPCID::PCID_PROD_CV3) {
            echo myPipelineColumns::showSortableColumn('lenderInternalNotes', $sortOpt, $orderBy, 'Funding Comments', true);
        } else {
            echo myPipelineColumns::showSortableColumn('lenderInternalNotes', $sortOpt, $orderBy, 'Lender Internal Notes', true);
        }
    }
    if (myPipelineColumns::isColumnActive('referralPoints')) {
        myPipelineColumns::$columnOrder['referralPoints'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('referralPoints', $sortOpt, $orderBy, 'Referral Points', true);

    }
    if (myPipelineColumns::isColumnActive('warehouseInvestor')) {
        myPipelineColumns::$columnOrder['warehouseInvestor'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('warehouseInvestor', $sortOpt, $orderBy, 'Warehouse Investor', true);
    }
    if (myPipelineColumns::isColumnActive('wireAmountSent')) {
        myPipelineColumns::$columnOrder['wireAmountSent'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('wireAmountSent', $sortOpt, $orderBy, 'Wire Amount Sent', true);
    }

    if (myPipelineColumns::isColumnActive('eCoaWaiverStatus')) {
        myPipelineColumns::$columnOrder['eCoaWaiverStatus'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('eCoaWaiverStatus', $sortOpt, $orderBy, 'ECOA Waiver Status', true);
    }

    if (myPipelineColumns::isColumnActive('rateLockPeriod')) {
        myPipelineColumns::$columnOrder['rateLockPeriod'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('rateLockPeriod', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('rateLockPeriod'), true);
    }
    if (myPipelineColumns::isColumnActive('rateLockDate')) {
        myPipelineColumns::$columnOrder['rateLockDate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('rateLockDate', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('rateLockDate'), true);
    }
    if (myPipelineColumns::isColumnActive('rateLockExpirationDate')) {
        myPipelineColumns::$columnOrder['rateLockExpirationDate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('rateLockExpirationDate', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('rateLockExpirationDate'), true);
    }
    if (myPipelineColumns::isColumnActive('rateLockExtension')) {
        myPipelineColumns::$columnOrder['rateLockExtension'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('rateLockExtension', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('rateLockExtension'), true);
    }
    if (myPipelineColumns::isColumnActive('rateLockNotes')) {
        myPipelineColumns::$columnOrder['rateLockNotes'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('rateLockNotes', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('rateLockNotes'), true);
    }

    if (myPipelineColumns::isColumnActive('primaryAppraisalEcoaDeliveryDate')) {
        myPipelineColumns::$columnOrder['primaryAppraisalEcoaDeliveryDate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('primaryAppraisalEcoaDeliveryDate', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('primaryAppraisalEcoaDeliveryDate'), true);
    }

    if (myPipelineColumns::isColumnActive('propertyAppraisalSupplementalProductFormType1')) {
        myPipelineColumns::$columnOrder['propertyAppraisalSupplementalProductFormType1'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('propertyAppraisalSupplementalProductFormType1', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('propertyAppraisalSupplementalProductFormType') . ' 1', true);
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalSupplementalProductFormType2')) {
        myPipelineColumns::$columnOrder['propertyAppraisalSupplementalProductFormType2'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('propertyAppraisalSupplementalProductFormType2', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('propertyAppraisalSupplementalProductFormType') . ' 2', true);
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalSupplementalProductFormType3')) {
        myPipelineColumns::$columnOrder['propertyAppraisalSupplementalProductFormType3'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('propertyAppraisalSupplementalProductFormType3', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('propertyAppraisalSupplementalProductFormType') . ' 3', true);
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalSupplementalProductEffectiveDate1')) {
        myPipelineColumns::$columnOrder['propertyAppraisalSupplementalProductEffectiveDate1'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('propertyAppraisalSupplementalProductEffectiveDate1', $sortOpt, $orderBy, 'Supplemental Product Effective Date 1', true);
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalSupplementalProductEffectiveDate2')) {
        myPipelineColumns::$columnOrder['propertyAppraisalSupplementalProductEffectiveDate2'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('propertyAppraisalSupplementalProductEffectiveDate2', $sortOpt, $orderBy, 'Supplemental Product Effective Date 2', true);
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalSupplementalProductEffectiveDate3')) {
        myPipelineColumns::$columnOrder['propertyAppraisalSupplementalProductEffectiveDate3'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('propertyAppraisalSupplementalProductEffectiveDate3', $sortOpt, $orderBy, 'Supplemental Product Effective Date 3', true);
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalSupplementalProductEcoaADeliveryDate1')) {
        myPipelineColumns::$columnOrder['propertyAppraisalSupplementalProductEcoaADeliveryDate1'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('propertyAppraisalSupplementalProductEcoaADeliveryDate1', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('propertyAppraisalSupplementalProductEcoaADeliveryDate') . ' 1', true);
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalSupplementalProductEcoaADeliveryDate2')) {
        myPipelineColumns::$columnOrder['propertyAppraisalSupplementalProductEcoaADeliveryDate2'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('propertyAppraisalSupplementalProductEcoaADeliveryDate2', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('propertyAppraisalSupplementalProductEcoaADeliveryDate') . ' 2', true);
    }
    if (myPipelineColumns::isColumnActive('propertyAppraisalSupplementalProductEcoaADeliveryDate3')) {
        myPipelineColumns::$columnOrder['propertyAppraisalSupplementalProductEcoaADeliveryDate3'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('propertyAppraisalSupplementalProductEcoaADeliveryDate3', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('propertyAppraisalSupplementalProductEcoaADeliveryDate') . ' 3', true);
    }

    if (myPipelineColumns::isColumnActive('brokerPartnerType')) {
        myPipelineColumns::$columnOrder['brokerPartnerType'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('brokerPartnerType', $sortOpt, $orderBy, 'Broker/Partner Type', true);
    }
    if (myPipelineColumns::isColumnActive('totalPropertiesLoanAmount') && glCustomJobForProcessingCompany::showLoanInfoV2($PCID)) {
        myPipelineColumns::$columnOrder['totalPropertiesLoanAmount'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('totalPropertiesLoanAmount', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('totalPropertiesLoanAmount'), true);
    }
    if (myPipelineColumns::isColumnActive('PSAClosingDate')) {
        myPipelineColumns::$columnOrder['PSAClosingDate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('PSAClosingDate', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('PSAClosingDate'), true);
    }
    if (myPipelineColumns::isColumnActive('buildingAnalysisOutstanding')) {
        myPipelineColumns::$columnOrder['buildingAnalysisOutstanding'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('buildingAnalysisOutstanding', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('buildingAnalysisOutstanding'), true);
    }
    if (myPipelineColumns::isColumnActive('buildingAnalysisNeed')) {
        myPipelineColumns::$columnOrder['buildingAnalysisNeed'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('buildingAnalysisNeed', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('buildingAnalysisNeed'), true);
    }
    if (myPipelineColumns::isColumnActive('buildingAnalysisDueDate')) {
        myPipelineColumns::$columnOrder['buildingAnalysisDueDate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('buildingAnalysisDueDate', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('buildingAnalysisDueDate'), true);
    }
    if (myPipelineColumns::isColumnActive('targetSubmissionDate')) {
        myPipelineColumns::$columnOrder['targetSubmissionDate'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('targetSubmissionDate', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('targetSubmissionDate'), true);
    }
    if (myPipelineColumns::isColumnActive('authorizationStatus')) {
        myPipelineColumns::$columnOrder['authorizationStatus'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('authorizationStatus', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('authorizationStatus'), true);
    }
    if (myPipelineColumns::isColumnActive('VOMStatus')) {
        myPipelineColumns::$columnOrder['VOMStatus'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('VOMStatus', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('VOMStatus'), true);
    }
    if (myPipelineColumns::isColumnActive('payoffStatus')) {
        myPipelineColumns::$columnOrder['payoffStatus'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('payoffStatus', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('payoffStatus'), true);
    }
    if (myPipelineColumns::isColumnActive('trackRecord')) {
        myPipelineColumns::$columnOrder['trackRecord'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('trackRecord', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('trackRecord'), true);
    }

   if (myPipelineColumns::isColumnActive('welcomeCallStatus')) {
        myPipelineColumns::$columnOrder['welcomeCallStatus'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('welcomeCallStatus', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('welcomeCallStatus'), true);
    }
   if (myPipelineColumns::isColumnActive('propertyAppraisalStatus')) {
        myPipelineColumns::$columnOrder['propertyAppraisalStatus'] = $headerCount++;
        echo myPipelineColumns::showSortableColumn('propertyAppraisalStatus', $sortOpt, $orderBy, myPipelineColumns::getFieldLabel('propertyAppraisalStatus'), true);
    }
   if (myPipelineColumns::isColumnActive('LOISentDate')) {
        myPipelineColumns::$columnOrder['LOISentDate'] = $headerCount++;
        echo myPipelineColumns::showNonSortableColumn('LOISentDate', myPipelineColumns::getFieldLabel('LOISentDate'));
    }


    if (!($fileType == 2 || $fileType == 'CFPB' || $userGroup == 'CFPB Auditor' || $userGroup == 'Auditor Manager')) {
        if (($userRole == 'Super' && ($PCID > 0 || sizeof(Arrays::explodeIntVals($executiveId)) || sizeof(Arrays::explodeIntVals($brokerNumb)))) ||
            $userRole == 'Manager' ||
            ($userRole == 'Super' && ($fileType == 4 || $fileType == 'LA')) ||
            (PageVariables::$allowToMassUpdate)) {
            myPipelineColumns::$columnOrder['massUpdate'] = $headerCount++;
            ?>
            <th class="text-nowrap thClassMultiSelect  text-center" style="width: 120px">
                                <span class="checkbox-inline w-100px">
                                    <label for="bulk" class="checkbox checkbox-outline checkbox-square tooltipClass"
                                           title="Click to select all (<?php echo $allECount ?>) files returned from search."><input
                                                class="newCheck"
                                                type="checkbox"
                                                name="bulk"
                                                onclick="selectBelowFiles();"
                                                id="bulk"/><span></span>All</label>
                                             <button type="button"
                                                     onclick="selectedFiles()"
                                                     title="Select the files below and click here to do a mass update."
                                                     class="btn btn-primary btn-sm ml-2 tooltipClass">
                                                    <i class="fas fa-tasks icon-nm"></i></button>
                           </span>
                <a class="btn1 d-none" id="massUpdateButton"
                   href='#'
                   data-href="<?php echo CONST_URL_POPS; ?>massUpdateForm.php"
                   data-wsize='modal-lg'
                   data-name='Mass Update'
                   data-toggle='modal' data-target='#exampleModal1'
                   data-id=''>
                    <i class="ki ki-solid-plus icon-xs"></i>
                </a>
            </th>
            <?php
        }

        if (myPipelineColumns::$allowEmailCampaignPC
            && PageVariables::$allowEmailCampaign == 1
            && $activeFile == 1) {
            if ($allECount < $noOfRecordsPerPage) {
                $noOfRecordsPerPage = $allECount;
            }
            myPipelineColumns::$columnOrder['massEmail'] = $headerCount++;
            ?>
            <th class="thClassMultiSelctLimit p-0 ">
                <a class="btn1 d-none" id="massEmailTemplateButton"
                   href='#'
                   data-href="<?php echo CONST_URL_POPS; ?>massUpdateEmailTemplateForm.php"
                   data-wsize='modal-xl'
                   data-name='Mass Email Template'
                   data-toggle='modal' data-target='#exampleModal1'
                   data-id=''>
                    <i class="ki ki-solid-plus icon-nm"></i>
                </a>
                <span class="checkbox-inline w-150px">
                                        <label for="checkAllFileRowCnt"
                                               class="checkbox checkbox-outline checkbox-square tooltipClass ml-2"
                                               title="Click to select all (<?php echo $noOfRecordsPerPage ?>) files below."
                                        ><input
                                                    class="newCheck selectBelowAll "
                                                    type="checkbox"
                                                    name="checkAllFileRowCnt"
                                                    onchange="selectBelowFilesForEmailTemplate('checkAllFile','checkAllFileRowCnt','');"
                                                    id="checkAllFileRowCnt"/>
                                            <span></span>
                                            <?php echo $noOfRecordsPerPage ?>
                                        </label>
                                        <label class="checkbox checkbox-outline checkbox-square tooltipClass ml-2"
                                               title="Click to select all (<?php echo $allECount ?>) files returned from search."
                                               for="checkAllFile">
                                            <input class="newCheck "
                                                   type="checkbox"
                                                   name="checkAllFile"
                                                   onclick="selectBelowFilesForEmailTemplate('checkAllFileRowCnt','checkAllFile','Confirm');"
                                                   id="checkAllFile"/>
                                            <span></span>All</label>

                    <!-- email_schedule Removed From Below Class -->

                               <button type="button" id="popsSelectAll"
                                       onclick="selectedFilesForEmailTemplate()"
                                       title="Select the files below and click here to email."
                                       class="btn btn-primary btn-sm ml-2 tooltipClass">
                                                    <i class="fas fa-calendar-plus icon-nm"></i></button>
                                    </span>
            </th>
        <?php }
    } ?>
</tr>
</thead>
