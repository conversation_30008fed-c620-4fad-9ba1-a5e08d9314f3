<?php
global $moduleValue;

use models\composite\oBranch\getBranchModules;
use models\composite\oBranch\getBranchServices;
use models\composite\oBranch\getMyDetails;
use models\composite\oPrimaryStatus\getPCPrimaryStatus;
use models\constants\gl\glEntityTypeArray;
use models\constants\gl\glHMLOCreditScoreRange;
use models\constants\gl\glHMLOExitStrategy;
use models\constants\gl\glHMLOLoanTerms;
use models\constants\gl\glPCID;
use models\constants\gl\glPropertyCondition;
use models\constants\gl\gltypeOfHMLOLoanRequesting;
use models\constants\GpropertyTypeNumbArray;
use models\cypher;
use models\Request;
use models\standard\Arrays;
use models\standard\Strings;
use models\composite\oLoanStages\LoanStagesManager;

session_start();
require '../includes/util.php';

$glEntityTypeArray = glEntityTypeArray::$glEntityTypeArray;
$gltypeOfHMLOLoanRequesting = gltypeOfHMLOLoanRequesting::$gltypeOfHMLOLoanRequesting;
$glHMLOExitStrategy = glHMLOExitStrategy::$glHMLOExitStrategy;
$glHMLOCreditScoreRange = glHMLOCreditScoreRange::$glHMLOCreditScoreRange;
$glHMLOLoanTerms = glHMLOLoanTerms::$glHMLOLoanTerms;
$glPropertyCondition = glPropertyCondition::$glPropertyCondition;
$loanFileStages = LoanStagesManager::getCachedStages();

$responseArray = [];
/**
 * Processing Company Id.
 */
$PCID = Strings::GetSess('PCID');
$glHMLOCreditScoreRange = glHMLOCreditScoreRange::getCreditScoreRange($PCID);
/**
 * Branch Id.
 */
$executiveId = cypher::myDecryption(Arrays::getArrayValue('executiveId', $_REQUEST));
$action = Request::isset('action') ? Request::GetClean('action') : null;
/**
 * File Type / Module.
 */
$fileType = Arrays::getArrayValue('fileType', $_REQUEST);
/**
 * Branch or file type.
 */
$option = Arrays::getArrayValue('option', $_REQUEST);

if ($executiveId > 0) {

    /**
     * Branch selected file type
     */

    /**
     * Get Branch Information.
     */
    $getMyDetails = getMyDetails::getReport([
        'executiveId' => $executiveId,
    ]);
    $branchArray = $getMyDetails[$executiveId];

    $modulesRequested = getBranchModules::getReport([
        'branchID' => $executiveId,
    ]);
    $responseArray['modulesRequested'] = $modulesRequested[$executiveId];

    /**
     * Branch selected service type
     */
    $servicesRequested = getBranchServices::getReport([
        'branchID'   => $executiveId,
        'moduleCode' => $fileType,
    ]);
    $responseArray['servicesRequested'] = $servicesRequested;

    // if ($option == 'branchId') {
    /**
     * Processing Company selected Status
     */

    $PCStatusInfo = getPCPrimaryStatus::getReport([
        'PCID'       => $PCID,
        'opt1'       => 'list',
        'keyNeeded'  => 'n',
        'moduleCode' => $fileType,
    ]);
    if (isset($PCStatusInfo['primaryStatusInfo'])) {
        $PCStatusInfo = $PCStatusInfo['primaryStatusInfo'];
    }
    $responseArray['PCStatusInfo'] = $PCStatusInfo;
    /**
     * Get Branch information.
     */
    $responseArray['branchInfo'] = $branchArray;
    /**
     * Transaction Type
     */
    $responseArray['transactionType'] = $gltypeOfHMLOLoanRequesting;
    /**
     * States
     */
    $responseArray['States'] = Arrays::fetchStates();
    /**
     * Entity Type
     */
    $responseArray['entityType'] = $glEntityTypeArray;
    /**
     * Exit Strategy
     */
    $responseArray['exitStrategy'] = $glHMLOExitStrategy;
    /**
     * Loan Term
     */
    $responseArray['loanTerm'] = $glHMLOLoanTerms;
    /**
     * Property Condition
     */
    $responseArray['propertyCondition'] = $glPropertyCondition;
    /**
     * Property Type
     */
    if ($action == 'leadPosting') {
        asort(GpropertyTypeNumbArray::$GpropertyTypeNumbArray2);
    }
    $responseArray['propertyType'] = GpropertyTypeNumbArray::$GpropertyTypeNumbArray2;
    /**
     * Credit score
     */
    $responseArray['creditScore'] = $glHMLOCreditScoreRange;

    /**
     * Loan File Stages
     */
    if($loanFileStages) {
        $loanFileStages = array_map(function($stage) {
            return [
                'systemName' => $stage->stageSystemName,
                'displayName' => $stage->stageDisplayName,
            ];
         }, $loanFileStages);
    }
}
$responseArray['loanFileStages'] = $loanFileStages;

echo json_encode($responseArray);
exit;
