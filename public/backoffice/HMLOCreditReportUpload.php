<?php
global $allowToEdit, $LMRResponseId, $executiveId, $LMRId, $userGroup, $borrowerName, $docArray,
       $recordDate, $oldFPCID, $userTimeZone,
       $userRole, $allowToDeleteUploadedDocs;

use models\cypher;
use models\PageVariables;
use models\standard\Dates;
use models\standard\Directories;
use models\standard\Strings;

?>
<!-- HMLOCreditReportUpload.php -->
<div class="card card-custom borrowerEmploymentInfo">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label">
                Credit Report Upload&nbsp;

            </h3>
        </div>
        <div class="card-toolbar ">
            <?php
            if ($allowToEdit) { ?>
                <a title="Upload document"
                   data-href="<?php echo CONST_URL_POPS; ?>uploadDocs.php"
                   data-toggle='modal' data-target='#exampleModal1' data-wsize='modal-xl'
                   data-id='rId=<?php echo cypher::myEncryption($LMRResponseId) ?>&exID=<?php echo cypher::myEncryption($executiveId) ?>&LMRId=<?php echo cypher::myEncryption($LMRId) ?>&userType=<?php echo cypher::myEncryption($userGroup) ?>&userNumber=<?php echo cypher::myEncryption(PageVariables::$userNumber) ?>&picOpt=3'
                   class="btn btn-sm btn-success btn-text-primary  btn-icon ml-4 tooltipClass"
                   data-name="File: <?php echo $borrowerName ?>  > Upload Documents">
                    <i class="icon-md fas fa-upload"></i>
                </a>
            <?php } ?>
            <a href="javascript:void(0);"
               class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass "
               data-card-tool="toggle"
               data-section="borrowerEmploymentInfo"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </a>
            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none" data-card-tool="reload"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </a>
            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary d-none" data-card-tool="remove"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Remove Card">
                <i class="ki ki-close icon-nm"></i>
            </a>
        </div>
    </div>
    <div class="card-body borrowerEmploymentInfo_body">
        <div class="form-group row">
            <div class="clear pad5 UploadFileDocList"></div>
            <div class="col-md-12">
                <input type="hidden" name="borrowerName" id="borrowerName" value="<?php echo $borrowerName ?>"/>
                <table style="width:100%;"
                       class="table table-hover  table-bordered table-condensed table-sm table-vertical-center"
                       id="UploadFileDocList">
                    <thead class="thead-light">
                    <tr>
                        <th class="text-center">#</th>
                        <th>&nbsp;</th>
                        <th>&nbsp;</th>
                        <th>Document Name</th>
                        <th>File Type / Size</th>
                        <th>Uploaded Info</th>
                        <th>&nbsp;</th>
                    </tr>
                    </thead>

                    <?php
                    if (count($docArray) == 0) {
                        ?>
                        <tr>
                            <td colspan="7" style="text-align: center;" class="font-weight-bold">No Records Found</td>
                        </tr>
                        <?php
                    }
                    $clientIdArray = [];
                    $empIdArray = [];
                    $branchIdArray = [];
                    $agentIdArray = [];
                    $getFileDocPathInfoArray = [];
                    $resFileSizeInfo = [];
                    for ($i = 0; $i < count($docArray); $i++) {
                        $userId = 0;
                        $userType = '';
                        $userId = $docArray[$i]['uploadedBy'];
                        $userType = $docArray[$i]['uploadingUserType'];

                        if ($userType == 'Processor' || $userType == 'Employee') {
                            $empIdArray[] = $userId;
                        }
                        if ($userType == 'LMR Executive' || $userType == 'Branch') {
                            $branchIdArray[] = $userId;
                        }
                        if ($userType == 'Broker' || $userType == 'Agent') {
                            $agentIdArray[] = $userId;
                        }
                        if ($userType == 'Client' && $userId > 0) {
                            $clientIdArray[] = $userId;
                        }

                        $docName = '';
                        $fP = '';
                        $docName = trim($docArray[$i]['docName']);

                        $tempRecDate = str_replace('-', '', $recordDate);
                        $fP = $oldFPCID . '/' . date('Y', strtotime($tempRecDate)) . '/' . date('m', strtotime($tempRecDate)) . '/' . date('d', strtotime($tempRecDate)) . '/' . $LMRId . '/upload/' . $docName;
                        $getFileDocPathInfoArray['UD']['file_path' . $i] = $fP;
                    }

                    if (count($docArray) > 0) {

                        $getFileDocPathInfoArray['UD']['cnt'] = count($docArray);

                        require 'fetchDocsInfoFromRemote.php';
                    }


                    $empInfoArray = [];
                    $branchInfoArray = [];
                    $agentInfoArray = [];
                    $clientInfoArray = [];
                    if (count($empIdArray) > 0) {
                        $ip = ['empId' => implode(',', $empIdArray)];
                        $empInfoArray = \models\composite\oEmployee\getMyDetails::getReport($ip);
                    }
                    if (count($branchIdArray) > 0) {
                        $ip = ['executiveId' => implode(',', $branchIdArray)];
                        $branchInfoArray = \models\composite\oBranch\getMyDetails::getReport($ip);
                    }
                    if (count($agentIdArray) > 0) {
                        $ip = ['agentId' => implode(',', $agentIdArray)];
                        $agentInfoArray = \models\composite\oBroker\getMyDetails::getReport($ip);
                    }

                    if (count($clientIdArray) > 0) {
                        $ip = ['clientId' => implode(',', $clientIdArray)];
                        $clientInfoArray = \models\composite\oClient\getMyDetails::getReport($ip);
                    }
                    for ($doc = 0; $doc < count($docArray); $doc++) {
                        $tempDocArray = [];
                        $flatNotes = '';
                        $docName = '';
                        $displayDocName = '';
                        $docId = 0;
                        $myUploadedBy = '';
                        $myUploadedRole = '';
                        $docCategory = '';

                        $docName = trim($docArray[$doc]['docName']);
                        $displayDocName = trim($docArray[$doc]['displayDocName']);
                        $uploadedDate = trim($docArray[$doc]['uploadedDate']);
                        $userId = trim($docArray[$doc]['uploadedBy']);
                        $userType = trim($docArray[$doc]['uploadingUserType']);
                        $docCategory = trim($docArray[$doc]['docCategory']);
                        $docId = trim($docArray[$doc]['docID']);
                        $fileType = trim($docArray[$doc]['fileType']);

                        $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
                        $ipArray['outputZone'] = $userTimeZone;
                        $ipArray['inputTime'] = $uploadedDate;
                        $uploadedDate = Dates::timeZoneConversion($ipArray);

                        $flatNotes = trim($docArray[$doc]['notes']);

                        if (Dates::IsEmpty($uploadedDate)) {
                            $uploadedDate = '';
                        } else {
                            $uploadedDate = Dates::formatDateWithRE($uploadedDate, 'YMD_HMS', 'm/d/Y h:i A');
                        }
                        if ($displayDocName == '' || $displayDocName == NULL) $displayDocName = $docName;
                        $tempRecDate = str_replace('-', '', $recordDate);
                        $folderName = $oldFPCID . '/' . date('Y', strtotime($tempRecDate)) . '/' . date('m', strtotime($tempRecDate)) . '/' . date('d', strtotime($tempRecDate));
                        $fileValue = $LMRId;
                        $fP = $folderName . '/' . $fileValue . '/upload';
                        $uploadDocUrl = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($docName) . '&fd=' . cypher::myEncryption(CONST_PATH_LMR_FILE_DOCS . $fP) . '&opt=enc';
                        $fP .= '/' . $docName;
                        $imgName = CONST_DOC_PATH . CONST_PATH_LMR_FILE_DOCS . $fP;
                        $dispFS = 0;
                        $tempFileSize = 0;

                        if (count($resFileSizeInfo) > 0) {
                            if (array_key_exists('UD', $resFileSizeInfo) && count($resFileSizeInfo['UD']) > 0) {
                                if (array_key_exists('file_size_' . $doc, $resFileSizeInfo['UD'])) $tempFileSize = trim($resFileSizeInfo['UD']['file_size_' . $doc]);
                            }
                        }
                        $dispFS = trim(Directories::calculateAndDisplayRemoteFileSize($tempFileSize));

                        if ($userType == 'Processor' || $userType == 'Employee') {
                            if (array_key_exists($userId, $empInfoArray)) {
                                $tempEmpInfo = $empInfoArray[$userId];
                                $myUploadedBy = trim($tempEmpInfo['processorName']);
                                $myUploadedRole = trim($tempEmpInfo['role']);
                            }

                            if ($myUploadedRole == '' || $myUploadedRole == NULL) $myUploadedRole = 'Staff';
                            $myUploadedBy = $myUploadedBy . ' (' . $myUploadedRole . ')';
                        } else if ($userType == 'LMR Executive' || $userType == 'Branch') {
                            if (array_key_exists($userId, $branchInfoArray)) {
                                $tempBranchInfo = $branchInfoArray[$userId];
                                $myUploadedBy = trim($tempBranchInfo['LMRExecutive']);
                            }
                            $myUploadedBy = $myUploadedBy . ' (Branch)';
                        } else if ($userType == 'Broker' || $userType == 'Agent') {
                            if (array_key_exists($userId, $agentInfoArray)) {
                                $tempAgentInfo = $agentInfoArray[$userId];
                                $myUploadedBy = trim($tempAgentInfo['brokerName']) . ' (Agent)';
                            }
                        } else if ($userType == 'Client') {
                            if (array_key_exists($userId, $clientInfoArray)) {
                                $tempClientInfo = [];
                                $tempClientInfo = $clientInfoArray[$userId];

                                $myUploadedBy = trim($tempClientInfo['clientFName']) . ' ' . trim($tempClientInfo['clientLName']) . ' (' . $userType . ')';
                            }
                        }
                        ?>
                        <tr class="bbg" id="upload_<?php echo $docId ?>">
                            <td class="text-center"><?php echo($doc + 1); ?></td>
                            <td class="text-center">
                                <?php if ($dispFS > 0) { ?>
                                    <a class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 propertyPictures tooltipClass"

                                       style="text-decoration:none;" rel="nofollow" target="_blank"
                                       href="<?php echo $uploadDocUrl; ?>"
                                       title="Click to view Credit Report Picture">
                                        <i class="fa fa-eye"></i>
                                    </a>
                                <?php } ?>
                            </td>
                            <td class="text-center">
                                <?php
                                if ($allowToEdit) { ?>
                                    <div id="<?php echo $docId ?>_editFile">
                                        <a class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 tooltipClass"
                                           data-toggle='modal' data-target='#exampleModal1' data-wsize='modal-xl'
                                           data-href="<?php echo CONST_URL_POPS; ?>editFileDocInfo.php"
                                           data-id='LMRId=<?php echo cypher::myEncryption($LMRId) ?>&amp;docId=<?php echo cypher::myEncryption($docId) ?>&amp;displayDocName=<?php echo cypher::myEncryption($displayDocName) ?>&amp;docCategory=<?php echo cypher::myEncryption($docCategory) ?>&amp;userId=<?php echo cypher::myEncryption($userId) ?>'
                                           data-name='File: <?php echo htmlentities($borrowerName, ENT_QUOTES, 'UTF-8') ?> > Doc Info'
                                           title="Click to edit File Doc Info"><i class="fa fa-edit"></i> </a>
                                    </div>
                                <?php } ?>
                            </td>
                            <td id="<?php echo $docId ?>_docName">
                                <div class="force-wrap"><?php echo stripslashes($displayDocName) ?></div>
                            </td>
                            <td>(<?php echo strtoupper($fileType) ?>) <br>
                                <?php
                                if ($dispFS > 0) {
                                    echo '(' . $dispFS . ')';
                                } else {
                                    ?>
                                    <div class="with-children-tip"><a class="fa fa-warning fa-2x tip-bottom icon-red"
                                                                      style="text-decoration:none"
                                                                      href="javascript:void(0);"
                                                                      title="File is corrupted/File not found"></a>
                                    </div>
                                    <?php
                                }
                                ?>
                            </td>
                            <td>
                                <div class="force-wrap"><?php echo ucwords($myUploadedBy) ?>
                                    <br><?php echo $uploadedDate . ' - ' . $userTimeZone ?></div>
                            </td>
                            <td>
                                <?php
                                if ($allowToEdit) {
                                    if (($userGroup == 'Employee' && $userRole == 'Manager')
                                        || ($userGroup == 'Super')
                                        || ($userGroup == 'Employee' && $userRole != 'Manager' && $allowToDeleteUploadedDocs == 1 && $userId == PageVariables::$userNumber)
                                        || ($userGroup == 'Branch' && $allowToDeleteUploadedDocs == 1 && $userId == PageVariables::$userNumber)
                                        || ($userGroup == 'Agent' && $allowToDeleteUploadedDocs == 1 && $userId == PageVariables::$userNumber)
                                    ) { ?>
                                        <a class="btn btn-xs btn-danger btn-text-primary  btn-icon m-1 tooltipClass"
                                           title="Click To Delete"
                                           href="javascript:deleteUploadFileDoc('<?php echo $docId ?>', '<?php echo cypher::myEncryption($userId) ?>', this, '<?php echo cypher::myEncryption($LMRId) ?>',  'upload_<?php echo $docId ?>', 'UploadFileDocList');"><i
                                                    class="flaticon2-trash"></i> </a>
                                        <?php
                                    }
                                }
                                ?>
                            </td>
                        </tr>
                        <?php
                    }
                    ?>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- HMLOCreditReportUpload.php -->


