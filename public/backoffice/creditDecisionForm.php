<?php

use models\constants\gl\glDate;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\loanForm;
use models\lendingwise\tblCreditDecisionForm;
use models\standard\Dates;
use models\standard\Strings;
use models\Controllers\LMRequest\creditDecision as creditDecisionController;

require_once 'LMRequest/creditDecision/sections/creditDecision.php';
require_once 'LMRequest/creditDecision/sections/secondSign.php';
require_once 'LMRequest/creditDecision/sections/clearToClose.php';
require_once 'LMRequest/creditDecision/sections/dealDesk.php';
require_once 'LMRequest/creditDecision/sections/exception.php';


$creditDecisionFormData = creditDecisionController::$creditDecisionFormData;
if(!$creditDecisionFormData) {
    $creditDecisionFormData = [
        new tblCreditDecisionForm(),
    ];
}
?>
<input type="hidden" name="creditDecisionFormId" id="creditDecisionFormId" value="<?php echo $creditDecisionFormData->id;?>">
<div class="clearfix"></div>
<div class="card-body">
    <div class="form-group">
        <div class="col-lg-12 row">
            <div class="col-lg-3">
                <div class="checkbox-inline">
                    <label class="checkbox font-weight-bold">
                        <input type="checkbox"
                               name="doNotSell"
                               value="Yes" <?php echo Strings::isChecked('Yes', $creditDecisionFormData->doNotSell);?> >
                        <span></span> &nbsp;&nbsp;&nbsp; DO NOT SELL</label>
                </div>
            </div>
            <div class="col-lg-3">
                <label class="font-weight-bold" for="doNotSellDate">Do Not Sell Date</label>
                <div class="input-group">
                    <div class="input-group-prepend actionDate">
                        <span class="input-group-text">
                            <i class="fa fa-calendar text-primary icon-lg"></i>
                        </span>
                    </div>
                    <input
                        class="form-control input-sm dateNewClass"
                        type="text"
                        name="doNotSellDate"
                        id="doNotSellDate"
                        value="<?php echo Dates::formatDateWithRE($creditDecisionFormData->doNotSellDate, 'YMD H:i:s', 'm/d/Y');?>"
                        data-before-creation-date="true"
                        data-start-date="<?php echo glDate::getCreationDate(LMRequest::File()->recordDate); ?>"
                        placeholder="MM/DD/YYYY"
                        maxlength="10"
                        autocomplete="off" >
                </div>
            </div>
            <div class="col-lg-3">
                <label class="font-weight-bold" for="doNotSellReason">Do Not Sell Reason</label>
                <textarea name="doNotSellReason" id="doNotSellReason" class="form-control validateMaxLength"
                          maxlength="<?php echo loanForm::getFieldLength('doNotSellReason','tblCreditDecisionForm'); ?>"
                         ><?php echo $creditDecisionFormData->doNotSellReason;?></textarea>
            </div>
        </div>
    </div>
</div>
<div class="clearfix"></div>
<div class="col-md-12" id="buttons">
    <div class="col-md-12 text-center mt-5 mb-5">
        <input type="submit" class="btn btn-primary btnSave" name="btnSave" id="saveBtn" value="Save">
        <input type="submit" class="btn btn-primary btnSave ml-1" name="btnSave" id="saveNextBtn"
               value="Save & Next"
               onclick="if(this.disabled===false) {return true;} else {return false;}">
    </div>
</div>
<?php
Strings::includeMyScript(['/backoffice/LMRequest/creditDecision/js/creditDecision.js']);
?>
