<?php
global $fileTab, $allowToEdit, $tabIndex, $liabilitiesInfo, $LMRId, $publicUser, $activeTab;

use models\Controllers\backoffice\LMRequest;
use models\Controllers\loanForm;
use models\CustomField;
use models\cypher;
use models\lendingwise\tblFile;
use models\PageVariables;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Strings;

$ischk = count($secArr = BaseHTML::sectionAccess2(['sId' => 'LIE', 'opt' => $fileTab, 'activeTab' => $activeTab])) > 0 ? 'checked' : '';
loanForm::pushSectionID('LIE');

if ($publicUser == 1) {
    if ($LMRId > 0 && $ischk != '') {
        $ischk = 'checked';
    } else {
        $ischk = '';
    }
}
?>
<!-- liabilitiesSection.php -->
<div class="card card-custom LiabilitiesDivCard LIE HMLOLoanInfoSections
<?php if (trim($ischk) == 'checked') {
    echo 'secShow';
} else {
    echo 'secHide';
} ?>" style="<?php echo $HMLOLoanInfoSectionsDisp; ?>">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label">
                <?php echo BaseHTML::getSectionHeading('LIE'); ?>
            </h3>
            <?php if (trim(BaseHTML::getSectionTooltip('LIE')) != '') { ?>&nbsp;
                <i class="popoverClass fas fa-info-circle text-primary "
                   data-html="true"
                   data-content="<?php echo BaseHTML::getSectionTooltip('LIE'); ?>"></i>
            <?php } ?>
        </div>
        <div class="card-toolbar ">
            <a href="javascript:void(0);"
               class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
               data-card-tool="toggle"
               data-section="LiabilitiesDivCard"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </a>
            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none"
               data-card-tool="reload"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </a>
            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary d-none" data-card-tool="remove"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Remove Card">
                <i class="ki ki-close icon-nm"></i>
            </a>

        </div>
    </div>
    <div class="card-body LiabilitiesDivCard_body">
        <div class="form-group row alimonyChildSupportSeparatePayments_disp
                        <?php echo loanForm::showField('alimonyChildSupportSeparatePayments'); ?>">
            <?php echo loanForm::label('alimonyChildSupportSeparatePayments', 'col-md-6 '); ?>
            <div class="col-md-6">

                <div class="input-group">
                    <div class="input-group-prepend">
                                <span class="input-group-text">
                                    $
                                </span>
                    </div>
                    <input type="text" name="alimonyChildSupportSeparatePayments"
                           id="alimonyChildSupportSeparatePayments"
                           placeholder="0.00"
                           onblur="currencyConverter(this, this.value);"
                           value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('alimonyChildSupportSeparatePayments',
                               'fileLOAssetsInfo')); ?>"
                           size="20" maxlength="40" autocomplete="off" class="form-control"
                           tabindex="<?php echo $tabIndex++; ?>"/>
                </div>
            </div>
        </div>
        <div class="form-group row childSupportPayments_disp
                        <?php echo loanForm::showField('childSupportPayments'); ?>">
            <?php echo loanForm::label('childSupportPayments', 'col-md-6 '); ?>
            <div class="col-md-6">
                <div class="input-group">
                    <div class="input-group-prepend">
                                <span class="input-group-text">
                                    $
                                </span>
                    </div>
                    <input type="text" name="childSupportPayments"
                           placeholder="0.00"
                           id="childSupportPayments"
                           onblur="currencyConverter(this, this.value);"
                           value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('childSupportPayments', 'fileLOAssetsInfo')); ?>"
                           size="20" maxlength="40" autocomplete="off" class="form-control"
                           tabindex="<?php echo $tabIndex++; ?>"/>
                </div>
            </div>
        </div>
        <div class="form-group row separateMaintenancePayments_disp
                        <?php echo loanForm::showField('separateMaintenancePayments'); ?>">
            <?php echo loanForm::label('separateMaintenancePayments', 'col-md-6 '); ?>
            <div class="col-md-6">
                <div class="input-group">
                    <div class="input-group-prepend">
                                <span class="input-group-text">
                                    $
                                </span>
                    </div>
                    <input type="text" name="separateMaintenancePayments"
                           placeholder="0.00"
                           id="separateMaintenancePayments"
                           onblur="currencyConverter(this, this.value);"
                           value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('separateMaintenancePayments', 'fileLOAssetsInfo')); ?>"
                           size="20" maxlength="40" autocomplete="off" class="form-control"
                           tabindex="<?php echo $tabIndex++; ?>"/>
                </div>
            </div>
        </div>
        <div class="form-group row jobRelatedExpense_disp
                        <?php echo loanForm::showField('jobRelatedExpense'); ?>">
            <?php echo loanForm::label('jobRelatedExpense', 'col-md-6 '); ?>
            <div class="col-md-6">
                <div class="input-group">
                    <div class="input-group-prepend">
                            <span class="input-group-text">
                                $
                            </span>
                    </div>
                    <input type="text" name="jobRelatedExpense" id="jobRelatedExpense"
                           placeholder="0.00"
                           onblur="currencyConverter(this, this.value);"
                           value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('jobRelatedExpense', 'fileLOAssetsInfo')); ?>"
                           class="form-control"
                           size="20" maxlength="40" autocomplete="off"
                           tabindex="<?php echo $tabIndex++; ?>"/>
                </div>

            </div>
        </div>
        <div class="form-group row otherPayments_disp
                        <?php echo loanForm::showField('otherPayments'); ?>">
            <?php echo loanForm::label('otherPayments', 'col-md-6 '); ?>
            <div class="col-md-6">
                <div class="input-group">
                    <div class="input-group-prepend">
                                <span class="input-group-text">
                                    $
                                </span>
                    </div>
                    <input type="text" name="otherPayments"
                           id="otherPayments"
                           placeholder="0.00"
                           onblur="currencyConverter(this, this.value);"
                           value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('otherPayments', 'fileLOAssetsInfo')); ?>"
                           size="20" maxlength="40" autocomplete="off" class="form-control"
                           tabindex="<?php echo $tabIndex++; ?>"/>
                </div>
            </div>
        </div>
        <div class="form-group row owedTo_disp
                        <?php echo loanForm::showField('owedTo'); ?>">
            <?php echo loanForm::label('owedTo', 'col-md-6 '); ?>
            <div class="col-md-6"><input type="text" name="owedTo" id="owedTo"
                                         value="<?php echo htmlentities(Strings::showField('owedTo', 'fileLOAssetsInfo')); ?>"
                                         class="form-control"
                                         size="20"
                                         maxlength="40"
                                         autocomplete="off" tabindex="<?php echo $tabIndex++; ?>"/></div>
        </div>
        <?php require 'addLOLiabilitiesInfo.php'; ?>
        <div class="form-group row d-none" id="LOLiabilitiesInfo">
            <div class="col-md-12">
                <?php
                if (count($liabilitiesInfo) > 0) {
                    ?>
                    <table class="table table-hover  table-bordered table-condensed table-sm table-vertical-center"
                           width="100%">
                        <thead class="thead-light">
                        <tr>
                            <th>#</th>
                            <?php if ($allowToEdit) { ?>
                                <th></th>
                                <?php
                            }
                            ?>
                            <th style="border-right:1px solid #ffffff;">&nbsp;Name & Address of Company</th>
                            <th style="border-right:1px solid #ffffff;">&nbsp;Monthly Payment</th>
                            <th style="border-right:1px solid #ffffff;">&nbsp;Months Left to Pay</th>
                            <th style="border-right:1px solid #ffffff;">&nbsp;Unpaid Balance</th>
                            <?php if ($allowToEdit) { ?>
                                <th>&nbsp;</th>
                                <?php
                            } ?>
                        </tr>
                        </thead>
                        <tbody>
                        <?php
                        for ($d = 0;
                        $d < count($liabilitiesInfo);
                        $d++) {
                        $nameAddrOfCompany = '';
                        $monthlyPayment = '';
                        $monthsLeftToPay = '';
                        $unpaidBalance = '';
                        $encLMRId = '';
                        $encLID = '';
                        $cls = '';

                        $LMRId = '';
                        $LOLID = '';
                        $nameAddrOfCompany = $liabilitiesInfo[$d]['nameAddrOfCompany'];
                        $monthlyPayment = $liabilitiesInfo[$d]['monthlyPayment'];
                        $monthsLeftToPay = $liabilitiesInfo[$d]['monthsLeftToPay'];
                        $unpaidBalance = $liabilitiesInfo[$d]['unpaidBalance'];

                        $LMRId = $liabilitiesInfo[$d]['fileID'];
                        $LOLID = $liabilitiesInfo[$d]['LOLID'];

                        $encLMRId = cypher::myEncryption($LMRId);
                        $encLID = cypher::myEncryption($LOLID);
                        if ((($d + 1) % 2) == 0) $cls = 'even';

                        ?>
                        <tr class="<?php echo $cls ?>">
                            <td class="text-center"><?php echo($d + 1) ?></td>
                            <?php if ($allowToEdit) { ?>
                                <td class="text-center">
                                    <div id="buttonDisp">
                                        <a class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 tooltipClass"
                                           title="Click to edit"
                                           href="javascript:editLOLiabilitiesInfo('<?php echo $LMRId; ?>','<?php echo $LOLID; ?>');"><i
                                                    class="fa fa-edit"></i></a>
                                    </div>
                                </td>
                                <?php
                            }
                            ?>
                            <td><?php echo $nameAddrOfCompany; ?></td>
                            <td><?php echo Currency::formatDollarAmountWithDecimal($monthlyPayment); ?></td>
                            <td><?php echo Currency::formatDollarAmountWithDecimal($monthsLeftToPay); ?></td>
                            <td><?php echo Currency::formatDollarAmountWithDecimal($unpaidBalance); ?></td>
                            <?php if ($allowToEdit) { ?>
                                <td>
                                    <a class="btn btn-xs btn-danger btn-text-primary  btn-icon m-1 tooltipClass"
                                       style="text-decoration:none;"
                                       href="javascript:deleteLiabilitiesInfo('<?php echo $LMRId ?>', '<?php echo $LOLID ?>');"
                                       alt="Click to delete" title="Click to delete">
                                        <i class="flaticon2-trash"></i>
                                    </a>
                                </td>
                                <?php
                            }
                            ?>
                        </tr>
                        </tbody>
                        <?php
                        }
                        ?>
                    </table>
                    <?php
                }
                ?>
            </div>
        </div>

        <?php echo CustomField::RenderForTabSection(
            PageVariables::$PCID,
            tblFile::class,
            LMRequest::$LMRId,
            'LIE',
            $fileTab,
            $activeTab,
            LMRequest::myFileInfo()->getFileTypes(),
            LMRequest::myFileInfo()->getLoanPrograms()
        ); ?>
    </div>
</div>

<!-- liabilitiesSection.php -->
