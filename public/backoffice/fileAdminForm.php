<?php
global $LMRId, $fileInfo, $isMF, $LMRClientTypeInfo, $userRole, $PCID,
       $op, $isSLMOnly, $fileStatus, $isHMLO, $showStartLoanNumber, $tabIndex,
       $isHOALien, $PCStatusInfo, $executiveId, $PCSubStatusInfo,
       $fileSubstatusInfo, $substatusArr, $userGroup, $fileWorkflowArray, $PCWFStepsArray, $viewPrivateNotes, $LMRResponseId,
       $borrowerName, $substatusArray, $allowCFPBAuditing, $allowUserToUpdateCFPBFile, $viewPublicNotes;

use models\constants\accessSecondaryWFPC;
use models\constants\closedDispositionArray;
use models\constants\eoaWaiverStatus;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glHMLOLeadPostPC;
use models\constants\gl\glNewShortWebformBranch;
use models\constants\gl\glNewShortWebformPC;
use models\constants\gl\glSelectedFUModulesNotesTypeIdArray;
use models\constants\gl\glSelectedNotesTypeIdArray;
use models\constants\gl\glShowANINumberPC;
use models\constants\gl\glUSAPCPrimaryStatusId;
use models\constants\resolutionTypeArray;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\loanForm;
use models\cypher;
use models\lendingwise\tblWelcomeCallStatus;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;
use models\tabIndex;

if (!$tabIndex) {
    $tabIndex = tabIndex::next();
}

$accessSecondaryWFPC = accessSecondaryWFPC::$accessSecondaryWFPC;
$glSelectedNotesTypeIdArray = glSelectedNotesTypeIdArray::$glSelectedNotesTypeIdArray;
$closedDispositionArray = closedDispositionArray::$closedDispositionArray;
$glSelectedFUModulesNotesTypeIdArray = glSelectedFUModulesNotesTypeIdArray::$glSelectedFUModulesNotesTypeIdArray;
$glShowANINumberPC = glShowANINumberPC::$glShowANINumberPC;
$glNewShortWebformPC = glNewShortWebformPC::$glNewShortWebformPC;
$glNewShortWebformBranch = glNewShortWebformBranch::$glNewShortWebformBranch;
$glHMLOLeadPostPC = glHMLOLeadPostPC::$glHMLOLeadPostPC;
$glUSAPCPrimaryStatusId = glUSAPCPrimaryStatusId::$glUSAPCPrimaryStatusId;
$resolutionTypeArray = resolutionTypeArray::$resolutionTypeArray;

$receivedDate = '';
$closedDate = '';
$assignProcLink = '';
$assignedEmpLink = '';
$salesDate = '';
$attorneyPhone1 = '';
$attorneyPhone2 = '';
$attorneyPhone3 = '';
$attorneyPhoneExt = '';
$attorneyFax1 = '';
$attorneyFax2 = '';
$attorneyFax3 = '';
$attorneyState = '';
$trialModReceivedDate = '';
$firstTrialPaymentDate = '';
$AssignedStaffInfo = [];
$assignAppendComma = '';
$coBorrowerName = '';
$brokerName = '';
$attorneyPhone = '';
$BorrowerName = '';
$attorneyFax = '';
$leadSource = '';
$formatAttorneyFax = '';
$formatAttorneyPhone = '';
$borrowerCallBack = '';
$lenderCallBack = '';
$lenderSubmission = '';
$processorCommentsArray = [];
$attorneyFaxArray = [];
$QAInfo = [];
$LMRInfoResultArray = [];
$HAFADate = '';
$welcomeCallDate = '';
$totalCallsPlaced = 0;
$bankCallCompleted = '';
$appealDate = '';
$closingDate = '';
$attorneyReviewDate = '';
$trialPaymentDate1 = '';
$trialPaymentDate2 = '';
$trialPaymentDate3 = '';
$firstModPaymentAmt = '';
$retainerDate = '';
$escalationDate = '';
$denialDate = '';
$ANINo = '';
$fileSecondaryWFStatus = [];
$WFStatusArray = [];
$PSID = 0;
$YRF4506TDate1 = '';
$YRF4506TDate2 = '';
$YRF4506TDate3 = '';
$YRF4506TDate4 = '';
$maturityDate = '';
$fileCreatedDate = '';
$myNoteTypes = [];
$notesType = $showSysGenNote = '';
$hearingDate = '';

$myFileInfo = LMRequest::myFileInfo();

$processorCommentsArray = $myFileInfo->processorComments(
    PageVariables::$viewPrivateNotes,
    PageVariables::$viewPublicNotes
);


if (array_key_exists($LMRId, $fileInfo)) $myFileInfo = $fileInfo[$LMRId];
if (array_key_exists('AssignedBOStaffInfo', $myFileInfo)) $AssignedStaffInfo = $myFileInfo['AssignedBOStaffInfo'];

if (array_key_exists('fileSecondaryWFStatus', $myFileInfo)) $fileSecondaryWFStatus = $myFileInfo['fileSecondaryWFStatus'];
if (array_key_exists('QAInfo', $myFileInfo)) $QAInfo = $myFileInfo['QAInfo'];

if (count($myFileInfo) > 0) {
    $notesType = trim(Strings::showField('notesType', 'PCInfo'));
    $showSysGenNote = Strings::showField('showSysGenNote', 'PCInfo');
    if ($notesType != '') $myNoteTypes = explode(',', $notesType);
}
if (!count($myNoteTypes) > 0) {
    if ($isMF == 1) {
        $myNoteTypes = $glSelectedFUModulesNotesTypeIdArray;
    } else {
        $myNoteTypes = $glSelectedNotesTypeIdArray;
    }

}
/** Fetch Q+A info **/

/* Hide the Workflow Actions custom functionality for all users as per Daniel Request. changed on Feb 11, 2016 
* Hide the fetch function - Viji on Jul 21, 2017 */
/*
	if (count($fileSecondaryWFStatus)>0) {
		$WFIDsArray  = array();	$WFIDs = '';	$WFSIDs = '';
		$WFSIDsArray = array();
		$tempArray = array();
		$tempArray = array_keys($fileSecondaryWFStatus);
		for($i=0;$i<count($tempArray);$i++) {
			array_push($WFIDsArray, trim($tempArray[$i]));
			array_push($WFSIDsArray, $fileSecondaryWFStatus[trim($tempArray[$i])]['WFSID']);
		}
		if (count($WFIDsArray)>0) {
			$WFIDs = implode(', ', $WFIDsArray);
		}
		if (count($WFSIDsArray)>0) {
			$WFSIDs = implode(', ', $WFSIDsArray);
		}
		$oWorkflow = new oWorkflow();
		$WFStatusArray = $oWorkflow->getPCSecondaryWFRule(array('WFSID' => $WFSIDs));
	}*/
$isLM = 0;
$isSS = 0;
$isDL = 0;
$isFC = 0;
for ($i = 0; $i < count($LMRClientTypeInfo); $i++) {
    if (trim($LMRClientTypeInfo[$i]['ClientType']) == 'LM' || trim($LMRClientTypeInfo[$i]['ClientType']) == 'SS' || trim($LMRClientTypeInfo[$i]['ClientType']) == 'DL' || trim($LMRClientTypeInfo[$i]['ClientType']) == 'FC') {
        $isLM = 1;
        $isSS = 1;
        $isDL = 1;
        $isFC = 1;
    }
}
$assignedWorkflow = [];
$assignedWFIDs = [];

if (count($myFileInfo) > 0) {
    $receivedDate = Strings::showField('receivedDate', 'LMRInfo');
    $closedDate = Strings::showField('closedDate', 'LMRInfo');
    $salesDate = Strings::showField('salesDate', 'LMRInfo');
    $attorneyPhone = Strings::showField('attorneyPhone', 'QAInfo');
    $attorneyFax = Strings::showField('attorneyFax', 'QAInfo');
    $trialModReceivedDate = Strings::showField('trialModReceivedDate', 'ResponseInfo');
    $firstModPaymentAmt = Strings::showField('firstModPaymentAmt', 'ResponseInfo');
    $brokerName = Strings::showField('firstName', 'BrokerInfo') . ' ' . Strings::showField('lastName', 'BrokerInfo');
    $BorrowerName = Strings::showField('borrowerName', 'LMRInfo') . ' ' . Strings::showField('borrowerLName', 'LMRInfo');
    $coBorrowerName = Strings::showField('coBorrowerFName', 'LMRInfo') . ' ' . Strings::showField('coBorrowerLName', 'LMRInfo');
    $borrowerCallBack = Strings::showField('borrowerCallBack', 'ResponseInfo');
    $HAFADate = Strings::showField('HAFADate', 'ResponseInfo');
    $welcomeCallDate = Strings::showField('welcomeCallDate', 'ResponseInfo');
    $bankCallCompleted = Strings::showField('bankCallCompleted', 'ResponseInfo');
    $appealDate = Strings::showField('appealDate', 'ResponseInfo');
    $lenderCallBack = Strings::showField('lenderCallBack', 'ResponseInfo');
    $lenderSubmission = Strings::showField('lenderSubmissionDate', 'ResponseInfo');
    $foreclosureStartDate = Strings::showField('foreclosureStartDate', 'ResponseInfo');
    $foreclosureSentDate = Strings::showField('foreclosureSentDate', 'ResponseInfo');
    $attorneyReviewDate = Strings::showField('attorneyReviewDate', 'ResponseInfo');
    $trialPaymentDate1 = Strings::showField('trialPaymentDate1', 'ResponseInfo');
    $trialPaymentDate2 = Strings::showField('trialPaymentDate2', 'ResponseInfo');
    $trialPaymentDate3 = Strings::showField('trialPaymentDate3', 'ResponseInfo');
    $retainerDate = Strings::showField('retainerDate', 'ResponseInfo');
    $escalationDate = Strings::showField('escalationDate', 'ResponseInfo');
    $denialDate = Strings::showField('denialDate', 'ResponseInfo');
    $totalCallsPlaced = Strings::showField('totalCallsPlaced', 'ResponseInfo');
    $ANINo = Strings::showField('ANINo', 'ResponseInfo');
    $closingDate = Strings::showField('closingDate', 'QAInfo');
    $hearingDate = Strings::showField('hearingDate', 'QAInfo');
    $PSID = Strings::showField('primeStatusId', 'ResponseInfo');
    $fileCreatedDate = Dates::formatDateWithRE(Strings::showField('recordDate', 'LMRInfo'), 'YMD', 'm/d/Y');
}
$formatAttorneyPhone = Strings::formatPhoneNumber($attorneyPhone);
$formatAttorneyFax = Strings::formatPhoneNumber($attorneyFax);

$attorneyPhoneArray = Strings::splitPhoneNumber($attorneyPhone);
if (count($attorneyPhoneArray) > 0) {
    $attorneyPhone1 = trim($attorneyPhoneArray['No1']);
    $attorneyPhone2 = trim($attorneyPhoneArray['No2']);
    $attorneyPhone3 = trim($attorneyPhoneArray['No3']);
    $attorneyPhoneExt = trim($attorneyPhoneArray['Ext']);
}
$attorneyFaxArray = Strings::splitPhoneNumber($attorneyFax);
if (count($attorneyFaxArray) > 0) {
    $attorneyFax1 = trim($attorneyFaxArray['No1']);
    $attorneyFax2 = trim($attorneyFaxArray['No2']);
    $attorneyFax3 = trim($attorneyFaxArray['No3']);
}
if (Dates::IsEmpty($attorneyReviewDate)) {
    $attorneyReviewDate = '';
} else {
    $attorneyReviewDate = Dates::formatDateWithRE($attorneyReviewDate, 'YMD', 'm/d/Y');
}

if (Dates::IsEmpty($trialPaymentDate1)) {
    $trialPaymentDate1 = '';
} else {
    $trialPaymentDate1 = Dates::formatDateWithRE($trialPaymentDate1, 'YMD', 'm/d/Y');
}

if (Dates::IsEmpty($trialPaymentDate2)) {
    $trialPaymentDate2 = '';
} else {
    $trialPaymentDate2 = Dates::formatDateWithRE($trialPaymentDate2, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($trialPaymentDate3)) {
    $trialPaymentDate3 = '';
} else {
    $trialPaymentDate3 = Dates::formatDateWithRE($trialPaymentDate3, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($retainerDate)) {
    $retainerDate = '';
} else {
    $retainerDate = Dates::formatDateWithRE($retainerDate, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($hearingDate)) {
    $hearingDate = '';
} else {
    $hearingDate = Dates::formatDateWithRE($hearingDate, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($denialDate)) {
    $denialDate = '';
} else {
    $denialDate = Dates::formatDateWithRE($denialDate, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($closingDate)) {
    $closingDate = '';
} else {
    $closingDate = Dates::formatDateWithRE($closingDate, 'YMD', 'm/d/Y');
}

if (Dates::IsEmpty($receivedDate)) {
    $receivedDate = '';
} else {
    $receivedDate = Dates::formatDateWithRE($receivedDate, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($closedDate)) {
    $closedDate = '';
} else {
    $closedDate = Dates::formatDateWithRE($closedDate, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($salesDate)) {
    $salesDate = '';
} else {
    $salesDate = Dates::formatDateWithRE($salesDate, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($trialModReceivedDate)) {
    $trialModReceivedDate = '';
} else {
    $trialModReceivedDate = Dates::formatDateWithRE($trialModReceivedDate, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($firstTrialPaymentDate)) {
    $firstTrialPaymentDate = '';
} else {
    $firstTrialPaymentDate = Dates::formatDateWithRE($firstTrialPaymentDate, 'YMD', 'm/d/Y');
}

if (Dates::IsEmpty($borrowerCallBack)) {
    $borrowerCallBack = '';
} else {
    $borrowerCallBack = Dates::formatDateWithRE($borrowerCallBack, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($HAFADate)) {
    $HAFADate = '';
} else {
    $HAFADate = Dates::formatDateWithRE($HAFADate, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($welcomeCallDate)) {
    $welcomeCallDate = '';
} else {
    $welcomeCallDate = Dates::formatDateWithRE($welcomeCallDate, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($bankCallCompleted)) {
    $bankCallCompleted = '';
} else {
    $bankCallCompleted = Dates::formatDateWithRE($bankCallCompleted, 'YMD', 'm/d/Y');
}

if (Dates::IsEmpty($lenderCallBack)) {
    $lenderCallBack = '';
} else {
    $lenderCallBack = Dates::formatDateWithRE($lenderCallBack, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($lenderSubmission)) {
    $lenderSubmission = '';
} else {
    $lenderSubmission = Dates::formatDateWithRE($lenderSubmission, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($appealDate)) {
    $appealDate = '';
} else {
    $appealDate = Dates::formatDateWithRE($appealDate, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($foreclosureStartDate)) {
    $foreclosureStartDate = '';
} else {
    $foreclosureStartDate = Dates::formatDateWithRE($foreclosureStartDate, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($foreclosureSentDate)) {
    $foreclosureSentDate = '';
} else {
    $foreclosureSentDate = Dates::formatDateWithRE($foreclosureSentDate, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($escalationDate)) {
    $escalationDate = '';
} else {
    $escalationDate = Dates::formatDateWithRE($escalationDate, 'YMD', 'm/d/Y');
}

if (count($QAInfo)) {
    $YRF4506TDate1 = Strings::showField('YRF4506TDate1', 'QAInfo'); // YRF - Year requested For 4506T
    $YRF4506TDate2 = Strings::showField('YRF4506TDate2', 'QAInfo');
    $YRF4506TDate3 = Strings::showField('YRF4506TDate3', 'QAInfo');
    $YRF4506TDate4 = Strings::showField('YRF4506TDate4', 'QAInfo');

    if (Dates::IsEmpty($YRF4506TDate1)) {
        $YRF4506TDate1 = '';
    } else {
        $YRF4506TDate1 = Dates::formatDateWithRE($YRF4506TDate1, 'YMD', 'm/d/Y');
    }
    if (Dates::IsEmpty($YRF4506TDate2)) {
        $YRF4506TDate2 = '';
    } else {
        $YRF4506TDate2 = Dates::formatDateWithRE($YRF4506TDate2, 'YMD', 'm/d/Y');
    }
    if (Dates::IsEmpty($YRF4506TDate3)) {
        $YRF4506TDate3 = '';
    } else {
        $YRF4506TDate3 = Dates::formatDateWithRE($YRF4506TDate3, 'YMD', 'm/d/Y');
    }
    if (Dates::IsEmpty($YRF4506TDate4)) {
        $YRF4506TDate4 = '';
    } else {
        $YRF4506TDate4 = Dates::formatDateWithRE($YRF4506TDate4, 'YMD', 'm/d/Y');
    }
}
$staffCnt = count($AssignedStaffInfo);
for ($m = 0; $m < $staffCnt; $m++) {
    $empName = '';
    $empStatus = 1;
    $empRole = '';
    $AID = 0;
    $empName = trim($AssignedStaffInfo[$m]['processorName']);
    $empStatus = trim($AssignedStaffInfo[$m]['activeStatus']);
    $empRole = trim($AssignedStaffInfo[$m]['role']);
    $AID = trim($AssignedStaffInfo[$m]['AID']);
    $assignedEmpLink .= "<span id=ass_$AID>";
    $assignedEmpLink .= $assignAppendComma;
    if ($empStatus == 0) $assignedEmpLink .= '<span class=red>';
    if (trim($empRole) != '') $assignedEmpLink .= '<b>' . $empRole . ': </b> ';
    $assignedEmpLink .= $empName;
    if ($empStatus == 0) $assignedEmpLink .= '</span>';
    $assignedEmpLink .= '</span><br>';
}
/* Allow USA Private Money, LLC Agent's to update Admin section under Lead Status - Apr 06, 2017  */
if ($userRole == 'Agent' && in_array($PCID, $glHMLOLeadPostPC) && in_array($PSID, $glUSAPCPrimaryStatusId)) {
    $allowToUpdateFileAdminSection = 1;
}
$allowToEdit = false;
if ($allowToUpdateFileAdminSection == 1 && $op != 'view' && $userRole != 'Auditor') $allowToEdit = true;

//
$disclosureSentDate = LMRequest::myFileInfo()->getFileAdminInfo()->disclosureSentDate ?? '';
$disclosureSentDate = Dates::formatDateWithRE($disclosureSentDate, 'YMD', 'm/d/Y');

?>
<?php if ($isMF == 1) {
} else { ?>
    <input type="hidden" name="OSID" id="OSID"
           value="<?php echo Strings::showField('primeStatusId', 'ResponseInfo') ?>">
    <input type="hidden" name="isSLMOnly" id="isSLMOnly" value="<?php echo cypher::myEncryption($isSLMOnly) ?>">
    <input type="hidden" name="previousPSName" id="previousPSName" value="<?php echo $fileStatus ?>">
    <input type="hidden" name="isHMLO" id="isHMLO" value="<?php echo $isHMLO ?>">
    <input type="hidden" name="primaryStatus" id="primaryStatus" value="">
    <div class="card card-custom fileinfoAdmin">
        <div class="card-header card-header-tabs-line bg-gray-100  ">
            <div class="card-title">
                <h3 class="card-label">
                    File Info
                </h3>
            </div>

            <div class="card-toolbar ">
                <a href="javascript:void(0);"
                   class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                   data-card-tool="toggle"
                   data-section="fileinfoAdmin"
                   data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                    <i class="ki ki-arrow-down icon-nm"></i>
                </a>
                <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none" data-card-tool="reload"
                   data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                    <i class="ki ki-reload icon-nm"></i>
                </a>
                <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary d-none" data-card-tool="remove"
                   data-toggle="tooltip" data-placement="top" title="" data-original-title="Remove Card">
                    <i class="ki ki-close icon-nm"></i>
                </a>
            </div>
        </div>
        <div class="card-body fileinfoAdmin_body">

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold">
                            File ID :
                        </label>
                        <div class="col-md-7"><?php echo $LMRId; ?></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold">
                            File Created Date :
                        </label>
                        <div class="col-md-7"><?php echo $fileCreatedDate; ?></div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold">
                            <?php if ($isHMLO == 1) { ?>Loan number <?php } else { ?>File Number <?php } ?>
                        </label>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <?php if ($isHMLO == 1) { ?>
                                    <?php if ($showStartLoanNumber == 1 && !glCustomJobForProcessingCompany::generateFileIDAsLoanNumber($PCID)) { ?>
                                        <div class="input-group">
                                            <input type="text" class="form-control" name="loanNumber"
                                                   id="loanNumber"
                                                   value="<?php echo htmlspecialchars(Strings::showField('loanNumber', 'LMRInfo')); ?>"
                                                   maxlength="45" size="25" autocomplete="off"
                                                   tabindex="<?php echo $tabIndex++; ?>" readonly>
                                            <?php if (Strings::showField('loanNumber', 'LMRInfo') > 0) {
                                            } else { ?>
                                                <div class="input-group-append"><span class="input-group-text"
                                                                                      id="getLoanNo"><a
                                                                style="text-decoration:none;"
                                                                class="fa fa-refresh"
                                                                onclick="getAvailableLoanNo('<?php echo cypher::myEncryption($PCID) ?>','loanNumber');"
                                                                title="Click to auto create loan number."><i
                                                                    class="tooltipClass flaticon2-reload text-success"></i></a></span>
                                                </div>
                                            <?php } ?>
                                        </div>
                                    <?php } else { ?>
                                        <input type="text" name="loanNumber" id="loanNumber" class="form-control"
                                               value="<?php echo htmlspecialchars(Strings::showField('loanNumber', 'LMRInfo')); ?>"
                                               maxlength="45"
                                               size="25"
                                               autocomplete="off" tabindex=1
                                            <?php if (glCustomJobForProcessingCompany::generateFileIDAsLoanNumber($PCID)) {
                                                echo 'readonly';
                                            } ?>>
                                    <?php }
                                } else { ?>
                                    <input type="text" name="fileNumber" id="fileNumber" class="form-control"
                                           value="<?php echo Strings::showField('fileNumber', 'ResponseInfo') ?>"
                                           maxlength="45"
                                           size="25" autocomplete="off" tabindex=1>
                                <?php } ?>
                            <?php } else { ?>
                                <?php if ($isHMLO == 1) { ?>
                                    <h5><?php echo Strings::showField('loanNumber', 'LMRInfo') ?></h5>
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('fileNumber', 'ResponseInfo') ?></h5>
                                <?php } ?>
                            <?php } ?></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold">
                            Lead Source
                        </label>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <div class="input-group">
                                    <input type="text" name="leadSource" id="leadSource" class="form-control"
                                           value="<?php echo htmlspecialchars(Strings::showField('leadSource', 'ResponseInfo')); ?>"
                                           placeholder=" - Type Lead Source - " maxlength="48" tabindex=2 size="40"
                                           autocomplete="off" onclick="listAllLeadSource(this.value);"/>
                                    <div class="input-group-append">
                                    <span class="input-group-text tooltipClass"
                                          onclick="clearLeadSourceVal('loanModForm', 'leadSource');"
                                          title="Click to remove Lead Source">
			<i class="fa fa-times text-danger"></i>
		</span>
                                    </div>
                                </div>
                            <?php } else { ?>
                                <h5><?php echo Strings::showField('leadSource', 'ResponseInfo') ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>


                <?php
                if (in_array($PCID, $glShowANINumberPC) && ($isLM == 1 || $isSS == 1)) {
                    /** Allowed only this File Type = SS and LM for NVA PC = 1456, Added the Testing PC Dave = 820, Awata = 2  **/
                    ?>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold">
                                ANI #
                            </label>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="ANINo" id="ANINo" class="form-control"
                                           value="<?php echo htmlspecialchars(Strings::showField('ANINo', 'ResponseInfo')); ?>"
                                           maxlength="25"
                                           size="25"
                                           autocomplete="off">
                                <?php } else { ?>
                                    <h5><?php echo $ANINo ?></h5>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                <?php } else {
                } ?>
            </div>

            <div class="row">
                <div class="col-md-6 <?php if (glCustomJobForProcessingCompany::isCustomHideAdminTabFields($PCID)) {
                    echo 'd-none';
                } ?>">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold">
                            <?php if ($isSLMOnly == 1) { ?>Processing <?php } else { ?>Application <?php } ?>Received
                            Date
                        </label>
                        <!--                        data-date-start-date="05/18/2024"
                                           data-date-end-date="01/01/2025"-->
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <div class="input-group ">
                                    <div class="input-group-prepend receivedDate">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary"></i>
                                    </span>
                                    </div>
                                    <input type="text" name="receivedDate" id="receivedDate"
                                           class="form-control dateNewClass"
                                           placeholder="MM/DD/YYYY"
                                           value="<?php echo $receivedDate ?>"
                                           maxlength="10" size="12" tabindex=3>
                                </div>
                            <?php } else { ?>
                                <h5><?php echo $receivedDate ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold">
                            <?php if ($isHMLO == 1) {
                                loanForm::pushSectionID('Admin');
                                echo loanForm::getFieldLabel('purchaseCloseDate');
                                loanForm::popSectionID();
                            } else { ?>
                                <?php if ($isSLMOnly == 1) { ?>Signed Agreement Date<?php } else { ?>Closed Date<?php } ?>
                            <?php } ?>
                        </label>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>

                                <?php if ($isHMLO == 1) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend closingDate">
		<span class="input-group-text">
			<i class="fa fa-calendar text-primary"></i>
		</span>
                                        </div>
                                        <input type="text" name="closingDate" id="closingDate"
                                               class="form-control dateNewClass"
                                               placeholder="MM/DD/YYYY"
                                               value="<?php echo $closingDate ?>" maxlength="10" size="12"
                                               tabindex=4>
                                        <input type="hidden" name="closedDate" id="closedDate"
                                               class="form-control dateNewClass"
                                               placeholder="MM/DD/YYYY"
                                               value="<?php echo $closedDate ?>"
                                               maxlength="10" size="12" tabindex=4>
                                    </div>
                                <?php } else { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend closedDate">
		<span class="input-group-text">
			<i class="fa fa-calendar text-primary"></i>
		</span>
                                        </div>
                                        <input type="text" name="closedDate" id="closedDate"
                                               class="form-control dateNewClass"
                                               placeholder="MM/DD/YYYY"
                                               value="<?php echo $closedDate ?>"
                                               maxlength="10" size="12" tabindex=4>
                                    </div>
                                <?php } ?>


                            <?php } else { ?>
                                <h5><?php echo $closingDate ?></h5>
                            <?php } ?>

                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold">
                            <?php
                            loanForm::pushSectionID('Admin');
                            echo loanForm::getFieldLabel('targetClosingDate');
                            loanForm::popSectionID();
                            ?>
                        </label>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend ">
                                                    <span class="input-group-text">
                                                        <i class="fa fa-calendar text-primary icon-lg"></i>
                                                    </span>
                                    </div>
                                    <input
                                            class="form-control input-sm dateNewClass  "
                                            type="text"
                                            name="targetClosingDate"
                                            id="targetClosingDate"
                                            value="<?php echo Dates::formatDateWithRE(LMRequest::myFileInfo()->fileHMLOInfo()->targetClosingDate, 'YMD', 'm/d/Y'); ?>"
                                            size="10"
                                            autocomplete="off"
                                            placeholder="MM/DD/YYYY">
                                </div>
                            <?php } else { ?>
                                <div class="left">
                                    <h5> <?php echo Dates::formatDateWithRE(LMRequest::myFileInfo()->fileHMLOInfo()->targetClosingDate, 'YMD', 'm/d/Y') ?></h5>
                                </div>
                            <?php } ?>
                        </div>
                    </div>
                </div>


                <?php if ($isHMLO != 1) { ?>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold">
                                Closed Disposition
                            </label>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <select name="dispositionId" id="dispositionId" tabindex=5 class="form-control">
                                        <option value=""> - Select -</option>
                                        <?php for ($cdm = 1; $cdm <= count($closedDispositionArray); $cdm++) { ?>
                                            <option value="<?php echo trim($closedDispositionArray[$cdm]) ?>" <?php echo Arrays::isSelected(Strings::showField('closedDisposition', 'ResponseInfo'), trim($closedDispositionArray[$cdm])) ?>><?php echo trim($closedDispositionArray[$cdm]) ?></option>
                                        <?php } ?>
                                    </select>
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('dispositionId', 'ResponseInfo') ?></h5>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                <?php } ?>


                <div class="col-md-6 <?php if (glCustomJobForProcessingCompany::isCustomHideAdminTabFields($PCID)) {
                    echo 'd-none';
                } ?>">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold">
                            <?php if (glCustomJobForProcessingCompany::isPC_CV3($PCID)) { ?>
                                Confirmed Signing Date
                            <?php } else {
                                if ($isSLMOnly == 1) { ?>
                                    Client
                                <?php } else { ?>
                                    Borrower
                                <?php } ?> Call back
                            <?php } ?>
                        </label>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>

                                <div class="input-group">
                                    <div class="input-group-prepend borrowerCallBack">
		<span class="input-group-text">
			<i class="fa fa-calendar text-primary"></i>
		</span>
                                    </div>
                                    <input type="text" name="borrowerCallBack" id="borrowerCallBack"
                                           placeholder="MM/DD/YYYY"
                                           class="form-control dateNewClass"
                                           value="<?php echo $borrowerCallBack ?>" maxlength="10" size="12"
                                           tabindex=6>
                                </div>
                            <?php } else { ?>
                                <h5><?php echo $borrowerCallBack ?></h5>
                            <?php } ?>


                        </div>
                    </div>
                </div>


                <?php if ($isHMLO == 1) {
                } else { ?>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold">
                                <?php if ($isSLMOnly == 1) { ?>Welcome Package Sent<?php } else { ?>Lender Call back<?php } ?>
                            </label>
                            <div class="col-md-7">

                                <?php if ($allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend lenderCallBack">
                                                            <span class="input-group-text">
                                                                <i class="fa fa-calendar text-primary"></i>
                                                            </span>
                                        </div>
                                        <input type="text" name="lenderCallBack" id="lenderCallBack"
                                               class="form-control dateNewClass"
                                               placeholder="MM/DD/YYYY"
                                               value="<?php echo $lenderCallBack ?>" maxlength="10" size="12"
                                               tabindex=7>
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo $lenderCallBack ?></h5>
                                <?php } ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold">
                                <?php if ($isSLMOnly == 1) { ?>File<?php } else { ?>Lender<?php } ?> Submission Date
                            </label>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend lenderSubmission">
                                                <span class="input-group-text">
                                                    <i class="fa fa-calendar text-primary"></i>
                                                </span>
                                        </div>
                                        <input type="text" name="lenderSubmission" id="lenderSubmission"
                                               class="form-control dateNewClass"
                                               placeholder="MM/DD/YYYY"
                                               value="<?php echo $lenderSubmission ?>" maxlength="10" size="10"
                                               tabindex=8>
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo $lenderSubmission ?></h5>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                <?php } ?>

                <?php
                if ($isSLMOnly != 1 && $isHOALien != 1 && $isHMLO != 1) {
                    /** Hide Date fields **/ ?>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold">
                                HAFA Date :
                            </label>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend HAFADate">
                                                    <span class="input-group-text">
                                                        <i class="fa fa-calendar text-primary"></i>
                                                    </span>
                                        </div>
                                        <input type="text" name="HAFADate" id="HAFADate" value="<?php echo $HAFADate ?>"
                                               placeholder="MM/DD/YYYY"
                                               class="form-control dateNewClass" maxlength="10" size="12" tabindex=6>
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo $HAFADate ?></h5>
                                <?php } ?>

                            </div>
                        </div>
                    </div>
                <?php } ?>


                <div class="col-md-6 <?php if (glCustomJobForProcessingCompany::isCustomHideAdminTabFields($PCID)) {
                    echo 'd-none';
                } ?>">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold">
                            Welcome Call Date
                        </label>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend welcomeCallDate">
                                            <span class="input-group-text">
                                                <i class="fa fa-calendar text-primary"></i>
                                            </span>
                                    </div>
                                    <input type="text" name="welcomeCallDate" id="welcomeCallDate"
                                           class="form-control dateNewClass"
                                           placeholder="MM/DD/YYYY"
                                           value="<?php echo $welcomeCallDate ?>" maxlength="10" size="12" tabindex=7>
                                </div>
                            <?php } else { ?>
                                <h5><?php echo $welcomeCallDate ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>

                <?php loanForm::pushSectionID('Admin'); ?>
                <div class="col-md-6 <?php echo loanForm::showField('welcomeCallStatus'); ?>">
                    <div class="form-group row">
                        <?php echo loanForm::label('welcomeCallStatus', 'col-md-5 '); ?>
                        <div class="col-md-7">
                            <?php
                            if ($allowToEdit) {
                                echo loanForm::select(
                                    'welcomeCallStatus',
                                    LMRequest::$allowToEdit,
                                    1,
                                    LMRequest::File()->getTblFileAdminInfo_by_LMRId()->welcomeCallStatus,
                                    tblWelcomeCallStatus::options(),
                                    '',
                                    ' chzn-select ',
                                    ' ',
                                    'Please Select ' . loanForm::getFieldLabel('welcomeCallStatus')
                                );
                            } else {
                                echo '<h5>' . tblWelcomeCallStatus::options()[LMRequest::File()->getTblFileAdminInfo_by_LMRId()->welcomeCallStatus] . '</h5>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
                <?php loanForm::popSectionID(); ?>

                <?php if ($isHMLO == 1) { /* Law offices */
                } else { ?>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold">
                                <?php if ($isSLMOnly == 1) { ?>Forbearance<?php } else { ?>Bank<?php } ?> Call Completed
                            </label>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend bankCallCompleted">
                                        <span class="input-group-text">
                                            <i class="fa fa-calendar text-primary"></i>
                                        </span>
                                        </div>
                                        <input type="text" name="bankCallCompleted" id="bankCallCompleted"
                                               class="form-control dateNewClass"
                                               placeholder="MM/DD/YYYY"
                                               value="<?php echo $bankCallCompleted ?>" maxlength="10" size="10"
                                               tabindex=8>
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo $bankCallCompleted ?></h5>
                                <?php } ?>
                            </div>
                        </div>
                    </div>

                <?php } ?>


                <?php if ($isHMLO == 1) { ?>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold">
                                Maturity Date
                            </label>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend maturityDate">
                                            <span class="input-group-text">
                                                <i class="fa fa-calendar text-primary"></i>
                                            </span>
                                        </div>
                                        <input type="text" name="maturityDate" id="maturityDate"
                                               class="form-control dateNewClass"
                                               placeholder="MM/DD/YYYY"
                                               value="<?php echo Dates::formatDateWithRE(Strings::showField('maturityDate', 'fileHMLOPropertyInfo'), 'YMD', 'm/d/Y'); ?>"
                                               autocomplete="off" maxlength="10" size="12"
                                               tabindex="<?php echo $tabIndex++; ?>"/>
                                    </div>
                                    <?php
                                } else {
                                    echo '<h5>' . Dates::formatDateWithRE(Strings::showField('maturityDate', 'fileHMLOPropertyInfo'), 'YMD', 'm/d/Y') . '</h5>';
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                <?php } ?>

                <?php loanForm::pushSectionID('Admin'); ?>
                <div class="col-md-6 <?php echo loanForm::showField('targetSubmissionDate'); ?>">
                    <div class="form-group row">
                        <?php echo loanForm::label('targetSubmissionDate', 'col-md-5 '); ?>
                        <div class="col-md-7">
                            <?php
                            if ($allowToEdit) {
                                echo loanForm::date(
                                    'targetSubmissionDate',
                                    $allowToEdit,
                                    $tabIndex++,
                                    LMRequest::File()->getTblFileAdminInfo_by_LMRId()->targetSubmissionDate ?? '',
                                    ' dateNewClass ',
                                );
                            } else {
                                echo '<h5>' . Dates::StandardDate(LMRequest::File()->getTblFileAdminInfo_by_LMRId()->targetSubmissionDate) . '</h5>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 <?php echo loanForm::showField('finalSubmissionDate'); ?>">
                    <div class="form-group row">
                        <?php echo loanForm::label('finalSubmissionDate', 'col-md-5 '); ?>
                        <div class="col-md-7">
                            <?php
                            if ($allowToEdit) {
                                echo loanForm::date(
                                    'finalSubmissionDate',
                                    $allowToEdit,
                                    $tabIndex++,
                                    LMRequest::File()->getTblFileAdminInfo_by_LMRId()->finalSubmissionDate ?? '',
                                    ' dateNewClass ',
                                );
                            } else {
                                echo '<h5>' . Dates::StandardDate(LMRequest::File()->getTblFileAdminInfo_by_LMRId()->finalSubmissionDate) . '</h5>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 <?php echo loanForm::showField('initialCommentsDate'); ?>">
                    <div class="form-group row">
                        <?php echo loanForm::label('initialCommentsDate', 'col-md-5 '); ?>
                        <div class="col-md-7">
                            <?php
                            if ($allowToEdit) {
                                echo loanForm::date(
                                    'initialCommentsDate',
                                    $allowToEdit,
                                    $tabIndex++,
                                    LMRequest::File()->getTblFileAdminInfo_by_LMRId()->initialCommentsDate ?? '',
                                    ' dateNewClass ',
                                );
                            } else {
                                echo '<h5>' . Dates::StandardDate(LMRequest::File()->getTblFileAdminInfo_by_LMRId()->initialCommentsDate) . '</h5>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 <?php echo loanForm::showField('finalApprovalDate'); ?>">
                    <div class="form-group row">
                        <?php echo loanForm::label('finalApprovalDate', 'col-md-5 '); ?>
                        <div class="col-md-7">
                            <?php
                            if ($allowToEdit) {
                                echo loanForm::date(
                                    'finalApprovalDate',
                                    $allowToEdit,
                                    $tabIndex++,
                                    LMRequest::File()->getTblFileAdminInfo_by_LMRId()->finalApprovalDate ?? '',
                                    ' dateNewClass ',
                                );
                            } else {
                                echo '<h5>' . Dates::StandardDate(LMRequest::File()->getTblFileAdminInfo_by_LMRId()->finalApprovalDate) . '</h5>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 <?php echo loanForm::showField('buildAnalysisDeliveredDate'); ?>">
                    <div class="form-group row">
                        <?php echo loanForm::label('buildAnalysisDeliveredDate', 'col-md-5 '); ?>
                        <div class="col-md-7">
                            <?php
                            if ($allowToEdit) {
                                echo loanForm::date(
                                    'buildAnalysisDeliveredDate',
                                    $allowToEdit,
                                    $tabIndex++,
                                    LMRequest::File()->getTblFileAdminInfo_by_LMRId()->buildAnalysisDeliveredDate ?? '',
                                    ' dateNewClass ',
                                );
                            } else {
                                echo '<h5>' . Dates::StandardDate(LMRequest::File()->getTblFileAdminInfo_by_LMRId()->buildAnalysisDeliveredDate) . '</h5>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
                <?php loanForm::popSectionID(); ?>

                <?php if ($isHMLO == 1) { ?>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold">
                                Funding Date
                            </label>
                            <div class="col-md-7">

                                <?php if ($allowToEdit) { ?>

                                    <div class="input-group">
                                        <div class="input-group-prepend fundingDate">
                                            <span class="input-group-text">
                                                <i class="fa fa-calendar text-primary"></i>
                                            </span>
                                        </div>
                                        <input type="text" name="fundingDate" id="fundingDate"
                                               class="form-control dateNewClass"
                                               placeholder="MM/DD/YYYY"
                                               value="<?php echo Dates::formatDateWithRE(Strings::showField('fundingDate', 'fileHMLOPropertyInfo'), 'YMD', 'm/d/Y'); ?>"
                                               autocomplete="off" maxlength="10" size="12"
                                               tabindex="<?php echo $tabIndex++; ?>"/>
                                    </div>


                                    <?php
                                } else {
                                    echo '<h5>' . Dates::formatDateWithRE(Strings::showField('fundingDate', 'fileHMLOPropertyInfo'), 'YMD', 'm/d/Y') . '</h5>';
                                }
                                ?>

                            </div>
                        </div>
                    </div>
                    <?php
                    if (glCustomJobForProcessingCompany::isPC_CV3($PCID)) {
                        ?>

                        <div class="col-md-6">
                            <div class="form-group row">
                                <label class="col-md-5 font-weight-bold">
                                    First Payment Due Date
                                </label>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend fundingDate">
                                            <span class="input-group-text">
                                                <i class="fa fa-calendar text-primary"></i>
                                            </span>
                                            </div>
                                            <input
                                                    type="text"
                                                    name="trialPaymentDate1"
                                                    id="trialPaymentDate1"
                                                    class="form-control dateNewClass"
                                                    placeholder="MM/DD/YYYY"
                                                    value="<?php echo $trialPaymentDate1; ?>"
                                                    autocomplete="off" maxlength="10" size="12"
                                                    tabindex="<?php echo $tabIndex++; ?>"/>
                                        </div>
                                        <?php
                                    } else {
                                        echo '<h5>' . $trialPaymentDate1 . '</h5>';
                                    }
                                    ?>

                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group row">
                                <label class="col-md-5 font-weight-bold">
                                    Disclosure Sent Date
                                </label>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend fundingDate">
                                            <span class="input-group-text">
                                                <i class="fa fa-calendar text-primary"></i>
                                            </span>
                                            </div>
                                            <input
                                                    type="text"
                                                    name="disclosureSentDate"
                                                    id="disclosureSentDate"
                                                    class="form-control dateNewClass"
                                                    placeholder="MM/DD/YYYY"
                                                    value="<?php echo $disclosureSentDate; ?>"
                                                    autocomplete="off" maxlength="10" size="12"
                                                    tabindex="<?php echo $tabIndex++; ?>"/>
                                        </div>
                                        <?php
                                    } else {
                                        echo '<h5>' . $disclosureSentDate . '</h5>';
                                    }
                                    ?>

                                </div>
                            </div>
                        </div>
                    <?php }
                    ?>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold" for="eCoaWaiverStatus">
                                ECOA Waiver Status
                            </label>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <div class="input-group">
                                        <select
                                                class="form-control input-sm"
                                                id="eCoaWaiverStatus"
                                                name="eCoaWaiverStatus">
                                            <option value="">-Select-</option>
                                            <?php foreach (eoaWaiverStatus::$eoaWaiverStatusArray as $eoaWaiverStatusVal) { ?>
                                                <option value="<?php echo $eoaWaiverStatusVal; ?>" <?php echo Arrays::isSelected($eoaWaiverStatusVal, LMRequest::myFileInfo()->ResponseInfo()->eCoaWaiverStatus); ?> ><?php echo $eoaWaiverStatusVal; ?></option>
                                            <?php } ?>
                                        </select>
                                    </div>
                                    <?php
                                } else {
                                    echo '<h5>' . LMRequest::myFileInfo()->ResponseInfo()->eCoaWaiverStatus . '</h5>';
                                }
                                ?>

                            </div>
                        </div>
                    </div>
                <?php } ?>
            </div>

            <div class="row">

                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold">
                            Assigned Employees
                            <?php if ($allowToEdit) { ?>
                                <a class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 tooltipClass popuplink"
                                   data-toggle='modal' data-target='#exampleModal1' data-html="true"
                                   data-wsize='modal-xl'
                                   style="text-decoration:none;"
                                   data-href="<?php echo CONST_URL_POPS; ?>assignEmployee.php"
                                   data-name=" <?php echo $borrowerName ?> > Assigning Employees"
                                   data-id="LMRId=<?php echo cypher::myEncryption($LMRId) ?>&amp;PCID=<?php echo cypher::myEncryption($PCID) ?>&amp;BRID=<?php echo cypher::myEncryption($executiveId) ?>&tab=admin"
                                   title="<?php echo $assignedEmpLink ?>"><i class="fas fa-users-cog"></i> </a>
                            <?php } ?>
                        </label>
                        <div class="col-md-7" id="divListAssignEmp"> <?php echo $assignedEmpLink ?></div>
                    </div>
                </div>

                <?php
                if ($isSLMOnly != 1 && $isHMLO != 1) {
                    /** Hide Date fields **/
                    ?>

                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold">
                                Appeal Date :
                            </label>
                            <div class="col-md-7">


                                <?php if ($allowToEdit) { ?>

                                    <div class="input-group">
                                        <div class="input-group-prepend appealDate">
		<span class="input-group-text">
			<i class="fa fa-calendar text-primary"></i>
		</span>
                                        </div>
                                        <input type="text" name="appealDate" id="appealDate"
                                               value="<?php echo $appealDate ?>" class="form-control dateNewClass"
                                               placeholder="MM/DD/YYYY"
                                               maxlength="10" size="12" tabindex=7>
                                    </div>

                                <?php } else { ?>
                                    <h5><?php echo $appealDate ?></h5>
                                <?php } ?></div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold">
                                Servicer Attorney Review Date
                            </label>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend attorneyReviewDate">
		<span class="input-group-text">
			<i class="fa fa-calendar text-primary"></i>
		</span>
                                        </div>
                                        <input type="text" name="attorneyReviewDate" id="attorneyReviewDate"
                                               class="form-control dateNewClass"
                                               placeholder="MM/DD/YYYY"
                                               value="<?php echo $attorneyReviewDate ?>" maxlength="10" size="10"
                                               tabindex=7>
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo $attorneyReviewDate ?></h5>
                                <?php } ?>
                            </div>
                        </div>
                    </div>

                    <?php
                } ?>


                <?php
                if ($isSLMOnly != 1 && $isHMLO != 1) {
                    /** Hide Date fields **/
                    ?>

                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold">
                                Enter Foreclosure Sale Date if provided by<br>bank/attorney/trustee
                            </label>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>


                                    <div class="input-group">
                                        <div class="input-group-prepend salesDate">
		<span class="input-group-text">
			<i class="fa fa-calendar text-primary"></i>
		</span>
                                        </div>
                                        <input type="text" name="salesDate" id="salesDate"
                                               value="<?php echo $salesDate ?>"
                                               placeholder="MM/DD/YYYY"
                                               maxlength="10" size="12" tabindex=9 class="form-control dateNewClass"
                                               onclick="checkSalesDate(<?php echo $LMRId ?>);">
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo $salesDate ?></h5>
                                <?php } ?>

                            </div>
                        </div>
                    </div>


                    <?php
                }
                ?>

                <div class="col-md-6 <?php if (glCustomJobForProcessingCompany::isCustomHideAdminTabFields($PCID)) {
                    echo 'd-none';
                } ?>">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold">
                            Priority Level
                        </label>
                        <div class="col-md-7">

                            <?php if ($allowToEdit) { ?>

                                <div class="radio-inline">
                                    <label class="radio radio-solid font-weight-bold" for="priorityLevelHigh">
                                        <input type="radio" name="priorityLevel" value="High" id="priorityLevelHigh"
                                               tabindex=10 <?php echo Strings::isChecked(strtolower(Strings::showField('priorityLevel', 'ResponseInfo')), 'high'); ?> >
                                        <span></span> High
                                        <i class="fa fa-fire text-danger ml-1"></i>
                                        <i class="fa fa-fire text-danger ml-1"></i>
                                        <i class="fa fa-fire text-danger ml-1"></i>
                                    </label>

                                    <label class="radio radio-solid font-weight-bold" for="priorityLevelMedium">
                                        <input type="radio" name="priorityLevel" value="Medium" id="priorityLevelMedium"
                                               tabindex=10 <?php echo Strings::isChecked(strtolower(Strings::showField('priorityLevel', 'ResponseInfo')), 'medium'); ?>>
                                        <span></span>Medium
                                        <i class="fa fa-fire text-danger ml-1"></i>
                                        <i class="fa fa-fire text-danger ml-1"></i>
                                    </label>

                                    <label class="radio radio-solid font-weight-bold" for="priorityLevelLow">
                                        <input type="radio" name="priorityLevel" value="Low" id="priorityLevelLow"
                                               tabindex=10 <?php echo Strings::isChecked(strtolower(Strings::showField('priorityLevel', 'ResponseInfo')), 'low'); ?> >
                                        <span></span>Low
                                        <i class="fa fa-fire text-danger ml-1"></i>
                                    </label>
                                </div>
                            <?php } else { ?>
                                <h5><?php echo Strings::showField('priorityLevel', 'ResponseInfo') ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>

                <?php
                if ($isSLMOnly != 1 && $isHMLO != 1) {
                    /** Hide Date fields **/
                    ?>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold">
                                Resolution Type:
                            </label>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <select name="resolutionId" id="resolutionId" class="form-control" tabindex=11>
                                        <option value=""> - Select -</option>
                                        <?php
                                        if ($isLM == 1 || $isSS == 1) {
                                            $resolutionTypeArray['9'] = 'In House';
                                            $resolutionTypeArray['10'] = 'Streamlined';
                                        }
                                        if ($isHOALien == 1) {
                                            $resolutionTypeArray = ['1' => 'HOA Lien',
                                                                    '2' => 'HOA Deed',
                                                                    '3' => 'Short Payoff',
                                                                    '4' => 'Short Sale',
                                                                    '5' => 'Rental',
                                                                    '6' => 'Listed',];
                                        }
                                        for ($rdm = 1; $rdm <= count($resolutionTypeArray); $rdm++) {
                                            ?>
                                            <option value="<?php echo trim($resolutionTypeArray[$rdm]) ?>" <?php echo Arrays::isSelected(Strings::showField('resolutionType', 'ResponseInfo'), trim($resolutionTypeArray[$rdm])) ?>><?php echo trim($resolutionTypeArray[$rdm]) ?></option>
                                            <?php
                                        }
                                        ?>
                                    </select>
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('resolutionId', 'ResponseInfo') ?></h5>
                                <?php } ?>

                            </div>
                        </div>
                    </div>

                    <?php
                }
                ?>


                <?php
                if ($isHOALien == 1 || $isHMLO == 1) {

                } else { ?>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold">
                                <?php if ($isSLMOnly == 1) { ?>All Docs<?php } else { ?>Date Mod<?php } ?> Received
                            </label>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend trialModReceivedDate">
		<span class="input-group-text">
			<i class="fa fa-calendar text-primary"></i>
		</span>
                                        </div>
                                        <input type="text" name="trialModReceivedDate" id="trialModReceivedDate"
                                               class="form-control dateNewClass"
                                               placeholder="MM/DD/YYYY"
                                               value="<?php echo $trialModReceivedDate ?>" maxlength="10" size="12"
                                               tabindex=12>
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo $trialModReceivedDate ?></h5>
                                <?php } ?>

                            </div>
                        </div>
                    </div>

                    <?php
                    if ($isSLMOnly != 1 && $isHMLO != 1) {
                        /** Hide Date fields **/
                        ?>
                        <div class="col-md-6">
                            <div class="form-group row">
                                <label class="col-md-5 font-weight-bold">
                                    Amount of 1st Mod Payment
                                </label>
                                <div class="col-md-7">    <?php if ($allowToEdit) { ?>

                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="text" name="firstModPaymentAmt" id="firstModPaymentAmt"
                                                   onblur="currencyConverter(this, this.value);"
                                                   class="form-control"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal($firstModPaymentAmt) ?>"
                                                   maxlength="10" size="11" tabindex=13>
                                        </div>
                                    <?php } else { ?>
                                        <h5><?php echo Currency::formatDollarAmountWithDecimal($firstModPaymentAmt) ?></h5>
                                    <?php } ?></div>
                            </div>
                        </div>
                    <?php } ?>
                <?php } ?>


                <?php
                if ($isSLMOnly != 1 && $isHOALien != 1 && $isHMLO != 1) {
                    /** Hide Date fields **/
                    ?>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold ">
                                Date of 1st Trial / Perm Payment
                            </label>
                            <div class="col-md-7">

                                <?php if ($allowToEdit) { ?>

                                    <div class="input-group">
                                        <div class="input-group-prepend trialPaymentDate1">
		<span class="input-group-text">
			<i class="fa fa-calendar text-primary"></i>
		</span>
                                        </div>
                                        <input type="text" name="trialPaymentDate1" id="trialPaymentDate1"
                                               class="form-control dateNewClass"
                                               placeholder="MM/DD/YYYY"
                                               value="<?php echo $trialPaymentDate1 ?>" maxlength="10" size="12"
                                               tabindex=13>
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo $trialPaymentDate1 ?></h5>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold ">
                                Date of 2nd Trial / Perm Payment
                            </label>
                            <div class="col-md-7">

                                <?php if ($allowToEdit) { ?>

                                    <div class="input-group">
                                        <div class="input-group-prepend trialPaymentDate2">
		<span class="input-group-text">
			<i class="fa fa-calendar text-primary"></i>
		</span>
                                        </div>
                                        <input type="text" name="trialPaymentDate2" id="trialPaymentDate2"
                                               class="form-control dateNewClass"
                                               placeholder="MM/DD/YYYY"
                                               value="<?php echo $trialPaymentDate2 ?>" maxlength="10" size="12"
                                               tabindex=13>
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo $trialPaymentDate2 ?></h5>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold ">
                                Date of 3rd Trial / Perm Payment
                            </label>
                            <div class="col-md-7">

                                <?php if ($allowToEdit) { ?>

                                    <div class="input-group">
                                        <div class="input-group-prepend trialPaymentDate3">
		<span class="input-group-text">
			<i class="fa fa-calendar text-primary"></i>
		</span>
                                        </div>
                                        <input type="text" name="trialPaymentDate3" id="trialPaymentDate3"
                                               class="form-control dateNewClass"
                                               placeholder="MM/DD/YYYY"
                                               value="<?php echo $trialPaymentDate3 ?>" maxlength="10" size="12"
                                               tabindex=13>
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo $trialPaymentDate3 ?></h5>
                                <?php } ?>
                            </div>
                        </div>
                    </div>


                    <?php
                } /** Hide Date fields **/
                ?>


                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold">
                            Primary Client File Status

                            <i class="fa fa-info-circle text-primary tooltipClass"
                               title="Changing the Primary Status will also change where the client file displays under the main pipeline view."></i>
                        </label>
                        <div class="col-md-7">

                            <?php
                            if ($allowToEdit) {
                                $statuaCnt = 0;
                                $statuaCnt = count($PCStatusInfo);
                                $tempModulesCodeArr = [];
                                for ($j = 0; $j < $statuaCnt; $j++) {
                                    $moduleCode = $PCStatusInfo[$j]['moduleName'];

                                    $tempModulesCodeArr[$moduleCode][] = $PCStatusInfo[$j];
                                }
                                $moduleKeys = [];
                                $moduleKeys = array_keys($tempModulesCodeArr);
                                ?>
                                <select name="statusId" id="statusId" tabindex=14 class="primaryStatus form-control">
                                    <option value=""> - Select -</option>
                                    <?php for ($k = 0; $k < count($moduleKeys); $k++) { ?>
                                        <optgroup label="<?php echo $moduleKeys[$k] ?>">
                                            <?php
                                            $tempKeys = $tempModulesCodeArr[$moduleKeys[$k]];
                                            for ($plm = 0; $plm < count($tempKeys); $plm++) {
                                                //
                                                $showHidePrimaryStatusGroup = '';
                                                if (PageVariables::$userGroup == 'Employee') {
                                                    if ((trim($tempKeys[$plm]['allowBOToEditFile']) == 0)
                                                        && (PageVariables::$allowToEditMyFile == 2 || PageVariables::$allowToEditMyFile == 0)) {
                                                        $showHidePrimaryStatusGroup = 'disabled title="Status Disabled"';
                                                    }
                                                } elseif (PageVariables::$userGroup == 'Branch') {
                                                    if ((trim($tempKeys[$plm]['allowOthersToUpdate']) == 0)
                                                        && (PageVariables::$allowToEditMyFile == 2 || PageVariables::$allowToEditMyFile == 0)) {
                                                        $showHidePrimaryStatusGroup = 'disabled title="Status Disabled"';
                                                    }
                                                } elseif (PageVariables::$userGroup == 'Agent' && PageVariables::$externalBroker == 0) {
                                                    if ((trim($tempKeys[$plm]['allowAgentToEditFile']) == 0)
                                                        && (PageVariables::$allowToEditMyFile == 2 || PageVariables::$allowToEditMyFile == 0)) {
                                                        $showHidePrimaryStatusGroup = 'disabled title="Status Disabled"';
                                                    }
                                                } elseif (PageVariables::$userGroup == 'Agent' && PageVariables::$externalBroker == 1) {
                                                    if ((trim($tempKeys[$plm]['allowLoanOfficerToEditFile']) == 0)
                                                        && (PageVariables::$allowToEditMyFile == 2 || PageVariables::$allowToEditMyFile == 0)) {
                                                        $showHidePrimaryStatusGroup = 'disabled title="Status Disabled"';
                                                    }
                                                } elseif (PageVariables::$userGroup == 'Client') {
                                                    if (trim($tempKeys[$plm]['allowClientToEditFile']) == 0) {
                                                        $showHidePrimaryStatusGroup = 'disabled title="Status Disabled"';
                                                    }
                                                }
                                                ?>
                                                <option <?php echo $showHidePrimaryStatusGroup; ?>
                                                        value="<?php echo trim($tempKeys[$plm]['PSID']) ?>" <?php echo Arrays::isSelected(Strings::showField('primeStatusId', 'ResponseInfo'), trim($tempKeys[$plm]['PSID'])) ?>><?php echo trim($tempKeys[$plm]['primaryStatus']) ?></option>
                                                <?php
                                            }
                                            ?>
                                        </optgroup>
                                        <?php
                                    }
                                    ?>
                                </select>
                                <?php
                            } else { ?>
                                <h5><?php echo $fileStatus ?></h5>
                            <?php } ?>

                        </div>
                    </div>
                </div>


                <?php
                if ($isSLMOnly != 1 && $isHOALien != 1 && $isHMLO != 1) {
                    /** Hide Date fields **/
                    ?>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold">
                                Retainer Date :
                            </label>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>

                                    <div class="input-group">
                                        <div class="input-group-prepend retainerDate">
		<span class="input-group-text">
			<i class="fa fa-calendar text-primary"></i>
		</span>
                                        </div>
                                        <input type="text" name="retainerDate" id="retainerDate"
                                               class="form-control dateNewClass"
                                               value="<?php echo $retainerDate ?>"
                                               placeholder="MM/DD/YYYY"
                                               maxlength="10" size="12" tabindex=14>
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo $retainerDate ?></h5>
                                <?php } ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold">
                                Hearing Date :
                            </label>
                            <div class="col-md-7">

                                <?php if ($allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend hearingDate">
		<span class="input-group-text">
			<i class="fa fa-calendar text-primary"></i>
		</span>
                                        </div>
                                        <input type="text" name="hearingDate" id="hearingDate"
                                               class="form-control dateNewClass"
                                               value="<?php echo $hearingDate; ?>"
                                               placeholder="MM/DD/YYYY"
                                               maxlength="10" size="12" tabindex=15>
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo $hearingDate; ?></h5>
                                <?php } ?>
                            </div>
                        </div>
                    </div>


                    <?php
                    if ($isLM == 1 || $isSS == 1 || $isDL == 1 || $isFC == 1) { ?>

                        <div class="col-md-6">
                            <div class="form-group row">
                                <label class="col-md-5 font-weight-bold">
                                    Escalation Date :
                                </label>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>

                                        <div class="input-group">
                                            <div class="input-group-prepend escalationDate">
		<span class="input-group-text">
			<i class="fa fa-calendar text-primary"></i>
		</span>
                                            </div>
                                            <input type="text" name="escalationDate" id="escalationDate"
                                                   class="form-control dateNewClass"
                                                   placeholder="MM/DD/YYYY"
                                                   value="<?php echo $escalationDate ?>" maxlength="10" size="10"
                                                   tabindex=14>
                                        </div>
                                    <?php } else { ?>
                                        <h5><?php echo $escalationDate ?></h5>
                                    <?php } ?>

                                </div>
                            </div>
                        </div>


                        <?php
                    } else {

                    }
                } else {

                } /** Hide Date fields **/
                ?>
                <?php
                if ($isSLMOnly != 1 && $isHOALien != 1 && $isHMLO != 1) { ?>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold">
                                Denial Date :
                            </label>
                            <div class="col-md-7">

                                <?php if ($allowToEdit) { ?>

                                    <div class="input-group">
                                        <div class="input-group-prepend denialDate">
		<span class="input-group-text">
			<i class="fa fa-calendar text-primary"></i>
		</span>
                                        </div>
                                        <input type="text" name="denialDate" id="denialDate"
                                               placeholder="MM/DD/YYYY"
                                               value="<?php echo $denialDate ?>" class="form-control dateNewClass"
                                               maxlength="10" size="10">
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo $denialDate ?></h5>
                                <?php } ?>

                            </div>
                        </div>
                    </div>
                <?php } ?>

                <?php if ($PCID == 2 || $PCID == 1419) { ?>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold">
                                Total Calls Placed :
                            </label>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="totalCallsPlaced" id="totalCallsPlaced"
                                           class="form-control"
                                           value="<?php echo Strings::showField('totalCallsPlaced', 'ResponseInfo') ?>"
                                           maxlength="45"
                                           size="12"
                                           autocomplete="off">
                                <?php } else { ?>
                                    <h5><?php echo $totalCallsPlaced ?></h5>
                                <?php } ?>

                            </div>
                        </div>
                    </div>
                <?php } ?>


                <?php
                if (in_array($PCID, $glNewShortWebformPC) && in_array($executiveId, $glNewShortWebformBranch[$PCID]) && $isHMLO == 0) {
                    ?>

                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold">
                                Screener Name :
                            </label>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="screenerName" id="screenerName"
                                           value="<?php echo Strings::showField('screenerName', 'ResponseInfo') ?>"
                                           maxlength="45"
                                           size="12" class="form-control"
                                           autocomplete="off">
                                <?php } else { ?>
                                    <h5><?php echo $totalCallsPlaced ?></h5>
                                <?php } ?>

                            </div>
                        </div>
                    </div>


                <?php } ?>


            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold">
                            File Sub Status: <i class="fa fa-info-circle text-primary tooltipClass"
                                                data-html="true"
                                                title="These sub-statuses act as &quot;Tags&quot; and you may select more than 1 sub-status.<br>If you want to remove sub-statuses, you must de-select them from the company profile while logged in as a Manager.<br>If you need more sub-status options, please email us."></i>
                        </label>
                        <div class="col-md-7">

                            <?php if ($allowToEdit) { ?>
                                <select data-placeholder="" tabindex=15 name="LMRProcessorStatus"
                                        id="LMRProcessorStatus" data-placeholder="Select File Sub Status"
                                        class="chzn-select form-control " multiple="">
                                    <?php
                                    $subStatuaCnt = 0;
                                    $subStatuaCnt = count($PCSubStatusInfo);
                                    $tempModulesCodeArr = [];
                                    for ($j = 0; $j < $subStatuaCnt; $j++) {
                                        $moduleCode = $PCSubStatusInfo[$j]['moduleName'];
                                        $category = $PCSubStatusInfo[$j]['category'];


                                        $tempModulesCodeArr[$moduleCode][$category][] = $PCSubStatusInfo[$j];
                                    }

                                    $moduleKeys = [];
                                    $moduleKeys = array_keys($tempModulesCodeArr);
                                    for ($j = 0; $j < count($moduleKeys); $j++) {
                                        $categoryKeys = [];

                                        $moduleCode = $moduleKeys[$j];
                                        $categoryArr = $tempModulesCodeArr[$moduleCode];
                                        $categoryKeys = array_keys($categoryArr);
                                        ?>
                                        <option class="optnGrp" disabled value=""><?php echo $moduleCode ?></option>
                                        <?php
                                        for ($k = 0; $k < count($categoryKeys); $k++) {
                                            $category = '';
                                            $substatusArr = [];
                                            $category = $categoryKeys[$k];
                                            $substatusArr = $tempModulesCodeArr[$moduleCode][$category];
                                            ?>
                                            <optgroup label="<?php echo $category ?>">
                                                <?php

                                                for ($l = 0; $l < count($substatusArr); $l++) {
                                                    $PFSID = '';
                                                    $sOpt = '';
                                                    $substatus = '';
                                                    $PFSID = trim($substatusArr[$l]['PFSID']);
                                                    $substatus = trim($substatusArr[$l]['substatus']);
                                                    $chk = '';
                                                    $chk = Strings::isKeyChecked($fileSubstatusInfo, 'substatusId', $PFSID);
                                                    if (trim($chk) == 'checked') $chk = 'selected ';
                                                    ?>
                                                    <option
                                                        <?php echo $chk ?>value="<?php echo $PFSID ?>"><?php echo $substatus ?></option>
                                                    <?php
                                                }
                                                ?>
                                            </optgroup>
                                            <?php
                                        }
                                    }
                                    ?>
                                </select>
                            <?php } else {
                                $substatusName = [];
                                $subStatuaCnt = count($substatusArray);
                                for ($j = 0; $j < $subStatuaCnt; $j++) {
                                    $substatusName[] = trim($substatusArray[$j]['substatus']);
                                }
                                ?>
                                <h5><?php echo implode(', ', array_unique($substatusName)) ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>
                <?php
                if ($isHMLO == 1) { ?>

                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold">
                                Borrower Notes :
                            </label>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <textarea class="form-control input-sm validateMaxLength" name="mortgageNotes"
                                              maxlength="<?php echo loanForm::getFieldLength('mortgageNotes', 'tblFile'); ?>"
                                              id="mortgageNotes"><?php echo Strings::showField('mortgageNotes', 'LMRInfo') ?></textarea>
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('mortgageNotes', 'LMRInfo') ?></h5>
                                <?php } ?>

                            </div>
                        </div>
                    </div>

                    <?php
                    if ($userGroup == 'Employee' || PageVariables::$externalBroker) { ?>
                        <div class="col-md-6">
                            <div class="form-group row">
                                <label class="col-md-5 font-weight-bold">
                                    <?php if (glCustomJobForProcessingCompany::isPC_CV3($PCID)) { ?>
                                        Funding Comments
                                    <?php } else { ?>
                                        Lender Internal Notes :
                                    <?php } ?>
                                </label>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <textarea class="form-control input-sm validateMaxLength"
                                                  name="lenderInternalNotes"
                                                  id="lenderInternalNotes"
                                                  maxlength="<?php echo loanForm::getFieldLength('lenderInternalNotes', 'tblFileResponse'); ?>"
                                                  tabindex="<?php echo $tabIndex++; ?>"
                                        ><?php echo Strings::showField('lenderInternalNotes', 'ResponseInfo') ?></textarea>
                                    <?php } else { ?>
                                        <h5><?php echo Strings::showField('lenderInternalNotes', 'ResponseInfo'); ?></h5>
                                    <?php } ?>

                                </div>
                            </div>
                        </div>
                    <?php } ?>
                <?php } ?>
            </div>
        </div>
    </div>


    <?php
        if (in_array($PCID, $accessSecondaryWFPC)) { ?>
            <div class="card card-custom WorkflowStatusAdmin">
                <div class="card-header card-header-tabs-line bg-gray-100  ">
                    <div class="card-title">
                        <h3 class="card-label">
                            Workflow Status
                        </h3>
                    </div>
                    <div class="card-toolbar ">
                        <a href="javascript:void(0);"
                           class="btn btn-icon btn-sm btn-hover-light-primary mr-1 toggleClass"
                           data-card-tool="toggle"
                           data-section="WorkflowStatusAdmin"
                           data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                            <i class="ki ki-arrow-down icon-nm"></i>
                        </a>
                        <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none"
                           data-card-tool="reload"
                           data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                            <i class="ki ki-reload icon-nm"></i>
                        </a>
                        <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary d-none" data-card-tool="remove"
                           data-toggle="tooltip" data-placement="top" title="" data-original-title="Remove Card">
                            <i class="ki ki-close icon-nm"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body WorkflowStatusAdmin_body">
                    <table style="width:100%;">
                        <tr class="bbg">
                            <td colspan="2"><h3>Workflow</h3></td>
                            <td colspan="2"><h3>Current Status</h3></td>
                            <td colspan="2"><h3>Status Update</h3></td>
                        </tr>
                        <?php
                        for ($i = 0; $i < count($fileWorkflowArray); $i++) {
                            $WFID = 0;
                            $tempArray = [];
                            $secondaryWFStatus = [];
                            $currentStatus = '';
                            $currentWFSID = 0;
                            $WFStatusRuleArray = [];
                            $WFID = trim($fileWorkflowArray[$i]['WFID']);
                            $SID = 0;
                            if (array_key_exists($WFID, $fileSecondaryWFStatus)) {
                                $secondaryWFStatus = $fileSecondaryWFStatus[$WFID];
                                if (count($secondaryWFStatus) > 0) {
                                    $SID = trim($secondaryWFStatus['SID']);
                                    $currentStatus = trim($secondaryWFStatus['steps']);
                                    $currentWFSID = trim($secondaryWFStatus['WFSID']);
                                    if (array_key_exists($currentWFSID, $WFStatusArray)) {
                                        $WFStatusRuleArray = $WFStatusArray[$currentWFSID];
                                    }
                                }
                            } else {
                                if (array_key_exists($WFID, $PCWFStepsArray)) {
                                    $tempArray = $PCWFStepsArray[$WFID];
                                }
                            }
                            if ($userRole == 'Manager' || $userRole == 'Administrator') {
                                if (array_key_exists($WFID, $PCWFStepsArray)) {
                                    $tempArray = $PCWFStepsArray[$WFID];
                                }
                                $WFStatusRuleArray = $tempArray;
                            }

                            ?>
                            <input type="hidden" name="<?php echo $WFID ?>_oSID" value="<?php echo $currentWFSID ?>"/>
                            <tr <?php if (($i + 1) % 2 == 0) { ?> class="even" <?php } ?>>
                                <td colspan="2"><h3><?php echo trim($fileWorkflowArray[$i]['WFName']) ?></h3></td>
                                <?php
                                if ($currentStatus == '') {
                                    ?>
                                    <td colspan="4">
                                        <select name="fileWFStepID[]" id="fileWFStepID"
                                                style="width:200px;" <?php if (in_array($WFID, $assignedWFIDs)) {
                                        } else { ?> disabled class="notApplicable" <?php } ?> >
                                            <option value=""> - Current Status -</option>
                                            <?php
                                            for ($j = 0; $j < count($tempArray); $j++) {
                                                ?>
                                                <option value="<?php echo $SID . '_' . trim($tempArray[$j]['WFID']) . '_' . trim($tempArray[$j]['WFSID']) ?>"><?php echo trim($tempArray[$j]['steps']) ?></option>
                                                <?php
                                            }
                                            ?>
                                        </select>
                                    </td>
                                    <?php
                                } else {
                                    ?>
                                    <td colspan="2">
                                        <div class="left"><h5><?php echo $currentStatus; ?></h5></div>
                                    </td>
                                    <td><b>Update to</b></td>
                                    <td>
                                        <div class="left">
                                            <select name="fileWFStepID[]" id="fileWFStepID"
                                                    style="width:200px;" <?php if (in_array($WFID, $assignedWFIDs)) {
                                            } else { ?> disabled class="notApplicable" <?php } ?> >
                                                <option value=""> - New Status -</option>
                                                <?php
                                                for ($j = 0; $j < count($WFStatusRuleArray); $j++) {
                                                    if ($WFID == trim($WFStatusRuleArray[$j]['WFID'])) {
                                                    } else {
                                                        $SID = 0;
                                                    }
                                                    ?>
                                                    <option value="<?php echo $SID . '_' . trim($WFStatusRuleArray[$j]['WFID']) . '_' . trim($WFStatusRuleArray[$j]['WFSID']) ?>"><?php echo trim($WFStatusRuleArray[$j]['steps']) ?></option>
                                                    <?php
                                                }
                                                ?>

                                            </select>
                                        </div>
                                    </td>
                                    <?php
                                }
                                ?>
                            </tr>
                            <?php
                        }
                        ?>
                    </table>
                </div>
            </div>


            <?php
        }
    ?>


<?php } // pivotal task Number : 147894913 ?>


<?php
if ($allowToEdit && $isMF != 1) { ?>
    <div class="row text-center">
        <div class="col-md-12 text-center">
            <input type="submit" class="btn btn-primary btnSave" id="saveBtn" name="save" value="Save"
                   onclick="if(this.disabled==false) {return true;} else {return false;}">

            <input type="submit" class="btn btn-primary btnSave" id="saveNextBtn" name="save" value="Save & Next"
                   onclick="if(this.disabled==false) {return true;} else {return false;}">
        </div>
    </div>
    <?php
} ?>

<?php
if ($userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager' || ($allowCFPBAuditing == 1 && $allowUserToUpdateCFPBFile == 1 && Strings::showField('FPCID', 'LMRInfo') != $PCID)) { /* CFPB auditor disable notes icon START */
} else {
    if ($viewPrivateNotes == 1 || $viewPublicNotes == 1) { ?>
        <div class="card card-custom notesHistoryCard">
            <div class="card-header card-header-tabs-line bg-gray-100  ">
                <div class="card-title">
                    <h3 class="card-label">
                        Notes History
                    </h3>
                </div>
                <div class="card-toolbar ">

                    <span class="cursor-pointer tooltipClass btn btn-primary toggleClass <?php if ($op == 'view') {
                        echo 'd-none';
                    } ?>"

                          data-id='rId=<?php echo cypher::myEncryption($LMRResponseId) ?>&amp;exID=<?php echo cypher::myEncryption($executiveId) ?>&amp;LMRId=<?php echo cypher::myEncryption($LMRId) ?>&opt=file&amp;FPCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo')) ?>&showSaveBtn=1'
                          data-href="<?php echo CONST_URL_POPS; ?>addNotes.php"
                          data-name=" <?php echo htmlentities($borrowerName); ?> > add Notes"
                          data-toggle='modal'
                          data-target='#exampleModal1'
                          data-wsize='modal-xl'
                          title="Make new notes">
                        <?php if (count($processorCommentsArray)) { ?>
                            <i class="icon-md fas fa-comments"></i>
                        <?php } else { ?>
                            <i class="icon-md fas fa-comment-medical "></i>
                        <?php } ?> Add a Note
                    </span>


                    <a href="javascript:void(0);"
                       class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                       data-card-tool="toggle"
                       data-section="notesHistoryCard"
                       data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                        <i class="ki ki-arrow-down icon-nm"></i>
                    </a>
                </div>
            </div>
            <div class="card-body notesHistoryCard_Body">
                <div class="row">
                    <div class="col-md-6">
                        <?php if (count($processorCommentsArray) > 0 && $showSysGenNote == 1) { ?>
                            <div class="checkbox-inline">
                                <label class="checkbox" for="hideSysNotesVal">
                                    <input class="newCheck"
                                           type="checkbox"
                                           name="hideSysNotesVal"
                                           id="hideSysNotesVal"
                                           value="1" checked
                                           onclick="hideSystemNotes(this.value, this.checked);">
                                    <span></span> Show System Generated Notes
                                </label>
                            </div>
                        <?php } ?>
                        <div class="right" id="taskMsgDiv"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12" id="fileNotesDiv">
                        <?php require 'fileNotesList.php'; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
}
?>
<style>
    .primaryStatus option:disabled {
        color: red !important;
    }
</style>
