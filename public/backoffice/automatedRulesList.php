<?php
//global variables
global $assignedPCID, $userGroup;

//session_start();
use models\composite\oAutomatedActions\getAutomatedActions;
use models\composite\oAutomatedActions\getAutomatedActionTitle;
use models\composite\oAutomatedRules\getAutomatedRuleConditions;
use models\composite\oAutomatedRules\getAutomatedRules;
use models\composite\oBroker\listAllAgents;
use models\composite\oPC\getPCServiceType;
use models\composite\oPrimaryStatus\getPCPrimaryStatus;
use models\composite\oSubstatus\getPCFileSubstatus;
use models\composite\oWorkflow\getPCWFSteps;
use models\constants\borrowerStatusArray;
use models\constants\drawRequestStatusArray;
use models\constants\gl\glBorrowerType;
use models\Controllers\loanForm;
use models\cypher;
use models\constants\automationConstants;
use models\lendingwise\tblBranch;
use models\lendingwise\tblAutomatedRules;
use models\Request;
use models\standard\UserAccess;

$PCID = cypher::myEncryption($assignedPCID);
UserAccess::CheckAdminUse();

//
$rules = [];
//pagination
$pageNumber = 1;
$noOfRecords = 0;
$recNumbStart = 0;
$recNumbEnd = 0;
$total = 0;
$noOfRecordsPerPage = 100;

if (isset($_POST['pageNumber']) || isset($_GET['pageNumber'])) $pageNumber = Request::GetClean('pageNumber');

$recNumbStart = ($pageNumber - 1) * $noOfRecordsPerPage;


$pcid = cypher::myDecryption($PCID);
$params = [
    'PCID'               => $pcid,
    'noOfRecordsPerPage' => $noOfRecordsPerPage,
    'recNumbStart'       => $recNumbStart
];
$resArray = getAutomatedRules::getReport($params);
if (count($resArray) > 0) {
    if (array_key_exists('AutomatedRule', $resArray)) {
        $rules = $resArray['AutomatedRule'];
    }
    if (array_key_exists('count', $resArray)) {
        $total = $resArray['count'];
    }
}
$noOfRecords = $total;

if ($noOfRecords > $noOfRecordsPerPage) {
    $recNumbStart = ($pageNumber - 1) * $noOfRecordsPerPage - 1;
    $temp = $recNumbStart + 1;
}
$recNumbStart = ($pageNumber - 1) * $noOfRecordsPerPage + 1;
$recNumbEnd = $recNumbStart + $noOfRecordsPerPage - 1;
if ($recNumbEnd > $noOfRecords) $recNumbEnd = $noOfRecords;
$recNumbStart = $recNumbStart - 1;


?>
<div class="row">
    <div class="col-md-12">
        <div class="row mb-4">
            <div class="col-md-12 bg-gray-100 py-6">
                <p class="justify">
                    Automated Rules allows you to perform certain automatic actions on specific loan files,
                    like updating the Workflow Events, Primary File Status, File Sub Status based on filter criteria.
                    You can create a task, send an e-mail and trigger a Webhook using these automated rules.
                    You can also Assign Employee(s) and update the File Status to a specific status based on the conditions you set.
                </p>
            </div>
        </div>
    </div>
    <div class="col-md-12">
        <div class="row">
            <div class="col-md-6 p-0">
                <div>
                    <div class="checkbox-inline">
                        <label class="checkbox checkbox-lg font-weight-bold">
                            <input type="checkbox" disabled name="Checkboxes3_1">
                            <span class="bg-success"></span>Event Enabled</label>
                        <label class="checkbox checkbox-lg font-weight-bold">
                            <input type="checkbox" disabled name="Checkboxes3_1">
                            <span class="bg-danger"></span>Event Disabled</label>
                        <label class="checkbox checkbox-lg font-weight-bold">
                            <input type="checkbox" disabled name="Checkboxes3_1">
                            <span class="bg-primary"></span>Condition Only&nbsp;
                            <i class="tooltipAjax fas fa-info-circle text-primary" data-html="true"
                               data-toggle="tooltip" data-placement="right" title=""
                               data-original-title="The scheduled actions will be performed even if you update the conditions of the rule."></i>
                        </label>
                    </div>
                </div>
            </div>
            <div class="col-md-6 p-0">
                <div class="text-right">
                    <a href="javascript:void(0)" id="autoRuleForm" class="btn btn-sm btn-primary" role="button">Create
                        Rule</a>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
</div>

<div class="row">
    <div class="col-md-12">
        <form name="automatedRulesListForm" id="automatedRulesListForm">
            <input type="hidden" name="pageNumber" id="pageNumber" value="<?php echo htmlspecialchars($pageNumber); ?>">
            <input type="hidden" name="tabNumb" id="" value="4">
        </form>
    </div>
    <div class="col-md-12 p-0">
        <div class="table-responsive mt-5">
            <table id="tblAutoRule" class="table table-bordered table-hover">
                <thead class="thead-light">
                <tr>
                    <th></th>
                    <th>Rule Name</th>
                    <th>Rule For</th>
                    <th>Status Update</th>
                    <th>Event Name / Type</th>
                    <th>Created Date</th>
                    <th>Updated Date</th>
                    <th># of action(s)</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
                </thead>
                <tbody>
                <?php
                $no = $recNumbStart;
                foreach ($rules as $rule) {
                    $ruleId = $rule['id'];
                    $ruleName = $rule['ruleName'];
                    $fileType = $rule['fileType'];
                    $ruleStatus = ($rule['ruleStatus'] == 1) ? 'ON' : 'OFF';
                    $createdDate = ($rule['createdOn'] != '') ? date('m/d/Y', strtotime($rule['createdOn'])) : '';
                    $updatedDate = ($rule['updatedOn'] != '') ? date('m/d/Y', strtotime($rule['updatedOn'])) : '';

                    $ruleConds = getAutomatedRuleConditions::getReport($ruleId);
                    $ruleFor = '';
                    $ruleForName = '';
                    $params = [];
                    $newStatusTxt = $updateStatusTxt = 'No';
                    if (count($ruleConds) > 0) {
                        for ($r = 1; $r <= 3; $r++) {
                            $noOfDayStatus = $statusId = '';
                            if (array_key_exists('ruleFor' . $r, $ruleConds)) $ruleForName = $ruleConds['ruleFor' . $r];
                            if (array_key_exists('params' . $r, $ruleConds)) $params = explode('~', $ruleConds['params' . $r]);
                            if (array_key_exists('noOfDayStatus' . $r, $ruleConds)) $noOfDayStatus = $ruleConds['noOfDayStatus' . $r];
                            if (array_key_exists('changeStatus' . $r, $ruleConds)) $statusId = $ruleConds['changeStatus' . $r];
                            if ($ruleForName == automationConstants::$automation_FCU) {
                                $ruleFor .= '<b>' . automationConstants::$automation_LoanFileStatusTitle . ':</b><br>';
                                foreach ($params as $p) {
                                    if ($p == '1') {
                                        $ruleFor .= 'Create, ';
                                    } elseif ($p == '2') {
                                        $ruleFor .= 'Update, ';
                                    }
                                }
                            } elseif ($ruleForName == automationConstants::$automation_PFS) {
                                $fileStatusArray = $ruleForOptions = [];
                                $inArray = ['PCID' => $assignedPCID, 'opt1' => 'list', 'opt2' => 'Y', 'searchTerm' => $fileType];
                                $fileStatusArray = getPCPrimaryStatus::getReport($inArray);
                                $ruleForOptions = $fileStatusArray['primaryStatusInfo'][$fileType];
                                if (count($ruleForOptions) > 0) {
                                    $ruleFor .= '<b>' . automationConstants::$automation_PrimaryFileStatus . ':</b><br>';
                                    foreach ($ruleForOptions as $pfs) {
                                        if (in_array($pfs['PSID'], $params)) {
                                            $ruleFor .= $pfs['primaryStatus'] . ', ';
                                        }
                                        //new status
                                        if ($pfs['PSID'] == $statusId) {
                                            $newStatusTxt = $pfs['primaryStatus'];
                                        }
                                    }
                                    //Update Status Text
                                    if ($newStatusTxt != '0' && $noOfDayStatus != '0' && $noOfDayStatus != '') {
                                        $updateStatusTxt = "To <b>$newStatusTxt</b> in <b>$noOfDayStatus</b> Day(s)";
                                    }
                                }
                            } elseif ($ruleForName == automationConstants::$automation_FSS) {
                                $PCFileSubstatusArray = [];
                                $inArray = ['PCID' => $assignedPCID, 'opt1' => 'list', 'opt2' => 'Y', 'searchTerm' => $fileType];
                                $result = getPCFileSubstatus::getReport($inArray);
                                $PCFileSubstatusArray = $result['substatusInfo'][$fileType];
                                if (count($PCFileSubstatusArray) > 0) {
                                    if (count($ruleForOptions ?? []) > 0) $ruleFor .= '<br>';
                                    $ruleFor .= '<b>' . automationConstants::$automation_FileSubStatus . ':</b><br>';
                                    foreach ($PCFileSubstatusArray as $fss) {
                                        if (in_array($fss['PFSID'], $params)) {
                                            $ruleFor .= $fss['substatus'] . ', ';
                                        }
                                    }
                                }
                            } elseif ($ruleForName == automationConstants::$automation_Workflow) {
                                $PCWFStepsArray = [];
                                $inArray = ['PCID' => $assignedPCID];
                                $PCWFStepsArray = getPCWFSteps::getReport($inArray);
                                if (count($PCWFStepsArray) > 0) {
                                    if (count($PCFileSubstatusArray ?? []) > 0 || count($ruleForOptions ?? []) > 0) $ruleFor .= '<br>';
                                    $ruleFor .= '<b>' . automationConstants::$automation_Workflow . ':</b><br>';
                                    foreach ($PCWFStepsArray as $q => $wf) {
                                        foreach ($wf as $workflow) {
                                            $wfsId = $wfSteps = '';
                                            $wfsId = $workflow['WFSID'];
                                            $wfSteps = $workflow['steps'];
                                            if (in_array($wfsId, $params)) {
                                                $ruleFor .= $wfSteps . ', ';
                                            }
                                        }
                                    }
                                }
                            } elseif ($ruleForName == automationConstants::$automation_Branch) {
                                $branchArray = [];
                                $branchData = [];
                                $branchArray = tblBranch::GetAll([
                                    'processingCompanyId' => $assignedPCID,
                                    'activeStatus'        => 1,
                                ]);
                                if (count($branchArray) > 0) {
                                    if (count($PCWFStepsArray ?? []) > 0 || count($PCFileSubstatusArray ?? []) > 0 || count($ruleForOptions ?? []) > 0) $ruleFor .= '<br>';
                                    $ruleFor .= '<b>' . automationConstants::$automation_Branch . ':</b><br>';
                                    foreach ($branchArray as $branch) {
                                        if (in_array($branch->executiveId, $params)) {
                                            $ruleFor .= $branch->LMRExecutive . ', ';
                                        }
                                    }
                                }
                            } elseif ($ruleForName == automationConstants::$automation_Broker) {
                                $brokerData = [];
                                $brokerArray = listAllAgents::getObjects([
                                    'PCID'           => $assignedPCID,
                                    'externalBroker' => 0,
                                ]);
                                if (count($brokerArray) > 0) {
                                    if (count($branchArray ?? []) > 0
                                        || count($PCWFStepsArray ?? []) > 0
                                        || count($PCFileSubstatusArray ?? []) > 0
                                        || count($ruleForOptions ?? []) > 0
                                    ) $ruleFor .= '<br>';
                                    $ruleFor .= '<b>' . automationConstants::$automation_Broker . ':</b><br>';
                                    foreach ($brokerArray as $broker) {
                                        if (in_array($broker->brokerNumber, $params)) {
                                            $ruleFor .= $broker->brokerName . ', ';
                                        }
                                    }
                                }
                            } elseif ($ruleForName == automationConstants::$automation_LO) {
                                $loanOfficerArray = [];
                                $loanOfficerArray = listAllAgents::getObjects([
                                    'PCID'        => $assignedPCID,
                                    'loanOfficer' => 1,
                                ]);
                                if (count($loanOfficerArray) > 0) {
                                    if (count($brokerArray ?? []) > 0
                                        || count($branchArray ?? []) > 0
                                        || count($PCWFStepsArray ?? []) > 0
                                        || count($PCFileSubstatusArray ?? []) > 0
                                        || count($ruleForOptions ?? []) > 0
                                    ) $ruleFor .= '<br>';
                                    $ruleFor .= '<b>' . automationConstants::$automation_LoanOfficer . ':</b><br>';
                                    foreach ($loanOfficerArray as $loanOfficer) {
                                        if (in_array($loanOfficer->brokerNumber, $params)) {
                                            $ruleFor .= $loanOfficer->brokerName . ', ';
                                        }
                                    }
                                }
                            } elseif ($ruleForName == automationConstants::$automation_BorrowerStatus) {
                                $borrowerStatusArray = [];
                                $borrowerStatusArray = borrowerStatusArray::$borrowerStatusArray;
                                if (count($borrowerStatusArray) > 0) {
                                    if (count($loanOfficerArray ?? []) > 0
                                        || count($brokerArray ?? []) > 0
                                        || count($branchArray ?? []) > 0
                                        || count($PCWFStepsArray ?? []) > 0
                                        || count($PCFileSubstatusArray ?? []) > 0
                                        || count($ruleForOptions ?? []) > 0
                                    ) $ruleFor .= '<br>';
                                    $ruleFor .= '<b>' . automationConstants::$automation_BorrowerStatus_Title . ':</b><br>';
                                    foreach ($borrowerStatusArray as $borrowerStatus) {
                                        if (in_array($borrowerStatus, $params)) {
                                            $ruleFor .= $borrowerStatus . ', ';
                                        }
                                    }
                                }
                            } elseif ($ruleForName == automationConstants::$automation_BorrowerType) {
                                $borrowerTypeArray = [];
                                $borrowerTypeArray = glBorrowerType::$glBorrowerTypeArray;
                                if (count($borrowerTypeArray) > 0) {
                                    if (count($borrowerStatusArray ?? []) > 0
                                        || count($loanOfficerArray ?? []) > 0
                                        || count($brokerArray ?? []) > 0
                                        || count($branchArray ?? []) > 0
                                        || count($PCWFStepsArray ?? []) > 0
                                        || count($PCFileSubstatusArray ?? []) > 0
                                        || count($ruleForOptions ?? []) > 0
                                    ) $ruleFor .= '<br>';
                                    $ruleFor .= '<b>' . automationConstants::$automation_BorrowerType_Title . ':</b><br>';
                                    foreach ($borrowerTypeArray as $borrowerType) {
                                        if (in_array($borrowerType, $params)) {
                                            $ruleFor .= $borrowerType . ', ';
                                        }
                                    }
                                }
                            } elseif ($ruleForName == automationConstants::$automation_LoanProgram) {
                                $loanProgramData = [];
                                $PCServiceTypeInfoArray = getPCServiceType::getReport([
                                    'PCID'                    => cypher::myDecryption($PCID),
                                    'moduleCode'              => $fileType,
                                    'keyNeeded'               => 'n',
                                    'marketPlaceStatus'       => 'all',
                                    'marketPlacePublicStatus' => 'all',
                                ]);
                                if (count($PCServiceTypeInfoArray) > 0) {
                                    if (count($borrowerTypeArray ?? []) > 0
                                        || count($borrowerStatusArray ?? []) > 0
                                        || count($loanOfficerArray ?? []) > 0
                                        || count($brokerArray ?? []) > 0
                                        || count($branchArray ?? []) > 0
                                        || count($PCWFStepsArray ?? []) > 0
                                        || count($PCFileSubstatusArray ?? []) > 0
                                        || count($ruleForOptions ?? []) > 0
                                    ) $ruleFor .= '<br>';
                                    $ruleFor .= '<b>' . automationConstants::$automation_LoanProgram_Title . ':</b><br>';
                                    foreach ($PCServiceTypeInfoArray as $PCServiceType) {
                                        if (in_array($PCServiceType['LMRClientType'], $params)) {
                                            $ruleFor .= $PCServiceType['serviceType'] . ', ';
                                        }
                                    }
                                }
                            } elseif ($ruleForName == automationConstants::$automation_InitialSOW ||
                                      $ruleForName == automationConstants::$automation_DrawRequest ||
                                      $ruleForName == automationConstants::$automation_Revision) {
                                $drawRequestStatusArray = drawRequestStatusArray::$drawRequestStatusArray;
                                if (count($drawRequestStatusArray) > 0) {
                                    if (count($PCServiceTypeInfoArray ?? []) > 0
                                        || count($borrowerTypeArray ?? []) > 0
                                        || count($borrowerStatusArray ?? []) > 0
                                        || count($loanOfficerArray ?? []) > 0
                                        || count($brokerArray ?? []) > 0
                                        || count($branchArray ?? []) > 0
                                        || count($PCWFStepsArray ?? []) > 0
                                        || count($PCFileSubstatusArray ?? []) > 0
                                        || count($ruleForOptions ?? []) > 0
                                    ) $ruleFor .= '<br>';

                                    // Display the appropriate title based on rule type
                                    if ($ruleForName == automationConstants::$automation_InitialSOW) {
                                        $ruleFor .= '<b>' . automationConstants::$automation_InitialSOW_Title . ':</b><br>';
                                    } elseif ($ruleForName == automationConstants::$automation_DrawRequest) {
                                        $ruleFor .= '<b>' . automationConstants::$automation_DrawRequest_Title . ':</b><br>';
                                    } elseif ($ruleForName == automationConstants::$automation_Revision) {
                                        $ruleFor .= '<b>' . automationConstants::$automation_Revision_Title . ':</b><br>';
                                    }

                                    foreach ($drawRequestStatusArray as $statusKey => $statusValue) {
                                        if (in_array($statusKey, $params)) {
                                            $ruleFor .= $statusValue . ', ';
                                        }
                                    }
                                }
                            }
                        }
                    }

                    //events/actions
                    $data = ['PCID' => cypher::myDecryption($PCID), 'tblARId' => $ruleId];
                    $ruleActions = getAutomatedActions::getReport($data);
                    $noOfActions = count($ruleActions);
                    $actionTitle = '';
                    foreach ($ruleActions as $title) {
                        $titleActionId = $title['actionId'];
                        $titleActionName = $title['action'];
                        $titleActionType = $title['eventType'];
                        $contEventStatus = $title['contEventStatus'];
                        $actionParams = ['PCID' => cypher::myDecryption($PCID), 'id' => $titleActionId, 'action' => $titleActionName];
                        $actionDetails = getAutomatedActionTitle::getReport($actionParams);
                        $acDeDecode = json_decode($actionDetails);
                        $actionTitleName = $acDeDecode->actionTitle;
                        $actionStatus = $acDeDecode->status == 1 ? 'text-success' : 'text-danger';
                        if ($contEventStatus == 1) {
                            $actionStatus = 'text-primary';
                        }
                        if ($titleActionName === automationConstants::$automation_Employee) {
                            $actionTitle .= "<span class='" . $actionStatus . "'>$actionTitleName" . ' [Assign Loan File] </span><br>';
                        } elseif ($titleActionName === automationConstants::$automation_change_file_status) {
                            $updateStatusTxt = 'Yes (' . $actionTitleName . ')';
                            $actionTitle = "<span class='" . $actionStatus . "'>" . '(' . automationConstants::$automation_change_file_status . '/ ' . $titleActionType . ')' . "</span><br>";
                        } else {
                            $actionTitle .= "<span class='" . $actionStatus . "'>$actionTitleName" . ' (' . $titleActionName . ' / ' . $titleActionType . ') </span><br>';
                        }
                    }
                    ?>
                    <tr>
                        <td><?php echo $no + 1; ?></td>
                        <td><?php echo $ruleName; ?></td>
                        <td><?php echo $ruleFor; ?></td>
                        <td><?php echo $updateStatusTxt; ?></td>
                        <td><?php echo $actionTitle; ?></td>
                        <td><?php echo $createdDate; ?></td>
                        <td><?php echo $updatedDate; ?></td>
                        <td class="text-center"><?php echo $noOfActions; ?></td>
                        <td><?php echo $ruleStatus; ?></td>
                        <td>
                            <a class="btn btn-xs btn-light btn-text-primary btn-hover-primary btn-icon m-1 editRule"
                               href="<?php echo CONST_URL_BOSSL; ?>automatedActions.php?pcId=<?php echo $PCID; ?>&userGroup=Employee&view=form&tabNumb=4&id=<?php echo cypher::myEncryption($rule['id']); ?>">
                                <i class="tooltipAjax far fa-edit" title="" data-original-title="Click to edit"
                                   aria-hidden="true"></i>
                            </a> |
                            <a href="javascript:void(0)"
                               class="btn btn-xs btn-light btn-text-primary btn-hover-primary btn-icon m-1 removeRule"
                               id="<?php echo $rule['id']; ?>">
                                <i class="tooltipAjax flaticon2-trash" title="" data-original-title="Click to delete"
                                   aria-hidden="true"></i>
                            </a> |
                            <div> <?php
                                loanForm::$isPublicUser = null;
                                echo loanForm::changeLog(
                                    $rule['id'],
                                    'ruleStatus',
                                    tblAutomatedRules::class,
                                    'Rule Status'
                                ); ?>
                            </div>
                        </td>
                    </tr>
                    <?php
                    $no++;
                }
                if ($no == 0) { ?>
                    <tr>
                        <td colspan="10" class="text-center">No automated rules added</td>
                    </tr>
                    <?php
                }
                ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<div class="clearfix"></div>
<div class="col-md-12 no-padding">
    <?php if ($recNumbEnd > 0) {
        $myForm = 'automatedRulesListForm';
        require 'pageLinks.php';
    } ?>
</div>
<script>
    $(document).ready(function () {
        //redirect to auto rule create form
        $('#autoRuleForm').click(function () {
            let tabNumb = 4; // automated rules
            //var PCID = "<?php //echo $PCID;?>";
            let userGroup = "<?php echo $userGroup;?>";
            window.location = "<?php echo CONST_URL_BOSSL; ?>" + "automatedActions.php?&userGroup=" + userGroup + "&view=form&tabNumb=" + tabNumb;
        });

        $('.removeRule').click(function () {
            let PCID = "<?php echo $PCID;?>";
            let id = $(this).attr('id');
            let conf = 'Are you sure to delete this rule?';
            $.confirm({
                icon: 'fa fa-warning',
                closeIcon: true,
                title: 'Confirm',
                content: conf,
                type: 'red',
                backgroundDismiss: true,
                buttons: {
                    yes: function () {
                        $.ajax({
                            type: "POST",
                            url: "<?php echo CONST_URL_BOSSL; ?>" + 'automatedRulesController.php',
                            data: {action: "remove", PCID: PCID, id: id},
                            beforeSend: function () {

                            },
                            success: function (resp) {
                                let msg;
                                let type;
                                if (resp > 0) {
                                    type = '';
                                    msg = "Rule deleted successfully";
                                } else {
                                    type = 'error';
                                    msg = "Error deleting Rule, try again.";
                                }
                                toastrNotification(msg, type);
                                setTimeout(function () {
                                    location.reload();
                                }, 1000);
                            }
                        });
                    },
                    cancel: function () {

                    },
                },
                onClose: function () {

                },
            });
        });
    });
</script>
