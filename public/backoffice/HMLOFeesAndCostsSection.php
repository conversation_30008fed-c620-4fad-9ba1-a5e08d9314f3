<?php

global $lockedSections, $allowToEdit, $fileTab, $originationPointsRate, $brokerPointsRate, $disabledInputForClient,
       $tabIndex, $fldEditOpt, $origination_based_on_total_loan_amt, $originationPointsValue, $bufferAndMessengerFee,
       $closingCostFinancingFee, $applicationFee, $estdTitleClosingFee, $isEF, $drawsSetUpFee, $valuationAVEFee,
       $valuationBPOFee, $valuationAVMFee, $creditReportFee, $taxServiceFee, $wireFee, $floodCertificateFee,
       $inspectionFees, $dueDiligence, $thirdPartyFees, $escrowFees, $underwritingFees, $prePaidInterest,
       $insurancePremium, $wireTransferFeeToTitle, $pastDuePropertyTaxes, $wholeSaleAdminFee,
       $employmentVerificationFee, $taxReturnOrderFee, $wholeSaleAdminFee, $employmentVerificationFee,
       $taxReturnOrderFee, $taxImpoundsMonth, $taxImpoundsMonthAmt, $taxImpoundsFee, $diemDays,
       $totalDailyInterestCharge, $lien1Rate, $totalEstPerDiem, $interestChargedFromDate,
       $interestChargedEndDate, $broker_based_on_total_loan_amt, $brokerPointsValue, $travelNotaryFee,
       $attorneyFee, $appraisalFee, $processingFee, $drawsFee, $valuationCMAFee, $creditCheckFee,
       $backgroundCheckFee, $documentPreparationFee, $servicingSetUpFee, $floodServiceFee, $projectFeasibility,
       $UccLienSearch, $otherFee, $recordingFee, $propertyTax, $realEstateTaxes, $payOffLiensCreditors,
       $wireTransferFeeToEscrow, $survey, $cityCountyTaxStamps, $constructionHoldbackFee, $insImpoundsMonth,
       $insImpoundsMonthAmt, $insImpoundsFee, $totalFeesAndCost, $perClosingCostFinanced, $PCID,
       $netLenderFundsToBorrower, $feeSectionTotalLoanAmtOpt, $totalLoanAmount, $checkDisplayTermSheet, $lenderNotes;
global $publicUser, $userRole, $isTherePrePaymentPenalty, $prePaymentPenaltyPercentage,
       $prePaymentPenalty, $prePaymentPenaltyResArr, $prepayentSectionDisplay, $exitFeeAmount,
       $exitFeePoints, $extensionOptionPercentage, $extensionRatePercentage,
       $extensionOption, $extensionOptionsAmt, $perDiemToolTip, $customLoanFeeDataStatus;
global $executiveId, $brokerNumber, $prePaymentSelectValArr;

use models\composite\oFile\getFileInfo\fileHMLONewLoanInfo;
use models\composite\oHMLOInfo\fileExtensionOptions;
use models\composite\proposalFormula;
use models\constants\accrualTypes;
use models\constants\gl\glCustomJobForBranch;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glHMLOExtensionOption;
use models\constants\gl\glHMLOPrePaymentPenalty;
use models\constants\gl\glPCID;
use models\constants\gl\glprePaymentPenalty;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\loanForm;
use models\CustomField;
use models\HMLOLoanTermsCalculation;
use models\lendingwise\tblFile;
use models\lendingwise\tblFileExtensionOptions;
use models\lendingwise\tblFileHMLONewLoanInfo;
use models\lendingwise\tblFileHMLOPropInfo;
use models\myFileInfo;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;

$glHMLOPrePaymentPenalty = glHMLOPrePaymentPenalty::$glHMLOPrePaymentPenalty;
$glHMLOExtensionOption = glHMLOExtensionOption::$glHMLOExtensionOption;

$exitFeePoints = Strings::showField('exitFeePoints', 'fileHMLONewLoanInfo');
$exitFeeAmount = Strings::showField('exitFeeAmount', 'fileHMLONewLoanInfo');

//cv3 New Fields
$LMRId = LMRequest::$LMRId;
$fileHMLONewLoanInfo = fileHMLONewLoanInfo::getReport($fileTab, $LMRId);

$cv3OriginationPoint = $fileHMLONewLoanInfo['cv3OriginationPoint'] ?? '0.00';
$cv3ReferralPoint = $fileHMLONewLoanInfo['cv3ReferralPoint'] ?? '0.00';
$cv3OriginationAmount = $fileHMLONewLoanInfo['cv3OriginationAmount'] ?? '0.00';
$cv3ReferralAmount = $fileHMLONewLoanInfo['cv3ReferralAmount'] ?? '0.00';

$extensionFields = fileExtensionOptions::$fileExtensionOptionsData ?? [];
if (!sizeof($extensionFields)) {
    $extensionFields = [
        new tblFileExtensionOptions(),
    ];
}
if (in_array('Fees & Costs', $lockedSections)) {
    $BackupAllowToEdit = $allowToEdit;
    $allowToEdit = false;
}

/**
 * @param $clsName
 * @return mixed|string
 */
function tdHidetoVisibilityHidden($clsName)
{
    if (trim($clsName) == 'secShow') {
        return '';
    } else {
        return $clsName;
    }
}

//next block taken from HMLONewLoanInfoForm.php
$prePaymentPenaltyResArr = glprePaymentPenalty::getPCLevelPrePaymentPenalty($PCID, 'FC');
//end of block taken from HMLONewLoanInfoForm.php

$secArr = BaseHTML::sectionAccess2(['sId' => 'FC', 'opt' => $fileTab]);
loanForm::pushSectionID('FC');

if (trim(BaseHTML::fieldAccess([
        'fNm' => 'estPerDiemInt', 'sArr' => $secArr, 'opt' => 'D'])) == 'secHide' &&
    trim(BaseHTML::fieldAccess([
        'fNm' => 'interestChargedFrom', 'sArr' => $secArr, 'opt' => 'D'])) == 'secHide' &&
    trim(BaseHTML::fieldAccess([
        'fNm' => 'interestChargedEnd', 'sArr' => $secArr, 'opt' => 'D'])) == 'secHide'
) {
    $denimSeperator = 'd-none';
} else {
    $denimSeperator = '';
}

$myFileInfoObject = new myFileInfo();
$myFileInfoObject->LMRId = $LMRId;

$fileHMLONewLoanInfoObject = $myFileInfoObject->getFileHMLONewLoanInfo();
$sellerCreditsFee = $fileHMLONewLoanInfoObject->sellerCreditsFee;
?>
<!-- HMLOFeesAndCostsSection.php -->
<div class="card card-custom FC FCCard <?php if (count($secArr) <= 0) {
    echo 'secHide';
} ?> ">

    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label">
                <?php echo BaseHTML::getSectionHeading('FC'); ?>
            </h3>
            <?php if (trim(BaseHTML::getSectionTooltip('FC')) != '') { ?>&nbsp;
                <i class="popoverClass fas fa-info-circle text-primary "
                   data-html="true"
                   data-content="<?php echo BaseHTML::getSectionTooltip('FC'); ?>"></i>
            <?php } ?>
        </div>
        <div class="card-toolbar">
            <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none cursor-pointer"
                  data-card-tool="toggle"
                  data-toggle="tooltip" data-placement="top" title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>
            <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none cursor-pointer"
                  data-card-tool="reload"
                  data-toggle="tooltip" data-placement="top" title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </span>
            <span
                    class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer"
                    data-card-tool="toggle"
                    data-section="FCCard"
                    data-toggle="tooltip" data-placement="top" title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>
        </div>
    </div>
    <input type="hidden" name="oldOriginationPointsRate" id="oldOriginationPointsRate"
           value="<?php echo rtrim($originationPointsRate, 0); ?>">
    <input type="hidden" name="oldBrokerPointsRate" id="oldBrokerPointsRate"
           value="<?php echo rtrim($brokerPointsRate, 0); ?>">
    <div class="card-body FCCard_body">
        <?php if ($customLoanFeeDataStatus == 1) { ?>
            <div class="alert alert-custom alert-light-primary fade show mb-5" role="alert">
                <div class="alert-icon"><i class="flaticon-warning"></i></div>
                <div class="alert-text font-size-h4 text-primary">The fees below are Auto-populated from your loan
                    guidelines. Click save below if
                    they are correct.
                </div>
            </div>
        <?php } ?>
        <div class="row">
            <div class="col-md-6">
                <div class="row">
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div
                                    class="form-group <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'originationPoints', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                                <div class="row mb-2">
                                    <?php echo loanForm::label(
                                        'originationPoints',
                                        'col-md-6 font-weight-bold p-lg-0 my-lg-2',
                                        '',
                                        '@' . loanForm::changeLog(
                                            LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                            'originationPointsRate',
                                            tblFileHMLONewLoanInfo::class,
                                            loanForm::getFieldLabel('originationPoints')
                                        )); ?>
                                    <div class="col-md-6 p-lg-0">
                                        <div class="input-group">
                                            <input type="text" class="form-control input-sm"
                                                   name="originationPointsRate"
                                                   id="originationPointsRate"
                                                   value="<?php echo rtrim($originationPointsRate, 0); ?>"
                                                   TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                                   onblur="updateOriginationBrokerPoints('Origination'); validateMinMaxLoanGuidelines();" <?php if (($originationPointsRate > 0 && $fldEditOpt == 2) || HMLOLoanTermsCalculation::$lockOriginationValue) echo ' readonly '; ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'originationPoints', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                                            <div class="input-group-append">
                                      <span class="input-group-text">
                                        Points
                                      </span>
                                            </div>
                                        </div>
                                    </div>
                                    <input type="hidden" name="origination_based_on_total_loan_amt"
                                           id="origination_based_on_total_loan_amt"
                                           value="<?php echo $origination_based_on_total_loan_amt; ?>">
                                </div>
                                <?php if (glCustomJobForProcessingCompany::cv3FeesAndCostNewFields($PCID)
                                    && glCustomJobForBranch::cv3BranchIsRetail($executiveId)
                                    && $brokerNumber
                                ) {
                                    //calculate the cv3 points
                                    $cv3Points = HMLOLoanTermsCalculation::cv3PointsCalculation($originationPointsRate, $cv3ReferralPoint);
                                    $cv3OriginationPoint = $cv3Points['cv3OriginationPoint'];
                                    $cv3ReferralPoint = $cv3Points['cv3ReferralPoint'];
                                    ?>
                                    <div class="row mb-2">
                                        <label class="col-md-6">CV3 Origination Points</label>
                                        <div class="col-md-6 p-lg-0">
                                            <div class="input-group">
                                                <input type="text"
                                                       class="form-control input-sm"
                                                       name="cv3OriginationPoint"
                                                       id="cv3OriginationPoint"
                                                       value="<?php echo $cv3OriginationPoint; ?>"
                                                       readonly/>
                                                <div class="input-group-append">
                                              <span class="input-group-text">
                                                Points
                                              </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-md-6">Referral Points
                                            <?php
                                            echo loanForm::changeLog(
                                                LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                                'cv3ReferralPoint',
                                                tblFileHMLONewLoanInfo::class,
                                                'Referral Points'
                                            );
                                            ?>
                                        </label>
                                        <div class="col-md-6 p-lg-0">
                                            <div class="input-group">
                                                <input type="text"
                                                       class="form-control input-sm"
                                                       name="cv3ReferralPoint"
                                                       id="cv3ReferralPoint"
                                                       value="<?php echo $cv3ReferralPoint; ?>"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       autocomplete="off"
                                                       onchange="cv3Origination.cv3OriginationPoints();"/>
                                                <div class="input-group-append">
                                              <span class="input-group-text">
                                                Points
                                              </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php } ?>
                                <div class="row pl-5 pl-sm-0 pl-md-0 pl-lg-0 pl-xl-0 pt-5">
                                    <div class="checkbox-inline">
                                        <label
                                                class="checkbox checkbox-outline <?php if (HMLOLoanTermsCalculation::$lockOriginationValue) echo ' checkbox-disabled '; ?> ">
                                            <input type="checkbox"
                                                   name="origination_total_loan_amt_checked"
                                                   id="origination_total_loan_amt_checked"
                                                <?php if (HMLOLoanTermsCalculation::$lockOriginationValue) echo ' disabled '; ?>
                                                   onclick="updateOriginationBrokerPoints('Origination');"
                                                   value="1" <?php if ($origination_based_on_total_loan_amt > 0) echo 'checked'; ?>><span></span>
                                            Include Closing Cost Financed
                                        </label>
                                        <i class="fa fa-info-circle tooltipClass text-primary" data-html="true"
                                           title="<em>By default the closing costs financed amount is not included in the calculation of points. Check the box to include that amount when calculating total points</em>"></i>
                                    </div>
                                </div>
                            </div>
                        <?php } else { ?>
                            <div
                                    class="form-group <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'originationPoints', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                                <?php echo loanForm::label('originationPoints', 'font-weight-bold p-lg-0 my-lg-2',
                                    '', '@'); ?>
                                <label class="font-weight-bold ml-10">
                                    <span class=""><?php echo rtrim($originationPointsRate, 0); ?></span>
                                    <span>Points</span>
                                </label>
                            </div>
                        <?php } ?>
                    </div>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="row mb-2">
                                <div
                                        class="<?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'originationPoints', 'sArr' => $secArr, 'opt' => 'D'])); ?>">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text" TABINDEX="<?php echo $tabIndex++; ?>"
                                               placeholder="0.00"
                                               name="originationPointsValue"
                                               id="originationPointsValue"
                                               class="form-control input-sm"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal($originationPointsValue) ?>"
                                               onkeyup="return restrictAlphabetsLoanTerms(this)"
                                               onblur="currencyConverter(this, this.value);calculateOriginationPointsRate();"
                                            <?php if (($originationPointsRate > 0 && $fldEditOpt == 2) || HMLOLoanTermsCalculation::$lockOriginationValue) echo ' readonly '; ?> >
                                        <div class="input-group-append">
                                            <span class="input-group-text">
                                                <?php
                                                echo loanForm::changeLog(
                                                    LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                                    'originationPointsValue',
                                                    tblFileHMLONewLoanInfo::class,
                                                    'Origination Points Value'
                                                );
                                                ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php if (glCustomJobForProcessingCompany::cv3FeesAndCostNewFields($PCID)
                                && glCustomJobForBranch::cv3BranchIsRetail($executiveId)
                                && $brokerNumber
                            ) {
                                //calculate cv3 Amount
                                $cv3Amount = HMLOLoanTermsCalculation::cv3AmountCalculation($originationPointsValue, $cv3OriginationAmount, $cv3ReferralAmount);
                                if (!$cv3OriginationAmount || $cv3OriginationAmount = '0.00') {
                                    $cv3OriginationAmount = $cv3Amount['cv3OriginationAmount'];
                                }
                                if (!$cv3ReferralAmount || $cv3ReferralAmount = '0.00') {
                                    $cv3ReferralAmount = $cv3Amount['cv3ReferralAmount'];
                                }
                                ?>
                                <div class="row mb-2">
                                    <div class="">
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="text"
                                                   placeholder="0.00"
                                                   name="cv3OriginationAmount"
                                                   id="cv3OriginationAmount"
                                                   class="form-control input-sm"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal($cv3OriginationAmount); ?>"
                                                   readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mb-2">
                                    <div class="">
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="text" tabindex="<?php echo $tabIndex++; ?>"
                                                   placeholder="0.00"
                                                   name="cv3ReferralAmount"
                                                   id="cv3ReferralAmount"
                                                   class="form-control input-sm"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal($cv3ReferralAmount); ?>"
                                                   onkeyup="return restrictAlphabetsLoanTerms(this)"
                                                   onblur="currencyConverter(this, this.value);"
                                                   onchange="cv3Origination.cv3OriginationAmount()">
                                            <div class="input-group-append">
                                                <span class="input-group-text">
                                                    <?php
                                                    echo loanForm::changeLog(
                                                        LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                                        'cv3ReferralAmount',
                                                        tblFileHMLONewLoanInfo::class,
                                                        'Referral Amount'
                                                    );
                                                    ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php } ?>
                            <div
                                    class=" row pl-5 pl-sm-0 pl-md-0 pl-lg-0 pl-xl-0 pt-5 <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'originationPoints', 'sArr' => $secArr, 'opt' => 'D'])); ?>">
                                <label class="col-md-6 align-self-center" for="lockOriginationValue">Lock
                                    Origination Fee </label>
                                <div class="col-md-4">
                                    <div class="switch switch-sm switch-icon">
                                        <label class="font-weight-bold">
                                            <input class="form-control "
                                                <?php if (HMLOLoanTermsCalculation::$lockOriginationValue) echo 'checked'; ?>
                                                   id="lockOrigination"
                                                   type="checkbox"
                                                   onchange="toggleSwitch('lockOrigination', 'lockOriginationValue', '1', '0' );originationPoints.lockOriginationValue('lockOriginationValue');">
                                            <input type="hidden" name="lockOriginationValue"
                                                   id="lockOriginationValue"
                                                   value="<?php echo HMLOLoanTermsCalculation::$lockOriginationValue; ?>">
                                            <span></span>
                                        </label>
                                        <i class="fa fa-info-circle tooltipClass text-primary ml-2"
                                           data-html="true"
                                           title="If this box is checked, then the dollar amount for your origination fee will be locked in and only your points will auto-calculate."></i>
                                    </div>
                                </div>
                            </div>
                        <?php } else { ?>
                            <div
                                    class="form-group <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'originationPoints', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                                <label class="font-weight-bold font-weight-bold col-md-6 p-lg-0 my-lg-2">
                                    <span
                                            class="">$ <?php echo Currency::formatDollarAmountWithDecimal($originationPointsValue); ?></span>
                                </label>
                            </div>
                        <?php } ?>
                    </div>
                </div>

                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'bufferandMessengerFees', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('bufferandMessengerFees', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="bufferAndMessengerFee"
                                       placeholder="0.00"
                                       id="bufferAndMessengerFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($bufferAndMessengerFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'bufferandMessengerFees', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($bufferAndMessengerFee) ?></label>
                        <?php } ?>
                    </div>
                </div>

                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'settlementorClosingEscrowFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('settlementorClosingEscrowFee', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="escrowFees" id="escrowFees"
                                       placeholder="0.00"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($escrowFees) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();loanCalculation.initLTC2();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'settlementorClosingEscrowFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($escrowFees) ?></label>
                        <?php } ?>
                    </div>
                </div>

                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'closingCostFinancingFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('closingCostFinancingFee', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="closingCostFinancingFee"
                                       placeholder="0.00"
                                       id="closingCostFinancingFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($closingCostFinancingFee) ?>"
                                       size="20" maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'closingCostFinancingFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>
                                $ <?php echo Currency::formatDollarAmountWithDecimal($closingCostFinancingFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'applicationFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('applicationFee', 'col-md-6'); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="applicationFee"
                                       placeholder="0.00"
                                       id="applicationFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($applicationFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'applicationFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label> $ <?php echo Currency::formatDollarAmountWithDecimal($applicationFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'estimatedTitleInsuranceFees', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('estimatedTitleInsuranceFees', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="estdTitleClosingFee"
                                       placeholder="0.00"
                                       id="estdTitleClosingFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($estdTitleClosingFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'estimatedTitleInsuranceFees', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>
                                $ <?php echo Currency::formatDollarAmountWithDecimal($estdTitleClosingFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <?php if ($isEF != 1) { ?>
                    <div
                            class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'drawsSetUpFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                        <?php echo loanForm::label('drawsSetUpFee', 'col-md-6'); ?>
                        <div class="col-md-6">
                            <?php if ($allowToEdit && $disabledInputForClient) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input type="text" class="form-control input-sm" name="drawsSetUpFee"
                                           placeholder="0.00"
                                           id="drawsSetUpFee"
                                           value="<?php echo Currency::formatDollarAmountWithDecimal($drawsSetUpFee) ?>"
                                           size="20"
                                           maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                           onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'drawsSetUpFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label> $ <?php echo Currency::formatDollarAmountWithDecimal($drawsSetUpFee) ?></label>
                            <?php } ?>
                        </div>
                    </div>
                    <div
                            class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'valuationBpo', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                        <?php echo loanForm::label('valuationBpo', 'col-md-6 '); ?>
                        <div class="col-md-6">
                            <?php if ($allowToEdit && $disabledInputForClient) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input type="text" class="form-control input-sm" name="valuationBPOFee"
                                           placeholder="0.00"
                                           id="valuationBPOFee"
                                           value="<?php echo Currency::formatDollarAmountWithDecimal($valuationBPOFee) ?>"
                                           size="20"
                                           maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                           onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'valuationBpo', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal($valuationBPOFee) ?></label>
                            <?php } ?>
                        </div>
                    </div>
                <?php } ?>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'valuationAVE', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('valuationAVE', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="valuationAVEFee"
                                       placeholder="0.00"
                                       id="valuationAVEFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($valuationAVEFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'valuationAVE', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($valuationAVEFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'creditReport', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('creditReport', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="creditReportFee"
                                       placeholder="0.00"
                                       id="creditReportFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($creditReportFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'creditReport', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($creditReportFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'taxService', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('taxService', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="taxServiceFee" id="taxServiceFee"
                                       placeholder="0.00"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($taxServiceFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'taxService', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($taxServiceFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'wireFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('wireFee', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="wireFee" id="wireFee"
                                       placeholder="0.00"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($wireFee) ?>" size="20"
                                       maxlength="68"
                                       TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'wireFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($wireFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <?php if ($isEF != 1) { ?>
                    <div
                            class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'floodCertificate', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                        <?php echo loanForm::label('floodCertificate', 'col-md-6 '); ?>
                        <div class="col-md-6">
                            <?php if ($allowToEdit && $disabledInputForClient) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input type="text" class="form-control input-sm" name="floodCertificateFee"
                                           placeholder="0.00"
                                           id="floodCertificateFee"
                                           value="<?php echo Currency::formatDollarAmountWithDecimal($floodCertificateFee) ?>"
                                           size="20" maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>"
                                           autocomplete="off"
                                           onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'floodCertificate', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal($floodCertificateFee) ?></label>
                            <?php } ?>
                        </div>
                    </div>
                <?php } ?>
                <div class="form-group row <?php echo loanForm::showField('inspectionFees'); ?>">
                    <?php echo loanForm::label('inspectionFees', 'col-md-6'); ?>
                    <div class="col-md-6">
                        <?php echo loanForm::currency(
                            'inspectionFees',
                            $allowToEdit && $disabledInputForClient,
                            $tabIndex++,
                            $inspectionFees,
                            'input-sm',
                            'updateLoanDetail();'
                        ); ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'dueDiligence', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('dueDiligence', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="dueDiligence" id="dueDiligence"
                                       placeholder="0.00"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($dueDiligence) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'dueDiligence', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($dueDiligence) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'lenderCredittoOffset3rdPartyFees', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('lenderCredittoOffset3rdPartyFees', 'col-md-6 ',); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="thirdPartyFees"
                                       placeholder="0.00"
                                       id="thirdPartyFees"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($thirdPartyFees) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'lenderCredittoOffset3rdPartyFees', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($thirdPartyFees) ?></label>
                        <?php } ?>
                    </div>
                </div>

                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'underwritingFees', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('underwritingFees', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="underwritingFees"
                                       placeholder="0.00"
                                       id="underwritingFees"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($underwritingFees) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'underwritingFees', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($underwritingFees) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'prepaidInterest', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('prepaidInterest', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="prePaidInterest"
                                       placeholder="0.00"
                                       id="prePaidInterest"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($prePaidInterest) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'prepaidInterest', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($prePaidInterest) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'insurancePremium', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('insurancePremium', 'col-md-6 ',); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="insurancePremium"
                                       placeholder="0.00"
                                       id="insurancePremium"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($insurancePremium) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'insurancePremium', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($insurancePremium) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'wireTransferFeetoTitle', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('wireTransferFeetoTitle', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="wireTransferFeeToTitle"
                                       placeholder="0.00"
                                       id="wireTransferFeeToTitle"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($wireTransferFeeToTitle) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'wireTransferFeetoTitle', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($wireTransferFeeToTitle) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'pastDuePropertyTaxesFC', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('pastDuePropertyTaxesFC', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="pastDuePropertyTaxes"
                                       placeholder="0.00"
                                       id="pastDuePropertyTaxes"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($pastDuePropertyTaxes) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'pastDuePropertyTaxesFC', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($pastDuePropertyTaxes) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'wholesaleAdminFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('wholesaleAdminFee', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="wholeSaleAdminFee"
                                       placeholder="0.00"
                                       id="wholeSaleAdminFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($wholeSaleAdminFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'wholesaleAdminFee', 'sArr' => $secArr, 'opt' => 'I'])); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($wholeSaleAdminFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'employmentVerificationFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('employmentVerificationFee', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="employmentVerificationFee"
                                       placeholder="0.00"
                                       id="employmentVerificationFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($employmentVerificationFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'employmentVerificationFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($employmentVerificationFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'taxReturnOrderFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('taxReturnOrderFee', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="taxReturnOrderFee"
                                       placeholder="0.00"
                                       id="taxReturnOrderFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($taxReturnOrderFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'taxReturnOrderFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($taxReturnOrderFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'taximpounds', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php if ($allowToEdit && $disabledInputForClient) { ?>
                        <div class="col-md-6">
                            <div class="row">
                                <label class="col-md-12 font-weight-bold">
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'taximpounds', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                </label>
                                <div class=" col-md-12">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">Months</span>
                                        </div>
                                        <input type="number" class="form-control input-sm " name="taxImpoundsMonth"
                                               id="taxImpoundsMonth" value="<?php echo $taxImpoundsMonth; ?>"
                                               onblur="calculateTaxImpoundsFee('loanModForm', 'taxImpoundsFee');"
                                               TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"/>
                                        <div class="input-group-append">
                                            <span class="input-group-text">$ </span>
                                        </div>
                                        <input type="text" class="form-control input-sm " name="taxImpoundsMonthAmt"
                                               id="taxImpoundsMonthAmt"
                                               placeholder="0.00"
                                               value="<?php echo $taxImpoundsMonthAmt; ?>"
                                               onblur="currencyConverter(this, this.value);calculateTaxImpoundsFee('loanModForm', 'taxImpoundsFee');"
                                               TABINDEX="<?php echo $tabIndex++; ?>"
                                               autocomplete="off"/>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" TABINDEX="<?php echo $tabIndex++; ?>"
                                       placeholder="0.00"
                                       name="taxImpoundsFee"
                                       id="taxImpoundsFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($taxImpoundsFee) ?>"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();">
                            </div>
                        </div>
                    <?php } else { ?>
                        <span
                                class="H5"><?php echo $taxImpoundsMonth; ?></span>months @ $ <?php echo Currency::formatDollarAmountWithDecimal($taxImpoundsMonthAmt); ?>
                        <span class="H5"><?php echo Currency::formatDollarAmountWithDecimal($taxImpoundsFee) ?></span>
                    <?php } ?>
                </div>
            </div>


            <div class="mt-5 mt-sm-0 mt-md-0 mt-lg-0 mt-xl-0 col-md-6">
                <div class="row">
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) {
                            ?>
                            <div
                                    class="form-group <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'brokerPoints', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                                <div class="row mb-2">
                                    <?php echo loanForm::label(
                                        'brokerPoints',
                                        'col-md-6 font-weight-bold  my-lg-2',
                                        '',
                                        '@' . loanForm::changeLog(
                                            LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                            'brokerPointsRate',
                                            tblFileHMLONewLoanInfo::class,
                                            'Broker Points Rate'
                                        )); ?>
                                    <div class="col-md-6 p-lg-0">
                                        <div class="input-group">
                                            <input type="text" class="form-control input-sm"
                                                   placeholder="0.00"
                                                   name="brokerPointsRate" id="brokerPointsRate"
                                                   value="<?php echo rtrim($brokerPointsRate, 0); ?>"
                                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                                   autocomplete="off"
                                                   onkeyup="return restrictAlphabetsLoanTermsDecimal(this,3)"
                                                   onblur="updateOriginationBrokerPoints('Broker');" <?php if (($brokerPointsRate > 0 && $fldEditOpt == 2) || HMLOLoanTermsCalculation::$lockBrokerValue) echo ' readonly '; ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'brokerPoints', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                            <div class="input-group-append">
                                              <span class="input-group-text">
                                                Points
                                              </span>
                                            </div>
                                        </div>
                                    </div>
                                    <input type="hidden" name="broker_based_on_total_loan_amt"
                                           id="broker_based_on_total_loan_amt"
                                           value="<?php echo $broker_based_on_total_loan_amt; ?>">
                                </div>
                                <div class="row pl-5 pl-sm-0 pl-md-0 pl-lg-0 pl-xl-0 pt-5 ">
                                    <div class="checkbox-inline">
                                        <label
                                                class="checkbox checkbox-outline <?php if (HMLOLoanTermsCalculation::$lockBrokerValue) echo ' checkbox-disabled '; ?>">
                                            <input type="checkbox" name="broker_total_loan_amt_checked"
                                                   id="broker_total_loan_amt_checked"
                                                <?php if (HMLOLoanTermsCalculation::$lockBrokerValue) echo ' disabled '; ?>
                                                   onclick="updateOriginationBrokerPoints('Broker');" <?php if ($broker_based_on_total_loan_amt > 0) echo 'checked'; ?>
                                                   value="1"><span></span>
                                            Include Closing Cost Financed
                                        </label>
                                        <i class="fa fa-info-circle tooltipClass text-primary" data-html="true"
                                           title="<em>By default the closing costs financed amount is not included in the calculation of points. Check the box to include that amount when calculating total points</em>"></i>

                                    </div>
                                </div>
                            </div>
                        <?php } else { ?>
                            <div
                                    class="form-group <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'brokerPoints', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                                <?php echo loanForm::label('brokerPoints', 'font-weight-bold  my-lg-2', '', '@'); ?>
                                <label class="font-weight-bold ml-10">
                                    <span
                                            class=""><?php echo Currency::formatDollarAmountWithDecimal($brokerPointsRate) ?></span>
                                    <span>Points</span>
                                </label>
                            </div>
                        <?php } ?>
                    </div>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) {
                            ?>
                            <div class="row mb-2">
                                <div
                                        class=" <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'brokerPoints', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text" class="form-control input-sm"
                                               placeholder="0.00"
                                               tabindex="<?php echo $tabIndex++; ?>"
                                               name="brokerPointsValue"
                                               id="brokerPointsValue"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal($brokerPointsValue) ?>"
                                               onkeyup="return restrictAlphabetsLoanTerms(this)"
                                               onblur="currencyConverter(this, this.value);calculateBrokerPointsRate();"
                                            <?php if (($brokerPointsRate > 0 && $fldEditOpt == 2) || HMLOLoanTermsCalculation::$lockBrokerValue) echo ' readonly '; ?> >
                                        <div class="input-group-append">
                                             <span class="input-group-text">
                                                 <?php
                                                 echo loanForm::changeLog(
                                                     LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                                     'brokerPointsValue',
                                                     tblFileHMLONewLoanInfo::class,
                                                     'Broker Points Value'
                                                 );
                                                 ?>
                                             </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div
                                    class=" row pl-5 pl-sm-0 pl-md-0 pl-lg-0 pl-xl-0 pt-5 <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'brokerPoints', 'sArr' => $secArr, 'opt' => 'D'])); ?>">
                                <label class="col-md-6 align-self-center">Lock Broker Fee</label>
                                <div class="col-md-4">
                                    <div class="switch switch-sm switch-icon">
                                        <label class="font-weight-bold">
                                            <input class="form-control "
                                                <?php if (HMLOLoanTermsCalculation::$lockBrokerValue) echo 'checked'; ?>
                                                   id="lockBroker"
                                                   type="checkbox"
                                                   onchange="toggleSwitch('lockBroker', 'lockBrokerValue', '1', '0' );originationPoints.lockBrokerValue('lockBrokerValue');">
                                            <input type="hidden"
                                                   name="lockBrokerValue"
                                                   id="lockBrokerValue"
                                                   value="<?php echo HMLOLoanTermsCalculation::$lockBrokerValue; ?>">
                                            <span></span>
                                        </label>
                                        <i class="fa fa-info-circle tooltipClass text-primary ml-2"
                                           data-html="true"
                                           title="If this box is checked, then the dollar amount for your broker fee will be locked in and only your points will auto-calculate."></i>
                                    </div>
                                </div>
                            </div>

                        <?php } else { ?>
                            <div
                                    class="form-group <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'brokerPoints', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                                <label class="font-weight-bold">
                                    <span
                                            class="">$ <?php echo Currency::formatDollarAmountWithDecimal($brokerPointsValue) ?></span>
                                </label>
                            </div>
                        <?php } ?>
                    </div>
                </div>

                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'travelNotaryFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('travelNotaryFee', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="travelNotaryFee"
                                       placeholder="0.00"
                                       id="travelNotaryFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($travelNotaryFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'travelNotaryFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($travelNotaryFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'attorneyFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('attorneyFee', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="attorneyFee" id="attorneyFee"
                                       placeholder="0.00"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($attorneyFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'attorneyFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label> $ <?php echo Currency::formatDollarAmountWithDecimal($attorneyFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'appraisalFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('appraisalFee', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="appraisalFee" id="appraisalFee"
                                       placeholder="0.00"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($appraisalFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'appraisalFee', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                            </div>
                        <?php } else { ?>
                            <label> $ <?php echo Currency::formatDollarAmountWithDecimal($appraisalFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'processingFees', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('processingFees', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="processingFee" id="processingFee"
                                       placeholder="0.00"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($processingFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'processingFees', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                            </div>
                        <?php } else { ?>
                            <label> $ <?php echo Currency::formatDollarAmountWithDecimal($processingFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'brokerProcessingFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('brokerProcessingFee', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm"
                                       name="brokerProcessingFee"
                                       id="brokerProcessingFee"
                                       placeholder="0.00"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($fileHMLONewLoanInfoObject->brokerProcessingFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'brokerProcessingFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($fileHMLONewLoanInfoObject->brokerProcessingFee) ?></label>
                        <?php } ?>
                    </div>
                </div>


                <?php if ($isEF != 1) { ?>
                    <div
                            class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'drawsFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                        <?php echo loanForm::label('drawsFee', 'col-md-6  '); ?>
                        <div class="col-md-6">
                            <?php if ($allowToEdit && $disabledInputForClient) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input type="text" class="form-control input-sm" name="drawsFee" id="drawsFee"
                                           placeholder="0.00"
                                           value="<?php echo Currency::formatDollarAmountWithDecimal($drawsFee) ?>"
                                           size="20"
                                           maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                           onblur="currencyConverter(this, this.value);"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'drawsFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal($drawsFee) ?></label>
                            <?php } ?>
                        </div>
                    </div>
                <?php } ?>




                <?php if ($isEF != 1) { ?>

                    <div
                            class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'valuationAVM', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                        <?php echo loanForm::label('valuationAVM', 'col-md-6  '); ?>
                        <div class="col-md-6">
                            <?php if ($allowToEdit && $disabledInputForClient) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input type="text" class="form-control input-sm" name="valuationAVMFee"
                                           placeholder="0.00"
                                           id="valuationAVMFee"
                                           value="<?php echo Currency::formatDollarAmountWithDecimal($valuationAVMFee) ?>"
                                           size="20"
                                           maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                           onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'valuationAVM', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal($valuationAVMFee) ?></label>
                            <?php } ?>
                        </div>
                    </div>
                <?php } ?>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'valuationCMA', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('valuationCMA', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="valuationCMAFee"
                                       placeholder="0.00"
                                       id="valuationCMAFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($valuationCMAFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'valuationCMA', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($valuationCMAFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'creditCheckFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('creditCheckFee', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="creditCheckFee"
                                       placeholder="0.00"
                                       id="creditCheckFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($creditCheckFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'creditCheckFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($creditCheckFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'backgroundCheck', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('backgroundCheck', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="backgroundCheckFee"
                                       placeholder="0.00"
                                       id="backgroundCheckFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($backgroundCheckFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'backgroundCheck', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($backgroundCheckFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'documentPreparation', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('documentPreparation', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="documentPreparationFee"
                                       placeholder="0.00"
                                       id="documentPreparationFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($documentPreparationFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'documentPreparation', 'sArr' => $secArr, 'opt' => 'L']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($documentPreparationFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <?php if ($isEF != 1) { ?>
                    <div
                            class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'servicingSetUpFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                        <?php echo loanForm::label('servicingSetUpFee', 'col-md-6  '); ?>
                        <div class="col-md-6">
                            <?php if ($allowToEdit && $disabledInputForClient) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input type="text" class="form-control input-sm" name="servicingSetUpFee"
                                           placeholder="0.00"
                                           id="servicingSetUpFee"
                                           value="<?php echo Currency::formatDollarAmountWithDecimal($servicingSetUpFee) ?>"
                                           size="20"
                                           maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                           onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'servicingSetUpFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal($servicingSetUpFee) ?></label>
                            <?php } ?>
                        </div>
                    </div>
                    <div
                            class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'floodService', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                        <?php echo loanForm::label('floodService', 'col-md-6  '); ?>
                        <div class="col-md-6">
                            <?php if ($allowToEdit && $disabledInputForClient) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input type="text" class="form-control input-sm" name="floodServiceFee"
                                           placeholder="0.00"
                                           id="floodServiceFee"
                                           value="<?php echo Currency::formatDollarAmountWithDecimal($floodServiceFee) ?>"
                                           size="20"
                                           maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                           onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'floodService', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal($floodServiceFee) ?></label>
                            <?php } ?>
                        </div>
                    </div>
                <?php } ?>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'projectFeasibility', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('projectFeasibility', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="projectFeasibility"
                                       placeholder="0.00"
                                       id="projectFeasibility"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($projectFeasibility) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'projectFeasibility', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($projectFeasibility) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'uccLienSearch', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('uccLienSearch', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="UccLienSearch" id="UccLienSearch"
                                       placeholder="0.00"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($UccLienSearch) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'uccLienSearch', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($UccLienSearch) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'other', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <label class="col-md-6"
                           for="otherFee"><?php echo BaseHTML::fieldAccess(['fNm' => 'other', 'sArr' => $secArr, 'opt' => 'L']); ?></label>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="otherFee" id="otherFee"
                                       placeholder="0.00"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($otherFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'other', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($otherFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'recordingFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('recordingFee', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="recordingFee" id="recordingFee"
                                       placeholder="0.00"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($recordingFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'recordingFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($recordingFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'propertytax', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('propertytax', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="propertyTax" id="propertyTax"
                                       placeholder="0.00"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($propertyTax) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'propertytax', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($propertyTax) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'realEstateTaxes', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('realEstateTaxes', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="realEstateTaxes"
                                       placeholder="0.00"
                                       id="realEstateTaxes"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($realEstateTaxes) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'realEstateTaxes', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($realEstateTaxes) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'payOffLiensCreditors', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('payOffLiensCreditors', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="payOffLiensCreditors"
                                       placeholder="0.00"
                                       id="payOffLiensCreditors"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($payOffLiensCreditors) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'payOffLiensCreditors', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($payOffLiensCreditors) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'wireTransferFeetoEscrow', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('wireTransferFeetoEscrow', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="wireTransferFeeToEscrow"
                                       placeholder="0.00"
                                       id="wireTransferFeeToEscrow"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($wireTransferFeeToEscrow) ?>"
                                       size="20" maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'wireTransferFeetoEscrow', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($wireTransferFeeToEscrow) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'survey', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('survey', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="survey" id="survey"
                                       placeholder="0.00"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($survey) ?>" size="20"
                                       maxlength="68"
                                       TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'survey', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($survey) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'cityCountyTaxStamps', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('cityCountyTaxStamps', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="cityCountyTaxStamps"
                                       placeholder="0.00"
                                       id="cityCountyTaxStamps"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($cityCountyTaxStamps) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'cityCountyTaxStamps', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($cityCountyTaxStamps) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'constructionHoldbackFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('constructionHoldbackFee', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="constructionHoldbackFee"
                                       placeholder="0.00"
                                       id="constructionHoldbackFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($constructionHoldbackFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'constructionHoldbackFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($constructionHoldbackFee) ?></label>
                        <?php } ?>
                    </div>
                </div>

                <div class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'sellerCreditsFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('sellerCreditsFee', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" name="sellerCreditsFee"
                                       placeholder="0.00"
                                       id="sellerCreditsFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($sellerCreditsFee); ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'sellerCreditsFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($sellerCreditsFee); ?></label>
                        <?php } ?>
                    </div>
                </div>

                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'insimpounds', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php if ($allowToEdit && $disabledInputForClient) { ?>
                        <div class="col-md-6">
                            <div class="row">
                                <label class="col-md-12 font-weight-bold">
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'insimpounds', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                </label>
                                <div class="col-md-12">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">Months</span>
                                        </div>
                                        <input type="number"
                                               class="form-control input-sm "
                                               name="insImpoundsMonth"
                                               id="insImpoundsMonth"
                                               value="<?php echo $insImpoundsMonth; ?>"
                                               TABINDEX="<?php echo $tabIndex++; ?>"
                                               autocomplete="off"
                                               onblur="calculateInsImpoundsFee('loanModForm', 'insImpoundsFee');"/>
                                        <div class="input-group-append">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text"
                                               class="form-control input-sm "
                                               name="insImpoundsMonthAmt"
                                               placeholder="0.00"
                                               id="insImpoundsMonthAmt"
                                               value="<?php echo $insImpoundsMonthAmt; ?>"
                                               onblur="currencyConverter(this, this.value);calculateInsImpoundsFee('loanModForm', 'insImpoundsFee');"
                                               TABINDEX="<?php echo $tabIndex++; ?>"
                                               autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm" TABINDEX="<?php echo $tabIndex++; ?>"
                                       placeholder="0.00"
                                       name="insImpoundsFee"
                                       id="insImpoundsFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($insImpoundsFee) ?>"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();">
                            </div>
                        </div>
                    <?php } else { ?>
                        <span
                                class="H5"><?php echo $insImpoundsMonth; ?></span>months @ $ <?php echo Currency::formatDollarAmountWithDecimal($insImpoundsMonthAmt); ?>
                        <span class="H5"><?php echo Currency::formatDollarAmountWithDecimal($insImpoundsFee) ?></span>
                    <?php } ?>
                </div>
            </div>

            <div
                    class="col-md-12 separator separator-dashed my-2 separator12 interestChargedFrom_disp <?php echo $denimSeperator; ?>"></div>
            <div class="col-md-12">
                <div class="form-group row">
                    <div class="col-4 <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'perDiemAccrualType', 'sArr' => $secArr, 'opt' => 'D'])); ?>">
                        <?php echo loanForm::label('perDiemAccrualType', 'col-md-6  '); ?>
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                        <select name="perDiemAccrualType"
                                class="form-control input-sm accrualTypeClass"
                                id="perDiemAccrualType"
                                onchange="loanCalculation.updateAccrualType(this);updateLoanDetail();"
                                tabindex="<?php echo $tabIndex++; ?>">
                            <option value="">Use Loan Accrual Type</option>
                            <?php foreach (accrualTypes::$accrualTypes as $k => $v) { ?>
                                <option value="<?php echo $k; ?>" <?php if (LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->perDiemAccrualType == $k) echo ' selected '; ?> >
                                    <?php echo $v; ?>
                                </option>
                            <?php } ?>
                        </select>
                        <?php }  else {
                            echo '<h7>' . (accrualTypes::$accrualTypes[LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->perDiemAccrualType] ?? '') . '</h7>';
                        } ?>
                    </div>
                    <div class="col-md-4 <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'interestChargedFrom', 'sArr' => $secArr, 'opt' => 'D'])); ?>">
                        <?php echo loanForm::label('interestChargedFrom', 'col-md-6  '); ?>
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <input type="date"
                                   class="form-control input-sm"
                                   name="interestChargedFromDate"
                                   id="interestChargedFromDate"
                                   onchange="updateLoanDetail();"
                                   value="<?php echo Dates::Datestamp($interestChargedFromDate); ?>"
                                   size="10"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off">
                        <?php } else { ?>
                            <label><?php echo Dates::StandardDate($interestChargedFromDate); ?></label>
                        <?php } ?>
                    </div>
                    <div
                            class="col-md-4 <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'interestChargedEnd', 'sArr' => $secArr, 'opt' => 'D'])); ?>">
                        <?php echo loanForm::label('interestChargedEnd', 'col-md-6  ', '', '(' . (PageVariables::$nonInclusivePerDiem ? 'Non-Inclusive' : 'Inclusive') . ')'); ?>
                        <?php if ($allowToEdit && $disabledInputForClient) { ?>
                            <input type="date"
                                   class="form-control input-sm"
                                   name="interestChargedEndDate"
                                   id="interestChargedEndDate"
                                   onchange="updateLoanDetail();"
                                   value="<?php echo Dates::Datestamp($interestChargedEndDate); ?>"
                                   size="10"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off">
                        <?php } else { ?>
                            <label><?php echo Dates::StandardDate($interestChargedEndDate); ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'estPerDiemInt', 'sArr' => $secArr, 'opt' => 'D'])); ?>">
                    <label
                            class="font-weight-bold col-md-12"><?php echo BaseHTML::fieldAccess(['fNm' => 'estPerDiemInt', 'sArr' => $secArr, 'opt' => 'L']); ?>
                        :
                        <u><span style="margin-left: 15px;"
                                 id="diemDays"><?php echo $diemDays; ?></span></u>
                        &nbsp; &nbsp; x Per Diem Interest:&nbsp;$<u>&nbsp;<span
                                    style="margin-right: 15px;display: none;"
                                    id="totalDailyInterestCharge"><?php echo($totalDailyInterestCharge); ?></span>
                            <span
                                    id="totalDailyInterestChargeDummy"><?php echo Currency::formatDollarAmountWithDecimal($totalDailyInterestCharge); ?></span>
                            <a id="perDiemToolTip" class="fa fa-info-circle  fa-lg tip-bottom"
                               style="text-decoration:none; display: inline;"
                               title="<?php echo $perDiemToolTip; ?>"></a>
                        </u>
                        &nbsp;(@ &nbsp;<u><span id="interestRateDual"><?php echo $lien1Rate; ?></span> %</u>)&nbsp;
                        =
                        Accrued Per Diem Interest: $&nbsp; <u><span
                                    id="totalEstPerDiem"><?php echo Currency::formatDollarAmountWithDecimal($totalEstPerDiem); ?></span></u>
                    </label>
                </div>
            </div>
            <div
                    class="col-md-12 separator separator-dashed my-2 separator12 estPerDiemInt_disp <?php echo $denimSeperator ?>"></div>


            <div class=" col-md-12">
                <div class="row form-group ">
                    <div
                            class="col-md-6 <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'totalFeesCosts', 'sArr' => $secArr, 'opt' => 'D'])); ?>">
                        <div
                                class="row ">
                            <?php echo loanForm::label('totalFeesCosts', 'col-md-6  '); ?>
                            <div class="col-md-6 font-weight-bold">
                                $ <span id="totalFeesAndCost">
                                <?php echo Currency::formatDollarAmountWithDecimal($totalFeesAndCost) ?>
                            </span>
                            </div>
                        </div>
                    </div>
                    <div
                            class="col-md-6 <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'percentageClosingCostFinanced', 'sArr' => $secArr, 'opt' => 'D'])); ?>">
                        <div
                                class="row ">
                            <?php echo loanForm::label('percentageClosingCostFinanced', 'col-md-6 '); ?>
                            <div class="col-md-6 font-weight-bold">
                            <span id="perClosingCostFinanced">
                                    <?php echo Currency::formatDollarAmountWithDecimal($perClosingCostFinanced) ?></span>
                                %
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class=" col-md-12">
                <div class="row form-group">
                    <div
                            class="col-md-6 <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'netLenderFundsToTitle', 'sArr' => $secArr, 'opt' => 'D'])); ?>">
                        <div class="row">
                            <div class="col-md-6 ">
                                <?php echo loanForm::label('netLenderFundsToTitle', '', HMLOLoanTermsCalculation::$netLenderFundsToBorrowerToolTip); ?>
                            </div>
                            <div class="col-md-6">
                                    <span id="netLenderFundsToBorrower"
                                          class="netLenderFundsToBorrower font-weight-bold ">
                                      <?php echo proposalFormula::convertToAbsoluteValueForDollar($netLenderFundsToBorrower) ?>
                                    </span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div
                                class="row  <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'feeCostTotalLoanAmount', 'sArr' => $secArr, 'opt' => 'D'])); ?>">
                            <div class="col-md-6 feeSectionTotalLoanAmt "
                                 style="<?php echo $feeSectionTotalLoanAmtOpt ?>">
                                <?php echo loanForm::label('feeCostTotalLoanAmount', ' '); ?>
                            </div>
                            <div class="col-md-6 feeSectionTotalLoanAmt"
                                 style="<?php echo $feeSectionTotalLoanAmtOpt ?>">
                                $ <span id="totalLoanAmount" class="totalLoanAmount font-weight-bold">
                                <?php echo Currency::formatDollarAmountWithDecimal($totalLoanAmount) ?>
                            </span>
                            </div>
                        </div>
                    </div>
                    <?php
                    if (glCustomJobForProcessingCompany::isCustomLenderRevenue($PCID)) {
                        $inAddArray = [
                            'originationPointsValue' => $originationPointsValue, // Origination Fee
                            'applicationFee' => $applicationFee, // Application Fee
                            'processingFee' => $processingFee, // Processing Fee
                            'underwritingFees' => $underwritingFees, // Underwriting Fee
                            'bufferAndMessengerFee' => $bufferAndMessengerFee, // Discount Fee
                            'drawsSetUpFee' => $drawsSetUpFee, // Draw Set Up Fee
                            'drawsFee' => $drawsFee, // Draw Fee
                            'valuationAVMFee' => $valuationAVMFee, //Valuation – AVM
                            'wireFee' => $wireFee, //Wire Fee
                            'servicingSetUpFee' => $servicingSetUpFee, //Servicing Set Up Fee
                            'floodCertificateFee' => $floodCertificateFee, //Flood Certificate
                            'backgroundCheckFee' => $backgroundCheckFee, //Background Check
                            'creditReportFee' => $creditReportFee, //Credit Report
                            'projectFeasibility' => $projectFeasibility, //Project Feasibility
                            'appraisalFee' => $appraisalFee, //Appraisal Fee
                        ];
                        $inSubArray = [
                            'thirdPartyFees' => $thirdPartyFees, //Lender Credit to Offset 3rd Party Fees
                        ];
                        $customLenderRevenue = proposalFormula::getLenderRevenue($inAddArray, $inSubArray);
                        ?>
                        <div class="col-md-6">
                            <div class="row form-group">
                                <label class="col-md-6 font-weight-bold">
                                    <i class="fa fa-info-circle tooltipClass text-primary" data-html="true"
                                       id="LenderRevenueToolTip"
                                       title="<?php echo "Lender Revenue = Origination Value + Application Fee + Processing Fees + Underwriting Fees + Discount Fee + Draw Set Up Fee + Draws Fee+ Valuation - AVM + Wire  + Servicing Setup + Flood Certificate + Background Check + Credit Report+ Project Feasibility + Appraisal Fee - Lender Credit to Offset 3 rd Party Fees"; ?>">
                                    </i>
                                    Lender Revenue:</label>
                                <div class="col-md-6 font-weight-bold "
                                     id="customLenderRevenue"> <?php echo proposalFormula::convertToAbsoluteValueForDollar($customLenderRevenue); ?></div>
                            </div>
                        </div>
                    <?php } ?>
                </div>
            </div>
            <div class=" col-md-12">
                <div
                        class="row form-group <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'lenderNotes', 'sArr' => $secArr, 'opt' => 'D'])); ?>">
                    <div class="col-md-6">
                        <div class="row">
                            <div class="col-md-6 ">
                                <?php echo loanForm::label('lenderNotes', ''); ?>
                            </div>
                            <div class="col-md-6">
                                <?php if ($allowToEdit && $disabledInputForClient) { ?>
                                    <input type="hidden" name="checkDisplayTermSheet" value="No">
                                    <div class="checkbox-inline">
                                        <label class="checkbox">
                                            <input type="checkbox" name="checkDisplayTermSheet"
                                                   id="checkDisplayTermSheet"
                                                   value="Yes"
                                                   tabindex="<?php echo $tabIndex++; ?>" <?php if ($checkDisplayTermSheet == 'Yes') echo 'Checked'; ?>><span></span>
                                            Include notes on term sheet?
                                        </label>
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo $checkDisplayTermSheet ?></h5>
                                <?php } ?>
                            </div>

                            <?php if ($publicUser != 1 && $userRole != 'Client') { ?>
                                <div class="col-md-12 ">
                                    <?php if ($allowToEdit && $disabledInputForClient) { ?>
                                        <textarea name="lenderNotes" class="form-control validateMaxLength" id="lenderNotes"
                                                  maxlength="<?php echo loanForm::getFieldLength('lenderNotes','tblFileHMLOPropInfo'); ?>"
                                                  tabindex="<?php echo $tabIndex++; ?>"><?php echo $lenderNotes; ?></textarea>
                                    <?php } else { ?>
                                        <h5><?php echo $lenderNotes ?></h5>
                                    <?php } ?>
                                </div>

                            <?php } ?>
                        </div>
                    </div>
                </div>
            </div>
            <label class="col-md-12 bg-secondary mb-2 p-2 font-weight-bolder" id="loanExitExtensionFeesFields">
                <?php echo BaseHTML::getSubSectionHeading('FC', 'loanExitExtensionFeesSubSection'); ?>
            </label>
            <div class="col-md-12 loanExitExtensionFeesFields">
                <div class="row form-group">
                    <div class="col-md-6 form-group isTherePrePaymentPenalty_disp <?php echo loanForm::showField('isTherePrePaymentPenalty'); ?>">
                        <div class="row">
                            <?php echo loanForm::label('isTherePrePaymentPenalty', 'col-md-8  '); ?>
                            <div class="col-md-4">
                                <?php if ($allowToEdit) { ?>
                                    <div class="radio-inline">
                                        <label class="radio radio-solid font-weight-bold"
                                               for="isTherePrePaymentPenaltyYes">
                                            <input type="radio" name="isTherePrePaymentPenalty"
                                                   id="isTherePrePaymentPenaltyYes"
                                                   onclick="showAndHidePrePaymentPenalty(this.value, 'prePaymentPenaltyDisOpt');"
                                                   value="Yes"
                                                   class="<?php echo BaseHTML::fieldAccess(['fNm' => 'isTherePrePaymentPenalty', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'isTherePrePaymentPenalty', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                                   tabindex="<?php echo $tabIndex++; ?>" <?php if ($isTherePrePaymentPenalty == 'Yes') {
                                                echo 'checked';
                                            } ?>><span></span>Yes
                                        </label>
                                        <label class="radio radio-solid font-weight-bold"
                                               for="isTherePrePaymentPenaltyNo">
                                            <input type="radio" name="isTherePrePaymentPenalty"
                                                   id="isTherePrePaymentPenaltyNo"
                                                   onclick="showAndHidePrePaymentPenalty(this.value, 'prePaymentPenaltyDisOpt');"
                                                   value="No"
                                                   class="<?php echo BaseHTML::fieldAccess(['fNm' => 'isTherePrePaymentPenalty', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'isTherePrePaymentPenalty', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                                   tabindex="<?php echo $tabIndex++; ?>" <?php if ($isTherePrePaymentPenalty == 'No') {
                                                echo 'checked';
                                            } ?>><span></span>No
                                        </label>
                                    </div>
                                <?php } else { ?>
                                    <label><?php echo $isTherePrePaymentPenalty; ?></label>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                    <div class="prePaymentPenaltyDisOpt col-md-6 form-group prePaymentPenaltyPercentage_child
                    <?php echo BaseHTML::parentFieldAccess(['fNm' => 'isTherePrePaymentPenalty', 'sArr' => $secArr,
                        'pv' => $isTherePrePaymentPenalty, 'av' => 'Yes']); ?>"
                         style="<?php echo $prepayentSectionDisplay; ?>">
                        <div class="row">
                            <?php echo loanForm::label('prePaymentPenaltyPercentage', 'col-md-6  '); ?>
                            <div class="col-md-6">
                                <?php if ($allowToEdit && $disabledInputForClient) { ?>
                                    <div class="row">
                                        <input type="text" name="prePaymentPenaltyPercentage"
                                               placeholder="0.0"
                                               class="form-control input-sm col-2 <?php echo BaseHTML::fieldAccess(['fNm' => 'prePaymentPenaltyPercentage', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               id="prePaymentPenaltyPercentage" <?php echo BaseHTML::fieldAccess(['fNm' => 'prePaymentPenaltyPercentage', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                               value="<?php echo Currency::formatRateAmountWithDecimal($prePaymentPenaltyPercentage); ?>"
                                               tabindex="<?php echo $tabIndex++; ?>"><span
                                                class="mt-2">&nbsp;%&nbsp;for&nbsp;</span>
                                        <select name="prePaymentPenalty"
                                                id="prePaymentPenalty" <?php echo BaseHTML::fieldAccess(['fNm' => 'prePaymentPenaltyPercentage',
                                            'sArr' => $secArr, 'opt' => 'I']); ?>
                                                class="form-control input-sm col-7 <?php echo BaseHTML::fieldAccess(['fNm' => 'prePaymentPenaltyPercentage',
                                                    'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                TABINDEX="<?php echo $tabIndex++; ?>">
                                            <option value=""> - Select -</option>
                                            <?php
                                            for ($i = 0; $i < count($glHMLOPrePaymentPenalty); $i++) {
                                                $sOpt = '';
                                                $prePayment = '';
                                                $prePayment = trim($glHMLOPrePaymentPenalty[$i]);
                                                $sOpt = Arrays::isSelected($prePayment, $prePaymentPenalty);
                                                echo "<option value=\"" . $prePayment . "\" " . $sOpt . '>' . $prePayment . '</option>';
                                            }
                                            ?>
                                        </select>
                                    </div>
                                <?php } else { ?>
                                    <span
                                            class="H5"><?php echo Currency::formatRateAmountWithDecimal($prePaymentPenaltyPercentage); ?></span> % for
                                    <span class="H5"><?php echo $prePaymentPenalty ?></span>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                    <div class="prePaymentPenaltyDisOpt col-md-6 form-group prePaymentSelectVal_child
                    <?php echo BaseHTML::parentFieldAccess(['fNm' => 'isTherePrePaymentPenalty', 'sArr' => $secArr,
                        'pv' => $isTherePrePaymentPenalty, 'av' => 'Yes']); ?> "
                         style="<?php echo $prepayentSectionDisplay; ?>">
                        <div class="row">
                            <?php echo loanForm::label('prePaymentSelectVal', 'col-md-4  '); ?>
                            <div class="col-md-8">
                                <div class="row">
                                    <?php
                                    echo loanForm::simpleHidden(
                                        'prePaymentSelectValHidden',
                                        implode(',', $prePaymentSelectValArr)
                                    ); ?>
                                    <?php if ($allowToEdit) { ?>
                                    <select
                                            class="chzn-select form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'prePaymentSelectVal',
                                                'sArr' => $secArr, 'opt' => 'M']); ?>"
                                            name="prePaymentSelectVal[]" id="prePaymentSelectVal"
                                            data-placeholder="Please Select "
                                            multiple="" <?php echo BaseHTML::fieldAccess(['fNm' => 'prePaymentSelectVal',
                                        'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        <option></option>
                                        <?php
                                        for ($k = 0; $k < count($prePaymentPenaltyResArr); $k++) {
                                            $sel = '';
                                            if (in_array($prePaymentPenaltyResArr[$k], $prePaymentSelectValArr ?? [])) $sel = ' selected ';
                                            ?>
                                            <option
                                                    value="<?php echo trim($prePaymentPenaltyResArr[$k]); ?>" <?php echo $sel; ?>><?php echo trim($prePaymentPenaltyResArr[$k]) ?></option>
                                            <?php
                                        }
                                        ?>
                                    </select>
                                    <?php } else { ?>
                                        <label><?php echo implode(',',$prePaymentSelectValArr); ?></label>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div
                        class="col-md-12 separator separator-dashed my-2 separator12 isTherePrePaymentPenalty_disp <?php echo loanForm::showField('isTherePrePaymentPenalty'); ?>"></div>
            </div>
            <div class="col-md-12 loanExitExtensionFeesFields">
                <div class="row form-group">
                    <div class="col-md-6 form-group">
                        <div class="form-group row exitFeePoints_disp <?php echo loanForm::showField('exitFeePoints'); ?>">
                            <?php echo loanForm::label('exitFeePoints', 'col-md-4  '); ?>
                            <div class="col-md-8">
                                <?php if ($allowToEdit) { ?>
                                    <div class="input-group">
                                        <input type="text" name="exitFeePoints" id="exitFeePoints"
                                               placeholder="0.00"
                                               class="form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'exitFeePoints', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal($exitFeePoints); ?>"
                                               onblur="currencyConverter(this, this.value);calculateExitAmount('points');"
                                               onkeyup="return restrictAlphabetsLoanTerms(this)"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'exitFeePoints', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                               tabindex="<?php echo $tabIndex++; ?>">
                                        <div class="input-group-append">
                                            <span class="input-group-text">
                                                %
                                            </span>
                                        </div>
                                    </div>
                                <?php } else { ?>
                                    <label><?php echo Strings::showField('exitFeePoints', 'fileHMLONewLoanInfo'); ?></label>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 form-group">
                        <div class="form-group row exitFeeAmount_disp <?php echo loanForm::showField('exitFeeAmount'); ?>">
                            <?php echo loanForm::label('exitFeeAmount', 'col-md-4  '); ?>
                            <div class="col-md-8">
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="exitFeeAmount" id="exitFeeAmount"
                                           placeholder="0.00"
                                           class="form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'exitFeeAmount', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           value="<?php echo Currency::formatDollarAmountWithDecimal($exitFeeAmount); ?>"
                                           onblur="currencyConverter(this, this.value);calculateExitAmount('amount')"
                                           onkeyup="return restrictAlphabetsLoanTerms(this)"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'exitFeeAmount', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                           tabindex="<?php echo $tabIndex++; ?>">
                                <?php } else { ?>
                                    <label><?php echo Strings::showField('exitFeeAmount', 'fileHMLOPropertyInfo'); ?></label>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                    <?php if (glCustomJobForProcessingCompany::accessToExtensionOptions($PCID, PageVariables::$userNumber)) { ?>
                        <?php
                        $extFieldNo = 1;
                        foreach ($extensionFields as $extField) {
                            $class = $extFieldNo == 1 ? 'hide' : 'show';
                            $calculatePercentageExtensionOption = HMLOLoanTermsCalculation::calculatePercentageExtensionOption($totalLoanAmount, $extField->extensionOptionPercentage);
                            ?>
                            <div class="col-md-10 form-group extensionOptionPercentage_disp loanExitExtensionFeesFieldsCloneDiv <?php echo loanForm::showField('extensionOptionPercentage'); ?>"
                                 id="loanExitExtensionFeesFieldsCloneDiv_<?php echo $extFieldNo; ?>">
                                <input type="hidden"
                                       name="extensionOptionFields[<?php echo $extFieldNo; ?>][extensionOptionId]"
                                       id="extensionOptionId_<?php echo $extFieldNo; ?>"
                                       value="<?php echo $extField->id; ?>">
                                <div class="row">
                                    <?php echo loanForm::label(
                                        'extensionOptionPercentage',
                                        'col-md-2  ',
                                        '',
                                        $extFieldNo,
                                        $extFieldNo
                                    ); ?>
                                    <div class="col-md-9 ml-12">
                                        <div class="row">
                                            <?php if ($allowToEdit && $disabledInputForClient) { ?>
                                                <input type="text"
                                                       placeholder="0.0"
                                                       name="extensionOptionFields[<?php echo $extFieldNo; ?>][extensionOptionPercentage]"
                                                       class="form-control col-2 <?php echo BaseHTML::fieldAccess(['fNm' => 'extensionOptionPercentage', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       id="extensionOptionPercentage_<?php echo $extFieldNo; ?>"
                                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'extensionOptionPercentage', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                                       value="<?php echo number_format($extField->extensionOptionPercentage, 3); ?>"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       onchange="calculatePercentageExtensionOption('loanModForm', this);">
                                                <span class="mt-2">&nbsp;%&nbsp;Fee for&nbsp;</span>
                                                <select name="extensionOptionFields[<?php echo $extFieldNo; ?>][extensionOption]"
                                                        id="extensionOption_<?php echo $extFieldNo; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'extensionOptionPercentage', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                                        class="form-control col-4 <?php echo BaseHTML::fieldAccess(['fNm' => 'extensionOptionPercentage', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                        tabindex="<?php echo $tabIndex++; ?>">
                                                    <option value=""> - Select -</option>
                                                    <?php
                                                    foreach ($glHMLOExtensionOption as $extKey => $extValue) {
                                                        $sOpt = '';
                                                        $sOpt = Arrays::isSelected($extKey, $extField->extensionOption);
                                                        echo "<option value=\"" . $extKey . "\" " . $sOpt . '>' . trim($extValue) . '</option>';
                                                    }
                                                    ?>
                                                </select>
                                                <?php if (!in_array($PCID, [glPCID::PCID_CRB, glPCID::PCID_DEV_DAVE])) { ?>
                                                    <span class="mt-2">&nbsp;@rate&nbsp;</span>
                                                    <input type="text"
                                                           name="extensionOptionFields[<?php echo $extFieldNo; ?>][extensionRatePercentage]"
                                                           id="extensionRatePercentage_<?php echo $extFieldNo; ?>"
                                                           placeholder="0.0"
                                                           class="form-control col-2 <?php echo BaseHTML::fieldAccess(['fNm' => 'extensionOptionPercentage', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                           value="<?php echo Currency::formatRateAmountWithDecimal($extField->extensionRatePercentage); ?>"
                                                           tabindex="<?php echo $tabIndex++; ?>">
                                                    <span class="mt-2">&nbsp;%&nbsp;&nbsp;&nbsp;</span>
                                                <?php } ?>
                                            <?php } else { ?>
                                                <label><?php echo $glHMLOExtensionOption[$extField->extensionOption]; ?></label>
                                            <?php } ?>
                                            <?php if (!in_array($PCID, [glPCID::PCID_CRB, glPCID::PCID_DEV_DAVE])) { ?>
                                                <span class="mt-2">=&nbsp;</span>
                                                <span class="mt-2">
                                                $<span id="extensionOptionsAmt_<?php echo $extFieldNo; ?>" class="H5">
                                                    <?php echo Currency::formatDollarAmountWithDecimal($calculatePercentageExtensionOption); ?>
                                                </span>
                                            </span>
                                            <?php } ?>
                                            <div>
                                            <span class="btn btn-xs btn-success btn-icon ml-2 mt-2 cursor-pointer"
                                                  data-toggle="popover"
                                                  data-content="Click to add Extension Option (Max 5)"
                                                  onclick="feesAndCost.addExtension('loanExitExtensionFeesFieldsCloneDiv');"
                                                  data-original-title="" title="">
                                                <i class="icon-s fas fa-plus"></i>
                                            </span>
                                                <span class="loanExitExtensionFeesFieldsCloneDivRemove <?php echo $class; ?> ">
                                                <span class="btn btn-xs btn-danger btn-icon ml-2 mt-2 cursor-pointer"
                                                      data-toggle="popover"
                                                      data-content="Click to remove" data-original-title="" title=""
                                                      onclick="feesAndCost.removeExtension(this);"
                                                      data-clone-section="loanExitExtensionFeesFieldsCloneDiv"
                                                      data-inc-id="<?php echo $extFieldNo; ?>"
                                                      data-row-id="<?php echo $extField->id; ?>"
                                                      data-LMRId="<?php echo $extField->LMRId; ?>">
                                                    <i class="icon-s fas fa-minus"></i>
                                                </span>
                                            </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php $extFieldNo++;
                        } ?>
                    <?php } ?>
                </div>
            </div>
        </div>

        <?php echo CustomField::RenderForTabSection(
            PageVariables::$PCID,
            tblFile::class,
            LMRequest::$LMRId,
            'FC',
            $fileTab,
            $activeTab,
            LMRequest::myFileInfo()->getFileTypes(),
            LMRequest::myFileInfo()->getLoanPrograms()
        ); ?>
    </div>
</div>
<?php

if (in_array('Fees & Costs', $lockedSections)) {
    //$BackupAllowToEdit = $allowToEdit;
    $allowToEdit = $BackupAllowToEdit;
}
?>
<style>
    .secHideVis {
        visibility: hidden;
    }
</style>
<script>
    $(function () {
        if ($('#originationPointsRate').val() != '' && ($('#originationPointsValue').val() == '' || $('#originationPointsValue').val() == null)) {
            calculateOriginationPointsValue();
        }
        if ($('#brokerPointsRate').val() != '' && ($('#brokerPointsValue').val() == '' || $('#brokerPointsValue').val() == null)) {
            calculateBrokerPointsValue();
        }

        loanCalculation.nonInclusivePerDiem = <?php echo PageVariables::$nonInclusivePerDiem ? 'true' : 'false'; ?>
    });

    if (!$('.loanExitExtensionFeesFields').find('input:visible').length) {
        $('#loanExitExtensionFeesFields').hide();
    }
    $(document).ready(function () {
        let prePaymentOptionCheck = false;
        prePaymentOptionCheck = "<?php echo glCustomJobForProcessingCompany::removeAddOptionForPrePayment($PCID); ?>";
        if (prePaymentOptionCheck) {
            $('#prePaymentSelectVal').chosen({
                'persistent_create_option': true,
                'skip_no_results': true
            });
        } else {
            $('#prePaymentSelectVal').chosen({
                'create_option': true,
                'persistent_create_option': true,
                'skip_no_results': true
            });

        }
    });
</script>
<?php
Strings::includeMyScript(['/backoffice/LMRequest/js/feesAndCost.js']);
?>
<!-- HMLOFeesAndCostsSection.php -->
