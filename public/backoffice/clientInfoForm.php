<?php
global $isHMLO;

use models\composite\oBranch\getBranchHearAbout;
use models\composite\oClient\getPCClientEntityInfo;
use models\composite\oUserAccess\getAutomationControlAccess;
use models\composite\proposalFormula;
use models\constants\gl\glAmountDesired;
use models\constants\gl\glBorrowerType;
use models\constants\gl\glCreditIssues;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glFirstRehabLending;
use models\constants\gl\glFundsNeeded;
use models\constants\gl\glLien1LoanTypeArray;
use models\constants\gl\glLien2LoanTypeArray;
use models\constants\gl\glMortgageInvestorOwnerArray;
use models\constants\gl\glMortgageOwnerArray;
use models\constants\lien1TermsArray;
use models\constants\lien2TermsArray;
use models\constants\monthsBehindArray;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\Property;
use models\Controllers\loanForm;
use models\cypher;
use models\lendingwise\tblFile;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Integers;
use models\standard\Strings;

$monthsBehindArray = monthsBehindArray::$monthsBehindArray;
$lien1TermsArray = lien1TermsArray::$lien1TermsArray;
$lien2TermsArray = lien2TermsArray::$lien2TermsArray;
$glLien1LoanTypeArray = glLien1LoanTypeArray::$glLien1LoanTypeArray;
$glLien2LoanTypeArray = glLien2LoanTypeArray::$glLien2LoanTypeArray;
$glMortgageOwnerArray = glMortgageOwnerArray::$glMortgageOwnerArray;
$glMortgageInvestorOwnerArray = glMortgageInvestorOwnerArray::$glMortgageInvestorOwnerArray;
$glCreditIssues = glCreditIssues::$glCreditIssues;
$glFundsNeeded = glFundsNeeded::$glFundsNeeded;
$glAmountDesired = glAmountDesired::$glAmountDesired;
$glFirstRehabLending = glFirstRehabLending::$glFirstRehabLending;

$ssn1 = '';
$ssn2 = '';
$ssn3 = '';
$borrowerFName = '';
$borrowerMName = '';
$borrowerLName = '';
$maritalStatus = '';
$maidenName = '';
$spouseName = '';
$phoneNumber = '';
$phNo1 = '';
$phNo2 = '';
$phNo3 = '';
$ext = '';
$altPhoneNumber = '';
$altPhNo1 = '';
$altPhNo2 = '';
$altPhNo3 = '';
$altExt = '';
$cellNumber = '';
$cellNo1 = '';
$cellNo2 = '';
$cellNo3 = '';
$fax = '';
$fax1 = '';
$fax2 = '';
$fax3 = '';
$workNumber = '';
$workNo1 = '';
$workNo2 = '';
$workNo3 = '';
$marriageDate = '';
$divorceDate = '';
$coBSsnNumber = '';
$coBSsn1 = '';
$coBSsn2 = '';
$coBSsn3 = '';
$coBorrowerDOB = '';
$coBPhoneNumber = '';
$coBPhNo1 = '';
$coBPhNo2 = '';
$coBPhNo3 = '';
$coBExt = '';
$coBAltPhoneNumber = '';
$coBAltPhNo1 = '';
$coBAltPhNo2 = '';
$coBAltPhNo3 = '';
$coBAltExt = '';
$workNoExt = '';
$REBroker = 'No';
$addBranchHearAbout = 0;
$coBCellNumber = '';
$coBCellNo1 = '';
$coBCellNo2 = '';
$coBCellNo3 = '';
$coBFax = '';
$coBFax1 = '';
$coBFax2 = '';
$coBFax3 = '';
$coBorrowerWorkNumber = '';
$coBWorkNo1 = '';
$coBWorkNo2 = '';
$coBWorkNo3 = '';
$coBWorkNoExt = '';
$servicer1 = '';
$servicer1Text = $originalLender1Text = $servicer2Text = $originalLender2Text = '- Type Name Here -';
$noOfMonthsBehind1 = '';
$noOfDaysBehind1 = '';
$mailingState = '';
$previousState = '';
$coBorrowerMailingState = '';
$coBorPreviousState = '';
$mortgageOwner1 = '';
$originalLender1 = '';
$lien1Payment = '';
$taxes1 = '';
$insurance1 = '';
$floodInsurance1 = '';
$mortgageInsurance1 = '';
$HOAFees1 = '';
$totalPayment = '';
$servicer2 = '';
$noOfMonthsBehind2 = '';
$noOfDaysBehind2 = '';
$originalLender2 = '';
$mortgageOwner2 = '';
$propertyCounty = '';
$coBorrowerCounty = '';
$propertyType = '';
$serviceProvider = '';
$phoneNumberArray = [];
$altPhoneNumberArray = [];
$cellNumberArray = [];
$faxArray = [];
$workNumberArray = [];
$coBSsnNumberArray = [];
$coBPhoneNumberArray = [];
$coBAltPhoneNumberArray = [];
$coBCellNumberArray = [];
$coBFaxArray = [];
$coBWorkNumberArray = [];
$propertyCountyInfo = [];
$coBorrowerCountyInfo = [];
$branchClientTypeInfo = [];
$brokerContactInfo = '';
$BrokerInfo = [];
$lien1LPMade = '';
$lien2LPMade = '';
$brokerName = '';
$brokerCompany = '';
$brokerEmail = '';
$brokerAddr = '';
$brokerAddress = '';
$brokerCity = '';
$brokerState = '';
$brokerZip = '';
$appStr = '';
$isCoBorrower = 0;
$leadSource = '';
$lien2Terms = '';
$lien1Terms = '';
$noteDate = '';
$SID = 0;
$mortgageInvestor1 = '';
$mortgageInvestor2 = '';
$borrowerTimeZone = '';
$coBorrowerTimeZone = '';
$coBServiceProvider = '';
$remainingMonths = '';
$noticeAccelerationDate = '';
$condominiumOrHOAFee = '0';
$bestTime = '';
$methodOfContact = '';
$borBackgroundExplanation = '';
$escrowAdvances = '';
$coBorBackgroundExplanation = '';
$projectedEscrowAdvances = '';
$proposalLateFees = '';
$mailingAddrAsPresent = 1;
$pastDueMtg = '';
$coBorMailingAddrAsPresent = 0;
$pastDueHOA = '';
$ssnNumber = '';
$HOA1ContactID = '';
$propertyManagementInfo = [];
$fileContacts = $QAInfo = [];
$HOPhone = '';
$HOPhNo1 = '';
$HOPhNo2 = '';
$HOPhExt = '';
$HOFax = '';
$HOPhNoArray = [];
$HOFaxArray = [];
$coMethodOfContact = '';
$nonBorrowerDOB = '';
$nonBorrowerSSNNumber = '';
$nonBorrowerSSNArray = [];
$coBestTime = '';
$tempNonborrowerSSN = '';
$isNonBorrower = 0;
$nonBorrrowerSSN1 = '';
$nonBorrrowerSSN2 = '';
$nonBorrrowerSSN3 = '';
/* HMDA (Borrower) */
$BGender = '';
$BRace = '';
$BEthnicity = '';
$bFiEthnicity = '';
$bFiEthnicitySub = '';
$bFiEthnicitySubOther = '';
$bFiSex = '';
$bFiRace = '';
$bFiRaceSub = '';
$bFiRaceAsianOther = '';
$bFiRacePacificOther = '';
$bDemoInfo = '';
/* HMDA (Borrower) */
/* HMDA (Co-Borrower) */
$PublishBInfo = '3';
$CBGender = '';
$noOfPeopleDependent = 0;
$CBRace = '';
$CBEthnicity = '';
$CBVeteran = '';
$CBFiEthnicity = '';
$CBFiGender = '';
$CBFiRace = '';
$CBDDemoInfo = '';
$PublishCBInfo = '3';
/* HMDA (Co-Borrower) */
$invoices = '';
$monthlyRevenue = '';
$majorityBusiness = '';
$ownersName = '';
$Owned = '';
$businessName = '';
$businessNameDBA = '';
$businessLegalName = '';
$businessTax = '';
$businessType = '';
$Website = '';
$Industry = '';
$businessConsumers = '';
$monthsBusiness = '';
$creditScore = '';
$grossRevenue = '';
$monthlyDepositVolume = '';
$bankBalance = '';
$invoiceAmount = '';
$requestedAmount = '';
$requestedTermLength = '';
$funding = '';
$businessCreditCards = '';
$businessFinancing = '';
$netProfit = '';
$amount = '';
$borResidedPresentAddr = 'NA';
$coBResidedPresentAddr = 'NA';
$ownCollateral = '';
$monthlyDebtPayments = '';
$fileLoanOriginationInfo = [];
$presentAddress = '';
$presentCity = '';
$presentState = '';
$presentZip = '';
$presentUnit = '';
$presentCountry = '';
$presentPropLengthMonths = '';
$currentRPM = '';
$previousUnit = '';
$previousCountry = '';
$previousPropLengthMonths = '';
$previousRPM = '';
$coBPresentAddress = '';
$coBPresentCity = $coBPresentState = $coBPresentZip = $coBNoOfDependent = '';
$borPresentPropType = $borMailingPropType = $borFormerPropType = '';
$coBPresentPropType = '';
$coBMailingPropType = '';
$coBFormerPropType = '';
$creditIssuesArray = [];
$fundsNeededReasonArray = [];
$haveEmployed = '';
$isFMInUSMilitary = '';
$ownBusiness = '';
$amtDesired = '';
$havePaystubW2ITReturn = '';
$FUInfo = [];
$creditIssues = '';
$fundsNeededReason = '';
$FUPropType = '';
$presentPropLengthTimeCoBor = '';
$borCompanyName = '';
$borCreditScoreRange = '';
$borCreditScore = '';
$fileHMLOInfo = [];
$coBorCompanyName = '';
$coBorCreditScoreRange = '';
$coBorCreditScore = '';
$REBrokerFName = '';
$creditScoreRange = '';
$fileHMLOEntityInfo = [];
$midFicoScore = '';

$entityName = '';
$entityType = '';
$borrowerType = '';
$ENINo = '';
$naicsCode = '';
$entityAddress = '';
$entityCity = '';
$entityState = '';
$entityZip = '';
$entityStateOfFormation = '';
$statesRegisterdIn = '';
$entityNotes = '';
$corporateSecretaryName = '';
$member1Name = '';
$member1Title = '';
$entityWebsite = '';
$member1Ownership = '';
$member2Name = '';
$member2Title = '';
$member2Ownership = '';
$member3Name = '';
$member3Title = '';
$member3Ownership = '';
$fileHMLOBackGroundInfo = [];
$fileHMLOExperienceInfo = [];
$isBorUSCitizen = '';
$isBorDecalredBankruptPastYears = '';
$isAnyCoBorOutstandingJudgements = '';
$hasCoBorAnyActiveLawsuits = '';
$hasCoBorPropertyTaxLiens = '';
$hasCoBorObligatedInForeclosure = '';
$isCoBorPresenltyDelinquent = '';
$isCoBorBorrowedDownPayment = '';
$isCoBorIntendToOccupyPropAsPRI = '';
$haveCoBorOtherFraudRelatedCrimes = '';
$isCoBorPersonallyGuaranteeLoan = '';
$borExperianScore = '';
$borEquifaxScore = '';
$borTransunionScore = '';
$coBorExperianScore = '';
$coBorEquifaxScore = '';
$coBorTransunionScore = $borrowerCitizenship = $isServicingMember = $servicingMemberInfo = $agesOfDependent = $numberOfDependents = '';
$isCoBorUSCitizen = '';
$isCoBorDecalredBankruptPastYears = '';
$isAnyBorOutstandingJudgements = '';
$hasBorAnyActiveLawsuits = '';
$hasBorPropertyTaxLiens = '';
$hasBorObligatedInForeclosure = '';
$isBorPresenltyDelinquent = '';
$isBorBorrowedDownPayment = '';
$isBorIntendToOccupyPropAsPRI = '';
$haveBorOtherFraudRelatedCrimes = '';
$isBorPersonallyGuaranteeLoan = '';
$haveBorREInvestmentExperience = '';
$borNoOfREPropertiesCompleted = '';
$borNoOfFlippingExperience = '';
$haveBorRehabConstructionExperience = '';
$borNoOfYearRehabExperience = '';
$borRehabPropCompleted = '';
$haveBorProjectCurrentlyInProgress = '';
$borNoOfProjectCurrently = '';
$haveBorOwnInvestmentProperties = '';
$borNoOfOwnProp = '';
$areBorMemberOfInvestmentClub = '';
$borClubName = '';
$haveCoBorREInvestmentExperience = '';
$coBorNoOfREPropertiesCompleted = '';
$haveCoBorRehabConstructionExperience = '';
$coBorNoOfYearRehabExperience = '';
$coBorRehabPropCompleted = '';
$haveCoBorProjectCurrentlyInProgress = '';
$coBorNoOfProjectCurrently = '';
$haveCoBorOwnInvestmentProperties = '';
$coBorNoOfOwnProp = '';
$areCoBorMemberOfInvestmentClub = '';
$coBorClubName = '';
$borREAddress1 = '';
$borOutcomeRE1 = '';
$borREAddress2 = '';
$borOutcomeRE2 = '';
$borREAddress3 = '';
$borOutcomeRE3 = '';
$coBorOutcomeRE3 = '';
$coBorREAddress3 = '';
$coBorOutcomeRE2 = '';
$coBorREAddress2 = '';
$coBorOutcomeRE1 = '';
$coBorREAddress1 = '';
$borRCOutcome1 = '';
$borRCAddress1 = '';
$borRCOutcome2 = '';
$borRCAddress2 = '';
$borRCOutcome3 = '';
$borRCAddress3 = '';
$coBorRCAddress1 = '';
$coBorOutcomeRC1 = '';
$coBorRCAddress2 = '';
$coBorRCOutcome2 = '';
$coBorRCAddress3 = '';
$coBorRCOutcome3 = '';
$showREInvestmentDispOpt = $showRCDispOpt = $showProjectInProgressDispOpt = 'display:none;';
$showOwnInvestmentDispOpt = $showMemberDispOpt = 'display:none;';
$showCoBorREInvestmentDispOpt = $showcoBorRCDispOpt = $showcoBorProjectInProgressDispOpt = 'display:none;';
$showcoBorOwnInvestmentDispOpt = $showcoBorMemberDispOpt = 'display:none;';
$showMember2DispOpt = $showMember3DispOpt = 'display:none;';
$showBorrowerEntityDispOpt = 'display:none;';

$borDesignatedBeneficiaryAgreement = '';
$borDesignatedBeneficiaryAgreementExpln = '';
$coBorDesignatedBeneficiaryAgreement = '';
$coBorDesignatedBeneficiaryAgreementExpln = '';
$marriedToBor = '';
$midFicoScoreCoBor = '';
$coBorrowerCitizenship = '';

$borDecalredBankruptExpln = $borOutstandingJudgementsExpln = $borActiveLawsuitsExpln = $borPropertyTaxLiensExpln = $borObligatedInForeclosureExpln = '';
$borDelinquentExpln = $borOtherFraudRelatedCrimesExpln = $borBorrowedDownPaymentExpln = '';
$borDecalredBankruptDispOpt = $borOutstandingJudgementsDispOpt = $borActiveLawsuitsDispOpt = $borPropertyTaxLiensDispOpt = $borObligatedInForeclosureDispOpt = $borOriginAndVisaDispOpt = 'display: none';
$borDelinquentDispOpt = $borOtherFraudRelatedCrimesDispOpt = $borBorrowedDownPaymentDispOpt = 'display: none';
$coBorDecalredBankruptExpln = $coBorOutstandingJudgementsExpln = $coBorActiveLawsuitsExpln = $coBorPropertyTaxLiensExpln = $coBorObligatedInForeclosureExpln = '';
$coBorDelinquentExpln = $coBorOtherFraudRelatedCrimesExpln = $coBorBorrowedDownPaymentExpln = '';
$borrowerUnderEntity = '';
$coBorDecalredBankruptDispOpt = $coBorOutstandingJudgementsDispOpt = $coBorActiveLawsuitsDispOpt = $coBorPropertyTaxLiensDispOpt = $coBorObligatedInForeclosureDispOpt = 'display: none';
$coBorPriInvesStrategyDispOpt = 'display: none';
$coBorDelinquentDispOpt = $coBorOtherFraudRelatedCrimesDispOpt = $coBorBorrowedDownPaymentDispOpt = 'display: none';
$memberCnt = 0;
$entityBillAddrIsPresent = '';
$entityBillAddress = '';
$entityBillCity = '';
$entityBillState = '';
$entityBillZip = '';
$entityLocation = '';
$sameAsEntityAddr = 0;
$propertyValuationDocInfo = [];

$docArray = [];
$fileAdditionalGuarantorsInfo = [];
$guarantorNotes = '';
$businessTypeEF = '';
$fileHMLONewLoanInfo = [];
$tradeName = '';
$crossCorporateGuarantor = '';
$noOfEmployees = '';
$grossAnnualRevenues = '';
$grossIncomeLastYear = '';
$netIncomeLastYear = '';
$grossIncome2YearsAgo = '';
$netIncome2YearsAgo = '';
$entityBusinessSell = '';
$entityBusinessType = '';
$entityService = '';
$entityProduct = '';
$entityB2B = '';
$entityB2C = '';
$businessDescription = '';
$liquidAssets = '';
$brokerDealPoints = '';
$brokerDealFee = '';
$brokerQuotedinterestRate = '';
$accessWholesalePricing = '';
$businessEntityPhone = '';
$businessEntityFax = '';
$organizationalRef = '';
$haveBorProfLicences = $borProfLicence = '';
$showProfLicencesOpt = $haveBorSellPropertieDisp = 'display:none;';
$haveCoBorProfLicences = '';
$coBorProfLicence = '';
$isAdditionalGuarantors = '';
$fullTimeRealEstateInvestor = '';
$clientExpProInfo = $clientGUExpInfo = [];
$clientDocsArray = [];
$fileExpFilpGroundUp = [];
$clientSellExpInfo = [];
$areBuilderDeveloper = '';
$doYouHireGC = '';
$isHMLOSelOpt = $isHMLO;

$haveBorSellPropertie = '';
$borNoOfProSellExperience = '';
$borNoOfProSellCompleted = '';
$haveCoBorSellPropertie = '';
$coBorNoOfProSellExperience = '';
$coBorNoOfProSellCompleted = '';
$coBorSellAddress1 = '';
$coBorSellOutcome1 = '';
$proofOfSale13 = '';
$coBorSellAddress2 = '';
$coBorSellOutcome2 = '';
$proofOfSale14 = '';
$coBorSellAddress3 = '';
$coBorSellOutcome3 = '';
$proofOfSale15 = '';

$coBorTotalExp = 0;
$borTotalExp = 0;
$borPriInvesStrategyArray = $coBorPriInvesStrategyArray = $FileProInfo = $FilePropInfo = [];
$amountOfFinancing = $amountOfFinancingTo = $typicalPurchasePrice = $typicalPurchasePriceTo = $typicalConstructionCosts = $typicalConstructionCostsTo = '';
$typicalSalePrice = $typicalSalePriceTo = $constructionDrawsPerProject = $constructionDrawsPerProjectTo = $monthsPurchaseDateToFirstConst = '';
$monthsPurchaseDateToFirstConstTo = $monthsPurchaseDateUntilConst = $monthsPurchaseDateUntilConstTo = $monthsPurchaseDateToSaleDate = '';
$monthsPurchaseDateToSaleDateTo = $NoOfSuchProjects = $NoOfSuchProjectsTo = '';
$coBorLicenseNo = '';
$borPrimaryInvestmentStrategy = '';
$borPrimaryInvestmentStrategyExplain = '';
$coBorPrimaryInvestmentStrategy = '';
$borLicenseNo = '';
$geographicAreas = [];

$coBorPrimaryInvestmentStrategyExplain = $borrowerCallBack = $welcomeCallDate = $receivedDate = $purchaseCloseDate = '';
$dateOfFormation = '';
$mailingAddressAsProp = 0;
$coBorNoOfFlippingExperience = 0;
$HMLOLoanInfoSectionsDisp = '';
$showcoBorProfLicenceDispOpt = 'display:none;';
$flipPropCompletedLifetime = $groundPropCompletedLifetime = $sellPropCompletedLifetime = '';
$LBContactName = $LBContactPhone = $presentOccupancy = '';
$overallRealEstateInvesExp = '';
$basementHome = 0;
$areTaxesInsuranceEscrowed = 1;
$areInsuranceEscrowed = 1;
$dateOfOperatingAgreement = '';
$presentPropLengthTime = 0;
$coBorliquidReserves = 0;
$driverLicenseState = '';
$driverLicenseNumber = '';
$coBorDriverLicenseState = '';
$coBorDriverLicenseNumber = '';
$maritalStatusCoBor = '';
$methodContactArray = [];

$SBABackground = [];
/* Business Entity */
$entityPropertyOwnerShip = '';
$valueOfProperty = '';
$totalDebtOnProperty = '';
$nameOfLenders = '';
$benBusinessHomeBased = '';
$benCreditCardPayments = '';
$benChargeSalesTax = '';
$benEmployeesPaid = '';
$terminalOrMakeModel = '';
$benCardProcessorBank = '';
$merchantProcessingBankName = '';
$benBusinessLocation = '';
$benHowManyLocation = '';
$benOtherLocation = '';
$benBusinessFranchise = '';
$benNameOfFranchise = '';
$benPointOfContact = '';
$benPointOfContactPhone = '';
$benPointOfContactEmail = '';
$benWebsiteForFranchise = '';
$noOfEmployeesAfterLoan = '';
$businessCategory = '';
$productTypeOrServiceSold = '';
$averageBankBalance = $landlordMortagageContactName = $landlordMortagagePhone = '';
$avgMonthlyCreditcardSale = $avgTotalMonthlySale = 0;
$isBusinessSeasonal = '';
$isBusinessSeasonalPeakMonth = '';
$startDateAtLocation = '';
/* Business Entity */
$recentNSFs = $hasBusinessBankruptcy = $businessBankruptcy = '';
$borrowerAlternateNamesArray = [];
$personalBankruptcy = '';
/* Borrower Info */
$borrowerSecondaryEmail = '';
/* Borrower Info */
for ($j = 1; $j <= 10; $j++) {
    if ($j == 1) {
        ${'showMember' . $j . 'DispOpt'} = 'display:table-row';
    } else {
        ${'showMember' . $j . 'DispOpt'} = 'display:none';
    }
    ${'member' . $j . 'Name'} = '';
    ${'member' . $j . 'Title'} = '';
    ${'member' . $j . 'Ownership'} = '';
    ${'member' . $j . 'Address'} = '';
    ${'member' . $j . 'Phone'} = '';
    ${'member' . $j . 'Cell'} = '';
    ${'member' . $j . 'SSN'} = '';
    ${'member' . $j . 'DOB'} = '';
    ${'member' . $j . 'Email'} = '';
    ${'member' . $j . 'CreditScore'} = '';
}
if (count($myFileInfo) > 0) {
    if (array_key_exists('propertyCountyInfo', $myFileInfo)) $propertyCountyInfo = $myFileInfo['propertyCountyInfo'];
    /** Fetch all county for the particular property States **/
    if (array_key_exists('coBorrowerCountyInfo', $myFileInfo)) $coBorrowerCountyInfo = $myFileInfo['coBorrowerCountyInfo'];
    /** Fetch all county for the particular co-borrower mailing States **/
    if (array_key_exists('branchModuleInfo', $myFileInfo)) $moduleRequested = $myFileInfo['branchModuleInfo'];
    /** Fetch all Branch modules requested **/
    if (array_key_exists('branchClientTypeInfo', $myFileInfo)) $servicesRequested = $myFileInfo['branchClientTypeInfo'];
    /** Fetch all Branch services requested **/
    if (array_key_exists('BrokerInfo', $myFileInfo)) $BrokerInfo = $myFileInfo['BrokerInfo'];
    /** Fetch agent info **/
    if (array_key_exists('RESTInfo', $myFileInfo)) $RESTInfo = $myFileInfo['RESTInfo'];
    /** Fetch all REST Info **/
    if (array_key_exists('fileContacts', $myFileInfo)) $fileContacts = $myFileInfo['fileContacts'];
    /** Fetch all REST Info **/
    if (array_key_exists('QAInfo', $myFileInfo)) $QAInfo = $myFileInfo['QAInfo'];
    /** Fetch all QA Info **/
    if (array_key_exists('fileLoanOriginationInfo', $myFileInfo)) $fileLoanOriginationInfo = $myFileInfo['fileLoanOriginationInfo'];
    if (array_key_exists('propertyValuation', $myFileInfo)) $propertyValuationDocInfo = $myFileInfo['propertyValuation'];
    if (array_key_exists('docArray', $myFileInfo)) $docArray = $myFileInfo['docArray'];
    if (count($fileLoanOriginationInfo) > 0) {
        $borResidedPresentAddr = Strings::showField('borResidedPresentAddr', 'fileLoanOriginationInfo');
        $coBResidedPresentAddr = Strings::showField('coBResidedPresentAddr', 'fileLoanOriginationInfo');
    }
    if (array_key_exists('LMRClientTypeInfo', $myFileInfo)) {
        if (array_key_exists($LMRId, $myFileInfo['LMRClientTypeInfo'])) $LMRClientTypeInfo = $myFileInfo['LMRClientTypeInfo'][$LMRId];
    }
    /** Fetch file services **/
    if (array_key_exists('fileModuleInfo', $myFileInfo)) {
        if (array_key_exists($LMRId, $myFileInfo['fileModuleInfo'])) $fileModuleInfo = $myFileInfo['fileModuleInfo'][$LMRId];
    }
    /** Fetch file modules **/
    if (array_key_exists('FUInfo', $myFileInfo)) $FUInfo = $myFileInfo['FUInfo'];
    /** Fetch all Funding Module Info **/

    if (array_key_exists('fileHMLOInfo', $myFileInfo)) $fileHMLOInfo = $myFileInfo['fileHMLOInfo'];
    if (array_key_exists('fileHMLOEntityInfo', $myFileInfo)) $fileHMLOEntityInfo = $myFileInfo['fileHMLOEntityInfo'];
    if (array_key_exists('fileMemberOfficerInfo', $myFileInfo)) $fileMemberOfficerInfo = $myFileInfo['fileMemberOfficerInfo'];

    if (array_key_exists('fileHMLOBackGroundInfo', $myFileInfo)) $fileHMLOBackGroundInfo = $myFileInfo['fileHMLOBackGroundInfo'];

    if (array_key_exists('SBABackground', $myFileInfo)) $SBABackground = $myFileInfo['SBABackground'];

    if (array_key_exists('fileHMLOExperienceInfo', $myFileInfo)) $fileHMLOExperienceInfo = $myFileInfo['fileHMLOExperienceInfo'];
    if (array_key_exists('AddGuarantorsInfo', $myFileInfo)) $fileAdditionalGuarantorsInfo = $myFileInfo['AddGuarantorsInfo'];
    if (array_key_exists('fileHMLONewLoanInfo', $myFileInfo)) $fileHMLONewLoanInfo = $myFileInfo['fileHMLONewLoanInfo'];
    if (array_key_exists('clientDocsArray', $myFileInfo)) $clientDocsArray = $myFileInfo['clientDocsArray'];
    /** Fetch Client Docs **/
    if (array_key_exists('fileExpFilpGroundUp', $myFileInfo)) $fileExpFilpGroundUp = $myFileInfo['fileExpFilpGroundUp'];
    /** Fetch File Background **/
    if (array_key_exists('FileProInfo', $myFileInfo)) $FileProInfo = $myFileInfo['FileProInfo'];
    if (array_key_exists('FilePropInfo', $myFileInfo)) $FilePropInfo = $myFileInfo['FilePropInfo'];
    if (array_key_exists('clientInfo', $myFileInfo)) $clientInfo = $myFileInfo['clientInfo'];
    if (array_key_exists('borrowerAlternateNames', $myFileInfo)) $borrowerAlternateNamesArray = $myFileInfo['borrowerAlternateNames'];
    if (array_key_exists('fileHMLOEntityRefInfo', $myFileInfo)) $fileHMLOEntityRefInfoArray = $myFileInfo['fileHMLOEntityRefInfo'] ?? [];

}
if (count($SBABackground) > 0) {
    unset($SBABackground['SBAID']);
    unset($SBABackground['fileID']);
    unset($SBABackground['CID']);
    foreach ($SBABackground as $sbKey => $sbVal) {
        ${$sbKey} = $sbVal;
    }
}


/** Fetch all Hard / Private Money LOS Module Info **/
$phoneNumber = Strings::cleanPhoneNo(Strings::showField('phoneNumber', 'LMRInfo'));
$altPhoneNumber = Strings::showField('altPhoneNumber', 'LMRInfo');
$cellNumber = Strings::showField('cellNumber', 'LMRInfo') == '' ? Strings::showField('clientCell', 'clientInfo') : Strings::showField('cellNumber', 'LMRInfo');
$borrFax = Strings::showField('fax', 'LMRInfo');
$workNumber = Strings::showField('workNumber', 'LMRInfo');
$ssnNumber = Strings::showField('ssnNumber', 'LMRInfo') == '' ? Strings::showField('ssnNumber', 'clientInfo') : Strings::showField('ssnNumber', 'LMRInfo');
$coBSsnNumber = Strings::showField('coBSsnNumber', 'LMRInfo');
$coBPhoneNumber = Strings::showField('coBPhoneNumber', 'LMRInfo');
$coBAltPhoneNumber = Strings::showField('coBAltPhoneNumber', 'LMRInfo');
$coBCellNumber = Strings::showField('coBCellNumber', 'LMRInfo');
$coBFax = Strings::showField('coBFax', 'LMRInfo');
$coBorrowerWorkNumber = Strings::showField('coBorrowerWorkNumber', 'LMRInfo');
$servicer1 = Strings::showField('servicer1', 'LMRInfo');
$originalLender1 = Strings::showField('originalLender1', 'LMRInfo');
$mortgageOwner1 = Strings::showField('mortgageOwner1', 'LMRInfo');
$servicer2 = Strings::showField('servicer2', 'LMRInfo');
$originalLender2 = Strings::showField('originalLender2', 'LMRInfo');
$mortgageOwner2 = Strings::showField('mortgageOwner2', 'LMRInfo');
$marriageDate = Strings::showField('marriageDate', 'LMRInfo');
$divorceDate = Strings::showField('divorceDate', 'LMRInfo');
$isCoBorrower = Strings::showField('isCoBorrower', 'LMRInfo');
$coBorrowerDOB = Strings::showField('coBorrowerDOB', 'LMRInfo');
$coborrowerPOB = Strings::showField('coborrowerPOB', 'LMRInfo');
$mailingState = Strings::showField('mailingState', 'LMRInfo');
$previousState = Strings::showField('previousState', 'LMRInfo');
$coBorrowerMailingState = Strings::showField('coBorrowerMailingState', 'LMRInfo');
$coBorPreviousState = Strings::showField('coBorPreviousState', 'LMRInfo');
$noOfMonthsBehind1 = Strings::showField('noOfMonthsBehind1', 'LMRInfo');
$noOfMonthsBehind2 = Strings::showField('noOfMonthsBehind2', 'LMRInfo');
$propertyCounty = Property::$primaryPropertyInfo->propertyCounty;
$coBorrowerCounty = Strings::showField('coBorrowerCounty', 'LMRInfo');
$propertyType = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId() ? Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyType : null;
$loanType = Strings::showField('loanType', 'LMRInfo');
$loanType2 = Strings::showField('loanType2', 'LMRInfo');
$lien1Terms = Strings::showField('lien1Terms', 'LMRInfo');
$lien2Terms = Strings::showField('lien2Terms', 'LMRInfo');
$serviceProvider = Strings::showField('serviceProvider', 'LMRInfo');
$coBServiceProvider = Strings::showField('coBServiceProvider', 'LMRInfo');
$executiveId = Strings::showField('FBRID', 'LMRInfo');
$agentNumber = Strings::showField('brokerNumber', 'LMRInfo');
$LMRResponseId = Strings::showField('LMRResponseId', 'ResponseInfo');
$leadSource = Strings::showField('leadSource', 'ResponseInfo');
$lien1LPMade = Strings::showField('lien1LPMade', 'LMRInfo');
$lien2LPMade = Strings::showField('lien2LPMade', 'LMRInfo');
$mortgageInvestor1 = Strings::showField('mortgageInvestor1', 'LMRInfo');
$mortgageInvestor2 = Strings::showField('mortgageInvestor2', 'LMRInfo');
$escrowAdvances = Strings::showField('lien1ProposalEscrowShortage', 'proposalInfo');
$projectedEscrowAdvances = Strings::showField('projectedEscrowAdvances', 'incomeInfo');
$proposalLateFees = Strings::showField('lien1ProposalFeesAdminCosts', 'proposalInfo');
$pastDueMtg = Strings::showField('pastDueMtg', 'incomeInfo');
$pastDueHOA = Strings::showField('pastDueHOA', 'incomeInfo');
$noteDate = Strings::showField('noteDate', 'RESTInfo');
$SID = Strings::showField('SID', 'RESTInfo');
$noticeAccelerationDate = Strings::showField('noticeAccelerationDate', 'file2Info');
$methodOfContact = Strings::showField('methodOfContact', 'file2Info');
$coMethodOfContact = Strings::showField('coMethodOfContact', 'file2Info');
$borrowerTimeZone = Strings::showField('borrowerTimeZone', 'LMRInfo');
$coBorrowerTimeZone = Strings::showField('coBorrowerTimeZone', 'LMRInfo');
$presentPropLengthTime = Strings::showField('presentPropLengthTime', 'file2Info');
$ssnNumberArray = Strings::splitSSNNumber($ssnNumber);
$borrowerFName = $borrowerName = Strings::showField('borrowerFName', 'LMRInfo');
$borrowerMName = Strings::showField('borrowerMName', 'LMRInfo');
$borrowerLName = Strings::showField('borrowerLName', 'LMRInfo');
$maritalStatus = Strings::showField('maritalStatus', 'LMRInfo');
$maidenName = Strings::showField('maidenName', 'LMRInfo');
$spouseName = Strings::showField('spouseName', 'LMRInfo');
$mailingAddressAsProp = Strings::showField('mailingAddressAsProp', 'LMRInfo');
$borrowerEmail = Strings::showField('borrowerEmail', 'LMRInfo');
$borrowerSecondaryEmail = Strings::showField('borrowerSecondaryEmail', 'LMRInfo');
$cellNo = Strings::showField('cellNo', 'LMRInfo');
$basementHome = Strings::showField('basementHome', 'LMRInfo');

$coBorrowerFName = Strings::showField('coBorrowerFName', 'LMRInfo');
$coBorrowerLName = Strings::showField('coBorrowerLName', 'LMRInfo');
$coBorrowerEmail = Strings::showField('coBorrowerEmail', 'LMRInfo');
$coBorrowerFax = Strings::showField('coBFax', 'LMRInfo');
$areTaxesInsuranceEscrowed = Strings::showField('areTaxesInsuranceEscrowed', 'LMRInfo');
$areInsuranceEscrowed = Strings::showField('areInsuranceEscrowed', 'LMRInfo');

$remainingMonths = Strings::showField('remainingMonths', 'LMRInfo');
$nonBorrowerDOB = Strings::showField('nonBorrowerDOB', 'file2Info');
$nonBorrowerSSNNumber = Strings::showField('nonBorrowerSSN', 'file2Info');
$isNonBorrower = Strings::showField('isNonBorrower', 'file2Info');
$mailingAddrAsPresent = Strings::showField('mailingAddrAsPresent', 'file2Info');
$coBorMailingAddrAsPresent = Strings::showField('coBorMailingAddrAsPresent', 'file2Info');
/** Fetch the LO Present Address Section **/
$presentAddress = Strings::showField('presentAddress', 'file2Info');
$presentCity = Strings::showField('presentCity', 'file2Info');
$presentState = Strings::showField('presentState', 'file2Info');
$presentZip = Strings::showField('presentZip', 'file2Info');
$presentUnit = Strings::showField('presentUnit', 'file2Info');
$presentCountry = Strings::showField('presentCountry', 'file2Info');
$presentPropLengthMonths = Strings::showField('presentPropLengthMonths', 'file2Info');
$currentRPM = Strings::showField('currentRPM', 'file2Info');
$previousUnit = Strings::showField('previousUnit', 'file2Info');
$previousCountry = Strings::showField('previousCountry', 'file2Info');
$previousPropLengthMonths = Strings::showField('previousPropLengthMonths', 'file2Info');
$previousRPM = Strings::showField('previousRPM', 'file2Info');
$coBPresentAddress = Strings::showField('coBPresentAddress', 'file2Info');
$coBPresentCity = Strings::showField('coBPresentCity', 'file2Info');
$coBPresentState = Strings::showField('coBPresentState', 'file2Info');
$coBPresentZip = Strings::showField('coBPresentZip', 'file2Info');
$coBNoOfDependent = Strings::showField('coBNoOfDependent', 'file2Info');
$borPresentPropType = Strings::showField('borPresentPropType', 'file2Info');
$borMailingPropType = Strings::showField('borMailingPropType', 'file2Info');
$borFormerPropType = Strings::showField('borFormerPropType', 'file2Info');
$coBPresentPropType = Strings::showField('coBPresentPropType', 'file2Info');
$presentPropLengthTimeCoBor = Strings::showField('presentPropLengthTimeCoBor', 'file2Info');
$coBMailingPropType = Strings::showField('coBMailingPropType', 'file2Info');
$coBFormerPropType = Strings::showField('coBFormerPropType', 'file2Info');
$guarantorNotes = Strings::showField('guarantorNotes', 'file2Info');
$methodOfContact = Strings::showField('methodOfContact', 'file2Info');
if ($methodOfContact != '') $methodContactArray = explode(',', $methodOfContact);
$presentOccupancy = Property::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId() ? Property::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId()->propertyPresentOccupancy : null;

if (($maritalStatus == 'Married'
        || $maritalStatus == 'Divorced'
        || $maritalStatus == 'Widowed'
        || $maritalStatus == 'Separated'
    ) && $isMF == 0) {
    $maritalDisp = 'display: block;';
} else {
    $maritalDisp = 'display: none;';
}
/** Fetch the Merchant Funding Section (March 25, 2016) **/
$majorityBusiness = Strings::showField('majorityBusiness', 'MFInfo');
$ownersName = Strings::showField('ownersName', 'MFInfo');
$Owned = Strings::showField('Owned', 'MFInfo');
$businessName = Strings::showField('businessName', 'MFInfo');
$businessNameDBA = Strings::showField('businessNameDBA', 'MFInfo');
$businessLegalName = Strings::showField('businessLegalName', 'MFInfo');
$businessTax = Strings::showField('businessTax', 'MFInfo');
$businessType = Strings::showField('businessType', 'MFInfo');
$Website = Strings::showField('Website', 'MFInfo');
$Industry = Strings::showField('Industry', 'MFInfo');
$businessConsumers = Strings::showField('businessConsumers', 'MFInfo');
$monthsBusiness = Strings::showField('monthsBusiness', 'MFInfo');
$monthlyRevenue = Strings::showField('monthlyRevenue', 'MFInfo');
$creditScore = Strings::showField('creditScore', 'MFInfo');
$grossRevenue = Strings::showField('grossRevenue', 'MFInfo');
$monthlyDepositVolume = Strings::showField('monthlyDepositVolume', 'MFInfo');
$bankBalance = Strings::showField('bankBalance', 'MFInfo');
$invoices = Strings::showField('invoices', 'MFInfo');
$invoiceAmount = Strings::showField('invoiceAmount', 'MFInfo');
$requestedAmount = Strings::showField('requestedAmount', 'MFInfo');
$requestedTermLength = Strings::showField('requestedTermLength', 'MFInfo');
$funding = Strings::showField('funding', 'MFInfo');
$businessCreditCards = Strings::showField('businessCreditCards', 'MFInfo');
$businessFinancing = Strings::showField('businessFinancing', 'MFInfo');
$netProfit = Strings::showField('netProfit', 'MFInfo');
$amount = Strings::showField('amount', 'MFInfo');
$ownCollateral = Strings::showField('ownCollateral', 'MFInfo');
$monthlyDebtPayments = Strings::showField('monthlyDebtPayments', 'MFInfo');
/** Fetch the Hard / Private Money LOS Modules (Nov 21, 2016) **/
$borCompanyName = Strings::showField('borCompanyName', 'fileHMLOInfo');
$borCreditScoreRange = Strings::showField('borCreditScoreRange', 'fileHMLOInfo');
$midFicoScore = Strings::showField('midFicoScore', 'fileHMLOInfo');
$borCreditScore = Strings::showField('borCreditScore', 'fileHMLOInfo');
$coBorCompanyName = Strings::showField('coBorCompanyName', 'fileHMLOInfo');
$coBorCreditScoreRange = Strings::showField('coBorCreditScoreRange', 'fileHMLOInfo');
$coBorCreditScore = Strings::showField('coBorCreditScore', 'fileHMLOInfo');
$borExperianScore = Strings::showField('borExperianScore', 'fileHMLOInfo');
$borEquifaxScore = Strings::showField('borEquifaxScore', 'fileHMLOInfo');
$borTransunionScore = Strings::showField('borTransunionScore', 'fileHMLOInfo');
$coBorExperianScore = Strings::showField('coBorExperianScore', 'fileHMLOInfo');
$coBorEquifaxScore = Strings::showField('coBorEquifaxScore', 'fileHMLOInfo');
$coBorTransunionScore = Strings::showField('coBorTransunionScore', 'fileHMLOInfo');
$midFicoScoreCoBor = Strings::showField('midFicoScoreCoBor', 'fileHMLOInfo');
$coBorrowerCitizenship = Strings::showField('coBorrowerCitizenship', 'fileHMLOInfo');
$maritalStatusCoBor = Strings::showField('maritalStatusCoBor', 'fileHMLOInfo');
$borrowerCitizenship = Strings::showField('borrowerCitizenship', 'fileHMLOInfo');
$isServicingMember = Strings::showField('isServicingMember', 'fileHMLOInfo');
$servicingMemberInfo = Strings::showField('servicingMemberInfo', 'fileHMLOInfo');
$agesOfDependent = Strings::showField('agesOfDependent', 'fileHMLOInfo');
$numberOfDependents = Strings::showField('numberOfDependents', 'fileHMLOInfo');

$entityName = Strings::showField('entityName', 'fileHMLOEntityInfo');
$entityType = Strings::showField('entityType', 'fileHMLOEntityInfo');
$borrowerType = Strings::showField('borrowerType', 'fileHMLOEntityInfo');
$ENINo = Strings::showField('ENINo', 'fileHMLOEntityInfo');
$naicsCode = Strings::showField('naicsCode', 'fileHMLOEntityInfo');
$entityAddress = Strings::showField('entityAddress', 'fileHMLOEntityInfo');
$entityCity = Strings::showField('entityCity', 'fileHMLOEntityInfo');
$entityState = Strings::showField('entityState', 'fileHMLOEntityInfo');
$entityZip = Strings::showField('entityZip', 'fileHMLOEntityInfo');
$entityStateOfFormation = Strings::showField('entityStateOfFormation', 'fileHMLOEntityInfo');
$statesRegisterdIn = Strings::showField('statesRegisterdIn', 'fileHMLOEntityInfo');
$businessEntityFax = Strings::showField('businessEntityFax', 'fileHMLOEntityInfo');
$businessEntityPhone = Strings::showField('businessEntityPhone', 'fileHMLOEntityInfo');
$entityNotes = Strings::showField('entityNotes', 'fileHMLOEntityInfo');
$corporateSecretaryName = Strings::showField('corporateSecretaryName', 'fileHMLOEntityInfo');
$borrowerUnderEntity = Strings::showField('borrowerUnderEntity', 'fileHMLOEntityInfo');
$entityWebsite = Strings::showField('entityWebsite', 'fileHMLOEntityInfo');
$startDateAtLocation = Strings::showField('startDateAtLocation', 'fileHMLOEntityInfo');
$trustType = Strings::showField('trustType', 'fileHMLOEntityInfo') ?? '';
$retirementEntity = Strings::showField('retirementEntity', 'fileHMLOEntityInfo') ?? '';
if (Dates::IsEmpty($startDateAtLocation)) {
    $startDateAtLocation = '';
} else {
    $startDateAtLocation = Dates::formatDateWithRE($startDateAtLocation, 'YMD', 'm/d/Y');
}

$entityBillAddress = Strings::showField('entityBillAddress', 'fileHMLOEntityInfo');
$entityBillCity = Strings::showField('entityBillCity', 'fileHMLOEntityInfo');
$entityBillState = Strings::showField('entityBillState', 'fileHMLOEntityInfo');
$entityBillZip = Strings::showField('entityBillZip', 'fileHMLOEntityInfo');
$entityLocation = Strings::showField('entityLocation', 'fileHMLOEntityInfo');
$sameAsEntityAddr = Strings::showField('sameAsEntityAddr', 'fileHMLOEntityInfo');
$businessTypeEF = Strings::showField('businessTypeEF', 'fileHMLOEntityInfo');
$dateOfFormation = Strings::showField('dateOfFormation', 'fileHMLOEntityInfo');
$CBEID = Strings::showField('CBEID', 'fileHMLOEntityInfo');
$dateOfOperatingAgreement = Strings::showField('dateOfOperatingAgreement', 'fileHMLOEntityInfo');

$tradeName = Strings::showField('tradeName', 'fileHMLOEntityInfo');
$crossCorporateGuarantor = Strings::showField('crossCorporateGuarantor', 'fileHMLOEntityInfo');
$noOfEmployees = Strings::showField('noOfEmployees', 'fileHMLOEntityInfo');
$noOfEmployeesAfterLoan = Strings::showField('noOfEmployeesAfterLoan', 'fileHMLOEntityInfo');
$grossAnnualRevenues = Strings::showField('grossAnnualRevenues', 'fileHMLOEntityInfo');
$grossIncomeLastYear = Strings::showField('grossIncomeLastYear', 'fileHMLOEntityInfo');
$netIncomeLastYear = Strings::showField('netIncomeLastYear', 'fileHMLOEntityInfo');
$grossIncome2YearsAgo = Strings::showField('grossIncome2YearsAgo', 'fileHMLOEntityInfo');
$netIncome2YearsAgo = Strings::showField('netIncome2YearsAgo', 'fileHMLOEntityInfo');
$averageBankBalance = Strings::showField('averageBankBalance', 'fileHMLOEntityInfo');
$recentNSFs = Strings::showField('recentNSFs', 'fileHMLOEntityInfo');
$hasBusinessBankruptcy = Strings::showField('hasBusinessBankruptcy', 'fileHMLOEntityInfo');
$businessBankruptcy = Strings::showField('businessBankruptcy', 'fileHMLOEntityInfo');
//$entityBusinessSell = Strings::showField('entityBusinessSell', 'fileHMLOEntityInfo');
$entityService = Strings::showField('entityService', 'fileHMLOEntityInfo');
$entityProduct = Strings::showField('entityProduct', 'fileHMLOEntityInfo');
//$entityBusinessType = Strings::showField('entityBusinessType', 'fileHMLOEntityInfo');
$entityB2B = Strings::showField('entityB2B', 'fileHMLOEntityInfo');
$entityB2C = Strings::showField('entityB2C', 'fileHMLOEntityInfo');
$benBusinessHomeBased = Strings::showField('benBusinessHomeBased', 'fileHMLOEntityInfo');
$benCardProcessorBank = Strings::showField('benCardProcessorBank', 'fileHMLOEntityInfo');
$benCreditCardPayments = Strings::showField('benCreditCardPayments', 'fileHMLOEntityInfo');
$benChargeSalesTax = Strings::showField('benChargeSalesTax', 'fileHMLOEntityInfo');
$benEmployeesPaid = Strings::showField('benEmployeesPaid', 'fileHMLOEntityInfo');
$terminalOrMakeModel = Strings::showField('terminalOrMakeModel', 'fileHMLOEntityInfo');
$benBusinessLocation = Strings::showField('benBusinessLocation', 'fileHMLOEntityInfo');
$benHowManyLocation = Strings::showField('benHowManyLocation', 'fileHMLOEntityInfo');
$benOtherLocation = Strings::showField('benOtherLocation', 'fileHMLOEntityInfo');
$benBusinessFranchise = Strings::showField('benBusinessFranchise', 'fileHMLOEntityInfo');
$benNameOfFranchise = Strings::showField('benNameOfFranchise', 'fileHMLOEntityInfo');
$benPointOfContact = Strings::showField('benPointOfContact', 'fileHMLOEntityInfo');
$benPointOfContactPhone = Strings::showField('benPointOfContactPhone', 'fileHMLOEntityInfo');
$benPointOfContactEmail = Strings::showField('benPointOfContactEmail', 'fileHMLOEntityInfo');
$benWebsiteForFranchise = Strings::showField('benWebsiteForFranchise', 'fileHMLOEntityInfo');
$entityPropertyOwnerShip = Strings::showField('entityPropertyOwnerShip', 'fileHMLOEntityInfo');
$valueOfProperty = Strings::showField('valueOfProperty', 'fileHMLOEntityInfo');
$totalDebtOnProperty = Strings::showField('totalDebtOnProperty', 'fileHMLOEntityInfo');
$nameOfLenders = Strings::showField('nameOfLenders', 'fileHMLOEntityInfo');
$landlordMortagageContactName = Strings::showField('landlordMortagageContactName', 'fileHMLOEntityInfo');
$landlordMortagagePhone = Strings::showField('landlordMortagagePhone', 'fileHMLOEntityInfo');
$avgMonthlyCreditcardSale = Strings::showField('avgMonthlyCreditcardSale', 'fileHMLOEntityInfo');


$rentMortagagePayment = Strings::showField('rentMortagagePayment', 'fileHMLOEntityInfo');
$annualGrossSales = Strings::showField('annualGrossSales', 'fileHMLOEntityInfo');
$ordinaryBusinessIncome = Strings::showField('ordinaryBusinessIncome', 'fileHMLOEntityInfo');
$annualGrossProfit = Strings::showField('annualGrossProfit', 'fileHMLOEntityInfo');


$avgTotalMonthlySale = Strings::showField('avgTotalMonthlySale', 'fileHMLOEntityInfo');
$businessDescription = Strings::showField('businessDescription', 'fileHMLOEntityInfo');
$merchantProcessingBankName = Strings::showField('merchantProcessingBankName', 'fileHMLOEntityInfo');
$organizationalRef = Strings::showField('organizationalRef', 'fileHMLOEntityInfo');
$driverLicenseState = Strings::showField('driverLicenseState', 'LMRInfo');
$driverLicenseNumber = Strings::showField('driverLicenseNumber', 'LMRInfo');
$coBorDriverLicenseState = Strings::showField('coBorDriverLicenseState', 'LMRInfo');
$coBorDriverLicenseNumber = Strings::showField('coBorDriverLicenseNumber', 'LMRInfo');

$businessCategory = Strings::showField('businessCategory', 'fileHMLOEntityInfo');
$businessType = Strings::showField('businessType', 'fileHMLOEntityInfo');
$productTypeOrServiceSold = Strings::showField('productTypeOrServiceSold', 'fileHMLOEntityInfo');
$isBusinessSeasonal = Strings::showField('isBusinessSeasonal', 'fileHMLOEntityInfo');
$isBusinessSeasonalPeakMonth = Strings::showField('isBusinessSeasonalPeakMonth', 'fileHMLOEntityInfo');

$minTimeInBusiness = Strings::showField('minTimeInBusiness', 'fileHMLOEntityInfo') ?? '';
$businessPhone = Strings::showField('businessPhone', 'fileHMLOEntityInfo') ?? '';
$businessReference = Strings::showField('businessReference', 'fileHMLOEntityInfo');


for ($j = 1; $j <= 10; $j++) {
    ${'member' . $j . 'Name'} = Strings::showField('member' . $j . 'Name', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Title'} = Strings::showField('member' . $j . 'Title', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Ownership'} = Strings::showField('member' . $j . 'Ownership', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Address'} = Strings::showField('member' . $j . 'Address', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Phone'} = Strings::showField('member' . $j . 'Phone', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Cell'} = Strings::showField('member' . $j . 'Cell', 'fileHMLOEntityInfo');
    ${'member' . $j . 'SSN'} = Strings::showField('member' . $j . 'SSN', 'fileHMLOEntityInfo');
    ${'member' . $j . 'DOB'} = Strings::showField('member' . $j . 'DOB', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Email'} = Strings::showField('member' . $j . 'Email', 'fileHMLOEntityInfo');
    ${'member' . $j . 'CreditScore'} = Strings::showField('member' . $j . 'CreditScore', 'fileHMLOEntityInfo');
}

$isBorUSCitizen = Strings::showField('isBorUSCitizen', 'fileHMLOBackGroundInfo');
$isBorDecalredBankruptPastYears = Strings::showField('isBorDecalredBankruptPastYears', 'fileHMLOBackGroundInfo');
$isAnyBorOutstandingJudgements = Strings::showField('isAnyBorOutstandingJudgements', 'fileHMLOBackGroundInfo');
$hasBorAnyActiveLawsuits = Strings::showField('hasBorAnyActiveLawsuits', 'fileHMLOBackGroundInfo');
$hasBorPropertyTaxLiens = Strings::showField('hasBorPropertyTaxLiens', 'fileHMLOBackGroundInfo');
$hasBorObligatedInForeclosure = Strings::showField('hasBorObligatedInForeclosure', 'fileHMLOBackGroundInfo');
$isBorPresenltyDelinquent = Strings::showField('isBorPresenltyDelinquent', 'fileHMLOBackGroundInfo');
$isBorBorrowedDownPayment = Strings::showField('isBorBorrowedDownPayment', 'fileHMLOBackGroundInfo');
$isBorIntendToOccupyPropAsPRI = Strings::showField('isBorIntendToOccupyPropAsPRI', 'fileHMLOBackGroundInfo');
$haveBorOtherFraudRelatedCrimes = Strings::showField('haveBorOtherFraudRelatedCrimes', 'fileHMLOBackGroundInfo');
$isBorPersonallyGuaranteeLoan = Strings::showField('isBorPersonallyGuaranteeLoan', 'fileHMLOBackGroundInfo');
$borBackgroundExplanation = Strings::showField('borBackgroundExplanation', 'fileHMLOBackGroundInfo');
$borDesignatedBeneficiaryAgreement = Strings::showField('borDesignatedBeneficiaryAgreement', 'fileHMLOBackGroundInfo');
$borDesignatedBeneficiaryAgreementExpln = Strings::showField('borDesignatedBeneficiaryAgreementExpln', 'fileHMLOBackGroundInfo');

if ($borrowerCitizenship == 'U.S. Citizen') {
    $isBorUSCitizen = 'Yes';
} elseif ($borrowerCitizenship != '') {
    $isBorUSCitizen = 'No';
}

$personalBankruptcy = Strings::showField('personalBankruptcy', 'fileHMLOBackGroundInfo');
$isCoBorUSCitizen = Strings::showField('isCoBorUSCitizen', 'fileHMLOBackGroundInfo');
$isCoBorDecalredBankruptPastYears = Strings::showField('isCoBorDecalredBankruptPastYears', 'fileHMLOBackGroundInfo');
$isAnyCoBorOutstandingJudgements = Strings::showField('isAnyCoBorOutstandingJudgements', 'fileHMLOBackGroundInfo');
$hasCoBorAnyActiveLawsuits = Strings::showField('hasCoBorAnyActiveLawsuits', 'fileHMLOBackGroundInfo');
$hasCoBorPropertyTaxLiens = Strings::showField('hasCoBorPropertyTaxLiens', 'fileHMLOBackGroundInfo');
$hasCoBorObligatedInForeclosure = Strings::showField('hasCoBorObligatedInForeclosure', 'fileHMLOBackGroundInfo');
$isCoBorPresenltyDelinquent = Strings::showField('isCoBorPresenltyDelinquent', 'fileHMLOBackGroundInfo');
$isCoBorBorrowedDownPayment = Strings::showField('isCoBorBorrowedDownPayment', 'fileHMLOBackGroundInfo');
$isCoBorIntendToOccupyPropAsPRI = Strings::showField('isCoBorIntendToOccupyPropAsPRI', 'fileHMLOBackGroundInfo');
$haveCoBorOtherFraudRelatedCrimes = Strings::showField('haveCoBorOtherFraudRelatedCrimes', 'fileHMLOBackGroundInfo');
$isCoBorPersonallyGuaranteeLoan = Strings::showField('isCoBorPersonallyGuaranteeLoan', 'fileHMLOBackGroundInfo');
$coBorBackgroundExplanation = Strings::showField('coBorBackgroundExplanation', 'fileHMLOBackGroundInfo');
$borOrigin = Strings::showField('borOrigin', 'fileHMLOBackGroundInfo');
$borVisaStatus = Strings::showField('borVisaStatus', 'fileHMLOBackGroundInfo');
$statusForeclosure = Strings::showField('statusForeclosure', 'fileHMLOBackGroundInfo');

if ($coBorrowerCitizenship == 'U.S. Citizen') {
    $isCoBorUSCitizen = 'Yes';
} elseif ($coBorrowerCitizenship != '') {
    $isCoBorUSCitizen = 'No';
}

$coBorDesignatedBeneficiaryAgreement = Strings::showField('coBorDesignatedBeneficiaryAgreement', 'fileHMLOBackGroundInfo');
$coBorDesignatedBeneficiaryAgreementExpln = Strings::showField('coBorDesignatedBeneficiaryAgreementExpln', 'fileHMLOBackGroundInfo');
$marriedToBor = Strings::showField('marriedToBor', 'fileHMLOBackGroundInfo');
$haveBorREInvestmentExperience = Strings::showField('haveBorREInvestmentExperience', 'fileHMLOExperienceInfo');
$borNoOfREPropertiesCompleted = Strings::showField('borNoOfREPropertiesCompleted', 'fileHMLOExperienceInfo');
$borNoOfFlippingExperience = Strings::showField('borNoOfFlippingExperience', 'fileHMLOExperienceInfo');
$haveBorRehabConstructionExperience = Strings::showField('haveBorRehabConstructionExperience', 'fileHMLOExperienceInfo');
$borNoOfYearRehabExperience = Strings::showField('borNoOfYearRehabExperience', 'fileHMLOExperienceInfo');
$borRehabPropCompleted = Strings::showField('borRehabPropCompleted', 'fileHMLOExperienceInfo');
$haveBorProjectCurrentlyInProgress = Strings::showField('haveBorProjectCurrentlyInProgress', 'fileHMLOExperienceInfo');
$borNoOfProjectCurrently = Strings::showField('borNoOfProjectCurrently', 'fileHMLOExperienceInfo');
$haveBorOwnInvestmentProperties = Strings::showField('haveBorOwnInvestmentProperties', 'fileHMLOExperienceInfo');
$borNoOfOwnProp = Strings::showField('borNoOfOwnProp', 'fileHMLOExperienceInfo');
$areBorMemberOfInvestmentClub = Strings::showField('areBorMemberOfInvestmentClub', 'fileHMLOExperienceInfo');
$borClubName = Strings::showField('borClubName', 'fileHMLOExperienceInfo');
$liquidAssets = Strings::showField('liquidAssets', 'fileHMLOExperienceInfo');
$areBuilderDeveloper = Strings::showField('areBuilderDeveloper', 'fileHMLOExperienceInfo');
$doYouHireGC = Strings::showField('doYouHireGC', 'fileHMLOExperienceInfo');
$coBorliquidReserves = Strings::showField('coBorliquidReserves', 'fileHMLOExperienceInfo');

$haveCoBorREInvestmentExperience = Strings::showField('haveCoBorREInvestmentExperience', 'fileHMLOExperienceInfo');
$coBorNoOfREPropertiesCompleted = Strings::showField('coBorNoOfREPropertiesCompleted', 'fileHMLOExperienceInfo');
$haveCoBorRehabConstructionExperience = Strings::showField('haveCoBorRehabConstructionExperience', 'fileHMLOExperienceInfo');
$coBorNoOfYearRehabExperience = Strings::showField('coBorNoOfYearRehabExperience', 'fileHMLOExperienceInfo');
$coBorRehabPropCompleted = Strings::showField('coBorRehabPropCompleted', 'fileHMLOExperienceInfo');
$haveCoBorProjectCurrentlyInProgress = Strings::showField('haveCoBorProjectCurrentlyInProgress', 'fileHMLOExperienceInfo');
$coBorNoOfProjectCurrently = Strings::showField('coBorNoOfProjectCurrently', 'fileHMLOExperienceInfo');
$haveCoBorOwnInvestmentProperties = Strings::showField('haveCoBorOwnInvestmentProperties', 'fileHMLOExperienceInfo');
$coBorNoOfOwnProp = Strings::showField('coBorNoOfOwnProp', 'fileHMLOExperienceInfo');
$areCoBorMemberOfInvestmentClub = Strings::showField('areCoBorMemberOfInvestmentClub', 'fileHMLOExperienceInfo');
$coBorNoOfFlippingExperience = Strings::showField('coBorNoOfFlippingExperience', 'fileHMLOExperienceInfo');

$coBorClubName = Strings::showField('coBorClubName', 'fileHMLOExperienceInfo');
$borREAddress1 = Strings::showField('borREAddress1', 'fileHMLOExperienceInfo');
$borOutcomeRE1 = Strings::showField('borOutcomeRE1', 'fileHMLOExperienceInfo');
$borREAddress2 = Strings::showField('borREAddress2', 'fileHMLOExperienceInfo');
$borOutcomeRE2 = Strings::showField('borOutcomeRE2', 'fileHMLOExperienceInfo');
$borREAddress3 = Strings::showField('borREAddress3', 'fileHMLOExperienceInfo');
$borOutcomeRE3 = Strings::showField('borOutcomeRE3', 'fileHMLOExperienceInfo');
$coBorREAddress1 = Strings::showField('coBorREAddress1', 'fileHMLOExperienceInfo');
$coBorOutcomeRE1 = Strings::showField('coBorOutcomeRE1', 'fileHMLOExperienceInfo');
$coBorREAddress2 = Strings::showField('coBorREAddress2', 'fileHMLOExperienceInfo');
$coBorOutcomeRE2 = Strings::showField('coBorOutcomeRE2', 'fileHMLOExperienceInfo');
$coBorREAddress3 = Strings::showField('coBorREAddress3', 'fileHMLOExperienceInfo');
$coBorOutcomeRE3 = Strings::showField('coBorOutcomeRE3', 'fileHMLOExperienceInfo');

$borRCAddress1 = Strings::showField('borRCAddress1', 'fileHMLOExperienceInfo');
$borRCOutcome1 = Strings::showField('borRCOutcome1', 'fileHMLOExperienceInfo');
$borRCAddress2 = Strings::showField('borRCAddress2', 'fileHMLOExperienceInfo');
$borRCOutcome2 = Strings::showField('borRCOutcome2', 'fileHMLOExperienceInfo');
$borRCAddress3 = Strings::showField('borRCAddress3', 'fileHMLOExperienceInfo');
$borRCOutcome3 = Strings::showField('borRCOutcome3', 'fileHMLOExperienceInfo');

$coBorRCAddress1 = Strings::showField('coBorRCAddress1', 'fileHMLOExperienceInfo');
$coBorRCOutcome1 = Strings::showField('coBorRCOutcome1', 'fileHMLOExperienceInfo');
$coBorRCAddress2 = Strings::showField('coBorRCAddress2', 'fileHMLOExperienceInfo');
$coBorRCOutcome2 = Strings::showField('coBorRCOutcome2', 'fileHMLOExperienceInfo');
$coBorRCAddress3 = Strings::showField('coBorRCAddress3', 'fileHMLOExperienceInfo');
$coBorRCOutcome3 = Strings::showField('coBorRCOutcome3', 'fileHMLOExperienceInfo');
$haveBorProfLicences = Strings::showField('haveBorProfLicences', 'fileHMLOExperienceInfo');
$borProfLicence = Strings::showField('borProfLicence', 'fileHMLOExperienceInfo');
$haveCoBorProfLicences = Strings::showField('haveCoBorProfLicences', 'fileHMLOExperienceInfo');
$coBorProfLicence = Strings::showField('coBorProfLicence', 'fileHMLOExperienceInfo');
$coBorLicenseNo = Strings::showField('coBorLicenseNo', 'fileHMLOExperienceInfo');
$fullTimeRealEstateInvestor = Strings::showField('fullTimeRealEstateInvestor', 'fileHMLOExperienceInfo');
$coFullTimeRealEstateInvestor = Strings::showField('coFullTimeRealEstateInvestor', 'fileHMLOExperienceInfo');

$haveBorSellPropertie = Strings::showField('haveBorSellPropertie', 'fileHMLOExperienceInfo');
$borNoOfProSellExperience = Strings::showField('borNoOfProSellExperience', 'fileHMLOExperienceInfo');
$borNoOfProSellCompleted = Strings::showField('borNoOfProSellCompleted', 'fileHMLOExperienceInfo');

$haveCoBorSellPropertie = Strings::showField('haveCoBorSellPropertie', 'fileHMLOExperienceInfo');
$coBorNoOfProSellExperience = Strings::showField('coBorNoOfProSellExperience', 'fileHMLOExperienceInfo');
$coBorNoOfProSellCompleted = Strings::showField('coBorNoOfProSellCompleted', 'fileHMLOExperienceInfo');

$coBorSellAddress1 = Strings::showField('coBorSellAddress1', 'fileHMLOExperienceInfo');
$coBorSellAddress2 = Strings::showField('coBorSellAddress2', 'fileHMLOExperienceInfo');
$coBorSellAddress3 = Strings::showField('coBorSellAddress3', 'fileHMLOExperienceInfo');

$coBorSellOutcome1 = Strings::showField('coBorSellOutcome1', 'fileHMLOExperienceInfo');
$coBorSellOutcome2 = Strings::showField('coBorSellOutcome2', 'fileHMLOExperienceInfo');
$coBorSellOutcome3 = Strings::showField('coBorSellOutcome3', 'fileHMLOExperienceInfo');

$amountOfFinancing = Strings::showField('amountOfFinancing', 'fileHMLOExperienceInfo');
$amountOfFinancingTo = Strings::showField('amountOfFinancingTo', 'fileHMLOExperienceInfo');
$typicalPurchasePrice = Strings::showField('typicalPurchasePrice', 'fileHMLOExperienceInfo');
$typicalPurchasePriceTo = Strings::showField('typicalPurchasePriceTo', 'fileHMLOExperienceInfo');
$typicalConstructionCosts = Strings::showField('typicalConstructionCosts', 'fileHMLOExperienceInfo');
$typicalConstructionCostsTo = Strings::showField('typicalConstructionCostsTo', 'fileHMLOExperienceInfo');
$typicalSalePrice = Strings::showField('typicalSalePrice', 'fileHMLOExperienceInfo');
$typicalSalePriceTo = Strings::showField('typicalSalePriceTo', 'fileHMLOExperienceInfo');

$constructionDrawsPerProject = Strings::showField('constructionDrawsPerProject', 'fileHMLOExperienceInfo');
$constructionDrawsPerProjectTo = Strings::showField('constructionDrawsPerProjectTo', 'fileHMLOExperienceInfo');
$monthsPurchaseDateToFirstConst = Strings::showField('monthsPurchaseDateToFirstConst', 'fileHMLOExperienceInfo');
$monthsPurchaseDateToFirstConstTo = Strings::showField('monthsPurchaseDateToFirstConstTo', 'fileHMLOExperienceInfo');
$monthsPurchaseDateUntilConst = Strings::showField('monthsPurchaseDateUntilConst', 'fileHMLOExperienceInfo');
$monthsPurchaseDateUntilConstTo = Strings::showField('monthsPurchaseDateUntilConstTo', 'fileHMLOExperienceInfo');
$monthsPurchaseDateToSaleDate = Strings::showField('monthsPurchaseDateToSaleDate', 'fileHMLOExperienceInfo');
$monthsPurchaseDateToSaleDateTo = Strings::showField('monthsPurchaseDateToSaleDateTo', 'fileHMLOExperienceInfo');
$NoOfSuchProjects = Strings::showField('NoOfSuchProjects', 'fileHMLOExperienceInfo');
$NoOfSuchProjectsTo = Strings::showField('NoOfSuchProjectsTo', 'fileHMLOExperienceInfo');
$borPrimaryInvestmentStrategy = Strings::showField('borPrimaryInvestmentStrategy', 'fileHMLOExperienceInfo');
$borPrimaryInvestmentStrategyExplain = Strings::showField('borPrimaryInvestmentStrategyExplain', 'fileHMLOExperienceInfo');

$coBorPrimaryInvestmentStrategy = Strings::showField('coBorPrimaryInvestmentStrategy', 'fileHMLOExperienceInfo');
$coBorPrimaryInvestmentStrategyExplain = Strings::showField('coBorPrimaryInvestmentStrategyExplain', 'fileHMLOExperienceInfo');
$borLicenseNo = Strings::showField('borLicenseNo', 'fileHMLOExperienceInfo');
$flipPropCompletedLifetime = Strings::showField('flipPropCompletedLifetime', 'fileHMLOExperienceInfo');
$groundPropCompletedLifetime = Strings::showField('groundPropCompletedLifetime', 'fileHMLOExperienceInfo');
$sellPropCompletedLifetime = Strings::showField('sellPropCompletedLifetime', 'fileHMLOExperienceInfo');
$overallRealEstateInvesExp = Strings::showField('overallRealEstateInvesExp', 'fileHMLOExperienceInfo');


if (Strings::showField('geographicAreas', 'fileHMLOExperienceInfo') != '') {
    $geographicAreas = explode(',', Strings::showField('geographicAreas', 'fileHMLOExperienceInfo'));
}

$borrowerCallBack = Strings::showField('borrowerCallBack', 'ResponseInfo');
$welcomeCallDate = Strings::showField('welcomeCallDate', 'ResponseInfo');
$receivedDate = Strings::showField('receivedDate', 'LMRInfo');
$purchaseCloseDate = Strings::showField('closingDate', 'QAInfo');
$hearingDate = Strings::showField('hearingDate', 'QAInfo');
if ($haveCoBorProfLicences == 'Yes') {
    $showcoBorProfLicenceDispOpt = 'display:block;';
}
if (Dates::IsEmpty($borrowerCallBack)) {
    $borrowerCallBack = '';
} else {
    $borrowerCallBack = Dates::formatDateWithRE($borrowerCallBack, 'YMD', 'm/d/Y');
}

if (Dates::IsEmpty($receivedDate)) {
    $receivedDate = '';
} else {
    $receivedDate = Dates::formatDateWithRE($receivedDate, 'YMD', 'm/d/Y');
}

if (Dates::IsEmpty($welcomeCallDate)) {
    $welcomeCallDate = '';
} else {
    $welcomeCallDate = Dates::formatDateWithRE($welcomeCallDate, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($purchaseCloseDate)) {
    $purchaseCloseDate = '';
} else {
    $purchaseCloseDate = Dates::formatDateWithRE($purchaseCloseDate, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($hearingDate)) {
    $hearingDate = '';
} else {
    $hearingDate = Dates::formatDateWithRE($hearingDate, 'YMD', 'm/d/Y');
}


if ($borPrimaryInvestmentStrategy != '') $borPriInvesStrategyArray = explode(',', $borPrimaryInvestmentStrategy);
if ($coBorPrimaryInvestmentStrategy != '') $coBorPriInvesStrategyArray = explode(',', $coBorPrimaryInvestmentStrategy);

if (in_array('Other', $borPriInvesStrategyArray)) {
    $borPriInvesStrategyDispOpt = 'display:block;';
}
if (in_array('Other', $coBorPriInvesStrategyArray)) {
    $coBorPriInvesStrategyDispOpt = 'display:block;';
}

if ($haveBorProfLicences == 'Yes') {
    $showProfLicencesOpt = 'display:block;';
}
$borDecalredBankruptExpln = Strings::showField('borDecalredBankruptExpln', 'fileHMLOBackGroundInfo');
$borOutstandingJudgementsExpln = Strings::showField('borOutstandingJudgementsExpln', 'fileHMLOBackGroundInfo');
$borActiveLawsuitsExpln = Strings::showField('borActiveLawsuitsExpln', 'fileHMLOBackGroundInfo');
$borPropertyTaxLiensExpln = Strings::showField('borPropertyTaxLiensExpln', 'fileHMLOBackGroundInfo');
$borObligatedInForeclosureExpln = Strings::showField('borObligatedInForeclosureExpln', 'fileHMLOBackGroundInfo');
$borDelinquentExpln = Strings::showField('borDelinquentExpln', 'fileHMLOBackGroundInfo');
$borOtherFraudRelatedCrimesExpln = Strings::showField('borOtherFraudRelatedCrimesExpln', 'fileHMLOBackGroundInfo');
$borBorrowedDownPaymentExpln = Strings::showField('borBorrowedDownPaymentExpln', 'fileHMLOBackGroundInfo');

$coBorDecalredBankruptExpln = Strings::showField('coBorDecalredBankruptExpln', 'fileHMLOBackGroundInfo');
$coBorOutstandingJudgementsExpln = Strings::showField('coBorOutstandingJudgementsExpln', 'fileHMLOBackGroundInfo');
$coBorActiveLawsuitsExpln = Strings::showField('coBorActiveLawsuitsExpln', 'fileHMLOBackGroundInfo');
$coBorPropertyTaxLiensExpln = Strings::showField('coBorPropertyTaxLiensExpln', 'fileHMLOBackGroundInfo');
$coBorObligatedInForeclosureExpln = Strings::showField('coBorObligatedInForeclosureExpln', 'fileHMLOBackGroundInfo');
$coBorDelinquentExpln = Strings::showField('coBorDelinquentExpln', 'fileHMLOBackGroundInfo');
$coBorOtherFraudRelatedCrimesExpln = Strings::showField('coBorOtherFraudRelatedCrimesExpln', 'fileHMLOBackGroundInfo');
$coBorBorrowedDownPaymentExpln = Strings::showField('coBorBorrowedDownPaymentExpln', 'fileHMLOBackGroundInfo');

$isAdditionalGuarantors = Strings::showField('isAdditionalGuarantors', 'fileHMLOPropertyInfo');

$isAdditionalGuarantorsDisp = 'display: none;';
if ($isAdditionalGuarantors == 'Yes') $isAdditionalGuarantorsDisp = 'display: block;';

if ($userRole == 'Client' && $LMRId == 0 && $isHMLO == 1 || $isEF == 1) { // Client Info For New Client Create

    if (count($PCClientBackgroundInfoArray) > 0) {
        $isBorUSCitizen = $PCClientBackgroundInfoArray['isBorUSCitizen'];
        $isBorDecalredBankruptPastYears = $PCClientBackgroundInfoArray['isBorDecalredBankruptPastYears'];
        $isAnyBorOutstandingJudgements = $PCClientBackgroundInfoArray['isAnyBorOutstandingJudgements'];
        $hasBorAnyActiveLawsuits = $PCClientBackgroundInfoArray['hasBorAnyActiveLawsuits'];
        $hasBorPropertyTaxLiens = $PCClientBackgroundInfoArray['hasBorPropertyTaxLiens'];
        $hasBorObligatedInForeclosure = $PCClientBackgroundInfoArray['hasBorObligatedInForeclosure'];
        $isBorPresenltyDelinquent = $PCClientBackgroundInfoArray['isBorPresenltyDelinquent'];
        $isBorBorrowedDownPayment = $PCClientBackgroundInfoArray['isBorBorrowedDownPayment'];
        $isBorIntendToOccupyPropAsPRI = $PCClientBackgroundInfoArray['isBorIntendToOccupyPropAsPRI'];
        $haveBorOtherFraudRelatedCrimes = $PCClientBackgroundInfoArray['haveBorOtherFraudRelatedCrimes'];
        $isBorPersonallyGuaranteeLoan = $PCClientBackgroundInfoArray['isBorPersonallyGuaranteeLoan'];
        $borBackgroundExplanation = $PCClientBackgroundInfoArray['borBackgroundExplanation'];

        $borDecalredBankruptExpln = $PCClientBackgroundInfoArray['borDecalredBankruptExpln'];
        $borOutstandingJudgementsExpln = $PCClientBackgroundInfoArray['borOutstandingJudgementsExpln'];
        $borActiveLawsuitsExpln = $PCClientBackgroundInfoArray['borActiveLawsuitsExpln'];
        $borPropertyTaxLiensExpln = $PCClientBackgroundInfoArray['borPropertyTaxLiensExpln'];
        $borObligatedInForeclosureExpln = $PCClientBackgroundInfoArray['borObligatedInForeclosureExpln'];
        $borDelinquentExpln = $PCClientBackgroundInfoArray['borDelinquentExpln'];
        $borOtherFraudRelatedCrimesExpln = $PCClientBackgroundInfoArray['borOtherFraudRelatedCrimesExpln'];
        $borBorrowedDownPaymentExpln = $PCClientBackgroundInfoArray['borBorrowedDownPaymentExpln'];
        $borOrigin = $PCClientBackgroundInfoArray['borOrigin'];
        $borVisaStatus = $PCClientBackgroundInfoArray['borVisaStatus'];
    }

    if (count($PCClientExperienceInfoArray) > 0) {
        $haveBorREInvestmentExperience = $PCClientExperienceInfoArray['haveBorREInvestmentExperience'];
        $borNoOfREPropertiesCompleted = $PCClientExperienceInfoArray['borNoOfREPropertiesCompleted'];
        $borNoOfFlippingExperience = $PCClientExperienceInfoArray['borNoOfFlippingExperience'];
        $haveBorRehabConstructionExperience = $PCClientExperienceInfoArray['haveBorRehabConstructionExperience'];
        $borNoOfYearRehabExperience = $PCClientExperienceInfoArray['borNoOfYearRehabExperience'];
        $borRehabPropCompleted = $PCClientExperienceInfoArray['borRehabPropCompleted'];
        $haveBorProjectCurrentlyInProgress = $PCClientExperienceInfoArray['haveBorProjectCurrentlyInProgress'];
        $borNoOfProjectCurrently = $PCClientExperienceInfoArray['borNoOfProjectCurrently'];
        $haveBorOwnInvestmentProperties = $PCClientExperienceInfoArray['haveBorOwnInvestmentProperties'];
        $borNoOfOwnProp = $PCClientExperienceInfoArray['borNoOfOwnProp'];
        $areBorMemberOfInvestmentClub = $PCClientExperienceInfoArray['areBorMemberOfInvestmentClub'];
        $borClubName = $PCClientExperienceInfoArray['borClubName'];

        $borREAddress1 = $PCClientExperienceInfoArray['borREAddress1'];
        $borOutcomeRE1 = $PCClientExperienceInfoArray['borOutcomeRE1'];
        $borREAddress2 = $PCClientExperienceInfoArray['borREAddress2'];
        $borOutcomeRE2 = $PCClientExperienceInfoArray['borOutcomeRE2'];
        $borREAddress3 = $PCClientExperienceInfoArray['borREAddress3'];
        $borOutcomeRE3 = $PCClientExperienceInfoArray['borOutcomeRE3'];

        $borRCAddress1 = $PCClientExperienceInfoArray['borRCAddress1'];
        $borRCOutcome1 = $PCClientExperienceInfoArray['borRCOutcome1'];
        $borRCAddress2 = $PCClientExperienceInfoArray['borRCAddress2'];
        $borRCOutcome2 = $PCClientExperienceInfoArray['borRCOutcome2'];
        $borRCAddress3 = $PCClientExperienceInfoArray['borRCAddress3'];
        $borRCOutcome3 = $PCClientExperienceInfoArray['borRCOutcome3'];
    }
}
if ($haveBorREInvestmentExperience == 'Yes') {
    $showREInvestmentDispOpt = 'display:block;';
}

if ($haveBorSellPropertie == 'Yes') {
    $haveBorSellPropertieDisp = 'display:block;';
}
if ($haveBorRehabConstructionExperience == 'Yes') {
    $showRCDispOpt = 'display:block;';
}

if ($haveBorProjectCurrentlyInProgress == 'Yes') {
    $showProjectInProgressDispOpt = 'display:block;';
}

if ($haveBorOwnInvestmentProperties == 'Yes') {
    $showOwnInvestmentDispOpt = 'display:block;';
}

if ($areBorMemberOfInvestmentClub == 'Yes') {
    $showMemberDispOpt = 'display:block;';
}

if ($isBorDecalredBankruptPastYears == 'Yes') {
    $borDecalredBankruptDispOpt = 'display: table-row;';
}
if ($isAnyBorOutstandingJudgements == 'Yes') {
    $borOutstandingJudgementsDispOpt = 'display: table-row;';
}
if ($hasBorAnyActiveLawsuits == 'Yes') {
    $borActiveLawsuitsDispOpt = 'display: table-row;';
}
if ($hasBorPropertyTaxLiens == 'Yes') {
    $borPropertyTaxLiensDispOpt = 'display: table-row;';
}
if ($hasBorObligatedInForeclosure == 'Yes') {
    $borObligatedInForeclosureDispOpt = 'display: table-row;';
}
if ($isBorPresenltyDelinquent == 'Yes') {
    $borDelinquentDispOpt = 'display: table-row;';
}
if ($haveBorOtherFraudRelatedCrimes == 'Yes') {
    $borOtherFraudRelatedCrimesDispOpt = 'display: table-row;';
}
if ($isBorBorrowedDownPayment == 'Yes') {
    $borBorrowedDownPaymentDispOpt = 'display: table-row;';
}

if ($isBorUSCitizen == 'No') {
    $borOriginAndVisaDispOpt = 'display: table-row;';
}

if ($isCoBorDecalredBankruptPastYears == 'Yes') {
    $coBorDecalredBankruptDispOpt = 'display: table-row;';
}
if ($isAnyCoBorOutstandingJudgements == 'Yes') {
    $coBorOutstandingJudgementsDispOpt = 'display: table-row;';
}
if ($hasCoBorAnyActiveLawsuits == 'Yes') {
    $coBorActiveLawsuitsDispOpt = 'display: table-row;';
}
if ($hasCoBorPropertyTaxLiens == 'Yes') {
    $coBorPropertyTaxLiensDispOpt = 'display: table-row;';
}
if ($hasCoBorObligatedInForeclosure == 'Yes') {
    $coBorObligatedInForeclosureDispOpt = 'display: table-row;';
}
if ($isCoBorPresenltyDelinquent == 'Yes') {
    $coBorDelinquentDispOpt = 'display: table-row;';
}
if ($haveCoBorOtherFraudRelatedCrimes == 'Yes') {
    $coBorOtherFraudRelatedCrimesDispOpt = 'display: table-row;';
}
if ($isCoBorBorrowedDownPayment == 'Yes') {
    $coBorBorrowedDownPaymentDispOpt = 'display: table-row;';
}

if ($haveCoBorREInvestmentExperience == 'Yes') {
    $showCoBorREInvestmentDispOpt = 'display:block;';
}

if ($haveCoBorRehabConstructionExperience == 'Yes') {
    $showcoBorRCDispOpt = 'display:block;';
}

if ($haveCoBorProjectCurrentlyInProgress == 'Yes') {
    $showcoBorProjectInProgressDispOpt = 'display:block;';
}

if ($haveCoBorOwnInvestmentProperties == 'Yes') {
    $showcoBorOwnInvestmentDispOpt = 'display:block;';
}

if ($areCoBorMemberOfInvestmentClub == 'Yes') {
    $showcoBorMemberDispOpt = 'display:block;';
}
for ($m = 1; $m <= 10; $m++) {
    if (${'member' . $m . 'Name'} != '' || ${'member' . $m . 'Title'} != '' || ${'member' . $m . 'Ownership'} > 0) {
        ${'showMember' . $m . 'DispOpt'} = 'display: table-row';
        $memberCnt++;
    }
}

for ($j = 1; $j <= 10; $j++) {
    ${'member' . $j . 'Name'} = Strings::showField('member' . $j . 'Name', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Title'} = Strings::showField('member' . $j . 'Title', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Ownership'} = Strings::showField('member' . $j . 'Ownership', 'fileHMLOEntityInfo');
    ${'member' . $j . 'AnnualSalary'} = Strings::showField('member' . $j . 'AnnualSalary', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Address'} = Strings::showField('member' . $j . 'Address', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Phone'} = Strings::showField('member' . $j . 'Phone', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Cell'} = Strings::showField('member' . $j . 'Cell', 'fileHMLOEntityInfo');
    ${'member' . $j . 'SSN'} = Strings::showField('member' . $j . 'SSN', 'fileHMLOEntityInfo');
    ${'member' . $j . 'DOB'} = Strings::showField('member' . $j . 'DOB', 'fileHMLOEntityInfo');
    ${'member' . $j . 'Email'} = Strings::showField('member' . $j . 'Email', 'fileHMLOEntityInfo');
    ${'member' . $j . 'CreditScore'} = Strings::showField('member' . $j . 'CreditScore', 'fileHMLOEntityInfo');

    if (${'member' . $j . 'Name'} != '' || ${'member' . $j . 'Title'} != '' || ${'member' . $j . 'Ownership'} > 0 || $j == 1) {
        ${'showMember' . $j . 'DispOpt'} = 'display : table-row';
    } else {
        ${'showMember' . $j . 'DispOpt'} = 'display:none';
    }
}

$showBorrowerEntityTBLOpt = 'display: none';
if ($borrowerType == glBorrowerType::BORROWER_TYPE_ENTITY) {
    $showBorrowerEntityDispOpt = 'display: contents';
    $showBorrowerEntityTBLOpt = 'display: table';
}
/*story - 27792 populate the parent question for entity field*/
if ($PCID != 4495) {
    //$showBorrowerEntityDispOpt = 'display: contents;';
}

if ($borrowerUnderEntity == 'Yes' && $sameAsEntityAddr != 1) {
    $showBillEntityDispOpt = 'display: table-row';
} else {
    $showBillEntityDispOpt = 'display: none';
}

if (trim($nonBorrowerSSNNumber) != '') $nonBorrowerSSNArray = Strings::splitSSNNumber($nonBorrowerSSNNumber);
if (count($FUInfo) > 0) {
    $haveEmployed = Strings::showField('haveEmployed', 'FUInfo');
    $havePaystubW2ITReturn = Strings::showField('havePaystubW2ITReturn', 'FUInfo');
    $isFMInUSMilitary = Strings::showField('isFMInUSMilitary', 'FUInfo');
    $ownBusiness = Strings::showField('ownBusiness', 'FUInfo');
    $creditIssues = Strings::showField('creditIssues', 'FUInfo');
    $fundsNeededReason = Strings::showField('fundsNeededReason', 'FUInfo');
    $FUPropType = Strings::showField('propType', 'FUInfo');
    $amtDesired = Strings::showField('amtDesired', 'FUInfo');
    if (trim($creditIssues) != '') {
        $creditIssuesArray = explode(',', $creditIssues);
    }
    if (trim($fundsNeededReason) != '') {
        $fundsNeededReasonArray = explode(',', $fundsNeededReason);
    }
}
/*
        if ($borrowerTimeZone == '') {
            $borrowerTimeZone = recursive_array_search($mailingState, $stateTimeZone);
        }
        if ($coBorrowerTimeZone == '') {
            $coBorrowerTimeZone = recursive_array_search($coBorrowerMailingState, $stateTimeZone);
        }
*/
$HOPhone = Strings::showField('HOPhone', 'listingRealtorInfo');
$HOFax = Strings::showField('HOFax', 'listingRealtorInfo');
$HOPhNoArray = Strings::splitPhoneNumber($HOPhone);
if (count($HOPhNoArray) > 0) {
    $HOPhNo1 = $HOPhNoArray['No1'];
    $HOPhNo2 = $HOPhNoArray['No2'];
    $HOPhNo3 = $HOPhNoArray['No3'];
    $HOPhExt = substr($HOPhone, 10, 5);
}
$HOFaxNo1 = '';
$HOFaxNo2 = '';
$HOFaxNo3 = '';
$HOFaxArray = Strings::splitPhoneNumber($HOFax);
if (count($HOFaxArray) > 0) {
    $HOFaxNo1 = $HOFaxArray['No1'];
    $HOFaxNo2 = $HOFaxArray['No2'];
    $HOFaxNo3 = $HOFaxArray['No3'];
}
if (count($nonBorrowerSSNArray) > 0) {
    $nonBorrrowerSSN1 = trim($nonBorrowerSSNArray['No1']);
    $nonBorrrowerSSN2 = trim($nonBorrowerSSNArray['No2']);
    $nonBorrrowerSSN3 = trim($nonBorrowerSSNArray['No3']);
}
$tempNonborrowerSSN = $nonBorrrowerSSN1 . $nonBorrrowerSSN2 . $nonBorrrowerSSN3;

$altPhoneNumberArray = Strings::splitPhoneNumber($altPhoneNumber);
if (count($altPhoneNumberArray) > 0) {
    $altPhNo1 = trim($altPhoneNumberArray['No1']);
    $altPhNo2 = trim($altPhoneNumberArray['No2']);
    $altPhNo3 = trim($altPhoneNumberArray['No3']);
    $altExt = trim($altPhoneNumberArray['Ext']);
}
$workNumberArray = Strings::splitPhoneNumber($workNumber);
if (count($workNumberArray) > 0) {
    $workNo1 = trim($workNumberArray['No1']);
    $workNo2 = trim($workNumberArray['No2']);
    $workNo3 = trim($workNumberArray['No3']);
    $workNoExt = trim($workNumberArray['Ext']);
}
$coBPhoneNumberArray = Strings::splitPhoneNumber($coBPhoneNumber);
if (count($coBPhoneNumberArray) > 0) {
    $coBPhNo1 = trim($coBPhoneNumberArray['No1']);
    $coBPhNo2 = trim($coBPhoneNumberArray['No2']);
    $coBPhNo3 = trim($coBPhoneNumberArray['No3']);
    $coBExt = trim($coBPhoneNumberArray['Ext']);
}
$coBAltPhoneNumberArray = Strings::splitPhoneNumber($coBAltPhoneNumber);
if (count($coBAltPhoneNumberArray) > 0) {
    $coBAltPhNo1 = trim($coBAltPhoneNumberArray['No1']);
    $coBAltPhNo2 = trim($coBAltPhoneNumberArray['No2']);
    $coBAltPhNo3 = trim($coBAltPhoneNumberArray['No3']);
    $coBAltExt = trim($coBAltPhoneNumberArray['Ext']);
}
$coBCellNumberArray = Strings::splitPhoneNumber($coBCellNumber);
if (count($coBCellNumberArray) > 0) {
    $coBCellNo1 = trim($coBCellNumberArray['No1']);
    $coBCellNo2 = trim($coBCellNumberArray['No2']);
    $coBCellNo3 = trim($coBCellNumberArray['No3']);
}
$coBFaxArray = Strings::splitPhoneNumber($coBFax);
if (count($coBFaxArray) > 0) {
    $coBFax1 = trim($coBFaxArray['No1']);
    $coBFax2 = trim($coBFaxArray['No2']);
    $coBFax3 = trim($coBFaxArray['No3']);
}
$coBWorkNumberArray = Strings::splitPhoneNumber($coBorrowerWorkNumber);
if (count($coBWorkNumberArray) > 0) {
    $coBWorkNo1 = trim($coBWorkNumberArray['No1']);
    $coBWorkNo2 = trim($coBWorkNumberArray['No2']);
    $coBWorkNo3 = trim($coBWorkNumberArray['No3']);
    $coBWorkNoExt = trim($coBWorkNumberArray['Ext']);
}

$coBSsnNumberArray = Strings::splitSSNNumber($coBSsnNumber);
if (count($coBSsnNumberArray) > 0) {
    $coBSsn1 = trim($coBSsnNumberArray['No1']);
    $coBSsn2 = trim($coBSsnNumberArray['No2']);
    $coBSsn3 = trim($coBSsnNumberArray['No3']);
}

$inDBArray['lastPaymentMade'] = Strings::showField('lien1LPMade', 'LMRInfo');
$noOfDaysBehind1 = Integers::calculateNoOfDaysBehind($inDBArray);
/** No of days behind calculation **/
$inDBArray['lastPaymentMade'] = Strings::showField('lien2LPMade', 'LMRInfo');
$noOfDaysBehind2 = Integers::calculateNoOfDaysBehind($inDBArray);
/** No of days behind calculation **/
if ($noOfDaysBehind1 <= 0) {
    $noOfDaysBehind1 = '';
}
if ($noOfDaysBehind2 <= 0) {
    $noOfDaysBehind2 = '';
}
if (Dates::IsEmpty($coBorrowerDOB)) {
    $coBorrowerDOB = '';
} else {
    $coBorrowerDOB = Dates::formatDateWithRE($coBorrowerDOB, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($nonBorrowerDOB)) {
    $nonBorrowerDOB = '';
} else {
    $nonBorrowerDOB = Dates::formatDateWithRE($nonBorrowerDOB, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($lien1LPMade)) {
    $lien1LPMade = '';
} else {
    $lien1LPMade = Dates::formatDateWithRE($lien1LPMade, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($lien2LPMade)) {
    $lien2LPMade = '';
} else {
    $lien2LPMade = Dates::formatDateWithRE($lien2LPMade, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($noteDate)) {
    $noteDate = '';
} else {
    $noteDate = Dates::formatDateWithRE($noteDate, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($dateOfFormation)) {
    $dateOfFormation = '';
} else {
    $dateOfFormation = Dates::formatDateWithRE($dateOfFormation, 'YMD', 'm/d/Y');
}
if (Dates::IsEmpty($dateOfOperatingAgreement)) {
    $dateOfOperatingAgreement = '';
} else {
    $dateOfOperatingAgreement = Dates::formatDateWithRE($dateOfOperatingAgreement, 'YMD', 'm/d/Y');
}

if (Dates::IsEmpty($noticeAccelerationDate)) {
    $noticeAccelerationDate = '';
} else {
    $noticeAccelerationDate = Dates::formatDateWithRE($noticeAccelerationDate, 'YMD', 'm/d/Y');
}
$addBranchHearAbout = Strings::showField('addBranchHearAbout', 'BranchInfo');
$lien1Payment = Strings::showField('lien1Payment', 'LMRInfo');
$taxes1 = Strings::showField('taxes1', 'incomeInfo');
$insurance1 = Strings::showField('insurance1', 'incomeInfo');
$mortgageInsurance1 = Strings::showField('mortgageInsurance1', 'incomeInfo');
$HOAFees1 = Strings::showField('HOAFees1', 'incomeInfo');
$floodInsurance1 = Strings::showField('floodInsurance1', 'incomeInfo');
$totalPayment = Strings::replaceCommaValues($lien1Payment)
    + Strings::replaceCommaValues($taxes1)
    + Strings::replaceCommaValues($insurance1)
    + Strings::replaceCommaValues($mortgageInsurance1)
    + Strings::replaceCommaValues($HOAFees1)
    + Strings::replaceCommaValues($floodInsurance1);

if (count($clientFileListArray) > 0 && $userRole == 'Client' && $LMRId == 0 && $isHMLO == 1 && $isEF == 1) { // Client Info For New Client Create
    $borrowerFName = $clientFileListArray['borrowerName'];
    $borrowerLName = $clientFileListArray['borrowerLName'];
    $phoneNumber = $clientFileListArray['phoneNumber'];
    $cellNumber = $clientFileListArray['cellNumber'];
    $fax = $clientFileListArray['fax'];
    $maritalStatus = $clientFileListArray['maritalStatus'];
    $marriageDate = $clientFileListArray['marriageDate'];
    $divorceDate = $clientFileListArray['divorceDate'];
    $maidenName = $clientFileListArray['maidenName'];
    $spouseName = $clientFileListArray['spouseName'];
    $borrowerDOB = $clientFileListArray['borrowerDOB'];
    $borrowerPOB = $clientFileListArray['borrowerPOB'];
    $ssnNumber = $clientFileListArray['ssnNumber'];
    $serviceProvider = $clientFileListArray['serviceProvider'];
    $borCreditScoreRange = $clientFileListArray['borCreditScoreRange'];
    $midFicoScore = $clientFileListArray['midFicoScore'];
    $borExperianScore = $clientFileListArray['borExperianScore'];
    $borEquifaxScore = $clientFileListArray['borEquifaxScore'];
    $borTransunionScore = $clientFileListArray['borTransunionScore'];
    $borrowerDOB = Dates::formatDateWithRE($borrowerDOB, 'YMD', 'm/d/Y');
}

$phoneNumberArray = Strings::splitPhoneNumber($phoneNumber);
if (count($phoneNumberArray) > 0) {
    $phNo1 = trim($phoneNumberArray['No1']);
    $phNo2 = trim($phoneNumberArray['No2']);
    $phNo3 = trim($phoneNumberArray['No3']);
    $ext = trim($phoneNumberArray['Ext']);
}

$cellNumberArray = Strings::splitPhoneNumber($cellNumber);
if (count($cellNumberArray) > 0) {
    $cellNo1 = trim($cellNumberArray['No1']);
    $cellNo2 = trim($cellNumberArray['No2']);
    $cellNo3 = trim($cellNumberArray['No3']);
}

$marriageDate = Dates::formatDateWithRE($marriageDate, 'YMD', 'm/d/Y');
$divorceDate = Dates::formatDateWithRE($divorceDate, 'YMD', 'm/d/Y');

$ssnNumberArray = Strings::splitSSNNumber($ssnNumber);
if (count($ssnNumberArray) > 0) {
    $ssn1 = trim($ssnNumberArray['No1']);
    $ssn2 = trim($ssnNumberArray['No2']);
    $ssn3 = trim($ssnNumberArray['No3']);
}

$faxArray = Strings::splitPhoneNumber($fax);
if (count($faxArray) > 0) {
    $fax1 = trim($faxArray['No1']);
    $fax2 = trim($faxArray['No2']);
    $fax3 = trim($faxArray['No3']);
}

if ($memberCnt == 0) $showMember1DispOpt = 'display: table-row';

if (trim($mailingAddrAsPresent) == '') $mailingAddrAsPresent = 0;
if (trim($coBorMailingAddrAsPresent) == '') $coBorMailingAddrAsPresent = 0;
//same as borrower address
if ($coBorMailingAddrAsPresent == 1) {
    $coBPresentAddress = $presentAddress;
    $coBPresentCity = $presentCity;
    $coBPresentState = $presentState;
    $coBPresentZip = $presentZip;
}
if ($isCoBorrower == '1') {
    $coBorDisp = 'display: block;';
} else {
    $coBorDisp = 'display: none;';
}
if ($isNonBorrower == '') $isNonBorrower = 0;
if (trim($servicer1) == '' && $allowToEdit) $servicer1 = $servicer1Text;
if (trim($originalLender1) == '' && $allowToEdit) $originalLender1 = $originalLender1Text;
if (trim($servicer2) == '' && $allowToEdit) $servicer2 = $servicer2Text;
if (trim($originalLender2) == '' && $allowToEdit) $originalLender2 = $originalLender2Text;
if ($publicUser == 1 || $isHMLO == 1 || $isEF == 1) {
    $branchHAInfoArray = [];
    if ($executiveId > 0) {
        $inArray['LMRAEID'] = $executiveId;
        $branchHearAboutInfoArray = getBranchHearAbout::getReport($inArray);
        if (count($branchHearAboutInfoArray) > 0) {
            if (array_key_exists($executiveId, $branchHearAboutInfoArray)) {
                $branchHAInfoArray = $branchHearAboutInfoArray[$executiveId];
            }
        }
    }
}

if (count($FileProInfo) > 0) {
    $LBContactName = Property::$primaryPropertyInfo->getTblPropertiesAccess_by_propertyId()->propertyAccessName;
    $LBContactPhone = Strings::formatPhoneNumber(Property::$primaryPropertyInfo->getTblPropertiesAccess_by_propertyId()->propertyAccessPhone);
}

if ($remainingMonths <= 0) {
    /** Calculate Remaining months **/
    try {
        $remainingMonths = Dates::calculateRemainingMonths(
            ['loanOriginationDate' => Strings::showField('noteDate', 'RESTInfo')
                , 'terms'          => $lien1Terms]
        );
    } catch (Exception $e) {
    }
}
$propMgmntContactID = '';
$tempPropMgmntContactName = '';
$propMgmntContactPerson = '';
$propMgmntCompany = '';
$propMgmntNotes = '';
$propMgmntPhone = '';
$propMgmntContactEmail = '';
$propMgmntAddress = '';
$propMgmntCity = '';
$propMgmntState = '';
$propMgmntZip = '';
$HOA2Contacts = [];
$HOA2ContactID = 0;
$HOA2ContactName = '';
$HOA2CompanyName = '';
if (array_key_exists('HOA2', $fileContacts)) {
    $HOA2Contacts = $fileContacts['HOA2'];
}
if (count($HOA2Contacts) > 0) {
    $HOA2ContactID = trim($HOA2Contacts['CID']);
    $HOA2ContactName = stripslashes(trim($HOA2Contacts['contactName']));
    $HOA2CompanyName = trim($HOA2Contacts['companyName']);
}
if (array_key_exists('PM', $fileContacts)) {
    $propertyManagementInfo = $fileContacts['PM'];
}
if (count($propertyManagementInfo) > 0) {
    $propMgmntContactID = trim($propertyManagementInfo['CID']);
    $propMgmntContactPerson = $tempPropMgmntContactName = trim($propertyManagementInfo['contactName']);
    $propMgmntCompany = trim($propertyManagementInfo['companyName']);
    $propMgmntContactEmail = trim($propertyManagementInfo['email']);
    $propMgmntAddress = trim($propertyManagementInfo['address']);
    $propMgmntCity = trim($propertyManagementInfo['city']);
    $propMgmntState = trim($propertyManagementInfo['state']);
    $propMgmntZip = trim($propertyManagementInfo['zip']);
    $propMgmntNotes = trim($propertyManagementInfo['description']);
    $propMgmntPhone = trim($propertyManagementInfo['phone']);
}
$propMgmntPhoneArray = [];
$propMgmntPhNo1 = '';
$propMgmntPhNo2 = '';
$propMgmntPhNo3 = '';
$propMgmntPhExt = '';
$propMgmntPhoneArray = Strings::splitPhoneNumber($propMgmntPhone);
if (count($propMgmntPhoneArray) > 0) {
    $propMgmntPhNo1 = $propMgmntPhoneArray['No1'];
    $propMgmntPhNo2 = $propMgmntPhoneArray['No2'];
    $propMgmntPhNo3 = $propMgmntPhoneArray['No3'];
    $propMgmntPhExt = $propMgmntPhoneArray['Ext'];
}
$tabIndexNo = 1;
/* Loan origination */
if (count($QAInfo) > 0) {
    $PublishBInfo = Strings::showField('PublishBInfo', 'QAInfo');
    $BEthnicity = Strings::showField('BEthnicity', 'QAInfo');
    $BRace = Strings::showField('BRace', 'QAInfo');
    $BGender = Strings::showField('BGender', 'QAInfo');
    $PublishCBInfo = Strings::showField('PublishCBInfo', 'QAInfo');
    $CBEthnicity = Strings::showField('CBEthnicity', 'QAInfo');
    $CBRace = Strings::showField('CBRace', 'QAInfo');
    $CBGender = Strings::showField('CBGender', 'QAInfo');
    $CBVeteran = Strings::showField('CBVeteran', 'QAInfo');
    $CBFiEthnicity = Strings::showField('CBFiEthnicity', 'QAInfo');
    $CBFiGender = Strings::showField('CBFiGender', 'QAInfo');
    $CBFiRace = Strings::showField('CBFiRace', 'QAInfo');
    $CBDDemoInfo = Strings::showField('CBDDemoInfo', 'QAInfo');
    $noOfPeopleDependent = Strings::showField('noOfPeopleDependent', 'QAInfo');
}
if ($PublishCBInfo != 2) {
    $CBEthnicity = '';
    $CBRace = '';
    $CBGender = '';
    $CBVeteran = '';
    $CBFiEthnicity = '';
    $CBFiGender = '';
    $CBFiRace = '';
    $CBDDemoInfo = '';
}
if ($PublishBInfo != 2) {
    $BEthnicity = '';
    $BRace = '';
    $BGender = '';
}
/* Loan Origination */
$showLimitedMandatoryField = 0; /* This Field used for PC = Mitigation Resolve,LLC Sep 23, 2015 */
if (($publicUser == 1) && ($PCID == 609)) { /*  Mitigation Resolve,LLC = 609 Sep 23, 2015 */
    $showLimitedMandatoryField = 1;
}

$LODispOpt = '';
$LOSectionDispOpt = $LOSectionDivDispOpt = 'display: none;';
$LOAndHMLOSectionDivDispOpt = 'display: none;';
$LOBorResidedDispOpt = $LOCoBorResidedDispOpt = 'display: block;';
if ($isLO == 1 && $publicUser != 1) {
    /** Loan Origination **/
    $LODispOpt = 'display: none;';
    $LOSectionDivDispOpt = 'display: block;';
    $LOSectionDispOpt = 'display: table-row;';
    $LOAndHMLOSectionDivDispOpt = 'display: block;';
    $LOBorResidedDispOpt = $LOCoBorResidedDispOpt = 'display: none;';
    if ($borResidedPresentAddr == 'Yes') {
        $LOBorResidedDispOpt = 'display: block;';
    }
    if ($coBResidedPresentAddr == 'Yes') {
        $LOCoBorResidedDispOpt = 'display: block;';
    }
}
$MFDispOpt = '';
$MFSectionDispOpt = $MFSectionDivDispOpt = 'display: none;';
if ($isMF == 1 && $publicUser != 1) {
    /** Merchant Funding **/
    $MFDispOpt = 'display: none;';
    $MFSectionDivDispOpt = 'display: block;';
    $MFSectionDispOpt = 'display: table-row;';
}
$FUDispOpt = '';
$FUSectionDispOpt = $FUSectionDivDispOpt = $FUSectionSubEmpDivDispOpt = $FUSectionSubOwnBusinessDivDispOpt = $FUSectionSubNotesDivDispOpt = 'display: none;';
if ($isFU == 1 && $publicUser != 1) {
    /** Funding **/
    $FUDispOpt = 'display: none;';
    $FUSectionDivDispOpt = 'display: block;';
    $FUSectionDispOpt = 'display: table-row;';

    if ($haveEmployed == 'Yes') {
        $FUSectionSubEmpDivDispOpt = 'display: table-cell;';
    }
    if ($ownBusiness == 'Yes') {
        $FUSectionSubOwnBusinessDivDispOpt = 'display: table-row;';
    }
    if (count($fundsNeededReasonArray) > 0) {
        if (in_array('Other', $fundsNeededReasonArray)) {
            $FUSectionSubNotesDivDispOpt = 'display: table-row;';
        }
    }
}

/**
 ** Description    : Hard / Private Money LOS module Section Show and Hide
 ** Developer    : Viji & Venkatesh
 ** Author        : Awatasoftsys
 ** Date            : Nov 18, 2016
 **/
$HMLODispOpt = '';
$HMLOSectionDispOpt = $HMLOSectionDivDispOpt = 'display: none;';
$EFEXPDisp = '';
$HMLOBorResidedDispOpt = $HMLOCoBorResidedDispOpt = 'display: block;';
$EFDispOptNew = 'display: block;';
$hideLOC = 'display :block;';

if (($isHMLO == 1 || $isEF == 1) && $publicUser != 1) {
    /** Hard / Private Money LOS **/
    $HMLODispOpt = 'display: none;';
    $HMLOSectionDivDispOpt = 'display: block;';
    $HMLOSectionDispOpt = 'display: table-row;';
    $HMLOBorResidedDispOpt = $HMLOCoBorResidedDispOpt = 'display: none;';
    $LOAndHMLOSectionDivDispOpt = 'display: block;';
    if ($borResidedPresentAddr == 'Yes') {
        $HMLOBorResidedDispOpt = 'display: block;';
    }
    if ($coBResidedPresentAddr == 'Yes') {
        $HMLOCoBorResidedDispOpt = 'display: block;';
    }
}
if ($isEF == 1) {
    $EFDispOptNew = 'display: none;';
    $EFEXPDisp = 'display :none;';
}

if ($isLOC == 1) $hideLOC = 'display :none;'; //https://www.pivotaltracker.com/story/show/159946019

$EFDispOpt = '';
$EFSectionDispOpt = $EFSectionDivDispOpt = '';
$EFBorResidedDispOpt = $EFCoBorResidedDispOpt = 'display: block;';

$propDetailsProcess = Strings::showField('propDetailsProcess', 'fileHMLOPropertyInfo');


$secArr = Arrays::getValueFromArray('Admin', $fieldsInfo);
$borrowerActiveSectionDisp = '';
if ($publicUser == 1 && $REBroker == 'Yes' && $propDetailsProcess == 'Looking for General Info' || $propDetailsProcess == 'Actively Looking For Property' && trim(BaseHTML::fieldAccess(['fNm' => 'propDetailsProcess', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') {
    $borrowerActiveSectionDisp = 'display: none';
}

if ($publicUser == 1 && $LMRId == 0 && trim(BaseHTML::fieldAccess(['fNm' => 'propDetailsProcess', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') {
    $borrowerActiveSectionDisp = 'display: none';
}
if ($borrowerActiveSectionDisp == '') $borrowerActiveSectionDisp = $HMLOLoanInfoSectionsDisp;

/**
 * Get Borrower Flipping Experience and Ground Up Construction Experience
 */
if (array_key_exists('Flip', $fileExpFilpGroundUp)) $clientExpProInfo = $fileExpFilpGroundUp['Flip'];
if (array_key_exists('Gup', $fileExpFilpGroundUp)) $clientGUExpInfo = $fileExpFilpGroundUp['Gup'];
if (array_key_exists('Sell', $fileExpFilpGroundUp)) $clientSellExpInfo = $fileExpFilpGroundUp['Sell'];

$previousState = Strings::showField('previousState', 'LMRInfo');
$PCClientEntityInfoArray = getPCClientEntityInfo::getReport(['PCID' => $PCID, 'CID' => $selClientId]);

if (in_array($PCID, $glFirstRehabLending)) {
    unset($glHMLOPresentOccupancy[1]);
    unset($glHMLOPresentOccupancy[2]);
    unset($glHMLOPresentOccupancy[3]);
    $glHMLOPresentOccupancy = array_values($glHMLOPresentOccupancy);
}
?>
<input type="hidden" name="SID" value="<?php echo $SID; ?>">
<input type="hidden" name="typeText" value="<?php echo $servicer1Text; ?>">
<input type="hidden" name="isLOOpt" value="<?php echo $isLO; ?>">


<?php
if ($isHOALien == 1 && $publicUser != 1) {
    ?>
    <input type="hidden" name="borrowerEmail" id="borrowerEmail"
           value="<?php echo Strings::showField('borrowerEmail', 'LMRInfo'); ?>">
    <?php
}

if ($publicUser != 1) { // Not allow this section for public users
    $tabIndex = $tabIndexNo;
    require 'fileAdminInfo.php'; // | File Admin Section..
} else {
    ?>
    <input type="hidden" name="agentId" id="agentId" value="<?php echo cypher::myEncryption($agentNumber); ?>">
    <input type="hidden" name="selectedPC" id="selectedPC" value="<?php echo $PCID; ?>">

    <div class="form-group col-md-6">
        <label class="col-md-6" for="fileModule">File Type<br/>
            <div class="note">Type name in the box to select File Type(s).</div>
        </label>
        <div class="col-md-6">
            <?php
            if ($allowToEdit) {
                ?>
                <div class="left" id="module_container">
                    <select class="mandatory chzn-select" data-placeholder="" name="fileModule[]" id="fileModule"
                            tabindex="<?php echo $tabIndexNo++; ?>"
                            onchange="getServiceTypes('loanModForm'); showOrHideProperServiceDiv(this.value); formControl.clearLoanProgramFields('LMRClientType',this.value); formControl.controlFormFields(this.id, '','LMRClientType','fileType');"
                            multiple="" style="width: 250px;">
                        <?php
                        $moduleCnt = 0;
                        if (count($moduleRequested) > 0) $moduleCnt = count($moduleRequested);
                        for ($j = 0; $j < $moduleCnt; $j++) {
                            $moduleCode = '';
                            $sOpt = '';
                            $moduleName = '';
                            $moduleCode = trim($moduleRequested[$j]['moduleCode']);
                            $moduleName = trim($moduleRequested[$j]['moduleName']);
                            $chk = '';
                            $chk = Strings::isKeyChecked($fileModuleInfo, 'moduleCode', $moduleCode);
                            if (trim($chk) == 'checked') $chk = 'selected ';
                            ?>
                            <option <?php echo $chk; ?>
                                    value="<?php echo $moduleCode; ?>"><?php echo $moduleName; ?></option>
                        <?php } ?>
                    </select>
                </div>
                <?php
            } else {
                $selectServices = '';
                $j = 0;
                for ($i = 0; $i < count($fileModuleInfo); $i++) {
                    if ($i > 0) $selectServices .= ', ';
                    $selectServices .= trim($fileModuleInfo[$i]['moduleName']);
                }
                ?>
                <h5><?php echo $selectServices; ?></h5>
                <?php
            }
            ?>
        </div>
    </div>
    <div class="form-group col-md-6">
        <label class="col-md-6" for="LMRClientType">What kind of program are you looking for?</label>
        <div class="col-md-6" id="service_container">
            <select data-placeholder="" name="LMRClientType[]"
                    onchange="formControl.controlFormFields('fileModule', '',this.id,'loanProgram');" id="LMRClientType"
                    tabindex="<?php echo $tabIndexNo++; ?>" class="chzn-select" style="width: 250px;">
                <option value="">- Select -</option>
                <?php
                $serviceCnt = 0;
                if (count($servicesRequested) > 0) $serviceCnt = count($servicesRequested);
                for ($j = 0; $j < $serviceCnt; $j++) {
                    $LMRClientTypeCode = '';
                    $sOpt = '';
                    $LMRClientType = '';
                    $LMRClientTypeCode = trim($servicesRequested[$j]['LMRClientType']);
                    $LMRClientType = trim($servicesRequested[$j]['serviceType']);
                    $chk = '';
                    $chk = Strings::isKeyChecked($LMRClientTypeInfo, 'ClientType', $LMRClientTypeCode);
                    if (trim($chk) == 'checked') $chk = 'selected ';
                    if ($servicesRequested[$j]['internalLoanProgram'] == 0) {
                        ?>
                        <option <?php echo $chk; ?>
                                value="<?php echo $LMRClientTypeCode; ?>"><?php echo $LMRClientType; ?></option>
                        <?php
                    }
                }
                ?>
            </select>
        </div>
    </div>
    <?php
}
?>
<?php
if ($_REQUEST['tabOpt'] == 'CI' && !$LMRId) {

    require 'borrowerInfo.php';

} else {

    if (in_array('LO', $fileMC) || in_array('LM', $fileMC) || (!$LMRId)) {
        require 'propDetailsLoanMod.php';
    } // https://www.pivotaltracker.com/story/show/161583314

    if ($isHOALien == 1 && $publicUser != 1) {
        $borDispOpt = 'display: none;';
    } else {
        $borDispOpt = 'display: block;';
    }
    ?>
    <?php require 'HMLOLoanTermsForm.php'; /** Loan Terms Section. **/ ?>
    <?php require 'borrowerInfo.php'; ?>
    <?php require 'borrowerAddress.php'; ?>
    <?php require 'businessEntitySection.php'; ?>
    <?php require 'salesMethod.php'; ?>
    <?php require 'feeScheduleSection.php'; ?>
    <?php require 'equipmentInformationSection.php'; ?>
    <?php //if($isHMLO ==1 || $isEF == 1) { ?>
    <?php
    include('borrowerBackground.php');
    include_once 'sbaBackgroundAdditionalQuestions.php';
    ?>

    <input type="hidden" name="recordDate" id="recordDate"
           value="<?php echo Strings::showField('recordDate', 'LMRInfo') ?>"/>
    <input type="hidden" name="max_upload_size" id="max_upload_size"
           value="<?php echo CONST_GLUPLOAD_MAX_BYTESFILESIZE_ALLOWED ?>">
    <input type="hidden" name="fileSize" id="fileSize" value="<?php echo ini_get('upload_max_filesize') ?>">
    <?php
    $recordDate = Strings::showField('recordDate', 'LMRInfo');
    /**
     * Desc : Proof of sale (HUD)
     * Date : 09 Mar, 2017
     */
    $docCnt = 1;
    $proofOfSale1 = '';
    $proofOfSale2 = '';
    $proofOfSale3 = '';
    $proofOfSale4 = '';
    $proofOfSale5 = '';
    $proofOfSale6 = '';
    $proofOfSale7 = '';
    $proofOfSale8 = '';
    $proofOfSale9 = '';
    $proofOfSale10 = '';
    $proofOfSale11 = '';
    $proofOfSale12 = '';
    $proofOfSale13 = '';
    $proofOfSale14 = '';
    $proofOfSale15 = '';
    $hudDocN7 = '';
    $hudDocN8 = '';
    $hudDocN9 = '';
    $hudDocN10 = '';
    $hudDocN11 = '';
    $hudDocN12 = '';
    $hudDocN13 = '';
    $hudDocN14 = '';
    $hudDocN15 = '';

    for ($doc = 0; $doc < count($docArray); $doc++) {
        $tempDocArray = [];
        $docCategoryArray = [];
        $flatNotes = '';
        $uploadDocUrl = '';
        $docName = '';
        $displayDocName = '';
        $docId = 0;
        $myUploadedBy = '';
        $myUploadedRole = '';
        $docCategory = '';

        $docName = trim($docArray[$doc]['docName']);
        $displayDocName = trim($docArray[$doc]['displayDocName']);
        $uploadedDate = trim($docArray[$doc]['uploadedDate']);
        $userId = trim($docArray[$doc]['uploadedBy']);
        $userType = trim($docArray[$doc]['uploadingUserType']);
        $docCategory = trim($docArray[$doc]['docCategory']);
        $docId = trim($docArray[$doc]['docID']);
        $fileType = trim($docArray[$doc]['fileType']);

        $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
        $ipArray['outputZone'] = $userTimeZone;
        $ipArray['inputTime'] = $uploadedDate;
        $uploadedDate = Dates::timeZoneConversion($ipArray);

        $docCategoryArray = explode('-', $docCategory);
        if (count($docCategoryArray) > 0) {
            if ($docCategoryArray[0] == 'Proof of sale (HUD)') {

                if (Dates::IsEmpty($uploadedDate)) {
                    $uploadedDate = '';
                } else {
                    $uploadedDate = Dates::formatDateWithRE($uploadedDate, 'YMD_HMS', 'm/d/Y h:i A');
                }

                if ($displayDocName == '' || $displayDocName == NULL) {
                    $displayDocName = $docName;
                }

                $tempRecDate = str_replace('-', '', $recordDate);
                $folderName = $oldFPCID . '/' . date('Y', strtotime($tempRecDate)) . '/' . date('m', strtotime($tempRecDate)) . '/' . date('d', strtotime($tempRecDate));
                $fileValue = $LMRId;

                $fP = $folderName . '/' . $fileValue . '/upload';
                $uploadDocUrl = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($docName) . '&fd=' . cypher::myEncryption(CONST_PATH_LMR_FILE_DOCS . $fP) . '&opt=enc';
                ${'proofOfSale' . $docCategoryArray[1]} = $uploadDocUrl;
                if (isset($docCategoryArray[1])) ${'hudDocN' . $docCategoryArray[1]} = $displayDocName;
            }
        }
    }

    ?>
    <div class="showOnHMLO showOnEF hideOnFU hideOnLO hideOnMF hideOnEF"
         style="<?php echo $HMLOSectionDivDispOpt . $EFEXPDisp . $MFDispOpt . $FUDispOpt . $LODispOpt; ?>">
        <?php require 'borrowerExperience.php'; ?>
    </div>
    <?php
    if (count($secArr = BaseHTML::sectionAccess2(['sId' => 'ACF', 'opt' => $fileTab])) > 0) { // Get Active Fields only...
        loanForm::pushSectionID('ACF');

        if (count(Arrays::getValueFromArray('ACF', $fieldsInfo)) > 0) {
            ?>
            <div class="col-md-12 <?php echo loanForm::showField('guidelinewarning'); ?>"
                 id="divGuidelinesErrorMsg"></div>
            <?php
        }
    }
    ?>
    <div class="hideOnMF hideOnLO hideOnFU hideOnHMLO hideOnEF"
         style="<?php echo $LODispOpt . $MFDispOpt . $FUDispOpt . $HMLODispOpt . $EFDispOpt ?>">
        <div class="card-body ">
            <div class="row">
                <label class="col-md-6 font-weight-bold">Is there a Non-Borrower Contributor? </label>
                <div class="col-md-6">
                    <?php if ($allowToEdit) { ?>
                        <span class="switch switch-icon">
	<label class="font-weight-bold">
		<input class="form-control" <?php if ($isNonBorrower == '1') { ?> checked="checked" <?php } ?>
			   id="isNonBo" type="checkbox"
               onchange="toggleSwitch('isNonBo', 'isNonBorrower', '1', '0' );showAndHideNonBorrDiv('nonBorrowerDiv', 2);">
	  <input type="hidden" name="isNonBorrower" id="isNonBorrower"
             value="<?php echo $isNonBorrower; ?>">
		<span></span>
	</label>
</span>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>

    <?php
    if ($isNonBorrower == '1' && $isLO != 1 && $isMF != 1 && $isFU != 1) {
        $nonBorDisp = 'display: block;';
    } else {
        $nonBorDisp = 'display: none;';
    } /** Loan Origination **/
    ?>
    <div class="card card-custom nonBorrowerContributorCard HMLOLoanInfoSections" id="nonBorrowerDiv1"
         style="<?php echo $nonBorDisp ?>">
        <div class="card-header card-header-tabs-line bg-gray-100  ">
            <div class="card-title">
                <h3 class="card-label">
                    Non-Borrower Contributor
                </h3>
            </div>
            <div class="card-toolbar ">
                <a href="javascript:void(0);"
                   class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                   data-card-tool="toggle"
                   data-section="nonBorrowerContributorCard"
                   data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                    <i class="ki ki-arrow-down icon-nm"></i>
                </a>
                <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none" data-card-tool="reload"
                   data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                    <i class="ki ki-reload icon-nm"></i>
                </a>
                <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary d-none" data-card-tool="remove"
                   data-toggle="tooltip" data-placement="top" title="" data-original-title="Remove Card">
                    <i class="ki ki-close icon-nm"></i>
                </a>
            </div>
        </div>
        <div class="card-body nonBorrowerContributorCard_body">
            <div class="row">
                <div class="col-md-6 nonBorrowerName_disp">
                    <div class="form-group  row">
                        <label class="col-md-5 font-weight-bold" for="nonBorrowerName">Name</label>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input type="text"
                                       class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'nonBorrowerName', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       name="nonBorrowerName" id="nonBorrowerName" tabindex="<?php echo $tabIndex++; ?>"
                                       value="<?php echo Strings::showField('nonBorrowerName', 'file2Info'); ?>"
                                       autocomplete="off">
                            <?php } else { ?>
                                <b><?php echo Strings::showField('nonBorrowerName', 'file2Info'); ?></b>
                            <?php } ?>
                        </div>
                    </div>
                </div>
                <div class=" col-md-6 ">
                    <div class="form-group  row">
                        <label class="col-md-5  font-weight-bold" for="nonBorrowerDOB">Date Of Birth</label>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend nonBorrowerDOB">
		<span class="input-group-text">
			<i class="fa fa-calendar text-primary"></i>
		</span>
                                    </div>

                                    <input type="text"
                                           class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'nonBorrowerDOB', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass"
                                           placeholder="MM/DD/YYYY"
                                           name="nonBorrowerDOB" id="nonBorrowerDOB"
                                           tabindex="<?php echo $tabIndex++; ?>"
                                           value="<?php echo $nonBorrowerDOB; ?>" autocomplete="off">
                                </div>

                            <?php } else { ?>
                                <b><?php echo $nonBorrowerDOB; ?></b>
                            <?php } ?>
                        </div>
                    </div>
                </div>
                <div class=" col-md-6 ">
                    <div class="form-group  row">
                        <label class="col-md-5  font-weight-bold" for="nonBorrowerSSN">SSN Number</label>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input type="text"
                                       class="form-control input-sm mask_ssn <?php echo BaseHTML::fieldAccess(['fNm' => 'nonBorrowerSSN', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       name="nonBorrowerSSN" id="nonBorrowerSSN" tabindex="<?php echo $tabIndex++; ?>"
                                       value="<?php echo $nonBorrowerSSNNumber; ?>" autocomplete="off">
                            <?php } else { ?>
                                <b><?php echo $nonBorrowerSSNNumber; ?></b>
                            <?php } ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 ">
                    <div class="form-group  row">
                        <label class="col-md-5  font-weight-bold" for="nonBorrowerEmail">Email Address</label>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input type="email"
                                       class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'nonBorrowerEmail', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       name="nonBorrowerEmail" id="nonBorrowerEmail"
                                       tabindex="<?php echo $tabIndex++; ?>"
                                       value="<?php echo Strings::showField('nonBorrowerEmail', 'file2Info'); ?>"
                                       autocomplete="off">
                            <?php } else { ?>
                                <b><?php echo Strings::showField('nonBorrowerEmail', 'file2Info'); ?></b>
                            <?php } ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 ">
                    <div class="form-group  row">
                        <label class="col-md-5  font-weight-bold" for="monthlyContribution">Monthly Contribution</label>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input type="text"
                                       class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'monthlyContribution', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                       name="monthlyContribution" id="monthlyContribution"
                                       tabindex="<?php echo $tabIndex++; ?>"
                                       value="<?php echo Strings::showField('monthlyContribution', 'file2Info'); ?>"
                                       autocomplete="off">
                            <?php } else { ?>
                                <b><?php echo Strings::showField('monthlyContribution', 'file2Info'); ?></b>
                            <?php } ?>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>


    <?php if ($isHMLO == 1 || $isEF == 1) { ?>
        <!-- Additional Guarantors Info-->
        <?php require 'additionalGuarantorsSection.php'; ?>
        <!-- Additional Guarantors Info End-->
    <?php } // Additional Guarantors End ?>

    <!-- Co-Borrower Address Start -->
    <?php require 'coborInformation.php'; ?>
    <!-- Co-Borrower Address End -->

    <!-- Property Address Div -->
    <div class="showOnHMLO showOnEF hideOnFU hideOnLO hideOnMF"
         style="<?php echo $HMLOSectionDivDispOpt . $EFSectionDivDispOpt . $MFDispOpt . $FUDispOpt . $LODispOpt ?>">
        <?php require 'coborrowerBackground.php'; ?>
    </div>

    <!-- Co-Borrower Mailing Address-Start -->
    <?php require 'coBorrowerExperience.php'; ?>
    <!-- Co-Borrower Mailing Address-End -->

    <?php
    if ($publicUser == 1) {
        ?>
        <div class="card card-custom firstLienCurrentMortgageCard">
            <div class="card-header card-header-tabs-line bg-gray-100  ">
                <div class="card-title">
                    <h3 class="card-label">
                        1st Lien Current Mortgage Scenario
                    </h3>
                </div>
                <div class="card-toolbar ">
                    <a href="javascript:void(0);"
                       class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                       data-card-tool="toggle"
                       data-section="firstLienCurrentMortgageCard"
                       data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                        <i class="ki ki-arrow-down icon-nm"></i>
                    </a>
                    <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none" data-card-tool="reload"
                       data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                        <i class="ki ki-reload icon-nm"></i>
                    </a>
                    <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary d-none" data-card-tool="remove"
                       data-toggle="tooltip" data-placement="top" title="" data-original-title="Remove Card">
                        <i class="ki ki-close icon-nm"></i>
                    </a>
                </div>
            </div>
            <div class="card-body firstLienCurrentMortgageCard_body">
                <div id="lien1Div">
                    <table> <!-- current mortgage section -->
                        <tr>
                            <td><b>Loan Type</b></td>
                            <td style="vertical-align: middle;"><b>Terms</b></td>
                            <td style="vertical-align: middle;"><b>Rate %</b></td>
                            <td><b>Current Unpaid <br> Principal Balance $</b></td>
                            <td><b>Original Loan Amt $</b></td>
                            <td style="vertical-align: middle;"><b>Loan Number</b></td>
                            <td style="vertical-align: middle;"><b>Notice of Acceleration</b></td>
                        </tr>
                        <tr class="even">
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <select name="loanType"
                                            id="loanType" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>
                                            tabindex="86">
                                        <option value=""> - Select -</option>
                                        <?php
                                        for ($i = 0; $i < count($glLien1LoanTypeArray); $i++) {
                                            $sOpt = '';
                                            $sOpt = Arrays::isSelected(trim($glLien1LoanTypeArray[$i]), $loanType);
                                            echo "<option value=\"" . trim($glLien1LoanTypeArray[$i]) . "\" " . $sOpt . '>' . trim($glLien1LoanTypeArray[$i]) . '</option>';
                                        }
                                        ?>
                                    </select>
                                <?php } else { ?>
                                    <h5><?php echo $loanType ?></h5>
                                <?php } ?>
                            </td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <select name="lien1Terms"
                                            id="lien1Terms" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>
                                            tabindex="87"
                                            onchange="calculateRemainingMonths('<?php echo date('m/d/Y'); ?>', 'loanModForm', 'loanOriginationDate', 'lien1Terms', 'remainingMonths');">
                                        <option value=""> - Select -</option>
                                        <?php
                                        for ($i = 1; $i <= count($lien1TermsArray); $i++) {
                                            $sOpt = '';
                                            if (trim($lien1TermsArray[$i]) != 'Not Sure') {
                                                $sOpt = Arrays::isSelected(trim($lien1TermsArray[$i]), $lien1Terms);
                                                echo "<option value=\"" . trim($lien1TermsArray[$i]) . "\" " . $sOpt . '>' . trim($lien1TermsArray[$i]) . '</option>';
                                            }
                                        }
                                        ?>
                                    </select>
                                <?php } else { ?>
                                    <h5><?php echo $lien1Terms; ?></h5>
                                <?php } ?>
                            </td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="lien1Rate" id="lien1Rate"
                                           value="<?php echo number_format(Strings::replaceCommaValues(Strings::showField('lien1Rate', 'LMRInfo')), 3) ?>"
                                           autocomplete="off" maxlength="12"
                                           size="5" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>
                                           tabindex="88">
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('lien1Rate', 'LMRInfo') ?></h5>
                                <?php } ?>
                            </td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="lien1Amount" id="lien1Amount"
                                           value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('lien1Amount', 'LMRInfo')) ?>"
                                           autocomplete="off" maxlength="13"
                                           size="12" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>
                                           tabindex="89"
                                           onchange="populateNewHomeProposalValue('loanModForm', 'lien1Amount', 'lien1UnPaidBalance');calculateAdjustedGrossUPB('loanModForm', 'lien1AdjustedGrossUPB');"
                                           onblur="currencyConverter(this, this.value);">
                                <?php } else { ?>
                                    <h5><?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('lien1Amount', 'LMRInfo')) ?></h5>
                                <?php } ?>
                            </td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="lien1OriginalBalance" id="lien1OriginalBalance"
                                           onblur="currencyConverter(this, this.value);"
                                           value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('lien1OriginalBalance', 'LMRInfo')) ?>"
                                           autocomplete="off" maxlength="13"
                                           size="12" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>
                                           tabindex="90">
                                <?php } else { ?>
                                    <h5><?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('lien1OriginalBalance', 'LMRInfo')) ?></h5>
                                <?php } ?>
                            </td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="loanNumber" id="loanNumber"
                                           value="<?php echo Strings::showField('loanNumber', 'LMRInfo') ?>"
                                           autocomplete="off"
                                           maxlength="50" size="10"
                                           tabindex="91" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('loanNumber', 'LMRInfo'); ?></h5>
                                <?php } ?>
                            </td>


                            <td colspan="0">
                                <?php if ($allowToEdit) { ?>
                                    <div class="left">
                                        <input type="text" name="noticeAccelerationDate" id="noticeAccelerationDate"
                                               value="<?php echo $noticeAccelerationDate ?>" maxlength="10" size="12"
                                               tabindex="91" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory dateNewClass" <?php } else { ?>
                                            class="dateNewClass"
                                        <?php } ?>>
                                        <br>(MM/DD/YYYY)
                                    </div>
                                    <div class="left pad2">
                                        <a class="fa fa-calendar fa-2x noticeAccelerationDate cursor icon-red"
                                           style="text-decoration:none;" title="Click to open the calender"></a>
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo $noticeAccelerationDate ?></h5>
                                <?php } ?>
                            </td>
                        </tr>
                        <tr>
                            <?php if ($publicUser == 1) { ?>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td> <?php } else { ?>
                                <td nowrap>
                                    <b>Loan Origination Date</b>
                                </td>
                                <td nowrap>
                                    <b>Remaining Months</b>
                                </td>
                            <?php } ?>
                            <td>
                                <b>Months Behind</b> <i>(Manual Entry)</i>
                            </td>
                            <td>
                                <b>Last Payment Made</b>
                            </td>
                            <td>
                                <b>Days Behind</b>
                            </td>
                        </tr>
                        <tr class="even">
                            <?php if ($publicUser == 1) { ?>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td> <?php } else { ?>

                                <td nowrap>
                                    <?php if ($allowToEdit) { ?>
                                        <div class="left">
                                            <input type="text" name="loanOriginationDate" id="loanOriginationDate"
                                                   value="<?php echo $noteDate ?>"

                                                   autocomplete="off" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory dateNewClass" <?php } else { ?>class=" dateNewClass"<?php } ?>
                                                   tabindex="92" maxlength="10" size="9"
                                                   onchange="calculateRemainingMonths('<?php echo date('m/d/Y'); ?>', 'loanModForm', 'loanOriginationDate', 'lien1Terms', 'remainingMonths');">
                                            <br>
                                            <div class="hint">(MM/DD/YYYY)</div>
                                        </div>
                                        <div class="left">
                                            <a class="fa fa-calendar fa-2x loanOriginationDate cursor icon-red"
                                               style="text-decoration:none;" href="javascript:void(0);"
                                               title="Click to open the calender"></a>
                                        </div>
                                    <?php } else { ?>
                                        <h5><?php echo $noteDate ?></h5>
                                    <?php } ?>
                                </td>

                                <td nowrap>
                                    <?php if ($allowToEdit) { ?>
                                        <input type="text" name="remainingMonths" id="remainingMonths"
                                               value="<?php echo $remainingMonths ?>" size="4" maxlength="3"
                                               tabindex="92"/>
                                    <?php } else { ?>
                                        <h5><?php echo $remainingMonths ?></h5>
                                    <?php } ?>
                                </td>
                            <?php } ?>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <select name="noOfMonthsBehind1" id="noOfMonthsBehind1"
                                            tabindex="93" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                                        <option value=""> - Select -</option>
                                        <?php
                                        for ($i = 0; $i < count($monthsBehindArray); $i++) {
                                            $sOpt = '';
                                            $monthsBehind = '';
                                            $monthsBehind = trim($monthsBehindArray[$i]);
                                            $sOpt = Arrays::isSelected($monthsBehind, $noOfMonthsBehind1);
                                            echo "<option value=\"" . $monthsBehind . "\" " . $sOpt . '>' . $monthsBehind . '</option>';
                                        }
                                        ?>
                                    </select>
                                <?php } else { ?>
                                    <h5><?php echo $noOfMonthsBehind1 ?></h5>
                                <?php } ?>
                            </td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <div class="left">
                                        <div class="left">
                                            <input type="text" name="lien1LPMade" id="lien1LPMade"
                                                   value="<?php echo $lien1LPMade ?>"
                                                   onchange="calculateNoOfDaysBehind('<?php echo date('m/d/Y'); ?>',this.value, 'lien1DaysBehind');"
                                                   autocomplete="off" tabindex="104" maxlength="10"
                                                   size="9" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory dateNewClass" <?php } else { ?>
                                                class=" dateNewClass"

                                            <?php } ?>>
                                            <br>
                                            <div class="hint">(MM/DD/YYYY)</div>

                                        </div>
                                        <div class="left">
                                            <a class="fa fa-calendar fa-2x lien1LPMade cursor icon-red"
                                               style="text-decoration:none;" href="javascript:void(0);"
                                               title="Click to open the calender"></a>
                                        </div>
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo $lien1LPMade ?></h5>
                                <?php } ?>
                            </td>
                            <td class="valign-top" colspan="3">
                                <div id="lien1DaysBehind" class="pad5"><?php echo $noOfDaysBehind1; ?></div>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2">
                                <b>Original Lender</b>
                            </td>
                            <td colspan="2">
                                <b>Current Lender/Servicer</b>
                            </td>
                            <td colspan="2">
                                <b>Mortgage Type</b>
                            </td>
                            <td>
                                <b>Mortgage Investor/Owner</b>
                            </td>

                        </tr>
                        <tr class="even">
                            <td colspan="2">
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="originalLender1" id="originalLender1"
                                           value="<?php echo $originalLender1 ?>" autocomplete="off" maxlength="50"
                                           size="45"
                                           tabindex="94" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>
                                           onclick="clearMyMsg('loanModForm', 'originalLender1', '<?php echo $originalLender1Text ?>');"
                                           onfocus="clearMyMsg('loanModForm', 'originalLender1', '<?php echo $originalLender1Text ?>');"
                                           onblur="putMyMsg('loanModForm', 'originalLender1', '<?php echo $originalLender1Text ?>');"
                                           style="width:200px;">
                                <?php } else { ?>
                                    <h5><?php echo $originalLender1 ?></h5>
                                <?php } ?>
                            </td>

                            <td colspan="2">

                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="servicer1" id="servicer1" value="<?php echo $servicer1 ?>"
                                           autocomplete="off" maxlength="50" tabindex="95"
                                           size="45" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>
                                           onclick="clearMyMsg('loanModForm', 'servicer1', '<?php echo $servicer1Text ?>');"
                                           onfocus="clearMyMsg('loanModForm', 'servicer1', '<?php echo $servicer1Text ?>');"
                                           onblur="putMyMsg('loanModForm', 'servicer1', '<?php echo $servicer1Text ?>');"
                                           style="width:200px;">
                                <?php } else { ?>
                                    <h5><?php echo $servicer1 ?></h5>
                                <?php } ?>
                            </td>
                            <td colspan="2">
                                <?php if ($allowToEdit) { ?>
                                    <select name="mortgageOwner1" id="mortgageOwner1"
                                            tabindex="96" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                                        <option value=""> - Select -</option>
                                        <?php
                                        $glMortgageOwnerKeyArray = [];
                                        $glMortgageOwnerKeyArray = array_keys($glMortgageOwnerArray);
                                        for ($i = 0; $i < count($glMortgageOwnerKeyArray); $i++) {
                                            $sOpt = '';
                                            $sOpt = Arrays::isSelected(trim($glMortgageOwnerKeyArray[$i]), $mortgageOwner1);
                                            echo "<option value=\"" . trim($glMortgageOwnerKeyArray[$i]) . "\" " . $sOpt . '>' . trim($glMortgageOwnerArray[$glMortgageOwnerKeyArray[$i]]) . '</option>';
                                        }
                                        ?>
                                    </select>
                                <?php } else { ?>
                                    <h5><?php echo $mortgageOwner1 ?></h5>
                                <?php } ?>
                            </td>


                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <select name="mortgageInvestor1" id="mortgageInvestor1" tabindex="97"
                                            style="width:150px;" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                                        <option value=""> - Select -</option>
                                        <?php
                                        $glMortgageInvestorOwnerKeyArray = [];
                                        $glMortgageInvestorOwnerKeyArray = array_keys($glMortgageInvestorOwnerArray);
                                        for ($i = 0; $i < count($glMortgageInvestorOwnerKeyArray); $i++) {
                                            $sOpt = '';
                                            $sOpt = Arrays::isSelected(trim($glMortgageInvestorOwnerKeyArray[$i]), $mortgageInvestor1);
                                            echo "<option value=\"" . trim($glMortgageInvestorOwnerKeyArray[$i]) . "\" " . $sOpt . '>' . trim($glMortgageInvestorOwnerArray[$glMortgageInvestorOwnerKeyArray[$i]]) . '</option>';
                                        }
                                        ?>
                                    </select>
                                <?php } else { ?>
                                    <h5><?php echo $mortgageInvestor1 ?></h5>
                                <?php } ?>
                            </td>
                        </tr>

                        <tr>
                            <td><b>Monthly P + I Payment $</b></td>
                            <td><b>Monthly Taxes $</b></td>
                            <td class="no-wrap"><b>Monthly Property<br>Insurance $</b></td>
                            <td><b>Flood<br>Insurance $</b></td>
                            <td nowrap><b>Priv. Monthly<br>Mortg. Insurance $</b></td>
                            <td><b>Monthly<br>H.O.A Fees $</b></td>
                            <td style="vertical-align: middle;text-align:center;">&nbsp; = &nbsp;<b>Total Monthly
                                    Payment</b></td>
                        </tr>
                        <tr class="even">
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <div class="left">
                                        <input type="text" name="lien1Payment" id="lien1Payment"
                                               onblur="currencyConverter(this, this.value);"
                                               value="<?php echo $lien1Payment ?>" autocomplete="off" maxlength="13"
                                               size="8"
                                               tabindex="98" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>
                                               onblur="calculateTotalPayment('loanModForm', 'totalPayment');calculateHomePITIA('loanModForm');">
                                    </div>
                                    <div class="left with-children-tip pad2">
                                        <a class="fa fa-info-circle fa-2x tip-bottom" style="text-decoration:none;"
                                           href="javascript:void(0);"
                                           title='Enter the monthly <b>Principal and Interest</b> payment only. Please refer to your mortgage statement or call your lender/servicer.<br>If you make interest only payments, please enter that amount. If you have a Pay Option Loan,<br>enter the <b>Fully Indexed Payment</b>, <u>NOT</u> the minimum payment amount.'></a>
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo $lien1Payment ?></h5>
                                <?php } ?>
                            </td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <div class="left">
                                        <input type="text" name="taxes1" id="taxes1"
                                               onblur="currencyConverter(this, this.value);"
                                               value="<?php echo $taxes1 ?>" autocomplete="off" maxlength="12"
                                               size="5" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0 && $branchReferralCode != '696655') { ?> class="mandatory" <?php } else { ?><?php } ?>
                                               onblur="calculateTotalPayment('loanModForm', 'totalPayment');calculateHomePITIA('loanModForm')"
                                               tabindex="99">
                                    </div>
                                    <div class="left with-children-tip pad2">
                                        <a class="fa fa-info-circle fa-2x tip-bottom" style="text-decoration:none;"
                                           href="javascript:void(0);"
                                           title='Enter the monthly property tax. If you pay annually, please divide the yearly amount by 12.<br>If your lender pays this out of escrow, please reference your mortgage statement or call your lender/servicer.'></a>
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo $taxes1 ?></h5>
                                <?php } ?>
                            </td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <div class="left">
                                        <input type="text" name="insurance1" id="insurance1"
                                               onblur="currencyConverter(this, this.value);"
                                               value="<?php echo $insurance1 ?>" autocomplete="off" maxlength="12"
                                               size="6" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>
                                               onblur="calculateTotalPayment('loanModForm', 'totalPayment');calculateHomePITIA('loanModForm')"
                                               tabindex="100">
                                    </div>
                                    <div class="left with-children-tip pad2">
                                        <a class="fa fa-info-circle fa-2x tip-bottom" style="text-decoration:none;"
                                           href="javascript:void(0);"
                                           title='Enter the monthly property insurance. If you pay annually, please divide the amount by 12.<br>If your lender pays this out of escrow, please reference your mortgage statement or call your lender/servicer or property insurance provider.'></a>
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo $insurance1 ?></h5>
                                <?php } ?>
                            </td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="floodInsurance1" id="floodInsurance1" tabindex="101"
                                           onblur="currencyConverter(this, this.value);"
                                           value="<?php echo $floodInsurance1 ?>" autocomplete="off" size="8"
                                           maxlength="12"
                                           onblur="calculateTotalPayment('loanModForm', 'totalPayment');calculateHomePITIA('loanModForm')" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                                <?php } else { ?>
                                    <h5><?php echo $floodInsurance1 ?></h5>
                                <?php } ?>
                            </td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <div class="left">
                                        <input type="text" name="mortgageInsurance1" id="mortgageInsurance1"
                                               onblur="currencyConverter(this, this.value); calculateTotalPayment('loanModForm', 'totalPayment');calculateHomePITIA('loanModForm');populateNewHomeProposalValue('loanModForm', 'mortgageInsurance1', 'lien1MtgInsurance');calculateAdjustedGrossUPB('loanModForm', 'lien1AdjustedGrossUPB');"
                                               value="<?php echo $mortgageInsurance1 ?>" autocomplete="off"
                                               maxlength="12"
                                               size="6" tabindex="102"
                                            <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0 && $branchReferralCode != '696655') { ?> class="mandatory" <?php } else { ?><?php } ?>>
                                    </div>
                                    <div class="left with-children-tip pad2">
                                        <a class="fa fa-info-circle fa-2x tip-bottom" style="text-decoration:none;"
                                           href="javascript:void(0);"
                                           title='If you have Mortgage Insurance, please enter the monthly amount allocated for this. Mortgage Insurance is usually for loans originally taken over 80% LTV.<br>Reference your mortgage statement to find this amount if applicable, or call your lender/servicer.'></a>
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo $mortgageInsurance1 ?></h5>
                                <?php } ?>
                            </td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <div class="left">
                                        <input type="text" name="HOAFees1" id="HOAFees1" tabindex="103"
                                               onblur="currencyConverter(this, this.value); calculateTotalPayment('loanModForm', 'totalPayment');calculateHomePITIA('loanModForm');"
                                               value="<?php echo $HOAFees1 ?>" autocomplete="off" maxlength="12"
                                               size="5"
                                            <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                                    </div>
                                    <div class="left with-children-tip pad2">
                                        <a class="fa fa-info-circle fa-2x tip-bottom" style="text-decoration:none;"
                                           href="javascript:void(0);"
                                           title='HOA Fees or associations fees are paid be homeowners who live in a condominium, town house, or housing development.<br>This amount is usually paid separate from a mortgage payment, but is considered in the housing payment.'></a>
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo $HOAFees1 ?></h5>
                                <?php } ?>
                            </td>
                            <td style="text-align:center;">
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="totalPayment" id="totalPayment"
                                           value="<?php echo $totalPayment ?>" autocomplete="off" maxlength="13"
                                           size="12"
                                           readonly class="totalPaymentDiv">
                                <?php } else { ?>
                                    <h5><?php echo $totalPayment ?></h5>
                                <?php } ?>
                            </td>
                        </tr>
                        <tr>
                            <!--                    <td>
                          <b>Last Payment Made</b>
                        </td>
                        <td>
                          <b>Days Behind</b>
                        </td>//-->
                            <td>
                                <div class="left"><b>Past Due<br>Interest</b></div>
                                <div class="pad5  left  with-children-tip">
                                    <a class="fa fa-info-circle fa-2x tip-bottom" style="text-decoration:none;"
                                       href="javascript:void(0);"
                                       title='This is the total accrued interest that is past due. Do NOT include any past due principal or escrow.'></a>
                                </div>
                            </td>
                            <td>
                                <div class="left"><b>Out of Pocket<br>Escrow Adv $</b></div>
                                <div class="pad5 left  with-children-tip">
                                    <a class="fa fa-info-circle fa-2x tip-bottom cursor" style="text-decoration:none;"
                                       title="This is the amount needed to get the property taxes and insurance current"></a>
                                </div>
                            </td>
                            <td class="no-wrap">
                                <div class="left"><b>Projected Escrow<br>Adv during Trial $</b></div>
                                <div class="pad5 left  with-children-tip">
                                    <a class="fa fa-info-circle fa-2x tip-bottom cursor" style="text-decoration:none;"
                                       title="Most trial periods are 3 months. Please estimate the amount of escrow payments required to be recapitalized for taxes and insurances"></a>
                                </div>
                            </td>
                            <td colspan="1">
                                <b>Past Due Mtg. Ins $</b>
                            </td>
                            <td>
                                <b>Past Due H.O.A $</b>
                            </td>
                            <td colspan="3">
                                <div class="left"><b>Late Fees<br>(Not Included) $</b></div>
                                <div class="pad5 left with-children-tip">
                                    <a class="fa fa-info-circle fa-2x tip-bottom cursor" style="text-decoration:none;"
                                       title="Late fees should NOT be capitalized back into the new loan balance"></a>
                                </div>
                            </td>
                        </tr>
                        <tr class="even">
                            <td class="valign-top">
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="lien1BalanceDue" id="lien1BalanceDue"
                                           value="<?php echo Strings::showField('lien1BalanceDue', 'LMRInfo') ?>"
                                           autocomplete="off"
                                           maxlength="50" size="10" tabindex="105"
                                           onblur="populateNewHomeProposalValue('loanModForm', 'lien1BalanceDue', 'lien1PastDuePI');calculateAdjustedGrossUPB('loanModForm', 'lien1AdjustedGrossUPB');" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                                <?php } else { ?>
                                    <h5><?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('lien1BalanceDue', 'LMRInfo')) ?></h5>
                                <?php } ?>
                            </td>
                            <td class="valign-top">
                                <?php
                                if ($allowToEdit) {
                                    ?>
                                    <input type="text" name="lien1ProposalEscrowShortage"
                                           id="lien1ProposalEscrowShortage"
                                           onblur="currencyConverter(this, this.value); populateNewHomeProposalValue('loanModForm', 'lien1ProposalEscrowShortage', 'lien1PocketProposalEscrowAdvances');calculateAdjustedGrossUPB('loanModForm', 'lien1AdjustedGrossUPB');"
                                           value="<?php echo $escrowAdvances ?>" size="5" maxlength="13" tabindex="106"
                                        <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>/>
                                    <?php
                                } else {
                                    if ($escrowAdvances < 0) { ?><span class="text-danger">(<?php } ?>
                                    $ <?php echo proposalFormula::convertToAbsoluteValue($escrowAdvances) ?><?php if ($escrowAdvances < 0) { ?>)</span><?php }
                                }
                                ?>
                            </td>

                            <td class="valign-top">
                                <?php
                                if ($allowToEdit) {
                                    ?>
                                    <input type="text" name="projectedEscrowAdvances" id="projectedEscrowAdvances"
                                           onblur="currencyConverter(this, this.value);populateNewHomeProposalValue('loanModForm', 'projectedEscrowAdvances', 'lien1ProjectedProposalEscrowAdvances');calculateAdjustedGrossUPB('loanModForm', 'lien1AdjustedGrossUPB');"
                                           value="<?php echo $projectedEscrowAdvances ?>" size="5" maxlength="13"
                                           tabindex="107" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>/>
                                    <?php
                                } else {
                                    if ($projectedEscrowAdvances < 0) { ?>
                                        <span class="text-danger">(
                                    <?php } ?>
                                    $ <?php echo proposalFormula::convertToAbsoluteValue($projectedEscrowAdvances) ?>

                                    <?php if ($projectedEscrowAdvances < 0) { ?>)
                                        </span>
                                        <?php
                                    }
                                }
                                ?>
                            </td>
                            <td class="valign-top">
                                <input type="text" name="pastDueMtg" id="pastDueMtg" tabindex="108"
                                       onblur="currencyConverter(this, this.value);"
                                       value="<?php echo $pastDueMtg ?>" autocomplete="off" size="8"
                                       maxlength="12" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                            </td>
                            <td class="valign-top">
                                <input type="text" name="pastDueHOA" id="pastDueHOA" tabindex="109"
                                       onblur="currencyConverter(this, this.value);"
                                       value="<?php echo $pastDueHOA ?>" autocomplete="off" size="8"
                                       maxlength="12" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                            </td>
                            <td class="valign-top" colspan="3">
                                <?php
                                if ($allowToEdit) {
                                    ?>
                                    <input type="text" name="lien1ProposalFeesAdminCosts"
                                           id="lien1ProposalFeesAdminCosts"
                                           onblur="currencyConverter(this, this.value);populateNewHomeProposalValue('loanModForm', 'lien1ProposalFeesAdminCosts', 'lien1ProposalLateFees');calculateAdjustedGrossUPB('loanModForm', 'lien1AdjustedGrossUPB');"
                                           value="<?php echo $proposalLateFees ?>" size="5" maxlength="13"
                                           tabindex="110"
                                        <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>/>
                                    <?php
                                } else {
                                    if ($proposalLateFees < 0) { ?><span class="text-danger">(<?php } ?>
                                    $ <?php echo proposalFormula::convertToAbsoluteValue($proposalLateFees) ?><?php if ($proposalLateFees < 0) { ?>)</span><?php }
                                }
                                ?>
                            </td>
                        </tr>

                    </table>
                </div>
            </div>
        </div>
        <!-- pad2 current mortgage section -->
        <div class="card card-custom secondLienCurrentMortgageCard">
            <div class="card-header card-header-tabs-line bg-gray-100  ">
                <div class="card-title">
                    <h3 class="card-label">
                        2nd Lien Mortgage Scenario
                    </h3>
                </div>
                <div class="card-toolbar ">
                    <a href="javascript:void(0);"
                       class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                       data-card-tool="toggle"
                       data-section="secondLienCurrentMortgageCard"
                       data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                        <i class="ki ki-arrow-down icon-nm"></i>
                    </a>
                    <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none" data-card-tool="reload"
                       data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                        <i class="ki ki-reload icon-nm"></i>
                    </a>
                    <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary d-none" data-card-tool="remove"
                       data-toggle="tooltip" data-placement="top" title="" data-original-title="Remove Card">
                        <i class="ki ki-close icon-nm"></i>
                    </a>
                </div>
            </div>
            <div class="card-body secondLienCurrentMortgageCard_body">
                <div id="lien2Div">
                    <table style="width:100%"> <!-- 2nd lien mortgage section -->
                        <tr>
                            <td><b>2nd lien<br>Loan Type</b></td>
                            <td style="vertical-align: middle;"><b>Terms</b></td>
                            <td style="vertical-align: middle;"><b>Rate %</b></td>
                            <td><b>Current Unpaid<br>Balance $</b></td>
                            <td><b>Original Loan<br>Amount $</b></td>
                            <td style="vertical-align: middle;"><b>Payment $</b></td>
                            <td style="vertical-align: middle;"><b>Loan Number</b></td>
                            <td>
                                <div class="left"><b>Past Due<br>Interest</b></div>
                                <div class="pad5 left with-children-tip">
                                    <a class="fa fa-info-circle fa-2x tip-bottom" style="text-decoration:none;"
                                       href="javascript:void(0);"
                                       title='This is the total accrued interest that is past due. Do NOT include any past due principal or escrow.'></a>
                                </div>
                            </td>
                            <td style="vertical-align: middle;"><b>Last Payment Made</b></td>
                        </tr>
                        <tr class="even">
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <select name="loanType2" id="loanType2"
                                            tabindex="111" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                                        <option value=""> - Select -</option>
                                        <?php
                                        for ($i = 0; $i < count($glLien2LoanTypeArray); $i++) {
                                            $sOpt = '';
                                            $sOpt = Arrays::isSelected(trim($glLien2LoanTypeArray[$i]), $loanType2);
                                            echo "<option value=\"" . trim($glLien2LoanTypeArray[$i]) . "\" " . $sOpt . '>' . trim($glLien2LoanTypeArray[$i]) . '</option>';
                                        }
                                        ?>
                                    </select>
                                <?php } else { ?>
                                    <h5><?php echo $loanType2 ?></h5>
                                <?php } ?>
                            </td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <select name="lien2Terms" id="lien2Terms"
                                            tabindex="113" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                                        <option value=""> - Select -</option>
                                        <?php
                                        for ($i = 1; $i <= count($lien2TermsArray); $i++) {
                                            $sOpt = '';
                                            if (trim($lien2TermsArray[$i]) != 'Not Sure') {
                                                $sOpt = Arrays::isSelected(trim($lien2TermsArray[$i]), $lien2Terms);
                                                echo "<option value=\"" . trim($lien2TermsArray[$i]) . "\" " . $sOpt . '>' . trim($lien2TermsArray[$i]) . '</option>';
                                            }
                                        }
                                        ?>
                                    </select>
                                <?php } else { ?>
                                    <h5><?php echo $lien2Terms; ?></h5>
                                <?php } ?>
                            </td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="lien2Rate" id="lien2Rate" tabindex="114"
                                           value="<?php echo number_format(Strings::replaceCommaValues(Strings::showField('lien2Rate', 'LMRInfo')), 3) ?>"
                                           autocomplete="off" maxlength="12"
                                           size="5" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="form-control input-sm mandatory" <?php } else { ?>class="form-control input-sm"<?php } ?>>
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('lien2Rate', 'LMRInfo') ?></h5>
                                <?php } ?>
                            </td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="lien2Amount" id="lien2Amount" tabindex="115"
                                           onblur="currencyConverter(this, this.value);"
                                           value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('lien2Amount', 'LMRInfo')) ?>"
                                           autocomplete="off" maxlength="13"
                                           size="12" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="form-control input-sm mandatory" <?php } else { ?> class="form-control input-sm" <?php } ?>>
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('lien2Amount', 'LMRInfo') ?></h5>
                                <?php } ?>
                            </td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="lien2OriginalBalance" id="lien2OriginalBalance"
                                           tabindex="116"
                                           onblur="currencyConverter(this, this.value);"
                                           value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('lien2OriginalBalance', 'LMRInfo')) ?>"
                                           autocomplete="off" maxlength="13"
                                           size="12" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('lien2OriginalBalance', 'LMRInfo') ?></h5>
                                <?php } ?>
                            </td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="lien2Payment" id="lien2Payment" tabindex="117"
                                           onblur="currencyConverter(this, this.value);"
                                           value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('lien2Payment', 'LMRInfo')) ?>"
                                           autocomplete="off" maxlength="13"
                                           size="6" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('lien2Payment', 'LMRInfo') ?></h5>
                                <?php } ?>
                            </td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="loanNumber2" id="loanNumber2" tabindex="118"
                                           value="<?php echo Strings::showField('loanNumber2', 'LMRInfo'); ?>"
                                           autocomplete="off"
                                           maxlength="50"
                                           size="10" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="form-control input-sm mandatory" <?php } else { ?> class="form-control input-sm" <?php } ?>>
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('loanNumber2', 'LMRInfo') ?></h5>
                                <?php } ?>
                            </td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="lien2BalanceDue" id="lien2BalanceDue" tabindex="119"
                                           value="<?php echo Strings::showField('lien2BalanceDue', 'LMRInfo') ?>"
                                           autocomplete="off"
                                           maxlength="50"
                                           size="10" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('lien2BalanceDue', 'LMRInfo') ?></h5>
                                <?php } ?>
                            </td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <div class="left">
                                        <input type="text" name="lien2LPMade" id="lien2LPMade" tabindex="120"
                                               value="<?php echo $lien2LPMade ?>" autocomplete="off" maxlength="10"
                                               size="9"
                                               onchange="calculateNoOfDaysBehind('<?php echo date('m/d/Y'); ?>',this.value, 'lien2DaysBehind');" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory dateNewClass" <?php } else { ?>
                                            class=" dateNewClass"
                                        <?php } ?>>
                                        <br>
                                        <div class="hint">(MM/DD/YYYY)</div>
                                    </div>
                                    <div class="left">
                                        <a class="fa fa-calendar fa-2x lien2LPMade cursor icon-red"
                                           style="text-decoration:none;" href="javascript:void(0);"
                                           title="Click to open the calender"></a>
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo $lien2LPMade ?></h5>
                                <?php } ?>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="3">
                                <b>Current Lender/Servicer</b>
                            </td>
                            <td colspan="2">
                                <b>Months Behind</b> <i>(Manual Entry)</i>
                            </td>
                            <td colspan="4">
                                <b>Days Behind</b> <i>(Based on last payment date)</i>
                            </td>
                        </tr>
                        <tr class="even">
                            <td colspan="3">
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="servicer2" id="servicer2" value="<?php echo $servicer2 ?>"
                                           tabindex="121" autocomplete="off" maxlength="50" size="45"
                                           onclick="clearMyMsg('loanModForm', 'servicer2', '<?php echo $servicer2Text ?>');"
                                           onfocus="clearMyMsg('loanModForm', 'servicer2', '<?php echo $servicer2Text ?>');"
                                           onblur="putMyMsg('loanModForm', 'servicer2', '<?php echo $servicer2Text ?>');"
                                           style="width:200px;" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                                <?php } else { ?>
                                    <h5><?php echo $servicer2 ?></h5>
                                <?php } ?>
                            </td>
                            <td colspan="2">
                                <?php if ($allowToEdit) { ?>
                                    <select name="noOfMonthsBehind2" id="noOfMonthsBehind2"
                                            tabindex="122" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                                        <option value=""> - Select -</option>
                                        <?php
                                        for ($i = 0; $i < count($monthsBehindArray); $i++) {
                                            $sOpt = '';
                                            $monthsBehind = '';
                                            $monthsBehind = trim($monthsBehindArray[$i]);
                                            $sOpt = Arrays::isSelected($monthsBehind, $noOfMonthsBehind2);
                                            echo "<option value=\"" . $monthsBehind . "\" " . $sOpt . '>' . $monthsBehind . '</option>';
                                        }
                                        ?>            </select>
                                <?php } else { ?>
                                    <h5><?php echo $noOfMonthsBehind2 ?></h5>
                                <?php } ?>
                            </td>
                            <td colspan="4">
                                <div id="lien2DaysBehind"><?php echo $noOfDaysBehind2 ?></div>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="3">
                                <b>Original Lender</b>
                            </td>
                            <td>
                                <b>Mortgage Type</b>
                            </td>
                            <td colspan="5">
                                <b>Mortgage Investor/Owner</b>
                            </td>
                        </tr>
                        <tr class="even">
                            <td colspan="3">
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="originalLender2" id="originalLender2"
                                           value="<?php echo $originalLender2 ?>" tabindex="123" autocomplete="off"
                                           maxlength="50" size="45"
                                           onclick="clearMyMsg('loanModForm', 'originalLender2', '<?php echo $originalLender2Text ?>');"
                                           onfocus="clearMyMsg('loanModForm', 'originalLender2', '<?php echo $originalLender2Text ?>');"
                                           onblur="putMyMsg('loanModForm', 'originalLender2', '<?php echo $originalLender2Text ?>');"
                                           style="width:200px;" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                                <?php } else { ?>
                                    <h5><?php echo $originalLender2 ?></h5>
                                <?php } ?>
                            </td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <select name="mortgageOwner2" id="mortgageOwner2"
                                            tabindex="124" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                                        <option value=""> - Select -</option>
                                        <?php
                                        $glMortgageOwnerKeyArray = [];
                                        $glMortgageOwnerKeyArray = array_keys($glMortgageOwnerArray);
                                        for ($i = 0; $i < count($glMortgageOwnerKeyArray); $i++) {
                                            $sOpt = '';
                                            $sOpt = Arrays::isSelected(trim($glMortgageOwnerKeyArray[$i]), $mortgageOwner2);
                                            echo "<option value=\"" . trim($glMortgageOwnerKeyArray[$i]) . "\" " . $sOpt . '>' . trim($glMortgageOwnerArray[$glMortgageOwnerKeyArray[$i]]) . '</option>';
                                        }
                                        ?>
                                    </select>
                                <?php } else { ?>
                                    <h5><?php echo $mortgageOwner2 ?></h5>
                                <?php } ?>
                            </td>
                            <td colspan="5">
                                <?php if ($allowToEdit) { ?>
                                    <select name="mortgageInvestor2" id="mortgageInvestor2"
                                            tabindex="125" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>>
                                        <option value=""> - Select -</option>
                                        <?php
                                        $glMortgageInvestorOwnerKeyArray = [];
                                        $glMortgageInvestorOwnerKeyArray = array_keys($glMortgageInvestorOwnerArray);
                                        for ($i = 0; $i < count($glMortgageInvestorOwnerKeyArray); $i++) {
                                            $sOpt = '';
                                            $sOpt = Arrays::isSelected(trim($glMortgageInvestorOwnerKeyArray[$i]), $mortgageInvestor2);
                                            echo "<option value=\"" . trim($glMortgageInvestorOwnerKeyArray[$i]) . "\" " . $sOpt . '>' . trim($glMortgageInvestorOwnerArray[$glMortgageInvestorOwnerKeyArray[$i]]) . '</option>';
                                        }
                                        ?>
                                    </select>
                                <?php } else { ?>
                                    <h5><?php echo $mortgageInvestor2 ?></h5>
                                <?php } ?>
                            </td>

                        </tr>
                    </table> <!-- 2nd lien mortgage table End -->
                </div>
            </div>
        </div>
        <?php
    }
    ?>
    <?php require 'governmentInfo.php'; /* Government For Monitoring Purposes  */ ?>

    <?php
    if ($isFU != 1) {
        $notesDisp = 'display: block;';
    } else {
        $notesDisp = 'display: none;';
    }
    if ($userGroup != 'Client') {
        ?>
        <div class="card card-custom <?php if ($PCID == 4495) {
            echo 'd-none';
        } ?>"><!-- story -27772 request-->
            <div class="card-body">
                <div class="row">
                    <div class="row col-md-12 hideOnMF" style="<?php echo $notesDisp . $MFDispOpt ?>">
                        <table style="width:100%"> <!-- mortgage notes section -->
                            <tr class="hideOnFU">
                                <td>
                                    <b><?php if ($isHMLO == 1) { ?>Borrower<?php } else { ?>Client<?php } ?>
                                        Notes <?php if ($allowToEdit) { ?><a href="#"
                                                                             onclick="printerfriendlytextbox('mortgageNotes', '<?php echo Strings::escapeQuote(Strings::showField('borrowerFName', 'LMRInfo')) ?>', '<?php echo Strings::escapeQuote(Strings::showField('borrowerLName', 'LMRInfo')) ?>');return false;">
                                                PrintFriendly</a><?php } ?></b>

                                </td>
                            </tr>
                            <tr class="hideOnFU">
                                <td>
                                    <?php if ($allowToEdit) { ?>
                                        <textarea rows="9" cols="100" name="mortgageNotes" tabindex="126"
                                                  class="form-control "
                                                  id="mortgageNotes"><?php echo Strings::showField('mortgageNotes', 'LMRInfo') ?></textarea>
                                    <?php } else { ?>
                                        <div style="text-align: left;width:900px;">
                                            <h5><?php echo Strings::showField('mortgageNotes', 'LMRInfo') ?></h5></div>
                                    <?php } ?>
                                </td>
                            </tr>

                        </table> <!-- save button -->
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    if ($allowToEdit) { ?>

        <?php /** Merchant Funding start **/ ?>
        <div class="row showOnMF" style="<?php echo $MFSectionDispOpt; ?>">
            <div class="col-md-6">
                <div class="card card-custom ">
                    <div class="card-header card-header-tabs-line bg-gray-100  ">
                        <div class="card-title">
                            <h3 class="card-label">
                                Personal Information
                            </h3>
                        </div>
                        <div class="card-toolbar d-none">
                            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1" data-card-tool="toggle"
                               data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                                <i class="ki ki-arrow-down icon-nm"></i>
                            </a>
                            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1" data-card-tool="reload"
                               data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                                <i class="ki ki-reload icon-nm"></i>
                            </a>
                            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary" data-card-tool="remove"
                               data-toggle="tooltip" data-placement="top" title="" data-original-title="Remove Card">
                                <i class="ki ki-close icon-nm"></i>
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="personalInfoDiv">
                            <table style="width:100%" class="table table-hover table-condensed ">
                                <tr>
                                    <td style="width:50%" class="font-weight-bold">Do you own the majority of the
                                        business?
                                    </td>
                                    <td style="width:50%">
                                        <div class="radio-inline">
                                            <label class="radio radio-solid font-weight-bold" for="majorityBusinessYes">
                                                <input type="radio" name="majorityBusiness" value="Yes"
                                                       id="majorityBusinessYes"
                                                       tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $majorityBusiness); ?>><span></span>Yes
                                            </label>
                                            <label class="radio radio-solid font-weight-bold" for="majorityBusinessNo">
                                                <input type="radio" name="majorityBusiness" value="No"
                                                       id="majorityBusinessNo"
                                                       tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $majorityBusiness); ?>><span></span>No
                                            </label></div>
                                    </td>
                                </tr>
                                <tr class="even">
                                    <td class="font-weight-bold">Owner's Name</td>
                                    <td>
                                        <input type="text" name="ownersName" class="form-control"
                                               value="<?php echo Strings::showField('ownersName', 'MFInfo') ?>"
                                               tabindex="<?php echo $tabIndexNo++; ?>">
                                    </td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">% Owned</td>
                                    <td>
                                        <input type="text" name="Owned" class="form-control"
                                               value="<?php echo Strings::showField('Owned', 'MFInfo') ?>"
                                               tabindex="<?php echo $tabIndexNo++; ?>">
                                    </td>
                                </tr>
                            </table>
                        </div> <!-- personalInfoDiv END -->
                    </div>

                </div><!-- Personal Information END -->

                <div class="card card-custom ">
                    <div class="card-header card-header-tabs-line bg-gray-100  ">
                        <div class="card-title">
                            <h3 class="card-label">
                                Business Information
                            </h3>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="businessInfoDiv">
                            <table style="width:100%" class="table table-hover table-condensed ">
                                <tr>
                                    <td style="width:40%" class="font-weight-bold">Business Name</td>
                                    <td style="width:60%">
                                        <input type="text" name="businessName" class="form-control"
                                               value="<?php echo Strings::showField('businessName', 'MFInfo') ?>"
                                               tabindex="<?php echo $tabIndexNo++; ?>">
                                    </td>
                                </tr>
                                <tr class="even">
                                    <td class="font-weight-bold">Business Name (DBA)</td>
                                    <td>
                                        <input type="text" name="businessNameDBA" class="form-control"
                                               value="<?php echo Strings::showField('businessNameDBA', 'MFInfo') ?>"
                                               tabindex="<?php echo $tabIndexNo++; ?>">
                                    </td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Business Legal Name</td>
                                    <td>
                                        <input type="text" name="businessLegalName" id="businessLegalName"
                                               class="form-control"
                                               value="<?php echo Strings::showField('businessLegalName', 'MFInfo') ?>"
                                               tabindex="<?php echo $tabIndexNo++; ?>">
                                    </td>
                                </tr>
                                <tr class="even">
                                    <td class="font-weight-bold">Business Tax ID</td>
                                    <td>
                                        <input type="text" name="businessTax" id="businessTax" class="form-control"
                                               value="<?php echo Strings::showField('businessTax', 'MFInfo') ?>"
                                               tabindex="<?php echo $tabIndexNo++; ?>">
                                    </td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Business Entity Type</td>
                                    <td>
                                        <input type="text" name="businessTypeMF" id="businessTypeMF" class="form-control"
                                               value="<?php echo Strings::showField('businessType', 'MFInfo') ?>"
                                               tabindex="<?php echo $tabIndexNo++; ?>">
                                    </td>
                                </tr>
                                <tr class="even">
                                    <td class="font-weight-bold">Website</td>
                                    <td>
                                        <input type="text" name="Website" id="Website" class="form-control"
                                               value="<?php echo Strings::showField('Website', 'MFInfo') ?>"
                                               tabindex="<?php echo $tabIndexNo++; ?>">
                                    </td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Industry</td>
                                    <td>
                                        <input type="text" name="Industry" id="Industry" class="form-control"
                                               value="<?php echo Strings::showField('Industry', 'MFInfo') ?>"
                                               tabindex="<?php echo $tabIndexNo++; ?>">
                                    </td>
                                </tr>
                                <tr class="even">
                                    <td class="font-weight-bold">Are your clients businesses or consumers ?</td>
                                    <td>
                                        <div class="radio-inline">
                                            <label class="radio radio-solid font-weight-bold"
                                                   for="businessConsumersBus">
                                                <input type="radio" name="businessConsumers" value="bus"
                                                       id="businessConsumersBus"
                                                       tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('bus', $businessConsumers); ?>><span></span>Businesses
                                            </label>
                                            <label class="radio radio-solid font-weight-bold"
                                                   for="businessConsumersCons">
                                                <input type="radio" name="businessConsumers" value="con"
                                                       id="businessConsumersCons"
                                                       tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('con', $businessConsumers); ?>><span></span>Consumers
                                            </label></div>
                                    </td>
                                </tr>
                            </table>
                        </div><!-- businessInfoDiv END -->
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card card-custom ">
                    <div class="card-header card-header-tabs-line bg-gray-100  ">
                        <div class="card-title">
                            <h3 class="card-label">
                                Financial Information
                            </h3>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="financialInfoDiv">
                            <table style="width:100%" class="table table-hover table-condensed ">
                                <tr>
                                    <td style="width:50%" class="font-weight-bold">Months in business?</td>
                                    <td style="width:50%">
                                        <input type="text" name="monthsBusiness" class="form-control"
                                               value="<?php echo Strings::showField('monthsBusiness', 'MFInfo') ?>"
                                               tabindex="<?php echo $tabIndexNo++; ?>">
                                    </td>
                                </tr>
                                <tr class="even">
                                    <td class="font-weight-bold">Average monthly revenue ($)</td>
                                    <td>
                                        <input type="text" name="monthlyRevenue" class="form-control"
                                               onblur="currencyConverter(this, this.value);"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('monthlyRevenue', 'MFInfo')) ?>"
                                               tabindex="<?php echo $tabIndexNo++; ?>">
                                    </td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Credit score</td>
                                    <td>
                                        <input type="text" name="creditScore" id="creditScore" class="form-control"
                                               value="<?php echo Strings::showField('creditScore', 'MFInfo') ?>"
                                               tabindex="<?php echo $tabIndexNo++; ?>">
                                    </td>
                                </tr>
                                <tr class="even">
                                    <td class="font-weight-bold">Annual Gross Revenue ($)</td>
                                    <td>
                                        <input type="text" name="grossRevenue" id="grossRevenue" class="form-control"
                                               onblur="currencyConverter(this, this.value);"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('grossRevenue', 'MFInfo')) ?>"
                                               tabindex="<?php echo $tabIndexNo++; ?>">
                                    </td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Monthly Bank Deposit Volume ($)</td>
                                    <td>
                                        <input type="text" name="monthlyDepositVolume" id="monthlyDepositVolume"
                                               class="form-control"
                                               onblur="currencyConverter(this, this.value);"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('monthlyDepositVolume', 'MFInfo')) ?>"
                                               tabindex="<?php echo $tabIndexNo++; ?>">
                                    </td>
                                </tr>
                                <tr class="even">
                                    <td class="font-weight-bold">Average Daily Bank Balance ($)</td>
                                    <td>
                                        <input type="text" name="bankBalance" id="bankBalance" class="form-control"
                                               onblur="currencyConverter(this, this.value);"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('bankBalance', 'MFInfo')) ?>"
                                               tabindex="<?php echo $tabIndexNo++; ?>">
                                    </td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Do you have invoices to factor?</td>
                                    <td>
                                        <?php
                                        if ($invoices == 'Yes') {
                                            $invoicesDisp = '';//'display: table-row;';
                                        } else {
                                            $invoicesDisp = 'display: none;';
                                        }
                                        ?>
                                        <div class="radio-inline">
                                            <label class="radio radio-solid font-weight-bold" for="iyes">
                                                <input type="radio" name="invoices" id="iyes" value="Yes"
                                                       tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $invoices); ?>><span></span>Yes</label>
                                            <label class="radio radio-solid font-weight-bold" for="ino">
                                                <input type="radio" name="invoices" id="ino" value="No"
                                                       tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $invoices); ?>><span></span>No
                                            </label>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="even" id="row-yes" style="<?php echo $invoicesDisp; ?>">
                                    <td style="text-align:right;" class="font-weight-bold">How much ($) ?</td>
                                    <td><input type="text" name="invoiceAmount" id="invoiceAmount" class="form-control"
                                               onblur="currencyConverter(this, this.value);"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('invoiceAmount', 'MFInfo')) ?>"
                                               tabindex="<?php echo $tabIndexNo++; ?>"></td>
                                </tr>
                                <tr class="even">
                                    <td class="font-weight-bold">Requested Advance Amount ($)</td>
                                    <td>
                                        <input type="text" name="requestedAmount" id="requestedAmount"
                                               class="form-control"
                                               onblur="currencyConverter(this, this.value);"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('requestedAmount', 'MFInfo')) ?>"
                                               tabindex="<?php echo $tabIndexNo++; ?>">
                                    </td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Requested Term Length (in Months)</td>
                                    <td>
                                        <input type="text" name="requestedTermLength" id="requestedTermLength"
                                               class="form-control"
                                               value="<?php echo Strings::showField('requestedTermLength', 'MFInfo') ?>"
                                               tabindex="<?php echo $tabIndexNo++; ?>">
                                    </td>
                                </tr>
                                <tr class="even">
                                    <td class="font-weight-bold">How do you plan to use your funding?</td>
                                    <td>
                                        <input type="text" name="funding" id="funding" class="form-control"
                                               value="<?php echo Strings::showField('funding', 'MFInfo') ?>"
                                               tabindex="<?php echo $tabIndexNo++; ?>">
                                    </td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Does the business accept credit cards?</td>
                                    <td>
                                        <div class="radio-inline">
                                            <label class="radio radio-solid font-weight-bold"
                                                   for="businessCreditCardsYes">
                                                <input type="radio" name="businessCreditCards" value="Yes"
                                                       id="businessCreditCardsYes"
                                                       tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $businessCreditCards); ?>><span></span>Yes
                                            </label>
                                            <label class="radio radio-solid font-weight-bold"
                                                   for="businessCreditCardsNo">
                                                <input type="radio" name="businessCreditCards" value="No"
                                                       id="businessCreditCardsNo"
                                                       tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $businessCreditCards); ?>><span></span>No
                                            </label></div>
                                    </td>
                                </tr>
                                <tr class="even">
                                    <td class="font-weight-bold">Does the business have exsisting financing?</td>
                                    <td>
                                        <div class="radio-inline">
                                            <label class="radio radio-solid font-weight-bold"
                                                   for="businessFinancingYes">
                                                <input type="radio" name="businessFinancing" value="Yes"
                                                       id="businessFinancingYes"
                                                       tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $businessFinancing); ?>><span></span>Yes</label>
                                            <label class="radio radio-solid font-weight-bold" for="businessFinancingNo">
                                                <input type="radio" name="businessFinancing" value="No"
                                                       id="businessFinancingNo"
                                                       tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $businessFinancing); ?>><span></span>No</label>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">On your most recent business tax return, did you show a
                                        net
                                        profit or a net loss?
                                    </td>
                                    <td>
                                        <div class="radio-inline">
                                            <label class="radio radio-solid font-weight-bold" for="netProfitPro">
                                                <input type="radio" name="netProfit" value="Pro" id="netProfitPro"
                                                       tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Pro', $netProfit); ?>><span></span>Profit</label>
                                            <label class="radio radio-solid font-weight-bold" for="netProfitLoss">
                                                <input type="radio" name="netProfit" value="Loss" id="netProfitLoss"
                                                       tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Loss', $netProfit); ?>><span></span>Loss</label>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="even">
                                    <td class="font-weight-bold">Amount?</td>
                                    <td>
                                        <input type="text" name="amount" id="amount" class="form-control"
                                               onblur="currencyConverter(this, this.value);"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('amount', 'MFInfo')) ?>"
                                               tabindex="<?php echo $tabIndexNo++; ?>">
                                    </td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Do you own collateral?</td>
                                    <td>
                                        <div class="radio-inline">
                                            <label class="radio radio-solid font-weight-bold" for="ownCollateralYes">
                                                <input type="radio" name="ownCollateral" value="Yes"
                                                       id="ownCollateralYes"
                                                       tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $ownCollateral); ?>><span></span>Yes</label>
                                            <label class="radio radio-solid font-weight-bold" for="ownCollateralNo">
                                                <input type="radio" name="ownCollateral" value="No" id="ownCollateralNo"
                                                       tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $ownCollateral); ?>><span></span>No</label>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="even">
                                    <td class="font-weight-bold">Sum of monthly recurring debt payments</td>
                                    <td>
                                        <input type="text" name="monthlyDebtPayments" id="monthlyDebtPayments"
                                               class="form-control"
                                               onblur="currencyConverter(this, this.value);"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('monthlyDebtPayments', 'MFInfo')) ?>"
                                               tabindex="<?php echo $tabIndexNo++; ?>">
                                    </td>
                                </tr>
                            </table>
                        </div><!-- financialInfoDiv END -->

                    </div>
                </div>
            </div>
        </div>
    <?php } ?>

    <div class="showOnFU hideOnLO hideOnMF" style="<?php echo $FUSectionDispOpt; ?>">
        <div class="card card-custom">
            <div class="card-header card-header-tabs-line bg-gray-100  ">
                <div class="card-title">
                    <h3 class="card-label">
                        Q+A
                    </h3>
                </div>
                <div class="card-toolbar d-none">
                    <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1" data-card-tool="toggle"
                       data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                        <i class="ki ki-arrow-down icon-nm"></i>
                    </a>
                    <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1" data-card-tool="reload"
                       data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                        <i class="ki ki-reload icon-nm"></i>
                    </a>
                    <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary" data-card-tool="remove"
                       data-toggle="tooltip" data-placement="top" title="" data-original-title="Remove Card">
                        <i class="ki ki-close icon-nm"></i>
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div id="QAInfoDiv">
                    <table style="width:100%" class="table table-hover table-condensed ">
                        <tr>
                            <td class="font-weight-bold" width="25%">Credit Check Total Username</td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="creditChkUsername" id="creditChkUsername"
                                           class="form-control"
                                           value="<?php echo htmlentities(Strings::showField('creditChkUsername', 'FUInfo')); ?>"
                                           tabindex="<?php echo $tabIndexNo++; ?>" autocomplete="off" maxlength="50"
                                    />
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('creditChkUsername', 'FUInfo') ?></h5>
                                <?php } ?>

                            </td>
                            <td class="font-weight-bold" width="25%">Credit Check Total Password</td>
                            <td width="25%">
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="creditChkPwd" id="creditChkPwd" class="form-control"
                                           value="<?php echo htmlentities(Strings::showField('creditChkPwd', 'FUInfo')); ?>"
                                           tabindex="<?php echo $tabIndexNo++; ?>" autocomplete="off"/>
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('creditChkPwd', 'FUInfo') ?></h5>
                                <?php } ?>
                            </td>
                        </tr>
                        <tr class="even">
                            <td class="font-weight-bold">Experian Score</td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="experianScore" id="experianScore" class="form-control"
                                           value="<?php echo Strings::showField('experianScore', 'FUInfo') ?>"
                                           tabindex="<?php echo $tabIndexNo++; ?>"/>
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('experianScore', 'FUInfo') ?></h5>
                                <?php } ?>
                            </td>
                            <td class="font-weight-bold">Equifax Score</td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="equifaxScore" id="equifaxScore" class="form-control"
                                           value="<?php echo Strings::showField('equifaxScore', 'FUInfo') ?>"
                                           tabindex="<?php echo $tabIndexNo++; ?>"/>
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('equifaxScore', 'FUInfo') ?></h5>
                                <?php } ?>
                            </td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">Transunion Score</td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="transunionScore" id="transunionScore" class="form-control"
                                           value="<?php echo Strings::showField('transunionScore', 'FUInfo') ?>"
                                           tabindex="<?php echo $tabIndexNo++; ?>"/>
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('transunionScore', 'FUInfo') ?></h5>
                                <?php } ?>
                            </td>
                            <td class="font-weight-bold">Monthly Income before Taxes?</td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="monthlyIncomeBFTax" id="monthlyIncomeBFTax"
                                           class="form-control"
                                           value="<?php echo Strings::showField('monthlyIncomeBFTax', 'FUInfo') ?>"
                                           tabindex="<?php echo $tabIndexNo++; ?>"/>
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('monthlyIncomeBFTax', 'FUInfo') ?></h5>
                                <?php } ?>
                            </td>
                        </tr>
                        <tr class="even">
                            <td class="font-weight-bold">Monthly Income after Taxes?</td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="monthlyIncomeAFTax" id="monthlyIncomeAFTax"
                                           class="form-control"
                                           value="<?php echo Strings::showField('monthlyIncomeAFTax', 'FUInfo') ?>"
                                           tabindex="<?php echo $tabIndexNo++; ?>"/>
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('monthlyIncomeAFTax', 'FUInfo') ?></h5>
                                <?php } ?>
                            </td>
                            <td class="font-weight-bold">Are you employed?</td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <div class="radio-inline">
                                        <label class="radio radio-solid font-weight-bold" for="haveEmployedYes">
                                            <input type="radio" name="haveEmployed" value="Yes" id="haveEmployedYes"
                                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $haveEmployed); ?>
                                                   onclick="showFundingSubDiv(this.value, 'Employed');"><span></span>Yes
                                        </label>
                                        <label class="radio radio-solid font-weight-bold" for="haveEmployedNo">
                                            <input type="radio" name="haveEmployed" value="No" id="haveEmployedNo"
                                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $haveEmployed); ?>
                                                   onclick="showFundingSubDiv(this.value, 'Employed');"><span></span>No
                                        </label>
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo $haveEmployed ?></h5>
                                <?php } ?>
                            </td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">Can you prove income with paystub, W2, and income<br>tax
                                return?
                            </td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <div class="radio-inline">
                                        <label class="radio radio-solid font-weight-bold"
                                               for="havePaystubW2ITReturnYes">
                                            <input type="radio" name="havePaystubW2ITReturn" value="Yes"
                                                   id="havePaystubW2ITReturnYes"
                                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $havePaystubW2ITReturn); ?>><span></span>Yes
                                        </label>
                                        <label class="radio radio-solid font-weight-bold" for="havePaystubW2ITReturnNo">
                                            <input type="radio" name="havePaystubW2ITReturn" value="No"
                                                   id="havePaystubW2ITReturnNo"
                                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $havePaystubW2ITReturn); ?>><span></span>No
                                        </label></div>
                                <?php } else { ?>
                                    <h5><?php echo $havePaystubW2ITReturn ?></h5>
                                <?php } ?>
                            </td>
                            <td class="showOnEmployedFUDiv font-weight-bold"
                                style="<?php echo $FUSectionSubEmpDivDispOpt ?>">
                                Number of Months at Current Job?
                            </td>
                            <td class="showOnEmployedFUDiv" style="<?php echo $FUSectionSubEmpDivDispOpt ?>">
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="noOfMonthsAtCurrentJob" id="noOfMonthsAtCurrentJob"
                                           class="form-control"
                                           value="<?php echo Strings::showField('noOfMonthsAtCurrentJob', 'FUInfo') ?>"
                                           tabindex="<?php echo $tabIndexNo++; ?>" style="width:150px"/>
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('noOfMonthsAtCurrentJob', 'FUInfo') ?></h5>
                                <?php } ?>
                            </td>
                        </tr>
                        <tr class="even">
                            <td class="font-weight-bold">Have you or any family member served in the US Military?</td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <div class="radio-inline">
                                        <label class="radio radio-solid font-weight-bold" for="isFMInUSMilitaryYes">
                                            <input type="radio" name="isFMInUSMilitary" value="Yes"
                                                   id="isFMInUSMilitaryYes"
                                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $isFMInUSMilitary); ?>><span></span>Yes
                                        </label>
                                        <label class="radio radio-solid font-weight-bold" for="isFMInUSMilitaryNo">
                                            <input type="radio" name="isFMInUSMilitary" value="No"
                                                   id="isFMInUSMilitaryNo"
                                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $isFMInUSMilitary); ?>><span></span>No</label>
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo $isFMInUSMilitary ?></h5>
                                <?php } ?>
                            </td>
                            <td class="font-weight-bold">Do you own a business ?</td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <div class="radio-inline">
                                        <label class="radio radio-solid font-weight-bold" for="ownBusinessYes">
                                            <input type="radio" name="ownBusiness" value="Yes" id="ownBusinessYes"
                                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $ownBusiness); ?>
                                                   onclick="showFundingSubDiv(this.value, 'Business');"><span></span>Yes
                                        </label>
                                        <label class="radio radio-solid font-weight-bold" for="ownBusinessNo">
                                            <input type="radio" name="ownBusiness" value="No" id="ownBusinessNo"
                                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $ownBusiness); ?>
                                                   onclick="showFundingSubDiv(this.value, 'Business');"><span></span>No
                                        </label></div>
                                <?php } else { ?>
                                    <h5><?php echo $ownBusiness ?></h5>
                                <?php } ?>
                            </td>
                        </tr>
                        <tr class="showOnBusinessFUDiv even" style="<?php echo $FUSectionSubOwnBusinessDivDispOpt ?>">
                            <td class="font-weight-bold">Type of Business</td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="typeOfBusiness" id="typeOfBusiness" class="form-control"
                                           value="<?php echo htmlentities(Strings::showField('typeOfBusiness', 'FUInfo')); ?>"
                                           tabindex="<?php echo $tabIndexNo++; ?>" maxlength="45" style="width:210px"/>
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('typeOfBusiness', 'FUInfo') ?></h5>
                                <?php } ?>
                            </td>
                            <td class="font-weight-bold">When did you incorporate?</td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <input type="date" name="businessStartDate" id="businessStartDate"
                                           class="form-control"
                                           value="<?php echo Strings::showField('businessStartDate', 'FUInfo') ?>"
                                           tabindex="<?php echo $tabIndexNo++; ?>"/>
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('businessStartDate', 'FUInfo') ?></h5>
                                <?php } ?>
                            </td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">Do you own or rent?</td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <div class="radio-inline">
                                        <label class="radio radio-solid font-weight-bold" for="propTypeYes">
                                            <input type="radio" name="propType" value="Yes" id="propTypeYes"
                                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $FUPropType); ?>><span></span>Own</label>
                                        <label class="radio radio-solid font-weight-bold" for="propTypeNo">
                                            <input type="radio" name="propType" value="No" id="propTypeNo"
                                                   tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $FUPropType); ?>><span></span>Rent
                                        </label>
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo $FUPropType ?></h5>
                                <?php } ?>
                            </td>
                            <td class="font-weight-bold">Credit Issues</td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <select data-placeholder="" name="creditIssues[]" id="creditIssues"
                                                    class="chzn-select odd "
                                                    multiple="" tabindex="<?php echo $tabIndexNo++; ?>">
                                                <?php
                                                for ($s = 0; $s < count($glCreditIssues); $s++) {
                                                    $sOpt = '';
                                                    $sOpt = Arrays::isSelectedArray($creditIssuesArray, $glCreditIssues[$s]);
                                                    echo "<option value=\"" . $glCreditIssues[$s] . "\" " . $sOpt . '>' . $glCreditIssues[$s] . '</option>';
                                                }
                                                ?>
                                            </select></div>
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo implode(', ', $creditIssuesArray); ?></h5>
                                <?php } ?>

                            </td>
                        </tr>
                        <tr class="even">
                            <td class="valign-top font-weight-bold">Amount Desired?</td>
                            <td class="valign-top">
                                <?php if ($allowToEdit) { ?>
                                    <select name="amtDesired" class="form-control" id="amtDesired"
                                            tabindex="<?php echo $tabIndexNo++; ?>">
                                        <option value=""> - Select -</option>
                                        <?php
                                        $glAmountDesiredKeys = [];
                                        $glAmountDesiredKeys = array_keys($glAmountDesired);
                                        for ($i = 0; $i < count($glAmountDesiredKeys); $i++) {
                                            $amountDesired = $sOpt = '';
                                            $amountDesired = trim($glAmountDesiredKeys[$i]);
                                            $sOpt = Arrays::isSelected($amtDesired, $amountDesired);

                                            ?>
                                            <option value="<?php echo $amountDesired ?>" <?php echo $sOpt ?>><?php echo $glAmountDesired[$amountDesired] ?></option>
                                            <?php
                                        }
                                        ?>
                                    </select>
                                <?php } else { ?>
                                    <h5><?php echo $amtDesired ?></h5>
                                <?php } ?>
                            </td>
                            <td class="valign-top font-weight-bold">Reason for Funds</td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <select data-placeholder="" name="fundsNeededReason[]" id="fundsNeededReason"
                                            class="chzn-select odd form-control" multiple=""
                                            tabindex="<?php echo $tabIndexNo++; ?>"
                                            onchange="showFundingNotesDiv('fundsNeededReason', 'MortgageNotes');">
                                        <?php
                                        for ($s = 0; $s < count($glFundsNeeded); $s++) {
                                            $sOpt = '';
                                            $sOpt = Arrays::isSelectedArray($fundsNeededReasonArray, $glFundsNeeded[$s]);
                                            echo "<option value=\"" . $glFundsNeeded[$s] . "\" " . $sOpt . '>' . $glFundsNeeded[$s] . '</option>';
                                        }
                                        ?>
                                    </select>
                                <?php } else { ?>
                                    <h5><?php echo implode(', ', $fundsNeededReasonArray); ?></h5>
                                <?php } ?>
                            </td>
                        </tr>
                        <tr class="showOnMortgageNotesFUDiv" style="<?php echo $FUSectionSubNotesDivDispOpt ?>">
                            <td colspan="4" class="font-weight-bold">Comments<br>
                                <?php if ($allowToEdit) { ?>
                                    <textarea name="FUMortgageNotes" class="form-control "
                                              tabindex="<?php echo $tabIndexNo++; ?>"
                                              id="FUMortgageNotes"><?php echo Strings::showField('mortgageNotes', 'LMRInfo') ?></textarea>
                                <?php } else { ?>
                                    <div style="text-align: left;width:900px;">
                                        <h5><?php echo Strings::showField('mortgageNotes', 'LMRInfo') ?></h5></div>
                                <?php } ?>

                            </td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">Who Referred You?</td>
                            <td>
                                <?php if ($allowToEdit) { ?>
                                    <input type="text" name="referredBy" id="referredBy" class="form-control"
                                           value="<?php echo htmlentities(Strings::showField('referredBy', 'FUInfo')); ?>"
                                           tabindex="<?php echo $tabIndexNo++; ?>"/>
                                <?php } else { ?>
                                    <h5><?php echo Strings::showField('referredBy', 'FUInfo') ?></h5>
                                <?php } ?>
                            </td>
                            <td></td>
                            <td></td>
                        </tr>

                    </table>
                </div>
            </div>
        </div>
    </div>

<?php } ?>

<div class="my-4">
    <table style="width:100%"> <!-- save section -->
        <?php if ($allowToEdit) { ?>
            <tr>
                <td style="text-align: center;">
                    <input type="submit" class="btn btn-primary btnSave" id="saveBtn" name="btnSave" value="Save"
                           tabindex="<?php echo $tabIndexNo++; ?>"
                           onclick="return this.disabled===false;">
                    <input type="submit" class="btn btn-primary btnSave" id="saveNextBtn" name="btnSave" value="Save & Next"
                           tabindex="<?php echo $tabIndexNo++; ?>"
                           onclick="return this.disabled===false;">
                </td>
            </tr>
        <?php } ?>
    </table> <!-- save button -->
</div>
<script>
    $(function () {
        $('#iyes').click(function () {
            $('#row-yes').show();
        });
        $('#ino').click(function () {
            $('#row-yes').hide();
        });
    });
    $(document).ready(function () {
        $('#naicsCode:enabled').inputmask("999999");

        let publicUser = <?php echo $publicUser ?>;
        let LMRId = <?php echo $LMRId; ?>;
        if (!publicUser && !LMRId) {
            let branchList = <?php echo json_encode($branchList); ?>;
            let PCID = <?php echo $PCID ?>;
            let moduleCode = '<?php echo LMRequest::$moduleCode; ?>';
            if (Object.keys(branchList).length === 1) {
                getModules('loanModForm', PCID, moduleCode);
                getServiceTypes('loanModForm');
                getBranchAgents($('#branchId').val());
            }
        }
    });
</script>
<!-- clientInfoForm.php -->
