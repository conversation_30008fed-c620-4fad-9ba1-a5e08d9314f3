<?php
global $publicUser, $userGroup, $allowToupdateFileAndClient, $pcClient_id, $allowPeerstreet, $allowToSeeAlowareTab, $thirdPartyServices,
       $glThirdPartyServices, $allowPCUsersToMarketPlace, $allowToViewMarketPlace, $subscribeToHOME, $subscribePCToHOME, $allowServicing,
       $isPCActiveAloware, $viewSubmitOfferTab, $pcPriceEngineStatus,
       $userRole, $viewPublicNotes, $viewPrivateNotes,
       $allowCFPBAuditing, $userEmail, $userPriceEngineStatus, $allowToEditCommission, $externalBroker,
       $allowClientToCreateHMLOFile, $allowToAccessInternalLoanProgram, $allowToSeeAllBrokers;

use models\composite\oAcqualify\getClientCreditInfo;
use models\composite\oBilling\getBillingReminderInfoForFile;
use models\composite\oBinder\getBinderDocName;
use models\composite\oBinder\getBinderDocument;
use models\composite\oBranch\getacqualifyBranchDetails;
use models\composite\oBranch\getBranches;
use models\composite\oBranch\getBranchesForAgent;
use models\composite\oBranch\getBranchModules;
use models\composite\oBranch\getBranchServices;
use models\composite\oBranch\getBranchUploadedDocuments;
use models\composite\oBroker\getBrokerInfo;
use models\composite\oBroker\getPCAgents;
use models\composite\oBroker\listAllAgents;
use models\composite\oBroker\listAllAgentsLoanOfficer;
use models\composite\oClient\getClientFileBranchAndAgent;
use models\composite\oClient\getPCClientBackgroundInfo;
use models\composite\oClient\getPCClientExperienceInfo;
use models\composite\oEmployee\getEmployeeAssignedBranches;
use models\composite\oEmployee\getPCEmployeeList;
use models\composite\oFile\checkFileAccess;
use models\composite\oFile\getBorrowerNumberOfDeals;
use models\composite\oFile\getFileInfo;
use models\composite\oFile\getFileModules;
use models\composite\oFile\getMyFileInfo;
use models\composite\oFile\insertLastView;
use models\composite\oFileHardship\getMyHardships;
use models\composite\oPC\getacqualifyPCDetails;
use models\composite\oPC\getacqualifyUserDetails;
use models\composite\oPC\getFileTabsForPCModules;
use models\composite\oPC\getPCInternalServiceType;
use models\composite\oPC\getPCList;
use models\composite\oPC\getPCModules;
use models\composite\oPC\getPCServiceType;
use models\composite\oPrimaryStatus\getPCPrimaryStatus;
use models\composite\oServiceType\getServiceTypes;
use models\composite\oSubstatus\getPCFileSubstatus;
use models\composite\oTask\getFileTasks;
use models\composite\oWorkflow\getFileWFSteps;
use models\composite\oWorkflow\getFileWorkflow;
use models\constants\gl\glAllowEmpToUpdateBranchsForFiles;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glTypeOfSaleEF;
use models\constants\GpropertyTypeNumbArray;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\Property;
use models\Database2;
use models\lendingwise\tblBrokerAssociatedLoanOfficers;
use models\LoanMenu;
use models\cypher;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\Strings;
use models\standard\UserAccess;

$glTypeOfSaleEF = glTypeOfSaleEF::$glTypeOfSaleEF;
$glAllowEmpToUpdateBranchsForFiles = glAllowEmpToUpdateBranchsForFiles::$glAllowEmpToUpdateBranchsForFiles;

if (isset($_REQUEST['eOpt'])) $eOpt = trim($_REQUEST['eOpt']);
if (isset($_REQUEST['tabOpt'])) $activeTab = trim($_REQUEST['tabOpt']);
if (isset($_REQUEST['lId'])) $LMRId = trim(cypher::myDecryption(trim($_REQUEST['lId'])));
if (isset($_REQUEST['cliType'])) $cliType = trim($_REQUEST['cliType']);
if (isset($_REQUEST['moduleCode'])) $moduleCode = trim($_REQUEST['moduleCode']);
LMRequest::$moduleCode = $moduleCode;

if ($publicUser != 1) {
    $executiveId = 0;
    if (isset($_REQUEST['eId'])) $executiveId = trim(cypher::myDecryption(trim($_REQUEST['eId'])));
}

/*
* Object file we need to include start
* Please don't add anything in between below lines
*/
LMRequest::setLMRId($LMRId);
Property::init($LMRId);
if ($LMRId > 0) {
    $getMyFileInfo = getMyFileInfo::getReport($LMRId);                                        // Get File Details..

    insertLastView::getReport($LMRId);  // Latest opened

    $PCID = $getMyFileInfo[$LMRId]['FPCID'];

    if ($userGroup == 'Super') {
        $exInArray['TABLENAME'] = 'tblProcessingCompany';
        $exResultArray = [];
        $exInArray['FIELDNAMEARRAY'] = ['client_id'];
        $exInArray['CONDITION'] = ' where activeStatus = 1 and dstatus = 0 and PCID = ' . $PCID;

        $exResultArray = Database2::getInstance()->getSelectedFieldsForFile($exInArray);
        $pcClient_id = trim($exResultArray['client_id']);
    }
    /**
     * Get borrower number of deals.
     */
    //$borrowerNumberOfDeals = getBorrowerNumberOfDeals::getReport(Arrays::getArrayValue('clientId', $getMyFileInfo[$LMRId]), $PCID);
    $borrowerNumberOfDeals = getBorrowerNumberOfDeals::getBorrowerAllNumberOfDeals(Arrays::getArrayValue('borrowerEmail', $getMyFileInfo[$LMRId]), $PCID);
    /**
     * Write a condition, that only display the 3rd option, if the system has more than one loan file active.
     */
    $confirmType = '';
    if (Arrays::getArrayValue('numberOfDeals', $borrowerNumberOfDeals) == 1 && $allowToupdateFileAndClient != '') {
        $allowToupdateFileAndClient = '';
        $confirmType = 'Update current loan file and borrower profile';
    }

    $propertyNeedRehab = $getMyFileInfo[$LMRId]['propertyNeedRehab'];

    $moduleFileTabInfo = LoanMenu::$moduleFileTabInfo ?: getFileModules::getReport(['fileID' => $LMRId]);
    $fileModuleInfo = [];
    if (isset($moduleFileTabInfo[$LMRId])) {
        $fileModuleInfo = $moduleFileTabInfo[$LMRId];
    }
    $apStr = '';
    foreach ($fileModuleInfo as $i => $item) {
        $ftModuleCode .= $apStr . $item['moduleCode'];
        $apStr = ',';
    }

} else {
    $ftModuleCode = $moduleCode;
    /* https://www.pivotaltracker.com/story/show/161162469 */
    if (trim($cliType) == 'SLM') {
        $activeTab = 'SLM'; // Student Loan Mod File
    } elseif (!($_REQUEST['tabOpt'] ?? null)) {
        $activeTab = 'CI';
    }
}

/*   
** Description	: Dynamically displayed the Tab Name
** Auther		: AwataSoftSys
** Developer	: Viji & Venky
** Date			: Sep 15, 2017

*/
$moduleFileTabInfo = getFileTabsForPCModules::getReport(['PCID' => $PCID, 'userGroup' => $userGroup, 'moduleCode' => $ftModuleCode, 'isPublic' => $publicUser]);
unset($moduleFileTabInfo['SER']); // deprecated

$fileTabs = $moduleFileTabInfo;

if ($userGroup == 'Client' && $PCID == 3866) {  //3866 is loanguys (ticket is sc-24934) **exception**
    $fileTabs = ['DOC' => 'Docs', 'LI' => 'Full App', 'CON' => 'Contacts'];
}
if (count($fileTabs) > 0) {
    if ($propertyNeedRehab != 'Yes' && isset($fileTabs['BD'])) unset($fileTabs['BD']); // https://www.pivotaltracker.com/story/show/*********
    if ($pcClient_id == '' || $allowPeerstreet != 1) unset($fileTabs['PE']);
    if (!$allowToSeeAlowareTab > 0) unset($fileTabs['AW']);
    if (!(($thirdPartyServices > 0 && $glThirdPartyServices > 0) || PageVariables::$glThirdPartyServicesLegalDocs > 0)) unset($fileTabs['TPS']);
    if ($userGroup == 'Client' && glCustomJobForProcessingCompany::isHideBorrowerPortal($PCID)) {
        unset($fileTabs['HMLI']);
    }
    if (!in_array($PCID, glCustomJobForProcessingCompany::$glCustomTabDealSizerCommercial)) {
        unset($fileTabs['DS']);
    }
    if (!glCustomJobForProcessingCompany::showLoanInfoV2($PCID)) {
        unset($fileTabs['LIV2']);
    }

    if (glCustomJobForProcessingCompany::hideTabsForBorrowerPortal($PCID) && $userGroup == 'Client') {
        unset($fileTabs['CW']);
        unset($fileTabs['BD']);
        unset($fileTabs['HMLI']);
        unset($fileTabs['CI']);
        unset($fileTabs['PI']);
        unset($fileTabs['CON']);
        unset($fileTabs['DA']);
        unset($fileTabs['EM']);
    }

    $fileTabsKey = array_keys($fileTabs);
    $tabCheckArray = ['MP', 'HR', 'SER2', 'AW', 'PE', 'SO', 'PEN'];
    for ($k = 0; $k < count($fileTabsKey); $k++) {
        if ($activeTab == '') {
            if (in_array($fileTabsKey[$k], $tabCheckArray)) {
                switch ($fileTabsKey[$k]) {
                    case 'MP':
                        if ($allowPCUsersToMarketPlace == '1' && $allowToViewMarketPlace == '1') {
                            $activeTab = $fileTabsKey[$k];
                        }
                        break;
                    case 'HR':
                        if ($subscribePCToHOME == '1' && $subscribeToHOME == '1') {
                            $activeTab = $fileTabsKey[$k];
                        }
                        break;
                    case 'SER2':
                        if ($allowServicing == '1') {
                            $activeTab = $fileTabsKey[$k];
                        }
                        break;
                    case 'AW':
                        if ($allowToSeeAlowareTab == '1' && $isPCActiveAloware == '1') {
                            $activeTab = $fileTabsKey[$k];
                        }
                        break;
                    case 'PE':
                        if ($allowPeerstreet == '1') {
                            $activeTab = $fileTabsKey[$k];
                        }
                        break;
                    case 'SO':
                        if ($viewSubmitOfferTab == '1') {
                            $activeTab = $fileTabsKey[$k];
                        }
                        break;
                    case 'PEN':
                        if ($pcPriceEngineStatus == 1 && $userPriceEngineStatus == 1) {
                            $activeTab = $fileTabsKey[$k];
                        }
                        break;
                }
            } else {
                $activeTab = $fileTabsKey[$k];
                break;
            }
        } else {
            break;
        }
    }
    // https://www.pivotaltracker.com/story/show/160146292
}
if ((!isset($_REQUEST['tabOpt'])) && $publicUser != 1) {
    $_REQUEST['tabOpt'] = $activeTab;
}
if (trim($userGroup) == 'Auditor') $activeTab = 'LA';                /* Auditor User active tab */

$tabsForNewFile = [];
if (!$LMRId) {

    $tabsForNewFile[$activeTab] = $fileTabs[$activeTab];
    $fileTabs = array_intersect($tabsForNewFile, $fileTabs);
}

//$inFileArray[] = CONST_ROOT_PATH . "models/oFileUpdate.php";
//$inFileArray[] = CONST_ROOT_PATH . "models/oLockFile.php";


//$inFileArray[] = CONST_ROOT_PATH . 'includes/proposalFormula.php';


//$inFileArray[] = CONST_ROOT_PATH . "models/oUser.php";
//$inFileArray[] = CONST_ROOT_PATH . "models/oCFPBAudit.php";
//$inFileArray[] = CONST_ROOT_PATH . "models/oFileTabs.php";
//$inFileArray[] = CONST_ROOT_PATH . "models/oChecklist.php";


/** REST tab **/
//if ($activeTab == 'IE') $inFileArray[] = CONST_ROOT_PATH . 'models/oIncExp.php';
/** Income & Expenses tab **/
//if ($activeTab == 'AL') $inFileArray[] = CONST_ROOT_PATH . 'models/oIncExp.php';
/** Income & Expenses tab **/
//if ($activeTab == 'HA') $inFileArray[] = CONST_ROOT_PATH . 'models/oFileHardship.php';
/** Hardship tab **/
//if ($activeTab == 'TA') {
//    $inFileArray[] = CONST_ROOT_PATH . 'models/oTask.php';
//    /** Task tab **/
//    $inFileArray[] = CONST_ROOT_PATH . 'models/oWorkflow.php';
//    /** Task tab **/
//}
//if ($activeTab == 'CW' || $activeTab == 'ADMIN') $inFileArray[] = CONST_ROOT_PATH . 'models/oWorkflow.php';
///** Workflow tab **/
//if ($activeTab == 'BC') $inFileArray[] = CONST_ROOT_PATH . 'models/oBilling.php';
/** BC tab **/
//if ($activeTab == 'ADMIN' || $activeTab == 'LI' || $activeTab == 'QAPP' || $activeTab == 'CI') $inFileArray[] = CONST_ROOT_PATH . 'models/oSubstatus.php';
/** Admin tab **/
//if ($activeTab == 'DOC') {
//    /** Doc tab **/

//    $inFileArray[] = CONST_ROOT_PATH . "models/oCustomDocs.php";
//    $inFileArray[] = CONST_ROOT_PATH . "models/oPackage.php";
//    $inFileArray[] = CONST_ROOT_PATH . "models/oTrust.php";
//    $inFileArray[] = CONST_ROOT_PATH . "models/oFax.php";
//    $inFileArray[] = CONST_ROOT_PATH . "models/oModules.php";
//}
//if ($activeTab == 'SP') {
//    /** SSProposal tab **/
//    $inFileArray[] = CONST_ROOT_PATH . 'models/oSSProposal.php';
//}
if ($activeTab == 'LSS') {
    /** Loan Servicing Summary tab **/
    $inFileArray[] = CONST_ROOT_PATH . 'includes/loanServicingSummaryFormula.php';
//    $inFileArray[] = CONST_ROOT_PATH . 'models/oLoanServicing.php';
}

for ($i = 0; $i < count($inFileArray); $i++) {
    require $inFileArray[$i];
}
//if ($activeTab == 'ADMIN' || $activeTab == 'LI' || $activeTab == 'CI' || $activeTab == 'QAPP') $oSubstatus = new oSubstatus();

/** Object file we need to include end **/

$titleBorName = '';
$fileInfo = [];
$stateArray = [];
$myFileInfo = [];
$borrowerName = '';
$coBorrowerName = '';
$LMRResponseId = 0;
$brokerNumber = 0;
$secondaryBrokerNumber = 0;
$editOpt = '';
$copyOpt = '';
$createdBy = '';
$createdUserType = '';
$clientId = 0;
$allowToEdit = false;
$PCStatusInfo = [];
$servicesRequested = [];
$tempBranchList = [];
$branchList = [];
$agentList = [];
$branchIds = '';
$agentIds = '';
$empIds = '';
$empChkInfoArray = [];
$fileWorkflowArray = [];
$PCWFServiceTypeArray = [];
$PCWFStepsArray = [];
$fileWFStepsArray = [];
$LMRBranchChkInfoArray = [];
$workflowArray = [];
$op = '';
$employeeList = [];
$fileTaskInfo = [];
$LMRClientTypeInfo = [];
$tempRESTReportArray = [];
$loggedEmpInfoArray = [];
$fileSubstatusInfo = [];
$proposalInfoArray = [];
$AssignedBranches = [];
$propertyAddress = '';
$AssignedBranchesInfo = [];
$assignedBranchesID = '';
$showSSTab = $showSysGenNote = 0;
$isSLM = 0;
$isSLMOnly = 0;
$oldFPCID = 0;
$isLM = 0;
$isLOC = 0;
$isSysNotesPrivate = 0;
$tempClientEmail = '';

$dummyBrokerInfo = [];
$dummyBrokerId = 0;

$moduleRequested = [];
$fileModuleInfo = [];
$isHOALien = 0; /* HOA Lien Services - Selected in the file */
$isLO = 0; /* Loan Origination Services - Selected in the file */
$isMF = 0; /* Merchant Funding Services - Selected in the file */
$isFU = 0; /* Funding Module - Selected in the file */
$isLSR = 0; /* Loan Servicing Module - Selected in the file */
$isHMLO = 0;  /* Hard / Private Money LOS Module - Selected in the file */
$isEF = 0;  /* Equipment Financing - EF Module - Selected in the file (PT-#*********)*/
$isLOC = 0;    /* Line Of Credit. - LOC Module - Selected in the file (PT-#159946019) - August 27, 2018 */
$addedToFav = 0;

$processorCommentsArray = [];
$fileResInfo = [];

if (isset($_REQUEST['bId'])) $brokerNumber = trim(cypher::myDecryption(trim($_REQUEST['bId'])));
if (isset($_REQUEST['op'])) $op = trim(cypher::myDecryption(trim($_REQUEST['op'])));

if ($userRole == 'Branch') $executiveId = trim(PageVariables::$userNumber);
else if ($userRole == 'Agent') $brokerNumber = trim(PageVariables::$userNumber);

if ($eOpt == 0) {
    $op = 'create';
}

$inArray['LMRId'] = $LMRId;
$inArray['userNo'] = PageVariables::$userNumber;
$inArray['userRole'] = $userGroup;

if ($LMRId > 0) {
} else {
    $inArray['PN'] = 'createfiles';
    if ($publicUser == 1) {
    } else {
        UserAccess::checkPageAccess($inArray);
    }
}

if ($LMRId > 0) {
    $ip = [
        'PCID' => $PCID,
        'userGroup' => $userGroup,
        'userNumber' => PageVariables::$userNumber,
        'executiveId' => $executiveId,
        'LMRId' => $LMRId,
        'fetchTab' => $activeTab,
        'viewPrivateNotes' => $viewPrivateNotes,
        'viewPublicNotes' => $viewPublicNotes,

    ];
    $fileInfo = getFileInfo::getReport($ip);

}

$stateArray = Arrays::fetchStates();
/** Fetch all States **/

if (count($fileInfo) > 0) {
    if (array_key_exists($LMRId, $fileInfo)) {
        $myFileInfo = $fileInfo[$LMRId];
    }
    $addedToFav = $myFileInfo['addedToFav'];
    $executiveId = Strings::showField('FBRID', 'LMRInfo');
    $LMRResponseId = Strings::showField('LMRResponseId', 'ResponseInfo');
    if (array_key_exists('LMRClientTypeInfo', $myFileInfo)) {
        if (count($myFileInfo['LMRClientTypeInfo']) > 0) {
            $LMRClientTypeInfo = $myFileInfo['LMRClientTypeInfo'][$LMRId];
            LMRequest::$loanProgram = $LMRClientTypeInfo[0]['ClientType'];
        }
    }
    if (array_key_exists('branchClientTypeInfo', $myFileInfo)) $servicesRequested = $myFileInfo['branchClientTypeInfo'];
    if (array_key_exists('fileModuleInfo', $myFileInfo)) {
        if (array_key_exists($LMRId, $myFileInfo['fileModuleInfo'])) $fileModuleInfo = $myFileInfo['fileModuleInfo'][$LMRId];
    }

    if (array_key_exists('SBABackground', $myFileInfo)) {
        $SBABackground = $myFileInfo['SBABackground'];
        if (count($SBABackground) > 0) {
            unset($SBABackground['SBAID']);
            unset($SBABackground['fileID']);
            unset($SBABackground['CID']);
            unset($SBABackground['recordDate']);
            foreach ($SBABackground as $sbKey => $sbVal) {
                ${$sbKey} = $sbVal;
            }
        }
    }

    /* SBA Other Business */
    if (array_key_exists('sbaOtherBusinessData', $myFileInfo)) {
        $sbaOtherBusinessData = $myFileInfo['sbaOtherBusinessData'];
    }
    /* SBA Other Business */
    /** Fetch file modules **/
    if (array_key_exists('branchModuleInfo', $myFileInfo)) $moduleRequested = $myFileInfo['branchModuleInfo'];

    if (array_key_exists('fileSubstatusInfo', $myFileInfo)) $fileSubstatusInfo = $myFileInfo['fileSubstatusInfo'];
    /** Fetch all file sub-status **/
    if (array_key_exists('processorComments', $myFileInfo)) $processorCommentsArray = $myFileInfo['processorComments']();

    $propertyAddress = Property::$primaryPropertyInfo->propertyAddress;
    $borrowerName = Strings::showField('borrowerFName', 'LMRInfo') . ' ' . Strings::showField('borrowerLName', 'LMRInfo');
    $coBorrowerName = Strings::showField('coBorrowerFName', 'LMRInfo') . ' ' . Strings::showField('coBorrowerLName', 'LMRInfo');
    if ($userGroup == 'Super' || $userGroup == 'Auditor' || $userGroup == 'Agent' || $userGroup == 'Client') $PCID = Strings::showField('FPCID', 'LMRInfo');
    $titleBorName = $borrowerName . ' - ';
    $borrowerFullName = '';
    $borrowerFullName = $borrowerName;
    $oldFPCID = Strings::showField('oldFPCID', 'LMRInfo');

    $isSysotesPrivate = Strings::showField('isSysNotesPrivate', 'PCInfo');
    $showSysGenNote = Strings::showField('showSysGenNote', 'PCInfo');

}

if ($publicUser == 1) {
} else {

    $ipArray['LMRId'] = $LMRId;
    $ipArray['userNo'] = PageVariables::$userNumber;
    $ipArray['userRole'] = $userRole;
    $ipArray['userGroup'] = $userGroup;
    $ipArray['allowCFPBAuditing'] = $allowCFPBAuditing;
    if ($userGroup == 'Client') {
        $CPwd = '';
        if (isset($_SESSION['userPwd'])) {
            $CPwd = trim($_SESSION['userPwd']);
        }
        $ipArray['clientEmail'] = $userEmail;
        $ipArray['clientPwd'] = $CPwd;
    }

    if (!checkFileAccess::getReport($ipArray)) {
        header('Location: ' . CONST_SITE_URL . 'invalidFileAccess.php');
        exit();
    }
}
if (trim($cliType != '') && $LMRId == 0) $LMRClientTypeInfo[0]['ClientType'] = $cliType;
if (trim($moduleCode != '') && $LMRId == 0) $fileModuleInfo[0]['moduleCode'] = $moduleCode; /* Default Module Select */
if (count($fileModuleInfo) > 0) {
    if (array_key_exists('moduleName', $fileModuleInfo[0])) $moduleName = $fileModuleInfo[0]['moduleName']; /* Default Module Select */
}

$inArray['LMRId'] = $LMRId;


$glLMRClientTypeArray = getServiceTypes::getReport(['activeStatus' => '1']);

if ($activeTab == 'BC' && $allowToEditCommission == '0') {
    if ($userGroup == 'Employee') {
        $ip['commissionUserId'] = PageVariables::$userNumber;
        $ip['commissionUserType'] = 'Attorney/Employee';
    } else if ($userGroup == 'Branch') {
        $ip['commissionUserId'] = PageVariables::$userNumber;
        $ip['commissionUserType'] = 'Branch/AE';
    } else if ($userGroup == 'Agent') {
        $ip['commissionUserId'] = PageVariables::$userNumber;
        $ip['commissionUserType'] = 'Agent';
    }
}

$clientFileListArray = [];
$PCClientBackgroundInfoArray = [];
$PCClientExperienceInfoArray = [];
if (in_array($activeTab, ['CI', 'LI', 'SSS', 'BC', 'SLM', 'INT', 'QAPP', 'CT'])) {

    if ($userRole == 'Super') {
        $PCListArray = getPCList::getReport(['userRole' => $userRole, 'activeStatus' => '1']);
    }

    $ip = ['PCID' => $PCID];
    if ($userRole == 'Client' && $allowClientToCreateHMLOFile == 1) { /* Allow client to create HMLO file - Mar 30, 2017 */
        $tempBRID = $tempAGID = 0;

        $clientFileListArray = getClientFileBranchAndAgent::getReport(['PCID' => $PCID, 'CID' => PageVariables::$userNumber]);
        $PCClientBackgroundInfoArray = getPCClientBackgroundInfo::getReport(['PCID' => $PCID, 'CID' => PageVariables::$userNumber]);
        $PCClientExperienceInfoArray = getPCClientExperienceInfo::getReport(['PCID' => $PCID, 'CID' => PageVariables::$userNumber]);
        if ($PCClientExperienceInfoArray > 0) $PCClientExperienceInfoArray = $PCClientExperienceInfoArray['clientBackExpInfo'][0];

        if (count($clientFileListArray) > 0) {
            $tempAGID = $clientFileListArray['AGID'];
            $tempBRID = $clientFileListArray['BRID'];
            $tempClientEmail = $clientFileListArray['borrowerEmail'];
        }
        if ($tempClientEmail == '' && $userRole == 'Client') {
            $tempClientEmail = $exResultArray['loggedInEmail'];
        }
        $ip['agentId'] = $tempAGID;
        $ip['execID'] = $tempBRID;
    }


    if ($userRole == 'Agent') {
        $ip['brokerNumber'] = PageVariables::$userNumber;
        $branchList = getBranchesForAgent::getReport($ip);
        /** Fetch Branches assigned to Agents **/
    } else {
        if ($userGroup == 'Employee') {
            $AssignedBranches = getEmployeeAssignedBranches::getReport(['EID' => PageVariables::$userNumber]);
            if (array_key_exists(PageVariables::$userNumber, $AssignedBranches)) {
                $AssignedBranchesInfo = $AssignedBranches[PageVariables::$userNumber];
            }
            for ($i = 0; $i < count($AssignedBranchesInfo); $i++) {
                if ($i > 0) {
                    $assignedBranchesID .= ', ';
                }
                $assignedBranchesID .= trim($AssignedBranchesInfo[$i]['LMRAEID']);
            }
            if ($assignedBranchesID != '') {
                if (in_array(PageVariables::$userNumber, $glAllowEmpToUpdateBranchsForFiles) && $userGroup == 'Employee') {
                } else {
                    $ip['execID'] = $assignedBranchesID;
                }
            }
        }
        $ip['moduleCode'] = trim($moduleCode);
        $tempBranchList = getBranches::getReport($ip);
        /** Fetch Branches assigned to PC **/
        if (count($tempBranchList) > 0) {
            if (array_key_exists('branchList', $tempBranchList)) {
                $branchList = $tempBranchList['branchList'];
            }
        }
    }
    if ($userRole == 'Branch') {
        $ip['executiveId'] = PageVariables::$userNumber;
        $ip['opt'] = 'list';
        $agentList = listAllAgents::getReport($ip);
    } else if ($userRole == 'Agent' && $externalBroker == 1) {
        $ip['opt'] = 'list';
        if ($PCID > 0) {
            $branchListForLoanOfficer = Arrays::buildKeyByValue($branchList, 'executiveId');
            if ($LMRId > 0) {
                $ip['branchIds'] = $executiveId;
            } else {
                $ip['branchIds'] = implode(',', array_keys($branchListForLoanOfficer));
            }
            $ip['externalBroker'] = 0;
            $ip['loanOfficerId'] = PageVariables::$userNumber;
            $brokerId = 0;
            if (trim($myFileInfo['BrokerInfo']['email']) != trim($PCID) . '@dummyAgentemail.com') {
                $brokerId = Strings::showField('brokerNumber', 'LMRInfo');
            }
            if ($LMRId && $brokerId) {
                $ip['fileBrokerId'] = $brokerId;
                $brokerAssosiateLOData = tblBrokerAssociatedLoanOfficers::getData($brokerId,PageVariables::$userNumber);
                if(!count($brokerAssosiateLOData)){
                    $brokerAssosiateLOData = tblBrokerAssociatedLoanOfficers::getData($brokerId);
                    $assignedLoArray = [];
                    $assignedLoArray = array_column($brokerAssosiateLOData,'loanOfficerId');
                    array_push($assignedLoArray,PageVariables::$userNumber); $assignedLoArray = array_values(array_unique($assignedLoArray));
                    $tblBrokerAssociatedLoanOfficers = new tblBrokerAssociatedLoanOfficers();
                    $updateCount = $tblBrokerAssociatedLoanOfficers->saveData($assignedLoArray, $brokerId);
                }
            }
            $ip['allowToSeeAllBrokers'] = $allowToSeeAllBrokers;
            if ($userGroup == 'Agent') {
                /** For Agents assigned to PC's Branches **/
                $ip['userGroup'] = $userGroup;
            }
            $ip['PCID'] = $PCID;
            $agentList = listAllAgentsLoanOfficer::getReport($ip);
        }
    } else {
        $ip['opt'] = 'list';
        if ($PCID > 0) {
            if ($userGroup == 'Employee') {
                $ip['executiveId'] = $executiveId;
                /** For Agents assigned to PC's Branches **/
                $ip['userGroup'] = $userGroup;
            }
            $agentList = listAllAgents::getReport($ip);
        }
    }
    $employeeList = getPCEmployeeList::getReport($ip);

    $dummyBrokerInfo = getBrokerInfo::getReport(['mail' => $PCID . '@dummyAgentemail.com']);
    if (isset($dummyBrokerInfo['userNumber'])) {
        $dummyBrokerId = ($dummyBrokerInfo['userNumber']);
    }
    foreach ($agentList as $row) {
        LMRequest::$agentList[] = new getPCAgents($row);
    }
}

if ($LMRId == 0 && ($activeTab == 'CI' || $activeTab == 'DOC' || $activeTab == 'SLM' || $activeTab == 'LI' || $activeTab == 'QAPP')) {

    $ip = ['PCID' => $PCID, 'opt1' => 'list', 'keyNeeded' => 'n', 'userGroup' => $_SESSION['userGroup']];
    $PCStatusInfo = getPCPrimaryStatus::getReport($ip);
    /** Fetch PC primary status **/
    if (array_key_exists('primaryStatusInfo', $PCStatusInfo)) {
        $PCStatusInfo = $PCStatusInfo['primaryStatusInfo'];
    }

    if ($userGroup == 'Branch' && PageVariables::$userNumber > 0) {
        $ip = ['branchID' => PageVariables::$userNumber, 'moduleCode' => $moduleCode];
        $ip['keyNeeded'] = 'n';
        $servicesRequested = getBranchServices::getReport($ip);
        /** Fetch Branch services requested **/
        if (array_key_exists(PageVariables::$userNumber, $servicesRequested)) $servicesRequested = $servicesRequested[PageVariables::$userNumber];

        $moduleRequested = getBranchModules::getReport($ip);
        /** Fetch Branch Modules requested **/
        if (array_key_exists($executiveId, $moduleRequested)) $moduleRequested = $moduleRequested[$executiveId];
    } else if ($publicUser == 1) {
        $ip = ['branchID' => $executiveId, 'moduleCode' => $moduleCode];
        $ip['keyNeeded'] = 'n';
        $servicesRequested = getBranchServices::getReport($ip);
        /** Fetch Branch services requested for iframe users **/
        if (array_key_exists($executiveId, $servicesRequested)) $servicesRequested = $servicesRequested[$executiveId];
        $moduleRequested = getBranchModules::getReport($ip);
        /** Fetch Branch Modules requested **/
        if (array_key_exists($executiveId, $moduleRequested)) $moduleRequested = $moduleRequested[$executiveId];
    } else {
        $ip['keyNeeded'] = 'n';
        $ip['moduleCode'] = $moduleCode;
        if ($PCID > 0) {
            $servicesRequested = getPCServiceType::getReport($ip);
            /** Fetch PC services requested **/

            $ip['keyNeeded'] = 'n';
            $moduleRequested = getPCModules::getReport($ip);
            /** Fetch PC Modules requested **/
        }

    }
}

$internalLoanProgramList = [];

global $fileLMRInternalLoanprograms;

$fileLMRInternalLoanprograms = array_merge($myFileInfo['LMRInternalLoanprograms'] ?? [], $myFileInfo['LMRadditionalLoanprograms'] ?? []);

$ip['keyNeeded'] = 'n';
if ($moduleCode != '') {
    $ip['moduleCode'] = $moduleCode;
} else {
    $ip['moduleCode'] = trim($ftModuleCode);
}
if ($userGroup == 'Employee' || $userGroup == 'Super' || ($userGroup == 'Branch' && $allowToAccessInternalLoanProgram == 1) || ($userGroup == 'Agent' && $allowToAccessInternalLoanProgram == 1)) {
    $internalLoanProgramList = getPCInternalServiceType::getReport($ip);
}

/* Checklist Info */
$ClientTypes = '';
if ($activeTab == 'DOC') {
    if (count($myFileInfo) > 0) {
        if (array_key_exists('LMRChecklistInfo', $myFileInfo)) $LMRChecklistInfo = $myFileInfo['LMRChecklistInfo'];
        $WFCnt = count($LMRClientTypeInfo);
        for ($w = 0; $w < $WFCnt; $w++) {
            $PCServiceType = '';
            $tempClientArray = [];
            $ClientType = trim($LMRClientTypeInfo[$w]['ClientType']);
            if ($w > 0) $ClientTypes .= ', ';
            $ClientTypes .= "'" . trim($LMRClientTypeInfo[$w]['ClientType']) . "'";
        }
    }
    $tempArary = array_keys($LMRChecklistInfo);
    $et = 0;
    $br = 0;
    $ag = 0;
    for ($s2 = 0; $s2 < count($tempArary); $s2++) {
        $updatedBy = '';
        $updatedUserType = '';
        $updatedBy = $LMRChecklistInfo[$tempArary[$s2]]['updatedBy'];
        $updatedUserType = $LMRChecklistInfo[$tempArary[$s2]]['updatedUserType'];

        if ($updatedUserType == 'Employee') {
            if ($et > 0) {
                $empIds .= ', ';
            }
            $et++;
            $empIds .= $updatedBy;
        }
        if ($updatedUserType == 'Branch') {
            if ($br > 0) {
                $branchIds .= ', ';
            }
            $br++;
            $branchIds .= $updatedBy;
        }
        if ($updatedUserType == 'Agent') {
            if ($ag > 0) {
                $agentIds .= ', ';
            }
            $ag++;
            $agentIds .= $updatedBy;
        }
    }
    $empChkInfoArray = \models\composite\oEmployee\getMyDetails::getReport(['empId' => $empIds]);
    $LMRBranchChkInfoArray = \models\composite\oBranch\getMyDetails::getReport(['executiveId' => $branchIds]);
}
$LMRInternalLoanprogramsArray = [];
$LMRInternalLoanprograms = '';
$LMRInternalLoanprogramsSingleQuote = '';
$ServiceTypeInternalLoanProgramsSingelQuote = '';
$ServiceTypeInternalLoanPrograms = '';

$LMRInternalLoanprogramsArray = $myFileInfo['LMRInternalLoanprograms'] ?? null;
if (count($LMRInternalLoanprogramsArray ?? []) > 0) {
    for ($LMRiL = 0; $LMRiL < count($LMRInternalLoanprogramsArray); $LMRiL++) {
        if ($LMRiL > 0) {
            $LMRInternalLoanprograms .= ',';
            $LMRInternalLoanprogramsSingleQuote .= ',';
        }
        $LMRInternalLoanprogramsSingleQuote .= "'" . trim($LMRInternalLoanprogramsArray[$LMRiL]) . "'";
        $LMRInternalLoanprograms .= trim($LMRInternalLoanprogramsArray[$LMRiL]);
    }
}

$LMRadditionalLoanprograms = '';
$LMRadditionalLoanprogramsSingleQuote = '';
$LMRadditionalLoanprogramsArray = $myFileInfo['LMRadditionalLoanprograms'] ?? null;
if (count($LMRadditionalLoanprogramsArray ?? []) > 0) {
    for ($LMRiL = 0; $LMRiL < count($LMRadditionalLoanprogramsArray); $LMRiL++) {
        if ($LMRiL > 0) {
            $LMRadditionalLoanprograms .= ',';
            $LMRadditionalLoanprogramsSingleQuote .= ',';
        }
        $LMRadditionalLoanprogramsSingleQuote .= "'" . trim($LMRadditionalLoanprogramsArray[$LMRiL]) . "'";
        $LMRadditionalLoanprograms .= trim($LMRadditionalLoanprogramsArray[$LMRiL]);
    }
}

if ($activeTab == 'CW') {
    for ($w = 0; $w < count($LMRClientTypeInfo); $w++) {
        $PCServiceType = '';
        $tempClientArray = [];
        $ClientType = trim($LMRClientTypeInfo[$w]['ClientType']);
        if ($w > 0) $ClientTypes .= ', ';
        $ClientTypes .= "'" . trim($LMRClientTypeInfo[$w]['ClientType']) . "'";
        $WFLMRIDServiceType .= trim($LMRClientTypeInfo[$w]['ClientType']);
    }

    $ServiceTypeInternalLoanProgramsSingelQuote = $ClientTypes;
    if ($LMRInternalLoanprogramsSingleQuote != '') {
        $ServiceTypeInternalLoanProgramsSingelQuote = $ClientTypes . ',' . $LMRInternalLoanprogramsSingleQuote;
    }
    $ServiceTypeInternalLoanPrograms = $WFLMRIDServiceType;
    if ($LMRInternalLoanprograms != '') {
        $ServiceTypeInternalLoanPrograms = $WFLMRIDServiceType . ',' . $LMRInternalLoanprograms;
    }
    if ($LMRadditionalLoanprograms != '') {
        $ServiceTypeInternalLoanPrograms = $ServiceTypeInternalLoanPrograms . ',' . $LMRadditionalLoanprograms;

    }

    $workflowArray = getFileWorkflow::getReport(['WFServiceType' => $ClientTypes,
        'LMRID' => $LMRId,
        'PCID' => $PCID,
        'userRole' => $userRole,
        'WFInternalLoanProgram' => $LMRInternalLoanprogramsSingleQuote,
        'WFAdditionalLoanProgram' => $LMRadditionalLoanprogramsSingleQuote]);

    if (count($workflowArray) > 0) {
        $fileWorkflowArray = $workflowArray['fileWorkflow'];
        $PCWFServiceTypeArray = $workflowArray['PCWFServiceType'];
        $PCWFStepsArray = $workflowArray['PCWFSteps'];
    }
    $fileWFStepsArray = getFileWFSteps::getReport(['LMRID' => $LMRId]);
}
/* Checklist Info */


/* Checklist Info */
if ($activeTab == 'DOC') {
    $processingCompanyInfoArray = [];

    if (count($myFileInfo) > 0) {
        if (array_key_exists('LMRClientTypeInfo', $myFileInfo)) {
            if (array_key_exists($LMRId, $myFileInfo['LMRClientTypeInfo'])) $LMRClientTypeInfo = $myFileInfo['LMRClientTypeInfo'][$LMRId];
        }
        /** Fetch file services **/

        if (array_key_exists('branchClientTypeInfo', $myFileInfo)) $servicesRequested = $myFileInfo['branchClientTypeInfo'];
        /** Fetch all Branch services requested **/
    }
// Binder Start
    $binderDocsArray = [];
    $eSignDocIds = '';
    $dIds = '';
    $binderDocsNameArray = [];
    $d1 = 0;
    $d2 = 0;
    $esignedDocsArray = [];
    $uploadDocsArray = [];

    $inArray = ['LMRId' => $LMRId];

    $binderDocsArray = getBinderDocument::getReport($inArray);

    $binderDocsKeysArray = [];
    $binderDocsKeysArray = array_keys($binderDocsArray);

    for ($bd = 0; $bd < count($binderDocsKeysArray); $bd++) {
        $dId = 0;
        $eSignDocId = 0;
        if (array_key_exists('eSignDoc', $binderDocsArray[$binderDocsKeysArray[$bd]])) {
            $eSignDocId = $binderDocsArray[$binderDocsKeysArray[$bd]]['eSignDoc']['docIds'];
            if ($eSignDocId != '') {
                if ($d1 > 0) $eSignDocIds .= ',';
                $eSignDocIds .= $eSignDocId;
                $d1++;
            }
        }
        if (array_key_exists('userDoc', $binderDocsArray[$binderDocsKeysArray[$bd]])) {
            $dId = $binderDocsArray[$binderDocsKeysArray[$bd]]['userDoc']['docIds'];
            if ($dId != '') {
                if ($d2 > 0) $dIds .= ',';
                $dIds .= $dId;
                $d2++;
            }
        }
    }
    if ($d1 > 0 || $d2 > 0) {
        $inArray['dIds'] = $dIds;
        $inArray['eSignDocIds'] = $eSignDocIds;
        $inArray['LMRId'] = $LMRId;
        $binderDocsNameArray = getBinderDocName::getReport($inArray);
        if (count($binderDocsNameArray) > 0) {
            $esignedDocsArray = $binderDocsNameArray['result1'];
            $uploadDocsArray = $binderDocsNameArray['result2'];
        }
    }

// Binder End
    $inputArray = ['executiveId' => $executiveId,
        'attachWithEmail' => '',
        'publishInPQPortal' => '',
        'publishDoument' => '1',

    ];

    $branchDocArray = getBranchUploadedDocuments::getReport($inputArray);

}
/* Doc Info */

/* Task Info */
if ($activeTab == 'TA') {
    $ip['isAutomationTask'] = 0; //get only normal tasks
    $fileTaskInfo = getFileTasks::getReport($ip);
}
/* Task Info */

/* Billing Reminder Info */
if ($activeTab == 'BC') {
    $reminderInfo = [];
    $agentIDArray = [];
    $branchIDArray = [];
    $empIDArray = [];

    $reminderInfo = getBillingReminderInfoForFile::getReport(['LMRId' => $LMRId]);
    if (count($reminderInfo) > 0) {
        $agentIDArray = $reminderInfo['agentIDArray'];
        $branchIDArray = $reminderInfo['LMRAEIDArray'];
        $empIDArray = $reminderInfo['empIDArray'];
    }
    $empInfoArray = [];
    $branchInfoArray = [];
    $agentInfoArray = [];
    $clientInfoArray = [];
    if (count($empIDArray) > 0) {
        $ip = ['empId' => implode(',', $empIDArray)];
        $empInfoArray = \models\composite\oEmployee\getMyDetails::getReport($ip);
    }
    if (count($branchIDArray) > 0) {
        $ip = ['executiveId' => implode(',', $branchIDArray)];
        $branchInfoArray = \models\composite\oBranch\getMyDetails::getReport($ip);
    }
    if (count($agentIDArray) > 0) {
        $ip = ['agentId' => implode(',', $agentIDArray)];
        $agentInfoArray = \models\composite\oBroker\getMyDetails::getReport($ip);
    }

}
/* Billing Reminder Info */

if (Strings::showField('primeStatusId', 'ResponseInfo') > 0) {
    $exResultArray = [];
    $exInArray['TABLENAME'] = 'tblPCPrimeStatus';
    $exInArray['FIELDNAMEARRAY'] = ['primaryStatus'];
    $exInArray['CONDITION'] = " where PSID = '" . Strings::showField('primeStatusId', 'ResponseInfo') . "'";
    $exResultArray = Database2::getInstance()->getSelectedFieldsForFile($exInArray);
    if (count($exResultArray) > 0) $fileStatus = trim($exResultArray['primaryStatus']);
}
if ($activeTab == 'ADMIN') {
    $subStatuaCnt = 0;
    $substatusIdArray = [];
    $substatusArray = [];
    $tempSubstatusArray = [];
    $subStatuaCnt = count($fileSubstatusInfo);
    for ($j = 0; $j < $subStatuaCnt; $j++) {
        $substatusIdArray[] = trim($fileSubstatusInfo[$j]['substatusId']);
    }
    if (count($substatusIdArray) > 0) $tempSubstatusArray = getPCFileSubstatus::getReport(['PFSID' => implode(',', $substatusIdArray)]);
    /*** Allowed PC status ***/
    if (count($tempSubstatusArray) > 0) {
        if (array_key_exists('substatusInfo', $tempSubstatusArray)) {
            $substatusArray = $tempSubstatusArray['substatusInfo'];
        }
    }
}

/* Hardship Info */
if ($activeTab == 'HA') {
    $hardshipTemplateArray = [];
    $hardshipTemplateArray = getMyHardships::getReport(['LMRID' => $LMRId]);
}
/* Hardship Info */

/** Student loan mod file - Change the tab from CI to SLM (open) if user clicks from the pipeline **/

if ($activeTab == 'CI') {
    for ($i = 0; $i < count($fileModuleInfo); $i++) {
        if (trim($fileModuleInfo[$i]['moduleCode']) == 'SLM') {
            $isSLM = 1;
            break;
        }
    }
    if ($isSLM == 1 && (count($fileModuleInfo) == 1)) {
        $activeTab = 'SLM';
    }
}

for ($sc = 0; $sc < count($fileModuleInfo); $sc++) {
    if ($fileModuleInfo[$sc]['moduleCode'] == 'SS') $showSSTab = 1;
    if ($fileModuleInfo[$sc]['moduleCode'] == 'SLM') $isSLM = 1;
    if ($fileModuleInfo[$sc]['moduleCode'] == 'LM') $isLM = 1;
}
if ($isSLM == 1 && (count($fileModuleInfo) == 1)) {
    /** Show ONLY Student loan mod tabs and its respective tabs **/
    $isSLMOnly = 1;
}

for ($i = 0; $i < count($fileModuleInfo); $i++) {
    if (trim($fileModuleInfo[$i]['moduleCode']) == 'HOA') {
        /** HOA Lien Tab **/
        $isHOALien = 1;
        break;
    }
    if (trim($fileModuleInfo[$i]['moduleCode']) == 'LO') {
        /** LO Tab **/
        $isLO = 1;
        break;
    }
    if (trim($fileModuleInfo[$i]['moduleCode']) == 'LSR') {
        /** Loan Servicing Section For Doyen Asset Management PCs **/
        $isLSR = 1;
    }
    /*
            if(trim($LMRClientTypeInfo[$i]['ClientType']) == 'LO') {
                $isLO = 1;
                break;
            }
    */
    if (trim($fileModuleInfo[$i]['moduleCode']) == 'MF') {
        /** Merchant Funding Section **/
        $isMF = 1;
    }

    if (trim($fileModuleInfo[$i]['moduleCode']) == 'FU') {
        /** Funding Section **/
        $isFU = 1;
    }

    if (trim($fileModuleInfo[$i]['moduleCode']) == 'HMLO') {
        /** Hard / Private Money LOS Section on                                                                    Nov 17, 2016 **/
        $isHMLO = 1;
    }

    if (trim($fileModuleInfo[$i]['moduleCode']) == 'EF') {   /* Equipment Financing - EF Module - Selected in the file (PT-#*********) - April 18, 2018 */
        $isEF = 1;
    }

    if (trim($fileModuleInfo[$i]['moduleCode']) == 'loc') {   /* Line Of Credit. - LOC Module - Selected in the file (PT-#159946019) - August 27, 2018 */
        $isLOC = 1;
    }

}

if ($isLOC == 1) $isHMLO = 1;

/** Student loan mod file **/
if ($isHOALien == 1 && $publicUser != 1) {
    $titleBorName = $propertyAddress . ' - ';
}

/**  Fetch all File tabs for PC modules or File selected Modules **/

require 'getPermissionToEditAndLock.php'; /* Return Permission to edit files, locking, It is also used in export file task */

if ($isEF == 1) $glTypeOfSale = $glTypeOfSaleEF;    #PT - *********

$propertyTypeKeyArray = [];
if (count(GpropertyTypeNumbArray::$GpropertyTypeNumbArray) > 0) $propertyTypeKeyArray = array_keys(GpropertyTypeNumbArray::$GpropertyTypeNumbArray);
$GpropertyTypeKeyArray = [];
$GpropertyTypeKeyArray = $propertyTypeKeyArray;
$stateKeyArray = [];
$HMLOPCBasicMinSeasoningPersonalBankruptcyInfoArray = [];
$HMLOPCBasicMinSeasoningBusinessBankruptcyInfoArray = [];
$HMLOPCBasicMinSeasoningForeclosureInfoArray = [];
$HMLOPCBasicSBALoanProductInfoArray = [];
$HMLOPCBasicEquipmentTypeInfoArray = [];
$HMLOPCBasicEntitityStateFormationInfoArray = [];
$HMLOPCBasicPaymentFrequencyInfoArray = [];
$HMLOPCBasicLoanPurposeInfoArray = [];
$HMLOPCBasicMinTimeInBusinessInfoArray = [];

/* PC loan terms rules - (Pivotal # : *********) */
if ($isHMLO || $isLOC) {
    require 'fileCustomPCLoanTerms.php';
}

$acqualifyPCDetails = [];
$acqualifyPCAccountId = 0;
$acqualifyPCMinCreditScore = 0;
$acqualifyBranchId = 0;
$acqualifyClientCreditInfo = [];
$acqualifynotificationList = '';
$applicantIdentifier = 0;
$applicantUserid = 0;
$defaultBranchId = 0;

if ($LMRId > 0) {

    $acqualifyPCDetails = getacqualifyPCDetails::getReport(['pcid' => $PCID]);
    if (count($acqualifyPCDetails) > 0) {
        $acqualifyPCAccountId = $acqualifyPCDetails[0]['accountId'];
        $acqualifyPCMinCreditScore = $acqualifyPCDetails[0]['minCreditScore'];
        $defaultBranchId = $acqualifyPCDetails[0]['defaultBranchId'];
    }
    $BRANCHAcqualifyArray = ['executiveId' => $executiveId];
    if($acqualifyPCAccountId){
        $BRANCHAcqualifyArray['accountId'] = $acqualifyPCAccountId;
    }
    $BRANCHAcqualifyArray = getacqualifyBranchDetails::getReport($BRANCHAcqualifyArray);
    foreach ($BRANCHAcqualifyArray as $BRANCHAcqualifyVal) {
        $acqualifyBranchId = $BRANCHAcqualifyVal['userId'];
    }
    $acqualifyClientId = Arrays::getArrayValue('clientId', $getMyFileInfo[$LMRId]);

    if ($acqualifyPCAccountId > 0) {
        $acQualifyInputArray['accountId'] = $acqualifyPCAccountId;
    }
    if ($acqualifyClientId > 0) {
        $acQualifyInputArray['clientId'] = $acqualifyClientId;
    }
    $acqualifyClientCreditInfo = getClientCreditInfo::getReport($acQualifyInputArray);

    $acQualifyUserDetails = getacqualifyUserDetails::getReport(['lwUserId' => $_SESSION['userNumber'], 'userRole' => $_SESSION['userGroup']]);
    if (count($acQualifyUserDetails) > 0) {
        $applicantUserid = $acQualifyUserDetails[0]['userId'];
    }
}


