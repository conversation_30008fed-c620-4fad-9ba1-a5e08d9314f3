<?php

use models\composite\oModules\getModules;
use models\composite\oPC\getPCModules;
use models\composite\oServiceType\getServiceTypes;
use models\constants\gl\glPackageCategoryArray;
use models\Database2;
use models\cypher;
use models\Request;
use models\standard\BaseHTML;
use models\standard\Strings;
use models\standard\UserAccess;

session_start();
require '../includes/util.php';
require '../includes/router.php';

require 'initPageVariables.php';
require 'getPageVariables.php';
global $userGroup, $PCID, $userRole, $isPCActive;
UserAccess::CheckAdminUse();
if (isset($_REQUEST['pcId'])) $assignedPCID = cypher::myDecryption(Request::GetClean('pcId'));
if ((trim($userGroup) == 'Super') || (trim($userGroup) == 'Sales') ||
    (trim($userGroup) == 'Employee' && ($PCID == $assignedPCID) && $assignedPCID > 0)
) {
} else {
    $redirectFile = CONST_SITE_URL . 'unauthorizedPage.php';
    header('Location: ' . $redirectFile);
    exit();
}

$glPackageCategoryArray = glPackageCategoryArray::$glPackageCategoryArray;

$tabNumb = 1;
$publicUser = 0;
$userId = 0;
$executiveId = 0;
$PLOId = 0;
$modulesArray = [];
$brokerNumber = 0;
$regnFor = '';
$serviceTypeArray = [];
$libModulesArray = $libModulesStatusArray = [];
$opt1 = '';
$hideBorrowerInfo = '';

if (isset($_REQUEST['tabNumb'])) $tabNumb = Request::GetClean('tabNumb');
echo BaseHTML::openPage('Master Profile - ' . CONST_DOMAIN, 1, 1);
?>

<style>
    nav {
        height: 83px !important;
    }
</style>

<?php
$scriptArray = [
    '/assets/js/processingCompany.js',
    '/assets/js/CWForm.js',
    '/assets/js/fileStatus.js',
    '/assets/js/fileSubstatus.js',
    '/assets/js/ccValidation.js',
    '/assets/js/PCPayment.js',
    '/assets/js/loan_math/per_diem.js',
    '/assets/js/PCFileDocuments.js',
    '/assets/js/3rdParty/tinymce/jscripts/tiny_mce/tiny_mce.js',
    CONST_BO_URL.'processingCompany/js/listPCFileTabs.js',
];
if ($tabNumb == 13) {
    $scriptArray[] = '/assets/js/HMLOLoanInfo.js';
    $scriptArray[] = '/assets/js/loanCalculation.js';
}

/*  Check if the Profile tab is 5 (workflow) or 13 (Loan guidelines)  */
if (($userRole == 'Super' || $userRole == 'Sales' || ($userRole == 'Manager' && $isPCActive)) && ($tabNumb == 5 || $tabNumb == 13)) {
} else {
    $scriptArray[] = '/assets/js/3rdParty/jquery-chosen-0.9.8/chosen.jquery.js';
}
if (($userRole == 'Super' || $userRole == 'Sales' || ($userRole == 'Manager' && $isPCActive)) && ($tabNumb == 13)) {
    $scriptArray[] = '/assets/js/chosen.jquery.1.4.2.js';
}
Strings::includeMyScript($scriptArray);

$cssArray = [
    '/assets/styles/autocomplete.css',
    '/assets/styles/tree-view.css'
];
if (($userRole == 'Super' || $userRole == 'Sales' || ($userRole == 'Manager' && $isPCActive)) && ($tabNumb == 5 || $tabNumb == 13)) {
} else {
    $cssArray[] = '/assets/styles/multi_select.css';
}
if (($userRole == 'Super' || $userRole == 'Sales' || ($userRole == 'Manager' && $isPCActive)) && ($tabNumb == 13)) {
    $cssArray[] = '/assets/styles/chosen.1.4.2.css';
}
Strings::includeMyCSS($cssArray);


$glLMRClientTypeArray = getServiceTypes::getReport(['activeStatus' => '1']);
//  $libModulesArray = $oModules->getLibModules(array("activeStatus"=> '1'));
if ($userRole == 'Super') {
    $opt1 = 'moduleInfo';
    $ip['opt1'] = $opt1;
    $ip['PCID'] = $assignedPCID;
    if ($tabNumb == 1) $ip['subscribedOpt'] = 'ALL';
} else {
    $opt1 = 'PCModuleInfo';
    $ip['opt1'] = $opt1;
    $ip['PCID'] = $assignedPCID;
    if ($tabNumb == 1) $ip['subscribedOpt'] = 'PC';
}

$modulesResultArray = [];
$tempLibModulesArray = [];
$modulesResultArray = getModules::getReport($ip);
if (count($modulesResultArray) > 0) {
    if (array_key_exists($opt1, $modulesResultArray)) {
        $tempLibModulesArray = $modulesResultArray[$opt1];
        unset($modulesResultArray[$opt1]);
    }
}
for ($j = 0; $j < count($tempLibModulesArray); $j++) {
    $libModulesArray[trim($tempLibModulesArray[$j]['moduleCode'])] = trim($tempLibModulesArray[$j]['moduleName']);
    /*if ($userRole == 'Super') {
} else {
$libModulesStatusArray[trim($tempLibModulesArray[$j]['moduleCode'])] = trim($tempLibModulesArray[$j]['activeStatus']);
}*/
}

$ip['keyNeeded'] = 'n';
//      $servicesRequested      = oPC::getPCServiceType($ip);                           /** Fetch PC services requested **/
//if ($assignedPCID > 0) {
$ip = ['PCID' => $assignedPCID];
$ip['keyNeeded'] = 'n';
//  $servicesRequested      = oPC::getPCServiceType($ip);                           /** Fetch PC services requested **/

$modulesArray = getPCModules::getReport($ip);
//}

$fileModules = [];
for ($j = 0; $j < count($modulesArray); $j++) {
    //if(trim($modulesArray[$j]['moduleCode']) == 'SLM') { /** Skip SLM from selected services to show in separate DIV **/
    //} else {
    $fileModules[] = $modulesArray[$j]['moduleCode'];
    //}
}

$tempArray = [];
$tempArray = array_keys($glPackageCategoryArray);
for ($i = 0; $i < count($tempArray); $i++) {
    $fileModules[] = $tempArray[$i];
}

$tempPkgGroups = array_merge($libModulesArray, $glPackageCategoryArray);
$exInArray['TABLENAME'] = 'tblProcessingCompany';
$exResultArray = [];
$exInArray['FIELDNAMEARRAY'] = ['isPLO', 'processingCompanyName', 'hideBorrowerInfo'];
$exInArray['CONDITION'] = ' where activeStatus = 1 and dstatus = 0 and PCID = ' . $assignedPCID . ' ';

$exResultArray = Database2::getInstance()->getSelectedFieldsForFile($exInArray);

if (count($exResultArray) > 0) {
    $isPLO = trim($exResultArray['isPLO']);
    $PCName = trim($exResultArray['processingCompanyName']);
    $hideBorrowerInfo = trim($exResultArray['hideBorrowerInfo']);
}

$video = ' <a target="_blank" class="purple-text px-6 h5"
                                               href="https://www.youtube.com/watch?v=KP_9SjroPB4"
                                               style="text-decoration:none;">Help
                            Video: Customizing your system</a>';
if ($userRole != 'Manager' && $userRole != 'Super') {
    $titleArray = [['href' => '#', 'title' => 'Processing Company Profile' . $video]];
} else if ($assignedPCID > 0 && $userRole == 'Super') {
    $titleArray = [['href' => 'processingCompanyList.php', 'title' => 'Processing Company List'], ['href' => '#', 'title' => 'Update Company Profile &amp; System Settings - ' . $PCName]];
} else if ($assignedPCID > 0) {
    $titleArray = [['href' => '#', 'title' => 'Update Company Profile &amp; System Settings - ' . $PCName . $video]];
} else {
    $titleArray = [['href' => '#', 'title' => 'Account Default settings' . $video]];
}

/* Dynamic $Breadcrumb */
$Breadcrumb = [];
$Breadcrumb['icon'] = 'fas fa-users icon-md';
$Breadcrumb['breadcrumbList'] = '';//array(array('href' => '#', 'title' => 'Sample home'), array('href' => '#', 'title' => 'Sample Child'));
$Breadcrumb['toolbarHTML'] = '';
/* End of Dynamic $Breadcrumb */
$Breadcrumb['title'] = 'Back Office';/* Dynamic $Breadcrumb */
$Breadcrumb = [];
$Breadcrumb['icon'] = 'fas fa-users icon-md';
$Breadcrumb['breadcrumbList'] = $titleArray;
$Breadcrumb['toolbarHTML'] = '';

require('../includesNew/_page-body-loader.php');
require('../includesNew/_layoutOpen.php');

?>
<script>
    <!--
    <?php
    if(($userRole == 'Super' || $userRole == 'Sales' || ($userRole == 'Manager' && $isPCActive)) && ($tabNumb == 5)) {
    } else {
    ?>
    $(document).ready(function () {
        $(".chzn-select").chosen();
    });
    <?php
    }
    ?>
    var assignedPCID = 0;
    assignedPCID = '<?php echo htmlspecialchars($assignedPCID)?>';

    function showTabUrl(tabNumb, PCID) {
        window.location.href = "defaultPCSettings.php?pcId=" + PCID + "&tabNumb=" + tabNumb;
    }

    function showSMTPInfo(opt, smtpId) {
        if (opt == 1) {
            document.getElementById(smtpId).style.display = 'block';
        } else {
            document.getElementById(smtpId).style.display = 'none';
        }
    }

    function showFaxInfo(opt1, faxId) {
        if (opt1 == 1) {
            document.getElementById(faxId).style.display = 'block';
        } else {
            document.getElementById(faxId).style.display = 'none';
        }
    }

    // -->
</script>
<style>
    .acctTable td {
        padding: 5px;
    }

    .padLeft50 {
        padding-left: 50px;
    }

    .divW295 {
        width: 295px;
    }

    .overlayout {
        z-index: 20000;
        -moz-opacity: 0.5;
        opacity: .50;
        filter: alpha(opacity=50);
        background-color: #FAFCFD;
    }

    .overlay-alert {
        clear: both;
        padding: 0px;
        margin: 0px;
        font-family: arial;
        font-weight: bold;
        font-size: 20px;
        color: #00326d;
        line-height: 42px;
        border: 0px solid #d2d2d2;
        z-index: 20000;
        position: absolute;
        top: 280px;
        left: 680px;
        width: 28%;
        height: auto;
        zoom: 1;
        color: red;
    }

    #tabledivbody tr, .connectedSortable tr, .ui-sortable-helper {
        cursor: move;
    }

    .connectedSortable tr:first-child {
        cursor: default;
    }
</style>

<div class="card card-custom p-0 card-sticky " id="kt_page_sticky_card">
    <div class="card-header px-2 py-2 border" style="">
        <div class="pipelineNavigation ">
            <ul class="nav nav-pills">

                <?php
                if ($userRole == 'Super') {
                    if (($tabNumb == 4)) {
                        $newHref = '';
                    } else {
                        $newHref = " onclick=\"javascript:showTabUrl('4', '" . cypher::myEncryption(htmlspecialchars($assignedPCID)) . "');\"";
                    }
                    ?>
                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                        <a class="nav-link px-4 py-2 <?php if ($tabNumb == 4) {
                            echo 'active';
                        } else {
                            echo '';
                        } ?> " href="#" <?php echo $newHref ?>>Required Docs</a>
                    </li>
                    <?php
                    if (($tabNumb == 5)) {
                        $newHref = '';
                    } else {
                        $newHref = " onclick=\"javascript:showTabUrl('5', '" . cypher::myEncryption(htmlspecialchars($assignedPCID)) . "');\"";
                    }
                    ?>
                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                        <a class="nav-link px-4 py-2 <?php if ($tabNumb == 5) {
                            echo 'active';
                        } else {
                            echo '';
                        } ?> " href="#" <?php echo $newHref ?>>Workflow</a>
                    </li>
                    <?php
                    /* Check profile >>> tab = 6 (File Status) */
                    if (($tabNumb == 6)) {
                        $newHref = '';
                    } else {
                        $newHref = " onclick=\"javascript:showTabUrl('6','" . cypher::myEncryption(htmlspecialchars($assignedPCID)) . "');\"";
                    }
                    ?>
                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                        <a class="nav-link px-4 py-2 <?php if ($tabNumb == 6) {
                            echo 'active';
                        } else {
                            echo '';
                        } ?> " href="#" <?php echo $newHref ?>>File Status</a>
                    </li>
                    <?php

                    /* Check profile >>> tab = 7 (File Sub-status) */
                    if (($tabNumb == 7)) {
                        $newHref = '';
                    } else {
                        $newHref = " onclick=\"javascript:showTabUrl('7','" . cypher::myEncryption(htmlspecialchars($assignedPCID)) . "');\"";
                    }
                    ?>
                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                        <a class="nav-link px-4 py-2 <?php if ($tabNumb == 7) {
                            echo 'active';
                        } else {
                            echo '';
                        } ?> " href="#" <?php echo $newHref ?>>File Substatus</a>
                    </li>
                    <?php
                } ?>

            </ul>
        </div>
    </div>
</div><!-- tab Container Div -->
<div class="card card-custom">
    <div class="card-body">
        <div style="float:right;display:block;" id="divLoader"><img src="<?php echo IMG_PROGRESS_BAR; ?>"></div>
        <div class="pad5 right" id="sessMsg">
            <?php
            echo Strings::DisplayMessage(Strings::GetSess('msg'));
            Strings::SetSess('msg', '');
            ?>
        </div>
        <?php
        if ($tabNumb == 1) {
            require 'defaultPCSettings.php';
        } else if ($tabNumb == 2 && $assignedPCID > 0) {
            require 'packageList.php';
        } else if ($tabNumb == 3) {
            $paymentURL = 'backoffice/defaultPCSettings.php';
            if ($publicUser == 1 || $userRole == 'Super' || $userRole == 'Sales') {
                require 'PCPaymentForm.php';
            }
        } else if ($tabNumb == 4) {
            if ($userRole == 'Super') require 'checklistForm.php';
        } else if ($tabNumb == 5) {
            if (($userRole == 'Super')) require 'PCWorkflowForm.php';
        } else if ($tabNumb == 6) {
            if ($userRole == 'Super') require 'PCFileStatusForm.php';
        } else if ($tabNumb == 7) {
            if ($userRole == 'Super') require 'PCFileSubStatusForm.php';
        }
        ?>
    </div>
</div>
<?php
if ($userRole == 'Super' || $userRole == 'Sales' || ($userRole == 'Manager' && $isPCActive)) {
    ?>
    <script type='text/javascript'>
        $(document).ready(function () {

            <?php
            if($tabNumb == 2) {
            ?>
            $(".expander")
                .click(function () {
                    $(this).next(".subExpander").toggle('fast');
                    $(this).not(".caret").children(".fa").toggleClass("fa-folder-o fa-folder-open-o");
                    return false; //signal as event handled, don't bubble up the chain
                })
                .not(".expanded").next(".subExpander").hide();
            <?php
            } else if($tabNumb == 7 ) {
            ?>
            $(".expander")
                .click(function () {
                    $(this).next(".subExpander").toggle('fast');
                    $(this).not(".caret").children(".fa").toggleClass("fa-minus-square fa-plus-square");
                    return false; //signal as event handled, don't bubble up the chain
                })
                .not(".expanded").next(".subExpander").hide();
            <?php
            }
            ?>

        });
    </script>
    <?php
}
?>

<?php
require('../includesNew/_layoutClose.php');
require 'adminFooter.php';
echo BaseHTML::closePage();
require('../includesNew/_customPage.php');
?>

