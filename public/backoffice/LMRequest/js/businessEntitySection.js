$("#dateOfOperatingAgreement,#dateOfFormation,#borLicenseIssuance,#borLicenseExpiration,#startDateAtLocation,.onChangeEntity").change(function () {
    $('.ischanged').val(1);
});



function restrictAlphabets(el, evt, afterDecimal = 2) {
    var beforeDecimal = 8;

    $('#' + el.id).on('input', function () {
        this.value = this.value
            .replace(/[^\d.]/g, '')
            .replace(new RegExp("(^[\\d]{" + beforeDecimal + "})[\\d]", "g"), '$1')
            .replace(/(\..*)\./g, '$1')
            .replace(new RegExp("(\\.[\\d]{" + afterDecimal + "}).", "g"), '$1');
    })
}

//date format - add slash
function addSlash(dob) {
    if (dob.length > 0) {
        thisVal = dob.substr(0, 2) + '/' + dob.substr(2, 2) + '/' + dob.substr(4, 7);
    } else {
        thisVal = '';
    }
    return thisVal;
}

//Copy Borrower Info
$(document).on('change', '#borrowerInfo', function () {
    let chk = $(this).is(':checked');

    // Function to get concatenated address
    function getAddress() {
        return [$('#presentAddress').val(), $('#presentCity').val(), $("#presentState option:selected").text(), $('#presentZip').val()]
            .filter(Boolean) // Remove empty values
            .join(' , ');
    }

    // Get values if checked, else set to empty
    let data = chk ? {
        name: $('#borrowerFName').val() + " " + $('#borrowerLName').val(),
        address: getAddress(),
        phone: $('#workNumber').val(),
        cell: $('#cellNo').val(),
        ssn: $('#ssn').val(),
        dob: $('#borrowerDOB').val(),
        email: $('#borrowerEmail').val(),
        dl: $('#driverLicenseNumber').val(),
        dls: $('#driverLicenseState').val(),
        creditScore: $('#borCreditScoreRange').val(),
        maritalStatus: $('input[name="maritalStatus"]:checked').val(),
        borrowerCitizenship: $('input[name="borrowerCitizenship"]:checked').val(),
        marriageDate: $('#marriageDate').val(),
        divorceDate: $('#divorceDate').val(),
        maidenName: $('#maidenName').val(), 
        spouseName: $('#spouseName').val(),
    } : {};

    // Assign values
    $('#memberName0').val(data.name || '');
    $('#memberAddress0').val(data.address || '');
    $('#memberPhone0').val(data.phone || '');
    $('#memberCell0').val(data.cell || '');
    $('#memberSSN0').val(data.ssn || '');
    $('#memberDOB0').val(data.dob || '');
    $('#memberEmail0').val(data.email || '');
    $('#memberDriversLicenseState0').val(data.dls || '');
    $('#memberDriversLicense0').val(data.dl || '');
    $('#memberCreditScore0').val(data.creditScore || '');
    let _memberMarriageDate = $('#memberMarriageDate0');
    let _memberDivorceDate = $('#memberDivorceDate0');
    let _memberMaidenName = $('#memberMaidenName0');
    let _memberSpouseName = $('#memberSpouseName0');

    $(`input[name="memberMaritalStatus_0"]`).prop('checked', false);
    _memberMarriageDate.val('');
    _memberDivorceDate.val('');
    _memberMaidenName.val('');
    _memberSpouseName.val('');

    if (data.maritalStatus) {
        $(`input[name="memberMaritalStatus_0"][value="${data.maritalStatus}"]`).prop('checked', true).click();
        if(data.maritalStatus === 'Married'
            || data.maritalStatus === 'Separated') {
            _memberMarriageDate.val(data.marriageDate || '');
            _memberDivorceDate.val(data.divorceDate || '');
            _memberMaidenName.val(data.maidenName || '');
            _memberSpouseName.val(data.spouseName || '');
        }
    }
    $(`input[name="memberCitizenship_0"]`).prop('checked', false);
    if(data.borrowerCitizenship) {
        $(`input[name="memberCitizenship_0"][value="${data.borrowerCitizenship}"]`).prop('checked', true).click();
    }

});
$(document).ready(function () {
    $('#naicsCode:enabled').inputmask("999999");
    $('#benCardProcessorBank:enabled').inputmask("99");
    $('#benHowManyLocation:enabled').inputmask("999999");
    //showHideOwnershipChildFields
    var val = $('#entityPropertyOwnerShip').val();
    showHideOwnershipChildFields(val);
    $(document).on('change', '#entityPropertyOwnerShip', function () {
        var val = $(this).val();
        showHideOwnershipChildFields(val);
    });
    //showHideCreditCardFields
    $(document).on('change', '.benCreditCardPayments', function () {
        var cc = $(this).val();
        showHideCreditCardFields(cc);
    });
    //show business location Fields
    $(document).on('change', '.benBusinessLocation', function () {
        var bl = $(this).val();
        showHideBusinessLocation(bl);
    });
    $(document).on('change', '.benBusinessFranchise', function () {
        var bf = $(this).val();
        showHideBusinessFranchise(bf);
    });
    $(document).on('change', '.businessReference', function () {
        var bf = $(this).val();
        if (bf == '1') {
            $('.businessReferenceInfo').css({"display": "contents"});
        } else {
            $('.businessReferenceInfo').css({"display": "none"});
        }
    });

    $(document).on('click', '.cloneBusinessRef', function () {
        //  $('.cloneBusinessRef').bind("click", function () {

        data_hide_class = $(this).attr('data-hide-class');
        $('.' + data_hide_class).show();
        $(this).hide();
        cnt = $(".cloneBusinessReferenceInfo").length + 1;
        console.log(cnt);
        var clone = $(".cloneBusinessReferenceInfo:last").clone(false);
        var ParentRow = $(".cloneBusinessReferenceInfo").last();
        jQuery(clone).find(':input').each(function (i) {
            switch (this.type) {
                case 'password':
                case 'text':
                case 'textarea':
                case 'file':
                case 'select-one':
                case 'select-multiple':
                case 'date':
                case 'number':
                case 'tel':
                case 'email':
                    jQuery(this).val('');
                    break;
                case 'checkbox':
                case 'radio':
                    this.checked = false;
                    break;
            }
        });
        jQuery(clone).find('.businesRefNumber').each(function (i) {
            var idArr = [];
            elmNum = this.innerHTML;
            newEleNum = parseInt(elmNum) + 1;
            $(this).html(newEleNum);
            //alert("div_requiredBy_" + cnt)
        });
        jQuery(clone).find(':input').each(function (i) {
            var idArr = [];
            var elmId = this.id;
            idArr = elmId.split('_');
            $(this).attr('id', idArr[0] + "_" + cnt);
            jQuery(this).val('');
        });

        jQuery(clone).find('.cloneBusinessRef').each(function (i) {
            $(this).show();
        });
        jQuery(clone).find('.deleteBusinessRef').each(function (i) {
            $(this).hide();
        });
        clone.clone(true).insertAfter(ParentRow);
        $('.businessPhoneMask:enabled').inputmask("mask", {mask: "999 - 999 - 9999"});
    });
});
$('.businessPhoneMask:enabled').inputmask("mask", {mask: "999 - 999 - 9999"});

$(document).on('click', '.deleteBusinessRef', function () {
    var r = confirm("Do you want to delete?");
    if (r == true) {
        $("#" + $(this).attr('data-delete-row')).remove();
    }
});

function showHideOwnershipChildFields(val) {
    if (val == 'Own') { //show child fields
        $('#OwnershipChildFieldsDiv').css({"display": "contents"});
    } else { //hide child fields
        $('#OwnershipChildFieldsDiv').css({"display": "none"});
    }
}

function showHideCreditCardFields(cc) {
    if (cc == 'Yes') { //show child fields
        $('#creditCardFieldsDiv').show();
    } else { //hide child fields
        $('#creditCardFieldsDiv').hide();
    }
}

function showHideBusinessLocation(bl) {
    if (bl == 'Yes') { //show child fields
        $("#BenBusinessLocationDiv").show();
    } else { //hide child fields
        $("#BenBusinessLocationDiv").hide();
    }
}

function showHideBusinessFranchise(bf) {
    if (bf == 'Yes') { //show child fields
        $("#BenBusinessFranchiseDiv").show();
    } else { //hide child fields
        $("#BenBusinessFranchiseDiv").hide();
    }
}

/* hide sub headers of section if all fields are hidden conditionally */
//General Business Info
if ($('.BenGbiTitle.secShow').length > 0) { //show title
    $("#BenGbiTitle").show();
} else { //hide title
    $("#BenGbiTitle").hide();
}
//Location/Property Details
if ($('.BenLpdTitle.secShow').length > 0) { //show title
    $("#BenLpdTitle").show();
} else { //hide title
    $("#BenLpdTitle").hide();
}
//Business Details
if ($('.BenBdTitle.secShow').length > 0) { //show title
    $("#BenBdTitle").show();
} else { //hide title
    $("#BenBdTitle").hide();
}
//Business Financials
if ($('.BenBfTitle.secShow').length > 0) { //show title
    $("#BenBfTitle").show();
} else { //hide title
    $("#BenBfTitle").hide();
}



function showHideEntityFields(cc) {
    if (cc === 'Yes') { //show child fields
        $('.entityField').show();
        $('.GOVT,.GOVT-CB').addClass('d-none');
    } else { //hide child fields
        $('.entityField').hide();
        $('.GOVT').removeClass('d-none');
        if ($('#isCoBorrower').val()) {
            $('.GOVT-CB').removeClass('d-none')
        }
    }
}

function showHideBusinessEntityFields(val) {
    let entityFieldElements = $('.entityField');
    let retirementEntity_dispElements = $('.retirementEntity_disp');
    let trustType_dispElements = $('.trustType_disp');
    let GOVT_dispElements = $('.GOVT');
    let GOVT_CB_dispElements = $('.GOVT-CB');
    let BenGbiTitle = $('#BenGbiTitle');
    BenGbiTitle.hide();
    switch (val) {
        case 'Individual':
            entityFieldElements.hide();
            retirementEntity_dispElements.addClass('d-none');
            trustType_dispElements.addClass('d-none');
            GOVT_dispElements.removeClass('d-none');
            if ($('#isCoBorrower').val()) {
                GOVT_CB_dispElements.removeClass('d-none')
            }
            clear_form_elements('entityField');
            clear_form_elements('retirementEntity_disp');
            clear_form_elements('trustType_disp');
            break;
        case 'Entity':
            BenGbiTitle.show();
            entityFieldElements.show();
            retirementEntity_dispElements.addClass('d-none');
            trustType_dispElements.addClass('d-none');
            GOVT_dispElements.addClass('d-none');
            GOVT_CB_dispElements.addClass('d-none');
            clear_form_elements('govtCard_body');
            clear_form_elements('GOVTCBCard_body');
            clear_form_elements('retirementEntity_disp');
            clear_form_elements('trustType_disp');
            break;
        case 'Trust':
            trustType_dispElements.removeClass('d-none');
            retirementEntity_dispElements.addClass('d-none');
            entityFieldElements.hide();
            GOVT_dispElements.addClass('d-none');
            GOVT_CB_dispElements.addClass('d-none');
            clear_form_elements('govtCard_body');
            clear_form_elements('GOVTCBCard_body');
            clear_form_elements('entityField');
            clear_form_elements('retirementEntity_disp');
            break;
        case 'Retirement Entity':
            retirementEntity_dispElements.removeClass('d-none');
            trustType_dispElements.addClass('d-none');
            entityFieldElements.hide();
            GOVT_dispElements.addClass('d-none');
            GOVT_CB_dispElements.addClass('d-none');
            clear_form_elements('govtCard_body');
            clear_form_elements('GOVTCBCard_body');
            clear_form_elements('entityField');
            clear_form_elements('trustType_disp');
            break;
        default :
            entityFieldElements.hide();
            retirementEntity_dispElements.addClass('d-none');
            trustType_dispElements.addClass('d-none');
            GOVT_dispElements.addClass('d-none');
            GOVT_CB_dispElements.addClass('d-none');
            clear_form_elements('govtCard_body');
            clear_form_elements('GOVTCBCard_body');
            clear_form_elements('entityField');
            clear_form_elements('retirementEntity_disp');
            clear_form_elements('trustType_disp');
            break;
    }
}

class businessEntity {
    static showHideMaritalFields(val, cls) {
        let maritalStatusElements = $('.' + cls);
        switch (val) {
            case 'Married':
            case 'Separated':
                maritalStatusElements.not('.secHide').show();
                clear_form_elements(cls);
                break;
            default :
                maritalStatusElements.hide();
                clear_form_elements(cls);
                break;
        }
    }

}

class BusinessEntitySection {
    static memberTypeShowHideFields(field) {
        let fd = field.split('_');
        let memberType = fd[0];
        let memberIndex = fd[1];
        switch (memberType) {
            case 'Individual':
                BusinessEntitySection.showIndividualFields(memberIndex);
                break;
            case 'Entity':
                BusinessEntitySection.showEntityFields(memberIndex);
                break;
            default:
                break;
        }
    }

    static memberTypeShowHideFieldsCV3(field,ele) {
        console.log({
            func: 'memberTypeShowHideFieldsCV3',
            field: field
        });
        let fd = field.split('_');
        let memberType = fd[0];
        let memberIndex = fd[1] + '_' + fd[2];
        $(ele).closest('.card-body').find('.memberDocs').hide();
        $(ele).closest('.card-body').find('.'+memberType+'ClassDoc').show();
        switch (memberType) {
            case 'Individual':
                BusinessEntitySection.showIndividualFields(memberIndex);
                break;
            case 'Entity':
                BusinessEntitySection.showEntityFields(memberIndex);
                break;
            default:
                break;
        }
    }

    static showIndividualFields(memberIndex) {
        let howManyMemberOfficer = $('#howManyMemberOfficer_' + memberIndex);
        //show Individual Fields
        $('#memberTitle_' + memberIndex).show();
        $('#memberType_' + memberIndex).show();
        $('#memberAnnualSalary_' + memberIndex).show();
        $('#memberSSN_' + memberIndex).show();
        $('#memberCreditScore_' + memberIndex).show();
        $('#memberCreditScoreDate_' + memberIndex).show();
        $('#memberDOB_' + memberIndex).show();
        $('#memberEmail_' + memberIndex).show();
        $('#memberRentOrOwn_' + memberIndex).show();
        $('#memberMonthlyRentOrMortgage_' + memberIndex).show();
        $('#memberDateMovedAddress_' + memberIndex).show();
        $('#memberRealEstateValue_' + memberIndex).show();
        $('#memberRetirementAccountBalance_' + memberIndex).show();
        $('#memberCashSavingsStocksBalance_' + memberIndex).show();
        $('#memberCreditCardBalance_' + memberIndex).show();
        $('#memberMortgageBalance_' + memberIndex).show();
        $('#memberAutoLoanBalance_' + memberIndex).show();
        $('#memberTotalNetWorth_' + memberIndex).show();
        $('#memberMaritalStatus_' + memberIndex).show();
        $('#memberMarriageDate_' + memberIndex).show();
        $('#memberDivorceDate_' + memberIndex).show();
        $('#memberMaidenName_' + memberIndex).show();
        $('#memberSpouseName_' + memberIndex).show();
        $('#memberDriversLicenseState_' + memberIndex).show();
        $('#memberDriversLicense_' + memberIndex).show();
        $('#memberCitizenship_' + memberIndex).show();
        $('#memberPersonalGuarantee_' + memberIndex).show();
        $('#memberAuthorizedSigner_' + memberIndex).show();
        //hide Entity Fields
        $('#memberTin_' + memberIndex).hide();
        howManyMemberOfficer.hide();
        //clear Entity Fields
        BusinessEntitySection.clearEntityMemberFields(memberIndex);
        //hide Entity Members Fields
        //$('#entityMembersDiv_' + memberIndex).hide();
        //hide add more entity members button
        //$('#addMoreEntityMembers_' + memberIndex).hide();
        //Hide New Members text
        $('#new_member_' + memberIndex).hide();
        //Remove the last set of members set
        $('#' + memberIndex).val(0); //reset the value
        $('#formContainer_' + memberIndex).empty();
    }

    static showEntityFields(memberIndex) {
        let howManyMemberOfficer = $('#howManyMemberOfficer_' + memberIndex);
        //show Entity Fields
        $('#memberTin_' + memberIndex).show();
        howManyMemberOfficer.show();
        //hide Individual Fields
        $('#memberTitle_' + memberIndex).hide();
        $('#memberType_' + memberIndex).hide();
        $('#memberAnnualSalary_' + memberIndex).hide();
        $('#memberSSN_' + memberIndex).hide();
        $('#memberCreditScore_' + memberIndex).hide();
        $('#memberCreditScoreDate_' + memberIndex).hide();
        $('#memberDOB_' + memberIndex).hide();
        $('#memberEmail_' + memberIndex).hide();
        $('#memberRentOrOwn_' + memberIndex).hide();
        $('#memberMonthlyRentOrMortgage_' + memberIndex).hide();
        $('#memberDateMovedAddress_' + memberIndex).hide();
        $('#memberRealEstateValue_' + memberIndex).hide();
        $('#memberRetirementAccountBalance_' + memberIndex).hide();
        $('#memberCashSavingsStocksBalance_' + memberIndex).hide();
        $('#memberCreditCardBalance_' + memberIndex).hide();
        $('#memberMortgageBalance_' + memberIndex).hide();
        $('#memberAutoLoanBalance_' + memberIndex).hide();
        $('#memberTotalNetWorth_' + memberIndex).hide();
        $('#memberMaritalStatus_' + memberIndex).hide();
        $('#memberMarriageDate_' + memberIndex).hide();
        $('#memberDivorceDate_' + memberIndex).hide();
        $('#memberMaidenName_' + memberIndex).hide();
        $('#memberSpouseName_' + memberIndex).hide();
        $('#memberDriversLicenseState_' + memberIndex).hide();
        $('#memberDriversLicense_' + memberIndex).hide();
        $('#memberCitizenship_' + memberIndex).hide();
        $('#memberPersonalGuarantee_' + memberIndex).hide();
        $('#memberAuthorizedSigner_' + memberIndex).hide();
        //clear Individual Fields
        BusinessEntitySection.clearIndividualMemberFields(memberIndex);
        //show Entity Members Fields
        $('#entityMembersDiv_' + memberIndex).show();
        //show add more entity members button
        //$('#addMoreEntityMembers_' + memberIndex).show();
        //Show New Members text
        $('#new_member_' + memberIndex).show();
    }

    static clearEntityMemberFields(memberIndex) {
        $('#memberTin_' + memberIndex).val('');
        $('#howManyMemberOfficer_' + memberIndex).val('');
    }

    static clearIndividualMemberFields(memberIndex) {
        $('#memberTitle_' + memberIndex).val('');
        $('#memberType_' + memberIndex).val('');
        $('#memberAnnualSalary_' + memberIndex).val('');
        $('#memberSSN_' + memberIndex).val('');
        $('#memberCreditScore_' + memberIndex).val('');
        $('#memberCreditScoreDate_' + memberIndex).val('');
        $('#memberDOB_' + memberIndex).val('');
        $('#memberEmail_' + memberIndex).val('');
        $('#memberRentOrOwn_' + memberIndex).val('');
        $('#memberMonthlyRentOrMortgage_' + memberIndex).val('');
        $('#memberDateMovedAddress_' + memberIndex).val('');
        $('#memberRealEstateValue_' + memberIndex).val('');
        $('#memberRetirementAccountBalance_' + memberIndex).val('');
        $('#memberCashSavingsStocksBalance_' + memberIndex).val('');
        $('#memberCreditCardBalance_' + memberIndex).val('');
        $('#memberMortgageBalance_' + memberIndex).val('');
        $('#memberAutoLoanBalance_' + memberIndex).val('');
        $('#memberTotalNetWorth_' + memberIndex).val('');
        $('#memberMarriageDate_' + memberIndex).val('');
        $('#memberDivorceDate_' + memberIndex).val('');
        $('#memberMaidenName_' + memberIndex).val('');
        $('#memberSpouseName_' + memberIndex).val('');
        $('#memberDriversLicenseState_' + memberIndex).val('');
        $('#memberDriversLicense_' + memberIndex).val('');
        $('#memberCitizenship_' + memberIndex).attr('checked', false);
        $('#memberPersonalGuarantee_' + memberIndex).attr('checked', false);
        $('#memberAuthorizedSigner_' + memberIndex).attr('checked', false);
        $('#memberMaritalStatus_' + memberIndex).attr('checked', false);

        //clear Entity Members Fields
        $('.entityMemberName_' + memberIndex).val('');
        $('.entityMemberTitle_' + memberIndex).val('');
        $('.entityMemberOwnership_' + memberIndex).val('');
        $('.entityMemberPhone_' + memberIndex).val('');
        $('.entityMemberCell_' + memberIndex).val('');
        $('.entityMemberSSN_' + memberIndex).val('');
        $('.entityMemberDOB_' + memberIndex).val('');
        $('.entityMemberEmail_' + memberIndex).val('');
    }

    static clearEachEntityMemberFields(memberIndex, entityIndex) {
        $('#entityMemberName_' + memberIndex + '_' + entityIndex).val('');
        $('#entityMemberTitle_' + memberIndex + '_' + entityIndex).val('');
        $('#entityMemberOwnership_' + memberIndex + '_' + entityIndex).val('');
        $('#entityMemberPhone_' + memberIndex + '_' + entityIndex).val('');
        $('#entityMemberCell_' + memberIndex + '_' + entityIndex).val('');
        $('#entityMemberSSN_' + memberIndex + '_' + entityIndex).val('');
        $('#entityMemberDOB_' + memberIndex + '_' + entityIndex).val('');
        $('#entityMemberEmail_' + memberIndex + '_' + entityIndex).val('');
        //radio button
        $("input:radio[name='members[" + memberIndex + "][entityMember][" + entityIndex + "][entityMemberCitizenship]']").prop('checked', false);
        $("input:radio[name='members[" + memberIndex + "][entityMember][" + entityIndex + "][entityMemberPersonalGuarantee]']").prop('checked', false);
        $("input:radio[name='members[" + memberIndex + "][entityMember][" + entityIndex + "][entityMemberAuthorizedSigner]']").prop('checked', false);
    }

    static removeEntityMemberFields(_this) {

        let encInc = $(_this).attr('data-entityMemberInc');
        let encMemId = $(_this).attr('data-encMemberId');
        let encEntId = $(_this).attr('data-encEntityMemberId');
        let memId = $(_this).attr('data-memberId');
        let entId = $(_this).attr('data-entityMemberId');
        let rowId = $(_this).attr('data-RowId');

        let conf = 'Are you sure to delete?';
        $.confirm({
            icon: 'fas fa-exclamation-triangle text-danger',
            closeIcon: true,
            title: 'Confirm',
            content: conf,
            type: 'red',
            backgroundDismiss: true,
            buttons: {
                yes: function () {
                    if(rowId !== '0') { // delete from db
                        HTTP.Post('/JQFiles/removeEntityMembers.php', {
                                //'inc': inc,
                                'memId': encMemId,
                                'entId': encEntId,
                            }, function (response) {
                                if (response.success) {
                                    if(encInc === '1') { // 1
                                        BusinessEntitySection.clearEachEntityMemberFields(memId, entId);
                                    } else {
                                        $('#entityMemberDiv_' + memId + '_' + entId).remove();
                                    }
                                    toastrNotification(response.success, 'success');
                                } else {
                                    toastrNotification(response.error, 'error');
                                }
                            }
                        );
                    } else { // delete from UI
                        if(encInc === '1') { // 1
                            BusinessEntitySection.clearEachEntityMemberFields(memId, entId);
                        } else {
                            //console.log('entityMemberDiv_' + memId + '_' + entId);
                            $('#entityMemberDiv_' + memId + '_' + entId).remove();
                        }
                        toastrNotification('Deleted Successfully', 'success');
                    }
                }, cancel: function () {

                },
            },
            onClose: function () {

            },
        });
    }

    static addMoreFields(section, entityMemberDivClone) {
        let _max = 5;
        let sectionClass = section;
        let _cs = $('.' + section + ':visible');
        let _csCnt = $('.' + entityMemberDivClone + ':visible');
        if (_csCnt.length === _max) {
            toastrNotification(
                'Max allowed 5 Entity Members',
                'error',
            );
            return false;
        }
        let clonedSection = _cs.last().clone();
        //console.log(clonedSection.attr('id'));
        let clonedIDArray = clonedSection.attr('id').split('_');
        let clonedIDCount = parseInt(clonedIDArray[parseInt(clonedIDArray.length) - 1]);
        let newSectionCount = clonedIDCount + 1;
        let cloneSectionDivArray;
        clonedSection
            .attr('id', function (idx, attrVal) {
                try {
                    cloneSectionDivArray = attrVal.split('_');
                    if (cloneSectionDivArray.length === 2) {
                        return cloneSectionDivArray[0] + '_' + (parseInt(cloneSectionDivArray[1]) + 1);
                    }
                    if (cloneSectionDivArray.length === 3) {
                        return cloneSectionDivArray[0] + '_' + cloneSectionDivArray[1] + '_' + (parseInt(cloneSectionDivArray[2]) + 1);
                    }
                    if (cloneSectionDivArray.length === 4) {
                        return cloneSectionDivArray[0] + '_' + cloneSectionDivArray[1] + '_' + cloneSectionDivArray[2] + '_' + (parseInt(cloneSectionDivArray[3]) + 1);
                    }

                } catch (e) {
                }
            })
            .find(":input")
            .attr('id', function (idx, attrVal) {
                try {
                    cloneSectionDivArray = attrVal.split('_');
                    if (cloneSectionDivArray.length === 2) {
                        return cloneSectionDivArray[0] + '_' + (parseInt(cloneSectionDivArray[1]) + 1);
                    }
                    if (cloneSectionDivArray.length === 3) {
                        return cloneSectionDivArray[0] + '_' + cloneSectionDivArray[1] + '_' + (parseInt(cloneSectionDivArray[2]) + 1);
                    }
                    if (cloneSectionDivArray.length === 4) {
                        return cloneSectionDivArray[0] + '_' + cloneSectionDivArray[1] + '_' + cloneSectionDivArray[2] + '_' + (parseInt(cloneSectionDivArray[3]) + 1);
                    }
                } catch (e) {
                }
            })
            .attr('name', function (idx, attrVal) {
                try {
                    return attrVal.replace('[entityMember][' + clonedIDCount + ']', '[entityMember][' + newSectionCount + ']');
                } catch (e) {
                }
            })
            .attr('type', function (idx, attrVal) {
                if (attrVal === 'radio'|| attrVal === 'checkbox') {
                    $(this).prop("checked", false);
                } else {
                    $(this).val('');
                }
            })
            .removeAttr('checked').prop("checked", false).end()
            .find('.dateClass').datepicker({
                autoclose: true,
                changeMonth: true,
                changeYear: true,
                dateFormat: 'mm/dd/yy'
            }).end()
            .find('.mask_phone').inputmask("mask", {mask: "(999) 999 - 9999 Ext 9999"}).end()
            .find('.mask_ssn').inputmask("mask", {mask: "***********"}).end()
            .find('.mask_cellnew').inputmask("mask", {mask: "(999) 999 - 9999"}).end()
            .find('label').attr('for', function (idx, attrVal) {
            try {
                cloneSectionDivArray = attrVal.split('_');
                if (cloneSectionDivArray.length === 2) {
                    return cloneSectionDivArray[0] + '_' + (parseInt(cloneSectionDivArray[1]) + 1);
                }
                if (cloneSectionDivArray.length === 3) {
                    return cloneSectionDivArray[0] + '_' + cloneSectionDivArray[1] + '_' + (parseInt(cloneSectionDivArray[2]) + 1);
                }
            } catch (e) {
            }
        }).end()
            .find('span')
            .attr('data-inc-id', function (idx, attrVal) {
                try {
                    cloneSectionDivArray = attrVal.split('_');
                    if (cloneSectionDivArray.length === 2) {
                        return cloneSectionDivArray[0] + '_' + (parseInt(cloneSectionDivArray[1]) + 1);
                    }
                    if (cloneSectionDivArray.length === 3) {
                        return cloneSectionDivArray[0] + '_' + cloneSectionDivArray[1] + '_' + (parseInt(cloneSectionDivArray[2]) + 1);
                    }
                } catch (e) {
                }
            }).end()
            .find('span')
            .attr('data-entityMemberInc', function (idx, attrVal) {
                try {
                    return parseInt(attrVal) + 1;
                } catch (e) {
                }
            })
            .end()
            .find('span')
            .attr('data-entityMemberId', function (idx, attrVal) {
                try {
                    return parseInt(attrVal) + 1;
                } catch (e) {
                }
            })
            .end()
            .find('span')
            .attr('data-RowId', function (idx, attrVal) {
                try {
                    return 0;
                } catch (e) {
                }
            })
            .end()
            .insertAfter('.' + section + ':visible:last');
    }

    static parentEntityMemberFields(_element, i) {
        let number = parseFloat($(_element).val());
        let fileRow = $('#fileRow').val();
        let publicUser = parseInt($('#publicUser').val());
        if (!number) {
            toastrNotification('Please enter a valid number', 'error');
            $(_element).val(''); //reset the value
            return false;
        }
        if (number > 10) {
            toastrNotification('Please enter a valid number and less than or equal to 10', 'error');
            $(_element).val(''); //reset the value
            return false;
        }
        let row = $(_element).data('row');
        let inc = $(_element).data('inc');
        let lmrid = $(_element).data('lmrid');
        let memberId = $(_element).data('memberid');
        let activeTab = $(_element).data('activetab');
        let formContainerId = $(_element).attr('id');
        if ($(_element).attr('id') === 'parentEntityMemberCount') {
            formContainerId = '1_1';
        }
        let formContainer;
        if(row === 1) {
            formContainer = 'entityMembersDiv_' + formContainerId;
        } else {
            formContainer = 'formContainer_' + formContainerId;
        }
        HTTP.Post(
            '/backoffice/businessEntity/',
            {
                number: number,
                row: row,
                inc: inc,
                lmrid: lmrid,
                memberId: memberId,
                activeTab: activeTab,
                fileRow: fileRow,
                publicUser: publicUser,
            },
            function (response) {
                let html = response.html;
                $('#' + formContainer).empty().append(html);
                // Reapply the input mask to the newly appended input fields
                $('.mask_phone:enabled').inputmask("mask", {mask: "(999) 999 - 9999 Ext 9999"});
                $('.mask_ssn:enabled').inputmask("mask", {mask: "***********"});
                $('.mask_cellnew:enabled').inputmask("mask", {mask: "(999) 999 - 9999"});
                $('.mask_ein:enabled').inputmask("mask", {mask: "99-9999999"});
                $('.memberCreditScore:enabled').inputmask({
                    mask: "999",
                    placeholder: '',
                });
                // Initialize tooltips for the newly added elements
                $('#' + formContainer + ' [data-toggle="tooltip"]').tooltip();
            }
        );
    }

    static removeEntityMemberFieldsCV3(_this) {
        let accordion = $(_this).data('rem-accordion');
        let lmrid = $(_this).data('rem-lmrid');
        let rowId = $(_this).data('rem-rowid');
        let pkId = $(_this).data('rem-tableid');
        let accordionDiv = $('#' + accordion);
        console.log({
            func: 'removeEntityMemberFieldsCV3',
            accordion: accordion,
            lmrid: lmrid,
            tableId: pkId,
        });
        if(lmrid) { //remove from the db
            let conf = 'Are you sure to delete?';
            $.confirm({
                icon: 'fas fa-exclamation-triangle text-danger',
                closeIcon: true,
                title: 'Confirm',
                content: conf,
                type: 'red',
                backgroundDismiss: true,
                buttons: {
                    yes: function () {
                        HTTP.Post('/backoffice/businessEntity/member/remove_member',
                            {
                                lmrid: lmrid,
                                tableId: pkId,
                            },
                            function (response) {
                                if (response.success) {
                                    // Dispose of any tooltips within the card to be deleted
                                    accordionDiv.find('[data-toggle="tooltip"]').tooltip('dispose');
                                    accordionDiv.remove();
                                    toastrNotification(response.success, 'success');
                                } else {
                                    toastrNotification(response.error, 'error');
                                }
                            });
                    }, cancel: function () {

                    },
                },
                onClose: function () {

                },
            });
        } else { //remove from the ui
            // Dispose of any tooltips within the card to be deleted
            accordionDiv.find('[data-toggle="tooltip"]').tooltip('dispose');
            accordionDiv.remove();
        }
    }

    static loadEntityMemberFields() {
        let lmrid = $('#encryptedLId').val();
        let parentEntityMemberCount = $('#parentEntityMemberCount').val();
        if (lmrid && parentEntityMemberCount) {
            HTTP.Post(
                '/backoffice/businessEntity/',
                {
                    lmrid: lmrid,
                    number: parentEntityMemberCount,
                },
                function (response) {
                    let html = response.html;
                    $('#formContainer_1_1').empty().append(html);
                    // Initialize tooltips for the newly added elements
                    $('#formContainer_1_1 [data-toggle="tooltip"]').tooltip();
                });
        }
    }

    static syncBorrowerProfileEntity(CID, CBEID) {
        HTTP.Post('/backoffice/borrowerProfile/profile/loadBorrowerProfile', {
            CID: CID,
            CBEID: CBEID,
            sync: 1,
            }, function (data) {
                if (data.html) {
                    showHideBusinessEntityFields($('#borrowerType:visible').val());
                    $('#entityMembersDiv_1_1').empty().html(data.html);
                    $('#borrowerProfileEntityInfoSync').val('1');
                }
            }
        );
    }

    static calculateOwnerShip(element) {
        // Get all class names
        let allClasses = $(element).attr('class').split(/\s+/); //split class names

        // Filter class names that match the pattern className_#
        let allFields = allClasses.filter(function(className) {
            return /^memberOwnership_\d+$/.test(className);
        });
        let ownershipPercentage = 0;
        let check = 1;
        $('.' + allFields).each(function () {
            //console.log($(this).attr('class'));
            if (!$(this).val()) {
                check = 0;
            } //to validate only when all fields are filled
            ownershipPercentage += parseFloat($(this).val());
        });
        if (check && ownershipPercentage > 100) {
            toastrNotification('Total ownership percentage should be 100 %', 'error');
            return false;
        }

        console.log({
            func: 'calculateOwnerShip',
            className : allClasses,
            allFields : allFields,
            ownershipPercentage: ownershipPercentage,
        });
    }

    static showHideMaritalFields(_this) {

        let val = $(_this).val();
        let index = $(_this).data('index');
        let MarriedFields = ['memberMarriageDate', 'memberMaidenName', 'memberSpouseName'];
        let UnMarriedFields = [];
        let SeparatedFields = ['memberDivorceDate'];

        switch (val) {
            case 'Married':
                BusinessEntitySection.showMarriedFields(MarriedFields, index);
                BusinessEntitySection.hideSeparatedFields(SeparatedFields, index);
                break;
            case 'Unmarried':
                BusinessEntitySection.hideMarriedFields(MarriedFields, index);
                BusinessEntitySection.hideSeparatedFields(SeparatedFields, index);
                break;
            case 'Separated':
                BusinessEntitySection.showMarriedFields(MarriedFields, index);
                BusinessEntitySection.showSeparatedFields(SeparatedFields, index);
                break;
            default :
                break;
        }
    }

    static showMarriedFields(MarriedFields, index) {
        MarriedFields.forEach(function (element) {
            $('#' + element + '_' + index).show();
        });
    }

    static hideMarriedFields(MarriedFields, index) {
        MarriedFields.forEach(function (element) {
            $('#' + element + '_' + index).hide();
        });
    }
    static showSeparatedFields(SeparatedFields, index) {
        SeparatedFields.forEach(function (element) {
            var $el = $('#' + element + '_' + index);
            $el.show().removeClass('secHide');
        });
    }

    static hideSeparatedFields(SeparatedFields, index) {
        SeparatedFields.forEach(function (element) {
            $('#' + element + '_' + index).hide();
        });
    }

    static calculateTotalNetWorth(_this) {

        //Total Net Worth-→ Auto-calculate [(Cash/Savings/Stocks + Real Estate Value + Retirement balance) - (Credit card balances + Mortgage balances + Auto loan balances)]= Total Net Worth
        let index = $(_this).data('index');
        let sumFields = 0;
        let minusFields = 0;
        let totalNetWorth = 0;
        let fields = [
            'memberCashSavingsStocksBalance',
            'memberRealEstateValue',
            'memberRetirementAccountBalance',
        ];
        fields.forEach(function (element) {
            let val = replaceCommaValues($('#' + element + index).val());
            if (val) {
                sumFields += val;
            }
        });

        let otherFields = [
            'memberCreditCardBalance',
            'memberMortgageBalance',
            'memberAutoLoanBalance',
        ];
        otherFields.forEach(function (element) {
            let val = replaceCommaValues($('#' + element + index).val());
            if (val) {
                minusFields += val;
            }
        });

        totalNetWorth = sumFields - minusFields;

        currencyConverter('#memberTotalNetWorth' + index, totalNetWorth.toFixed(2));
    }
    static getAddress() {
        return [$('#presentAddress').val(), $('#presentCity').val(), $("#presentState option:selected").text(), $('#presentZip').val()]
            .filter(Boolean) // Remove empty values
            .join(' , ');
    }

    static sameAsBorrowerInfo(ele, memberId, parentId) {
        let chk = $(ele).is(':checked');
        // Function to get concatenated address
        // Get values if checked, else set to empty
        let data = chk ? {
            name: $('#borrowerFName').val() + " " + $('#borrowerLName').val(),
            address: BusinessEntitySection.getAddress(),
            phone: $('#workNumber').val(),
            cell: $('#cellNo').val(),
            ssn: $('#ssn').val(),
            dob: $('#borrowerDOB').val(),
            email: $('#borrowerEmail').val(),
            dl: $('#driverLicenseNumber').val(),
            dls: $('#driverLicenseState').val(),
            creditScore: $('#borCreditScoreRange').val(),
            maritalStatus: $('input[name="maritalStatus"]:checked').val(),
            borrowerCitizenship: $('input[name="borrowerCitizenship"]:checked').val(),
            marriageDate: $('#marriageDate').val(),
            divorceDate: $('#divorceDate').val(),
            maidenName: $('#maidenName').val(),
            spouseName: $('#spouseName').val(),
        } : {};

        // Assign values
        $('#memberName' + memberId + '_' + parentId).val(data.name || '');
        $('#memberAddress' + memberId + '_' + parentId).val(data.address || '');
        $('#memberPhone' + memberId + '_' + parentId).val(data.phone || '');
        $('#memberCell' + memberId + '_' + parentId).val(data.cell || '');
        $('#memberSSN' + memberId + '_' + parentId).val(data.ssn || '');
        $('#memberDOB' + memberId + '_' + parentId).val(data.dob || '');
        $('#memberEmail' + memberId + '_' + parentId).val(data.email || '');
        $('#memberDriversLicenseState' + memberId + '_' + parentId).val(data.dls || '');
        $('#memberDriversLicense' + memberId + '_' + parentId).val(data.dl || '');
        $('#memberCreditScore' + memberId + '_' + parentId).val(data.creditScore || '');

        let _memberMarriageDate   = $('#memberMarriageDate' + memberId + '_' + parentId);
        let _memberDivorceDate    = $('#memberDivorceDate' + memberId + '_' + parentId);
        let _memberMaidenName     = $('#memberMaidenName' + memberId + '_' + parentId);
        let _memberSpouseName     = $('#memberSpouseName' + memberId + '_' + parentId);

        $(`input[name="memberMaritalStatus_${memberId}_${parentId}"]`).prop('checked', false);
        _memberMarriageDate.val('');
        _memberDivorceDate.val('');
        _memberMaidenName.val('');
        _memberSpouseName.val('');

        if (data.maritalStatus) {
            $(`input[name="memberMaritalStatus_${memberId}_${parentId}"][value="${data.maritalStatus}"]`)
                .prop('checked', true)
                .trigger('change');

            if (data.maritalStatus === 'Married' || data.maritalStatus === 'Separated') {
                _memberMarriageDate.val(data.marriageDate || '');
                _memberDivorceDate.val(data.divorceDate || '');
                _memberMaidenName.val(data.maidenName || '');
                _memberSpouseName.val(data.spouseName || '');
            }
        }

        $(`input[name="memberCitizenship_${memberId}_${parentId}"]`).prop('checked', false);

        if (data.borrowerCitizenship) {
            $(`input[name="memberCitizenship_${memberId}_${parentId}"][value="${data.borrowerCitizenship}"]`)
                .prop('checked', true)
                .trigger('change');
        }
    }

}

