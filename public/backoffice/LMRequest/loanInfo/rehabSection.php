<?php
global $fileTab, $isEF,$HMLOLoanInfoSectionsDisp;

use models\constants\gl\glGroundUpConstruction;
use models\constants\gl\glLotStatus;
use models\constants\gl\glPlansAndPermitStatus;
use models\constants\gl\glYesNo;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\backoffice\LoanInfo;
use models\Controllers\LMRequest\loanPropertySummary;
use models\Controllers\loanForm;
use models\HMLOLoanTermsCalculation;
use models\lendingwise\tblBuildingAnalysisNeed;
use models\lendingwise\tblBuildingAnalysisOutstanding;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Strings;

$ischk = count($RCISecArr = BaseHTML::sectionAccess2(['sId' => 'RCI', 'opt' => $fileTab, 'activeTab' => LMRequest::$activeTab])) > 0 ? 'checked' : '';

if (PageVariables::$publicUser == 1) {
    $ischk = LMRequest::$LMRId > 0 && $ischk != '' ? 'checked' : '';
}

$glLotStatus = glLotStatus::$glLotStatus;
?>


<div class="card card-custom rehabCard RCI <?php if (trim($ischk) == 'checked') {
    echo 'secShow';
} else {
    echo 'secHide';
} ?>" id="rehabCard" style="<?php echo $HMLOLoanInfoSectionsDisp; ?>">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label"><?php echo BaseHTML::getSectionHeading('RCI'); ?> </h3>
            <?php if (trim(BaseHTML::getSectionTooltip('RCI')) != '') { ?>&nbsp;
                <i class="popoverClass fas fa-info-circle text-primary "
                   data-html="true"
                   data-content="<?php echo BaseHTML::getSectionTooltip('RCI'); ?>"></i>
            <?php } ?>
        </div>
        <div class="card-toolbar">
            <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none cursor-pointer"
                  data-card-tool="toggle"
                  data-toggle="tooltip" data-placement="top" title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>
            <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none cursor-pointer"
                  data-card-tool="reload"
                  data-toggle="tooltip" data-placement="top" title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </span>
            <span
                    class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer"
                    data-card-tool="toggle"
                    data-section="rehabCard"
                    data-toggle="tooltip" data-placement="top" title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>
        </div>
    </div>
    <div class="card-body rehabCard_body">
        <div class="row">
            <?php
            loanForm::pushSectionID('RCI');
            ?>
            <div class="col-md-12"><!-- Row 12 Start -->
                <div class="row">
                    <div class="row12SeparatorCls col-md-3 doesPropertyNeedRehabSection propertyNeedRehab_disp <?php echo loanForm::showField('propertyNeedRehab'); ?>"
                         style="<?php echo HMLOLoanTermsCalculation::$doesPropertyNeedRehabSection ?>">
                        <div class="row form-group">
                            <div class="showHidePropertyRehab"
                                 style="<?php echo(loanPropertySummary::showHidePropertyRehabCv3(LMRequest::$PCID, LMRequest::File()->getTblLMRClientType_by_LMRID()->ClientType) ? 'display:none;' : ''); ?>">
                                <?php echo loanForm::label('propertyNeedRehab',
                                    'col-md-12 ',
                                    '',
                                    loanForm::changeLog(
                                        LMRequest::myFileInfo()->fileHMLOPropertyInfo()->HMLOPID,
                                        'propertyNeedRehab',
                                        \models\lendingwise\tblFileHMLOPropInfo::class,
                                        loanForm::getFieldLabel('propertyNeedRehab')
                                    ),
                                ); ?>
                                <div class="col-md-12">
                                    <?php if (LMRequest::$allowToEdit) { ?>
                                        <div class="radio-inline">
                                            <label class="radio radio-solid " for="propertyNeedRehabYes">
                                                <input type="radio"
                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'propertyNeedRehab', 'sArr' => $RCISecArr, 'opt' => 'M']); ?>"
                                                       name="propertyNeedRehab"
                                                       id="propertyNeedRehabYes"
                                                       value="Yes"
                                                       tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                                    <?php echo Strings::isChecked('Yes', HMLOLoanTermsCalculation::$propertyNeedRehab); ?>
                                                       onclick="hideAndShowPropertyNeedRehab();"
                                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyNeedRehab', 'sArr' => $RCISecArr, 'opt' => 'I']); ?>><span></span>Yes
                                            </label>
                                            <label class="radio radio-solid " for="propertyNeedRehabNo">
                                                <input type="radio"
                                                       name="propertyNeedRehab"
                                                       id="propertyNeedRehabNo"
                                                       value="No"
                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'propertyNeedRehab', 'sArr' => $RCISecArr, 'opt' => 'M']); ?>"
                                                       tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                                    <?php echo Strings::isChecked('No', HMLOLoanTermsCalculation::$propertyNeedRehab); ?>
                                                       onclick="hideAndShowPropertyNeedRehab();"
                                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyNeedRehab', 'sArr' => $RCISecArr, 'opt' => 'I']); ?>><span></span>No
                                            </label></div>
                                    <?php } else {
                                        echo '<b>' . HMLOLoanTermsCalculation::$propertyNeedRehab . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row12SeparatorCls col-md-3 propertyNeedRehabinitialTddisp costSpent_disp <?php echo loanForm::showField('costSpent'); ?>"
                         style="<?php echo HMLOLoanTermsCalculation::$doesPropertyNeedRehabDispTDDiv ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('costSpent', 'col-md-12 '); ?>
                            <div class="col-md-12">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text" name="costSpent" id="costSpent"
                                               value="<?php echo Currency::formatDollarAmountWithDecimalZeros(HMLOLoanTermsCalculation::$costSpent) ?>"
                                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'costSpent', 'sArr' => $RCISecArr, 'opt' => 'M']); ?>"
                                               onblur="currencyConverter(this, this.value);calculateTotalProjectCost();updateLoanDetail();"
                                               onkeyup='return restrictAlphabetsLoanTerms(this)'
                                               TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'costSpent', 'sArr' => $RCISecArr, 'opt' => 'I']); ?>
                                               autocomplete="off"/>
                                    </div>
                                <?php } else {
                                    echo '<b>$ ' . Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$costSpent) . '</b>';
                                } ?>
                            </div>
                        </div>
                    </div>
                    <div class="row12SeparatorCls col-md-3 propertyNeedRehabinitialTddisp assessedValue_disp <?php echo loanForm::showField('assessedValue'); ?>"
                         style="<?php echo HMLOLoanTermsCalculation::$doesPropertyNeedRehabDispTDDiv ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('assessedValue', 'col-md-12 '); ?>
                            <div class="col-md-12">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text"
                                               name="assessedValue"
                                               id="assessedValue"
                                               placeholder="0.00"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$assessedValue) ?>"
                                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assessedValue', 'sArr' => $RCISecArr, 'opt' => 'M']); ?>"
                                               onblur="currencyConverter(this, this.value);updateLoanDetail();validateMinMaxLoanGuidelines();"
                                               onkeyup='return restrictAlphabetsLoanTerms(this)'
                                               TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>"
                                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'assessedValue', 'sArr' => $RCISecArr, 'opt' => 'I']); ?>/>
                                    </div>
                                <?php } else {
                                    echo '<b>$ ' . Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$assessedValue) . '</b>';
                                } ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!-- Row 12 End -->
            <div class="col-md-12 row12SeparatorCls separator separator-dashed my-2 separator12"
                 id="row12SeparatorDiv"></div>

            <div class="col-md-12"><!-- Row 13 Start -->
                <div class="row">
                    <div class="row13SeparatorCls col-md-3 propertyNeedRehabinitialTddisp isThisGroundUpConstruction_disp <?php echo loanForm::showField('isThisGroundUpConstruction'); ?>"
                         style="<?php echo HMLOLoanTermsCalculation::$doesPropertyNeedRehabDispTDDiv ?>">
                        <div class="row form-group doesPropertyNeedRehabDispDiv">
                            <?php echo loanForm::label('isThisGroundUpConstruction', 'col-md-12 '); ?>
                            <div class="col-md-12">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <div class="radio-inline">
                                        <label class="radio radio-solid " for="isThisGroundUpConstructionYes">
                                            <input type="radio"
                                                   class="<?php echo BaseHTML::fieldAccess(['fNm' => 'isThisGroundUpConstruction', 'sArr' => $RCISecArr, 'opt' => 'M']); ?>"
                                                   name="isThisGroundUpConstruction"
                                                   id="isThisGroundUpConstructionYes" value="Yes"
                                                   onclick="showAndHideGroupUpFields(this.value)"
                                                   tabindex="<?php echo LoanInfo::$tabIndex++; ?>" <?php echo Strings::isChecked('Yes', LoanInfo::$isThisGroundUpConstruction); ?>
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'isThisGroundUpConstruction', 'sArr' => $RCISecArr, 'opt' => 'I']); ?>><span></span>Yes
                                        </label>
                                        <label class="radio radio-solid " for="isThisGroundUpConstructionNo">
                                            <input type="radio" name="isThisGroundUpConstruction"
                                                   id="isThisGroundUpConstructionNo" value="No"
                                                   onclick="showAndHideGroupUpFields(this.value)"
                                                   class="<?php echo BaseHTML::fieldAccess(['fNm' => 'isThisGroundUpConstruction', 'sArr' => $RCISecArr, 'opt' => 'M']); ?>"
                                                   tabindex="<?php echo LoanInfo::$tabIndex++; ?>" <?php echo Strings::isChecked('No', LoanInfo::$isThisGroundUpConstruction); ?>
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'isThisGroundUpConstruction', 'sArr' => $RCISecArr, 'opt' => 'I']); ?>><span></span>No
                                        </label></div>
                                <?php } else {
                                    echo '<b>' . LoanInfo::$isThisGroundUpConstruction . '</b>';
                                } ?>
                            </div>
                        </div>
                    </div>
                    <div style="<?php echo HMLOLoanTermsCalculation::$groundUpDispTDDiv; ?>"
                         class="row13SeparatorCls  col-md-3 groundUpFields propertyNeedRehabinitialTddisp lotStatus_disp <?php echo loanForm::showField('lotStatus'); ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('lotStatus', 'col-md-12 '); ?>
                            <div class="col-md-12">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <select
                                            class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'lotStatus', 'sArr' => $RCISecArr, 'opt' => 'M']); ?>"
                                            name="lotStatus" id="lotStatus"
                                            TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'lotStatus', 'sArr' => $RCISecArr, 'opt' => 'I']); ?>>
                                        <option value=""> - Select -</option>
                                        <?php
                                        for ($i = 0; $i < count($glLotStatus); $i++) {
                                            $sOpt = '';
                                            $sOpt = Arrays::isSelected(trim($glLotStatus[$i]), LoanInfo::$lotStatus);
                                            echo "<option value=\"" . trim($glLotStatus[$i]) . "\" " . $sOpt . '>' . trim($glLotStatus[$i]) . '</option>';
                                        }
                                        ?>
                                    </select>
                                <?php } else {
                                    echo '<b>' . LoanInfo::$lotStatus . '</b>';
                                } ?>
                            </div>
                        </div>
                    </div>
                    <div class="row13SeparatorCls col-md-3 groundUpFields propertyNeedRehabinitialTddisp lotPurchasePrice_disp <?php echo loanForm::showField('lotPurchasePrice'); ?>"
                         style="<?php echo HMLOLoanTermsCalculation::$groundUpDispTDDiv; ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('lotPurchasePrice', 'col-md-12 '); ?>
                            <div class="col-md-12">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text"
                                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'lotPurchasePrice', 'sArr' => $RCISecArr, 'opt' => 'M']); ?>"
                                               name="lotPurchasePrice" id="lotPurchasePrice"
                                               onblur="currencyConverter(this, this.value);"
                                               onkeyup='return restrictAlphabetsLoanTerms(this)'
                                               placeholder="0.00"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$lotPurchasePrice); ?>"
                                               tabindex="<?php echo LoanInfo::$tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'lotPurchasePrice', 'sArr' => $RCISecArr, 'opt' => 'I']); ?>/>
                                    </div>
                                <?php } else {
                                    echo '<b>$ ' . Currency::formatDollarAmountWithDecimal(LoanInfo::$lotPurchasePrice) . '</b>';
                                } ?>
                            </div>
                        </div>
                    </div>
                    <div class="row13SeparatorCls col-md-3 groundUpFields propertyNeedRehabinitialTddisp currentLotMarket_disp <?php echo loanForm::showField('currentLotMarket'); ?>"
                         style="<?php echo HMLOLoanTermsCalculation::$groundUpDispTDDiv; ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('currentLotMarket', 'col-md-12 '); ?>
                            <div class="col-md-12">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text"
                                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'currentLotMarket', 'sArr' => $RCISecArr, 'opt' => 'M']); ?>"
                                               name="currentLotMarket" id="currentLotMarket"
                                               onblur="currencyConverter(this, this.value);"
                                               onkeyup='return restrictAlphabetsLoanTerms(this)'
                                               placeholder="0.00"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$currentLotMarket); ?>"
                                               tabindex="<?php echo LoanInfo::$tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'currentLotMarket', 'sArr' => $RCISecArr, 'opt' => 'I']); ?>/>
                                    </div>
                                <?php } else {
                                    echo '<b>$ ' . Currency::formatDollarAmountWithDecimal(LoanInfo::$currentLotMarket) . '</b>';
                                } ?>
                            </div>
                        </div>
                    </div>
                    <?php if ($isEF != 1) { ?>
                        <div class="row13SeparatorCls col-md-3 form-group <?php echo loanForm::showField('isSubjectUnderConst'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label2('isSubjectUnderConst', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php echo loanForm::radio(
                                        'isSubjectUnderConst',
                                        LMRequest::$allowToEdit,
                                        LoanInfo::$tabIndex++,
                                        LoanInfo::$isSubjectUnderConst,
                                        [
                                            'Yes' => 'Yes',
                                            'No'  => 'No'
                                        ],
                                        '',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                    <div class="row13SeparatorCls col-md-3 propertyNeedRehabFootageTddisp"
                         style="<?php echo HMLOLoanTermsCalculation::$doesPropertyNeedRehabFootageDispTDDiv; ?>">
                        <div class="row">
                            <div class="col-md-12 form-group <?php echo loanForm::showField('haveBorSquareFootage'); ?>">
                                <div class="row">
                                    <?php echo loanForm::label2('haveBorSquareFootage', 'col-md-12 '); ?>
                                    <div class="col-md-12">
                                        <?php echo loanForm::radio(
                                            'haveBorSquareFootage',
                                            LMRequest::$allowToEdit,
                                            LoanInfo::$tabIndex++,
                                            HMLOLoanTermsCalculation::$haveBorSquareFootage,
                                            [
                                                'Yes' => 'Yes',
                                                'No'  => 'No'
                                            ],
                                            'showAndHideBorSquareFootage(this.value, \'borSquareFootageDiv\');',
                                            ''
                                        ); ?>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div class="row13SeparatorCls  col-md-3 borSquareFootageDiv borNoOfSquareFeet_child">
                        <div class="row">
                            <div class="col-md-12 form-group  borSquareFootageDiv borNoOfSquareFeet_child
                            <?php echo BaseHTML::parentFieldAccess(['fNm' => 'haveBorSquareFootage', 'sArr' => $RCISecArr,
                                                                    'pv'  => HMLOLoanTermsCalculation::$haveBorSquareFootage, 'av' => 'Yes']); ?>"
                                 id="borSquareFootageDiv">
                                <div class="row">
                                    <?php echo loanForm::label2('borNoOfSquareFeet', 'col-md-12 '); ?>
                                    <div class="col-md-12">
                                        <?php if (LMRequest::$allowToEdit) { ?>
                                            <input type="number" name="borNoOfSquareFeet" id="borNoOfSquareFeet"
                                                   tabindex="<?php echo LoanInfo::$tabIndex++; ?>" <?php echo BaseHTML::fieldAccess([
                                                'fNm' => 'borNoOfSquareFeet', 'sArr' => $RCISecArr, 'opt' => 'I']); ?>
                                                   class="form-control <?php echo BaseHTML::fieldAccess(['fNm'  => 'borNoOfSquareFeet',
                                                                                                         'sArr' => $RCISecArr, 'opt' => 'M']); ?>"
                                                   value="<?php echo LoanInfo::$borNoOfSquareFeet; ?>" maxlength="10"
                                                   autocomplete="off">
                                        <?php } else { ?>
                                            <label><?php echo LoanInfo::$borNoOfSquareFeet ?></label>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row13SeparatorCls col-md-3  <?php echo loanForm::showField('rehabToBeMade'); ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label2('rehabToBeMade', 'col-md-12 '); ?>
                            <div class="col-md-12">
                                <?php echo loanForm::textarea(
                                    'rehabToBeMade',
                                    LMRequest::$allowToEdit,
                                    LoanInfo::$tabIndex++,
                                    LoanInfo::$rehabToBeMade,
                                    '',
                                    ''
                                ); ?>
                            </div>
                        </div>
                    </div>
                    <div class="row13SeparatorCls col-md-3 form-group <?php echo loanForm::showField('rehabTime'); ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label2('rehabTime', 'col-md-12 '); ?>
                            <div class="col-md-12">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <input type="number" name="rehabTime" id="rehabTime"
                                           class="form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'rehabTime', 'sArr' => $RCISecArr, 'opt' => 'M']); ?>"
                                           value="<?php echo LoanInfo::$rehabTime; ?>"
                                           tabindex="<?php echo LoanInfo::$tabIndex++; ?>" autocomplete="off"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'rehabTime', 'sArr' => $RCISecArr, 'opt' => 'I']); ?> >
                                <?php } else { ?>
                                    <label><?php echo LoanInfo::$rehabTime; ?></label>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!-- Row 13 End -->
            <div class="col-md-12 row13SeparatorCls separator separator-dashed my-2 separator12"
                 id="row13SeparatorDiv"></div>

            <div class="col-md-12"><!-- Row 14 Start -->
                <div class="row">
                    <div class="row14SeparatorCls col-md-3 <?php echo loanForm::showField('plansAndPermitsStatus'); ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('plansAndPermitsStatus', 'col-md-12 font-weight-bold label_highlight'); ?>
                            <div class="col-md-12">
                                <?php
                                echo loanForm::select(
                                    'plansAndPermitsStatus',
                                    LMRequest::$allowToEdit,
                                    1,
                                    LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->plansAndPermitsStatus,
                                    glPlansAndPermitStatus::getGlPlansAndPermitStatus(PageVariables::$PCID),
                                    ' ',
                                    'chzn-select form-control',
                                    ' ',
                                    'Please Select ' . loanForm::$permissions['plansAndPermitsStatus']->fieldLabel
                                );
                                ?>
                            </div>
                        </div>
                    </div>
                    <div class="row14SeparatorCls col-md-3 <?php echo loanForm::showField('isProjectRequireRezoning'); ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('isProjectRequireRezoning', 'col-md-12 font-weight-bold label_highlight'); ?>
                            <div class="col-md-12">
                                <?php
                                echo loanForm::select(
                                    'isProjectRequireRezoning',
                                    LMRequest::$allowToEdit,
                                    1,
                                    LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->isProjectRequireRezoning,
                                    glYesNo::$glYesNo,
                                    ' ',
                                    'chzn-select form-control',
                                    ' ',
                                    'Please Select ' . loanForm::$permissions['isProjectRequireRezoning']->fieldLabel
                                );
                                ?>
                            </div>
                        </div>
                    </div>
                    <div class="row14SeparatorCls col-md-3 <?php echo loanForm::showField('anticipatedHoldTime'); ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('anticipatedHoldTime', 'col-md-12 font-weight-bold label_highlight'); ?>
                            <div class="col-md-12">
                                <?php
                                echo loanForm::number(
                                    'anticipatedHoldTime',
                                    LMRequest::$allowToEdit,
                                    1,
                                    LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->anticipatedHoldTime,
                                    null,
                                    '',
                                    null,
                                    'Months'
                                );
                                ?>
                            </div>
                        </div>
                    </div>
                    <div class="row14SeparatorCls col-md-3 <?php echo loanForm::showField('anticipatedPlansPermitTimeline'); ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('anticipatedPlansPermitTimeline', 'col-md-12 font-weight-bold label_highlight'); ?>
                            <div class="col-md-12">
                                <?php
                                echo loanForm::number(
                                    'anticipatedPlansPermitTimeline',
                                    LMRequest::$allowToEdit,
                                    1,
                                    LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->anticipatedPlansPermitTimeline,
                                    null,
                                    '',
                                    null,
                                    'Months'
                                );
                                ?>
                            </div>
                        </div>
                    </div>
                    <div class="row14SeparatorCls col-md-3 <?php echo loanForm::showField('anticipatedConstructionTimeline'); ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('anticipatedConstructionTimeline', 'col-md-12 font-weight-bold label_highlight'); ?>
                            <div class="col-md-12">
                                <?php
                                echo loanForm::number(
                                    'anticipatedConstructionTimeline',
                                    LMRequest::$allowToEdit,
                                    1,
                                    LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->anticipatedConstructionTimeline,
                                    null,
                                    '',
                                    null,
                                    'Months'
                                );
                                ?>
                            </div>
                        </div>
                    </div>
                    <div class="row14SeparatorCls col-md-3 <?php echo loanForm::showField('buildingAnalysisOutstanding'); ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('buildingAnalysisOutstanding', 'col-md-12 font-weight-bold label_highlight'); ?>
                            <div class="col-md-12">
                                <?php
                                echo loanForm::select(
                                    'buildingAnalysisOutstanding',
                                    LMRequest::$allowToEdit,
                                    1,
                                    LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->buildingAnalysisOutstanding,
                                    tblBuildingAnalysisOutstanding::options(),
                                    '',
                                    ' chzn-select ',
                                    ' ',
                                    'Please Select ' . loanForm::getFieldLabel('buildingAnalysisOutstanding')
                                );
                                ?>
                            </div>
                        </div>
                    </div>
                    <div class="row14SeparatorCls col-md-3 <?php echo loanForm::showField('buildingAnalysisNeed'); ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('buildingAnalysisNeed', 'col-md-12 font-weight-bold label_highlight'); ?>
                            <div class="col-md-12">
                                <?php
                                echo loanForm::select(
                                    'buildingAnalysisNeed',
                                    LMRequest::$allowToEdit,
                                    1,
                                    LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->buildingAnalysisNeed,
                                    tblBuildingAnalysisNeed::options(),
                                    '',
                                    ' chzn-select ',
                                    ' ',
                                    'Please Select ' . loanForm::getFieldLabel('buildingAnalysisNeed')
                                );
                                ?>
                            </div>
                        </div>
                    </div>

                    <div class="row14SeparatorCls col-md-3 <?php echo loanForm::showField('constructionType'); ?>">
                        <div class="form-group ">
                            <?php echo loanForm::label(
                                'constructionType',
                                ' ',
                                '',
                                loanForm::changeLog(
                                    LMRequest::myFileInfo()->getLoanPropertySummary()->id,
                                    'constructionType',
                                    \models\lendingwise\tblLoanPropertySummary::class,
                                    loanForm::getFieldLabel('constructionType')
                                ),
                            );
                            echo loanForm::select(
                                'constructionType',
                                LMRequest::$allowToEdit,
                                1,
                                LMRequest::myFileInfo()->getLoanPropertySummary()->constructionType,
                                glGroundUpConstruction::$constructionType,
                                ' ',
                                'chzn-select form-control',
                                ' ',
                                'Please Select ' . loanForm::$permissions['constructionType']->fieldLabel
                            ); ?>
                        </div>
                    </div>

                    <div class="row14SeparatorCls col-md-3 <?php echo loanForm::showField('constructionHardCost'); ?>">
                        <div class="form-group ">
                            <?php echo loanForm::label(
                                'constructionHardCost',
                                ' ',
                                '',
                                loanForm::changeLog(
                                    LMRequest::myFileInfo()->getLoanPropertySummary()->id,
                                    'constructionHardCost',
                                    \models\lendingwise\tblLoanPropertySummary::class,
                                    loanForm::getFieldLabel('constructionHardCost')
                                ),
                            );
                            echo loanForm::currency(
                                'constructionHardCost',
                                LMRequest::$allowToEdit,
                                '1',
                                LMRequest::myFileInfo()->getLoanPropertySummary()->constructionHardCost,
                                null,
                                'loanInfoV2Form.calculateContingencyAmount();loanInfoV2Form.cloneHardCost();'
                            ); ?>
                        </div>
                    </div>

                    <div class="row14SeparatorCls col-md-3 <?php echo loanForm::showField('constructionSoftCost'); ?>">
                        <div class="form-group ">
                            <?php echo loanForm::label(
                                'constructionSoftCost',
                                ' ',
                                '',
                                loanForm::changeLog(
                                    LMRequest::myFileInfo()->getLoanPropertySummary()->id,
                                    'constructionSoftCost',
                                    \models\lendingwise\tblLoanPropertySummary::class,
                                    loanForm::getFieldLabel('constructionSoftCost')
                                ),
                            );
                            echo loanForm::currency(
                                'constructionSoftCost',
                                LMRequest::$allowToEdit,
                                '1',
                                LMRequest::myFileInfo()->getLoanPropertySummary()->constructionSoftCost,
                                null,
                                'loanInfoV2Form.calculateContingencyAmount();loanInfoV2Form.cloneSoftCost();'
                            ); ?>
                        </div>
                    </div>

                    <div class="row14SeparatorCls col-md-3 <?php echo loanForm::showField('contingencyTypeOption'); ?>">
                        <div class="form-group ">
                            <?php echo loanForm::label(
                                'contingencyTypeOption',
                                ' ',
                                '',
                                loanForm::changeLog(
                                    LMRequest::myFileInfo()->getLoanPropertySummary()->id,
                                    'contingencyTypeOption',
                                    \models\lendingwise\tblLoanPropertySummary::class,
                                    loanForm::getFieldLabel('contingencyTypeOption')
                                ),
                            );
                            echo loanForm::select(
                                'contingencyTypeOption',
                                LMRequest::$allowToEdit,
                                1,
                                LMRequest::myFileInfo()->getLoanPropertySummary()->contingencyTypeOption ? LMRequest::myFileInfo()->getLoanPropertySummary()->contingencyTypeOption : 10,
                                glGroundUpConstruction::$contingencyType,
                                'loanInfoV2Form.calculateContingencyAmount();',
                                'chzn-select form-control',
                                ' ',
                                'Please Select ' . loanForm::$permissions['contingencyTypeOption']->fieldLabel
                            );
                            ?>
                        </div>
                    </div>

                    <div class="row14SeparatorCls col-md-3 <?php echo loanForm::showField('contingencyPercentage'); ?>">
                        <div class="form-group ">
                            <?php echo loanForm::label(
                                'contingencyPercentage',
                                ' ',
                                '',
                                loanForm::changeLog(
                                    LMRequest::myFileInfo()->getLoanPropertySummary()->id,
                                    'contingencyPercentage',
                                    \models\lendingwise\tblLoanPropertySummary::class,
                                    loanForm::getFieldLabel('contingencyPercentage')
                                ),
                            );
                            echo loanForm::percentage(
                                'contingencyPercentage',
                                LMRequest::$allowToEdit,
                                '1',
                                LMRequest::myFileInfo()->getLoanPropertySummary()->contingencyPercentage ? LMRequest::myFileInfo()->getLoanPropertySummary()->contingencyPercentage : 10,
                                '  ',
                                'loanInfoV2Form.calculateContingencyAmount();',
                                LMRequest::myFileInfo()->getLoanPropertySummary()->contingencyTypeOption != 'Other',
                                5);
                            ?>
                        </div>
                    </div>

                    <div class="row14SeparatorCls col-md-3 <?php echo loanForm::showField('contingencyAmount'); ?>">
                        <div class="form-group ">
                            <?php echo loanForm::label(
                                'contingencyAmount',
                                ' ',
                                '',
                                loanForm::changeLog(
                                    LMRequest::myFileInfo()->getLoanPropertySummary()->id,
                                    'contingencyAmount',
                                    \models\lendingwise\tblLoanPropertySummary::class,
                                    loanForm::getFieldLabel('contingencyAmount')
                                ),
                            );
                            echo loanForm::currency(
                                'contingencyAmount',
                                LMRequest::$allowToEdit,
                                '1',
                                LMRequest::myFileInfo()->getLoanPropertySummary()->contingencyAmount,
                                '  ',
                                'loanInfoV2Form.calculateContingencyPercentage();',
                                LMRequest::myFileInfo()->getLoanPropertySummary()->contingencyTypeOption != 'Other',
                                '');
                            ?>
                        </div>
                    </div>
                </div>
            </div><!-- Row 13 End -->
            <div class="col-md-12 row14SeparatorCls separator separator-dashed my-2 separator12"
                 id="row14SeparatorDiv"></div>
            <?php
            loanForm::popSectionID();
            ?>
        </div>
    </div>
</div>

<script>
    $(function () {
        for(let ind = 12; ind < 15 ; ind++){
            if ($('.row'+ind+'SeparatorCls.secShow').length > 0) { //show titlessssss
                $("#row"+ind+"SeparatorDiv").show();
            } else { //hide title
                $("#row"+ind+"SeparatorDiv").hide();
            }
        }
    });
</script>
