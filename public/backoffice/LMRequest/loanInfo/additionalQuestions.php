<?php
//global variables
global $lockedSections, $allowToEdit, $publicUser, $fileTab, $LMRId, $isEF, $tabIndex;
global $acceptedPurchase, $acceptedPurchaseDispOpt, $PAExpirationDate, $isBorPersonallyGuaranteeLoan;
global $isBorBorrowedDownPayment, $borBorrowedDownPaymentDispOpt, $secondaryFinancingAmount, $borBorrowedDownPaymentExpln;
global $HMLOEstateHeldIn, $isBorIntendToOccupyPropAsPRI, $isCoBorIntendToOccupyPropAsPRI;
global $isTherePrePaymentPenalty, $prepayentSectionDisplay, $disabledInputForClient, $prePaymentPenaltyPercentage;
global $prePaymentPenalty, $extensionOptionPercentage, $prePaymentSelectValArr;
global $extensionOption, $extensionRatePercentage, $extensionOptionsAmt, $assumability, $involvedPurchase, $involvedPurchaseDispOpt;
global $wholesaleFee, $borComment, $rentalIncomePerMonthFieldDispOpt, $additionalPropertyRestrictionsDispOpt, $restrictionsExplain;
global $exitStrategy, $exitStrategyExplainDispOpt, $rentalIncomePerMonth, $additionalPropertyRestrictions;
global $landValueCls, $isOwnLand, $balloonPayment, $prePayExcessOf20percent, $limitedOrNot, $loanMadeWholly, $doesPropertyNeedRehabFootageDispTDDiv;
global $haveBorSquareFootage, $isBlanketLoan, $doesPropertyNeedRehabNoofFootageDispTDDiv, $securityInstrument;
global $borNoOfSquareFeet, $noOfPropertiesAcquiring, $desiredFundingAmount, $purposeOfLoan, $useOfFunds, $haveCurrentLoanBal;
global $doYouHaveInvoiceToFactor, $doYouHaveInvoiceToFactorDispOpt, $amount, $PCID, $loanSigning;
global $haveCurrentLoanBalDispOpt, $balance, $heldWith, $courtOrderNecessary, $loanPurpose, $prePaymentPenaltyResArr,
       $HMLOPCBasicLoanPurposeInfoArray;

global $exitFeeAmount, $exitFeePoints, $activeTab, $fileMC;

global $rehabToBeMade, $rehabTime, $isSubjectUnderConst, $areKnownHazards, $areProReports, $isSubjectSS, $changeInCircumstance, $changeDescription, $useOfProceeds;
global $hideThisField, $secondaryHolderName, $HMLOLoanInfoSectionsDisp;

use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glEstateHeldIn;
use models\constants\gl\glFirstProbate;
use models\constants\gl\glHMLOCourtOrderNecessary;
use models\constants\gl\glHMLOExitStrategy;
use models\constants\gl\glHMLOLoanPurpose;
use models\constants\gl\glHMLOLoanSigning;
use models\constants\gl\glPCID;
use models\constants\gl\glprePaymentPenalty;
use models\constants\gl\glTitleType;
use models\constants\gl\glTypeOfProperty;
use models\constants\gl\glTypeOfSale;
use models\constants\gSecurityInstrumentArray;
use models\constants\loanGuaranteeTypes;
use models\constants\purposeOfLoanArray;
use models\constants\show1003FieldsArray;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\backoffice\LoanInfo;
use models\Controllers\loanForm;
use models\CustomField;
use models\HMLOLoanTermsCalculation;
use models\lendingwise\tblFile;
use models\lendingwise\tblFileHMLOPropInfo;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Strings;

$gSecurityInstrumentArray = gSecurityInstrumentArray::$gSecurityInstrumentArray;
$glEstateHeldIn = glEstateHeldIn::$glEstateHeldIn;
$glHMLOLoanSigning = glHMLOLoanSigning::$glHMLOLoanSigning;
$glHMLOCourtOrderNecessary = glHMLOCourtOrderNecessary::$glHMLOCourtOrderNecessary;
$glHMLOLoanPurpose = glHMLOLoanPurpose::$glHMLOLoanPurpose;
$glTypeOfProperty = glTypeOfProperty::$glTypeOfProperty;
$glTitleType = glTitleType::$glTitleType;
$glFirstProbate = glFirstProbate::$glFirstProbate;
if ($PCID == glPCID::PCID_PROD_CV3) {
    glEstateHeldIn::$options = glEstateHeldIn::$optionsCV3;
}
$borrowingMoney = '';
$isBorIntendToOccupyDispOpt = $borrowingMoneyDispOpt = $haveOwnershipInterestDispOpt = 'display: none;';
$borrowingMoney = Strings::showField('borrowingMoney', 'fileHMLOPropertyInfo');
$haveOwnershipInterest = Strings::showField('haveOwnershipInterest', 'fileHMLOPropertyInfo');
$typePropOwned = Strings::showField('typePropOwned', 'fileHMLOPropertyInfo');
$titleType = Strings::showField('titleType', 'fileHMLOPropertyInfo');
$glTypeOfSale = glTypeOfSale::$glTypeOfSale;

$BackupAllowToEdit = '';
if (in_array('Additional Questions', $lockedSections ?? [])) {
    $BackupAllowToEdit = $allowToEdit;
    $allowToEdit = false;
}

$ischk = BaseHTML::countFieldsExcludingSubSections($secArr = BaseHTML::sectionAccess2(['sId' => 'AQ', 'opt' => $fileTab, 'activeTab' => $activeTab]) ?? []) > 0 ? 'checked' : '';
loanForm::pushSectionID('AQ');


if ($publicUser == 1) {
    $ischk = $LMRId > 0 && $ischk != '' ? 'checked' : '';
}
$show1003FieldsArr = [];
$show1003FieldsArr['AQ'] = array_diff(array_keys($secArr), show1003FieldsArray::$show1003FieldsArray['AQ']);
if ($activeTab == '1003') {  /* story-31529 hide some fields based on the requirement mentioned in story*/
    foreach ($show1003FieldsArr['AQ'] as $showFieldkey => $hideFieldValue) {
        if (is_array($secArr[$hideFieldValue])) {
            $secArr[$hideFieldValue]['fieldDisplay'] = 0;
            loanForm::hideField('AQ', $hideFieldValue);
        }
    }
}


$loanGuarantee = Strings::showField('loanGuaranteeType', 'fileHMLOPropertyInfo');
$successFeePayment = Strings::showField('successFeePayment', 'fileHMLOPropertyInfo');
?>

<!-- additionalQuestions.php -->
<div class="card card-custom HMLOLoanInfoSections  borrowerActiveSection AQ AQCard <?php if (trim($ischk) == 'checked') {
    echo 'secShow';
} else {
    echo 'secHide';
} ?>" style="<?php echo $HMLOLoanInfoSectionsDisp; ?>">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <?php echo loanForm::toggleSectionV2(
            'AQ',
            true,
            true
        ); ?>
    </div>

    <div class="card-body AQCard_body">
        <div class="row">
            <label class="col-md-12 bg-secondary mb-2 p-2 font-weight-bolder" id="transactionDetailsSubHeading">Transaction
                Details</label>

            <div class="transactionDetails typeOfSale col-md-6 typeOfSale_disp <?php echo loanForm::showField('typeOfSale'); ?>"
                 style="<?php echo HMLOLoanTermsCalculation::$typeOfSaleTR ?>">
                <div class="row form-group">
                    <?php
                    echo loanForm::label('typeOfSale', 'col-md-6 ');
                    ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit) { ?>
                            <select
                                    class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'typeOfSale', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    name="typeOfSale" id="typeOfSale"
                                    TABINDEX="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'typeOfSale', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                <option value=""> - Select -</option>
                                <?php
                                sort($glTypeOfSale);
                                for ($i = 0; $i < count($glTypeOfSale); $i++) {
                                    $sOpt = '';
                                    $proTypeOfSale = '';
                                    $proTypeOfSale = trim($glTypeOfSale[$i]);
                                    $sOpt = Arrays::isSelected($proTypeOfSale, LoanInfo::$typeOfSale);
                                    echo "<option value=\"" . $proTypeOfSale . "\" " . $sOpt . '>' . $proTypeOfSale . '</option>';
                                }
                                ?>
                            </select>
                        <?php } else {
                            echo '<b>' . Strings::showField('typeOfSale', 'fileHMLOPropertyInfo') . '</b>';
                        } ?>
                    </div>
                </div>
            </div>

            <?php if ($isEF != 1) { ?>
                <div class="col-md-12">
                    <div class="row">
                        <div class="transactionDetails col-md-6 form-group <?php echo loanForm::showField('acceptedPurchase'); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('acceptedPurchase', 'col-md-8 '); ?>
                                <div class="col-md-4">
                                    <?php echo loanForm::radio(
                                        'acceptedPurchase',
                                        LMRequest::$allowToEdit,
                                        $tabIndex++,
                                        LoanInfo::$acceptedPurchase,
                                        [
                                            'Yes' => 'Yes',
                                            'No'  => 'No'
                                        ],
                                        'hideAndShowAcceptPurchaseAgreement(this.value, \'acceptedPurchaseDispOpt\');',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <div class="transactionDetails col-md-6 form-group acceptedPurchaseDispOpt PAExpirationDate_child <?php echo BaseHTML::parentFieldAccess(['fNm' => 'acceptedPurchase', 'sArr' => $secArr, 'pv' => $acceptedPurchase, 'av' => 'Yes']); ?>"
                             style="<?php echo HMLOLoanTermsCalculation::$acceptedPurchaseDispOpt ?>">
                            <div class="row">
                                <?php echo loanForm::label2('PAExpirationDate', 'col-md-7 '); ?>
                                <div class="col-md-5">
                                    <?php echo loanForm::date(
                                        'PAExpirationDate',
                                        LMRequest::$allowToEdit,
                                        $tabIndex++,
                                        $PAExpirationDate,
                                        ' dateNewClass ',
                                        '',
                                        false
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php } ?>
            <div class="col-md-12">
                <div class="row">
                    <div class="transactionDetails col-md-6 form-group <?php echo loanForm::showField('involvedPurchase'); ?>">
                        <div class="row">
                            <?php echo loanForm::label2('involvedPurchase', 'col-md-8 '); ?>
                            <div class="col-md-4">
                                <?php echo loanForm::radio(
                                    'involvedPurchase',
                                    LMRequest::$allowToEdit,
                                    $tabIndex++,
                                    LoanInfo::$involvedPurchase,
                                    [
                                        'Yes' => 'Yes',
                                        'No'  => 'No'
                                    ],
                                    'involvedPurchaseHideShow(this.value, \'wholesaleFee_disp\');',
                                    ''
                                ); ?>
                            </div>
                        </div>
                    </div>
                    <div class="transactionDetails col-md-6 form-group  <?php echo loanForm::showField('wholesaleFee',  LoanInfo::$wholesaleFee); ?>"
                         style="<?php if ( LoanInfo::$involvedPurchase != 'Yes') {
                             echo 'display:none;';
                         } ?>">
                        <div class="row">
                            <?php echo loanForm::label2('wholesaleFee', 'col-md-7 '); ?>
                            <div class="col-md-5">
                                <?php echo loanForm::currency(
                                    'wholesaleFee',
                                    LMRequest::$allowToEdit,
                                    $tabIndex++,
                                    LoanInfo::$wholesaleFee,
                                    '',
                                    ''
                                ); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12">
                <div class="row">
                    <div class="transactionDetails col-md-6 form-group <?php echo loanForm::showField('areKnownHazards'); ?>">
                        <div class="row">
                            <?php echo loanForm::label2('areKnownHazards', 'col-md-8 '); ?>
                            <div class="col-md-4">
                                <?php echo loanForm::radio(
                                    'areKnownHazards',
                                    LMRequest::$allowToEdit,
                                    $tabIndex++,
                                    LoanInfo::$areKnownHazards,
                                    [
                                        'Yes' => 'Yes',
                                        'No'  => 'No',
                                    ],
                                    'showAndHideBorSquareFootage(this.value, \'areProReportsDiv\');',
                                    ''
                                ); ?>
                            </div>
                        </div>
                    </div>
                    <div class="transactionDetails col-md-6 form-group  areProReportsDiv areKnownHazards_child
                            <?php echo BaseHTML::parentFieldAccess(['fNm' => 'areKnownHazards', 'sArr' => $secArr,
                                                                    'pv'  =>  LoanInfo::$areKnownHazards, 'av' => 'Yes']); ?>"
                         id="areProReportsDiv">
                        <div class="row">
                            <?php echo loanForm::label2('areProReports', 'col-md-7 '); ?>
                            <div class="col-md-5">
                                <?php echo loanForm::radio(
                                    'areProReports',
                                    LMRequest::$allowToEdit,
                                    $tabIndex++,
                                    LoanInfo:: $areProReports,
                                    [
                                        'Yes' => 'Yes',
                                        'No'  => 'No',
                                    ],
                                    '',
                                    ''
                                ); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12">
                <div class="row">
                    <div class="transactionDetails col-md-6 form-group <?php echo loanForm::showField('changeInCircumstance'); ?>">
                        <div class="row">
                            <?php echo loanForm::label2('changeInCircumstance', 'col-md-8 '); ?>
                            <div class="col-md-4">
                                <?php echo loanForm::radio(
                                    'changeInCircumstance',
                                    LMRequest::$allowToEdit,
                                    $tabIndex++,
                                    LoanInfo::$changeInCircumstance,
                                    [
                                        'Yes' => 'Yes',
                                        'No'  => 'No',
                                    ],
                                    'hideAndShowAcceptPurchaseAgreement(this.value, \'changeDescriptionDispOpt\');',
                                    ''
                                ); ?>
                            </div>
                        </div>
                    </div>
                    <div class="transactionDetails col-md-6 form-group changeDescriptionDispOpt changeDescription_child <?php echo BaseHTML::parentFieldAccess(['fNm' => 'changeInCircumstance', 'sArr' => $secArr, 'pv' =>  LoanInfo::$changeInCircumstance, 'av' => 'Yes']); ?>">
                        <div class="form-group row <?php echo loanForm::showField('changeDescription'); ?>">
                            <?php echo loanForm::label2('changeDescription', 'col-md-7 '); ?>
                            <div class="col-md-5">
                                <?php echo loanForm::textarea(
                                    'changeDescription',
                                    LMRequest::$allowToEdit,
                                    $tabIndex++,
                                    Strings::showField('changeDescription', 'fileHMLOPropertyInfo'),
                                    '',
                                    ''
                                ); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <?php if ($isEF != 1) { ?>
                <div class="col-md-12">
                    <div class="row">
                        <div class="transactionDetails col-md-6 form-group <?php echo loanForm::showField('additionalPropertyRestrictions'); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('additionalPropertyRestrictions', 'col-md-8 '); ?>
                                <div class="col-md-4">
                                    <?php echo loanForm::radio(
                                        'additionalPropertyRestrictions',
                                        LMRequest::$allowToEdit,
                                        $tabIndex++,
                                        LoanInfo::$additionalPropertyRestrictions,
                                        [
                                            'Yes' => 'Yes',
                                            'No'  => 'No'
                                        ],
                                        'additionalPropertyRestrictionsHideShow(this.value, \'restrictionsExplain\');',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <div class="transactionDetails col-md-6 form-group restrictionsExplain restrictionsExplain_child <?php echo BaseHTML::parentFieldAccess([
                            'fNm' => 'additionalPropertyRestrictions', 'sArr' => $secArr, 'pv' =>  LoanInfo::$additionalPropertyRestrictions, 'av' => 'Yes']); ?>"
                             style=" <?php if (LoanInfo::$additionalPropertyRestrictions != 'Yes') {
                                 echo 'display:none;';
                             } ?>">
                            <div class="row">
                                <?php echo loanForm::label2('restrictionsExplain', 'col-md-7 '); ?>
                                <div class="col-md-5">
                                    <?php echo loanForm::textarea(
                                        'restrictionsExplain',
                                        LMRequest::$allowToEdit,
                                        $tabIndex++,
                                        LoanInfo::$restrictionsExplain,
                                        '',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php } ?>
            <div class="col-md-12">
                <div class="row">
                    <div class="transactionDetails col-md-6 form-group <?php echo loanForm::showField('borrowingMoney'); ?>">
                        <div class="row">
                            <?php echo loanForm::label2('borrowingMoney', 'col-md-8 '); ?>
                            <div class="col-md-4">
                                <?php echo loanForm::radio(
                                    'borrowingMoney',
                                    LMRequest::$allowToEdit,
                                    $tabIndex++,
                                    LoanInfo::$borrowingMoney,
                                    [
                                        'Yes' => 'Yes',
                                        'No'  => 'No'
                                    ],
                                    'hideAndShowAcceptPurchaseAgreement(this.value, \'borrowingMoneyDispOpt\');',
                                    ''
                                ); ?>
                            </div>
                        </div>
                    </div>
                    <div style="<?php echo LoanInfo::$borrowingMoneyDispOpt; ?>"
                         class="transactionDetails col-md-6 form-group borrowingMoneyDispOpt borrowedAmt_child <?php echo BaseHTML::parentFieldAccess(['fNm' => 'borrowingMoney', 'sArr' => $secArr, 'pv' => $borrowingMoney, 'av' => 'Yes']); ?>">
                        <div class="row">
                            <?php echo loanForm::label2('borrowedAmt', 'col-md-7 '); ?>
                            <div class="col-md-5">
                                <?php echo loanForm::currency(
                                    'borrowedAmt',
                                    LMRequest::$allowToEdit,
                                    $tabIndex++,
                                    Strings::showField('borrowedAmt', 'fileHMLOPropertyInfo'),
                                    '',
                                    ''
                                ); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <?php if ($isEF != 1) { ?>
                <div class="col-md-12">
                    <div class="row">
                        <div class="transactionDetails col-md-6 form-group <?php echo loanForm::showField('purposeOfLoan'); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('purposeOfLoan', 'col-md-7 '); ?>
                                <div class="col-md-5">
                                    <input type="hidden" name="purposeOfLoanHidden" id="purposeOfLoanHidden"
                                           value="<?php echo implode(',', LoanInfo::$purposeOfLoan??[]); ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'purposeOfLoan', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                                    <?php echo loanForm::selectMulti(
                                        'purposeOfLoan',
                                        LMRequest::$allowToEdit,
                                        $tabIndex++,
                                        LoanInfo::$purposeOfLoan??[],
                                        count($HMLOPCBasicLoanPurposeInfoArray ?? []) > 0 ? $HMLOPCBasicLoanPurposeInfoArray : purposeOfLoanArray::$purposeOfLoanArray,
                                        'onPuprposeofloanchange();',
                                        'chzn-select',
                                        '-- Select One --'
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 form-group <?php echo LoanInfo::$purposeOfLoanDisplayCls; ?>"
                             id="purposeOfLoanExplanationDiv">
                            <div class="row">
                                <?php echo loanForm::label2('purposeOfLoanExplanation', 'col-md-7 font-weight-bold', '', 'Please Explain'); ?>
                                <div class="col-md-5">
                                    <?php echo loanForm::textarea(
                                        'purposeOfLoanExplanation',
                                        LMRequest::$allowToEdit,
                                        $tabIndex++,
                                        Strings::showField('purposeOfLoanExplanation', 'fileHMLOPropertyInfo'),
                                        '',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="transactionDetails col-md-6 form-group <?php echo loanForm::showField('desiredFundingAmount'); ?>">
                        <div class="row">
                            <?php echo loanForm::label2('desiredFundingAmount', 'col-md-7 '); ?>
                            <div class="col-md-5">
                                <?php echo loanForm::currency(
                                    'desiredFundingAmount',
                                    LMRequest::$allowToEdit,
                                    $tabIndex++,
                                    LoanInfo::$desiredFundingAmount,
                                    '',
                                    ''
                                ); ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="transactionDetails col-md-6 form-group <?php echo loanForm::showField('loanMadeWholly'); ?>">
                    <div class="row">
                        <?php echo loanForm::label2('loanMadeWholly', 'col-md-7 '); ?>
                        <div class="col-md-5">
                            <?php echo loanForm::radio(
                                'loanMadeWholly',
                                LMRequest::$allowToEdit,
                                $tabIndex++,
                                LoanInfo::$loanMadeWholly,
                                [
                                    'May'      => 'May',
                                    'Will'     => 'Will',
                                    'Will Not' => 'Will Not',
                                ],
                                '',
                                ''
                            ); ?>
                        </div>
                    </div>
                </div>
            <?php } ?>

            <div class="transactionDetails col-md-6 form-group <?php echo loanForm::showField('applyOtherLoan'); ?>">
                <div class="row">
                    <?php echo loanForm::label2('applyOtherLoan', 'col-md-8 '); ?>
                    <div class="col-md-4">
                        <?php echo loanForm::radio(
                            'applyOtherLoan',
                            LMRequest::$allowToEdit,
                            $tabIndex++,
                            Strings::showField('applyOtherLoan', 'fileHMLOPropertyInfo'),
                            [
                                'Yes' => 'Yes',
                                'No'  => 'No',
                            ],
                            '',
                            ''
                        ); ?>
                    </div>
                </div>
            </div>
            <div class="col-md-12">
                <div class="row">
                    <div class="transactionDetails col-md-6 form-group <?php echo loanForm::showField('isSubjectSS'); ?>">
                        <div class="row">
                            <?php echo loanForm::label2('isSubjectSS', 'col-md-8 '); ?>
                            <div class="col-md-4">
                                <?php echo loanForm::radio(
                                    'isSubjectSS',
                                    LMRequest::$allowToEdit,
                                    $tabIndex++,
                                    LoanInfo::$isSubjectSS,
                                    [
                                        'Yes' => 'Yes',
                                        'No'  => 'No',
                                    ],
                                    '',
                                    ''
                                ); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="transactionDetails col-md-6 form-group <?php echo loanForm::showField('applyNewCredit'); ?>">
                <div class="row">
                    <?php echo loanForm::label2('applyNewCredit', 'col-md-8 '); ?>
                    <div class="col-md-4">
                        <?php echo loanForm::radio(
                            'applyNewCredit',
                            LMRequest::$allowToEdit,
                            $tabIndex++,
                            Strings::showField('applyNewCredit', 'fileHMLOPropertyInfo'),
                            [
                                'Yes' => 'Yes',
                                'No'  => 'No',
                            ],
                            '',
                            ''
                        ); ?>
                    </div>
                </div>
            </div>
            <?php if ($hideThisField) { ?>
                <div class="transactionDetails col-md-6 form-group <?php echo loanForm::showField('displayNotes'); ?>">
                    <div class="row ">
                        <?php echo loanForm::label2('displayNotes', 'col-md-7 '); ?>
                        <div class="col-md-5">
                            <?php echo loanForm::textarea(
                                'displayNotes',
                                LMRequest::$allowToEdit,
                                $tabIndex++,
                                Strings::showField('displayNotes', 'fileHMLOPropertyInfo'),
                                '',
                                ''
                            ); ?>
                        </div>
                    </div>
                </div>
            <?php } ?>
            <div class="transactionDetails col-md-6 form-group <?php echo loanForm::showField('useOfProceeds'); ?>">
                <div class="row">
                    <?php echo loanForm::label2(
                        'useOfProceeds',
                        'col-md-7 ',
                        '',
                        loanForm::changeLog(
                            LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->HMLOPID,
                            'useOfProceeds',
                            tblFileHMLOPropInfo::class
                        )
                    ); ?>
                    <div class="col-md-5">
                        <?php echo loanForm::text(
                            'useOfProceeds',
                            LMRequest::$allowToEdit,
                            $tabIndex++,
                            LoanInfo::$useOfProceeds,
                            '',
                            ''
                        ); ?>
                    </div>
                </div>
            </div>
            <?php if (in_array($PCID, $glFirstProbate)) { ?>
                <div class="col-md-6 form-group">
                    <div class="row">
                        <?php echo loanForm::label2('loanSigning', 'col-md-7 font-weight-bold', '', 'How will you be signing for this loan?'); ?>
                        <div class="col-md-5">
                            <?php echo loanForm::select(
                                'loanSigning',
                                LMRequest::$allowToEdit && $disabledInputForClient,
                                $tabIndex++,
                                $loanSigning,
                                glHMLOLoanSigning::$options,
                                '',
                                '',
                                '-- Select One --'
                            ); ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 form-group">
                    <div class="row">
                        <?php echo loanForm::label2('courtOrderNecessary', 'col-md-7 font-weight-bold', '', 'Will a court order be necessary for this loan?'); ?>
                        <div class="col-md-5">
                            <?php echo loanForm::select(
                                'courtOrderNecessary',
                                LMRequest::$allowToEdit && $disabledInputForClient,
                                $tabIndex++,
                                $courtOrderNecessary,
                                glHMLOCourtOrderNecessary::$options,
                                '',
                                '',
                                '-- Select One --'
                            ); ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 form-group">
                    <div class="row">
                        <?php echo loanForm::label2('loanPurpose', 'col-md-7 font-weight-bold', '', 'Loan Purpose'); ?>
                        <div class="col-md-5">
                            <?php echo loanForm::select(
                                'courtOrderNecessary',
                                LMRequest::$allowToEdit && $disabledInputForClient,
                                $tabIndex++,
                                $loanPurpose,
                                glHMLOLoanPurpose::$options,
                                '',
                                '',
                                '-- Select One --'
                            ); ?>
                        </div>
                    </div>
                </div>
            <?php } ?>
            <?php if ($isEF != 1) { ?>
                <div class="col-md-12">
                    <div class="row">
                        <div class="transactionDetails col-md-6 form-group <?php echo loanForm::showField('doYouHaveInvoiceToFactor'); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('doYouHaveInvoiceToFactor', 'col-md-7 '); ?>
                                <div class="col-md-5">
                                    <?php echo loanForm::radio(
                                        'doYouHaveInvoiceToFactor',
                                        LMRequest::$allowToEdit,
                                        $tabIndex++,
                                        $doYouHaveInvoiceToFactor,
                                        [
                                            'Yes' => 'Yes',
                                            'No'  => 'No'
                                        ],
                                        'hideAndShowAcceptPurchaseAgreement(this.value, \'doYouHaveInvoiceToFactorDispOpt\');',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <div class="transactionDetails col-md-6 form-group doYouHaveInvoiceToFactorDispOpt <?php echo loanForm::showField('doYouHaveInvoiceToFactor'); ?>"
                             style=" <?php if ($doYouHaveInvoiceToFactor != 'Yes') {
                                 echo 'display:none;';
                             } ?>">
                            <div class="row">
                                <?php echo loanForm::label2('amount', 'col-md-7 '); ?>
                                <div class="col-md-5">
                                    <?php echo loanForm::currency(
                                        'amount',
                                        LMRequest::$allowToEdit,
                                        $tabIndex++,
                                        $amount,
                                        '',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="row">
                        <div class="transactionDetails col-md-6 form-group <?php echo loanForm::showField('haveCurrentLoanBal'); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('haveCurrentLoanBal', 'col-md-8 '); ?>
                                <div class="col-md-4">
                                    <?php echo loanForm::radio(
                                        'haveCurrentLoanBal',
                                        LMRequest::$allowToEdit,
                                        $tabIndex++,
                                        $haveCurrentLoanBal,
                                        [
                                            'Yes' => 'Yes',
                                            'No'  => 'No'
                                        ],
                                        'hideAndShowAcceptPurchaseAgreement(this.value, \'haveCurrentLoanBalDispOpt\');',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <div style="<?php echo $haveCurrentLoanBalDispOpt; ?>"
                             class="transactionDetails col-md-6 form-group haveCurrentLoanBalDispOpt balance_child <?php echo BaseHTML::parentFieldAccess(['fNm' => 'haveCurrentLoanBal', 'sArr' => $secArr, 'pv' => $haveCurrentLoanBal, 'av' => 'Yes']); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('balance', 'col-md-7 '); ?>
                                <div class="col-md-5">
                                    <?php echo loanForm::currency(
                                        'balance',
                                        LMRequest::$allowToEdit,
                                        $tabIndex++,
                                        $balance,
                                        '',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <?php if ($hideThisField) { ?>
                            <div style="<?php echo $haveCurrentLoanBalDispOpt; ?>"
                                 class="transactionDetails col-md-6 form-group haveCurrentLoanBalDispOpt heldWith_child <?php echo BaseHTML::parentFieldAccess(['fNm' => 'haveCurrentLoanBal', 'sArr' => $secArr, 'pv' => $haveCurrentLoanBal, 'av' => 'Yes']); ?>">
                                <div class="row">
                                    <?php echo loanForm::label2('heldWith', 'col-md-2 '); ?>
                                    <div class="col-md-10">
                                        <?php echo loanForm::textarea(
                                            'heldWith',
                                            LMRequest::$allowToEdit,
                                            $tabIndex++,
                                            $heldWith,
                                            '',
                                            ''
                                        ); ?>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                    </div>
                </div>
            <?php } ?>
            <div class="transactionDetails col-md-6 applicantConfirmed_disp <?php echo loanForm::showField('applicantConfirmed'); ?>">
                <div class="row form-group mt-6">
                    <?php echo loanForm::label('applicantConfirmed', 'col-md-9 ', '',
                        loanForm::changeLog(
                            LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                            'applicantConfirmed',
                            \models\lendingwise\tblFileHMLONewLoanInfo::class,
                            loanForm::label('applicantConfirmed'),
                        )); ?>
                    <div class="col-md-3">
                        <?php if (LMRequest::$allowToEdit) { ?>
                            <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if (LoanInfo::$applicantConfirmed == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo LoanInfo::$applicantConfirmed ?>"
                                                       id="applicantConfirmedId"
                                                       onchange="toggleSwitch('applicantConfirmedId', 'applicantConfirmed','1','0' );"/>
                                                <input type="hidden" name="applicantConfirmed"
                                                       id="applicantConfirmed"
                                                       value="<?php echo LoanInfo::$applicantConfirmed ?>">
                                                <span></span>
                                            </label>
                                        </span>
                        <?php } ?>
                    </div>
                </div>
            </div>


        </div>
        <div class="row">
            <label class="col-md-12 bg-secondary mb-2 p-2 font-weight-bolder" id="occupancySubHeading">Occupancy</label>
            <?php if ($isEF != 1) { ?>
                <div class="col-md-12">
                    <div class="row">
                        <div class="occupancy col-md-6 form-group <?php echo loanForm::showField('isBorIntendToOccupyPropAsPRI'); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('isBorIntendToOccupyPropAsPRI', 'col-md-8 '); ?>
                                <div class="col-md-4">
                                    <?php echo loanForm::radio(
                                        'isBorIntendToOccupyPropAsPRI',
                                        LMRequest::$allowToEdit,
                                        $tabIndex++,
                                        LoanInfo::$isBorIntendToOccupyPropAsPRI,
                                        [
                                            'Yes' => 'Yes',
                                            'No'  => 'No'
                                        ],
                                        '',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <div class="occupancy col-md-6 form-group <?php
                        echo loanForm::showField('haveOwnershipInterest'); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('haveOwnershipInterest', 'col-md-7 '); ?>
                                <div class="col-md-5">
                                    <?php echo loanForm::radio(
                                        'haveOwnershipInterest',
                                        LMRequest::$allowToEdit,
                                        $tabIndex++,
                                        LoanInfo::$haveOwnershipInterest,
                                        [
                                            'Yes' => 'Yes',
                                            'No'  => 'No'
                                        ],
                                        'hideAndShowAcceptPurchaseAgreement(this.value, \'haveOwnershipInterestDispOpt\');',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <div style="<?php echo LoanInfo::$haveOwnershipInterestDispOpt ?>"
                             class="occupancy col-md-6 form-group  haveOwnershipInterestDispOpt typePropOwned_child
                              <?php echo BaseHTML::parentFieldAccess(['fNm' => 'haveOwnershipInterest', 'sArr' => $secArr,
                                                                      'pv'  => LoanInfo::$haveOwnershipInterest, 'av' => 'Yes']); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('typePropOwned', 'col-md-7 '); ?>
                                <div class="col-md-5">
                                    <?php echo loanForm::select(
                                        'typePropOwned',
                                        LMRequest::$allowToEdit,
                                        $tabIndex++,
                                        LoanInfo::$typePropOwned,
                                        glTypeOfProperty::$options,
                                        '',
                                        '',
                                        '-- Select One --'
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <?php if ($hideThisField) { ?>
                            <div style="<?php echo LoanInfo::$haveOwnershipInterestDispOpt ?>"
                                 class="occupancy col-md-6 form-group  haveOwnershipInterestDispOpt titleType_child
                              <?php echo BaseHTML::parentFieldAccess(['fNm' => 'haveOwnershipInterest', 'sArr' => $secArr,
                                                                      'pv'  => LoanInfo::$haveOwnershipInterest, 'av' => 'Yes']); ?>">
                                <div class="row">
                                    <?php echo loanForm::label2('titleType', 'col-md-7 '); ?>
                                    <div class="col-md-5">
                                        <?php echo loanForm::select(
                                            'titleType',
                                            LMRequest::$allowToEdit,
                                            $tabIndex++,
                                            LoanInfo::$titleType,
                                            glTitleType::$options,
                                            '',
                                            '',
                                            '-- Select One --'
                                        ); ?>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                    </div>
                </div>
                <div class="occupancy col-md-6 form-group <?php echo loanForm::showField('isCoBorIntendToOccupyPropAsPRI'); ?>">
                    <div class="row">
                        <?php echo loanForm::label2('isCoBorIntendToOccupyPropAsPRI', 'col-md-8 '); ?>
                        <div class="col-md-4">
                            <?php echo loanForm::radio(
                                'isCoBorIntendToOccupyPropAsPRI',
                                LMRequest::$allowToEdit,
                                $tabIndex++,
                                LoanInfo::$isCoBorIntendToOccupyPropAsPRI,
                                [
                                    'Yes' => 'Yes',
                                    'No'  => 'No'
                                ],
                                '',
                                ''
                            ); ?>
                        </div>
                    </div>
                </div>
            <?php } ?>
            <div class="occupancy col-md-6 form-group <?php echo loanForm::showField('famBizAffil'); ?>">
                <div class="row">
                    <?php echo loanForm::label2('famBizAffil', 'col-md-8 '); ?>
                    <div class="col-md-4">
                        <?php echo loanForm::radio(
                            'famBizAffil',
                            LMRequest::$allowToEdit,
                            $tabIndex++,
                            Strings::showField('famBizAffil', 'fileHMLOPropertyInfo'),
                            [
                                'Yes' => 'Yes',
                                'No'  => 'No',
                            ],
                            '',
                            ''
                        ); ?>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <label class="col-md-12 bg-secondary mb-2 p-2 font-weight-bolder" id="secondaryFinancingSubHeading">Secondary
                Financing</label>
            <div class="col-md-12">
                <div class="row">
                    <div class="secondaryFinancing col-md-6 form-group <?php echo loanForm::showField('isBorBorrowedDownPayment'); ?>">
                        <div class="row">
                            <?php echo loanForm::label2('isBorBorrowedDownPayment', 'col-md-8 '); ?>
                            <div class="col-md-4">
                                <?php echo loanForm::radio(
                                    'isBorBorrowedDownPayment',
                                    LMRequest::$allowToEdit,
                                    $tabIndex++,
                                    LoanInfo::$isBorBorrowedDownPayment,
                                    [
                                        'Yes' => 'Yes',
                                        'No'  => 'No'
                                    ],
                                    'showAndHideBorBackgroundDiv(this.value, \'borBorrowedDownPayment\');',
                                    ''
                                ); ?>
                            </div>
                        </div>
                    </div>
                    <div class="secondaryFinancing col-md-6 form-group borBorrowedDownPaymentTR secondaryHolderName_child <?php echo BaseHTML::parentFieldAccess(['fNm' => 'isBorBorrowedDownPayment', 'sArr' => $secArr, 'pv' => LoanInfo::$isBorBorrowedDownPayment, 'av' => 'Yes']); ?>">
                        <div class="borBorrowedDownPaymentTR" style="<?php echo HMLOLoanTermsCalculation::$borBorrowedDownPaymentDispOpt ?>">
                            <div class="row">
                                <?php echo loanForm::label2('secondaryHolderName', 'col-md-5 '); ?>
                                <div class="col-md-7">
                                    <?php echo loanForm::text(
                                        'secondaryHolderName',
                                        LMRequest::$allowToEdit,
                                        $tabIndex++,
                                        LoanInfo::$secondaryHolderName,
                                        '',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="secondaryFinancing col-md-6 form-group borBorrowedDownPaymentTR secondaryFinancingAmount_child <?php echo BaseHTML::parentFieldAccess(['fNm' => 'isBorBorrowedDownPayment', 'sArr' => $secArr, 'pv' => LoanInfo::$isBorBorrowedDownPayment, 'av' => 'Yes']); ?>">
                        <div class="borBorrowedDownPaymentTR" style="<?php echo HMLOLoanTermsCalculation::$borBorrowedDownPaymentDispOpt ?>">
                            <div class="row">
                                <?php echo loanForm::label2('secondaryFinancingAmount', 'col-md-5 '); ?>
                                <div class="col-md-7">
                                    <?php echo loanForm::currency(
                                        'secondaryFinancingAmount',
                                        LMRequest::$allowToEdit,
                                        $tabIndex++,
                                        LoanInfo::$secondaryFinancingAmount,
                                        '',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php if ($hideThisField) { ?>
                        <div class="secondaryFinancing col-md-6 form-group borBorrowedDownPaymentTR borBorrowedDownPaymentExpln_child
                    <?php echo BaseHTML::parentFieldAccess(['fNm' => 'isBorBorrowedDownPayment', 'sArr' => $secArr,
                                                            'pv'  => LoanInfo::$isBorBorrowedDownPayment, 'av' => 'Yes']); ?>"
                             style="<?php echo HMLOLoanTermsCalculation::$borBorrowedDownPaymentDispOpt ?>">
                            <div class="row">
                                <?php echo loanForm::label2('borBorrowedDownPaymentExpln', 'col-md-5 '); ?>
                                <div class="col-md-7">
                                    <?php echo loanForm::textarea(
                                        'borBorrowedDownPaymentExpln',
                                        LMRequest::$allowToEdit,
                                        $tabIndex++,
                                        LoanInfo::$borBorrowedDownPaymentExpln,
                                        '',
                                        ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </div>

        <?php echo CustomField::RenderForTabSection(
            PageVariables::$PCID,
            tblFile::class,
            LMRequest::$LMRId,
            'AQ',
            $fileTab,
            $activeTab,
            LMRequest::myFileInfo()->getFileTypes(),
            LMRequest::myFileInfo()->getLoanPrograms()
        ); ?>
    </div>
</div>
<?php loanForm::popSectionID(); ?>
<script>

    function onPuprposeofloanchange() {
        let purposeOfLoan = $('#purposeOfLoan').val().toString();
        let purposeOfLoanArray = purposeOfLoan.split(',');
        if ($.inArray('Other', purposeOfLoanArray) != -1) {
            $('#purposeOfLoanExplanationDiv').removeClass('d-none');
            $('#purposeOfLoanExplanation').val('');
        } else {
            $('#purposeOfLoanExplanationDiv').addClass('d-none');
            $('#purposeOfLoanExplanation').val('');
        }
    }

    if ($('.transactionDetails.secShow').length > 0) { //show title
        $("#transactionDetailsSubHeading").show();
    } else { //hide title
        $("#transactionDetailsSubHeading").hide();
    }
    if ($('.occupancy.secShow').length > 0) { //show title
        $("#occupancySubHeading").show();
    } else { //hide title
        $("#occupancySubHeading").hide();
    }
    if ($('.secondaryFinancing.secShow').length > 0) { //show title
        $("#secondaryFinancingSubHeading").show();
    } else { //hide title
        $("#secondaryFinancingSubHeading").hide();
    }
</script>
<!-- additionalQuestions.php -->

<?php
if (in_array('Additional Questions', $lockedSections ?? [])) {
    //$BackupAllowToEdit = $allowToEdit;
    LMRequest::$allowToEdit = $BackupAllowToEdit;
}
?>

