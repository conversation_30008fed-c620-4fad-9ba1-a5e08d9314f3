<?php
global $lockedSections, $isEF, $fileTab, $HMLOLoanInfoSectionsDisp;

use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\gldocType;
use models\constants\gl\glEstateHeldIn;
use models\constants\gl\glFundingAs;
use models\constants\gl\glHMLOCourtOrderNecessary;
use models\constants\gl\glHMLOExitStrategy;
use models\constants\gl\glHMLOHouseType;
use models\constants\gl\glHMLOLienPosition;
use models\constants\gl\glHMLOLoanPurpose;
use models\constants\gl\glHMLOLoanSigning;
use models\constants\gl\glpaymentFrequency;
use models\constants\gl\glPCID;
use models\constants\gl\glprincipalRepayment;
use models\constants\gl\glRateIndex;
use models\constants\gSecurityInstrumentArray;
use models\constants\loanGuaranteeTypes;
use models\constants\typeOfHMLOLoanRequesting;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\backoffice\LoanInfo;
use models\Controllers\loanForm;
use models\HMLOLoanTermsCalculation;
use models\lendingwise\tblFileHMLOPropInfo;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Strings;
use pages\backoffice\loan\web_form\HMLO\classes\fileCustomPCLoanTermsController;

$glHMLOHouseType = glHMLOHouseType::$glHMLOHouseType;
$gldocType = gldocType::$gldocType;
$glprincipalRepayment = glprincipalRepayment::$glprincipalRepayment;
$glpaymentFrequency = glpaymentFrequency::$glpaymentFrequency;
glRateIndex::$PCID = LMRequest::$PCID;
$glRateIndex = new glRateIndex();
$glRateIndex = glCustomJobForProcessingCompany::hideRateIndexFields(LMRequest::$PCID);
$glHMLOLienPosition = glHMLOLienPosition::getLienPosition(LMRequest::$PCID);
$glHMLOExitStrategy = glHMLOExitStrategy::$glHMLOExitStrategy;
$exitStrategyOptions = glHMLOExitStrategy::getExitStrategy(LMRequest::$PCID);

$ischk = count($ALSSecArr = BaseHTML::sectionAccess2(['sId' => 'ALS', 'opt' => $fileTab, 'activeTab' => LMRequest::$activeTab])) > 0 ? 'checked' : '';

if (PageVariables::$publicUser == 1) {
    $ischk = LMRequest::$LMRId > 0 && $ischk != '' ? 'checked' : '';
}
loanForm::pushSectionID('ALS');
?>

<div class="card card-custom additionalLoanSettingsCard ALS <?php if (trim($ischk) == 'checked') {
    echo 'secShow';
} else {
    echo 'secHide';
} ?>" id="additionalLoanSettingsCard" style="<?php echo $HMLOLoanInfoSectionsDisp; ?>">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label"><?php echo BaseHTML::getSectionHeading('ALS'); ?> </h3>
            <?php if (trim(BaseHTML::getSectionTooltip('ALS')) != '') { ?>&nbsp;
                <i class="popoverClass fas fa-info-circle text-primary "
                   data-html="true"
                   data-content="<?php echo BaseHTML::getSectionTooltip('ALS'); ?>"></i>
            <?php } ?>
        </div>
        <div class="card-toolbar">
            <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none cursor-pointer"
                  data-card-tool="toggle"
                  data-toggle="tooltip" data-placement="top" title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>
            <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none cursor-pointer"
                  data-card-tool="reload"
                  data-toggle="tooltip" data-placement="top" title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </span>
            <span
                    class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer"
                    data-card-tool="toggle"
                    data-section="additionalLoanSettingsCard"
                    data-toggle="tooltip" data-placement="top" title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>
        </div>
    </div>
    <div class="card-body additionalLoanSettingsCard_body ">
        <div class="row">
            <div class="col-md-12"><!-- Row 8 Start -->
                <div class="row">
                    <div class="row8SeparatorCls col-md-3 <?php echo loanForm::showField('fundingAs'); ?>">
                        <div class="form-group">
                            <?php
                            echo loanForm::label2(
                                    'fundingAs'
                            );
                            echo loanForm::select(
                                    'fundingAs',
                                    LMRequest::$allowToEdit,
                                    1,
                                    Strings::showField('fundingAs', 'fileHMLONewLoanInfo'),
                                    glFundingAs::$glFundingAs,
                                    '',
                                    'chzn-select form-control',
                                    ' ',
                                    'Please Select ' . loanForm::$permissions['fundingAs']->fieldLabel,
                                    '',
                                    'data-value = "' . Strings::showField('fundingAs', 'fileHMLONewLoanInfo') . '"'
                            );
                            ?>
                        </div>
                    </div>
                    <div class="row8SeparatorCls col-md-3 lienPosition_disp <?php echo loanForm::showField('lienPosition'); ?>">
                        <div class="row form-group">
                            <label class="col-md-12 font-weight-bold">Lien Position</label>
                            <div class="col-md-12">
                                <select
                                        class="form-control input-sm"
                                        name="lienPosition" id="lienPosition"
                                        TABINDEX="">
                                    <option value=""> - Select -</option>
                                    <?php
                                    $glHMLOLienPositionKeys = [];
                                    $glHMLOLienPositionKeys = array_keys($glHMLOLienPosition);
                                    for ($i = 0; $i < count($glHMLOLienPositionKeys); $i++) {
                                        $lienPos = $sOpt = '';
                                        $lienPos = trim($glHMLOLienPositionKeys[$i]);
                                        $sOpt = Arrays::isSelected($lienPos, LoanInfo::$lienPosition);
                                        ?>
                                        <option
                                                value="<?php echo $lienPos ?>" <?php echo $sOpt ?>><?php echo $glHMLOLienPosition[$lienPos] ?></option>
                                        <?php
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    <?php if (PageVariables::$publicUser != 1 && PageVariables::$userRole != 'Client') { ?>
                        <div class="row8SeparatorCls col-md-3 form-group rateIndex_disp <?php echo loanForm::showField('rateIndex'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('rateIndex', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php
                                    if (LMRequest::$allowToEdit) { ?>
                                        <select
                                                class="chzn-select form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'rateIndex', 'sArr' => $ALSSecArr, 'opt' => 'M']); ?>"
                                                name="rateIndex[]"
                                                id="rateIndex"
                                                data-placeholder="Please select"
                                                multiple="" <?php echo BaseHTML::fieldAccess(['fNm' => 'rateIndex', 'sArr' => $ALSSecArr, 'opt' => 'I']); ?>>
                                            <option value=""></option>
                                            <?php
                                            for ($k = 0; $k < count(glRateIndex::$glRateIndex ?? []); $k++) {
                                                $sel = '';
                                                if (in_array(glRateIndex::$glRateIndex[$k], LoanInfo::$rateIndexArr ?? [])) $sel = ' selected ';
                                                ?>
                                                <option
                                                        value="<?php echo trim(glRateIndex::$glRateIndex[$k]); ?>" <?php echo $sel; ?>><?php echo trim(glRateIndex::$glRateIndex[$k]) ?></option>
                                                <?php
                                            }
                                            ?>
                                        </select>
                                    <?php } else { ?>
                                        <?php echo '<b>' . implode(',', LoanInfo::$rateIndexArr) . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                    <div class="row8SeparatorCls col-md-6 calcInrBasedOnMonthlyPayment_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'calcInrBasedOnMonthlyPayment', 'sArr' => $ALSSecArr, 'opt' => 'D']); ?>">
                        <div class="row form-group">
                            <?php
                            echo loanForm::label('calcInrBasedOnMonthlyPayment', 'col-md-9 ');
                            ?>
                            <div class="col-md-3">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <div class="radio-inline">
                                        <label class="radio radio-solid "
                                               for="calcInrBasedOnMonthlyPaymentYes">
                                            <input type="radio"
                                                   class="<?php echo BaseHTML::fieldAccess(['fNm' => 'calcInrBasedOnMonthlyPayment', 'sArr' => $ALSSecArr, 'opt' => 'M']); ?>"
                                                   name="calcInrBasedOnMonthlyPayment"
                                                   id="calcInrBasedOnMonthlyPaymentYes"
                                                   value="Yes"
                                                   tabindex="<?php echo LoanInfo::$tabIndex++; ?>" <?php echo Strings::isChecked('Yes', LoanInfo::$calcInrBasedOnMonthlyPayment); ?>
                                                   onclick="involvedPurchaseHideShow(this.value, 'calcInrBasedOnMonthlyPaymentDispOpt', '');" <?php echo BaseHTML::fieldAccess(['fNm' => 'calcInrBasedOnMonthlyPayment', 'sArr' => $ALSSecArr, 'opt' => 'I']); ?> ><span></span>Yes
                                        </label>
                                        <label class="radio radio-solid "
                                               for="calcInrBasedOnMonthlyPaymentNo">
                                            <input type="radio" name="calcInrBasedOnMonthlyPayment"
                                                   id="calcInrBasedOnMonthlyPaymentNo" value="No"
                                                   tabindex="<?php echo LoanInfo::$tabIndex++; ?>" <?php echo Strings::isChecked('No', LoanInfo::$calcInrBasedOnMonthlyPayment); ?>
                                                   onclick="involvedPurchaseHideShow(this.value, 'calcInrBasedOnMonthlyPaymentDispOpt', '');" <?php echo BaseHTML::fieldAccess(['fNm' => 'calcInrBasedOnMonthlyPayment', 'sArr' => $ALSSecArr, 'opt' => 'I']); ?> ><span></span>No
                                        </label></div>
                                <?php } else { ?>
                                    <h5><?php echo LoanInfo::$calcInrBasedOnMonthlyPayment; ?></h5>
                                <?php } ?>
                            </div>


                            <div
                                    class="calcInrBasedOnMonthlyPaymentDispOpt col-md-12 <?php echo BaseHTML::parentFieldAccess(['fNm' => 'calcInrBasedOnMonthlyPayment', 'sArr' => $ALSSecArr, 'pv' => LoanInfo::$calcInrBasedOnMonthlyPayment, 'av' => 'Yes']); ?>">
                                <div class="row">
                                    <?php
                                    echo loanForm::label('InrBasedOnMonthlyPayment', 'col-md-8 ');
                                    ?>
                                    <div class="col-md-4">
                                        <?php if (LMRequest::$allowToEdit) { ?>
                                            <input type="text" name="InrBasedOnMonthlyPayment"
                                                   id="InrBasedOnMonthlyPayment"
                                                   tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                                   value="<?php echo LoanInfo::$InrBasedOnMonthlyPayment; ?>"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'InrBasedOnMonthlyPayment', 'sArr' => $ALSSecArr, 'opt' => 'M']); ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'InrBasedOnMonthlyPayment', 'sArr' => $ALSSecArr, 'opt' => 'I']); ?>
                                                   readonly>
                                        <?php } else { ?>
                                            <h5><?php echo LoanInfo::$InrBasedOnMonthlyPayment; ?></h5>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!-- Row 8 End -->
            <div class="col-md-12 row8SeparatorCls separator separator-dashed my-2 separator12"
                 id="row8SeparatorDiv"></div>

            <div class="col-md-12"><!-- Row 9 Start -->
                <div class="row">
                    <div class="row9SeparatorCls col-md-3  isBlanketLoanDiv  isBlanketLoanDiv isBlanketLoan_disp <?php echo loanForm::showField('isBlanketLoan'); ?>"
                         style="<?php echo HMLOLoanTermsCalculation::$isBlanketLoanDiv ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('isBlanketLoan', 'col-md-12 '); ?>
                            <div class="col-md-12">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <div class="radio-inline">
                                        <label class="radio radio-solid " for="isBlanketLoanYes">
                                            <input type="radio"
                                                   class="<?php echo BaseHTML::fieldAccess(['fNm' => 'isBlanketLoan', 'sArr' => $ALSSecArr, 'opt' => 'M']); ?>"
                                                   name="isBlanketLoan" id="isBlanketLoanYes" value="Yes"
                                                   tabindex="<?php echo LoanInfo::$tabIndex++; ?>" <?php echo Strings::isChecked('Yes', HMLOLoanTermsCalculation::$isBlanketLoan); ?>
                                                   onclick="hideAndShowBlanketLoan(this.value, 'isBlanketLoan');checktheMirrorFields(this.value,this.name);" <?php echo BaseHTML::fieldAccess(['fNm' => 'isBlanketLoan', 'sArr' => $ALSSecArr, 'opt' => 'I']); ?>><span></span>Yes
                                        </label>
                                        <label class="radio radio-solid" for="isBlanketLoanNo">
                                            <input type="radio" name="isBlanketLoan" id="isBlanketLoanNo" value="No"
                                                   class="<?php echo BaseHTML::fieldAccess(['fNm' => 'isBlanketLoan', 'sArr' => $ALSSecArr, 'opt' => 'M']); ?>"
                                                   tabindex="<?php echo LoanInfo::$tabIndex++; ?>" <?php echo Strings::isChecked('No', HMLOLoanTermsCalculation::$isBlanketLoan); ?>
                                                   onclick="hideAndShowBlanketLoan(this.value, 'isBlanketLoan');checktheMirrorFields(this.value,this.name);" <?php echo BaseHTML::fieldAccess(['fNm' => 'isBlanketLoan', 'sArr' => $ALSSecArr, 'opt' => 'I']); ?>><span></span>No
                                        </label></div>
                                <?php } else {
                                    echo '<b>' . HMLOLoanTermsCalculation::$isBlanketLoan . '</b>';
                                } ?>
                            </div>
                        </div>
                    </div>
                    <div class="row9SeparatorCls col-md-3 isBlanketLoan isBlanketLoanDiv noOfPropertiesAcquiring_disp <?php echo loanForm::showField('noOfPropertiesAcquiring'); ?>"
                         style="<?php echo HMLOLoanTermsCalculation::$isBlanketLoanDispOpt ?>">
                        <div class="row form-group isBlanketLoan"
                             style="<?php echo HMLOLoanTermsCalculation::$isBlanketLoanDispOpt ?>">
                            <?php echo loanForm::label('noOfPropertiesAcquiring', 'col-md-12 '); ?>
                            <div class="col-md-12">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <input type="number"
                                           class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'noOfPropertiesAcquiring', 'sArr' => $ALSSecArr, 'opt' => 'M']); ?>"
                                           <?php if (LMRequest::$activeTab != 'HMLI') { ?>onchange="mirrornoOfPropertiesAcquiring()" <?php } ?>
                                           name="noOfPropertiesAcquiring" id="noOfPropertiesAcquiring"
                                           value="<?php echo LoanInfo::$noOfPropertiesAcquiring ?>"
                                           autocomplete="off"
                                           tabindex="<?php echo LoanInfo::$tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'noOfPropertiesAcquiring', 'sArr' => $ALSSecArr, 'opt' => 'I']); ?>>
                                <?php } else { ?>
                                    <span class="H5"><?php echo LoanInfo::$noOfPropertiesAcquiring ?></span>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                    <div class="row9SeparatorCls col-md-3 isBlanketLoan isBlanketLoanDiv ownedSameEntity_disp <?php echo loanForm::showField('ownedSameEntity'); ?>"
                         style="<?php echo HMLOLoanTermsCalculation::$isBlanketLoanDispOpt ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('ownedSameEntity', 'col-md-12 '); ?>
                            <div class="col-md-12">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <div class="radio-inline">
                                        <label class="radio radio-solid " for="ownedSameEntityYes">
                                            <input type="radio"
                                                   class="<?php echo BaseHTML::fieldAccess(['fNm' => 'ownedSameEntity', 'sArr' => $ALSSecArr, 'opt' => 'M']); ?>"
                                                   name="ownedSameEntity" id="ownedSameEntityYes" value="Yes"
                                                   tabindex="<?php echo LoanInfo::$tabIndex++; ?>" <?php echo Strings::isChecked('Yes', LoanInfo::$ownedSameEntity); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'ownedSameEntity', 'sArr' => $ALSSecArr, 'opt' => 'I']); ?>><span></span>Yes
                                        </label>
                                        <label class="radio radio-solid " for="ownedSameEntityNo">
                                            <input type="radio" name="ownedSameEntity" id="ownedSameEntityNo"
                                                   value="No"
                                                   class="<?php echo BaseHTML::fieldAccess(['fNm' => 'ownedSameEntity', 'sArr' => $ALSSecArr, 'opt' => 'M']); ?>"
                                                   tabindex="<?php echo LoanInfo::$tabIndex++; ?>" <?php echo Strings::isChecked('No', LoanInfo::$ownedSameEntity); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'ownedSameEntity', 'sArr' => $ALSSecArr, 'opt' => 'I']); ?>><span></span>No
                                        </label>
                                    </div>
                                <?php } else {
                                    echo '<b>' . LoanInfo::$ownedSameEntity . '</b>';
                                } ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!-- Row 9 End -->
            <div class="col-md-12 row9SeparatorCls separator separator-dashed my-2 separator12"
                 id="row9SeparatorDiv"></div>


            <div class="col-md-12"><!-- Row 10 Start -->
                <div class="row">
                    <div class="row10SeparatorCls   col-md-3 isHouseProperty_disp <?php echo loanForm::showField('isHouseProperty'); ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('isHouseProperty', 'col-md-12 '); ?>
                            <div class="col-md-12">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <select class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'isHouseProperty', 'sArr' => $ALSSecArr, 'opt' => 'M']); ?>"
                                            name="isHouseProperty" id="isHouseProperty"
                                            TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'isHouseProperty', 'sArr' => $ALSSecArr, 'opt' => 'I']); ?>>
                                        <option value=""> - Select -</option>
                                        <?php
                                        for ($i = 0; $i < count($glHMLOHouseType); $i++) {
                                            $sOpt = '';
                                            $typeOfHouse = '';
                                            $typeOfHouse = trim($glHMLOHouseType[$i]);
                                            $sOpt = Arrays::isSelected($typeOfHouse, Strings::showField('isHouseProperty', 'FilePropInfo'));
                                            echo "<option value=\"" . $typeOfHouse . "\" " . $sOpt . '>' . $typeOfHouse . '</option>';
                                        }
                                        ?>
                                    </select>
                                <?php } else {
                                    echo '<b>' . Strings::showField('isHouseProperty', 'FilePropInfo') . '</b>';
                                } ?>
                            </div>
                        </div>
                    </div>
                    <div class="row10SeparatorCls  docType col-md-3 docType_disp <?php echo loanForm::showField('docType'); ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('docType', 'col-md-12 '); ?>
                            <div class="col-md-12">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <select
                                            class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'docType', 'sArr' => $ALSSecArr, 'opt' => 'M']); ?>"
                                            name="docType" id="docType"
                                            TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'docType', 'sArr' => $ALSSecArr, 'opt' => 'I']); ?>>
                                        <option value=""> - Select -</option>
                                        <?php
                                        sort($gldocType);
                                        for ($i = 0; $i < count($gldocType); $i++) {
                                            $sOpt = '';
                                            $prodocType = '';
                                            $prodocType = trim($gldocType[$i]);
                                            $sOpt = Arrays::isSelected($prodocType, LoanInfo::$docTypeLoanterms);
                                            echo "<option value=\"" . $prodocType . "\" " . $sOpt . '>' . $prodocType . '</option>';
                                        }
                                        ?>
                                    </select>
                                <?php } else {
                                    echo '<b>' . Strings::showField('docType', 'fileHMLOPropertyInfo') . '</b>';
                                } ?>
                            </div>
                        </div>
                    </div>
                    <div class="row10SeparatorCls col-md-3 principalRepayment_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'principalRepayment', 'sArr' => $ALSSecArr, 'opt' => 'D']); ?>">
                        <div class="row form-group">
                            <?php
                            echo loanForm::label('principalRepayment', 'col-md-12 ');
                            ?>
                            <div class="col-md-12">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <select
                                            class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'principalRepayment', 'sArr' => $ALSSecArr, 'opt' => 'M']); ?>"
                                            name="principalRepayment" id="principalRepayment"
                                            TABINDEX="<?php echo LoanInfo::$tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'principalRepayment', 'sArr' => $ALSSecArr, 'opt' => 'I']); ?>>
                                        <option value=""> - Select -</option>
                                        <?php
                                        for ($i = 0; $i < count($glprincipalRepayment); $i++) {
                                            $sOpt = '';
                                            $sOpt = Arrays::isSelected($glprincipalRepayment[$i], Strings::showField('principalRepayment', 'fileHMLONewLoanInfo'));
                                            echo "<option value=\"" . $glprincipalRepayment[$i] . "\" " . $sOpt . '>' . $glprincipalRepayment[$i] . '</option>';
                                        }
                                        ?>
                                    </select>
                                <?php } else {
                                    echo '<b>' . Strings::showField('principalRepayment', 'fileHMLONewLoanInfo') . '</b>';
                                } ?>
                            </div>
                        </div>
                    </div>
                    <div class="row10SeparatorCls col-md-3 paymentFrequency_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'paymentFrequency', 'sArr' => $ALSSecArr, 'opt' => 'D']); ?>">
                        <div class="row form-group">
                            <?php
                            echo loanForm::label('paymentFrequency', 'col-md-12 ');
                            ?>
                            <div class="col-md-12">
                                <?php if (LMRequest::$allowToEdit) { ?>
                                    <select name="paymentFrequency"
                                            class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'paymentFrequency', 'sArr' => $ALSSecArr, 'opt' => 'M']); ?>"
                                            id="paymentFrequency"
                                            tabindex="<?php echo LoanInfo::$tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'paymentFrequency', 'sArr' => $ALSSecArr, 'opt' => 'I']); ?>>
                                        <option value=""> - Select -</option>
                                        <?php if (LoanInfo::$paymentFrequency == '') LoanInfo::$paymentFrequency = '12';
                                        if (count(fileCustomPCLoanTermsController::$HMLOPCBasicPaymentFrequencyInfoArray ?? []) > 0) {
                                            foreach (fileCustomPCLoanTermsController::$HMLOPCBasicPaymentFrequencyInfoArray as $payFrequencyVal) {
                                                $selectOpt = '';
                                                if (LoanInfo::$paymentFrequency == $payFrequencyVal) $selectOpt = 'selected';
                                                echo "<option value=\"" . $payFrequencyVal . "\" " . $selectOpt . '>' . $glpaymentFrequency[$payFrequencyVal] . '</option>';
                                            }
                                        } else {
                                            foreach ($glpaymentFrequency as $payFrequencyKey => $payFrequencyVal) {
                                                $selectOpt = '';
                                                if (LoanInfo::$paymentFrequency == $payFrequencyKey) $selectOpt = 'selected';
                                                echo "<option value=\"" . $payFrequencyKey . "\" " . $selectOpt . '>' . $payFrequencyVal . '</option>';
                                            }
                                        }
                                        ?>
                                    </select>
                                <?php } else { ?>
                                    <b><?php echo Strings::showField('paymentFrequency', 'fileHMLOPropertyInfo') ?></b><?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!-- Row 10 End -->
            <div class="col-md-12 row10SeparatorCls separator separator-dashed my-2 separator12"
                 id="row10SeparatorDiv"></div>

            <div class="col-md-12"><!-- Row 11 Start -->
                <div class="row">
                    <div class="row11SeparatorCls col-md-3 applicationLoanExitPlan_disp <?php echo loanForm::showField('applicationLoanExitPlan'); ?>">
                        <div class="row">
                            <?php echo loanForm::label('applicationLoanExitPlan', 'col-md-12 '); ?>
                            <div class="col-md-12">
                                <?php
                                echo loanForm::select(
                                        'applicationLoanExitPlan',
                                        LMRequest::$allowToEdit,
                                        1,
                                        LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->applicationLoanExitPlan,
                                        glHMLOExitStrategy::getApplicationLoanExitOptions(LMRequest::$PCID, LMRequest::File()->getTblLMRClientType_by_LMRID()->ClientType),
                                        "if(this.value == 'Other'){
                                        $('.applicationLoanExitPlanOther').show() ;
                                        }
                                         else{
                                         $('#applicationLoanExitPlanOther').val('');
                                         $('.applicationLoanExitPlanOther').hide()
                                         }",
                                        ' chzn-select ',
                                        ' ',
                                        'Please Select ' . loanForm::$permissions['applicationLoanExitPlan']->fieldLabel,
                                        (LMRequest::$PCID == glPCID::PCID_PROD_CV3 && LMRequest::$activeTab == 'HMLI')
                                ); ?>
                            </div>
                        </div>
                    </div>
                    <div class="row11SeparatorCls col-md-3 form-group applicationLoanExitPlanOther applicationLoanExitPlanOther_child <?php echo loanForm::showField('applicationLoanExitPlanOther'); ?>"
                         style="<?php echo LoanInfo::$applicationLoanExitPlanDispOpt; ?>">
                        <div class="row">
                            <?php echo loanForm::label2('applicationLoanExitPlanOther', 'col-md-12 '); ?>
                            <div class="col-md-12">
                                <?php echo loanForm::text(
                                        'applicationLoanExitPlanOther',
                                        LMRequest::$allowToEdit,
                                        LoanInfo::$tabIndex++,
                                        LoanInfo::$applicationLoanExitPlanOther,
                                        '',
                                        ''
                                ); ?>
                            </div>
                        </div>
                    </div>
                    <div class="row11SeparatorCls col-md-3 accountExecutiveLoanExitNotes_disp <?php echo loanForm::showField('accountExecutiveLoanExitNotes'); ?>">
                        <div class="row">
                            <?php echo loanForm::label('accountExecutiveLoanExitNotes', 'col-md-12 '); ?>
                            <div class="col-md-12">
                                <?php echo loanForm::textarea(
                                        'accountExecutiveLoanExitNotes',
                                        LMRequest::$allowToEdit,
                                        1,
                                        LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->accountExecutiveLoanExitNotes,
                                        'validateMaxLength',
                                        '',
                                        '',
                                        '500',
                                ); ?>
                            </div>
                        </div>
                    </div>
                    <?php if ($isEF != 1) { ?>
                        <div class="row11SeparatorCls col-md-3 exitStrategy_disp <?php echo loanForm::showField('exitStrategy'); ?>">
                            <div class="row">
                                <?php echo loanForm::label(
                                        'exitStrategy',
                                        'col-md-12 ',
                                        ''
                                ); ?>
                                <div class="col-md-12">
                                    <?php echo loanForm::select(
                                            'exitStrategy_mirror',
                                            LMRequest::$allowToEdit,
                                            1,
                                            LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->exitStrategy,
                                            glHMLOExitStrategy::getExitStrategy(LMRequest::$PCID, LMRequest::File()->getTblLMRClientType_by_LMRID()->ClientType),
                                            'exitStrategyHideShow(this.value, \'exitStrategyExplain\', \'\');fileCommon.mirrorField(this,\'exitStrategy_mirror\');',
                                            ' chzn-select exitStrategy_mirror ',
                                            ' ',
                                            'Please Select ' . loanForm::$permissions['exitStrategy']->fieldLabel
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <div class="row11SeparatorCls col-md-3 form-group exitStrategyExplain borComment_child <?php echo loanForm::showField('borComment'); ?>"
                             style="<?php echo HMLOLoanTermsCalculation::$exitStrategyExplainDispOpt; ?>">
                            <div class="row">
                                <?php echo loanForm::label2('borComment', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php echo loanForm::textarea(
                                            'borComment',
                                            LMRequest::$allowToEdit,
                                            LoanInfo::$tabIndex++,
                                            LoanInfo::$borComment,
                                            '',
                                            ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <div class="row11SeparatorCls col-md-3 form-group rentalIncomePerMonthField rentalIncomePerMonth_child <?php echo loanForm::showField('rentalIncomePerMonth'); ?>"
                             style="<?php
                             echo HMLOLoanTermsCalculation::$rentalIncomePerMonthFieldDispOpt
                             ?>">
                            <div class="row">
                                <?php echo loanForm::label2('rentalIncomePerMonth', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php echo loanForm::currency(
                                            'rentalIncomePerMonth',
                                            LMRequest::$allowToEdit,
                                            LoanInfo::$tabIndex++,
                                            LoanInfo::$rentalIncomePerMonth,
                                            '',
                                            ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <div class="row11SeparatorCls col-md-3 form-group  expectedTimelineToExit_Disp <?php echo loanForm::showField('expectedTimelineToExit'); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('expectedTimelineToExit', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php if (LMRequest::$allowToEdit) { ?>
                                        <input type="text"
                                               name="expectedTimelineToExit"
                                               id="expectedTimelineToExit"
                                               class="form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'expectedTimelineToExit', 'sArr' => $ALSSecArr, 'opt' => 'M']); ?>"
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'expectedTimelineToExit', 'sArr' => $ALSSecArr, 'opt' => 'I']); ?>
                                               tabindex="<?php echo LoanInfo::$tabIndex++; ?>"
                                               maxlength="32"
                                               value="<?php echo htmlentities(Strings::showField('expectedTimelineToExit', 'fileHMLOPropertyInfo')); ?>">
                                    <?php } else { ?>
                                        <label><?php echo Strings::showField('expectedTimelineToExit', 'fileHMLOPropertyInfo'); ?></label>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                        <div class="row11SeparatorCls col-md-3 form-group <?php echo loanForm::showField('balloonPayment'); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('balloonPayment', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php echo loanForm::radio(
                                            'balloonPayment',
                                            LMRequest::$allowToEdit,
                                            1,
                                            LoanInfo::$balloonPayment,
                                            [
                                                    'Yes' => 'Yes',
                                                    'No'  => 'No'
                                            ],
                                            '',
                                            ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <div class="row11SeparatorCls col-md-3 form-group <?php echo loanForm::showField('prePayExcessOf20percent'); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('prePayExcessOf20percent', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php echo loanForm::radio(
                                            'prePayExcessOf20percent',
                                            LMRequest::$allowToEdit,
                                            1,
                                            LoanInfo::$prePayExcessOf20percent,
                                            [
                                                    'Yes' => 'Yes',
                                                    'No'  => 'No'
                                            ],
                                            '',
                                            ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <div class="row11SeparatorCls col-md-3 form-group <?php echo loanForm::showField('limitedOrNot'); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('limitedOrNot', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php echo loanForm::radio(
                                            'limitedOrNot',
                                            LMRequest::$allowToEdit,
                                            1,
                                            LoanInfo::$limitedOrNot,
                                            [
                                                    'Yes' => 'Yes',
                                                    'No'  => 'No'
                                            ],
                                            '',
                                            ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                    <div class="row11SeparatorCls col-md-3 form-group <?php echo loanForm::showField('isBorPersonallyGuaranteeLoan'); ?>">
                        <div class="row">
                            <?php echo loanForm::label2('isBorPersonallyGuaranteeLoan', 'col-md-12 '); ?>
                            <div class="col-md-12">
                                <?php echo loanForm::radio(
                                        'isBorPersonallyGuaranteeLoan',
                                        LMRequest::$allowToEdit,
                                        1,
                                        LoanInfo::$isBorPersonallyGuaranteeLoan,
                                        [
                                                'Yes' => 'Yes',
                                                'No'  => 'No'
                                        ],
                                        '',
                                        ''
                                ); ?>
                            </div>
                        </div>
                    </div>
                    <div class="row11SeparatorCls col-md-3 form-group <?php echo loanForm::showField('loanGuarantee'); ?>">
                        <div class="row">
                            <?php echo loanForm::label2('loanGuarantee', 'col-md-12 '); ?>
                            <div class="col-md-12">
                                <?php echo loanForm::select(
                                        'loanGuarantee',
                                        LMRequest::$allowToEdit,
                                        1,
                                        LoanInfo::$loanGuarantee,
                                        loanGuaranteeTypes::$options,
                                        '',
                                        '',
                                        '-- Select One --'
                                ); ?>
                            </div>
                        </div>
                    </div>
                    <div class="row11SeparatorCls col-md-3 form-group <?php echo loanForm::showField('assumability'); ?>">
                        <div class="row">
                            <?php echo loanForm::label2('assumability', 'col-md-12 '); ?>
                            <div class="col-md-12">
                                <?php echo loanForm::radio(
                                        'assumability',
                                        LMRequest::$allowToEdit,
                                        1,
                                        LoanInfo::$assumability,
                                        [
                                                'Yes' => 'Yes - See Terms &amp; Conditions',
                                                'No'  => 'No'
                                        ],
                                        '',
                                        ''
                                ); ?>
                            </div>
                        </div>
                    </div>
                    <div class="row11SeparatorCls col-md-3 form-group <?php echo loanForm::showField('securityInstrument'); ?>">
                        <div class="row">
                            <?php echo loanForm::label2('securityInstrument', 'col-md-12 '); ?>
                            <div class="col-md-12">
                                <?php echo loanForm::select(
                                        'securityInstrument',
                                        LMRequest::$allowToEdit,
                                        1,
                                        LoanInfo::$securityInstrument,
                                        gSecurityInstrumentArray::$options,
                                        '',
                                        '',
                                        '-- Select One --'
                                ); ?>
                            </div>
                        </div>
                    </div>
                    <?php if ($isEF != 1) { ?>
                        <div class="row11SeparatorCls col-md-3 form-group isOwnLand_disp <?php echo loanForm::showField('isOwnLand'); ?> landValueCls"
                             style="<?php echo LoanInfo::$landValueCls ?>">
                            <div class="row">
                                <?php echo loanForm::label2('isOwnLand', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php echo loanForm::radio(
                                            'isOwnLand',
                                            LMRequest::$allowToEdit,
                                            1,
                                            LoanInfo::$isOwnLand,
                                            [
                                                    'Yes' => 'Yes',
                                                    'No'  => 'No'
                                            ],
                                            '',
                                            ''
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <div class="row11SeparatorCls col-md-3 form-group <?php echo loanForm::showField('HMLOEstateHeldIn'); ?>">
                            <div class="row">
                                <?php echo loanForm::label2('HMLOEstateHeldIn', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php echo loanForm::select(
                                            'HMLOEstateHeldIn',
                                            LMRequest::$allowToEdit,
                                            1,
                                            LoanInfo::$HMLOEstateHeldIn,
                                            glEstateHeldIn::$options,
                                            '',
                                            'HMLOEstateHeldInCls',
                                            '-- Select One --'
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                    <div class="row11SeparatorCls col-md-3 form-group <?php echo loanForm::showField('useOfFunds'); ?>">
                        <div class="row">
                            <?php echo loanForm::label2('useOfFunds', 'col-md-12 '); ?>
                            <div class="col-md-12">
                                <?php
                                if (glCustomJobForProcessingCompany::isPC_CV3(LMRequest::$PCID)) {
                                    $useFundsMaxLength = loanForm::getFieldLength('useOfFunds', 'tblFileHMLOPropInfo');
                                } else {
                                    $useFundsMaxLength = 5000;
                                }
                                echo loanForm::textarea(
                                        'useOfFunds',
                                        LMRequest::$allowToEdit,
                                        LoanInfo::$tabIndex++,
                                        LoanInfo::$useOfFunds,
                                        'validateMaxLength',
                                        'assignValueToUseOfFundsMirrorfield(this.value,this.id);',
                                        '',
                                        $useFundsMaxLength
                                ); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!-- Row 11 end -->
            <div class="col-md-12 row11SeparatorCls separator separator-dashed my-2 separator12"
                 id="row11SeparatorDiv"></div>

        </div>
    </div>
</div>
<?php loanForm::popSectionID(); ?>
<script>
    $(document).ready(function () {
        exitStrategyHideShow($("#exitStrategy").val(), 'exitStrategyExplain', '');
        $('#rateIndex').chosen({'create_option': true, 'persistent_create_option': true, 'skip_no_results': true});
    });
    $('select.js-example-basic-multiple').select2();
    $(function () {
        for (let ind = 8; ind < 12; ind++) {
            if ($('.row' + ind + 'SeparatorCls.secShow').length > 0) { //show titlessssss
                $("#row" + ind + "SeparatorDiv").show();
            } else { //hide title
                $("#row" + ind + "SeparatorDiv").hide();
            }
        }
    });
</script>
