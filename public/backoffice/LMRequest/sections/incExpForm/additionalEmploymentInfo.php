<?php

use models\constants\employedInfo1Array;
use models\constants\gl\glCountryArray;
use models\constants\gl\glIncomeFromOtherSource;
use models\constants\states;
use models\Controllers\loanForm;
use models\cypher;
use models\standard\BaseHTML;
use models\standard\Dates;
use models\standard\Strings;

global $borEmploymentInfo, $LMRId, $op, $tabIndex, $secArr;

loanForm::pushSectionID('BEI');
?>

    <div class="form-group row border <?php echo loanForm::showField('addiEmpInfo'); ?>">
        <div class="col-md-12 p-0">
            <div class="card card-custom">
                <div class="card-header bg-primary-o-40">
                    <?php echo loanForm::addSectionV2(
                        'additionalEmploymentInfo',
                        true,
                        'btn btn-sm btn-success btn-text-primary  btn-icon ml-2 tooltipClass cloneFormButton ',
                        'onclick="cloneFormBorAddEmpInfo(\'borEmplAdditional\',\'icrementSec\')"',
                        'Click to add new Employment Information',
                        'Additional Employment Information',
                    ); ?>
                </div>
                <div class="card-body px-1 py-2" id="showEmploymentInfo">
                    <?php
                    $borEmploymentInfoCnt = count($borEmploymentInfo);
                    if ($borEmploymentInfoCnt == 0) {
                        $borEmploymentInfoCnt = 1;
                    }
                    $borForCnt = 1;
                    $encLMRId = cypher::myEncryption($LMRId);
                    for ($borEmpCnt = 0; $borEmpCnt < $borEmploymentInfoCnt; $borEmpCnt++) {

                        $nameOfEmployer = trim($borEmploymentInfo[$borEmpCnt]['nameOfEmployer']);
                        $addrOfEmployer = trim($borEmploymentInfo[$borEmpCnt]['addrOfEmployer']);
                        $cityOfEmployer = trim($borEmploymentInfo[$borEmpCnt]['cityOfEmployer']);
                        $stateOfEmployer = trim($borEmploymentInfo[$borEmpCnt]['stateOfEmployer']);
                        $zipOfEmployer = trim($borEmploymentInfo[$borEmpCnt]['zipOfEmployer']);
                        $countryOfEmployer = trim($borEmploymentInfo[$borEmpCnt]['countryOfEmployer']);
                        $addOrPrevJob = trim($borEmploymentInfo[$borEmpCnt]['addOrPrevJob']);

                        $employmentType = trim($borEmploymentInfo[$borEmpCnt]['employmentType']);
                        $employedFrom = trim($borEmploymentInfo[$borEmpCnt]['employedFrom']);
                        $employedTo = trim($borEmploymentInfo[$borEmpCnt]['employedTo']);
                        $monthlyIncome = trim($borEmploymentInfo[$borEmpCnt]['monthlyIncome']);
                        $yrsEmployed = trim($borEmploymentInfo[$borEmpCnt]['yrsEmployed']);
                        $employedByOtherParty = trim($borEmploymentInfo[$borEmpCnt]['employedByOtherParty']);
                        $ownerOrSelfEmpoyed = trim($borEmploymentInfo[$borEmpCnt]['ownerOrSelfEmpoyed']);
                        $overtime = trim($borEmploymentInfo[$borEmpCnt]['overtime']);
                        $commissionOrBonus = trim($borEmploymentInfo[$borEmpCnt]['commissionOrBonus']);
                        $militaryIncome = trim($borEmploymentInfo[$borEmpCnt]['militaryIncome']);
                        $otherHouseHold = trim($borEmploymentInfo[$borEmpCnt]['otherHouseHold']);
                        $otherSourcesIncome = trim($borEmploymentInfo[$borEmpCnt]['otherSourcesIncome']);
                        $otherIncomeSources = explode(',', trim($borEmploymentInfo[$borEmpCnt]['otherIncomeSources']));
                        $position = trim($borEmploymentInfo[$borEmpCnt]['position']);
                        $businessPhone = trim($borEmploymentInfo[$borEmpCnt]['businessPhone']);
                        $emptypeshare = trim($borEmploymentInfo[$borEmpCnt]['emptypeshare']);
                        $empmonthlyincome = trim($borEmploymentInfo[$borEmpCnt]['empmonthlyincome']);

                        if (Dates::IsEmpty($employedFrom)) {
                            $employedFrom = '';
                        } else {
                            $employedFrom = Dates::formatDateWithRE($employedFrom, 'YMD', 'm/d/Y');
                        }

                        if (Dates::IsEmpty($employedTo)) {
                            $employedTo = '';
                        } else {
                            $employedTo = Dates::formatDateWithRE($employedTo, 'YMD', 'm/d/Y');
                        }
                        $employedByOtherPartyChk = $ownerOrSelfEmpoyedChk = '';
                        if ($employedByOtherParty == 1) {
                            $employedByOtherPartyChk = ' checked ';
                        }
                        if ($ownerOrSelfEmpoyed == 1) {
                            $ownerOrSelfEmpoyedChk = ' checked ';
                        }
                        $additionalSel = $previousSel = '';
                        if ($addOrPrevJob == 'additional') {
                            $additionalSel = ' selected ';
                        } else if ($addOrPrevJob == 'previous') {
                            $previousSel = ' selected ';
                        }

                        $businessPhone = Strings::formatPhoneNumber($businessPhone);
                        //$LMRId = $borEmploymentInfo[$borEmpCnt]['fileID'];
                        $LOBEID = $borEmploymentInfo[$borEmpCnt]['LOBEID'];
                        $encLID = cypher::myEncryption($LOBEID);
                        ?>
                        <div class="card card-custom mb-2 borEmplAdditional borEmplAdditionalDiv<?php echo $borForCnt; ?>"
                             data-sN="<?php echo trim($borForCnt); ?>"
                             id="borEmpl_<?php echo trim($borForCnt); ?>_div">
                            <div class="card-header bg-primary-o-20">
                                <div class="card-title">
                                    <h3 class="card-label entityInfoCnt">
                                        Additional Employment Information
                                        :
                                        <span class="icrementSec"><?php echo trim($borForCnt); ?></span>
                                    </h3>
                                </div>
                                <div class="card-toolbar">
                                    <?php if ($op != 'view') { ?>
                                        <?php echo loanForm::sectionButtonDelete(
                                            'Click to delete',
                                            'deleteEmployementInfoCls', [
                                            'lmrid' => $encLMRId,
                                            'lobeid' => $encLID,
                                            'sectionnum' => $borForCnt,
                                            'toggle' => 'toggle',
                                            'placement' => 'top',
                                        ]); ?>
                                    <?php } ?>
                                    <?php echo loanForm::sectionButtonShowHide(
                                        true,
                                        'Toggle Card',
                                        null, [
                                            'card-tool' => 'toggle',
                                            'section' => 'body_borEmpl' . $borForCnt . '_body',
                                            'toggle' => 'tooltip',
                                            'placement' => 'top',
                                        ]
                                    ); ?>
                                </div>
                            </div>
                            <div class="card-body body_borEmpl<?php echo trim($borForCnt); ?>_body ">
                                <?php echo loanForm::hidden('AddiontalEmplInfo[' . $borForCnt . '][LOBEID]', $encLID); ?>

                                <div class="row">
                                    <div class="form-group col-md-6">
                                        <?php echo loanForm::label2(
                                            'AddiontalEmplInfo_' . $borForCnt . '_addOrPrevJob',
                                            null, null, null, null,
                                            'Is this job an additional or previous Job?'
                                        ); ?>

                                        <?php echo loanForm::select(
                                            'AddiontalEmplInfo[' . $borForCnt . '][addOrPrevJob]',
                                            true,
                                            $tabIndex++,
                                            $addOrPrevJob, [
                                            'additional' => 'Additional',
                                            'previous' => 'Previous',
                                        ],
                                            null,
                                            'chzn-select',
                                            '- Select One -'
                                        ); ?>

                                    </div>

                                    <div class="form-group col-md-6">

                                        <?php echo loanForm::label2(
                                            'AddiontalEmplInfo_' . $borForCnt . '_nameOfEmployer',
                                            null, null, null, null,
                                            'Name'
                                        ); ?>

                                        <?php echo loanForm::text(
                                            'AddiontalEmplInfo[' . $borForCnt . '][nameOfEmployer]',
                                            true,
                                            $tabIndex++,
                                            $nameOfEmployer
                                        ); ?>

                                    </div>

                                    <div class="form-group col-md-6">
                                        <script>
                                            $(document).ready(function() {
                                                $('#AddiontalEmplInfo[<?php echo $borForCnt; ?>][addrOfEmployer]').on('input', function() {
                                                    address_lookup.InitLegacy($(this));
                                                });
                                            });
                                        </script>

                                        <?php echo loanForm::label2(
                                            'AddiontalEmplInfo_' . $borForCnt . '_addrOfEmployer',
                                            null, null, null, null,
                                            'Address of Employer'
                                        ); ?>

                                        <?php echo loanForm::text(
                                            'AddiontalEmplInfo[' . $borForCnt . '][addrOfEmployer]',
                                            true,
                                            $tabIndex++,
                                            $addrOfEmployer,
                                            '',
                                            '',
                                            '',
                                            '',
                                            [
                                                'address' => 'AddiontalEmplInfo[' . $borForCnt . '][addrOfEmployer]',
                                                'city' => 'AddiontalEmplInfo[' . $borForCnt . '][cityOfEmployer]',
                                                'state' => 'AddiontalEmplInfo[' . $borForCnt . '][stateOfEmployer]',
                                                'zip' => 'AddiontalEmplInfo[' . $borForCnt . '][zipOfEmployer]',
                                            ]
                                        ); ?>
                                    </div>

                                    <div class="form-group col-md-6">

                                        <?php echo loanForm::label2(
                                            'AddiontalEmplInfo_' . $borForCnt . '_cityOfEmployer',
                                            null, null, null, null,
                                            'Employer City'
                                        ); ?>

                                        <?php echo loanForm::text(
                                            'AddiontalEmplInfo[' . $borForCnt . '][cityOfEmployer]',
                                            true,
                                            $tabIndex++,
                                            $cityOfEmployer
                                        ); ?>

                                    </div>

                                    <div class="form-group col-md-6">

                                        <?php echo loanForm::label2(
                                            'AddiontalEmplInfo_' . $borForCnt . '_stateOfEmployer',
                                            null, null, null, null,
                                            'Employer State'
                                        ); ?>

                                        <?php echo loanForm::select(
                                            'AddiontalEmplInfo[' . $borForCnt . '][stateOfEmployer]',
                                            true,
                                            $tabIndex++,
                                            $stateOfEmployer,
                                            states::getOptions(),
                                            null,
                                            null,
                                            '- Select One -'
                                        ); ?>

                                    </div>
                                    <div class="form-group col-md-6">

                                        <?php echo loanForm::label2(
                                            'AddiontalEmplInfo_' . $borForCnt . '_zipOfEmployer',
                                            null, null, null, null,
                                            'Employer Zip'
                                        ); ?>

                                        <?php echo loanForm::text(
                                            'AddiontalEmplInfo[' . $borForCnt . '][zipOfEmployer]',
                                            true,
                                            $tabIndex++,
                                            $zipOfEmployer
                                        ); ?>

                                    </div>

                                    <div class="form-group col-md-6">

                                        <?php echo loanForm::label2(
                                            'AddiontalEmplInfo_' . $borForCnt . '_countryOfEmployer',
                                            null, null, null, null,
                                            'Employer Country'
                                        ); ?>

                                        <?php echo loanForm::select(
                                            'AddiontalEmplInfo[' . $borForCnt . '][countryOfEmployer]',
                                            true,
                                            $tabIndex++,
                                            $countryOfEmployer,
                                            glCountryArray::$options,
                                            null,
                                            null,
                                            '- Select One -'
                                        ); ?>

                                    </div>

                                    <div class="form-group col-md-6">

                                        <?php echo loanForm::label2(
                                            'AddiontalEmplInfo_' . $borForCnt . '_employmentType',
                                            null, null, null, null,
                                            'Employer Type'
                                        ); ?>

                                        <?php echo loanForm::select(
                                            'AddiontalEmplInfo[' . $borForCnt . '][employmentType]',
                                            true,
                                            $tabIndex++,
                                            $employmentType,
                                            employedInfo1Array::$options,
                                            null,
                                            null,
                                            '- Select One -'
                                        ); ?>

                                    </div>

                                    <div class="employmentType_Disp form-group col-md-6">

                                        <?php echo loanForm::radio(
                                            'AddiontalEmplInfo[' . $borForCnt . '][emptypeshare]',
                                            true,
                                            $tabIndex++,
                                            $emptypeshare, [
                                                'lessthan25' => 'I have ownership share of less than 25%',
                                                'eqmorethan25' => 'I have ownership share of 25% or more',
                                            ]
                                        ); ?>

                                    </div>
                                    <div class="employmentType_Disp form-group col-md-6">

                                        <?php echo loanForm::label2(
                                            'AddiontalEmplInfo_' . $borForCnt . '_empmonthlyincome',
                                            null, null, null, null,
                                            'Monthly Income/Loss'
                                        ); ?>

                                        <?php echo loanForm::currency(
                                            'AddiontalEmplInfo[' . $borForCnt . '][empmonthlyincome]',
                                            true,
                                            $tabIndex++,
                                            $empmonthlyincome
                                        ); ?>

                                    </div>
                                    <div class="form-group col-md-6">

                                        <?php echo loanForm::label2(
                                            'AddiontalEmplInfo_' . $borForCnt . '_employedFrom',
                                            null, null, null, null,
                                            'Employed From'
                                        ); ?>

                                        <?php echo loanForm::date(
                                            'AddiontalEmplInfo[' . $borForCnt . '][employedFrom]',
                                            true,
                                            $tabIndex++,
                                            $employedFrom,
                                            ''
                                        ); ?>

                                    </div>

                                    <div class="form-group col-md-6">
                                        <?php echo loanForm::label2(
                                            'AddiontalEmplInfo_' . $borForCnt . '_employedTo',
                                            null, null, null, null,
                                            'Employed To'
                                        ); ?>

                                        <?php echo loanForm::date(
                                            'AddiontalEmplInfo[' . $borForCnt . '][employedTo]',
                                            true,
                                            $tabIndex++,
                                            $employedTo,
                                            ''
                                        ); ?>

                                    </div>

                                    <div class="form-group col-md-6">

                                        <?php echo loanForm::label2(
                                            'AddiontalEmplInfo_' . $borForCnt . '_yrsEmployed',
                                            null, null, null, null,
                                            'Years employed in this line of work / profession'
                                        ); ?>

                                        <?php echo loanForm::number(
                                            'AddiontalEmplInfo[' . $borForCnt . '][yrsEmployed]',
                                            true,
                                            $tabIndex++,
                                            $yrsEmployed
                                        ); ?>

                                    </div>

                                    <div class="form-group col-md-6">

                                        <?php echo loanForm::checkbox(
                                            'AddiontalEmplInfo[' . $borForCnt . '][employedByOtherParty]',
                                            true,
                                            $tabIndex++,
                                            1,
                                            $employedByOtherPartyChk,
                                            null,
                                            null,
                                            null,
                                            'Employed by a family member, property seller, real estate agent, or other party to the transaction?'
                                        ); ?>

                                    </div>
                                    <div class="form-group col-md-6">

                                        <?php echo loanForm::checkbox(
                                            'AddiontalEmplInfo[' . $borForCnt . '][ownerOrSelfEmpoyed]',
                                            true,
                                            $tabIndex++,
                                            1,
                                            $ownerOrSelfEmpoyedChk,
                                            null,
                                            null,
                                            null,
                                            'Are you the business Owner or Self-Employed?'
                                        ); ?>

                                    </div>
                                    <div class="form-group col-md-6">

                                        <?php echo loanForm::label2(
                                            'AddiontalEmplInfo_' . $borForCnt . '_monthlyIncome',
                                            null, null, null, null,
                                            'Monthly Income'
                                        ); ?>

                                        <?php echo loanForm::currency(
                                            'AddiontalEmplInfo[' . $borForCnt . '][monthlyIncome]',
                                            true,
                                            $tabIndex++,
                                            $monthlyIncome
                                        ); ?>

                                    </div>
                                    <div class="form-group col-md-6">

                                        <?php echo loanForm::label2(
                                            'AddiontalEmplInfo_' . $borForCnt . '_overtime',
                                            null, null, null, null,
                                            'Overtime'
                                        ); ?>

                                        <?php echo loanForm::currency(
                                            'AddiontalEmplInfo[' . $borForCnt . '][overtime]',
                                            true,
                                            $tabIndex++,
                                            $overtime
                                        ); ?>

                                    </div>

                                    <div class="form-group col-md-6">

                                        <?php echo loanForm::label2(
                                            'AddiontalEmplInfo_' . $borForCnt . '_commissionOrBonus',
                                            null, null, null, null,
                                            'Commission / Bonus'
                                        ); ?>

                                        <?php echo loanForm::currency(
                                            'AddiontalEmplInfo[' . $borForCnt . '][commissionOrBonus]',
                                            true,
                                            $tabIndex++,
                                            $commissionOrBonus
                                        ); ?>

                                    </div>

                                    <div class="form-group col-md-6">

                                        <?php echo loanForm::label2(
                                            'AddiontalEmplInfo_' . $borForCnt . '_militaryIncome',
                                            null, null, null, null,
                                            'Military Entitlements'
                                        ); ?>

                                        <?php echo loanForm::currency(
                                            'AddiontalEmplInfo[' . $borForCnt . '][militaryIncome]',
                                            true,
                                            $tabIndex++,
                                            $militaryIncome
                                        ); ?>

                                    </div>

                                    <div class="form-group col-md-6">

                                        <?php echo loanForm::label2(
                                            'AddiontalEmplInfo_' . $borForCnt . '_otherHouseHold',
                                            null, null, null, null,
                                            'Other'
                                        ); ?>

                                        <?php echo loanForm::currency(
                                            'AddiontalEmplInfo[' . $borForCnt . '][otherHouseHold]',
                                            true,
                                            $tabIndex++,
                                            $otherHouseHold
                                        ); ?>

                                    </div>
                                    <div class="form-group col-md-6">

                                        <?php echo loanForm::label2(
                                            'AddiontalEmplInfo_' . $borForCnt . '_otherIncomeSources',
                                            null, null, null, null,
                                            'Income From Other Sources'
                                        ); ?>

                                        <?php echo loanForm::selectMulti(
                                            'AddiontalEmplInfo[' . $borForCnt . '][otherIncomeSources]',
                                            true,
                                            $tabIndex++,
                                            $otherIncomeSources,
                                            glIncomeFromOtherSource::$options,
                                            null,
                                            'chzn-select',
                                            '- Select One -'
                                        ); ?>

                                    </div>
                                    <div class="form-group col-md-6">

                                        <?php echo loanForm::label2(
                                            'AddiontalEmplInfo_' . $borForCnt . '_otherSourcesIncome',
                                            null, null, null, null,
                                            'Total Monthly Income From Other Sources'
                                        ); ?>

                                        <?php echo loanForm::currency(
                                            'AddiontalEmplInfo[' . $borForCnt . '][otherSourcesIncome]',
                                            true,
                                            $tabIndex++,
                                            $otherSourcesIncome
                                        ); ?>

                                    </div>

                                    <div class="form-group col-md-6">

                                        <?php echo loanForm::label2(
                                            'AddiontalEmplInfo_' . $borForCnt . '_position',
                                            null, null, null, null,
                                            'Position / Title / Type of Business'
                                        ); ?>

                                        <?php echo loanForm::text(
                                            'AddiontalEmplInfo[' . $borForCnt . '][position]',
                                            true,
                                            $tabIndex++,
                                            $position
                                        ); ?>

                                    </div>

                                    <div class="form-group col-md-6">

                                        <?php echo loanForm::label2(
                                            'AddiontalEmplInfo_' . $borForCnt . '_businessPhone',
                                            null, null, null, null,
                                            'Employer Phone'
                                        ); ?>

                                        <?php echo loanForm::phone(
                                            'AddiontalEmplInfo[' . $borForCnt . '][businessPhone]',
                                            true,
                                            $tabIndex++,
                                            $businessPhone
                                        ); ?>

                                    </div>
                                </div>
                            </div>
                        </div>

                        <?php
                        $borForCnt++;
                    } ?>
                </div>
            </div>
        </div>
    </div>

    <!-- additionalEmploymentInfo.php -->
<?php

loanForm::popSectionID();
