<?php

use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\PFSWebForm;
use models\Controllers\loanForm;
use models\CustomField;
use models\lendingwise\tblFile;
use models\PageVariables;
use models\standard\Currency;
use models\standard\Strings;

global $HMLORealEstateTaxes, $HOAFees1, $totalInsurance, $tabIndex, $otherMortgage1, $otherMortgageBalance1, $unsecuredLoans1, $unsecuredLoanBalance1;
global $creditCards1, $creditCardsBalance1, $studentLoans1, $studentLoansBalance1, $childSupportOrAlimonyMonthly1, $childSupportOrAlimonyMonthlyBalance1, $expFedTax;
global $expFedTaxOwed, $expStateTax, $expStateTaxOwed, $expRentalPay, $expRentalPayOwed, $expMortgPayResi, $expMortgPayResiOwed, $expMortgPayInvest;
global $expMortgPayInvestOwed, $expPropTaxResi, $expPropTaxResiOwed, $expPropTaxInvest, $expPropTaxInvestOwed, $expLoanPayments;
global $expLoanPaymentsOwed, $expIns, $expInsOwed, $expInvestments, $expInvestmentsOwed, $expTuition, $expTuitionOwed, $expOtherLiving;
global $expOtherLivingOwed, $expMedical, $expMedicalOwed, $other1, $otherBalance1, $primTotalHouseHoldExpenses;

loanForm::pushSectionID('BME');
?>

<!-- borrowerMonthlyExpensesForm.php -->
<div class="card card-custom borrowerMonthlyExpenses">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <?php echo loanForm::toggleSectionV2('BME', true, true, '', 'Borrower Monthly Expenses'); ?>
    </div>

    <div class="card-body borrowerMonthlyExpenses_body">
        <div class="form-group row">
            <table class="table table-hover table-bordered table-condensed table-sm table-vertical-center">
                <tr class="bg-secondary">
                    <td style="width:35%"></td>
                    <td style="width:20%" class="font-weight-bolder">
                        Monthly Payment
                    </td>
                    <td style="width:25%" class="font-weight-bolder">
                        Balance Owed
                    </td>
                </tr>
                <tr class="<?php if (PFSWebForm::$isPFSWebForm) { echo 'd-none'; } ?> <?php echo loanForm::showField('lien1Payment'); ?>">
                    <td>This loan $</td>
                    <td>
                        <h5><?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('lien1Payment', 'LMRInfo')) ?></h5>
                    </td>
                    <td>
                        <h5>
                            $<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('approvedLoanAmt', 'fileHMLOPropertyInfo')) ?></h5>
                    </td>
                </tr>
                <tr class="<?php if (PFSWebForm::$isPFSWebForm) { echo 'd-none'; } ?> <?php echo loanForm::showField('taxes1'); ?>">
                    <td>Real Estate Taxes $</td>
                    <td>
                        <h5><?php echo Currency::formatDollarAmountWithDecimal($HMLORealEstateTaxes); ?></h5>

                        <?php echo loanForm::hidden('HMLORealEstateTaxes', $HMLORealEstateTaxes); ?>
                        <?php echo loanForm::hidden('taxes1', Strings::showField('taxes1', 'incomeInfo')); ?>
                    </td>
                    <td></td>
                </tr>
                <tr style="height:26px;" class="<?php if (PFSWebForm::$isPFSWebForm) { echo 'd-none'; } ?> <?php echo loanForm::showField('HOAFees1'); ?>">
                    <td>HOA Fees $</td>
                    <td>
                        <h5><?php echo Currency::formatDollarAmountWithDecimal($HOAFees1); ?></h5>

                        <?php echo loanForm::hidden('HOAFees1', $HOAFees1); ?>
                    </td>
                    <td></td>
                </tr>
                <tr style="height:26px;" class="even <?php if (PFSWebForm::$isPFSWebForm) {
                    echo 'd-none';
                } ?> <?php echo loanForm::showField('totalInsurance'); ?>">
                    <td>Insurances $</td>
                    <td>
                        <h5><?php echo Currency::formatDollarAmountWithDecimal($totalInsurance) ?> </h5>

                        <?php echo loanForm::hidden('totalInsurance', $totalInsurance); ?>
                    </td>
                    <td></td>
                </tr>
                <tr class="<?php echo loanForm::showField('otherMortgage1'); ?>">
                    <td>
                        <?php echo loanForm::label('otherMortgage1', null, null, null, null, 'Existing mortgages and Taxes/Insurance'); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'otherMortgage1',
                            true,
                            $tabIndex++,
                            $otherMortgage1,
                            null,
                            'calculatePrimaryTotalHouseHoldExpenses(this.value);'
                        ); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'otherMortgageBalance1',
                            true,
                            $tabIndex++,
                            $otherMortgageBalance1
                        ); ?>
                    </td>
                </tr>
                <tr class="even <?php echo loanForm::showField('unsecuredLoans1'); ?>">
                    <td>
                        <?php echo loanForm::label('unsecuredLoans1', null, null, null, null, 'Installment Loans'); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'unsecuredLoans1',
                            true,
                            $tabIndex++,
                            $unsecuredLoans1,
                            null,
                            'calculatePrimaryTotalHouseHoldExpenses(this.value);'
                        ); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'unsecuredLoanBalance1',
                            true,
                            $tabIndex++,
                            $unsecuredLoanBalance1
                        ); ?>
                    </td>
                </tr>
                <tr class="<?php echo loanForm::showField('creditCards1'); ?>">
                    <td>
                        <?php echo loanForm::label('creditCards1', null, null, null, null, 'Credit Cards'); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'creditCards1',
                            true,
                            $tabIndex++,
                            $creditCards1,
                            null,
                            'calculatePrimaryTotalHouseHoldExpenses(this.value);'
                        ); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'creditCardsBalance1',
                            true,
                            $tabIndex++,
                            $creditCardsBalance1
                        ); ?>
                    </td>
                </tr>
                <tr class="even <?php echo loanForm::showField('studentLoans1'); ?>">
                    <td>
                        <?php echo loanForm::label('studentLoans1', null, null, null, null, 'Student Loans'); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'studentLoans1',
                            true,
                            $tabIndex++,
                            $studentLoans1,
                            null,
                            'calculatePrimaryTotalHouseHoldExpenses(this.value);'
                        ); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'studentLoansBalance1',
                            true,
                            $tabIndex++,
                            $studentLoansBalance1
                        ); ?>
                    </td>
                </tr>
                <tr class="<?php echo loanForm::showField('childSupportOrAlimonyMonthly1'); ?>">
                    <td>
                        <?php echo loanForm::label('childSupportOrAlimonyMonthly1', null, null, null, null, 'Alimony/Child Support'); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'childSupportOrAlimonyMonthly1',
                            true,
                            $tabIndex++,
                            $childSupportOrAlimonyMonthly1,
                            null,
                            'calculatePrimaryTotalHouseHoldExpenses(this.value);'
                        ); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'childSupportOrAlimonyMonthlyBalance1',
                            true,
                            $tabIndex++,
                            $childSupportOrAlimonyMonthlyBalance1
                        ); ?>
                    </td>
                </tr>

                <tr class="<?php echo loanForm::showField('expFedTax'); ?>">
                    <td>
                        <?php echo loanForm::label('expFedTax', null, null, null, null, 'Federal Income and Other Taxes'); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'expFedTax',
                            true,
                            $tabIndex++,
                            $expFedTax,
                            null,
                            'calculatePrimaryTotalHouseHoldExpenses(this.value);'
                        ); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'expFedTaxOwed',
                            true,
                            $tabIndex++,
                            $expFedTaxOwed
                        ); ?>
                    </td>
                </tr>

                <tr class="<?php echo loanForm::showField('expStateTax'); ?>">
                    <td>
                        <?php echo loanForm::label('expStateTax', null, null, null, null, 'State Income and Other Taxes'); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'expStateTax',
                            true,
                            $tabIndex++,
                            $expStateTax,
                            null,
                            'calculatePrimaryTotalHouseHoldExpenses(this.value);'
                        ); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'expStateTaxOwed',
                            true,
                            $tabIndex++,
                            $expStateTaxOwed
                        ); ?>
                    </td>
                </tr>

                <tr class="<?php echo loanForm::showField('expRentalPay'); ?>">
                    <td>
                        <?php echo loanForm::label('expRentalPay', null, null, null, null, 'Rental Payments, Co-op or Condo Maintenance'); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'expRentalPay',
                            true,
                            $tabIndex++,
                            $expRentalPay,
                            null,
                            'calculatePrimaryTotalHouseHoldExpenses(this.value);'
                        ); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'expRentalPayOwed',
                            true,
                            $tabIndex++,
                            $expRentalPayOwed
                        ); ?>
                    </td>
                </tr>

                <tr class="<?php echo loanForm::showField('expMortgPayResi'); ?>">
                    <td>
                        <?php echo loanForm::label('expMortgPayResi', null, null, null, null, 'Mortgage Payments - Residential'); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'expMortgPayResi',
                            true,
                            $tabIndex++,
                            $expMortgPayResi,
                            null,
                            'calculatePrimaryTotalHouseHoldExpenses(this.value);'
                        ); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'expMortgPayResiOwed',
                            true,
                            $tabIndex++,
                            $expMortgPayResiOwed
                        ); ?>
                    </td>
                </tr>

                <tr class="<?php echo loanForm::showField('expMortgPayInvest'); ?>">
                    <td>
                        <?php echo loanForm::label('expMortgPayInvest', null, null, null, null, 'Mortgage Payments - Investment'); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'expMortgPayInvest',
                            true,
                            $tabIndex++,
                            $expMortgPayInvest,
                            null,
                            'calculatePrimaryTotalHouseHoldExpenses(this.value);'
                        ); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'expMortgPayInvestOwed',
                            true,
                            $tabIndex++,
                            $expMortgPayInvestOwed
                        ); ?>
                    </td>
                </tr>

                <tr class="<?php echo loanForm::showField('expPropTaxResi'); ?>">
                    <td>
                        <?php echo loanForm::label('expPropTaxResi', null, null, null, null, 'Property Taxes - Residential'); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'expPropTaxResi',
                            true,
                            $tabIndex++,
                            $expPropTaxResi,
                            null,
                            'calculatePrimaryTotalHouseHoldExpenses(this.value);'
                        ); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'expPropTaxResiOwed',
                            true,
                            $tabIndex++,
                            $expPropTaxResiOwed
                        ); ?>
                    </td>
                </tr>

                <tr class="<?php echo loanForm::showField('expPropTaxInvest'); ?>">
                    <td>
                        <?php echo loanForm::label('expPropTaxInvest', null, null, null, null, 'Property Taxes - Investment'); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'expPropTaxInvest',
                            true,
                            $tabIndex++,
                            $expPropTaxInvest,
                            null,
                            'calculatePrimaryTotalHouseHoldExpenses(this.value);'
                        ); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'expPropTaxInvestOwed',
                            true,
                            $tabIndex++,
                            $expPropTaxInvestOwed
                        ); ?>
                    </td>
                </tr>

                <tr class="<?php echo loanForm::showField('expLoanPayments'); ?>">
                    <td>
                        <?php echo loanForm::label('expLoanPayments', null, null, null, null, 'Interest & Principal Payments on Loans'); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'expLoanPayments',
                            true,
                            $tabIndex++,
                            $expLoanPayments,
                            null,
                            'calculatePrimaryTotalHouseHoldExpenses(this.value);'
                        ); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'expLoanPaymentsOwed',
                            true,
                            $tabIndex++,
                            $expLoanPaymentsOwed
                        ); ?>
                    </td>
                </tr>

                <tr class="<?php echo loanForm::showField('expIns'); ?>">
                    <td>
                        <?php echo loanForm::label('expIns', null, null, null, null, 'Insurance'); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'expIns',
                            true,
                            $tabIndex++,
                            $expIns,
                            null,
                            'calculatePrimaryTotalHouseHoldExpenses(this.value);'
                        ); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'expInsOwed',
                            true,
                            $tabIndex++,
                            $expInsOwed
                        ); ?>
                    </td>
                </tr>

                <tr class="<?php echo loanForm::showField('expInvestments'); ?>">
                    <td>
                        <?php echo loanForm::label('expInvestments', null, null, null, null, 'Investments (including tax shelters)'); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'expInvestments',
                            true,
                            $tabIndex++,
                            $expInvestments,
                            null,
                            'calculatePrimaryTotalHouseHoldExpenses(this.value);'
                        ); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'expInvestmentsOwed',
                            true,
                            $tabIndex++,
                            $expInvestmentsOwed
                        ); ?>
                    </td>
                </tr>

                <tr class="<?php echo loanForm::showField('expTuition'); ?>">
                    <td>
                        <?php echo loanForm::label('expTuition', null, null, null, null, 'Tuition'); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'expTuition',
                            true,
                            $tabIndex++,
                            $expTuition,
                            null,
                            'calculatePrimaryTotalHouseHoldExpenses(this.value);'
                        ); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'expTuitionOwed',
                            true,
                            $tabIndex++,
                            $expTuitionOwed
                        ); ?>
                    </td>
                </tr>

                <tr class="<?php echo loanForm::showField('expOtherLiving'); ?>">
                    <td>
                        <?php echo loanForm::label('expOtherLiving', null, null, null, null, 'Other Living Expenses'); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'expOtherLiving',
                            true,
                            $tabIndex++,
                            $expOtherLiving,
                            null,
                            'calculatePrimaryTotalHouseHoldExpenses(this.value);'
                        ); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'expOtherLivingOwed',
                            true,
                            $tabIndex++,
                            $expOtherLivingOwed
                        ); ?>
                    </td>
                </tr>

                <tr class="<?php echo loanForm::showField('expMedical'); ?>">
                    <td>
                        <?php echo loanForm::label('expMedical', null, null, null, null, 'Medical Expenses'); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'expMedical',
                            true,
                            $tabIndex++,
                            $expMedical,
                            null,
                            'calculatePrimaryTotalHouseHoldExpenses(this.value);'
                        ); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'expMedicalOwed',
                            true,
                            $tabIndex++,
                            $expMedicalOwed
                        ); ?>
                    </td>
                </tr>

                <tr class="even <?php echo loanForm::showField('other1'); ?>">
                    <td>
                        <?php echo loanForm::label('other1', null, null, null, null, 'Other (Job Related Expenses: Child Care, Union Dues,etc...)'); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'other1',
                            true,
                            $tabIndex++,
                            $other1,
                            null,
                            'calculatePrimaryTotalHouseHoldExpenses(this.value);'
                        ); ?>
                    </td>
                    <td>
                        <?php echo loanForm::currency(
                            'otherBalance1',
                            true,
                            $tabIndex++,
                            $otherBalance1
                        ); ?>
                    </td>
                </tr>
                <tr>
                    <td colspan="3">&nbsp;</td>
                </tr>
                <tr class="<?php if (PFSWebForm::$isPFSWebForm) { echo 'd-none'; } ?>">
                    <td style="vertical-align:middle;" class="font-weight-bolder">
                        Total Household Expenses
                    </td>
                    <td colspan="2" class="font-weight-bolder">
                        $
                        <span id="primTotalHouseHoldExpenses"><?php echo Currency::formatDollarAmountWithDecimal($primTotalHouseHoldExpenses) ?></span><br>
                        (Borrower)
                    </td>
                </tr>
            </table>
        </div>

        <?php echo CustomField::RenderForTabSection(
            PageVariables::$PCID,
            tblFile::class,
            LMRequest::$LMRId,
            'BME',
            $fileTab,
            $activeTab,
            LMRequest::myFileInfo()->getFileTypes(),
            LMRequest::myFileInfo()->getLoanPrograms()
        ); ?>
    </div>
</div>

<!-- borrowerMonthlyExpensesForm.php -->

<?php

loanForm::popSectionID();