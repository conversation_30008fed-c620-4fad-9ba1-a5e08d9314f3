<?php

use models\constants\employedInfo1Array;
use models\Controllers\loanForm;
use models\cypher;
use models\standard\BaseHTML;
use models\standard\Dates;
use models\standard\Strings;

global $coBEmploymentInfo, $op, $LMRId, $secArr, $tabIndex;

loanForm::pushSectionID('CBEI');
?>

<div class="form-group row border <?php echo loanForm::showField('coAddiEmpInfo'); ?>">
    <div class="col-md-12 p-0">
        <div class="card card-custom">
            <div class="card-header bg-primary-o-40">

                <?php echo loanForm::addSectionV2(
                    'additionalEmploymentInfo',
                    true,
                    'btn btn-sm btn-success btn-text-primary  btn-icon ml-2 tooltipClass cloneFormButton ',
                    'onclick="cloneFormBorAddEmpInfo(\'coborEmplAdditional\',\'cbicrementSec\')"',
                    'Click to add new Employment Information',
                    'Additional Employment Information',
                ); ?>

            </div>
            <div class="card-body px-1 py-2" id="showCoBEmploymentInfo">
                <?php
                $coBEmploymentInfoCnt = count($coBEmploymentInfo);
                if ($coBEmploymentInfoCnt == 0) {
                    $coBEmploymentInfoCnt = 1;
                }
                $borForCnt = 1;
                $encLMRId = cypher::myEncryption($LMRId);

                for ($borEmpCnt = 0; $borEmpCnt < $coBEmploymentInfoCnt; $borEmpCnt++) {

                    $nameOfEmployer = trim($coBEmploymentInfo[$borEmpCnt]['nameOfEmployer']);
                    $addrOfEmployer = trim($coBEmploymentInfo[$borEmpCnt]['addrOfEmployer']);

                    $employmentType = trim($coBEmploymentInfo[$borEmpCnt]['employmentType']);
                    $employedFrom = trim($coBEmploymentInfo[$borEmpCnt]['employedFrom']);
                    $employedTo = trim($coBEmploymentInfo[$borEmpCnt]['employedTo']);
                    $monthlyIncome = trim($coBEmploymentInfo[$borEmpCnt]['monthlyIncome']);
                    $position = trim($coBEmploymentInfo[$borEmpCnt]['position']);
                    $businessPhone = trim($coBEmploymentInfo[$borEmpCnt]['businessPhone']);
                    $emptypeshare = trim($coBEmploymentInfo[$borEmpCnt]['coboremptypeshare']);
                    $empmonthlyincome = trim($coBEmploymentInfo[$borEmpCnt]['coborpopempmonthlyincome']);
                    $coBorrowerOwnerOrSelfEmployed = trim($coBEmploymentInfo[$borEmpCnt]['coBorrowerOwnerOrSelfEmployed']);
                    if (Dates::IsEmpty($employedFrom)) {
                        $employedFrom = '';
                    } else {
                        $employedFrom = Dates::formatDateWithRE($employedFrom, 'YMD', 'm/d/Y');
                    }

                    if (Dates::IsEmpty($employedTo)) {
                        $employedTo = '';
                    } else {
                        $employedTo = Dates::formatDateWithRE($employedTo, 'YMD', 'm/d/Y');
                    }


                    $businessPhone = Strings::formatPhoneNumber($businessPhone);
                    // $LMRId = $coBEmploymentInfo[$borEmpCnt]['fileID'];
                    $LOCBID = $coBEmploymentInfo[$borEmpCnt]['LOCBID'];

                    $encLID = cypher::myEncryption($LOCBID);
                    $coBorrowerOwnerOrSelfEmployedChk = '';
                    if ($coBorrowerOwnerOrSelfEmployed) {
                        $coBorrowerOwnerOrSelfEmployedChk = ' checked ';
                    }
                    ?>
                    <div class="card card-custom mb-2 coborEmplAdditional coborEmplAdditionalDiv<?php echo $borForCnt; ?>"
                         data-sN="<?php echo trim($borForCnt); ?>"
                         id="borEmpl_<?php echo trim($borForCnt); ?>_div">
                        <div class="card-header bg-primary-o-20">
                            <div class="card-title">
                                <h3 class="card-label entityInfoCnt">
                                    Additional Employment Information
                                    :
                                    <span class="cbicrementSec"><?php echo trim($borForCnt); ?></span>
                                </h3>
                            </div>
                            <div class="card-toolbar">

                                <?php if ($op != 'view') { ?>
                                    <?php echo loanForm::sectionButtonDelete(
                                        'Click to delete',
                                        'deleteCoBorEmployementInfoCls', [
                                        'lmrid' => $encLMRId,
                                        'lobeid' => $encLID,
                                        'sectionnum' => $borForCnt,
                                        'toggle' => 'toggle',
                                        'placement' => 'top',
                                    ]); ?>
                                <?php } ?>
                                <?php echo loanForm::sectionButtonShowHide(
                                    true,
                                    'Toggle Card',
                                    null, [
                                        'card-tool' => 'toggle',
                                        'section' => 'body_coborEmpl' . $borForCnt . '_body',
                                        'toggle' => 'tooltip',
                                        'placement' => 'top',
                                    ]
                                ); ?>


                            </div>
                        </div>
                        <div class="card-body body_coborEmpl<?php echo trim($borForCnt); ?>_body ">

                            <?php echo loanForm::hidden('AdditionalCoBorEmplInfo[' . $borForCnt . '][LOCBID]', $encLID); ?>

                            <div class="row">

                                <div class="form-group col-md-6">

                                    <?php echo loanForm::label2(
                                        'AdditionalCoBorEmplInfo_' . $borForCnt . '_nameOfEmployer',
                                        null, null, null, null,
                                        'Name'
                                    ); ?>

                                    <?php echo loanForm::text(
                                        'AdditionalCoBorEmplInfo[' . $borForCnt . '][nameOfEmployer]',
                                        true,
                                        $tabIndex++,
                                        $nameOfEmployer
                                    ); ?>

                                </div>

                                <div class="form-group col-md-6">

                                    <?php echo loanForm::label2(
                                        'AdditionalCoBorEmplInfo_' . $borForCnt . '_addrOfEmployer',
                                        null, null, null, null,
                                        'Address of Employer'
                                    ); ?>

                                    <?php echo loanForm::textarea(
                                        'AdditionalCoBorEmplInfo[' . $borForCnt . '][addrOfEmployer]',
                                        true,
                                        $tabIndex++,
                                        $addrOfEmployer
                                    ); ?>

                                </div>

                            </div>

                            <div class="row">

                                <div class="form-group col-md-6">

                                    <?php echo loanForm::label2(
                                        'AdditionalCoBorEmplInfo_' . $borForCnt . '_employmentType',
                                        null, null, null, null,
                                        'Employer Type'
                                    ); ?>

                                    <?php echo loanForm::select(
                                        'AdditionalCoBorEmplInfo[' . $borForCnt . '][employmentType]',
                                        true,
                                        $tabIndex++,
                                        $employmentType,
                                        employedInfo1Array::$options,
                                        null,
                                        null,
                                        '- Select One -'
                                    ); ?>

                                </div>

                                <div class="employmentTypecls coboremploymentType_<?php echo $borForCnt; ?>_Disp form-group col-md-6
                                                    <?php if ($employmentType != 'Self-Employed') {
                                    echo 'd-none';
                                } ?>">

                                    <?php echo loanForm::radio(
                                        'AdditionalCoBorEmplInfo[' . $borForCnt . '][emptypeshare]',
                                        true,
                                        $tabIndex++,
                                        $emptypeshare,[
                                            'lessthan25' => 'I have ownership share of less than 25%',
                                            'eqmorethan25' => 'I have ownership share of 25% or more',
                                        ]
                                    ); ?>

                                </div>

                                <div class="employmentTypecls coboremploymentType_<?php echo $borForCnt; ?>_Disp form-group col-md-6 <?php if ($employmentType != 'Self-Employed') {
                                    echo 'd-none';
                                } ?>">

                                    <?php echo loanForm::label2(
                                        'AdditionalCoBorEmplInfo_' . $borForCnt . '_empmonthlyincome',
                                        null, null, null, null,
                                        'Monthly Income/Loss'
                                    ); ?>

                                    <?php echo loanForm::currency(
                                        'AdditionalCoBorEmplInfo[' . $borForCnt . '][empmonthlyincome]',
                                        true,
                                        $tabIndex++,
                                        $empmonthlyincome
                                    ); ?>

                                </div>
                                <div class="form-group col-md-6">

                                    <?php echo loanForm::label2(
                                        'AdditionalCoBorEmplInfo_' . $borForCnt . '_employedFrom',
                                        null, null, null, null,
                                        'Employed From'
                                    ); ?>

                                    <?php echo loanForm::date(
                                        'AdditionalCoBorEmplInfo[' . $borForCnt . '][employedFrom]',
                                        true,
                                        $tabIndex++,
                                        $employedFrom
                                    ); ?>

                                </div>

                                <div class="form-group col-md-6">

                                    <?php echo loanForm::label2(
                                        'AdditionalCoBorEmplInfo_' . $borForCnt . '_employedTo',
                                        null, null, null, null,
                                        'Employed To'
                                    ); ?>

                                    <?php echo loanForm::date(
                                        'AdditionalCoBorEmplInfo[' . $borForCnt . '][employedTo]',
                                        true,
                                        $tabIndex++,
                                        $employedTo
                                    ); ?>

                                </div>
                                <div class="form-group col-md-6">

                                    <?php echo loanForm::checkbox(
                                        'AdditionalCoBorEmplInfo[' . $borForCnt . '][coBorrowerOwnerOrSelfEmployed]',
                                        true,
                                        $tabIndex++,
                                        1,
                                        $coBorrowerOwnerOrSelfEmployedChk,
                                        null,
                                        null,
                                        null,
                                        'Are you the business Owner or Self-Employed?'); ?>

                                </div>

                                <div class="form-group col-md-6">

                                    <?php echo loanForm::label2(
                                        'AdditionalCoBorEmplInfo_' . $borForCnt . '_monthlyIncome',
                                        null, null, null, null,
                                        'Monthly Income'
                                    ); ?>

                                    <?php echo loanForm::currency(
                                        'AdditionalCoBorEmplInfo[' . $borForCnt . '][monthlyIncome]',
                                        true,
                                        $tabIndex++,
                                        $monthlyIncome
                                    ); ?>

                                </div>
                                <div class="form-group col-md-6">

                                    <?php echo loanForm::label2(
                                        'AdditionalCoBorEmplInfo_' . $borForCnt . '_position',
                                        null, null, null, null,
                                        'Position / Title / Type of Business'
                                    ); ?>

                                    <?php echo loanForm::text(
                                        'AdditionalCoBorEmplInfo[' . $borForCnt . '][position]',
                                        true,
                                        $tabIndex++,
                                        $position
                                    ); ?>

                                </div>
                                <div class="form-group col-md-6">

                                    <?php echo loanForm::label2(
                                        'AdditionalCoBorEmplInfo_' . $borForCnt . '_businessPhone',
                                        null, null, null, null,
                                        'Employer Phone'
                                    ); ?>

                                    <?php echo loanForm::phone(
                                        'AdditionalCoBorEmplInfo[' . $borForCnt . '][businessPhone]',
                                        true,
                                        $tabIndex++,
                                        $businessPhone
                                    ); ?>

                                </div>

                            </div>
                        </div>
                    </div>

                    <?php
                    $borForCnt++;
                } ?>
            </div>
        </div>
    </div>


</div>

<!-- additionalCoBorrowerEmploymentInfo.php -->

<?php

loanForm::popSectionID();