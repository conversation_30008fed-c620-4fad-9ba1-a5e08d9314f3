<?php

global $lockedSections,  $fileTab, $brokerPointsRate,
       $tabIndex, $fldEditOpt, $origination_based_on_total_loan_amt, $originationPointsValue, $bufferAndMessengerFee,
       $closingCostFinancingFee, $applicationFee, $estdTitleClosingFee, $isEF, $drawsSetUpFee, $valuationAVEFee,
       $valuationBPOFee, $valuationAVMFee, $creditReportFee, $taxServiceFee, $wireFee, $floodCertificateFee,
       $inspectionFees, $dueDiligence, $thirdPartyFees, $escrowFees, $underwritingFees, $prePaidInterest,
       $insurancePremium, $wireTransferFeeToTitle, $pastDuePropertyTaxes, $wholeSaleAdminFee,
       $employmentVerificationFee, $taxReturnOrderFee, $wholeSaleAdminFee, $employmentVerificationFee,
       $taxReturnOrderFee, $taxImpoundsMonth, $taxImpoundsMonthAmt, $taxImpoundsFee, $diemDays,
       $totalDailyInterestCharge, $lien1Rate, $totalEstPerDiem, $interestChargedFromDate,
       $interestChargedEndDate, $broker_based_on_total_loan_amt, $brokerPointsValue, $travelNotaryFee,
       $attorneyFee, $appraisalFee, $processingFee, $drawsFee, $valuationCMAFee, $creditCheckFee,
       $backgroundCheckFee, $documentPreparationFee, $servicingSetUpFee, $floodServiceFee, $projectFeasibility,
       $UccLienSearch, $otherFee, $recordingFee, $propertyTax, $realEstateTaxes, $payOffLiensCreditors,
       $wireTransferFeeToEscrow, $survey, $cityCountyTaxStamps, $constructionHoldbackFee, $insImpoundsMonth,
       $insImpoundsMonthAmt, $insImpoundsFee, $totalFeesAndCost, $perClosingCostFinanced, $PCID,
       $netLenderFundsToBorrower, $feeSectionTotalLoanAmtOpt, $totalLoanAmount, $checkDisplayTermSheet, $lenderNotes;
global $publicUser, $userRole, $isTherePrePaymentPenalty, $prePaymentPenaltyPercentage,
       $prePaymentPenalty, $prePaymentPenaltyResArr, $prepayentSectionDisplay, $exitFeeAmount,
       $exitFeePoints, $extensionOptionPercentage, $extensionRatePercentage,
       $extensionOption, $extensionOptionsAmt, $perDiemToolTip, $customLoanFeeDataStatus;
global $executiveId, $brokerNumber, $prePaymentSelectValArr;

use models\composite\oFile\getFileInfo\fileHMLONewLoanInfo;
use models\composite\oHMLOInfo\fileExtensionOptions;
use models\composite\proposalFormula;
use models\constants\accrualTypes;
use models\constants\gl\glCustomJobForBranch;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glHMLOExtensionOption;
use models\constants\gl\glHMLOPrePaymentPenalty;
use models\constants\gl\glPCID;
use models\constants\gl\glprePaymentPenalty;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\backoffice\LoanInfo;
use models\Controllers\loanForm;
use models\CustomField;
use models\HMLOLoanTermsCalculation;
use models\lendingwise\tblFile;
use models\lendingwise\tblFileExtensionOptions;
use models\lendingwise\tblFileHMLONewLoanInfo;
use models\lendingwise\tblFileHMLOPropInfo;
use models\myFileInfo;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;

$glHMLOPrePaymentPenalty = glHMLOPrePaymentPenalty::$glHMLOPrePaymentPenalty;
$glHMLOExtensionOption = glHMLOExtensionOption::$glHMLOExtensionOption;

$exitFeePoints = Strings::showField('exitFeePoints', 'fileHMLONewLoanInfo');
$exitFeeAmount = Strings::showField('exitFeeAmount', 'fileHMLONewLoanInfo');

//cv3 New Fields
$LMRId = LMRequest::$LMRId;
$fileHMLONewLoanInfo = fileHMLONewLoanInfo::getReport($fileTab, $LMRId);

$cv3OriginationPoint = $fileHMLONewLoanInfo['cv3OriginationPoint'] ?? '0.00';
$cv3ReferralPoint = $fileHMLONewLoanInfo['cv3ReferralPoint'] ?? '0.00';
$cv3OriginationAmount = $fileHMLONewLoanInfo['cv3OriginationAmount'] ?? '0.00';
$cv3ReferralAmount = $fileHMLONewLoanInfo['cv3ReferralAmount'] ?? '0.00';

$extensionFields = fileExtensionOptions::$fileExtensionOptionsData ?? [];
if (!sizeof($extensionFields)) {
    $extensionFields = [
        new tblFileExtensionOptions(),
    ];
}
if (in_array('Fees & Costs', $lockedSections)) {
    $BackupAllowToEdit = LMRequest::$allowToEdit;
    LMRequest::$allowToEdit = false;
}

/**
 * @param $clsName
 * @return mixed|string
 */
function tdHidetoVisibilityHidden($clsName)
{
    if (trim($clsName) == 'secShow') {
        return '';
    } else {
        return $clsName;
    }
}

//next block taken from HMLONewLoanInfoForm.php
$prePaymentPenaltyResArr = glprePaymentPenalty::getPCLevelPrePaymentPenalty($PCID, 'FC');
//end of block taken from HMLONewLoanInfoForm.php

$secArr = BaseHTML::sectionAccess2(['sId' => 'FC', 'opt' => $fileTab]);
loanForm::pushSectionID('FC');

if (trim(BaseHTML::fieldAccess([
        'fNm' => 'estPerDiemInt', 'sArr' => $secArr, 'opt' => 'D'])) == 'secHide' &&
    trim(BaseHTML::fieldAccess([
        'fNm' => 'interestChargedFrom', 'sArr' => $secArr, 'opt' => 'D'])) == 'secHide' &&
    trim(BaseHTML::fieldAccess([
        'fNm' => 'interestChargedEnd', 'sArr' => $secArr, 'opt' => 'D'])) == 'secHide'
) {
    $denimSeperator = 'd-none';
} else {
    $denimSeperator = '';
}

$myFileInfoObject = new myFileInfo();
$myFileInfoObject->LMRId = $LMRId;

$fileHMLONewLoanInfoObject = $myFileInfoObject->getFileHMLONewLoanInfo();
$sellerCreditsFee = $fileHMLONewLoanInfoObject->sellerCreditsFee;
?>
<!-- HMLOFeesAndCostsSection.php -->
<div class="card card-custom FC FCCard <?php if (count($secArr) <= 0) {
    echo 'secHide';
} ?> ">

    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label">
                <?php echo BaseHTML::getSectionHeading('FC'); ?>
            </h3>
            <?php if (trim(BaseHTML::getSectionTooltip('FC')) != '') { ?>&nbsp;
                <i class="popoverClass fas fa-info-circle text-primary "
                   data-html="true"
                   data-content="<?php echo BaseHTML::getSectionTooltip('FC'); ?>"></i>
            <?php } ?>
        </div>
        <div class="card-toolbar">
            <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none cursor-pointer"
                  data-card-tool="toggle"
                  data-toggle="tooltip" data-placement="top" title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>
            <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none cursor-pointer"
                  data-card-tool="reload"
                  data-toggle="tooltip" data-placement="top" title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </span>
            <span
                class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer"
                data-card-tool="toggle"
                data-section="FCCard"
                data-toggle="tooltip" data-placement="top" title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>
        </div>
    </div>
    <input type="hidden" name="oldOriginationPointsRate" id="oldOriginationPointsRate"
           value="<?php echo rtrim(LoanInfo::$originationPointsRate, 0); ?>">
    <input type="hidden" name="oldBrokerPointsRate" id="oldBrokerPointsRate"
           value="<?php echo rtrim(LoanInfo::$brokerPointsRate, 0); ?>">
    <div class="card-body FCCard_body">
        <?php if ($customLoanFeeDataStatus == 1) { ?>
            <div class="alert alert-custom alert-light-primary fade show mb-5" role="alert">
                <div class="alert-icon"><i class="flaticon-warning"></i></div>
                <div class="alert-text font-size-h4 text-primary">The fees below are Auto-populated from your loan
                    guidelines. Click save below if
                    they are correct.
                </div>
            </div>
        <?php } ?>
        <div class="row">
            <div class="col-md-6">
                <div class="row">
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div
                                class="form-group <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'originationPoints', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                                <div class="row mb-2">
                                    <?php echo loanForm::label(
                                        'originationPoints',
                                        'col-md-6 font-weight-bold p-lg-0 my-lg-2',
                                        '',
                                        '@' . loanForm::changeLog(
                                            LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                            'originationPointsRate',
                                            tblFileHMLONewLoanInfo::class,
                                            loanForm::getFieldLabel('originationPoints')
                                        )); ?>
                                    <div class="col-md-6 p-lg-0">
                                        <div class="input-group">
                                            <input type="text" class="form-control input-sm  <?php echo loanForm::isMandatory('originationPoints') ? 'mandatory' : ''; ?>"
                                                   name="originationPointsRate"
                                                   id="originationPointsRate"
                                                   value="<?php echo rtrim(LoanInfo::$originationPointsRate, 0); ?>"
                                                   TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                                   onblur="updateOriginationBrokerPoints('Origination'); validateMinMaxLoanGuidelines();" <?php if ((LoanInfo::$originationPointsRate > 0 && $fldEditOpt == 2) || HMLOLoanTermsCalculation::$lockOriginationValue) echo ' readonly '; ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'originationPoints', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                                            <div class="input-group-append">
                                      <span class="input-group-text">
                                        Points
                                      </span>
                                            </div>
                                        </div>
                                    </div>
                                    <input type="hidden" name="origination_based_on_total_loan_amt"
                                           id="origination_based_on_total_loan_amt"
                                           value="<?php echo HMLOLoanTermsCalculation::$origination_based_on_total_loan_amt; ?>">
                                </div>
                                <?php if (glCustomJobForProcessingCompany::cv3FeesAndCostNewFields(LMRequest::$PCID)
                                    && glCustomJobForBranch::cv3BranchIsRetail($executiveId)
                                    && $brokerNumber
                                ) {
                                    //calculate the cv3 points
                                    $cv3Points = HMLOLoanTermsCalculation::cv3PointsCalculation(LoanInfo::$originationPointsRate, $cv3ReferralPoint);
                                    $cv3OriginationPoint = $cv3Points['cv3OriginationPoint'];
                                    $cv3ReferralPoint = $cv3Points['cv3ReferralPoint'];
                                    ?>
                                    <div class="row mb-2">
                                        <label class="col-md-6">CV3 Origination Points</label>
                                        <div class="col-md-6 p-lg-0">
                                            <div class="input-group">
                                                <input type="text"
                                                       class="form-control input-sm"
                                                       name="cv3OriginationPoint"
                                                       id="cv3OriginationPoint"
                                                       value="<?php echo $cv3OriginationPoint; ?>"
                                                       readonly/>
                                                <div class="input-group-append">
                                              <span class="input-group-text">
                                                Points
                                              </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-md-6">Referral Points
                                            <?php
                                            echo loanForm::changeLog(
                                                LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                                'cv3ReferralPoint',
                                                tblFileHMLONewLoanInfo::class,
                                                'Referral Points'
                                            );
                                            ?>
                                        </label>
                                        <div class="col-md-6 p-lg-0">
                                            <div class="input-group">
                                                <input type="text"
                                                       class="form-control input-sm"
                                                       name="cv3ReferralPoint"
                                                       id="cv3ReferralPoint"
                                                       value="<?php echo $cv3ReferralPoint; ?>"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       autocomplete="off"
                                                       onchange="cv3Origination.cv3OriginationPoints();"/>
                                                <div class="input-group-append">
                                              <span class="input-group-text">
                                                Points
                                              </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php } ?>
                            </div>
                            <div class="form-group <?php echo BaseHTML::fieldAccess(['fNm' => 'origination_total_loan_amt_checked', 'sArr' => $secArr, 'opt' => 'D'])?>">
                                <div class="row pl-5 pl-sm-0 pl-md-0 pl-lg-0 pl-xl-0 pt-5">
                                    <div class="checkbox-inline">
                                        <label
                                                class="checkbox checkbox-outline <?php if (HMLOLoanTermsCalculation::$lockOriginationValue) echo ' checkbox-disabled '; ?> ">
                                            <input type="checkbox"
                                                   name="origination_total_loan_amt_checked"
                                                   id="origination_total_loan_amt_checked"
                                                    <?php if (HMLOLoanTermsCalculation::$lockOriginationValue) echo ' disabled '; ?>
                                                   onclick="updateOriginationBrokerPoints('Origination');"
                                                   value="1" <?php if (HMLOLoanTermsCalculation::$origination_based_on_total_loan_amt > 0) echo 'checked'; ?>><span></span>
                                            Include Closing Cost Financed
                                        </label>
                                        <i class="fa fa-info-circle tooltipClass text-primary" data-html="true"
                                           title="<em>By default the closing costs financed amount is not included in the calculation of points. Check the box to include that amount when calculating total points</em>"></i>
                                    </div>
                                </div>
                            </div>

                        <?php } else { ?>
                            <div
                                class="form-group <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'originationPoints', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                                <?php echo loanForm::label('originationPoints', 'font-weight-bold p-lg-0 my-lg-2',
                                    '', '@'); ?>
                                <label class="font-weight-bold ml-10">
                                    <span class=""><?php echo rtrim(LoanInfo::$originationPointsRate, 0); ?></span>
                                    <span>Points</span>
                                </label>
                            </div>
                        <?php } ?>
                    </div>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) {
                        ?>
                            <div class="row mb-2">
                                <div
                                    class="<?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'originationPoints', 'sArr' => $secArr, 'opt' => 'D'])); ?>">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text" TABINDEX="<?php echo $tabIndex++; ?>"
                                               placeholder="0.00"
                                               name="originationPointsValue"
                                               id="originationPointsValue"
                                               class="form-control input-sm <?php echo loanForm::isMandatory('originationPoints') ? 'mandatory' : ''; ?>"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$originationPointsValue) ?>"
                                               onkeyup="return restrictAlphabetsLoanTerms(this)"
                                               onblur="currencyConverter(this, this.value);calculateOriginationPointsRate();"
                                            <?php if ((LoanInfo::$originationPointsRate > 0 && $fldEditOpt == 2) || HMLOLoanTermsCalculation::$lockOriginationValue) echo ' readonly '; ?> >
                                        <div class="input-group-append">
                                            <span class="input-group-text">
                                                <?php
                                                echo loanForm::changeLog(
                                                    LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                                    'originationPointsValue',
                                                    tblFileHMLONewLoanInfo::class,
                                                    'Origination Points Value'
                                                );
                                                ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php if (glCustomJobForProcessingCompany::cv3FeesAndCostNewFields($PCID)
                                && glCustomJobForBranch::cv3BranchIsRetail($executiveId)
                                && $brokerNumber
                            ) {
                                //calculate cv3 Amount
                                $cv3Amount = HMLOLoanTermsCalculation::cv3AmountCalculation($originationPointsValue, $cv3OriginationAmount, $cv3ReferralAmount);
                                if (!$cv3OriginationAmount || $cv3OriginationAmount = '0.00') {
                                    $cv3OriginationAmount = $cv3Amount['cv3OriginationAmount'];
                                }
                                if (!$cv3ReferralAmount || $cv3ReferralAmount = '0.00') {
                                    $cv3ReferralAmount = $cv3Amount['cv3ReferralAmount'];
                                }
                                ?>
                                <div class="row mb-2">
                                    <div class="">
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="text"
                                                   placeholder="0.00"
                                                   name="cv3OriginationAmount"
                                                   id="cv3OriginationAmount"
                                                   class="form-control input-sm"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal($cv3OriginationAmount); ?>"
                                                   readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mb-2">
                                    <div class="">
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="text" tabindex="<?php echo $tabIndex++; ?>"
                                                   placeholder="0.00"
                                                   name="cv3ReferralAmount"
                                                   id="cv3ReferralAmount"
                                                   class="form-control input-sm"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal($cv3ReferralAmount); ?>"
                                                   onkeyup="return restrictAlphabetsLoanTerms(this)"
                                                   onblur="currencyConverter(this, this.value);"
                                                   onchange="cv3Origination.cv3OriginationAmount()">
                                            <div class="input-group-append">
                                                <span class="input-group-text">
                                                    <?php
                                                    echo loanForm::changeLog(
                                                        LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                                        'cv3ReferralAmount',
                                                        tblFileHMLONewLoanInfo::class,
                                                        'Referral Amount'
                                                    );
                                                    ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php } ?>
                            <div
                                class=" row pl-5 pl-sm-0 pl-md-0 pl-lg-0 pl-xl-0 pt-5 <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'originationPoints', 'sArr' => $secArr, 'opt' => 'D'])); ?>">
                                <label class="col-md-6 align-self-center" for="lockOriginationValue">Lock
                                    Origination Fee </label>
                                <div class="col-md-4">
                                    <div class="switch switch-sm switch-icon">
                                        <label class="font-weight-bold">
                                            <input class="form-control "
                                                <?php if (HMLOLoanTermsCalculation::$lockOriginationValue) echo 'checked'; ?>
                                                   id="lockOrigination"
                                                   type="checkbox"
                                                   onchange="toggleSwitch('lockOrigination', 'lockOriginationValue', '1', '0' );originationPoints.lockOriginationValue('lockOriginationValue');">
                                            <input type="hidden" name="lockOriginationValue"
                                                   id="lockOriginationValue"
                                                   value="<?php echo HMLOLoanTermsCalculation::$lockOriginationValue; ?>">
                                            <span></span>
                                        </label>
                                        <i class="fa fa-info-circle tooltipClass text-primary ml-2"
                                           data-html="true"
                                           title="If this box is checked, then the dollar amount for your origination fee will be locked in and only your points will auto-calculate."></i>
                                    </div>
                                </div>
                            </div>
                        <?php } else { ?>
                            <div
                                class="form-group <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'originationPoints', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                                <label class="font-weight-bold font-weight-bold col-md-6 p-lg-0 my-lg-2">
                                    <span
                                        class="">$ <?php echo Currency::formatDollarAmountWithDecimal($originationPointsValue); ?></span>
                                </label>
                            </div>
                        <?php } ?>
                    </div>
                </div>

                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'bufferandMessengerFees', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('bufferandMessengerFees', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('bufferandMessengerFees') ? 'mandatory' : ''; ?>" name="bufferAndMessengerFee"
                                       placeholder="0.00"
                                       id="bufferAndMessengerFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$bufferAndMessengerFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'bufferandMessengerFees', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$bufferAndMessengerFee) ?></label>
                        <?php } ?>
                    </div>
                </div>

                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'settlementorClosingEscrowFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('settlementorClosingEscrowFee', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('settlementorClosingEscrowFee') ? 'mandatory' : ''; ?>" name="escrowFees" id="escrowFees"
                                       placeholder="0.00"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$escrowFees) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();loanCalculation.initLTC2();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'settlementorClosingEscrowFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$escrowFees) ?></label>
                        <?php } ?>
                    </div>
                </div>

                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'closingCostFinancingFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('closingCostFinancingFee', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('closingCostFinancingFee') ? 'mandatory' : ''; ?>" name="closingCostFinancingFee"
                                       placeholder="0.00"
                                       id="closingCostFinancingFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$closingCostFinancingFee) ?>"
                                       size="20" maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'closingCostFinancingFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>
                                $ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$closingCostFinancingFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'applicationFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('applicationFee', 'col-md-6'); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('applicationFee') ? 'mandatory' : ''; ?>" name="applicationFee"
                                       placeholder="0.00"
                                       id="applicationFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$applicationFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'applicationFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label> $ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$applicationFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'estimatedTitleInsuranceFees', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('estimatedTitleInsuranceFees', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('estimatedTitleInsuranceFees') ? 'mandatory' : ''; ?>" name="estdTitleClosingFee"
                                       placeholder="0.00"
                                       id="estdTitleClosingFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$estdTitleClosingFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'estimatedTitleInsuranceFees', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>
                                $ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$estdTitleClosingFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <?php if ($isEF != 1) { ?>
                    <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'drawsSetUpFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                        <?php echo loanForm::label('drawsSetUpFee', 'col-md-6'); ?>
                        <div class="col-md-6">
                            <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('drawsSetUpFee') ? 'mandatory' : ''; ?>" name="drawsSetUpFee"
                                           placeholder="0.00"
                                           id="drawsSetUpFee"
                                           value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$drawsSetUpFee) ?>"
                                           size="20"
                                           maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                           onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'drawsSetUpFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label> $ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$drawsSetUpFee) ?></label>
                            <?php } ?>
                        </div>
                    </div>
                    <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'valuationBpo', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                        <?php echo loanForm::label('valuationBpo', 'col-md-6 '); ?>
                        <div class="col-md-6">
                            <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('valuationBpo') ? 'mandatory' : ''; ?>" name="valuationBPOFee"
                                           placeholder="0.00"
                                           id="valuationBPOFee"
                                           value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$valuationBPOFee) ?>"
                                           size="20"
                                           maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                           onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'valuationBpo', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$valuationBPOFee) ?></label>
                            <?php } ?>
                        </div>
                    </div>
                <?php } ?>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'valuationAVE', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('valuationAVE', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('valuationAVE') ? 'mandatory' : ''; ?>" name="valuationAVEFee"
                                       placeholder="0.00"
                                       id="valuationAVEFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$valuationAVEFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'valuationAVE', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$valuationAVEFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'creditReport', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('creditReport', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('creditReport') ? 'mandatory' : ''; ?>" name="creditReportFee"
                                       placeholder="0.00"
                                       id="creditReportFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$creditReportFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'creditReport', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$creditReportFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'taxService', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('taxService', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('taxService') ? 'mandatory' : ''; ?>" name="taxServiceFee" id="taxServiceFee"
                                       placeholder="0.00"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$taxServiceFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'taxService', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$taxServiceFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'wireFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('wireFee', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('wireFee') ? 'mandatory' : ''; ?>" name="wireFee" id="wireFee"
                                       placeholder="0.00"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$wireFee) ?>" size="20"
                                       maxlength="68"
                                       TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'wireFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$wireFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <?php if ($isEF != 1) { ?>
                    <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'floodCertificate', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                        <?php echo loanForm::label('floodCertificate', 'col-md-6 '); ?>
                        <div class="col-md-6">
                            <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('floodCertificate') ? 'mandatory' : ''; ?>" name="floodCertificateFee"
                                           placeholder="0.00"
                                           id="floodCertificateFee"
                                           value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$floodCertificateFee) ?>"
                                           size="20" maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>"
                                           autocomplete="off"
                                           onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'floodCertificate', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$floodCertificateFee) ?></label>
                            <?php } ?>
                        </div>
                    </div>
                <?php } ?>
                <div class="form-group row <?php echo loanForm::showField('inspectionFees'); ?>">
                    <?php echo loanForm::label('inspectionFees', 'col-md-6'); ?>
                    <div class="col-md-6">
                        <?php echo loanForm::currency(
                            'inspectionFees',
                            LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient,
                            LoanInfo::$tabIndex++,
                            LoanInfo::$inspectionFees,
                            'input-sm',
                            'updateLoanDetail();'
                        ); ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'dueDiligence', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('dueDiligence', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('dueDiligence') ? 'mandatory' : ''; ?>" name="dueDiligence" id="dueDiligence"
                                       placeholder="0.00"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$dueDiligence) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'dueDiligence', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$dueDiligence) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'lenderCredittoOffset3rdPartyFees', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('lenderCredittoOffset3rdPartyFees', 'col-md-6 ',); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('lenderCredittoOffset3rdPartyFees') ? 'mandatory' : ''; ?>" name="thirdPartyFees"
                                       placeholder="0.00"
                                       id="thirdPartyFees"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$thirdPartyFees) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'lenderCredittoOffset3rdPartyFees', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$thirdPartyFees) ?></label>
                        <?php } ?>
                    </div>
                </div>

                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'underwritingFees', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('underwritingFees', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('underwritingFees') ? 'mandatory' : ''; ?>" name="underwritingFees"
                                       placeholder="0.00"
                                       id="underwritingFees"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$underwritingFees) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'underwritingFees', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$underwritingFees) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'prepaidInterest', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('prepaidInterest', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('prepaidInterest') ? 'mandatory' : ''; ?>" name="prePaidInterest"
                                       placeholder="0.00"
                                       id="prePaidInterest"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$prePaidInterest) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'prepaidInterest', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$prePaidInterest) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'insurancePremium', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('insurancePremium', 'col-md-6 ',); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('insurancePremium') ? 'mandatory' : ''; ?>" name="insurancePremium"
                                       placeholder="0.00"
                                       id="insurancePremium"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$insurancePremium) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'insurancePremium', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$insurancePremium) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'wireTransferFeetoTitle', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('wireTransferFeetoTitle', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('wireTransferFeetoTitle') ? 'mandatory' : ''; ?>" name="wireTransferFeeToTitle"
                                       placeholder="0.00"
                                       id="wireTransferFeeToTitle"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$wireTransferFeeToTitle) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'wireTransferFeetoTitle', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$wireTransferFeeToTitle) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'pastDuePropertyTaxesFC', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('pastDuePropertyTaxesFC', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('pastDuePropertyTaxesFC') ? 'mandatory' : ''; ?>" name="pastDuePropertyTaxes"
                                       placeholder="0.00"
                                       id="pastDuePropertyTaxes"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$pastDuePropertyTaxes) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'pastDuePropertyTaxesFC', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$pastDuePropertyTaxes) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'wholesaleAdminFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('wholesaleAdminFee', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('wholesaleAdminFee') ? 'mandatory' : ''; ?>" name="wholeSaleAdminFee"
                                       placeholder="0.00"
                                       id="wholeSaleAdminFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$wholeSaleAdminFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'wholesaleAdminFee', 'sArr' => $secArr, 'opt' => 'I'])); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$wholeSaleAdminFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'employmentVerificationFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('employmentVerificationFee', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('employmentVerificationFee') ? 'mandatory' : ''; ?>" name="employmentVerificationFee"
                                       placeholder="0.00"
                                       id="employmentVerificationFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$employmentVerificationFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'employmentVerificationFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$employmentVerificationFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'taxReturnOrderFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('taxReturnOrderFee', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('taxReturnOrderFee') ? 'mandatory' : ''; ?>" name="taxReturnOrderFee"
                                       placeholder="0.00"
                                       id="taxReturnOrderFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$taxReturnOrderFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'taxReturnOrderFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$taxReturnOrderFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'taximpounds', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                        <div class="col-md-6">
                            <div class="row">
                                <label class="col-md-12 font-weight-bold">
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'taximpounds', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                </label>
                                <div class=" col-md-12">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">Months</span>
                                        </div>
                                        <input type="number" class="form-control input-sm <?php echo loanForm::isMandatory('taximpounds') ? 'mandatory' : ''; ?>" name="taxImpoundsMonth"
                                               id="taxImpoundsMonth" value="<?php echo LoanInfo::$taxImpoundsMonth; ?>"
                                               onblur="calculateTaxImpoundsFee('loanModForm', 'taxImpoundsFee');"
                                               TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"/>
                                        <div class="input-group-append">
                                            <span class="input-group-text">$ </span>
                                        </div>
                                        <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('taximpounds') ? 'mandatory' : ''; ?>" name="taxImpoundsMonthAmt"
                                               id="taxImpoundsMonthAmt"
                                               placeholder="0.00"
                                               value="<?php echo LoanInfo::$taxImpoundsMonthAmt; ?>"
                                               onblur="currencyConverter(this, this.value);calculateTaxImpoundsFee('loanModForm', 'taxImpoundsFee');"
                                               TABINDEX="<?php echo $tabIndex++; ?>"
                                               autocomplete="off"/>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('taximpounds') ? 'mandatory' : ''; ?>" TABINDEX="<?php echo $tabIndex++; ?>"
                                       placeholder="0.00"
                                       name="taxImpoundsFee"
                                       id="taxImpoundsFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$taxImpoundsFee) ?>"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();">
                            </div>
                        </div>
                    <?php } else { ?>
                        <span
                            class="H5"><?php echo LoanInfo::$taxImpoundsMonth; ?></span>months @ $ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$taxImpoundsMonthAmt); ?>
                        <span class="H5"><?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$taxImpoundsFee) ?></span>
                    <?php } ?>
                </div>
            </div>


            <div class="mt-5 mt-sm-0 mt-md-0 mt-lg-0 mt-xl-0 col-md-6">
                <div class="row">
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) {
                            ?>
                            <div
                                class="form-group <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'brokerPoints', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                                <div class="row mb-2">
                                    <?php echo loanForm::label(
                                        'brokerPoints',
                                        'col-md-6 font-weight-bold  my-lg-2',
                                        '',
                                        '@' . loanForm::changeLog(
                                            LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                            'brokerPointsRate',
                                            tblFileHMLONewLoanInfo::class,
                                            'Broker Points Rate'
                                        )); ?>
                                    <div class="col-md-6 p-lg-0">
                                        <div class="input-group">
                                            <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('brokerPoints') ? 'mandatory' : ''; ?>"
                                                   placeholder="0.00"
                                                   name="brokerPointsRate" id="brokerPointsRate"
                                                   value="<?php echo rtrim(LoanInfo::$brokerPointsRate, 0); ?>"
                                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                                   autocomplete="off"
                                                   onkeyup="return restrictAlphabetsLoanTermsDecimal(this,3)"
                                                   onblur="updateOriginationBrokerPoints('Broker');" <?php if ((LoanInfo::$brokerPointsRate > 0 && $fldEditOpt == 2) || HMLOLoanTermsCalculation::$lockBrokerValue) echo ' readonly '; ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'brokerPoints', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                            <div class="input-group-append">
                                              <span class="input-group-text">
                                                Points
                                              </span>
                                            </div>
                                        </div>
                                    </div>
                                    <input type="hidden" name="broker_based_on_total_loan_amt"
                                           id="broker_based_on_total_loan_amt"
                                           value="<?php echo $broker_based_on_total_loan_amt; ?>">
                                </div>
                            </div>
                            <div class="form-group <?php echo BaseHTML::fieldAccess(['fNm' => 'broker_total_loan_amt_checked', 'sArr' => $secArr, 'opt' => 'D']); ?>">
                                <div class="row pl-5 pl-sm-0 pl-md-0 pl-lg-0 pl-xl-0 pt-5 ">
                                    <div class="checkbox-inline">
                                        <label
                                                class="checkbox checkbox-outline <?php if (HMLOLoanTermsCalculation::$lockBrokerValue) echo ' checkbox-disabled '; ?>">
                                            <input type="checkbox" name="broker_total_loan_amt_checked"
                                                   id="broker_total_loan_amt_checked"
                                                    <?php if (HMLOLoanTermsCalculation::$lockBrokerValue) echo ' disabled '; ?>
                                                   onclick="updateOriginationBrokerPoints('Broker');" <?php if (HMLOLoanTermsCalculation::$broker_based_on_total_loan_amt > 0) echo 'checked'; ?>
                                                   value="1"><span></span>
                                            Include Closing Cost Financed
                                        </label>
                                        <i class="fa fa-info-circle tooltipClass text-primary" data-html="true"
                                           title="<em>By default the closing costs financed amount is not included in the calculation of points. Check the box to include that amount when calculating total points</em>"></i>

                                    </div>
                                </div>
                            </div>
                        <?php } else { ?>
                            <div
                                class="form-group <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'brokerPoints', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                                <?php echo loanForm::label('brokerPoints', 'font-weight-bold  my-lg-2', '', '@'); ?>
                                <label class="font-weight-bold ml-10">
                                    <span
                                        class=""><?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$brokerPointsRate) ?></span>
                                    <span>Points</span>
                                </label>
                            </div>
                        <?php } ?>
                    </div>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="row mb-2">
                                <div
                                    class=" <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'brokerPoints', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('brokerPoints') ? 'mandatory' : ''; ?>"
                                               placeholder="0.00"
                                               tabindex="<?php echo $tabIndex++; ?>"
                                               name="brokerPointsValue"
                                               id="brokerPointsValue"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$brokerPointsValue) ?>"
                                               onkeyup="return restrictAlphabetsLoanTerms(this)"
                                               onblur="currencyConverter(this, this.value);calculateBrokerPointsRate();"
                                            <?php if ((LoanInfo::$brokerPointsRate > 0 && $fldEditOpt == 2) || HMLOLoanTermsCalculation::$lockBrokerValue) echo ' readonly '; ?> >
                                        <div class="input-group-append">
                                             <span class="input-group-text">
                                                 <?php
                                                 echo loanForm::changeLog(
                                                     LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                                     'brokerPointsValue',
                                                     tblFileHMLONewLoanInfo::class,
                                                     'Broker Points Value'
                                                 );
                                                 ?>
                                             </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div
                                class=" row pl-5 pl-sm-0 pl-md-0 pl-lg-0 pl-xl-0 pt-5 <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'brokerPoints', 'sArr' => $secArr, 'opt' => 'D'])); ?>">
                                <label class="col-md-6 align-self-center">Lock Broker Fee</label>
                                <div class="col-md-4">
                                    <div class="switch switch-sm switch-icon">
                                        <label class="font-weight-bold">
                                            <input class="form-control "
                                                <?php if (HMLOLoanTermsCalculation::$lockBrokerValue) echo 'checked'; ?>
                                                   id="lockBroker"
                                                   type="checkbox"
                                                   onchange="toggleSwitch('lockBroker', 'lockBrokerValue', '1', '0' );originationPoints.lockBrokerValue('lockBrokerValue');">
                                            <input type="hidden"
                                                   name="lockBrokerValue"
                                                   id="lockBrokerValue"
                                                   value="<?php echo HMLOLoanTermsCalculation::$lockBrokerValue; ?>">
                                            <span></span>
                                        </label>
                                        <i class="fa fa-info-circle tooltipClass text-primary ml-2"
                                           data-html="true"
                                           title="If this box is checked, then the dollar amount for your broker fee will be locked in and only your points will auto-calculate."></i>
                                    </div>
                                </div>
                            </div>

                        <?php } else { ?>
                            <div
                                class="form-group <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'brokerPoints', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                                <label class="font-weight-bold">
                                    <span
                                        class="">$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$brokerPointsValue) ?></span>
                                </label>
                            </div>
                        <?php } ?>
                    </div>
                </div>

                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'travelNotaryFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('travelNotaryFee', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('travelNotaryFee') ? 'mandatory' : ''; ?>" name="travelNotaryFee"
                                       placeholder="0.00"
                                       id="travelNotaryFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$travelNotaryFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'travelNotaryFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$travelNotaryFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'attorneyFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('attorneyFee', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('attorneyFee') ? 'mandatory' : ''; ?>" name="attorneyFee" id="attorneyFee"
                                       placeholder="0.00"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$attorneyFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'attorneyFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label> $ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$attorneyFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'appraisalFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('appraisalFee', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('appraisalFee') ? 'mandatory' : ''; ?>" name="appraisalFee" id="appraisalFee"
                                       placeholder="0.00"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$appraisalFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'appraisalFee', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                            </div>
                        <?php } else { ?>
                            <label> $ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$appraisalFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'processingFees', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('processingFees', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('processingFees') ? 'mandatory' : ''; ?>" name="processingFee" id="processingFee"
                                       placeholder="0.00"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$processingFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'processingFees', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                            </div>
                        <?php } else { ?>
                            <label> $ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$processingFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                
                <div class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'brokerProcessingFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('brokerProcessingFee', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('brokerProcessingFee') ? 'mandatory' : ''; ?>"
                                       name="brokerProcessingFee"
                                       id="brokerProcessingFee"
                                       placeholder="0.00"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal($fileHMLONewLoanInfoObject->brokerProcessingFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'brokerProcessingFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal($fileHMLONewLoanInfoObject->brokerProcessingFee) ?></label>
                        <?php } ?>
                    </div>
                </div>


                <?php if ($isEF != 1) { ?>
                    <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'drawsFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                        <?php echo loanForm::label('drawsFee', 'col-md-6  '); ?>
                        <div class="col-md-6">
                            <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('drawsFee') ? 'mandatory' : ''; ?>" name="drawsFee" id="drawsFee"
                                           placeholder="0.00"
                                           value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$drawsFee) ?>"
                                           size="20"
                                           maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                           onblur="currencyConverter(this, this.value);"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'drawsFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$drawsFee) ?></label>
                            <?php } ?>
                        </div>
                    </div>
                <?php } ?>




                <?php if ($isEF != 1) { ?>

                    <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'valuationAVM', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                        <?php echo loanForm::label('valuationAVM', 'col-md-6  '); ?>
                        <div class="col-md-6">
                            <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('valuationAVM') ? 'mandatory' : ''; ?>" name="valuationAVMFee"
                                           placeholder="0.00"
                                           id="valuationAVMFee"
                                           value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$valuationAVMFee) ?>"
                                           size="20"
                                           maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                           onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'valuationAVM', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$valuationAVMFee) ?></label>
                            <?php } ?>
                        </div>
                    </div>
                <?php } ?>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'valuationCMA', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('valuationCMA', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('valuationCMA') ? 'mandatory' : ''; ?>" name="valuationCMAFee"
                                       placeholder="0.00"
                                       id="valuationCMAFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$valuationCMAFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'valuationCMA', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$valuationCMAFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'creditCheckFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('creditCheckFee', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('creditCheckFee') ? 'mandatory' : ''; ?>" name="creditCheckFee"
                                       placeholder="0.00"
                                       id="creditCheckFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$creditCheckFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'creditCheckFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$creditCheckFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'backgroundCheck', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('backgroundCheck', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('backgroundCheck') ? 'mandatory' : ''; ?>" name="backgroundCheckFee"
                                       placeholder="0.00"
                                       id="backgroundCheckFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$backgroundCheckFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'backgroundCheck', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$backgroundCheckFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'documentPreparation', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('documentPreparation', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('documentPreparation') ? 'mandatory' : ''; ?>" name="documentPreparationFee"
                                       placeholder="0.00"
                                       id="documentPreparationFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$documentPreparationFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'documentPreparation', 'sArr' => $secArr, 'opt' => 'L']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$documentPreparationFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <?php if ($isEF != 1) { ?>
                    <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'servicingSetUpFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                        <?php echo loanForm::label('servicingSetUpFee', 'col-md-6  '); ?>
                        <div class="col-md-6">
                            <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('servicingSetUpFee') ? 'mandatory' : ''; ?>" name="servicingSetUpFee"
                                           placeholder="0.00"
                                           id="servicingSetUpFee"
                                           value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$servicingSetUpFee) ?>"
                                           size="20"
                                           maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                           onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'servicingSetUpFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$servicingSetUpFee) ?></label>
                            <?php } ?>
                        </div>
                    </div>
                    <div
                        class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'floodService', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                        <?php echo loanForm::label('floodService', 'col-md-6  '); ?>
                        <div class="col-md-6">
                            <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('floodService') ? 'mandatory' : ''; ?>" name="floodServiceFee"
                                           placeholder="0.00"
                                           id="floodServiceFee"
                                           value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$floodServiceFee) ?>"
                                           size="20"
                                           maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                           onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'floodService', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                </div>
                            <?php } else { ?>
                                <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$floodServiceFee) ?></label>
                            <?php } ?>
                        </div>
                    </div>
                <?php } ?>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'projectFeasibility', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('projectFeasibility', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('projectFeasibility') ? 'mandatory' : ''; ?>" name="projectFeasibility"
                                       placeholder="0.00"
                                       id="projectFeasibility"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$projectFeasibility) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'projectFeasibility', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$projectFeasibility) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'uccLienSearch', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('uccLienSearch', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('uccLienSearch') ? 'mandatory' : ''; ?>" name="UccLienSearch" id="UccLienSearch"
                                       placeholder="0.00"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$UccLienSearch) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'uccLienSearch', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$UccLienSearch) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'other', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <label class="col-md-6"
                           for="otherFee"><?php echo BaseHTML::fieldAccess(['fNm' => 'other', 'sArr' => $secArr, 'opt' => 'L']); ?></label>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('other') ? 'mandatory' : ''; ?>" name="otherFee" id="otherFee"
                                       placeholder="0.00"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$otherFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'other', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$otherFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'recordingFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('recordingFee', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('recordingFee') ? 'mandatory' : ''; ?>" name="recordingFee" id="recordingFee"
                                       placeholder="0.00"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$recordingFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'recordingFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$recordingFee) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'propertytax', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('propertytax', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('propertytax') ? 'mandatory' : ''; ?>" name="propertyTax" id="propertyTax"
                                       placeholder="0.00"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$propertyTax) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'propertytax', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$propertyTax) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'realEstateTaxes', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('realEstateTaxes', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('realEstateTaxes') ? 'mandatory' : ''; ?>" name="realEstateTaxes"
                                       placeholder="0.00"
                                       id="realEstateTaxes"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$realEstateTaxes) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'realEstateTaxes', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$realEstateTaxes) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'payOffLiensCreditors', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('payOffLiensCreditors', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('payOffLiensCreditors') ? 'mandatory' : ''; ?>" name="payOffLiensCreditors"
                                       placeholder="0.00"
                                       id="payOffLiensCreditors"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$payOffLiensCreditors) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'payOffLiensCreditors', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$payOffLiensCreditors) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'wireTransferFeetoEscrow', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('wireTransferFeetoEscrow', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('wireTransferFeetoEscrow') ? 'mandatory' : ''; ?>" name="wireTransferFeeToEscrow"
                                       placeholder="0.00"
                                       id="wireTransferFeeToEscrow"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$wireTransferFeeToEscrow) ?>"
                                       size="20" maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'wireTransferFeetoEscrow', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$wireTransferFeeToEscrow) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'survey', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('survey', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('survey') ? 'mandatory' : ''; ?>" name="survey" id="survey"
                                       placeholder="0.00"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$survey) ?>" size="20"
                                       maxlength="68"
                                       TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'survey', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$survey) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'cityCountyTaxStamps', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('cityCountyTaxStamps', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('cityCountyTaxStamps') ? 'mandatory' : ''; ?>" name="cityCountyTaxStamps"
                                       placeholder="0.00"
                                       id="cityCountyTaxStamps"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$cityCountyTaxStamps) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'cityCountyTaxStamps', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$cityCountyTaxStamps) ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'constructionHoldbackFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('constructionHoldbackFee', 'col-md-6  '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('constructionHoldbackFee') ? 'mandatory' : ''; ?>" name="constructionHoldbackFee"
                                       placeholder="0.00"
                                       id="constructionHoldbackFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$constructionHoldbackFee) ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'constructionHoldbackFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$constructionHoldbackFee) ?></label>
                        <?php } ?>
                    </div>
                </div>

                <div class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'sellerCreditsFee', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php echo loanForm::label('sellerCreditsFee', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('sellerCreditsFee') ? 'mandatory' : ''; ?>" name="sellerCreditsFee"
                                       placeholder="0.00"
                                       id="sellerCreditsFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$sellerCreditsFee); ?>"
                                       size="20"
                                       maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'sellerCreditsFee', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                            </div>
                        <?php } else { ?>
                            <label>$ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$sellerCreditsFee); ?></label>
                        <?php } ?>
                    </div>
                </div>

                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'insimpounds', 'sArr' => $secArr, 'opt' => 'D'])); ?> ">
                    <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) {?>
                        <div class="col-md-6">
                            <div class="row">
                                <label class="col-md-12 font-weight-bold">
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'insimpounds', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                </label>
                                <div class="col-md-12">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">Months</span>
                                        </div>
                                        <input type="number"
                                               class="form-control input-sm <?php echo loanForm::isMandatory('insimpounds') ? 'mandatory' : ''; ?>"
                                               name="insImpoundsMonth"
                                               id="insImpoundsMonth"
                                               value="<?php echo LoanInfo::$insImpoundsMonth; ?>"
                                               TABINDEX="<?php echo $tabIndex++; ?>"
                                               autocomplete="off"
                                               onblur="calculateInsImpoundsFee('loanModForm', 'insImpoundsFee');"/>
                                        <div class="input-group-append">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text"
                                               class="form-control input-sm <?php echo loanForm::isMandatory('insimpounds') ? 'mandatory' : ''; ?>"
                                               name="insImpoundsMonthAmt"
                                               placeholder="0.00"
                                               id="insImpoundsMonthAmt"
                                               value="<?php echo LoanInfo::$insImpoundsMonthAmt; ?>"
                                               onblur="currencyConverter(this, this.value);calculateInsImpoundsFee('loanModForm', 'insImpoundsFee');"
                                               TABINDEX="<?php echo $tabIndex++; ?>"
                                               autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control input-sm <?php echo loanForm::isMandatory('insimpounds') ? 'mandatory' : ''; ?>" TABINDEX="<?php echo $tabIndex++; ?>"
                                       placeholder="0.00"
                                       name="insImpoundsFee"
                                       id="insImpoundsFee"
                                       value="<?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$insImpoundsFee) ?>"
                                       onblur="currencyConverter(this, this.value);updateLoanDetail();">
                            </div>
                        </div>
                    <?php } else { ?>
                        <span
                            class="H5"><?php echo $insImpoundsMonth; ?></span>months @ $ <?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$insImpoundsMonthAmt); ?>
                        <span class="H5"><?php echo Currency::formatDollarAmountWithDecimal(LoanInfo::$insImpoundsFee) ?></span>
                    <?php } ?>
                </div>
            </div>

            <div
                class="col-md-12 separator separator-dashed my-2 separator12 interestChargedFrom_disp <?php echo $denimSeperator; ?>"></div>
            <div class="col-md-12">
                <div class="form-group row">
                    <div class="col-4 <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'perDiemAccrualType', 'sArr' => $secArr, 'opt' => 'D'])); ?>">
                        <?php echo loanForm::label('perDiemAccrualType', 'col-md-6  '); ?>
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <select name="perDiemAccrualType"
                                    class="form-control input-sm accrualTypeClass <?php echo loanForm::isMandatory('perDiemAccrualType') ? 'mandatory' : ''; ?>"
                                    id="perDiemAccrualType"
                                    onchange="loanCalculation.updateAccrualType(this);updateLoanDetail();"
                                    tabindex="<?php echo $tabIndex++; ?>">
                                <option value="">Use Loan Accrual Type</option>
                                <?php foreach (accrualTypes::$accrualTypes as $k => $v) { ?>
                                    <option value="<?php echo $k; ?>" <?php if (LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->perDiemAccrualType == $k) echo ' selected '; ?> >
                                        <?php echo $v; ?>
                                    </option>
                                <?php } ?>
                            </select>
                        <?php }  else {
                            echo '<h7>' . (accrualTypes::$accrualTypes[LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->perDiemAccrualType] ?? '') . '</h7>';
                        } ?>
                    </div>
                    <div class="col-md-4 <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'interestChargedFrom', 'sArr' => $secArr, 'opt' => 'D'])); ?>">
                        <?php echo loanForm::label('interestChargedFrom', 'col-md-6  '); ?>
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <input type="date"
                                   class="form-control input-sm <?php echo loanForm::isMandatory('interestChargedFrom') ? 'mandatory' : ''; ?>"
                                   name="interestChargedFromDate"
                                   id="interestChargedFromDate"
                                   onchange="updateLoanDetail();"
                                   value="<?php echo Dates::Datestamp(LoanInfo::$interestChargedFromDate); ?>"
                                   size="10"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off">
                        <?php } else { ?>
                            <label><?php echo Dates::StandardDate(LoanInfo::$interestChargedFromDate); ?></label>
                        <?php } ?>
                    </div>
                    <div
                        class="col-md-4 <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'interestChargedEnd', 'sArr' => $secArr, 'opt' => 'D'])); ?>">
                        <?php echo loanForm::label('interestChargedEnd', 'col-md-6  ', '', '(' . (PageVariables::$nonInclusivePerDiem ? 'Non-Inclusive' : 'Inclusive') . ')'); ?>
                        <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                            <input type="date"
                                   class="form-control input-sm <?php echo loanForm::isMandatory('interestChargedEnd') ? 'mandatory' : ''; ?>"
                                   name="interestChargedEndDate"
                                   id="interestChargedEndDate"
                                   onchange="updateLoanDetail();"
                                   value="<?php echo Dates::Datestamp(LoanInfo::$interestChargedEndDate); ?>"
                                   size="10"
                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                   autocomplete="off">
                        <?php } else { ?>
                            <label><?php echo Dates::StandardDate(LoanInfo::$interestChargedEndDate); ?></label>
                        <?php } ?>
                    </div>
                </div>
                <div
                    class="form-group row <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'estPerDiemInt', 'sArr' => $secArr, 'opt' => 'D'])); ?>">
                    <label
                        class="font-weight-bold col-md-12"><?php echo BaseHTML::fieldAccess(['fNm' => 'estPerDiemInt', 'sArr' => $secArr, 'opt' => 'L']); ?>
                        :
                        <u><span style="margin-left: 15px;"
                                 id="diemDays"><?php echo HMLOLoanTermsCalculation::$diemDays; ?></span></u>
                        &nbsp; &nbsp; x Per Diem Interest:&nbsp;$<u>&nbsp;<span
                                style="margin-right: 15px;display: none;"
                                id="totalDailyInterestCharge"><?php echo(HMLOLoanTermsCalculation::$totalDailyInterestCharge); ?></span>
                            <span
                                id="totalDailyInterestChargeDummy"><?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$totalDailyInterestCharge); ?></span>
                            <a id="perDiemToolTip" class="fa fa-info-circle  fa-lg tip-bottom"
                               style="text-decoration:none; display: inline;"
                               title="<?php echo HMLOLoanTermsCalculation::$perDiemToolTip; ?>"></a>
                        </u>
                        &nbsp;(@ &nbsp;<u><span id="interestRateDual"><?php echo HMLOLoanTermsCalculation::$lien1Rate; ?></span> %</u>)&nbsp;
                        =
                        Accrued Per Diem Interest: $&nbsp; <u><span
                                id="totalEstPerDiem"><?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$totalEstPerDiem); ?></span></u>
                    </label>
                </div>
            </div>
            <div
                class="col-md-12 separator separator-dashed my-2 separator12 estPerDiemInt_disp <?php echo $denimSeperator ?>"></div>


            <div class=" col-md-12">
                <div class="row form-group ">
                    <div
                        class="col-md-6 <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'totalFeesCosts', 'sArr' => $secArr, 'opt' => 'D'])); ?>">
                        <div
                            class="row ">
                            <?php echo loanForm::label('totalFeesCosts', 'col-md-6  '); ?>
                            <div class="col-md-6 font-weight-bold">
                                $ <span id="totalFeesAndCost">
                                <?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$totalFeesAndCost) ?>
                            </span>
                            </div>
                        </div>
                    </div>
                    <div
                        class="col-md-6 <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'percentageClosingCostFinanced', 'sArr' => $secArr, 'opt' => 'D'])); ?>">
                        <div
                            class="row ">
                            <?php echo loanForm::label('percentageClosingCostFinanced', 'col-md-6 '); ?>
                            <div class="col-md-6 font-weight-bold">
                            <span id="perClosingCostFinanced">
                                    <?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$perClosingCostFinanced) ?></span>
                                %
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class=" col-md-12">
                <div class="row form-group">
                    <div
                        class="col-md-6 <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'netLenderFundsToTitle', 'sArr' => $secArr, 'opt' => 'D'])); ?>">
                        <div class="row">
                            <div class="col-md-6 ">
                                <?php echo loanForm::label('netLenderFundsToTitle', '', HMLOLoanTermsCalculation::$netLenderFundsToBorrowerToolTip); ?>
                            </div>
                            <div class="col-md-6">
                                    <span id="netLenderFundsToBorrower"
                                          class="netLenderFundsToBorrower font-weight-bold ">
                                      <?php echo proposalFormula::convertToAbsoluteValueForDollar(HMLOLoanTermsCalculation::$netLenderFundsToBorrower) ?>
                                    </span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div
                            class="row  <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'feeCostTotalLoanAmount', 'sArr' => $secArr, 'opt' => 'D'])); ?>">
                            <div class="col-md-6 feeSectionTotalLoanAmt "
                                 style="<?php echo $feeSectionTotalLoanAmtOpt ?>">
                                <?php echo loanForm::label('feeCostTotalLoanAmount', ' '); ?>
                            </div>
                            <div class="col-md-6 feeSectionTotalLoanAmt"
                                 style="<?php echo $feeSectionTotalLoanAmtOpt ?>">
                                $ <span id="totalLoanAmount" class="totalLoanAmount font-weight-bold">
                                <?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$totalLoanAmount) ?>
                            </span>
                            </div>
                        </div>
                    </div>
                    <?php
                    if (glCustomJobForProcessingCompany::isCustomLenderRevenue(LMRequest::$PCID)) {
                        $inAddArray = [
                            'originationPointsValue' => LoanInfo::$originationPointsValue, // Origination Fee
                            'applicationFee' => LoanInfo::$applicationFee, // Application Fee
                            'processingFee' => LoanInfo::$processingFee, // Processing Fee
                            'underwritingFees' => LoanInfo::$underwritingFees, // Underwriting Fee
                            'bufferAndMessengerFee' => LoanInfo::$bufferAndMessengerFee, // Discount Fee
                            'drawsSetUpFee' => LoanInfo::$drawsSetUpFee, // Draw Set Up Fee
                            'drawsFee' => LoanInfo::$drawsFee, // Draw Fee
                            'valuationAVMFee' => LoanInfo::$valuationAVMFee, //Valuation – AVM
                            'wireFee' => LoanInfo::$wireFee, //Wire Fee
                            'servicingSetUpFee' => LoanInfo::$servicingSetUpFee, //Servicing Set Up Fee
                            'floodCertificateFee' => LoanInfo::$floodCertificateFee, //Flood Certificate
                            'backgroundCheckFee' => LoanInfo::$backgroundCheckFee, //Background Check
                            'creditReportFee' => LoanInfo::$creditReportFee, //Credit Report
                            'projectFeasibility' => LoanInfo::$projectFeasibility, //Project Feasibility
                            'appraisalFee' => LoanInfo::$appraisalFee, //Appraisal Fee
                        ];
                        $inSubArray = [
                            'thirdPartyFees' => LoanInfo::$thirdPartyFees, //Lender Credit to Offset 3rd Party Fees
                        ];
                        $customLenderRevenue = proposalFormula::getLenderRevenue($inAddArray, $inSubArray);
                        ?>
                        <div class="col-md-6">
                            <div class="row form-group">
                                <label class="col-md-6 font-weight-bold">
                                    <i class="fa fa-info-circle tooltipClass text-primary" data-html="true"
                                       id="LenderRevenueToolTip"
                                       title="<?php echo "Lender Revenue = Origination Value + Application Fee + Processing Fees + Underwriting Fees + Discount Fee + Draw Set Up Fee + Draws Fee+ Valuation - AVM + Wire  + Servicing Setup + Flood Certificate + Background Check + Credit Report+ Project Feasibility + Appraisal Fee - Lender Credit to Offset 3 rd Party Fees"; ?>">
                                    </i>
                                    Lender Revenue:</label>
                                <div class="col-md-6 font-weight-bold "
                                     id="customLenderRevenue"> <?php echo proposalFormula::convertToAbsoluteValueForDollar($customLenderRevenue); ?></div>
                            </div>
                        </div>
                    <?php } ?>
                </div>
            </div>
            <div class=" col-md-12">
                <div
                    class="row form-group <?php echo tdHidetoVisibilityHidden(BaseHTML::fieldAccess(['fNm' => 'lenderNotes', 'sArr' => $secArr, 'opt' => 'D'])); ?>">
                    <div class="col-md-6">
                        <div class="row">
                            <div class="col-md-6 ">
                                <?php echo loanForm::label('lenderNotes', ''); ?>
                            </div>
                            <div class="col-md-6">
                                <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                                    <input type="hidden" name="checkDisplayTermSheet" value="No">
                                    <div class="checkbox-inline">
                                        <label class="checkbox">
                                            <input type="checkbox" name="checkDisplayTermSheet"
                                                   id="checkDisplayTermSheet"
                                                   value="Yes"
                                                   tabindex="<?php echo $tabIndex++; ?>" <?php if (LoanInfo::$checkDisplayTermSheet == 'Yes') echo 'Checked'; ?>><span></span>
                                            Include notes on term sheet?
                                        </label>
                                    </div>
                                <?php } else { ?>
                                    <h5><?php echo LoanInfo::$checkDisplayTermSheet ?></h5>
                                <?php } ?>
                            </div>

                            <?php if ($publicUser != 1 && $userRole != 'Client') { ?>
                                <div class="col-md-12 ">
                                    <?php if (LMRequest::$allowToEdit && LoanInfo::$disabledInputForClient) { ?>
                                        <textarea name="lenderNotes" class="form-control validateMaxLength <?php echo loanForm::isMandatory('lenderNotes') ? 'mandatory' : ''; ?>" id="lenderNotes"
                                                  maxlength="<?php echo loanForm::getFieldLength('lenderNotes','tblFileHMLOPropInfo'); ?>"
                                                  tabindex="<?php echo $tabIndex++; ?>"><?php echo LoanInfo::$lenderNotes; ?></textarea>
                                    <?php } else { ?>
                                        <h5><?php echo LoanInfo::$lenderNotes ?></h5>
                                    <?php } ?>
                                </div>

                            <?php } ?>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <?php echo CustomField::RenderForTabSection(
            PageVariables::$PCID,
            tblFile::class,
            LMRequest::$LMRId,
            'FC',
            $fileTab,
            $activeTab,
            LMRequest::myFileInfo()->getFileTypes(),
            LMRequest::myFileInfo()->getLoanPrograms()
        ); ?>
    </div>
</div>
<?php

if (in_array('Fees & Costs', $lockedSections)) {
    //$BackupAllowToEdit = LMRequest::$allowToEdit;
    LMRequest::$allowToEdit = $BackupAllowToEdit;
}
?>
<style>
    .secHideVis {
        visibility: hidden;
    }
</style>
<script>
    $(function () {
        if ($('#originationPointsRate').val() != '' && ($('#originationPointsValue').val() == '' || $('#originationPointsValue').val() == null)) {
            calculateOriginationPointsValue();
        }
        if ($('#brokerPointsRate').val() != '' && ($('#brokerPointsValue').val() == '' || $('#brokerPointsValue').val() == null)) {
            calculateBrokerPointsValue();
        }

        loanCalculation.nonInclusivePerDiem = <?php echo PageVariables::$nonInclusivePerDiem ? 'true' : 'false'; ?>
    });
</script>
<?php
Strings::includeMyScript(['/backoffice/LMRequest/js/feesAndCost.js']);
?>
<!-- HMLOFeesAndCostsSection.php -->

