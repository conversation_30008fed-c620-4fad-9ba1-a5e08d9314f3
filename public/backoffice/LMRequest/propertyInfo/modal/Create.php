<?php

use models\constants\gl\glCountryArray;
use models\constants\GpropertyTypeNumbArray;
use models\Controllers\backoffice\LMRequest;
use models\standard\Arrays;


?>
<div class="modal fade" id="create_property" tabindex="-1" role="dialog" aria-hidden="true"
     aria-labelledby="joinModalLabel">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Property</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body " id="propertyFields">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="font-weight-bold" for="isPrimary">Is this the main subject property?</label>
                            <div class="switch switch-icon">
                                <label class="font-weight-bold">
                                    <input type="checkbox"
                                           id="isPrimary"
                                           name="isPrimary"
                                           class="form-control"
                                           value="1"
                                           onchange="Property.checkIsPrimaryProperty(this);">
                                    <span></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="font-weight-bold" for="propertyType">Type</label>
                            <select class="form-control chzn-select"
                                    name="propertyType"
                                    id="propertyType"
                                    data-placeholder="Select Type">
                                <option value=""></option>
                                <?php foreach (GpropertyTypeNumbArray::$globalPropertyTypeGroup as $groupName => $groupValues) { ?>
                                    <optgroup label="<?php echo $groupName; ?>">
                                        <?php foreach ($groupValues as $propertyType => $propertyTypeValue) { ?>
                                            <option value="<?php echo trim($propertyType); ?>"
                                                    class="<?php echo (sizeof(LMRequest::$propertyTypeKeys ?? []) > 0) ?
                                                        (in_array($propertyType, LMRequest::$propertyTypeKeys) ? '' : 'd-none') : ''; ?>">
                                                <?php echo $propertyTypeValue; ?></option>
                                        <?php } ?>
                                    </optgroup>
                                <?php } ?>
                            </select>
                        </div>
                    </div>


                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="font-weight-bold" for="propertyAddress">Address</label>
                            <input type="text" class="form-control" name="propertyAddress" id="propertyAddress"
                                   placeholder="Enter Address"/>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="font-weight-bold" for="propertyUnit">Unit#</label>
                            <input type="text" class="form-control" name="propertyUnit" id="propertyUnit"
                                   placeholder="Enter Unit#"/>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="font-weight-bold" for="propertyCity">City</label>
                            <input type="text" class="form-control" name="propertyCity" id="propertyCity"
                                   placeholder="Enter City"/>
                        </div>
                    </div>
                    <div class="col-md-6 ">
                        <div class="form-group ">
                            <label class="font-weight-bold" for="propertyState">State</label>
                            <select class="form-control"
                                    data-placeholder="Select State"
                                    name="propertyState"
                                    onchange="getCountyByStateCode('countyClass', this.value, 'propertyCounty');"
                                    id="propertyState">
                                <option value="">- Select -</option>
                                <?php
                                foreach (Arrays::fetchStates() ?? [] as $eachState) { ?>
                                    <option value="<?php echo $eachState['stateCode']; ?>"
                                            class="<?php echo (sizeof(LMRequest::$customLoanGuidelinesStateKeys ?? []) > 0) ? (in_array($eachState['stateCode'], LMRequest::$customLoanGuidelinesStateKeys) ? '' : 'd-none') : ''; ?>">
                                        <?php echo $eachState['stateName']; ?>
                                    </option>
                                    <?php
                                } ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6 countyClass">
                        <div class="form-group">
                            <label class="font-weight-bold" for="propertyCounty">County</label>
                            <select class="form-control chzn-select"
                                    name="propertyCounty"
                                    id="propertyCounty"
                                    data-placeholder="Select County"
                                    onclick="checkCountyData('loanModForm', 'propertyCounty');">
                                <option value=''></option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="font-weight-bold" for="propertyZipCode">Zip Code</label>
                            <input type="text" class="form-control zipCode" name="propertyZipCode" id="propertyZipCode"
                                   placeholder="Enter Zip Code"/>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="font-weight-bold" for="propertyEstimatedValue">Property Estimated
                                Value</label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control "
                                       name="propertyEstimatedValue"
                                       id="propertyEstimatedValue"
                                       placeholder="0.00"
                                       onblur="currencyConverter(this, this.value);"
                                />
                            </div>
                        </div>
                    </div>


                    <div class="col-md-6 d-none">
                        <div class="form-group">
                            <label class="font-weight-bold" for="propertyCountry">Country</label>
                            <select class="form-control input-sm chzn-select"
                                    name="propertyCountry"
                                    data-placeholder="Select Country"
                                    disabled
                                    id="propertyCountry">
                                <option value=''></option>
                                <?php
                                foreach (glCountryArray::$glCountryArray as $propertyCountryName => $propertyCountryCode) { ?>
                                    <option value="<?php echo $propertyCountryCode; ?>"><?php echo trim($propertyCountryName); ?></option>
                                <?php }
                                ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6 d-none">
                        <div class="form-group">
                            <label class="font-weight-bold">Location</label>
                            <div class="radio-inline">
                                <label class="radio radio-solid "
                                       for="propertylocationUrban">
                                    <input type="radio"
                                           disabled
                                           name="propertyLocation"
                                           id="propertylocationUrban"
                                           value="Urban"/>
                                    <span></span>
                                    Urban
                                </label>
                                <label class="radio radio-solid "
                                       for="propertylocationRural">
                                    <input type="radio"
                                           disabled
                                           name="propertyLocation"
                                           id="propertylocationRural"
                                           value="Rural"/>
                                    <span></span>
                                    Rural
                                </label>
                                <label class="radio radio-solid "
                                       for="propertylocationSuburban">
                                    <input type="radio"
                                           disabled
                                           name="propertyLocation"
                                           id="propertylocationSuburban"
                                           value="Suburban"/>
                                    <span></span>
                                    Suburban
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 d-none">
                        <div class="form-group">
                            <label class="font-weight-bold" for="propertyMunicipality">Municipality</label>
                            <input type="text" class="form-control" name="propertyMunicipality"
                                   id="propertyMunicipality"
                                   disabled
                                   placeholder="Enter Municipality"/>
                        </div>
                    </div>
                    <div class="col-md-6 d-none">
                        <div class="form-group">
                            <label class="font-weight-bold" for="propertyParcelNumber">Parcel Number</label>
                            <input type="text"
                                   disabled
                                   class="form-control"
                                   name="propertyParcelNumber"
                                   id="propertyParcelNumber"
                                   placeholder="Enter Parcel Number"/>
                        </div>
                    </div>
                    <div class="col-md-6 d-none">
                        <div class="form-group">
                            <label class="font-weight-bold" for="propertyLegalDescription">Legal Description</label>
                            <textarea class="form-control input-sm"
                                      placeholder="Enter Legal Description"
                                      name="propertyLegalDescription"
                                      disabled
                                      id="propertyLegalDescription"></textarea>
                        </div>
                    </div>
                    <div class="col-md-6 d-none">
                        <div class="form-group">
                            <label class="font-weight-bold" for="propertyDistrict">District</label>
                            <input type="text"
                                   disabled
                                   class="form-control"
                                   name="propertyDistrict"
                                   id="propertyDistrict"
                                   placeholder="Enter Parcel Number"/>
                        </div>
                    </div>
                    <div class="col-md-6 d-none">
                        <div class="form-group">
                            <label class="font-weight-bold" for="propertySection">Section</label>
                            <textarea disabled class="form-control input-sm"
                                      placeholder="Enter Section"
                                      name="propertySection"
                                      id="propertySection"></textarea>
                        </div>
                    </div>
                    <div class="col-md-6 d-none">
                        <div class="form-group">
                            <label class="font-weight-bold" for="block">Block</label>
                            <input type="text" disabled class="form-control" name="propertyBlock" id="propertyBlock"
                                   placeholder="Enter Block"/>
                        </div>
                    </div>
                    <div class="col-md-6 d-none">
                        <div class="form-group">
                            <label class="font-weight-bold" for="propertyLot">Lot</label>
                            <input type="text" disabled class="form-control" name="propertyLot" id="propertyLot"
                                   placeholder="Enter Lot"/>
                        </div>
                    </div>
                    <div class="col-md-6 d-none">
                        <div class="form-group">
                            <label class="font-weight-bold" for="propertyMSA">MSA</label>
                            <input type="text" disabled class="form-control" name="propertyMSA" id="propertyMSA"
                                   placeholder="Enter MSA"/>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" onclick="Property.clear();" class="btn btn-light-primary font-weight-bold"
                        data-dismiss="modal">Close
                </button>
                <button type="button" class="btn btn-primary" onclick="Property.create();">Save</button>
            </div>
        </div>
    </div>
</div>
