<?php

use models\constants\creditDecision;
use models\constants\gl\glDate;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\creditDecision as creditDecisionController;
use models\Controllers\loanForm;
use models\lendingwise\tblCreditDecisionForm;
use models\standard\Arrays;
use models\standard\Dates;

$LMRId = LMRequest:: $LMRId;

$secondSignReasonArray = creditDecision::secondSignReason();
$secondSignDecisionArray = creditDecision::decisionArray();
$boStaffArray = creditDecision::getBOEmployeeAssignedToFile('CD', $LMRId);

$creditDecisionFormData = creditDecisionController::$creditDecisionFormData;
if(!$creditDecisionFormData) {
    $creditDecisionFormData = [
        new tblCreditDecisionForm(),
    ];
}
?>
<div class="card card-custom secondSign">
    <div class="card-header card-header-tabs-line bg-gray-100">
        <div class="card-title">
            <h3 class="card-label">
                Second Sign
            </h3>
        </div>
        <div class="card-toolbar">
            <span class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer"
                  data-card-tool="toggle"
                  data-section="secondSign"
                  data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Section">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>
        </div>
    </div>
    <div class="card-body secondSign_body">
        <div class="form-group row">
            <div class="col-lg-3">
                <label class="font-weight-bold" for="secondSignDate">Request for Second Sign Date</label>
                <div class="input-group">
                    <div class="input-group-prepend actionDate">
                        <span class="input-group-text">
                            <i class="fa fa-calendar text-primary icon-lg"></i>
                        </span>
                    </div>
                    <input
                        class="form-control input-sm dateNewClass"
                        type="text"
                        name="secondSignDate"
                        id="secondSignDate"
                        value="<?php echo Dates::formatDateWithRE($creditDecisionFormData->secondSignDate, 'YMD H:i:s', 'm/d/Y');?>"
                        data-before-creation-date="true"
                        data-start-date="<?php echo glDate::getCreationDate(LMRequest::File()->recordDate); ?>"
                        placeholder="MM/DD/YYYY"
                        maxlength="10"
                        autocomplete="off" >
                </div>
            </div>
            <div class="col-lg-3">
                <label class="font-weight-bold" for="secondSignReason">Reason For Second Sign</label>
                <select name="secondSignReason" id="secondSignReason" class="form-control">
                    <option value="">Select</option>
                    <?php foreach ($secondSignReasonArray as $secondSignReasonKey => $secondSignReasonValue) { ?>
                        <option value="<?php echo $secondSignReasonKey;?>" <?php echo Arrays::isSelected($secondSignReasonKey, $creditDecisionFormData->secondSignReason);?> ><?php echo $secondSignReasonValue;?></option>
                    <?php } ?>
                </select>
            </div>
        </div>

        <div class="form-group row">
            <div class="col-lg-3">
                <label class="font-weight-bold" for="secondSignDecision">Second Sign Decision</label>
                <select name="secondSignDecision" id="secondSignDecision" class="form-control">
                    <option value="">Select</option>
                    <?php foreach ($secondSignDecisionArray as $secondSignDecisionKey => $secondSignDecisionValue) { ?>
                        <option value="<?php echo $secondSignDecisionKey;?>" <?php echo Arrays::isSelected($secondSignDecisionKey, $creditDecisionFormData->secondSignDecision);?> ><?php echo $secondSignDecisionValue;?></option>
                    <?php } ?>
                </select>
            </div>
            <div class="col-lg-3">
                <label class="font-weight-bold" for="secondSignDecisionDate">Second Sign Decision Date</label>
                <div class="input-group">
                    <div class="input-group-prepend actionDate">
                        <span class="input-group-text">
                            <i class="fa fa-calendar text-primary icon-lg"></i>
                        </span>
                    </div>
                    <input
                        class="form-control input-sm dateNewClass "
                        type="text"
                        name="secondSignDecisionDate"
                        id="secondSignDecisionDate"
                        value="<?php echo Dates::formatDateWithRE($creditDecisionFormData->secondSignDecisionDate, 'YMD H:i:s', 'm/d/Y');?>"
                        data-before-creation-date="true"
                        data-start-date="<?php echo glDate::getCreationDate(LMRequest::File()->recordDate); ?>"
                        placeholder="MM/DD/YYYY"
                        maxlength="10"
                        autocomplete="off" >
                </div>
            </div>
            <div class="col-lg-3">
                <label class="font-weight-bold" for="secondSignDecisionBy">Second Sign Decision By</label>
                <select name="secondSignDecisionBy" id="secondSignDecisionBy" class="form-control">
                    <option value="">Select</option>
                    <?php foreach ($boStaffArray as $boStaffArrayKey => $boStaffArrayValue) { ?>
                        <option value="<?php echo $boStaffArrayValue['AID']; ?>" <?php echo Arrays::isSelected($boStaffArrayValue['AID'], $creditDecisionFormData->secondSignDecisionBy);?> ><?php echo $boStaffArrayValue['role'] . ' - ' . $boStaffArrayValue['processorName']; ?></option>
                    <?php } ?>
                </select>
            </div>
            <div class="col-lg-3">
                <label class="font-weight-bold" for="secondSignDecisionNotes">Second Sign Decision Notes</label>
                <textarea name="secondSignDecisionNotes" id="secondSignDecisionNotes" class="form-control validateMaxLength"
                          maxlength="<?php echo loanForm::getFieldLength('secondSignDecisionNotes','tblCreditDecisionForm'); ?>"
                ><?php echo $creditDecisionFormData->secondSignDecisionNotes;?></textarea>
            </div>
        </div>
    </div>
</div>
