<?php

use models\composite\oClient\getClientInfo;
use models\composite\oClient\saveOrUpdateBorrowerAlternateNes;
use models\composite\oFileUpdate\checkAndSaveFileClient;
use models\composite\oFileUpdate\saveGiftsOrGrants;
use models\composite\oFileUpdate\saveOtherNewMortgageLoansOnProperty;
use models\composite\oHMLOInfo\saveAdditionalGuarantorsInfo;
use models\composite\oHMLOInfo\saveHMLOFilePropertyInfo;
use models\composite\oHMLOInfo\saveHMLOFileResponseInfo;
use models\composite\oHMLOInfo\saveHMLONewLoanInfo;
use models\composite\oHMLOInfo\saveHMLOQAInfo;
use models\composite\oIncExp\saveIncomeInfo;
use models\composite\oLoanOrigination\LOLiabilitiesInfoSave;
use models\composite\oLoanOrigination\saveCoBEmployementInfo;
use models\composite\oLoanOrigination\saveEmployementInfo;
use models\composite\oLoanOrigination\saveFinanceAndSecurities;
use models\composite\oLoanOrigination\saveLoanOriginatorInfo;
use models\composite\oLoanOrigination\saveLOAssetsInfo;
use models\composite\oLoanOrigination\scheduleRealEstateSave;
use models\Controllers\LMRequest\Property;
use models\CustomField;
use models\Database2;
use models\lendingwise\db\tblFileHMLOPropInfo_db;
use models\lendingwise\tblAutomatedRuleRequestV2;
use models\lendingwise\tblFile;
use models\cypher;
use models\lendingwise\tblFileHMLOPropInfo;
use models\lendingwise_log\ChangeLog;
use models\PageVariables;
use models\Request;
use models\standard\Dates;
use models\standard\HTTP;
use models\standard\Strings;
use models\types\strongType;

session_start();
require '../includes/util.php';

require 'initPageVariables.php';
require 'getPageVariables.php';

global $userGroup, $DIYUser, $userRole, $userName,
       $allowAutomation;
$CID = 0;
$clientInfo = [];
$LMRId = isset($_POST['encryptedLId']) ? cypher::myDecryption(trim($_POST['encryptedLId'])) : 0;
$encryptedBId = isset($_POST['encryptedBId']) ? cypher::myDecryption(trim($_POST['encryptedBId'])) : 0;
$executiveId = isset($_POST['encryptedEId']) ? cypher::myDecryption(trim($_POST['encryptedEId'])) : 0;
$responseId = isset($_POST['encryptedRId']) ? cypher::myDecryption(trim($_POST['encryptedRId'])) : 0;
$PCID = isset($_POST['encryptedPCID']) ? cypher::myDecryption(trim($_POST['encryptedPCID'])) : 0;
$clientId = isset($_POST['encryptedCId']) ? cypher::myDecryption(trim($_POST['encryptedCId'])) : 0;
$assetId = isset($_POST['assetId']) ? trim($_POST['assetId']) : 0;
$selClientId = isset($_POST['selClientId']) ? trim($_POST['selClientId']) : 0;
$op = isset($_POST['op']) ? trim($_POST['op']) : 0;
$activeTab = isset($_POST['activeTab']) ? trim($_POST['activeTab']) : 0;
$publicUser = isset($_POST['publicUser']) ? trim($_POST['publicUser']) : 0;
$accType = isset($_POST['accType']) ? trim($_POST['accType']) : '';
$LOCSID = isset($_POST['LOCSID']) ? trim($_POST['LOCSID']) : null;

if (isset($_REQUEST['LOLID'])) $LOLID = $_REQUEST['LOLID'];
if (isset($_REQUEST['nameAddrOfCompany'])) $nameAddrOfCompany = $_REQUEST['nameAddrOfCompany'];
if (isset($_REQUEST['monthlyPaymentExpenses'])) $monthlyPayment = $_REQUEST['monthlyPaymentExpenses'];
if (isset($_REQUEST['monthsLeftToPay'])) $monthsLeftToPay = $_REQUEST['monthsLeftToPay'];
if (isset($_REQUEST['unpaidBalanceExpenses'])) $unpaidBalance = $_REQUEST['unpaidBalanceExpenses'];
if (isset($_REQUEST['accountNo'])) $accountNo = $_REQUEST['accountNo'];
if (isset($_REQUEST['liabilityAccType'])) $liabilityAccType = $_REQUEST['liabilityAccType'];
if (isset($_REQUEST['liabilityAtorBeforeClose'])) $liabilityAtorBeforeClose = $_REQUEST['liabilityAtorBeforeClose'];
if (isset($_REQUEST['deletedLOLId'])) $deletedLOLId = $_REQUEST['deletedLOLId'];

$btnValue = Request::isset('btnSave') ? Request::GetClean('btnSave') : '';
$isSave = Request::isset('isSave') ? Request::GetClean('isSave') : '';
$goToTab = Request::isset('goToTab') ? Request::GetClean('goToTab') : '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    ChangeLog::LogChanges(
        tblFile::class,
        $LMRId,
        basename(__FILE__, '.php'),
        $_REQUEST,
        PageVariables::$userNumber
    );
}

$_POST['PCID'] = $_POST['FPCID'];
$_POST['UID'] = PageVariables::$userNumber;
$_POST['URole'] = $userGroup;
$getClientInfo = [];
if ($selClientId > 0) {
    $getClientInfo = getClientInfo::getReport(['CID' => $selClientId, 'email' => '', 'PCID' => $PCID]);
}
/* Get Client Information */
if (array_key_exists('getClientInfo', $getClientInfo)) $getClientInfo = $getClientInfo['getClientInfo'];
if (array_key_exists('clientInfo', $getClientInfo)) $clientInfo = $getClientInfo['clientInfo'][0];

$qc = $qc1 = '';
$qv = $qv1 = '';
$qs = $qs1 = '';
$qu = $qu1 = '';

$q = 'SELECT F2ID FROM tblFile2 WHERE LMRId = :LMRId ';
$sqlParams = [
    'LMRId' => $LMRId,
];
$result2Array = Database2::getInstance()->queryData($q, $sqlParams, null, true);

$file2FieldsArray = [
    'presentAddress'
    , 'presentCounty'
    , 'presentCity'
    , 'presentState'
    , 'presentZip'
    , 'presentPropLengthTime'
    , 'previousPropLengthTime'
    , 'borPresentPropType'
    , 'borMailingPropType'
    , 'borFormerPropType'
    , 'mailingAddrAsPresent'
    , 'presentPropLengthTimeCoBor'
    , 'presentUnit'
    , 'presentCountry'
    , 'presentPropLengthMonths'
    , 'currentRPM'
    , 'previousUnit'
    , 'previousCountry'
    , 'previousPropLengthMonths'
    , 'previousRPM'
];

$clientAddrFields = [
    'presentAddress' => 'clientAddress',
    'presentCity'    => 'clientCity',
    'presentState'   => 'clientState',
    'presentZip'     => 'clientZip'
];
$currencyFieldArray = ['currentRPM', 'previousRPM'];
$clientParams = [];
foreach ($file2FieldsArray as $f => $column) {
    if (isset($_REQUEST[$column])) {
        $qc .= $qs . " $column ";
        if (in_array($column, $currencyFieldArray)) {
            $postVal = trim(Strings::replaceCommaValues($_REQUEST[$column]));
        } else {
            $postVal = trim($_REQUEST[$column]);
        }
        $params[$column] = $postVal;
        $qv .= $qs . ' :' . $column . ' ';
        $qu .= $qs . " $column = :" . $column . ' ';
        $qs = ', ';

        if ($postVal != '' && array_key_exists($column, $clientAddrFields)) {
            $colname = $clientAddrFields[$column];
            $qc1 .= $qs1 . " $colname ";
            $qv1 .= $qs1 . ' :' . $colname;
            $qu1 .= $qs1 . " $colname = :" . $colname . ' ';
            $clientParams[$colname] = $postVal;
            $qs1 = ', ';
        }
    } elseif (array_key_exists($column, $clientAddrFields)) {
        if (array_key_exists($clientAddrFields[$column], $clientInfo)) {
            $tF = $clientAddrFields[$column];
            if (trim($clientInfo[$tF]) != '') {
                $qc .= $qs . " $column ";
                $postVal = trim($clientInfo[$tF]);
                $params[$column] = $postVal;
                $qv .= $qs . '  :' . $column . ' ';
                $qu .= $qs . " $column =  :" . $column . ' ';
                $qs = ', ';
            }
        }
    }
}

$params['LMRId'] = $LMRId;
if (count($result2Array) > 0) {
    if ($qu != '') {
        $qry = ' UPDATE tblFile2 SET ' . $qu . ' WHERE LMRID = :LMRId ;';
        Database2::getInstance()->update($qry, $params);
    }
} elseif ($qc != '' && $qv != '') {
    $qry = ' INSERT INTO tblFile2 (LMRID, ' . $qc . ') VALUES ( :LMRId ,' . $qv . ');  ';
    Database2::getInstance()->insert($qry, $params);
}

$params['borResidedPresentAddr'] = $_POST['borResidedPresentAddr'];
$qry = ' UPDATE tblFileLoanOrigination SET borResidedPresentAddr = :borResidedPresentAddr WHERE fileID = :LMRId ;';
Database2::getInstance()->update($qry, $params);

$qc = '';
$qv = '';
$qs = '';
$qu = '';

$clientResult = checkAndSaveFileClient::getReport($_POST ?? []);

if (count($clientResult) > 0) {
    $CID = trim($clientResult['CID']);
    $clientEmail = trim($clientResult['clientEmail']);
}

if (isset($_REQUEST['borrowerFName'])) {
    $params['borrowerFName'] = HTTP::escapeQuoteForPOST($_REQUEST['borrowerFName']);
    $params['enc_borrowerFName'] = trim(cypher::myEncryption(trim(stripslashes($_REQUEST['borrowerFName']))));

    $qc .= $qs . ' borrowerName, enc_borrowerName ';
    $qv .= $qs . " :borrowerFName , :enc_borrowerFName ";
    $qu .= $qs . " borrowerName = :borrowerFName , enc_borrowerName = :enc_borrowerFName ";
    $qs = ', ';
    if ($_REQUEST['borrowerFName'] != '') {
        $qc1 .= $qs1 . ' clientFName ';
        $qv1 .= $qs1 . ' :clientFName ';
        $qu1 .= $qs1 . ' clientFName = :clientFName ';
        $clientParams['clientFName'] = $params['borrowerFName'];
        $qs1 = ', ';
    }

}
if (isset($_REQUEST['borrowerMName'])) {
    $params['borrowerMName'] = HTTP::escapeQuoteForPOST($_REQUEST['borrowerMName']);

    $qc .= $qs . ' borrowerMName ';
    $qv .= $qs . " :borrowerMName ";
    $qu .= $qs . " borrowerMName = :borrowerMName ";
    $qs = ', ';
}

if (isset($_REQUEST['borrowerLName'])) {
    $params['borrowerLName'] = HTTP::escapeQuoteForPOST($_REQUEST['borrowerLName']);
    $params['enc_borrowerLName'] = trim(cypher::myEncryption(trim(stripslashes($_REQUEST['borrowerLName']))));

    $qc .= $qs . ' borrowerLName, enc_borrowerLName ';
    $qv .= $qs . " :borrowerLName , :enc_borrowerLName ";
    $qu .= $qs . " borrowerLName = :borrowerLName , enc_borrowerLName = :enc_borrowerLName ";
    $qs = ', ';
    if ($_REQUEST['borrowerLName'] != '') {
        $qc1 .= $qs1 . ' clientLName ';
        $qv1 .= $qs1 . '  :clientLName ';
        $qu1 .= $qs1 . ' clientLName = :clientLName ';
        $clientParams['clientLName'] = $params['borrowerLName'];
        $qs1 = ', ';
    }

}
if (isset($_REQUEST['ssn'])) {
    $params['ssn'] = Strings::NumbersOnly($_REQUEST['ssn']);
    $params['enc_ssn'] = trim(cypher::myEncryption(trim(stripslashes($_REQUEST['ssn']))));

    $qc .= $qs . ' ssnNumber, enc_ssnNumber ';
    $qv .= $qs . " :ssn ,  :enc_ssn ";
    $qu .= $qs . " ssnNumber = :ssn , enc_ssnNumber = :enc_ssn ";
    $qs = ', ';

    if ($_REQUEST['ssn'] != '') {
        $qc1 .= $qs1 . ' ssnNumber';
        $qv1 .= $qs1 . '  :ssnNumber ';
        $qu1 .= $qs1 . ' ssnNumber = :ssnNumber ';
        $clientParams['ssnNumber'] = $params['ssn'];
        $qs1 = ', ';
    }
}
if (isset($_REQUEST['borrowerDOB'])) {
    $borrowerDOB = trim($_REQUEST['borrowerDOB']);
    if (Dates::IsEmpty($borrowerDOB)) {
        $borrowerDOB = '0000-00-00';
    } else {
        $borrowerDOB = trim(Dates::formatDateWithRE($borrowerDOB, 'MDY', 'Y-m-d'));
    }
    $params['borrowerDOB'] = $borrowerDOB;
    $params['enc_borrowerDOB'] = trim(cypher::myEncryption(trim($borrowerDOB)));

    $qc .= $qs . ' borrowerDOB, enc_borrowerDOB ';
    $qv .= $qs . " :borrowerDOB ,  :enc_borrowerDOB ";
    $qu .= $qs . " borrowerDOB = :borrowerDOB , enc_borrowerDOB = :enc_borrowerDOB ";
    $qs = ', ';

    if ($borrowerDOB != '0000-00-00') {
        $qc1 .= $qs1 . ' borrowerDOB ';
        $qv1 .= $qs1 . ' :borrowerDOB ';
        $qu1 .= $qs1 . ' borrowerDOB = :borrowerDOB ';
        $clientParams['borrowerDOB'] = trim($borrowerDOB);
        $qs1 = ', ';
    }
}
if (isset($_REQUEST['phoneNumber']) && !strpos($qc, 'phoneNumber')) {
    $params['phoneNumber'] = Strings::getNumberValue(trim($_REQUEST['phoneNumber']));

    if ($_REQUEST['phoneNumber'] != '') {

        $qc .= $qs . ' phoneNumber ';
        $qv .= $qs . " :phoneNumber ";
        $qu .= $qs . " phoneNumber = :phoneNumber ";
        $qs = ', ';

        $qc1 .= $qs1 . ' clientPhone ';
        $qv1 .= $qs1 . '  :clientPhone ';
        $qu1 .= $qs1 . ' clientPhone =  :clientPhone ';
        $clientParams['clientPhone'] = $params['phoneNumber'];
        $qs1 = ', ';
    } elseif (array_key_exists('clientPhone', $clientInfo) && (trim($clientInfo['clientPhone']) != '')) {                        /* Insert/Update Form Client Profile */
        $qc .= $qs . ' phoneNumber ';
        $qv .= $qs . " :phoneNumber ";
        $qu .= $qs . " phoneNumber = :phoneNumber ";
        $qs = ', ';
    }
}
if (isset($_REQUEST['cellNo'])) {
    $params['cellNo'] = Strings::getNumberValue(trim($_REQUEST['cellNo']));

    $qc .= $qs . ' cellNumber ';
    $qv .= $qs . " :cellNo ";
    $qu .= $qs . " cellNumber = :cellNo ";
    $qs = ', ';

    if ($_REQUEST['cellNo'] != '') {
        $qc1 .= $qs1 . ' clientCell ';
        $qv1 .= $qs1 . '  :clientCell ';
        $qu1 .= $qs1 . ' clientCell = :clientCell ';
        $clientParams['clientCell'] = $params['cellNo'];
        $qs1 = ', ';
    }

}
if (isset($_REQUEST['workNumber'])) {
    $qc .= $qs . ' workNumber ';
    $qv .= $qs . " '" . Strings::getNumberValue(trim($_REQUEST['workNumber'])) . "' ";
    $qu .= $qs . " workNumber = '" . Strings::getNumberValue(trim($_REQUEST['workNumber'])) . "' ";
    $qs = ', ';
}

if (isset($_REQUEST['propertyType'])) {
    $qc .= $qs . ' propertyType ';
    $qv .= $qs . " '" . (trim($_REQUEST['propertyType'])) . "' ";
    $qu .= $qs . " propertyType = '" . (trim($_REQUEST['propertyType'])) . "' ";
    $qs = ', ';
}

if (isset($_REQUEST['propertyAddress'])) {
    $qc .= $qs . ' propertyAddress , enc_propertyAddress ';
    $qv .= $qs . " '" . trim($_REQUEST['propertyAddress']) . "' ,'" . cypher::myEncryption(trim($_REQUEST['propertyAddress'])) . "' ";
    $qu .= $qs . " propertyAddress = '" . trim($_REQUEST['propertyAddress']) . "' ,
    enc_propertyAddress = '" . cypher::myEncryption(trim($_REQUEST['propertyAddress'])) . "' ";
    $qs = ', ';
}

if (isset($_REQUEST['propertyCity'])) {
    $qc .= $qs . ' propertyCity , enc_propertyCity ';
    $qv .= $qs . " '" . trim($_REQUEST['propertyCity']) . "' ,'" . cypher::myEncryption(trim($_REQUEST['propertyCity'])) . "' ";
    $qu .= $qs . " propertyCity = '" . trim($_REQUEST['propertyCity']) . "' ,
    enc_propertyCity = '" . cypher::myEncryption(trim($_REQUEST['propertyCity'])) . "' ";
    $qs = ', ';
}

if (isset($_REQUEST['propertyState'])) {
    $qc .= $qs . ' propertyState , enc_propertyState ';
    $qv .= $qs . " '" . trim($_REQUEST['propertyState']) . "' ,'" . cypher::myEncryption(trim($_REQUEST['propertyState'])) . "' ";
    $qu .= $qs . " propertyState = '" . trim($_REQUEST['propertyState']) . "' ,
    enc_propertyState = '" . cypher::myEncryption(trim($_REQUEST['propertyState'])) . "' ";
    $qs = ', ';
}

if (isset($_REQUEST['propertyZip'])) {
    $qc .= $qs . ' propertyZip , enc_propertyZip ';
    $qv .= $qs . " '" . trim($_REQUEST['propertyZip']) . "' ,'" . cypher::myEncryption(trim($_REQUEST['propertyZip'])) . "' ";
    $qu .= $qs . " propertyZip = '" . trim($_REQUEST['propertyZip']) . "' ,
    enc_propertyZip = '" . cypher::myEncryption(trim($_REQUEST['propertyZip'])) . "' ";
    $qs = ', ';
}
if (Request::isset('loanNumber')) {
    $qc .= $qs . ' loanNumber ';
    $qv .= $qs . " '" . Request::GetClean('loanNumber') . "' ";
    $qu .= $qs . " loanNumber = '" . Request::GetClean('loanNumber') . "' ";
    $qs = ', ';
}
if (Request::isset('lien1Rate')) {
    $qc .= $qs . ' lien1Rate ';
    $qv .= $qs . " '" . Request::GetClean('lien1Rate') . "' ";
    $qu .= $qs . " lien1Rate = '" . Request::GetClean('lien1Rate') . "' ";
    $qs = ', ';
}
if (Request::isset('lien1Terms')) {
    $qc .= $qs . ' lien1Terms ';
    $qv .= $qs . " '" . Request::GetClean('lien1Terms') . "' ";
    $qu .= $qs . " lien1Terms = '" . Request::GetClean('lien1Terms') . "' ";
    $qs = ', ';
}
if (Request::isset('homeValue')) {
    $qc .= $qs . ' homeValue ';
    $qv .= $qs . " '" . Strings::replaceCommaValues(Request::GetClean('homeValue')) . "' ";
    $qu .= $qs . " homeValue = '" . Strings::replaceCommaValues(Request::GetClean('homeValue')) . "' ";
    $qs = ', ';
}

if (isset($_REQUEST['borrowerEmail'])) {
    $clientEmail = $_REQUEST['borrowerEmail'];
    $qc .= $qs . ' borrowerEmail, enc_borrowerEmail ';
    $qv .= $qs . " '" . HTTP::escapeQuoteForPOST($clientEmail) . "', '" . trim(cypher::myEncryption(trim(stripslashes($clientEmail)))) . "' ";
    $qu .= $qs . " borrowerEmail = '" . HTTP::escapeQuoteForPOST($clientEmail) . "', enc_borrowerEmail = '" . trim(cypher::myEncryption(trim(stripslashes($clientEmail)))) . "' ";
    $qs = ', ';
}

if ($CID > 0 && trim($clientId) != trim($CID)) {
    $qc .= $qs . ' clientId ';
    $qv .= $qs . " '" . trim($CID) . "' ";
    $qu .= $qs . " clientId = '" . trim($CID) . "' ";
    $qs = ', ';
}

$DFArray = [
    'mailingAddress'
    , 'mailingCity'
    , 'mailingState'
    , 'mailingZip'
    , 'mailingUnit'
    , 'mailingCountry'
    , 'previousAddress'
    , 'previousCity'
    , 'previousState'
    , 'previousZip'
    , 'maritalStatus'
    , 'mailingAddressAsProp'
];
for ($f = 0; $f < count($DFArray); $f++) {
    if (isset($_REQUEST["$DFArray[$f]"])) {
        $qc .= $qs . " $DFArray[$f] ";
        $postVal = HTTP::escapeQuoteForPOST($_REQUEST["$DFArray[$f]"]);
        $qv .= $qs . " '" . $postVal . "' ";
        $qu .= $qs . " $DFArray[$f] = '" . $postVal . "' ";
        $qs = ', ';

        if ($DFArray[$f] == 'serviceProvider' && $postVal != '') {
            $qc1 .= $qs1 . " $DFArray[$f] ";
            $qv1 .= $qs1 . " '" . $postVal . "' ";
            $qu1 .= $qs1 . " $DFArray[$f] = '" . $postVal . "' ";
            $qs1 = ', ';
        }
    } elseif (isset($clientInfo["$DFArray[$f]"]) && ($clientInfo["$DFArray[$f]"] != '')) {
        $qc .= $qs . " $DFArray[$f] ";
        $postVal = HTTP::escapeQuoteForPOST($clientInfo["$DFArray[$f]"]);
        $qv .= $qs . " '" . $postVal . "' ";
        $qu .= $qs . " $DFArray[$f] = '" . $postVal . "' ";
        $qs = ', ';
    }
}

if ($LMRId > 0) {
    $qry = ' UPDATE tblFile SET ' . $qu . ", lastUpdatedDate = '" . Dates::Timestamp() . "' WHERE LMRId = '" . $LMRId . "'";
}

$qry = Database2::getInstance()->safeQuery($qry, $params);

$sql = 'CALL SP_SaveClientInfo (
                :qry,
                :notesDate,
                :PCID,
                :LMRId,
                :UID,
                :UGroup,
                :saveTab
        );';

$params = [
    'qry'       => addslashes($qry),
    'notesDate' => Dates::Timestamp(),
    'PCID'      => $PCID,
    'LMRId'     => $LMRId,
    'UID'       => PageVariables::$userNumber,
    'UGroup'    => $userGroup,
    'saveTab'   => 'CI',
];
$result = Database2::getInstance()->multiQueryData($sql, 'myOpt', $params);

$clientParams['CID'] = $CID;
if ($CID > 0 && $qu1 != '') {
    $clientqry = ' UPDATE tblClient SET ' . $qu1 . ' WHERE CID = :CID ;';
    Database2::getInstance()->queryData($clientqry, $clientParams);
}

$HMLOInput = [
    'p'            => $_POST,
    'LMRId'        => $LMRId,
    'saveOpt'      => 'HMLOSummary',
    'saveTab'      => 'HMLOSummary',
    'encryptedBId' => $encryptedBId,
    'PCID'         => $PCID,
    'URole'        => $userGroup,
    'UID'          => PageVariables::$userNumber,
    'userName'     => $userName,
    'assetId'      => $assetId,
    'clientId'     => $clientId
];
$ip = ['LMRID'           => $LMRId,
       'alternateFName'  => $_POST['alternateFName'],
       'alternateMName'  => $_POST['alternateMName'],
       'alternateLName'  => $_POST['alternateLName'],
       'alternateNameID' => $_POST['alternateNameID']
];
if ($LMRId > 0) {
    saveOrUpdateBorrowerAlternateNes::getReport($ip);
    Property::$LMRId = $LMRId;
    Property::$userGroup = $userGroup;
    Property::$userNumber = PageVariables::$userNumber;
    Property::initSave();
}

$sqlParams = [
    'fileID' => $LMRId,
];
$recordDate = Dates::Timestamp();
$q = 'SELECT HMLOID FROM tblFileHMLO WHERE fileID = :fileID ';
$resultArray = Database2::getInstance()->queryData($q, $sqlParams, null, true);
$dArr = ['serviceExpirationDate'];
$mArr = ['servicingMemberInfo'];
$colArr = ['borrowerCitizenship', 'isServicingMember', 'agesOfDependent', 'numberOfDependents'];
/** Direct Variables **/
$qryArr = strongType::bindInsAndUpQry(['col' => $colArr, 'date' => $dArr, 'mArr' => $mArr]);

if (count($resultArray) > 0) {
    if ($qryArr['qu'] != '') {
        $qry = ' UPDATE tblFileHMLO SET ' . $qryArr['qu'] . ' WHERE fileID = ' . $LMRId . ';';
        $cnt = Database2::getInstance()->update($qry);
    }
} elseif ($qryArr['qc'] != '' && $qryArr['qv'] != '') {
    $qry = ' INSERT INTO tblFileHMLO (fileID, recordDate, ' . $qryArr['qc'] . ") VALUES ('" . $LMRId . "', '" . $recordDate . "',  " . $qryArr['qv'] . ');';
    $cnt = Database2::getInstance()->update($qry);
}

$HMLOPropertyInfoFieldsArray = [
    'acceptedPurchase',
    'involvedPurchase',
    'wholesaleFee',
    'isTherePrePaymentPenalty',
    'prePaymentPenalty',
    'haveOwnershipInterest',
    'typePropOwned',
    'titleType',
    'famBizAffil',
    'applyOtherLoan',
    'applyNewCredit',
    'borrowingMoney',
    'borrowedAmt',
    'isAdditionalGuarantors',
    'applicationLoanExitPlan',
    'exitStrategy',
    'accountExecutiveLoanExitNotes',
    'docType',
    'loanTerm',
    'rateIndex',
    'spread',
    'accrualType',
    'annualPremium',
    'isBlanketLoan',
    'typeOfHMLOLoanRequesting',
    'typeOfSale'
];
/** DIRECT Variables **/
$HMLOPropInfoReplaceCommaField = [
    'wholesaleFee',
    'borrowedAmt',
    'rateIndex',
    'spread',
    'annualPremium'
];

$tblFileHMLOPropInfo = tblFileHMLOPropInfo::Get([tblFileHMLOPropInfo_db::COLUMN_FILEID => $LMRId]) ?? new tblFileHMLOPropInfo();
for ($f = 0; $f < count($HMLOPropertyInfoFieldsArray); $f++) {

    if (Request::isset("$HMLOPropertyInfoFieldsArray[$f]")) {
        if ($HMLOPropertyInfoFieldsArray[$f] == 'propertyNeedRehab') {
            if (Request::GetClean("$HMLOPropertyInfoFieldsArray[$f]") == 'No') {
                $tblFileHMLOPropInfo->isThisGroundUpConstruction = '';
            }

        }

        if (in_array($HMLOPropertyInfoFieldsArray[$f], $HMLOPropInfoReplaceCommaField)) {
            if ($HMLOPropertyInfoFieldsArray[$f] == 'rateIndex') {
                $postVal = implode(',', Request::GetClean('rateIndex'));

            } else {
                $postVal = trim(Strings::replaceCommaValues(Request::GetClean("$HMLOPropertyInfoFieldsArray[$f]")));
            }


        } elseif ($HMLOPropertyInfoFieldsArray[$f] == 'rateIndex') {
            $postVal = implode(',', Request::GetClean('rateIndex'));
        } else {
            $postVal = trim(Request::GetClean("$HMLOPropertyInfoFieldsArray[$f]"));
        }

        if ($HMLOPropertyInfoFieldsArray[$f] == 'expectForDueDiligence') $postVal = urlencode(Strings::stripQuote($postVal));
        $tblFileHMLOPropInfo->{$HMLOPropertyInfoFieldsArray[$f]} = $postVal;

    } elseif ($HMLOPropertyInfoFieldsArray[$f] == 'isBlanketLoan') {
        if (Request::isset('isBlanketLoanMirror')) {
            $postVal = trim(Request::GetClean('isBlanketLoanMirror'));
            $tblFileHMLOPropInfo->{$HMLOPropertyInfoFieldsArray[$f]} = $postVal;
        }

    }
}
if (Request::isset('haveCurrentLoanBal')) {
    $haveCurrentLoanBal = Request::GetClean('haveCurrentLoanBal');
    $tblFileHMLOPropInfo->haveCurrentLoanBal = $haveCurrentLoanBal;
}
if (Request::isset('balance')) {
    $balance = Strings::replaceCommaValues(Request::GetClean('balance'));
    $tblFileHMLOPropInfo->balance = $balance;
}

if (Request::isset('heldWith')) {
    $heldWith = Request::GetClean('heldWith');
    $tblFileHMLOPropInfo->heldWith = $heldWith;
}
if (Request::isset('fundingDate')) {
    $fundingDate = Request::GetClean('fundingDate');
    if (Dates::IsEmpty($fundingDate)) {
        $fundingDate = null;
    } else {
        $fundingDate = trim(Dates::formatDateWithRE($fundingDate, 'MDY', 'Y-m-d'));
    }
    $tblFileHMLOPropInfo->fundingDate = $fundingDate;
}
if (Request::isset('maturityDate')) {
    $maturityDate = Request::GetClean('maturityDate');
    if (Dates::IsEmpty($maturityDate)) {
        $maturityDate = null;
    } else {
        $maturityDate = trim(Dates::formatDateWithRE($maturityDate, 'MDY', 'Y-m-d'));
    }
    $tblFileHMLOPropInfo->maturityDate = $maturityDate;
}

$tblFileHMLOPropInfo->fileID = $LMRId;
$tblFileHMLOPropInfo->userName = Strings::processString($userName);
$tblFileHMLOPropInfo->userType = $userGroup;
if(!$tblFileHMLOPropInfo->HMLOPID){
    $tblFileHMLOPropInfo->recordDate = Dates::Timestamp();
}

$tblFileHMLOPropInfo->save();

if (Request::isset('trialPaymentDate1') ) {
    saveHMLOFileResponseInfo::getReport(['p' => $_POST, 'LMRId' => $LMRId, 'PCID' => $PCID]); // refactored
}
saveAdditionalGuarantorsInfo::getReport($HMLOInput);
saveHMLOFilePropertyInfo::getReport($_POST);
$scheduleestate = [
    'LMRId' => $LMRId,
    'schedulePropAddr' => $_POST['schedulePropAddr'],
    'schedulePropCity' => $_POST['schedulePropCity'],
    'schedulePropState' => $_POST['schedulePropState'],
    'schedulePropZip' => $_POST['schedulePropZip'],
    'scheduleStatus' => $_POST['scheduleStatus'],
    'propType' => $_POST['propType'],
    'presentMarketValue' => $_POST['presentMarketValue'],
    'amountOfMortgages' => $_POST['amountOfMortgages'],
    'grossRentalIncome' => $_POST['grossRentalIncome'],
    'mortgagePayments' => $_POST['mortgagePayments'],
    'insMaintTaxMisc' => $_POST['insMaintTaxMisc'],
    'netRentalIncome' => $_POST['netRentalIncome'],
    'scheduleID' => $_POST['scheduleID'],
    'titledUnder' => $_POST['titledUnder'],
    'datePurchased' => $_POST['datePurchased'],
    'purchasePrice' => $_POST['purchasePrice'],
    'valueofImprovementsMade' => $_POST['valueofImprovementsMade'],
    'intendedOccupancy' => $_POST['intendedOccupancy'],
    'p' => $_POST,
];
scheduleRealEstateSave::getReport($scheduleestate);
$inputArray = ['p' => $_POST, 'LMRId' => $LMRId];
saveLOAssetsInfo::getReport($inputArray);
saveFinanceAndSecurities::getReport($inputArray);
saveIncomeInfo::getReport($inputArray);
$InArray = ['LMRId' => $LMRId,
    'LOLID' => $LOLID,
    'nameAddrOfCompany' => $nameAddrOfCompany,
    'monthlyPayment' => $monthlyPayment,
    'monthsLeftToPay' => $monthsLeftToPay,
    'unpaidBalance' => $unpaidBalance,
    'accountNo' => $accountNo,
    'liabilityAccType' => $liabilityAccType,
    'liabilityAtorBeforeClose' => $liabilityAtorBeforeClose,
    'deletedLOLId' => $deletedLOLId,
];
LOLiabilitiesInfoSave::getReport($InArray);
$fileInArray = [
    'p' => $_POST,
    'LMRId' => $LMRId];
saveOtherNewMortgageLoansOnProperty::getReport($fileInArray);
saveGiftsOrGrants::getReport($fileInArray);
saveHMLONewLoanInfo::getReport($inputArray);
saveLoanOriginatorInfo::getReport($fileInArray);

$HMLOBGFieldsArray = [
    'isBorUSCitizen' => null,
    'isBorDecalredBankruptPastYears' => null,
    'isAnyBorOutstandingJudgements' => null,
    'hasBorAnyActiveLawsuits' => null,
    'hasBorPropertyTaxLiens' => null,
    'hasBorObligatedInForeclosure' => null,
    'isBorPresenltyDelinquent' => null,
    'isBorBorrowedDownPayment' => null,
    'isBorIntendToOccupyPropAsPRI' => null,
    'haveBorOtherFraudRelatedCrimes' => null,
    'isBorPersonallyGuaranteeLoan' => null,
    'borBackgroundExplanation' => null,
    'borDesignatedBeneficiaryAgreement' => null,

    'borDecalredBankruptExpln' => null,
    'borOutstandingJudgementsExpln' => null,
    'borActiveLawsuitsExpln' => null,
    'borPropertyTaxLiensExpln' => null,
    'borObligatedInForeclosureExpln' => null,
    'borDelinquentExpln' => null,
    'borOtherFraudRelatedCrimesExpln' => null,
    'borBorrowedDownPaymentExpln' => null,
    'borDesignatedBeneficiaryAgreementExpln' => null,

    'isCoBorUSCitizen' => null,
    'isCoBorDecalredBankruptPastYears' => null,
    'isAnyCoBorOutstandingJudgements' => null,
    'hasCoBorAnyActiveLawsuits' => null,
    'hasCoBorPropertyTaxLiens' => null,
    'hasCoBorObligatedInForeclosure' => null,
    'isCoBorPresenltyDelinquent' => null,
    'isCoBorBorrowedDownPayment' => null,
    'isCoBorIntendToOccupyPropAsPRI' => null,
    'haveCoBorOtherFraudRelatedCrimes' => null,
    'isCoBorPersonallyGuaranteeLoan' => null,
    'coBorBackgroundExplanation' => null,
    'coBorDesignatedBeneficiaryAgreement' => null,
    'marriedToBor' => null,

    'coBorDecalredBankruptExpln' => null,
    'coBorOutstandingJudgementsExpln' => null,
    'coBorActiveLawsuitsExpln' => null,
    'coBorPropertyTaxLiensExpln' => null,
    'coBorObligatedInForeclosureExpln' => null,
    'coBorDelinquentExpln' => null,
    'coBorOtherFraudRelatedCrimesExpln' => null,
    'coBorBorrowedDownPaymentExpln' => null,
    'coBorDesignatedBeneficiaryAgreementExpln' => null,
    'borOrigin' => null,
    'borVisaStatus' => null,

    'personalBankruptcy' => Database2::DATATYPE_NUMBER,
    'statusForeclosure' => Database2::DATATYPE_NUMBER,
    'completedPreForeclose' => null,
    'hasBorBeenForeclosed' => null,
    'bankruptcyTypes' => null,
    'coborOrigin' => null,
    'coborVisaStatus' => null,
    'previouslyHadShortSale' => null,

];
/** DIRECT Variables **/

$qu = '';
$qs = '';
$qc = '';
$qv = '';
$params = [];
foreach ($HMLOBGFieldsArray as $column => $type) {
    if (isset ($_REQUEST["$column"])) {
        $qc .= $qs . " $column ";

        if ($column == 'bankruptcyTypes') {
            $postVal = trim(implode(',', $_REQUEST["$column"]));
            $params[$column] = $postVal;
        } else {
            $postVal = trim($_REQUEST["$column"]);
            $params[$column] = $postVal;
        }
        if ($type) {
            $params[$column] = Database2::strongTypeValue($params[$column], $type);
        }
        $qv .= $qs . ' :' . $column;
        $qu .= $qs . " $column = :" . $column;
        $qs = ', ';
    }
}

$params['fileID'] = $LMRId;
$params['CID'] = $clientId;
$params['recordDate'] = Dates::Timestamp();
if (count($resultArray) > 0) {
    if ($qu != '') {
        $qry = ' UPDATE tblFileHMLOBackGround SET ' . $qu . ' WHERE fileID = ' . $LMRId . ';';
        $cnt = Database2::getInstance()->queryData($qry, $params);
    }
} elseif ($qc != '' && $qv != '') {
    $qry = ' INSERT INTO tblFileHMLOBackGround (fileID, CID, recordDate, ' . $qc . ') VALUES ( :fileID, :CID, :recordDate ,' . $qv . ');';
    $cnt = Database2::getInstance()->insert($qry, $params);
}

if (isset($_REQUEST['AddiontalEmplInfo'])) {
    saveEmployementInfo::saveEmplInfo($_REQUEST['AddiontalEmplInfo'], cypher::myEncryption($LMRId));
}
if (isset($_REQUEST['AdditionalCoBorEmplInfo'])) {
    saveCoBEmployementInfo::saveEmplInfo($_REQUEST['AdditionalCoBorEmplInfo'], cypher::myEncryption($LMRId));
}

$HMLOArray = ['p' => $_POST,
              'LMRId' => $LMRId,
              'clientId' => $clientId,
              'PCID' => $PCID,
];
saveHMLOQAInfo::getReport($HMLOArray);

tblAutomatedRuleRequestV2::Trigger($LMRId, $PCID); // last
CustomField::Save(
    tblFile::class,
    $LMRId,
    $PCID,
    PageVariables::$userNumber,
    PageVariables::$userRole,
    PageVariables::$userGroup
);
Database2::saveLogQuery();


Strings::SetSess('msg', 'Updated Successfully');

if ($userRole == 'Branch') $redirect = CONST_URL_BRSSL;
else if ($userRole == 'Agent') $redirect = CONST_URL_AG_SSL;
else if (($userRole == 'Client') && (preg_match('/client_new\//i', $_SERVER['HTTP_REFERER']))) $redirect = CONST_URL_CL_NEW_SSL;
else if ($userRole == 'Client') $redirect = CONST_URL_CL_SSL;
else    $redirect = CONST_URL_BOSSL;

$redirect .= 'LMRequest.php?eId=' . cypher::myEncryption($executiveId)
    . '&pId=' . cypher::myEncryption($PCID)
    . '&lId=' . cypher::myEncryption($LMRId)
    . '&rId=' . cypher::myEncryption($responseId)
    . '&op=' . trim($op)
    . '&tabOpt=' ;

if ($btnValue == 'Save' || $isSave == 1) $redirect .= $activeTab;
else $redirect .= $goToTab;


header('Location: ' . $redirect);

exit();
