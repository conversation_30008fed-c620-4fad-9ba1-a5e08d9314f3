<?php
global $myFileInfo, $userGroup, $userName;
use models\constants\gl\glAdverseAction;
use models\constants\gl\glDate;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\loanForm;
use models\lendingwise\tblAdverseAction;
use models\PageVariables;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;

$totalLoanAmount = $myFileInfo['ResponseInfo']['totalLoanAmount'] ?? '';
$interestRate = $myFileInfo['LMRInfo']['lien1Rate'] ?? '';
$loanTerm = $myFileInfo['fileHMLOPropertyInfo']['loanTerm'] ?? '';
$borrowerFirstName = stripslashes($myFileInfo['LMRInfo']['borrowerName'] ?? '');
$borrowerMiddleName = stripslashes($myFileInfo['LMRInfo']['borrowerMName'] ?? '');
$borrowerLastName = stripslashes($myFileInfo['LMRInfo']['borrowerLName'] ?? '');
$borrowerAddress = $myFileInfo['file2Info']['presentAddress'] ?? '';
$entityName = $myFileInfo['fileHMLOEntityInfo']['entityName'] ?? '';
$entityAddress = $myFileInfo['fileHMLOEntityInfo']['entityAddress'] ?? '';
$isCoBorrower = $myFileInfo['LMRInfo']['isCoBorrower'] ?? 0;
$CoBorrowerFirstName = $myFileInfo['LMRInfo']['coBorrowerFName'] ?? '';
$CoBorrowerMiddleName = '';
$CoBorrowerLastName = $myFileInfo['LMRInfo']['coBorrowerLName'] ?? '';
$CoBorrowerAddress = $myFileInfo['file2Info']['coBPresentAddress'] ?? '';
if ($interestRate != '') $interestRate = number_format(Strings::replaceCommaValues($interestRate), 3) . ' %';
$loanNumber = $myFileInfo['LMRInfo']['loanNumber'] ?? '';
//fetch data from database
$id = $myFileInfo['adverseActionInfo']['id'] ?? 0;
$accountDescription = $myFileInfo['adverseActionInfo']['accountDescription'] ?? '';
$HMDAActionTaken = $myFileInfo['adverseActionInfo']['HMDAActionTaken'] ?? '';
$actionDate = $myFileInfo['adverseActionInfo']['actionDate'] ?? '';
if (Dates::IsEmpty($actionDate)) {
    $actionDate = '';
} else {
    $actionDate = trim(Dates::formatDateWithRE($actionDate, 'YMD', 'm/d/Y'));
}
$actionNoticeDetails = $myFileInfo['adverseActionInfo']['actionNoticeDetails'] ?? '';
$informationByDate = $myFileInfo['adverseActionInfo']['informationByDate'] ?? '';
if (Dates::IsEmpty($informationByDate)) {
    $informationByDate = '';
} else {
    $informationByDate = trim(Dates::formatDateWithRE($informationByDate, 'YMD', 'm/d/Y'));
}
$dateDenied = $myFileInfo['adverseActionInfo']['dateDenied'] ?? '';
if (Dates::IsEmpty($dateDenied)) {
    $dateDenied = '';
} else {
    $dateDenied = trim(Dates::formatDateWithRE($dateDenied, 'YMD', 'm/d/Y'));
}
$denialReasonExp = [];
$denialReason = $myFileInfo['adverseActionInfo']['denialReason'] ?? [];
if ($denialReason) $denialReasonExp = explode('~', $denialReason);

$otherReason = $myFileInfo['adverseActionInfo']['otherReason'] ?? '';
$obtainedFromAgency = $myFileInfo['adverseActionInfo']['obtainedFromAgency'] ?? '';
$creditReportDate = $myFileInfo['adverseActionInfo']['creditReportDate'] ?? '';
if (Dates::IsEmpty($creditReportDate)) {
    $creditReportDate = '';
} else {
    $creditReportDate = trim(Dates::formatDateWithRE($creditReportDate, 'YMD', 'm/d/Y'));
}
$agencyName = $myFileInfo['adverseActionInfo']['agencyName'] ?? '';
$agencyAddress = $myFileInfo['adverseActionInfo']['agencyAddress'] ?? '';
$agencyPhone = $myFileInfo['adverseActionInfo']['agencyPhone'] ?? '';
$agencyScoreRangeFrom = $myFileInfo['adverseActionInfo']['agencyScoreRangeFrom'] ?? '';
$agencyScoreRangeTo = $myFileInfo['adverseActionInfo']['agencyScoreRangeTo'] ?? '';
$agencyScore = $myFileInfo['adverseActionInfo']['agencyScore'] ?? '';
if($agencyScore == 0) $agencyScore = '';
$agencyInquiries = $myFileInfo['adverseActionInfo']['agencyInquiries'] ?? '';
$additionalStatement = $myFileInfo['adverseActionInfo']['additionalStatement'] ?? '';
$deliveryMethod = $myFileInfo['adverseActionInfo']['deliveryMethod'] ?? '';
$dateActionDelivered = $myFileInfo['adverseActionInfo']['dateActionDelivered'] ?? '';
if (Dates::IsEmpty($dateActionDelivered)) {
    $dateActionDelivered = '';
} else {
    $dateActionDelivered = trim(Dates::formatDateWithRE($dateActionDelivered, 'YMD', 'm/d/Y'));
}
$completionOfLetter = $myFileInfo['adverseActionInfo']['completionOfLetter'] ?? '';
$noticeForBorrowerName = $myFileInfo['adverseActionInfo']['noticeForBorrowerName'] ?? '';
$informationNeeded = $myFileInfo['adverseActionInfo']['informationNeeded'] ?? '';
$daysForCondition = $myFileInfo['adverseActionInfo']['daysForCondition'] ?? '';
$lenderLetterContactName = $myFileInfo['adverseActionInfo']['lenderLetterContactName'] ?? '';
$lenderLetterEmail = $myFileInfo['adverseActionInfo']['lenderLetterEmail'] ?? '';
$lenderLetterPhone = $myFileInfo['adverseActionInfo']['lenderLetterPhone'] ?? '';
$dateNOIAEmailSent = Dates::formatDateWithRE($myFileInfo['adverseActionInfo']['dateNOIAEmailSent'], 'YMD', 'm/d/Y') ?? '';
$isCoBorrower = $myFileInfo['LMRInfo']['isCoBorrower'] ?? 0;
?>
<input type="hidden" name="adverseActionId" value="<?php echo $id;?>">
<input type="hidden" name="isCoBorrower" value="<?php echo $isCoBorrower;?>">
<input type="hidden" name="userGroup" value="<?php echo $userGroup; ?>">
<input type="hidden" name="userNumber" value="<?php echo PageVariables::$userNumber; ?>">
<input type="hidden" name="userName" value="<?php echo $userName; ?>">
<div class="card card-custom AdverseAction">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label">
                Adverse Action
            </h3>
        </div>
        <div class="card-toolbar ">
            <a href="javascript:void(0);"
               class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
               data-card-tool="toggle"
               data-section="AdverseAction"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </a>
        </div>
    </div>
    <div class="card-body AdverseAction_body">
        <label class="font-weight-bold bg-secondary py-2 col-lg-12">Loan Summary</label>
        <div class="col-md-12">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold">
                            Total Loan Amount :
                        </label>
                        <div class="col-md-7">
                            <span>$ <?php echo Currency::formatDollarAmountWithDecimal($totalLoanAmount);?></span>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold">
                            Interest Rate :
                        </label>
                        <div class="col-md-7">
                            <span><?php echo $interestRate;?></span>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold">
                            Loan Term :
                        </label>
                        <div class="col-md-7">
                            <span><?php echo $loanTerm;?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="clearfix"></div>
        <label class="font-weight-bold bg-secondary py-2 col-lg-12">Borrower Information</label>
        <div class="col-md-12">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold" for="borrowerFName">
                            First Name
                        </label>
                        <div class="col-md-7">
                            <input class="form-control input-sm" type="text" name="borrowerFName" id="borrowerFName"
                                   value="<?php echo htmlspecialchars($borrowerFirstName); ?>" autocomplete="off"/>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold" for="borrowerMName">
                            Middle Name
                        </label>
                        <div class="col-md-7">
                            <input class="form-control input-sm" type="text" name="borrowerMName" id="borrowerMName"
                                   value="<?php echo htmlspecialchars($borrowerMiddleName); ?>" autocomplete="off"/>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold" for="borrowerLName">
                            Last Name
                        </label>
                        <div class="col-md-7">
                            <input class="form-control input-sm" type="text" name="borrowerLName" id="borrowerLName"
                                   value="<?php echo htmlspecialchars($borrowerLastName); ?>" autocomplete="off"/>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold" for="presentAddress">
                            Borrower Address
                        </label>
                        <div class="col-md-7">
                            <input class="form-control input-sm" type="text" name="presentAddress" id="presentAddress"
                                   value="<?php echo htmlspecialchars($borrowerAddress); ?>" autocomplete="off"/>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold" for="entityName">
                            Entity Name
                        </label>
                        <div class="col-md-7">
                            <input class="form-control input-sm" type="text" name="entityName" id="entityName"
                                   value="<?php echo htmlspecialchars($entityName); ?>" autocomplete="off"/>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold" for="entityAddress">
                            Entity Address
                        </label>
                        <div class="col-md-7">
                            <input class="form-control input-sm" type="text" name="entityAddress" id="entityAddress"
                                   value="<?php echo htmlspecialchars($entityAddress); ?>" autocomplete="off"/>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold" for="accountDescription">
                            Description of Account, Transaction, or Requested Credit
                        </label>
                        <div class="col-md-7">
                            <textarea class="form-control input-sm validateMaxLength"
                                      maxlength="<?php echo loanForm::getFieldLength('accountDescription','tblAdverseAction'); ?>"
                                      name="accountDescription" id="accountDescription"><?php echo $accountDescription;?></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php if($isCoBorrower) { ?>
            <div class="clearfix"></div>
            <label class="font-weight-bold bg-secondary py-2 col-lg-12" for="coBorrowerFName">Co-Borrower Information</label>
            <div class="col-md-12">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold">
                                First Name
                            </label>
                            <div class="col-md-7">
                                <input class="form-control input-sm" type="text" name="coBorrowerFName" id="coBorrowerFName"
                                       value="<?php echo htmlspecialchars($CoBorrowerFirstName); ?>" autocomplete="off"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 d-none">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold">
                                Middle Name
                            </label>
                            <div class="col-md-7"></div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold" for="coBorrowerLName">
                                Last Name
                            </label>
                            <div class="col-md-7">
                                <input class="form-control input-sm" type="text" name="coBorrowerLName" id="coBorrowerLName"
                                       value="<?php echo htmlspecialchars($CoBorrowerLastName); ?>" autocomplete="off"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold" for="coBPresentAddress">
                                Co-Borrower Address
                            </label>
                            <div class="col-md-7">
                                <input class="form-control input-sm" type="text" name="coBPresentAddress" id="coBPresentAddress"
                                       value="<?php echo htmlspecialchars($CoBorrowerAddress); ?>" autocomplete="off"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php }?>
        <div class="clearfix"></div>
        <label class="font-weight-bold bg-secondary py-2 col-lg-12" for="HMDAActionTaken">Action Taken</label>
        <div class="col-md-12">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold">
                            HMDA Action Taken
                        </label>
                        <div class="col-md-7">
                            <select class="form-control input-sm" name="HMDAActionTaken" id="HMDAActionTaken">
                                <option value=""> - Please Select - </option>
                                <?php
                                $glAAActionTakenData = glAdverseAction::$glAAActionTaken;
                                foreach ($glAAActionTakenData as $glAAActionTakenDataKey => $glAAActionTakenDataValue ) { ?>
                                    <option value="<?php echo $glAAActionTakenDataKey;?>" <?php if($glAAActionTakenDataKey == $HMDAActionTaken)  echo 'selected=selected'; ?>><?php echo $glAAActionTakenDataValue;?></option>
                                <?php } ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold" for="actionDate">
                            Action Date
                        </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <div class="input-group-prepend actionDate">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary icon-lg"></i>
                                    </span>
                                </div>
                                <input class="form-control input-sm dateNewClass" type="text" name="actionDate" placeholder="MM/DD/YYYY"
                                       data-before-creation-date="true"
                                       data-start-date="<?php echo glDate::getCreationDate(LMRequest::File()->recordDate); ?>"
                                       id="actionDate" value="<?php echo $actionDate;?>" maxlength="10" autocomplete="off">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold" for="actionNoticeDetails">
                            Notice Detail
                        </label>
                        <div class="col-md-7">
                            <textarea class="form-control input-sm validateMaxLength"
                                      maxlength="<?php echo loanForm::getFieldLength('actionNoticeDetails','tblAdverseAction'); ?>"
                                      name="actionNoticeDetails" id="actionNoticeDetails"><?php echo $actionNoticeDetails;?></textarea>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold" for="informationByDate">
                            Counter Offer Expires / Missing Information By
                        </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <div class="input-group-prepend informationByDate">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary icon-lg"></i>
                                    </span>
                                </div>
                                <input class="form-control input-sm dateNewClass" type="text" name="informationByDate" placeholder="MM/DD/YYYY"
                                       data-before-creation-date="true"
                                       data-start-date="<?php echo glDate::getCreationDate(LMRequest::File()->recordDate); ?>"
                                       id="informationByDate" value="<?php echo $informationByDate;?>" maxlength="10" autocomplete="off">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold" for="dateDenied">
                            Date Denied
                        </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <div class="input-group-prepend dateDenied">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary icon-lg"></i>
                                    </span>
                                </div>
                                <input class="form-control input-sm dateNewClass" type="text" name="dateDenied" placeholder="MM/DD/YYYY"
                                       data-before-creation-date="true"
                                       data-start-date="<?php echo glDate::getCreationDate(LMRequest::File()->recordDate); ?>"
                                       id="dateDenied" value="<?php echo $dateDenied;?>" maxlength="10" autocomplete="off">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="clearfix"></div>
        <label class="font-weight-bold bg-secondary py-2 col-lg-12" for="denialReason">Principal Reasons For Denial</label>
        <div class="col-md-12">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold">
                            Principal Reason(s) for Denial or Other Action Taken
                        </label>
                        <div class="col-md-7">
                            <select class="form-control input-sm chosen-select" multiple name="denialReason[]" id="denialReason">
                                <option value=""> - Please Select - </option>
                                <optgroup label="CREDIT">
                                    <?php
                                    $glAACreditData = glAdverseAction::$glAACredit;
                                    foreach ($glAACreditData as $glAACreditKey => $glAACreditValue ) { ?>
                                        <option value="<?php echo $glAACreditKey; ?>" <?php if(in_array($glAACreditKey, $denialReasonExp)) { echo 'selected=selected'; } ?>><?php echo $glAACreditValue; ?></option>
                                    <?php } ?>
                                </optgroup>
                                <optgroup label="EMPLOYMENT STATUS">
                                    <?php
                                    $glAAEmployementStatusData = glAdverseAction::$glAAEmployementStatus;
                                    foreach ($glAAEmployementStatusData as $glAAEmployementStatusKey => $glAAEmployementStatusValue ) { ?>
                                        <option value="<?php echo $glAAEmployementStatusKey; ?>" <?php if(in_array($glAAEmployementStatusKey, $denialReasonExp)) { echo 'selected=selected'; } ?>><?php echo $glAAEmployementStatusValue; ?></option>
                                    <?php } ?>
                                </optgroup>
                                <optgroup label="INCOME">
                                    <?php
                                    $glAAIncomeData = glAdverseAction::$glAAIncome;
                                    foreach ($glAAIncomeData as $glAAIncomeKey => $glAAIncomeValue ) { ?>
                                        <option value="<?php echo $glAAIncomeKey; ?>" <?php if(in_array($glAAIncomeKey, $denialReasonExp)) { echo 'selected=selected'; } ?>><?php echo $glAAIncomeValue; ?></option>
                                    <?php } ?>
                                </optgroup>
                                <optgroup label="RESIDENCY">
                                    <?php
                                    $glAAResidencyData = glAdverseAction::$glAAResidency;
                                    foreach ($glAAResidencyData as $glAAResidencyKey => $glAAResidencyValue ) { ?>
                                        <option value="<?php echo $glAAResidencyKey; ?>" <?php if(in_array($glAAResidencyKey, $denialReasonExp)) { echo 'selected=selected'; } ?>><?php echo $glAAResidencyValue; ?></option>
                                    <?php } ?>
                                </optgroup>
                                <optgroup label="INS. GUARANTY OR PURCH DENIED BY">
                                    <?php
                                    $glAAInsData = glAdverseAction::$glAAIns;
                                    foreach ($glAAInsData as $glAAInsKey => $glAAInsValue ) { ?>
                                        <option value="<?php echo $glAAInsKey; ?>" <?php if(in_array($glAAInsKey, $denialReasonExp)) { echo 'selected=selected'; } ?>><?php echo $glAAInsValue; ?></option>
                                    <?php } ?>
                                </optgroup>
                                <optgroup label="OTHER">
                                    <?php
                                    $glAAOtherData = glAdverseAction::$glAAOther;
                                    foreach ($glAAOtherData as $glAAOtherKey => $glAAOtherValue ) { ?>
                                        <option value="<?php echo $glAAOtherKey; ?>" <?php if(in_array($glAAOtherKey, $denialReasonExp)) { echo 'selected=selected'; } ?>><?php echo $glAAOtherValue; ?></option>
                                    <?php } ?>
                                </optgroup>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6" id="otherReasonDiv" style="<?php if(! in_array(37, $denialReasonExp))  { echo 'display: none;';}?>">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold" for="otherReason">
                            Other Reason(s)
                        </label>
                        <div class="col-md-7">
                            <input class="form-control input-sm" type="text" name="otherReason" id="otherReason"
                                   value="<?php echo htmlspecialchars($otherReason);?>" autocomplete="off"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="clearfix"></div>
        <label class="font-weight-bold bg-secondary py-2 col-lg-12">Disclosure of Information Obtained from an Outside Source</label>
        <div class="col-md-12">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold">
                            Was information obtained in a report from a consumer reporting agency?
                        </label>
                        <div class="col-md-7">
                            <div class="radio-inline">
                                <label class="radio">
                                    <input type="radio" name="obtainedFromAgency" value="Yes" <?php if($obtainedFromAgency == 'Yes') { echo 'checked=checked'; } ?>>
                                    <span></span>Yes</label>
                                <label class="radio">
                                    <input type="radio" name="obtainedFromAgency" value="No" <?php if($obtainedFromAgency == 'No') { echo 'checked=checked'; } ?>>
                                    <span></span>No</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group row col-md-12 informationDiv" id="informationYes" style="<?php if($obtainedFromAgency != 'Yes') { echo 'display: none'; } ?>">
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold" for="creditReportDate">
                                Date Report Created
                            </label>
                            <div class="col-md-7">
                                <div class="input-group">
                                    <div class="input-group-prepend creditReportDate">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary icon-lg"></i>
                                    </span>
                                    </div>
                                    <input class="form-control input-sm dateNewClass" type="text" name="creditReportDate" placeholder="MM/DD/YYYY"
                                           data-before-creation-date="true"
                                           data-start-date="<?php echo glDate::getCreationDate(LMRequest::File()->recordDate); ?>"
                                           id="creditReportDate" value="<?php echo $creditReportDate;?>" maxlength="10" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold" for="agencyName">
                                Agency
                            </label>
                            <div class="col-md-7">
                                <input class="form-control input-sm" type="text" name="agencyName" id="agencyName"
                                       value="<?php echo htmlspecialchars($agencyName);?>" autocomplete="off"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold" for="agencyAddress">
                                Address
                            </label>
                            <div class="col-md-7">
                                <input class="form-control input-sm" type="text" name="agencyAddress" id="agencyAddress"
                                       value="<?php echo htmlspecialchars($agencyAddress);?>" autocomplete="off"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold" for="agencyPhone">
                                Phone
                            </label>
                            <div class="col-md-7">
                                <input class="form-control input-sm mask_cellnew" type="text" name="agencyPhone" id="agencyPhone"
                                       value="<?php echo $agencyPhone;?>" placeholder="(___) ___ - ____" autocomplete="off"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold" for="agencyScoreRangeFrom">
                                Range of Possible Scores
                            </label>
                            <label for="agencyScoreRangeTo"></label>
                            <div class="col-md-7">
                                <div class="input-daterange input-group">
                                    <input type="number" name="agencyScoreRangeFrom" id="agencyScoreRangeFrom" class="form-control form-controller-solid numbers"
                                           value="<?php echo $agencyScoreRangeFrom;?>" placeholder="Range From" autocomplete="off">
                                    <div class="input-group-append">
                                        <span class="input-group-text">
                                            <i class="la la-ellipsis-h"></i>
                                        </span>
                                    </div>
                                    <input type="number" name="agencyScoreRangeTo" id="agencyScoreRangeTo" class="form-control form-controller-solid numbers"
                                           value="<?php echo $agencyScoreRangeTo;?>" placeholder="Range To" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold" for="agencyScore">
                                Score
                            </label>
                            <div class="col-md-7">
                                <input class="form-control input-sm numbers" type="text" name="agencyScore" id="agencyScore"
                                       value="<?php echo $agencyScore;?>" autocomplete="off"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold" for="agencyInquiries">
                                Number of Recent Inquiries on Credit Report
                            </label>
                            <div class="col-md-7">
                                <input class="form-control input-sm numbers" type="number" name="agencyInquiries" id="agencyInquiries"
                                       value="<?php echo $agencyInquiries;?>" autocomplete="off"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group row col-md-12 informationDiv" id="informationNo" style="<?php if($obtainedFromAgency != 'No') { echo 'display: none'; } ?>">
                    <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5 font-weight-bold" for="additionalStatement">
                                Additional Statement if information source was other than a consumer reporting agency
                            </label>
                            <div class="col-md-7">
                                <textarea class="form-control input-sm" name="additionalStatement" id="additionalStatement"><?php echo $additionalStatement;?></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="clearfix"></div>
        <label class="font-weight-bold bg-secondary py-2 col-lg-12" for="deliveryMethod">Delivery Method</label>
        <div class="col-md-12">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold">
                            Delivery Method
                        </label>
                        <div class="col-md-7">
                            <select class="form-control input-sm" name="deliveryMethod" id="deliveryMethod">
                                <option value=""> - Please Select - </option>
                                <option value="Mailed" <?php if($deliveryMethod == 'Mailed') echo 'selected=selected';?> >Mailed</option>
                                <option value="E-Mailed" <?php if($deliveryMethod == 'E-Mailed') echo 'selected=selected';?> >E-Mailed</option>
                                <option value="Hand Delivered" <?php if($deliveryMethod == 'Hand Delivered') echo 'selected=selected';?> >Hand Delivered</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold" for="dateActionDelivered">
                            Date Delivered
                        </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <div class="input-group-prepend dateActionDelivered">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary icon-lg"></i>
                                    </span>
                                </div>
                                <input class="form-control input-sm dateNewClass" type="text" name="dateActionDelivered" placeholder="MM/DD/YYYY"
                                       data-before-creation-date="true"
                                       data-start-date="<?php echo glDate::getCreationDate(LMRequest::File()->recordDate); ?>"
                                       id="dateActionDelivered" value="<?php echo $dateActionDelivered;?>" maxlength="10" autocomplete="off">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold" for="completionOfLetter">
                            Completion of Letter By
                        </label>
                        <div class="col-md-7">
                            <input class="form-control input-sm" type="text" name="completionOfLetter" id="completionOfLetter"
                                   value="<?php echo htmlspecialchars($completionOfLetter);?>" autocomplete="off"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card card-custom NOI">
    <div class="card-header card-header-tabs-line bg-gray-100">
        <div class="card-title">
            <h3 class="card-label">
                Notice of Incompleteness
            </h3>
        </div>
        <div class="card-toolbar ">
            <a href="javascript:void(0);"
               class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
               data-card-tool="toggle"
               data-section="AdverseAction"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </a>
        </div>
    </div>
    <div class="card-body AdverseAction_body">
        <label class="font-weight-bold bg-secondary py-2 col-lg-12">Notice of Incompleteness</label>
        <div class="col-md-12">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold">
                            Loan Number :
                        </label>
                        <div class="col-md-7">
                            <span><?php echo $loanNumber;?></span>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold" for="noticeForBorrowerName">
                            Notice For (Borrower Name)
                        </label>
                        <div class="col-md-7">
                            <input class="form-control input-sm" type="text" name="noticeForBorrowerName" id="noticeForBorrowerName"
                                   value="<?php echo htmlspecialchars($noticeForBorrowerName);?>" autocomplete="off"/>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold" for="informationNeeded">
                            Information Still Needed
                        </label>
                        <div class="col-md-7">
                            <textarea class="form-control input-sm validateMaxLength"
                                      maxlength="<?php echo loanForm::getFieldLength('informationNeeded','tblAdverseAction'); ?>"
                                      name="informationNeeded" id="informationNeeded"><?php echo $informationNeeded;?></textarea>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold" for="daysForCondition">
                            Number of Days to Meet Conditions
                        </label>
                        <div class="col-md-7">
                            <input class="form-control input-sm numbers" type="number" name="daysForCondition" id="daysForCondition"
                                   value="<?php echo $daysForCondition;?>" autocomplete="off"/>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold" for="lenderLetterContactName">
                            Lender Contact Name on Letter
                        </label>
                        <div class="col-md-7">
                            <input class="form-control input-sm" type="text" name="lenderLetterContactName" id="lenderLetterContactName"
                                   value="<?php echo htmlspecialchars($lenderLetterContactName);?>" autocomplete="off"/>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold" for="lenderLetterEmail">
                            Lender Email on Letter
                        </label>
                        <div class="col-md-7">
                            <input class="form-control input-sm" type="email" name="lenderLetterEmail" id="lenderLetterEmail"
                                   value="<?php echo $lenderLetterEmail;?>" autocomplete="off"/>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold" for="lenderLetterPhone">
                            Lender Phone on Letter
                        </label>
                        <div class="col-md-7">
                            <input class="form-control input-sm mask_cellnew" type="text" name="lenderLetterPhone" id="lenderLetterPhone"
                                   value="<?php echo $lenderLetterPhone;?>" autocomplete="off"/>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5 font-weight-bold" for="dateNOIAEmailSent">
                            Date NOIA Email Sent
                        </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <div class="input-group-prepend dateNOIAEmailSent">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary icon-lg"></i>
                                    </span>
                                </div>
                                <input class="form-control input-sm dateNewClass"
                                       type="text"
                                       name="dateNOIAEmailSent"
                                       placeholder="MM/DD/YYYY"
                                       id="dateNOIAEmailSent"
                                       data-before-creation-date="true"
                                       data-start-date="<?php echo glDate::getCreationDate(LMRequest::File()->recordDate); ?>"
                                       value="<?php echo $dateNOIAEmailSent;?>"
                                       maxlength="10"
                                       autocomplete="off">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="clearfix"></div>
        <div class="col-md-12" id="buttons">
            <div class="col-md-12 text-center mt-25">
                <input type="submit" class="btn btn-primary btnSave" name="btnSave" id="saveBtn" value="Save">
                <input type="submit" class="btn btn-primary btnSave ml-1" name="btnSave" id="saveNextBtn"
                       value="Save & Next"
                       onclick="if(this.disabled===false) {return true;} else {return false;}">
            </div>
        </div>
    </div>
</div>
<script>
    $(document).ready(function (){
        //
        $('input[type=radio][name=obtainedFromAgency]').change(function() {
            $('.informationDiv').hide();
            let data = $(this).val();
            $('#information'+data).show();
        });

        $("#denialReason").chosen();
        $(document).on('change', '#denialReason', function (){
            let data = $(this).val();
            if(data.length){
                let valSplit = data.toString().split(",");
                if($.inArray('37', valSplit) !== -1) {
                    $('#otherReasonDiv').show();
                }else {
                    $('#otherReason').val('');
                    $('#otherReasonDiv').hide();
                }
            }
        });
        $('.numbers').inputmask({
            mask:"99[9]",
            placeholder: ''
        });

        //Email Validations
        /*$('#loanModForm').submit(function () {
            var email = $('#lenderLetterEmail').val();

        });*/
        $("#loanModForm").validate({
            rules: {
                lenderLetterEmail: {
                    email: true
                }
            },
            messages: {
                lenderLetterEmail: {
                    email: ""
                },
            },
            invalidHandler: function() {
                let msg = 'Please enter a valid email address.';
                toastrNotification(msg, 'error');
            }
        });
    });
</script>
