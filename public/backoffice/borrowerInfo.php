<?php
global $borrowerNumberOfDeals, $fileTab, $HMLOLoanInfoSectionsDisp, $allowToEdit, $PCID, $borrowerFName, $tabIndex, $borrowerLName,
       $hideThisField, $publicUser, $userGroup, $LMRId, $borrowerEmail, $allowClientToCreateHMLOFile, $tempClientEmail,
       $borrowerSecondaryEmail, $phoneNumber, $cellNumber, $borrFax, $serviceProvider, $methodContactArray,
       $landValueCls, $fldArr, $isOwnLand, $isCoBorrower, $altPhoneNumber, $workNumber,
       $displayNone, $presentAddress, $presentCity, $stateArray, $presentState, $presentZip,
       $presentPropLengthTime, $borPresentPropType, $borResidedPresentAddr,
       $showLimitedMandatoryField, $previousState, $borFormerPropType, $borrowerDOB, $borrowerPOB, $ssnNumber,
       $driverLicenseNumber, $driverLicenseState, $maritalStatus, $maritalDisp, $marriageDate, $divorceDate,
       $borrowerCitizenship, $isServicingMember, $servicingMemberInfo, $agesOfDependent, $numberOfDependents,
       $midFicoScore, $borEquifaxScore, $borTransunionScore, $borExperianScore,
       $borCreditScoreRange, $userRole, $executiveId, $borrowerMName,
       $presentUnit, $presentCountry, $presentPropLengthMonths, $currentRPM, $previousCountry,
       $previousUnit, $previousPropLengthMonths, $previousRPM, $borrowerAlternateNamesArray,
       $activeTab;
global $fileMC;

use models\composite\oFile\getFileInfo\propertyCountyInfo;
use models\constants\gl\glCountryArray;
use models\constants\gl\glDate;
use models\constants\gl\glFileTabs;
use models\constants\gl\glHMLOCreditScoreRange;
use models\constants\gl\glPCID;
use models\constants\gl\glPropTypeArray;
use models\constants\gl\glServicingMemberInfo;
use models\constants\hide1003FieldsArray;
use models\constants\methodOfContactArray;
use models\constants\showFieldsArray;
use models\constants\SMSServiceProviderArray;
use models\constants\streetTypeArray;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\loanForm;
use models\CustomField;
use models\cypher;
use models\Database2;
use models\lendingwise\tblBorrowerAuthorizationStatus;
use models\lendingwise\tblFile;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;

$glPropTypeArray = glPropTypeArray::$glPropTypeArray;
$SMSServiceProviderArray = SMSServiceProviderArray::$SMSServiceProviderArray;
$methodOfContactArray = methodOfContactArray::$methodOfContactArray;
$hide1003FieldsArray = hide1003FieldsArray::$hide1003FieldsArray;
$glCountryArray = glCountryArray::$glCountryArray;
$glHMLOCreditScoreRange = glHMLOCreditScoreRange::$glHMLOCreditScoreRange;
$glServicingMemberInfo = glServicingMemberInfo::$glServicingMemberInfo;
$streetTypeArray = streetTypeArray::$streetTypeArray;

$presentCounty = $myFileInfo['file2Info']['presentCounty'] ?? '';
$presentCountyInfo = propertyCountyInfo::getReport($activeTab, $presentState);
$states = [];
foreach ($stateArray as $stateKey => $stateValue) {
    $states[$stateValue['stateCode']] = $stateValue['stateName'];
}
$countyArray = [];
foreach ($presentCountyInfo as $countyKey => $countyValue) {
    $countyArray[$countyValue['countyName']] = $countyValue['countyName'];
}
$countryArray = [];
foreach ($glCountryArray as $countryKey => $countryValue) {
    $countryArray[$countryValue] = $countryKey;
}
$CICustomShowField = 1;
?>
<!-- borrowerInfo.php -->
<div class="borrowerInfoSection">
    <?php

    $dealsCount = Arrays::getArrayValue('numberOfDeals', $borrowerNumberOfDeals);

    if (count($secArr = BaseHTML::sectionAccess2(['sId' => 'BCI', 'opt' => $fileTab, 'activeTab' => $activeTab])) > 0) {// Get Active Fields only…
        loanForm::pushSectionID('BCI');

        if ($activeTab == '1003') {  /* story-31529 hide some fields based on the requirement mentioned in story*/
            foreach ($hide1003FieldsArray['BCI'] as $hideFieldkey => $hideFieldValue) {
                if (array_key_exists($hideFieldValue, $secArr)) {
                    if (is_array($secArr[$hideFieldValue])) {
                        $secArr[$hideFieldValue]['fieldDisplay'] = 0;
                        loanForm::hideField('BCI', $hideFieldValue);
                    }
                }
            }
        }
        if ($activeTab == 'CI' && !$LMRId) {
            $CICustomShowField = 0;
            foreach (showFieldsArray::$showCIFieldsArray['BCI'] as $showFieldKey => $showFieldValue) {
                if (array_key_exists($showFieldValue, $secArr)) {
                    $secArr[$showFieldValue]['fieldDisplay'] = 1;
                    loanForm::showFieldCustom('BCI', $showFieldValue);
                }
            }
        }
        $servicingMemberInfoArray = [];
        $servicingVal = '';
        if ($servicingMemberInfo != '') {
            $servicingMemberInfoArray = explode(',', trim($servicingMemberInfo));
        }
        $servicingMemberInfoDispOpt = ' display:none ';
        if (in_array('0', $servicingMemberInfoArray) &&
                trim(BaseHTML::parentFieldAccess(['fNm' => 'isServicingMember', 'sArr' => $secArr, 'pv' => $isServicingMember, 'av' => 'Yes'])) == 'subSecAc') {
            $servicingMemberInfoDispOpt = ' display:block ';
            $servicingVal = 0;
        }
        $serviceExpirationDate = '';
        $serviceExpirationDate = Strings::showField('serviceExpirationDate', 'fileHMLOInfo');
        if (Dates::IsEmpty($serviceExpirationDate)) {
            $serviceExpirationDate = '';
        } else {
            $serviceExpirationDate = Dates::formatDateWithRE($serviceExpirationDate, 'YMD', 'm/d/Y');
        }
        ?>
        <!-- Borrower Info Section Start -->
        <?php
        $nameValidation = '';
        if ($PCID != glPCID::PCID_MORTGAGE_ASSISTANCE) {
            $nameValidation = 'namevalidation';
        }
        ?>
        <div class="card card-custom HMLOLoanInfoSections BCI BCICard" style="<?php echo $HMLOLoanInfoSectionsDisp; ?>">
            <div class="card-header card-header-tabs-line bg-gray-100  ">
                <div class="card-title">
                    <h3 class="card-label">
                        <?php echo BaseHTML::getSectionHeading('BCI'); ?>
                    </h3>
                    <?php if (trim(BaseHTML::getSectionTooltip('BCI')) != '') { ?>&nbsp;
                        <i class="popoverClass fas fa-info-circle text-primary "
                           data-html="true"
                           data-content="<?php echo BaseHTML::getSectionTooltip('BCI'); ?>"></i>
                    <?php } ?>
                </div>
                <div class="card-toolbar">
                    <span class="cursor-pointer btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none"
                          data-card-tool="toggle"
                          data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                        <i class="ki ki-arrow-down icon-nm"></i>
                    </span>
                    <span class="cursor-pointer btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none"
                          data-card-tool="reload"
                          data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                        <i class="ki ki-reload icon-nm"></i>
                    </span>
                    <span class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer"
                          data-card-tool="toggle"
                          data-section="BCICard"
                          data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                        <i class="ki ki-arrow-down icon-nm"></i>
                    </span>
                </div>
            </div>
            <div class="card-body BCICard_body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group row">
                            <?php echo loanForm::label('borrowerFName', 'col-md-5'); ?>
                            <div class="col-md-7 isClientInfo">
                                <?php
                                echo loanForm::text(
                                        'borrowerFName',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $borrowerFName,
                                        'input-sm  ' . $nameValidation,
                                );
                                ?>
                                <input type="hidden" value='' id='isBorrowermailEdited' name='isBorrowermailEdited'>
                            </div>
                        </div>
                    </div>
                    <?php if ($hideThisField) { ?>

                        <div class="col-md-6 <?php echo loanForm::showField('borrowerMName'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label('borrowerMName', 'col-md-5'); ?>
                                <div class="col-md-7 isClientInfo">
                                    <?php
                                    echo loanForm::text(
                                            'borrowerMName',
                                            $allowToEdit,
                                            $tabIndex++,
                                            $borrowerMName,
                                            'input-sm  ' . $nameValidation,
                                    );
                                    ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 <?php echo loanForm::showField('borrowerLName'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label('borrowerLName', 'col-md-5'); ?>
                                <div class="col-md-7 isClientInfo">
                                    <?php
                                    echo loanForm::text(
                                            'borrowerLName',
                                            $allowToEdit,
                                            $tabIndex++,
                                            $borrowerLName,
                                            'input-sm ' . $nameValidation,
                                    );
                                    ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                    <?php if ($hideThisField) { ?>
                        <div class="col-md-6 <?php echo loanForm::showField('borrowerEmail'); ?>">
                            <div class="form-group row ">
                                <?php
                                $borrowerEmailToolTip = '';
                                if (!$publicUser) {
                                    $borrowerEmailToolTip = 'This email will be used to create a borrower profile, so you can create additional loans for this borrower in the future. 
                                    If you see duplicate emails, you can clean up the borrower list by removing the unneeded/duplicated borrower profiles. 
                                    NOTE: The order of duplicate emails is sorted by oldest to newest created date.';
                                }
                                echo loanForm::label('borrowerEmail', 'col-md-5', $borrowerEmailToolTip); ?>
                                <div class="col-md-7 isClientInfo">
                                    <?php if ($allowToEdit && ($userGroup != 'Client' || $publicUser == 1)) { ?>
                                        <?php if ($LMRId > 0) { //ToDo:: loanForm.php?>
                                            <div class="input-group">
                                                <input type="email"
                                                       class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'borrowerEmail', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       name="borrowerEmail" id="borrowerEmail"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       onblur="populateClientInfo('loanModForm', this.value, '<?php echo $PCID ?>', '<?php echo $LMRId ?>');fieldValidation(this.id,this.name);"
                                                       value="<?php echo $borrowerEmail; ?>"
                                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'borrowerEmail', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                                       readonly>
                                                <div class="input-group-append tooltipClass" title="Update Email"
                                                     onclick="updateExistingEmail('<?php echo $borrowerEmail ?>','<?php echo $dealsCount; ?>')"
                                                     id="updateExistingEmail">
                                                    <span class="input-group-text">
                                                        <i class="flaticon2-edit"></i>
                                                    </span>
                                                </div>
                                            </div>
                                        <?php } else {
                                            if ($publicUser) { //hide the dob,ssn.
                                                ?>
                                                <input type="email"
                                                       class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'borrowerEmail', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       name="borrowerEmail" id="borrowerEmail"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       onblur="populateClientInfo('client-loanModForm', this.value, '<?php echo $PCID ?>', '<?php echo $LMRId ?>');fieldValidation(this.id,this.name);"
                                                       value="<?php echo $borrowerEmail; ?>"
                                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'borrowerEmail', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                <?php
                                            } else { //show the dob,ssn BO?>
                                                <input type="email"
                                                       class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'borrowerEmail', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       name="borrowerEmail" id="borrowerEmail"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       onblur="populateClientInfo('loanModForm', this.value, '<?php echo $PCID ?>', '<?php echo $LMRId ?>');fieldValidation(this.id,this.name);"
                                                       value="<?php echo $borrowerEmail; ?>"
                                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'borrowerEmail', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                <?php
                                            }
                                        }

                                        ?>


                                    <?php } else if ($userGroup == 'Client' && $allowClientToCreateHMLOFile == 1 && $LMRId == 0) { ?>
                                        <h5 class="with-children-tip"><?php echo $tempClientEmail; ?>&nbsp;&nbsp;</h5>
                                        <input type="hidden" name="borrowerEmail" id="borrowerEmail"
                                               value="<?php echo $tempClientEmail; ?>" maxlength="75" size="40">
                                    <?php } else { ?>
                                        <h5><?php echo $borrowerEmail; ?></h5>
                                        <input type="hidden" name="borrowerEmail" id="borrowerEmail"
                                               value="<?php echo $borrowerEmail; ?>" maxlength="75" size="40">
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                        <div id="showClientEmailExists" class="left"></div>
                        <div id="divLo1" class="left pad2" style="display:none;">
                            Please Wait... <img src="<?php echo CONST_SITE_URL; ?>assets/images/ajax-loader.gif"
                                                alt="">
                        </div>
                    <?php } ?>

                    <?php if ($hideThisField) { ?>
                        <div class="col-md-6 <?php echo loanForm::showField('borrowerSecondaryEmail'); ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('borrowerSecondaryEmail', 'col-md-5'); ?>
                                <div class="col-md-7 isClientInfo">
                                    <?php echo loanForm::email(
                                            'borrowerSecondaryEmail',
                                            $allowToEdit,
                                            $tabIndex++,
                                            $borrowerSecondaryEmail,
                                            'input-sm',
                                            'fieldValidation(this.id,this.name)',
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>

                    <?php if ($hideThisField) { ?>
                        <div class="col-md-6 <?php echo loanForm::showField('phoneNumber'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label('phoneNumber', 'col-md-5'); ?>
                                <div class="col-md-7 isClientInfo">
                                    <?php echo loanForm::phone(
                                            'phoneNumber',
                                            $allowToEdit,
                                            $tabIndex++,
                                            $phoneNumber,
                                            'input-sm ',
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>


                    <?php if ($hideThisField) { ?>
                        <div class="col-md-6 <?php echo loanForm::showField('cellNo'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label('cellNo', 'col-md-5'); ?>
                                <div class="col-md-7 isClientInfo">
                                    <?php echo loanForm::cell(
                                            'cellNo',
                                            $allowToEdit,
                                            $tabIndex++,
                                            $cellNumber,
                                            'input-sm',
                                            'fieldValidation(this.id,this.name),getBorrwerWithCellNumber(this.id,this.name)',
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>

                    <?php if ($hideThisField) { ?>
                        <div class="col-md-6 <?php echo loanForm::showField('borrFax'); ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('borrFax', 'col-md-5'); ?>
                                <div class="col-md-7 isClientInfo">
                                    <?php echo loanForm::cell(
                                            'borrFax',
                                            $allowToEdit,
                                            $tabIndex++,
                                            $borrFax,
                                            ' input-sm ',
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>

                    <?php if ($hideThisField) { ?>
                        <div class="col-md-6 <?php echo loanForm::showField('serviceProvider'); ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('serviceProvider', 'col-md-5'); ?>
                                <div class="col-md-7 isClientInfo">
                                    <?php echo loanForm::select(
                                            'serviceProvider',
                                            $allowToEdit,
                                            $tabIndex++,
                                            $serviceProvider,
                                            $SMSServiceProviderArray,
                                            '',
                                            ' input-sm ',
                                            '- Select -',
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>

                    <?php if ($hideThisField) { ?>
                        <div class="col-md-6 <?php echo loanForm::showField('methodOfContact'); ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('methodOfContact', 'col-md-5'); ?>
                                <div class="col-md-7 isClientInfo">
                                    <input type="hidden" name="methodOfContactHidden" id="methodOfContactHidden"
                                           value="<?php echo implode(',', $methodContactArray); ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'methodOfContact', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                                    <?php //ToDo:: with out the [] in the name - the values don't save
                                    echo loanForm::selectMulti(
                                            'methodOfContact',
                                            $allowToEdit,
                                            $tabIndex++,
                                            $methodContactArray,
                                            $methodOfContactArray,
                                            '',
                                            ' chzn-select ',
                                            '',
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>

                    <div class="col-md-6 <?php echo loanForm::showField('isCoBorrower'); ?>">
                        <div class="form-group row">
                            <?php echo loanForm::label('isCoBorrower', 'col-md-5'); ?>
                            <div class="col-md-7">
                                <?php
                                //ToDo:: checkbox switch
                                if ($allowToEdit) { ?>
                                    <span class="switch switch-icon">
                                        <label class="font-weight-bold">
                                            <input class="form-control"
                                                <?php if ($isCoBorrower == '1') { ?>   checked="checked" <?php } ?>
                                                   class="<?php echo BaseHTML::fieldAccess(['fNm' => 'isCoBorrower', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   id="isCoBo" type="checkbox"
                                                   onchange="toggleSwitch('isCoBo', 'isCoBorrower', '1', '0' );
                                                           toggleSwitch('isCoBo', 'isCoBorrowerExists', '1', '0' );
                                                           hideShowCoBorrowerSections('isCoBorrower', 'coBorrowerSections');
                                                   <?php if ($publicUser) { ?> hideAndShowCoBorSignature('isCoBorrower'); <?php } ?>
                                                   <?php if ($activeTab != glFileTabs::CI) { ?>
                                                           showHMLOIncomeCalucation('loanModForm');
                                                   <?php } ?>"
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'isCoBorrower', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                <input type="hidden" name="isCoBorrower" id="isCoBorrower"
                                                       value="<?php echo $isCoBorrower; ?>">
                                                <input type="hidden" name="isCoBorrowerExists" id="isCoBorrowerExists"
                                                       value="<?php echo $isCoBorrower; ?>">
                                            <span></span>
                                        </label>
                                    </span>


                                <?php } else {
                                    if ($isCoBorrower == '1') {
                                        echo '<h5>Yes</h5>';
                                    } else {
                                        echo '<h5>No</h5>';
                                    }
                                }
                                ?>
                            </div>
                        </div>
                    </div>


                    <?php if ($hideThisField) { ?>
                        <div class="col-md-6 <?php echo loanForm::showField('altPhoneNumber'); ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('altPhoneNumber', 'col-md-5'); ?>
                                <div class="col-md-7">
                                    <?php echo loanForm::phone(
                                            'altPhoneNumber',
                                            $allowToEdit,
                                            $tabIndex++,
                                            $altPhoneNumber,
                                            ' input-sm ',
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>

                    <?php if ($hideThisField) { ?>
                        <div class="col-md-6  <?php echo loanForm::showField('workNumber'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label('workNumber', 'col-md-5'); ?>
                                <div class="col-md-7">
                                    <?php echo loanForm::phone(
                                            'workNumber',
                                            $allowToEdit,
                                            $tabIndex++,
                                            $workNumber,
                                            ' input-sm ',
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>




                    <?php if ($hideThisField) { ?>
                        <?php if ($publicUser == 1 && in_array($PCID, CONST_API_ACCESS_PC)) {
                            $sqry = ' select * from tblFileStreetData where LMRID = :LMRID ';
                            $streetresultArray = Database2::getInstance()->queryData($sqry, [
                                    'LMRID' => $LMRId,
                            ]);
                            $streetName = $streetresultArray[0]['streetName'];
                            $streetNumber = $streetresultArray[0]['streetNumber'];
                            $streetType = $streetresultArray[0]['streetType'];
                            ?>
                            <div class="col-md-6 <?php echo loanForm::showField('presentAddress'); ?>"
                                 style="<?php echo $displayNone; ?>">
                                <div class="form-group row ">
                                    <script>
                                        $(document).ready(function () {
                                            $('#presentAddress').on('input', function () {
                                                address_lookup.InitLegacy($(this));
                                            });
                                        });
                                    </script>
                                    <label class="col-md-5 font-weight-bold"
                                           for="streeNumber">Street Number</label>
                                    <div class="col-md-7 isClientInfo">
                                        <?php echo loanForm::text(
                                                'presentAddress',
                                                $allowToEdit,
                                                $tabIndex++,
                                                $streetNumber,
                                                ' input-sm ',
                                                null,
                                                null,
                                                null,
                                                [
                                                        'address' => 'presentAddress',
                                                        'city'    => 'presentCity',
                                                        'state'   => 'presentState',
                                                        'zip'     => 'presentZip',
                                                        'unit'    => 'presentUnit',
                                                        'county'  => 'presentCounty',
                                                ]
                                        ); ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 <?php echo loanForm::showField('presentAddress'); ?>"
                                 style="<?php echo $displayNone; ?>">
                                <div class="form-group row ">
                                    <script>
                                        $(document).ready(function () {
                                            $('#streetName').on('input', function () {
                                                address_lookup.InitLegacy($(this));
                                            });
                                        });
                                    </script>

                                    <label class="col-md-5 font-weight-bold"
                                           for="streeName">Street Name</label>
                                    <div class="col-md-7 isClientInfo">
                                        <?php echo loanForm::text(
                                                'streetName',
                                                $allowToEdit,
                                                $tabIndex++,
                                                $streetName,
                                                ' input-sm ',
                                                null,
                                                null,
                                                null,
                                                [
                                                        'address' => 'streetName',
                                                        'city'    => 'presentCity',
                                                        'state'   => 'presentState',
                                                        'zip'     => 'presentZip',
                                                        'unit'    => 'presentUnit',
                                                        'county'  => 'presentCounty',
                                                ]
                                        ); ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 <?php echo loanForm::showField('presentAddress'); ?>"
                                 style="<?php echo $displayNone; ?>">
                                <div class="form-group row ">
                                    <label class="col-md-5 font-weight-bold"
                                           for="streeNumber">Street Type</label>
                                    <div class="col-md-7 isClientInfo">
                                        <?php echo loanForm::select(
                                                'streetType',
                                                $allowToEdit,
                                                $tabIndex++,
                                                $streetType,
                                                $streetTypeArray,
                                                '',
                                                ' input-sm ',
                                                '- Select -',
                                        ); ?>
                                    </div>
                                </div>
                            </div>
                        <?php } else { ?>
                            <div class="col-md-6 <?php echo loanForm::showField('presentAddress'); ?>"
                                 style="<?php echo $displayNone; ?>">
                                <div class="form-group row ">
                                    <script>
                                        $(document).ready(function () {
                                            $('#presentAddress').on('input', function () {
                                                address_lookup.InitLegacy($(this));
                                            });
                                        });
                                    </script>
                                    <?php echo loanForm::label('presentAddress', 'col-md-5'); ?>
                                    <div class="col-md-7 isClientInfo">
                                        <?php echo loanForm::text(
                                                'presentAddress',
                                                $allowToEdit,
                                                $tabIndex++,
                                                $presentAddress,
                                                ' input-sm ',
                                                null,
                                                null,
                                                null,
                                                [
                                                        'address' => 'presentAddress',
                                                        'city'    => 'presentCity',
                                                        'state'   => 'presentState',
                                                        'zip'     => 'presentZip',
                                                        'unit'    => 'presentUnit',
                                                        'county'  => 'presentCounty',
                                                ]
                                        ); ?>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                    <?php } ?>
                    <?php if ($hideThisField) { ?>
                        <div class="col-md-6 <?php echo loanForm::showField('presentUnit'); ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('presentUnit', 'col-md-5'); ?>
                                <div class="col-md-7 isClientInfo">
                                    <?php echo loanForm::text(
                                            'presentUnit',
                                            $allowToEdit,
                                            $tabIndex++,
                                            $presentUnit,
                                            ' input-sm ',
                                    ); ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 <?php echo loanForm::showField('presentCity'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label('presentCity', 'col-md-5'); ?>
                                <div class="col-md-7 isClientInfo">
                                    <?php echo loanForm::text(
                                            'presentCity',
                                            $allowToEdit,
                                            $tabIndex++,
                                            $presentCity,
                                            ' input-sm ',
                                    ); ?>
                                </div>
                            </div>
                        </div>


                        <div class="col-md-6 <?php echo loanForm::showField('presentState'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label('presentState', 'col-md-5'); ?>
                                <div class="col-md-7 isClientInfo">
                                    <?php echo loanForm::select(
                                            'presentState',
                                            $allowToEdit,
                                            $tabIndex++,
                                            $presentState,
                                            $states,
                                            'populateStateCounty(\'loanModForm\', \'presentState\', \'presentCounty\')',
                                            ' input-sm ',
                                            '- Select -',
                                    ); ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 <?php echo loanForm::showField('presentZip'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label('presentZip', 'col-md-5'); ?>
                                <div class="col-md-7 isClientInfo">
                                    <?php echo loanForm::zipCode(
                                            'presentZip',
                                            $allowToEdit,
                                            $tabIndex++,
                                            $presentZip,
                                            ' input-sm ',
                                            'fieldValidation(this.id,this.name)',
                                    ); ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                    <div class="col-md-6 <?php echo loanForm::showField('presentCounty'); ?>">
                        <div class="form-group row">
                            <?php echo loanForm::label('presentCounty', 'col-md-5'); ?>
                            <div class="col-md-7 isClientInfo">
                                <?php echo loanForm::select(
                                        'presentCounty',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $presentCounty,
                                        $countyArray,
                                        '',
                                        ' input-sm ',
                                        '- Select -',
                                ); ?>
                            </div>
                        </div>
                    </div>


                    <div class="col-md-6 <?php echo loanForm::showField('presentCountry'); ?>">
                        <div class="form-group row">
                            <?php echo loanForm::label('presentCountry', 'col-md-5'); ?>
                            <div class="col-md-7 isClientInfo">
                                <?php echo loanForm::select(
                                        'presentCountry',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $presentCountry,
                                        $countryArray,
                                        '',
                                        ' input-sm ',
                                        '- Select -',
                                ); ?>
                            </div>
                        </div>
                    </div>

                    <?php if ($CICustomShowField) { ?>

                        <div class=" col-md-6  presentPropLengthTime_disp <?php echo loanForm::showField('presentPropLengthTime'); ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('presentPropLengthTime', 'col-md-5'); ?>
                                <div class="col-md-7 isClientInfo">
                                    <?php echo loanForm::text(
                                            'presentPropLengthTime',
                                            $allowToEdit,
                                            $tabIndex++,
                                            $presentPropLengthTime,
                                            ' input-sm ',
                                    ); ?>
                                </div>
                            </div>
                        </div>

                        <div class=" col-md-6  presentPropLengthMonths_disp <?php echo loanForm::showField('presentPropLengthMonths'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label('presentPropLengthMonths', 'col-md-5'); ?>
                                <div class="col-md-7 isClientInfo">
                                    <?php if ($allowToEdit) { ?>
                                        <input type="number"
                                               class="form-control input-sm
                                           echo BaseHTML::fieldAccess(['fNm' => 'presentPropLengthMonths', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               onkeyup="return isNumberOnly()"
                                               name="presentPropLengthMonths" id="presentPropLengthMonths"
                                               value="<?php echo $presentPropLengthMonths; ?>"
                                               autocomplete="off"
                                               tabindex="<?php echo $tabIndex++ ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'presentPropLengthMonths', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                    <?php } else {
                                        echo '<h5>' . $presentPropLengthMonths . '</h5>';
                                    } ?>
                                </div>
                            </div>
                        </div>

                        <div class=" col-md-6  borPresentPropType_disp <?php echo loanForm::showField('borPresentPropType'); ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('borPresentPropType', 'col-md-5'); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <select class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'borPresentPropType', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                name="borPresentPropType" id="borPresentPropType"
                                                tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'borPresentPropType', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                                            <option value=""> - Select -</option>
                                            <?php
                                            for ($i = 0; $i < count($glPropTypeArray); $i++) {
                                                $sOpt = '';
                                                $glPropType = '';
                                                $glPropType = trim($glPropTypeArray[$i]);
                                                $sOpt = Arrays::isSelected($glPropType, $borPresentPropType);
                                                echo "<option value=\"" . $glPropType . "\" " . $sOpt . '>' . $glPropType . '</option>';
                                            }
                                            ?>
                                        </select>
                                    <?php } else { ?><h5><?php echo $borPresentPropType; ?></h5><?php } ?>
                                </div>
                            </div>
                        </div>

                        <div class=" col-md-6  currentRPM_disp <?php echo loanForm::showField('currentRPM'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label('currentRPM', 'col-md-5'); ?>
                                <div class="col-md-7 isClientInfo">
                                    <?php if ($allowToEdit) { ?>
                                        <input type="text"
                                               placeholder="0.00"
                                               class="form-control input-sm
                                           <?php echo BaseHTML::fieldAccess(['fNm' => 'currentRPM', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               name="currentRPM" id="currentRPM"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal($currentRPM); ?>"
                                               onblur="currencyConverter(this, this.value);"
                                               autocomplete="off" onkeyup='return numericValues(this,event)'
                                               tabindex="<?php echo $tabIndex++ ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'currentRPM', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                    <?php } else {
                                        echo '<h5>' . Currency::formatDollarAmountWithDecimal($currentRPM) . '</h5>';
                                    } ?>
                                </div>
                            </div>
                        </div>


                        <div class=" col-md-6  borResidedPresentAddr_disp  <?php echo loanForm::showField('borResidedPresentAddr'); ?>">
                            <div class="form-group row ">
                                <?php echo loanForm::label('borResidedPresentAddr', 'col-md-5'); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="radio-inline">
                                            <label class="radio radio-solid font-weight-bold"
                                                   for="borResidedPresentAddrYes">
                                                <input type="radio"
                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'borResidedPresentAddr', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       name="borResidedPresentAddr" id="borResidedPresentAddrYes"
                                                       value="Yes"
                                                       tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $borResidedPresentAddr); ?>
                                                       onclick="showFormerAddrDiv(this.value, 'Bor'); hideAndShowAcceptPurchaseAgreement(this.value, 'borResidedPresentAddrDispOpt'); " <?php echo BaseHTML::fieldAccess(['fNm' => 'borResidedPresentAddr', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>Yes
                                            </label>
                                            <label class="radio radio-solid font-weight-bold"
                                                   for="borResidedPresentAddrNo">
                                                <input type="radio" name="borResidedPresentAddr"
                                                       id="borResidedPresentAddrNo"
                                                       value="No"
                                                       tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $borResidedPresentAddr); ?>
                                                       onclick="showFormerAddrDiv(this.value, 'Bor'); hideAndShowAcceptPurchaseAgreement(this.value, 'borResidedPresentAddrDispOpt'); " <?php echo BaseHTML::fieldAccess(['fNm' => 'borResidedPresentAddr', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>No
                                            </label></div>
                                    <?php } else { ?>
                                        <b><?php echo $borResidedPresentAddr ?></b>
                                    <?php } ?>
                                </div>
                            </div>

                        </div>

                    <?php } ?>
                </div>

                <?php if ($CICustomShowField) { ?>
                    <div class="row borResidedPresentAddrDispOpt   <?php echo BaseHTML::parentFieldAccess(['fNm' => 'borResidedPresentAddr', 'sArr' => $secArr, 'pv' => $borResidedPresentAddr, 'av' => 'Yes']); ?>">
                        <?php
                        if ($allowToEdit) { ?>
                            <div class=" col-md-6  previousAddress_disp <?php echo loanForm::showField('previousAddress'); ?>">
                                <div class="form-group row ">
                                    <?php echo loanForm::label('previousAddress', 'col-md-5'); ?>
                                    <div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <input type="text"
                                                   name="previousAddress"
                                                   id="previousAddress" tabindex="<?php echo $tabIndex++; ?>"
                                                   value="<?php echo htmlentities(Strings::showField('previousAddress', 'LMRInfo')); ?>"
                                                   size="40"
                                                   maxlength="75"
                                                   autocomplete="off"
                                                    <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?>
                                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'previousAddress', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                    <?php } else { ?>
                                                        class="form-control input-sm"
                                                    <?php } ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'previousAddress', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        <?php } else { ?>
                                            <h5><?php echo Strings::showField('previousAddress', 'LMRInfo') ?></h5>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>
                            <div class=" col-md-6  previousUnit_disp <?php echo loanForm::showField('previousUnit'); ?>">
                                <div class="form-group row ">
                                    <?php echo loanForm::label('previousUnit', 'col-md-5'); ?>
                                    <div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <input
                                                    type="text"
                                                    name="previousUnit"
                                                    id="previousUnit"
                                                    tabindex="<?php echo $tabIndex++; ?>"
                                                    value="<?php echo htmlentities($previousUnit); ?>"
                                                    size="20"
                                                    maxlength="30"
                                                    autocomplete="off"
                                                    <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?>
                                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'previousUnit', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                    <?php } else { ?>
                                                        class="form-control input-sm"
                                                    <?php } ?>
                                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'previousUnit', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        <?php } else { ?><h5><?php echo $previousUnit; ?></h5><?php } ?>
                                    </div>
                                </div>
                            </div>
                            <div class=" col-md-6  previousCity_disp <?php echo loanForm::showField('previousCity'); ?>">
                                <div class="form-group row ">
                                    <?php echo loanForm::label('previousCity', 'col-md-5'); ?>
                                    <div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <input
                                                    type="text"
                                                    name="previousCity"
                                                    id="previousCity"
                                                    tabindex="<?php echo $tabIndex++; ?>"
                                                    value="<?php echo htmlentities(Strings::showField('previousCity', 'LMRInfo')); ?>"
                                                    size="20"
                                                    maxlength="30"
                                                    autocomplete="off"
                                                    <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?>
                                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'previousCity', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                    <?php } else { ?>
                                                        class="form-control input-sm"
                                                    <?php } ?>
                                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'previousCity', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        <?php } else { ?>
                                            <h5><?php echo Strings::showField('previousCity', 'LMRInfo') ?></h5>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>

                            <div class=" col-md-6  <?php echo loanForm::showField('previousState'); ?>">
                                <div class="form-group row ">
                                    <?php echo loanForm::label('previousState', 'col-md-5'); ?>
                                    <div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <select class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'previousState', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                    name="previousState" id="previousState"
                                                    tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'previousState', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                                                <option value=''> - Select -</option>
                                                <?php
                                                for ($j = 0; $j < count($stateArray); $j++) {
                                                    $sOpt = '';
                                                    $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), $previousState);
                                                    echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                                                }
                                                ?>
                                            </select>
                                        <?php } else { ?><h5><?php echo $previousState; ?></h5><?php } ?>
                                    </div>
                                </div>
                            </div>

                            <div class=" col-md-6  previousZip_disp <?php echo loanForm::showField('previousZip'); ?>">
                                <div class="form-group row ">
                                    <?php echo loanForm::label('previousZip', 'col-md-5'); ?>
                                    <div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <input type="text"
                                                   name="previousZip"
                                                   id="previousZip"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   value="<?php echo Strings::showField('previousZip', 'LMRInfo') ?>"
                                                   autocomplete="off"
                                                    <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?>
                                                        class="form-control zipCode input-sm mandatory"
                                                    <?php } else { ?>
                                                        class="form-control zipCode input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'previousZip', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                    <?php } ?>
                                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'previousZip', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        <?php } else { ?>
                                            <h5><?php echo Strings::showField('previousZip', 'LMRInfo') ?></h5><?php } ?>
                                    </div>
                                </div>
                            </div>
                            <div class=" col-md-6  previousCountry_disp <?php echo loanForm::showField('previousCountry'); ?>">
                                <div class="form-group row ">
                                    <?php echo loanForm::label('previousCountry', 'col-md-5'); ?>
                                    <div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <select
                                                    class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'previousCountry', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                    name="previousCountry" id="previousCountry"
                                                    tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'previousCountry', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                <option value=''> - Select -</option>
                                                <?php
                                                foreach ($glCountryArray as $previousCountryName => $previousCountryCode) {
                                                    $sOpt = '';
                                                    $sOpt = Arrays::isSelected(trim($previousCountryCode), $previousCountry);
                                                    echo "<option value=\"" . trim($previousCountryCode) . "\" " . $sOpt . '>' . trim($previousCountryName) . '</option>';
                                                }
                                                ?>
                                            </select> <?php } else { ?>
                                            <h5><?php echo $previousCountry; ?></h5><?php } ?>
                                    </div>
                                </div>
                            </div>

                            <div class=" col-md-6  <?php echo loanForm::showField('previousPropLengthTime'); ?>">
                                <div class="form-group row ">
                                    <?php echo loanForm::label('previousPropLengthTime', 'col-md-5'); ?>
                                    <div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <input type="text" name="previousPropLengthTime"
                                                   id="previousPropLengthTime"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   value="<?php echo htmlentities(Strings::showField('previousPropLengthTime', 'file2Info')); ?>"
                                                   size="10"
                                                   maxlength="10"
                                                   autocomplete="off"
                                                    <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?>
                                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'previousPropLengthTime', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                    <?php } else { ?>
                                                        class="form-control input-sm"
                                                    <?php } ?>
                                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'previousPropLengthTime', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        <?php } else { ?>
                                            <h5><?php echo Strings::showField('previousPropLengthTime', 'file2Info'); ?></h5>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>
                            <div class=" col-md-6  previousPropLengthMonths_disp <?php echo loanForm::showField('previousPropLengthMonths'); ?>">
                                <div class="form-group row ">
                                    <?php echo loanForm::label('previousPropLengthMonths', 'col-md-5'); ?>
                                    <div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <input type="number"
                                                   name="previousPropLengthMonths"
                                                   onkeyup="return isNumberOnly(event)"
                                                   id="previousPropLengthMonths"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   value="<?php echo $previousPropLengthMonths; ?>"
                                                   size="20"
                                                   maxlength="30"
                                                   autocomplete="off"
                                                    <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?>
                                                        class="<?php echo BaseHTML::fieldAccess(['fNm' => 'previousPropLengthMonths', 'sArr' => $secArr, 'opt' => 'M']); ?> form-control input-sm"
                                                    <?php } else { ?>
                                                        class="form-control input-sm"
                                                    <?php } ?>
                                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'previousPropLengthMonths', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        <?php } else { ?><h5><?php echo $previousPropLengthMonths; ?></h5><?php } ?>
                                    </div>
                                </div>
                            </div>

                            <div class=" col-md-6  <?php echo loanForm::showField('borNoOfYrAtPrevAddr'); ?>">
                                <div class="form-group row ">
                                    <?php echo loanForm::label('borNoOfYrAtPrevAddr', 'col-md-5'); ?>
                                    <div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <input type="number" name="borNoOfYrAtPrevAddr"
                                                   id="borNoOfYrAtPrevAddr"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   value="<?php echo Strings::showField('borNoOfYrAtPrevAddr', 'fileLoanOriginationInfo'); ?>"
                                                   maxlength="30" autocomplete="off"
                                                   class="<?php echo BaseHTML::fieldAccess(['fNm' => 'borNoOfYrAtPrevAddr', 'sArr' => $secArr, 'opt' => 'M']); ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'borNoOfYrAtPrevAddr', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        <?php } else { ?>
                                            <h5><?php echo Strings::showField('borNoOfYrAtPrevAddr', 'fileLoanOriginationInfo'); ?></h5>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>
                            <div class=" col-md-6  <?php echo loanForm::showField('borFormerPropType'); ?>">
                                <div class="form-group row ">
                                    <?php echo loanForm::label('borFormerPropType', 'col-md-5'); ?>
                                    <div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <select class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'borFormerPropType', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                    name="borFormerPropType" id="borFormerPropType"
                                                    tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'borFormerPropType', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                <option value=""> - Select -</option>
                                                <?php
                                                for ($i = 0; $i < count($glPropTypeArray); $i++) {
                                                    $sOpt = '';
                                                    $glPropType = '';
                                                    $glPropType = trim($glPropTypeArray[$i]);
                                                    $sOpt = Arrays::isSelected($glPropType, $borFormerPropType);
                                                    echo "<option value=\"" . $glPropType . "\" " . $sOpt . '>' . $glPropType . '</option>';
                                                }
                                                ?>
                                            </select>
                                        <?php } else { ?>
                                            <h5><?php echo $borFormerPropType; ?></h5>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>
                            <div class=" col-md-6  previousRPM_disp <?php echo loanForm::showField('previousRPM'); ?>">
                                <div class="form-group row ">
                                    <?php echo loanForm::label('previousRPM', 'col-md-5'); ?>
                                    <div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
          <input type="text"
           class="form-control input-sm"
           name="previousRPM"
           placeholder="0.00"
           id="previousRPM"
           onkeyup='return numericValues(this)'
           tabindex="<?php  echo $tabIndex++;?>"
            onblur="currencyConverter(this, this.value);"
           value="<?php  echo Currency::formatDollarAmountWithDecimal($previousRPM);?>"
           size="20"
           maxlength="30"
           autocomplete="off"
           <?php  if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class = "<?php echo BaseHTML::fieldAccess(['fNm'=>'previousRPM', 'sArr'=>$secArr, 'opt'=>'M']); ?>" <?php  } else { ?>  <?php  } ?> <?php echo BaseHTML::fieldAccess(['fNm'=>'previousRPM', 'sArr'=>$secArr, 'opt'=>'I']); ?>">
<?php  } else {  ?><h5><?php  echo Currency::formatDollarAmountWithDecimal($previousRPM);?></h5><?php  } ?>
                                    </div>
                                </div>
                            </div>

                        <?php } ?>
                    </div>

                    <!-- Borrower Mailing Address Info Section Start -->
                    <?php if ($hideThisField) { ?>
                        <?php require 'borrowerMailingAddress.php' ?>
                    <?php } ?>
                    <!-- Borrower Mailing Address Info Section End -->
                    <?php if ($hideThisField) { ?>
                        <!-- alternate names Start -->
                        <div class="form-group row align-self-center justify-content-between col-lg-12 m-0 mt-2 mb-4 px-0"
                             id="alternateNamesSection">
                            <label class="bg-secondary align-self-center  py-2  col-lg-12">
                                <b><?php echo BaseHTML::getSubSectionHeading('BCI', 'alternateNames'); ?></b>
                                <a href="javascript:void(0);" id="alternameNameAddBtn"
                                   onclick="cloneFormAlternate('alternativeClass','icrementSec')"
                                   class="btn btn-sm btn-success btn-text-primary  btn-icon ml-2 tooltipClass float-right  <?php if (count($borrowerAlternateNamesArray ?? []) >= 3) {
                                       echo 'd-none';
                                   } ?>" title=""
                                   data-original-title="Click to add new row">
                                    <i class=" fas fa-plus  icon-md tooltipclass"
                                       title="Click to Add New Rent Roll"></i>
                                </a>
                            </label>
                            <div class="col-md-12">
                                <?php require 'alternateNames.php' ?>
                            </div>
                        </div>
                        <!-- alternate names end -->
                    <?php } ?>

                    <!-- personalInfo start -->
                    <div class="form-group row col-lg-12 m-0 mb-4 px-0" id="personalInfotitle">
                        <label class="bg-secondary py-2 col-lg-12"><b><?php echo BaseHTML::getSubSectionHeading('BCI', 'personalInfoSubSection'); ?></b></label>
                    </div>
                    <div class="row">
                        <?php if ($hideThisField) { ?>
                            <div class="personalInfo  col-md-6 borrowerDOB_disp <?php echo loanForm::showField('borrowerDOB'); ?>">
                                <div class="row form-group">
                                    <?php echo loanForm::label('borrowerDOB', 'col-md-5'); ?>
                                    <div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <div class="input-group isClientInfo">
                                                <div class="input-group-prepend borrowerDOB">
                                                <span class="input-group-text">
                                                     <i class="fa fa-calendar text-primary icon-lg"></i>
                                                </span>
                                                </div>
                                                <input class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'borrowerDOB', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass"
                                                       id="borrowerDOB" placeholder="MM/DD/YYYY" type="text"
                                                       name="borrowerDOB"
                                                       data-date-dob-start-date="<?php echo glDate::getMinRequirementDate(); ?>"
                                                       data-date-dob-end-date="<?php echo glDate::getMaxRequirementDate(); ?>"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       value="<?php echo $borrowerDOB; ?>"
                                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'borrowerDOB', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                            </div>
                                        <?php } else { ?>
                                            <h5><?php echo $borrowerDOB; ?></h5>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Place of Birth -->
                            <div class="personalInfo   col-md-6 borrowerPOB_disp <?php echo loanForm::showField('borrowerPOB'); ?>">
                                <div class="row form-group">
                                    <?php echo loanForm::label('borrowerPOB', 'col-md-5'); ?>
                                    <div class="col-md-7 isClientInfo">
                                        <?php if ($allowToEdit) { ?>
                                            <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'borrowerPOB', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="borrowerPOB" id="borrowerPOB" type="text"
                                                   value="<?php echo htmlentities($borrowerPOB); ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'borrowerPOB', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                        <?php } else { ?>
                                            <h5><?php echo $borrowerPOB; ?></h5>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>
                            <!-- // Place of Birth // -->
                        <?php } ?>
                        <?php if ($hideThisField) { ?>
                            <div class="personalInfo  col-md-6 ssn_disp <?php echo loanForm::showField('ssn'); ?>">
                                <div class="row form-group">
                                    <?php echo loanForm::label('ssn', 'col-md-5'); ?>
                                    <div class="col-md-7 isClientInfo">
                                        <?php if ($allowToEdit) { ?>
                                            <input class="form-control input-sm mask_ssn <?php echo BaseHTML::fieldAccess(['fNm' => 'ssn', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="ssn" id="ssn" type="text" placeholder="___ - __ - ____"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   onblur="fieldValidation(this.id,this.name);"
                                                   value="<?php echo Strings::formatSSNNumber($ssnNumber); ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'ssn', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                        <?php } else { ?>
                                            <h5><?php echo Strings::formatSSNNumber($ssnNumber); ?></h5>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                        <?php if ($hideThisField) { ?>
                            <div class="personalInfo  col-md-6 driverLicenseState_disp <?php echo loanForm::showField('driverLicenseState'); ?>">
                                <div class="row form-group">
                                    <?php echo loanForm::label('driverLicenseState', 'col-md-5'); ?>
                                    <div class="col-md-7 isClientInfo">
                                        <?php if ($allowToEdit) { ?>
                                            <select class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'driverLicenseState', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                    onchange="fieldValidation(this.id,this.name);"
                                                    name="driverLicenseState" id="driverLicenseState"
                                                    tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'driverLicenseState', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                <option value=''> - Select -</option>
                                                <?php
                                                for ($j = 0; $j < count($stateArray); $j++) {
                                                    $sOpt = '';
                                                    $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), $driverLicenseState);
                                                    echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                                                }
                                                ?>
                                            </select>
                                        <?php } else { ?>
                                            <h5><?php echo Strings::formatCellNumber($driverLicenseState); ?></h5>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                        <?php if ($hideThisField) { ?>
                            <div class="personalInfo  col-md-6 driverLicenseNumber_disp <?php echo loanForm::showField('driverLicenseNumber'); ?>">
                                <div class="row form-group">
                                    <?php echo loanForm::label('driverLicenseNumber', 'col-md-5'); ?>
                                    <div class="col-md-7 isClientInfo">
                                        <?php if ($allowToEdit) { ?>
                                            <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'driverLicenseNumber', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   type="text" name="driverLicenseNumber" id="driverLicenseNumber"
                                                   value="<?php echo htmlentities($driverLicenseNumber); ?>"
                                                   maxlength="20"
                                                   autocomplete="off"
                                                   onblur="fieldValidation(this.id,this.name)"
                                                   tabindex="<?php echo $tabIndex++ ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'driverLicenseNumber', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                        <?php } else { ?>
                                            <h5><?php echo $driverLicenseNumber; ?></h5>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>

                            <div class="personalInfo  col-md-6 driverLicenseIssuanceDate_disp <?php echo loanForm::showField('driverLicenseIssuanceDate'); ?>">
                                <div class="row form-group">
                                    <?php echo loanForm::label('driverLicenseIssuanceDate', 'col-md-5'); ?>
                                    <div class="col-md-7">
                                        <?php if (LMRequest::$allowToEdit) { ?>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                <span class="input-group-text">
                                                    <i class="fa fa-calendar text-primary"></i>
                                                </span>
                                                </div>
                                                <input type="text"
                                                       class="form-control input-sm  dateNewClass driverLicenseIssuanceDate"
                                                       name="driverLicenseIssuanceDate"
                                                       id="driverLicenseIssuanceDate"
                                                       value="<?php echo Dates::formatDateWithRE(LMRequest::myFileInfo()->file2Info()->driverLicenseIssuanceDate, 'YMD', 'm/d/Y'); ?>"
                                                       autocomplete="off"
                                                       placeholder="MM/DD/YYYY"/>
                                            </div>
                                        <?php } else { ?>
                                            <b><?php echo Dates::formatDateWithRE(LMRequest::myFileInfo()->file2Info()->driverLicenseIssuanceDate, 'YMD', 'm/d/Y'); ?></b>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>

                            <div class="personalInfo  col-md-6 driverLicenseExpirationDate_disp <?php echo loanForm::showField('driverLicenseExpirationDate'); ?>">
                                <div class="row form-group">
                                    <?php echo loanForm::label('driverLicenseExpirationDate', 'col-md-5'); ?>
                                    <div class="col-md-7">
                                        <?php if (LMRequest::$allowToEdit) { ?>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                <span class="input-group-text">
                                                    <i class="fa fa-calendar text-primary"></i>
                                                </span>
                                                </div>
                                                <input type="text"
                                                       class="form-control input-sm  dateNewClass driverLicenseExpirationDate"
                                                       name="driverLicenseExpirationDate"
                                                       id="driverLicenseExpirationDate"
                                                       value="<?php echo Dates::formatDateWithRE(LMRequest::myFileInfo()->file2Info()->driverLicenseExpirationDate, 'YMD', 'm/d/Y'); ?>"
                                                       autocomplete="off"
                                                       placeholder="MM/DD/YYYY"/>
                                            </div>
                                        <?php } else { ?>
                                            <b><?php echo Dates::formatDateWithRE(LMRequest::myFileInfo()->file2Info()->driverLicenseExpirationDate, 'YMD', 'm/d/Y'); ?></b>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>

                        <?php } ?>

                        <?php if ($hideThisField) { ?>
                            <div class="personalInfo  col-md-6 maritalStatus_disp <?php echo loanForm::showField('maritalStatus'); ?>">
                                <div class="row form-group">
                                    <?php echo loanForm::label('maritalStatus', 'col-md-5'); ?>
                                    <div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <div class="radio-inline">
                                                <label class="radio radio-solid font-weight-bold" for="maritalStatus_1">
                                                    <input
                                                            type="radio"
                                                            name="maritalStatus"
                                                            class="<?php echo BaseHTML::fieldAccess(['fNm' => 'maritalStatus', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                            id="maritalStatus_1"
                                                            tabindex="<?php echo $tabIndex++; ?>"
                                                            value="Unmarried" <?php if ($maritalStatus == 'Unmarried' || $maritalStatus == 'Single' || $maritalStatus == 'Divorced' || $maritalStatus == 'Widowed') echo 'checked'; ?>
                                                            onclick="showAndHideDiv('hide', 'maritalDiv', 0);" <?php echo BaseHTML::fieldAccess(['fNm' => 'maritalStatus', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>
                                                    Unmarried</label>

                                                <label class="radio radio-solid font-weight-bold" for="maritalStatus_2">
                                                    <input type="radio"
                                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'maritalStatus', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                           name="maritalStatus" id="maritalStatus_2"
                                                           tabindex="<?php echo $tabIndex++; ?>"
                                                           value="Married" <?php echo Strings::isChecked($maritalStatus, 'Married'); ?>
                                                           onclick="showAndHideDiv('show', 'maritalDiv', 0);" <?php echo BaseHTML::fieldAccess(['fNm' => 'maritalStatus', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>
                                                    Married </label>

                                                <?php if ($PCID != 3944) { //alc lending in live ?>
                                                    <label class="radio radio-solid font-weight-bold"
                                                           for="maritalStatus_3">
                                                        <input type="radio"
                                                               class="<?php echo BaseHTML::fieldAccess(['fNm' => 'maritalStatus', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                               name="maritalStatus" id="maritalStatus_3"
                                                               tabindex="<?php echo $tabIndex++; ?>"
                                                               value="Separated" <?php echo Strings::isChecked($maritalStatus, 'Separated'); ?>
                                                               onclick="showAndHideDiv('show', 'maritalDiv', 0);" <?php echo BaseHTML::fieldAccess(['fNm' => 'maritalStatus', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>Separated</label>
                                                <?php } ?>
                                            </div>
                                        <?php } else { ?>
                                            <h5><?php echo $maritalStatus; ?></h5>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                    </div>

                    <div class="row">
                        <?php if ($hideThisField) { ?>
                            <div class="col-md-12 maritalDivClass  <?php echo BaseHTML::parentFieldAccess(['fNm' => 'maritalStatus', 'sArr' => $secArr, 'pv' => $maritalStatus, 'mv' => 'Married,Divorced,Widowed']); ?>"
                                 id="maritalDiv"
                                 style="<?php echo $maritalDisp; ?>">
                                <div class=" row">
                                    <div class="personalInfo  col-md-6 marriageDate_disp <?php echo loanForm::showField('marriageDate'); ?>">
                                        <div class="row form-group">
                                            <?php echo loanForm::label('marriageDate', 'col-md-5'); ?>
                                            <div class="col-md-7">
                                                <?php if ($allowToEdit) { ?>

                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                            <span class="input-group-text">
                                            <i class="fa fa-calendar text-primary"></i>
                                            </span>
                                                        </div>
                                                        <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'marriageDate', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass"
                                                               placeholder="MM/DD/YYYY" type="text" name="marriageDate"
                                                               id="marriageDate"
                                                               value="<?php echo $marriageDate; ?>"
                                                               tabindex="<?php echo $tabIndex++; ?>"
                                                               autocomplete="off"
                                                               maxlength="10" <?php echo BaseHTML::fieldAccess(['fNm' => 'marriageDate', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                                    </div>
                                                <?php } else { ?>
                                                    <h5><?php echo $marriageDate; ?></h5>
                                                <?php } ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="personalInfo  col-md-6 divorceDate_disp <?php echo loanForm::showField('divorceDate'); ?>">
                                        <div class="form-group row">
                                            <?php echo loanForm::label('divorceDate', 'col-md-5'); ?>
                                            <div class="col-md-7">
                                                <?php if ($allowToEdit) { ?>
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                            <span class="input-group-text">
                                            <i class="fa fa-calendar text-primary"></i>
                                            </span>
                                                        </div>
                                                        <input class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'divorceDate', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass"
                                                               type="text" name="divorceDate" placeholder="MM/DD/YYYY"
                                                               id="divorceDate"
                                                               value="<?php echo $divorceDate; ?>"
                                                               tabindex="<?php echo $tabIndex++; ?>"
                                                               autocomplete="off"
                                                               maxlength="10" <?php echo BaseHTML::fieldAccess(['fNm' => 'divorceDate', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                                    </div>
                                                <?php } else { ?>
                                                    <h5><?php echo $divorceDate; ?></h5>
                                                <?php } ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="personalInfo  col-md-6 maidenName_disp <?php echo loanForm::showField('maidenName'); ?>">
                                        <div class="form-group row">
                                            <?php echo loanForm::label('maidenName', 'col-md-5'); ?>
                                            <div class="col-md-7">
                                                <?php if ($allowToEdit) { ?>
                                                    <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'maidenName', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                           type="text" name="maidenName" id="maidenName"
                                                           value="<?php echo Strings::showField('maidenName', 'LMRInfo'); ?>"
                                                           tabindex="<?php echo $tabIndex++; ?>"
                                                           autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'maidenName', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                                <?php } else { ?>
                                                    <h5><?php echo Strings::showField('maidenName', 'LMRInfo'); ?></h5>
                                                <?php } ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="personalInfo  col-md-6 spouseName_disp <?php echo loanForm::showField('spouseName'); ?>">
                                        <div class="form-group row">
                                            <?php echo loanForm::label('spouseName', 'col-md-5'); ?>
                                            <div class="col-md-7">
                                                <?php if ($allowToEdit) { ?>
                                                    <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'spouseName', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                           type="text" name="spouseName" id="spouseName"
                                                           value="<?php echo htmlentities(Strings::showField('spouseName', 'LMRInfo')); ?>"
                                                           tabindex="<?php echo $tabIndex++; ?>" autocomplete="off"
                                                           maxlength="30" <?php echo BaseHTML::fieldAccess(['fNm' => 'spouseName', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                                <?php } else { ?>
                                                    <h5><?php echo Strings::showField('spouseName', 'LMRInfo'); ?></h5>
                                                <?php } ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>

                        <div class="personalInfo col-md-6 borrowerCitizenship_disp <?php echo loanForm::showField('borrowerCitizenship'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('borrowerCitizenship', 'col-md-5'); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="radio-inline">
                                            <label class="radio radio-solid font-weight-bold"
                                                   for="borrowerCitizenship_0">
                                                <input type="radio" name="borrowerCitizenship"
                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'borrowerCitizenship', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       id="borrowerCitizenship_0"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       onclick="assignValueTOAreYouUSCitizen(this.value)"
                                                       value="U.S. Citizen" <?php if ($borrowerCitizenship == 'U.S. Citizen') echo 'checked'; ?>  <?php echo BaseHTML::fieldAccess(['fNm' => 'borrowerCitizenship', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>U.S.
                                                Citizen</label>
                                            <label class="radio radio-solid font-weight-bold"
                                                   for="borrowerCitizenship_1">
                                                <input type="radio"
                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'borrowerCitizenship', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       name="borrowerCitizenship"
                                                       id="borrowerCitizenship_1"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       onclick="assignValueTOAreYouUSCitizen(this.value)"
                                                       value="Perm Resident Alien" <?php echo Strings::isChecked($borrowerCitizenship, 'Perm Resident Alien'); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'borrowerCitizenship', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>Perm
                                                Resident </label>
                                            <?php if ($PCID != 3944) { //alc lending in live ?>
                                                <label class="radio radio-solid font-weight-bold"
                                                       for="borrowerCitizenship_3"> <input type="radio"
                                                                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'borrowerCitizenship', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                                                           name="borrowerCitizenship"
                                                                                           id="borrowerCitizenship_3"
                                                                                           tabindex="<?php echo $tabIndex++; ?>"
                                                                                           onclick="assignValueTOAreYouUSCitizen(this.value)"
                                                                                           value="Non-Perm Resident Alien" <?php echo Strings::isChecked($borrowerCitizenship, 'Non-Perm Resident Alien'); ?>  <?php echo BaseHTML::fieldAccess(['fNm' => 'borrowerCitizenship', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>
                                                    Non-Perm
                                                    Resident </label>
                                            <?php } ?>
                                            <label class="radio radio-solid font-weight-bold"
                                                   for="borrowerCitizenship_4"> <input type="radio"
                                                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'borrowerCitizenship', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                                                       name="borrowerCitizenship"
                                                                                       id="borrowerCitizenship_4"
                                                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                                                       onclick="assignValueTOAreYouUSCitizen(this.value)"
                                                                                       value="Foreign National" <?php echo Strings::isChecked($borrowerCitizenship, 'Foreign National'); ?>  <?php echo BaseHTML::fieldAccess(['fNm' => 'borrowerCitizenship', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>
                                                Foreign National</label>
                                        </div>
                                    <?php } else { ?>
                                        <h5><?php echo $borrowerCitizenship; ?></h5>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div class="personalInfo col-md-6 isServicingMember_disp <?php echo loanForm::showField('isServicingMember'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('isServicingMember', 'col-md-5'); ?>
                                <div class="col-md-4">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="radio-inline">
                                            <label class="radio radio-solid font-weight-bold" for="isServicingMember_1">
                                                <input type="radio"
                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'isServicingMember', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       name="isServicingMember" id="isServicingMember_1"
                                                       onclick="hideAndShowAcceptPurchaseAgreement(this.value, 'isServicingMemberDispOpt');"
                                                       value="Yes"
                                                       tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $isServicingMember); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'isServicingMember', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>Yes
                                            </label>
                                            <label class="radio radio-solid font-weight-bold" for="isServicingMember_2">
                                                <input type="radio" name="isServicingMember" id="isServicingMember_2"
                                                       onclick="hideAndShowAcceptPurchaseAgreement(this.value, 'isServicingMemberDispOpt');"
                                                       value="No"
                                                       tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $isServicingMember); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'isServicingMember', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>No</label>
                                        </div>
                                    <?php } else { ?><b><?php echo $isServicingMember ?></b><?php } ?>
                                </div>
                            </div>
                            <?php if ($hideThisField) { ?>
                                <div class="isServicingMemberDispOpt <?php echo BaseHTML::parentFieldAccess(['fNm' => 'isServicingMember', 'sArr' => $secArr, 'pv' => $isServicingMember, 'av' => 'Yes']); ?>">
                                    <div class="row form-group">
                                        <?php echo loanForm::label('servicingMemberInfo', 'col-md-5'); ?>
                                        <div class="col-md-7">
                                            <?php if ($allowToEdit) { ?>
                                                <input type="hidden" name="servicingMemberInfoHidden"
                                                       id="servicingMemberInfoHidden"
                                                       value="<?php echo implode(',', $servicingMemberInfoArray); ?>"
                                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'servicingMemberInfo', 'sArr' => $secArr, 'opt' => 'I']); ?> >

                                                <select name="servicingMemberInfo[]"
                                                        id="servicingMemberInfo"
                                                        tabindex="<?php echo $tabIndex++; ?>"
                                                        class="chzn-select form-control <?php echo BaseHTML::fieldAccess(['fNm'  => 'servicingMemberInfo',
                                                                                                                          'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                        onchange="populateServiceExpirationdate()"
                                                        data-placeholder="---Select Info---" multiple>
                                                    <?php
                                                    foreach ($glServicingMemberInfo as $SMInfoKey => $SMInfoVal) { ?>
                                                        <option value="<?php echo $SMInfoKey; ?>" <?php if (in_array($SMInfoKey, $servicingMemberInfoArray)) {
                                                            echo 'selected';
                                                        } ?>><?php echo $SMInfoVal; ?></option>
                                                    <?php } ?>
                                                </select>
                                            <?php } else { ?>
                                                <h5><?php echo $servicingMemberInfo; ?></h5>
                                            <?php } ?>
                                        </div>
                                    </div>
                                </div>
                            <?php } ?>
                            <div class=" isServicingMemberDispOpt servicingMemberInfoDispOpt serviceExpirationDate_disp
                        <?php echo BaseHTML::parentFieldAccess(['fNm' => 'servicingMemberInfo', 'sArr' => $secArr,
                                                                'pv'  => $servicingVal, 'av' => '0']); ?>"
                                 style="<?php echo $servicingMemberInfoDispOpt; ?>">
                                <div class="row form-group">
                                    <?php echo loanForm::label('serviceExpirationDate', 'col-md-5'); ?>
                                    <div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <div class="input-group serviceExpirationDate">
                                                <div class="input-group-prepend">
                                            <span class="input-group-text">
                                            <i class="fa fa-calendar text-primary"></i>
                                            </span>
                                                </div>
                                                <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm'  => 'serviceExpirationDate',
                                                                                                                      'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass"
                                                       placeholder="MM/DD/YYYY" type="text" name="serviceExpirationDate"
                                                       id="serviceExpirationDate"
                                                       value="<?php echo $serviceExpirationDate; ?>"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       autocomplete="off"
                                                       maxlength="10" <?php echo BaseHTML::fieldAccess(['fNm'  => 'serviceExpirationDate',
                                                                                                        'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                            </div>
                                        <?php } else { ?>
                                            <h5><?php echo $serviceExpirationDate; ?></h5>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <?php if ($hideThisField) { ?>
                            <div class="personalInfo  col-md-6 agesOfDependent_disp <?php echo loanForm::showField('agesOfDependent'); ?>">
                                <div class="row form-group">
                                    <?php echo loanForm::label('agesOfDependent', 'col-md-5'); ?>
                                    <div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'agesOfDependent', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   type="text" name="agesOfDependent" id="agesOfDependent"
                                                   value="<?php echo htmlentities($agesOfDependent); ?>"
                                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'agesOfDependent', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                        <?php } else { ?>
                                            <h5><?php echo $agesOfDependent ?></h5>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                        <?php if ($hideThisField) { ?>
                            <div class="personalInfo  col-md-6 numberOfDependents_disp <?php echo loanForm::showField('numberOfDependents'); ?>">
                                <div class="row form-group">
                                    <?php echo loanForm::label('numberOfDependents', 'col-md-5'); ?>
                                    <div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'numberOfDependents', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   type="number" name="numberOfDependents" id="numberOfDependents"
                                                   value="<?php echo $numberOfDependents ?>"
                                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'numberOfDependents', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                        <?php } else { ?>
                                            <h5><?php echo $numberOfDependents ?></h5>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>


                        <div class="personalInfo col-md-6 midFicoScore_disp <?php echo loanForm::showField('midFicoScore'); ?>"
                             data-sectionid="<?php echo loanForm::getSectionID(); ?>"
                        >
                            <div class=" row form-group ">
                                <?php
                                echo loanForm::label(
                                        'midFicoScore',
                                        'col-md-5 ',
                                        '',
                                        loanForm::changeLog(
                                                LMRequest::myFileInfo()->fileHMLOInfo()->HMLOID,
                                                'midFicoScore',
                                                \models\lendingwise\tblFileHMLO::class,
                                                loanForm::getFieldLabel('midFicoScore')
                                        ),
                                );
                                //  echo loanForm::label('midFicoScore', 'col-md-5');
                                ?>
                                <div class="col-md-7">
                                    <?php echo loanForm::number(
                                            'midFicoScore',
                                            $allowToEdit,
                                            $tabIndex++,
                                            $midFicoScore,
                                            'validateMinMaxLoanGuidelines();'
                                    ); ?>
                                </div>
                            </div>
                        </div>


                        <div class="personalInfo  col-md-6 borEquifaxScore_disp <?php echo loanForm::showField('borEquifaxScore'); ?>">
                            <div class="row form-group">
                                <?php
                                echo loanForm::label(
                                        'borEquifaxScore',
                                        'col-md-5 ',
                                        '',
                                        loanForm::changeLog(
                                                LMRequest::myFileInfo()->fileHMLOInfo()->HMLOID,
                                                'borEquifaxScore',
                                                \models\lendingwise\tblFileHMLO::class,
                                                loanForm::getFieldLabel('borEquifaxScore')
                                        ),
                                );
                                // echo loanForm::label('borEquifaxScore', 'col-md-5'); ?>
                                <div class="col-md-7">
                                    <?php echo loanForm::number(
                                            'borEquifaxScore',
                                            $allowToEdit,
                                            $tabIndex++,
                                            $borEquifaxScore,
                                    ); ?>
                                </div>
                            </div>
                        </div>


                        <div class="personalInfo  col-md-6 borTransunionScore_disp <?php echo loanForm::showField('borTransunionScore'); ?>">
                            <div class="row form-group">
                                <?php
                                echo loanForm::label(
                                        'borTransunionScore',
                                        'col-md-5 ',
                                        '',
                                        loanForm::changeLog(
                                                LMRequest::myFileInfo()->fileHMLOInfo()->HMLOID,
                                                'borTransunionScore',
                                                \models\lendingwise\tblFileHMLO::class,
                                                loanForm::getFieldLabel('borTransunionScore')
                                        ),
                                ); ?>
                                <div class="col-md-7">
                                    <?php echo loanForm::number(
                                            'borTransunionScore',
                                            $allowToEdit,
                                            $tabIndex++,
                                            $borTransunionScore,
                                    ); ?>
                                </div>
                            </div>
                        </div>


                        <div class="personalInfo  col-md-6 borExperianScore_disp <?php echo loanForm::showField('borExperianScore'); ?>">
                            <div class="row form-group">
                                <?php
                                echo loanForm::label(
                                        'borExperianScore',
                                        'col-md-5 ',
                                        '',
                                        loanForm::changeLog(
                                                LMRequest::myFileInfo()->fileHMLOInfo()->HMLOID,
                                                'borExperianScore',
                                                \models\lendingwise\tblFileHMLO::class,
                                                loanForm::getFieldLabel('borExperianScore')
                                        ),
                                ); ?>

                                <div class="col-md-7">
                                    <?php echo loanForm::number(
                                            'borExperianScore',
                                            $allowToEdit,
                                            $tabIndex++,
                                            $borExperianScore,
                                    ); ?>
                                </div>
                            </div>
                        </div>

                        <div class="personalInfo  col-md-6 borCreditScoreRange_disp <?php echo loanForm::showField('borCreditScoreRange'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('borCreditScoreRange', 'col-md-5'); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <select class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'borCreditScoreRange', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                name="borCreditScoreRange" id="borCreditScoreRange"
                                                tabindex="<?php echo $tabIndex++; ?>"
                                                autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'borCreditScoreRange', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                                            <option value=''> - Select -</option>
                                            <?php
                                            foreach ($glHMLOCreditScoreRange as $i => $score) {
                                                $glCreditScoreRange = trim($score);
                                                $sOpt = Arrays::isSelected($glCreditScoreRange, $borCreditScoreRange);
                                                echo "<option value=\"" . $glCreditScoreRange . "\" " . $sOpt . '>' . $glCreditScoreRange . '</option>';
                                            }
                                            ?>
                                        </select>
                                    <?php } else { ?>
                                        <h5><?php echo $borCreditScoreRange; ?></h5>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div class="personalInfo  col-md-6  <?php echo loanForm::showField('authorizationStatus'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('authorizationStatus', 'col-md-5'); ?>
                                <div class="col-md-7">
                                    <?php echo loanForm::select(
                                            'authorizationStatus',
                                            LMRequest::$allowToEdit,
                                            1,
                                            LMRequest::myFileInfo()->fileHMLOInfo()->authorizationStatus,
                                            tblBorrowerAuthorizationStatus::options(),
                                            '',
                                            ' chzn-select ',
                                            ' ',
                                            'Please Select ' . loanForm::getFieldLabel('authorizationStatus')
                                    );
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- personalInfo end -->
                <?php } ?>
                <?php echo CustomField::RenderForTabSection(
                        PageVariables::$PCID,
                        tblFile::class,
                        LMRequest::$LMRId,
                        'BCI',
                        $fileTab,
                        $activeTab,
                        LMRequest::myFileInfo()->getFileTypes(),
                        LMRequest::myFileInfo()->getLoanPrograms()
                ); ?>
            </div>
        </div>
        <?php
//}
        ?>
        <!-- Borrower Info Section End -->
        <?php
    } else { ?>
        <!-- Always show borrower First name -->
        <div class="card card-custom HMLOLoanInfoSections BCI BCICard" style="<?php echo $HMLOLoanInfoSectionsDisp; ?>">
            <div class="card-header card-header-tabs-line bg-gray-100  ">
                <div class="card-title">
                    <h3 class="card-label">
                        <?php echo BaseHTML::getSectionHeading('BCI'); ?>
                    </h3>
                    <?php if (trim(BaseHTML::getSectionTooltip('BCI')) != '') { ?>&nbsp;
                        <i class="popoverClass fas fa-info-circle text-primary "
                           data-html="true"
                           data-content="<?php echo BaseHTML::getSectionTooltip('BCI'); ?>"></i>
                    <?php } ?>
                </div>
                <div class="card-toolbar">
                    <span class="cursor-pointer btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none"
                          data-card-tool="toggle"
                          data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                        <i class="ki ki-arrow-down icon-nm"></i>
                    </span>
                    <span class="cursor-pointer btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none"
                          data-card-tool="reload"
                          data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                        <i class="ki ki-reload icon-nm"></i>
                    </span>
                    <span class="cursor-pointer tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                          data-card-tool="toggle"
                          data-section="BCICard"
                          data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                        <i class="ki ki-arrow-down icon-nm"></i>
                    </span>
                </div>
            </div>
            <div class="card-body BCICard_body <?php echo loanForm::showField('borrowerFName'); ?>">
                <div class="row">
                    <div class="form-group col-md-6">
                        <?php echo loanForm::label('borrowerFName', 'col-md-5'); ?>
                        <div class="col-md-7">
                            <?php if ($allowToEdit) { ?>
                                <input class="form-control input-sm mandatory" type="text" name="borrowerFName"
                                       id="borrowerFName" value="<?php echo $borrowerFName; ?>" autocomplete="off"
                                       tabindex="<?php echo $tabIndex++ ?>"/>
                            <?php } else {
                                echo '<h5>' . $borrowerFName . '</h5>';
                            } ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    ?>
</div>
<script type="text/javascript">
    let PageVariable_userNumber = parseInt("<?php echo PageVariables::$userNumber; ?>");
    let PageVariable_userGroup = ("<?php echo PageVariables::$userGroup; ?>");
    let PageVariable_PCID = parseInt("<?php echo $PCID; ?>");
    let borrowerInfo_LMRId = parseInt("<?php echo $LMRId; ?>");

    if (PageVariable_userGroup === 'Client'
        && PageVariable_userNumber > 0) {
        $(document).ready(function () {
            populateClientInfo('client-loanModForm', '<?php echo trim($tempClientEmail); ?>', PageVariable_PCID);
            if (borrowerInfo_LMRId === 0) {
                getModules('loanModForm', PageVariable_PCID);
                getServiceTypes('loanModForm');
                getBranchAgents('<?php echo cypher::myEncryption($executiveId) ?>')
            }
        });
    }
    //personal Info
    if ($('.personalInfo.secShow').length > 0) { //show title
        $("#personalInfotitle").show();
    } else { //hide title
        $("#personalInfotitle").hide();
    }

    if (PageVariable_userGroup !== 'Client'
        && PageVariable_userNumber > 0
        && $.inArray(PageVariable_PCID, [3138, 1337]) === -1) {
        $(document).ready(function () {
            const debounceDelay = 300;
            $('#borrowerEmail').autocomplete({
                serviceUrl: '/backoffice/api_v2/jqfiles/getBorrowers?searchType=email',
                minChars: 3,
                deferRequestBy: debounceDelay,
                onSelect: function (value, data) {
                    let res = data.split('_^_');
                    $('#borrowerEmail').val(replaceXMLProcess(res[0]));
                    populateClientInfo('loanModForm', replaceXMLProcess(res[0]), PageVariable_PCID, borrowerInfo_LMRId, res[1]);
                }
            });
            $('#borrowerFName').autocomplete({
                serviceUrl: '/backoffice/api_v2/jqfiles/getBorrowers?searchType=fstName',
                minChars: 3,
                deferRequestBy: debounceDelay,
                onSelect: function (value, data) {
                    let res = data.split('_^_');
                    $('#borrowerFName').val(replaceXMLProcess(res[2]));
                    populateClientInfo('loanModForm', replaceXMLProcess(res[0]), PageVariable_PCID, borrowerInfo_LMRId, res[1]);
                }
            });
            $('#borrowerLName').autocomplete({
                serviceUrl: '/backoffice/api_v2/jqfiles/getBorrowers?searchType=lastName',
                minChars: 3,
                deferRequestBy: debounceDelay,
                onSelect: function (value, data) {
                    let res = data.split('_^_');
                    $('#borrowerLName').val(replaceXMLProcess(res[3]));
                    populateClientInfo('loanModForm', replaceXMLProcess(res[0]), PageVariable_PCID, borrowerInfo_LMRId, res[1]);
                }
            });
        });
    }

    function populateServiceExpirationdate() {
        let servicingMemberInfo = $('#servicingMemberInfo').val();
        if ($.inArray('0', servicingMemberInfo) != -1) {
            $('.servicingMemberInfoDispOpt').show().removeClass('secHide');
            $('#serviceExpirationDate').removeClass('secHide').removeAttr("disabled");
        } else {
            $('.servicingMemberInfoDispOpt').hide();
        }
    }

    function assignValueTOAreYouUSCitizen(val) {
        if (val === 'U.S. Citizen') {
            $("#isBorUSCitizenYes").prop("checked", true);
            $("#isBorUSCitizenNo").prop("checked", false);
            hideAndShowSection('Yes', 'No', 'borOriginAndVisaTR');
        } else if (val) {
            $("#isBorUSCitizenNo").prop("checked", true);
            $("#isBorUSCitizenYes").prop("checked", false);
            hideAndShowSection('No', 'No', 'borOriginAndVisaTR');
        }
    }
</script>

<!-- borrowerInfo.php -->
