<?php

use models\composite\oAutomatedRules\checkAutomatedRuleJobEnqueue;
use models\composite\oAutomatedRules\deleteAutomatedRules;
use models\composite\oAutomatedRules\saveAutomatedRules;
use models\composite\oAutomatedRules\updateAutomatedRuleStatus;
use models\composite\oBroker\listAllAgents;
use models\composite\oPC\getPCServiceType;
use models\composite\oPrimaryStatus\getPCPrimaryStatus;
use models\composite\oSubstatus\getPCFileSubstatus;
use models\composite\oWorkflow\getPCWFSteps;
use models\constants\borrowerStatusArray;
use models\constants\drawRequestStatusArray;
use models\constants\gl\glBorrowerType;
use models\cypher;
use models\constants\automationConstants;
use models\lendingwise\tblBranch;
use models\Request;
use models\standard\Arrays;
use models\standard\Dates;
use models\standard\HTTP;
use models\standard\Strings;
use models\standard\UserAccess;

session_start();
require '../includes/util.php';
require 'initPageVariables.php';
require 'getPageVariables.php';
UserAccess::CheckAdminUse();
global $userTimeZone;
$action = '';
$PCID = '';
//variable assignments
if (isset($_POST['action'])) $action = Request::GetClean('action');
if (isset($_POST['PCID'])) $PCID = Request::GetClean('PCID');
//UserTimeZone
$ipArray = [];
$ipArray['inputZone'] = $userTimeZone;
$ipArray['outputZone'] = CONST_SERVER_TIME_ZONE;
$ipArray['inputTime'] = Dates::Timestamp();
$currentTimeStamp = Dates::timeZoneConversion($ipArray);
$drawManagementRules = [
                    automationConstants::$automation_InitialSOW,
                    automationConstants::$automation_DrawRequest,
                    automationConstants::$automation_Revision
                ];
//this code is for ajax calls
if ($action != '') {
    $ruleName = $fileType = $ruleFor = '';
    if (isset($_POST['ruleName'])) $ruleName = Request::GetClean('ruleName');
    if (isset($_POST['fileType'])) $fileType = Request::GetClean('fileType');
    if (isset($_POST['ruleFor'])) $ruleFor = Request::GetClean('ruleFor');
    if ($action == 'conditions') { // get the options for selected conditions
        if($ruleName == automationConstants::$automation_FCU) { //Loan File Create or Update
            echo json_encode(automationConstants::$automation_LoanFileStatus);
            exit();
        }
        elseif ($ruleName == 'PFS') { // Primary File Status
            $fileStatusArray = [];
            $inArray = ['PCID' => $PCID, 'opt1' => 'list', 'opt2' => 'Y', 'searchTerm' => $fileType];
            $fileStatusArray = getPCPrimaryStatus::getReport($inArray);
            if ($fileStatusArray) {
                echo json_encode($fileStatusArray['primaryStatusInfo'][$fileType]);
                exit();
            }
        } elseif ($ruleName == 'FSS') { // File Sub Status
            $PCFileSubstatusArray = [];
            $inArray = ['PCID' => $PCID, 'opt1' => 'list', 'opt2' => 'Y', 'searchTerm' => $fileType];
            $result = getPCFileSubstatus::getReport($inArray);
            if (count($result) > 0) {
                $PCFileSubstatusArray = $result['substatusInfo'][$fileType];
                $PCFileSubstatusArray = Arrays::buildKeyByValue($PCFileSubstatusArray, 'PSCID');
                echo json_encode($PCFileSubstatusArray);
                exit();
            }
        } elseif ($ruleName == 'Workflow') { // Workflow
            $PCWFStepsArray = [];
            $inArray = ['PCID' => $PCID];
            $PCWFStepsArray = getPCWFSteps::getReport($inArray);
            if (count($PCWFStepsArray) > 0) {
                echo json_encode($PCWFStepsArray);
                exit();
            }
        } elseif ($ruleName == 'Branch') { // Branch
            $branchArray = [];
            $branchData = [];
            $branchArray = tblBranch::GetAll([
                'processingCompanyId' => $PCID,
                'activeStatus' => 1,
            ]);
            foreach ($branchArray as $branch) {
                $branchData[] = [
                    'id' => $branch->executiveId,
                    'text' => $branch->LMRExecutive,
                ];
            }
            HTTP::ExitJSON($branchData);
        } elseif ($ruleName == 'Broker') { // Broker
            $brokerData = [];
            $brokerArray = listAllAgents::getObjects([
                'PCID' => $PCID,
                'externalBroker' => 0,
            ]);
            foreach ($brokerArray as $broker) {
                $brokerData[] = [
                    'id'   => $broker->brokerNumber,
                    'text' => $broker->brokerName,
                ];
            }
            HTTP::ExitJSON($brokerData);
        } elseif ($ruleName == 'LO') { // Loan Officer
            $loanOfficerData = [];
            $loanOfficerArray = listAllAgents::getObjects([
                'PCID'           => $PCID,
                'externalBroker' => 1,
            ]);
            foreach ($loanOfficerArray as $loanOfficer) {
                $loanOfficerData[] = [
                    'id'   => $loanOfficer->brokerNumber,
                    'text' => $loanOfficer->brokerName,
                ];
            }
            HTTP::ExitJSON($loanOfficerData);
        } elseif ($ruleName === 'BorrowerStatus') {
            $borrowerStatusData = [];
            $borrowerStatusArray = borrowerStatusArray::$borrowerStatusArray;
            foreach ($borrowerStatusArray as $key => $value) {
                $borrowerStatusData[] = [
                    'id'   => $key,
                    'text' => $value,
                ];
            }
            HTTP::ExitJSON($borrowerStatusData);
        } elseif ($ruleName === 'BorrowerType') {
            $borrowerTypeData = [];
            $borrowerTypes = glBorrowerType::$glBorrowerTypeArray;
            foreach ($borrowerTypes as $key => $value) {
                $borrowerTypeData[] = [
                    'id'   => $value,
                    'text' => $value,
                ];
            }
            HTTP::ExitJSON($borrowerTypeData);
        } elseif (in_array($ruleName, $drawManagementRules)) {
            $drawRequestStatusData = [];
            $drawRequestStatuses = drawRequestStatusArray::$drawRequestStatusArray;
            foreach ($drawRequestStatuses as $key => $value) {
                $drawRequestStatusData[] = [
                    'id'   => $key,
                    'text' => $value,
                ];
            }
            HTTP::ExitJSON($drawRequestStatusData);
        } elseif ($ruleName === 'LoanProgram') {
            $loanProgramData = [];
            $PCServiceTypeInfoArray = getPCServiceType::getReport([
                'PCID' => $PCID,
                'moduleCode' => $fileType,
                'keyNeeded' => 'n',
                'marketPlaceStatus' => 'all',
                'marketPlacePublicStatus' => 'all',
            ]);
            foreach ($PCServiceTypeInfoArray as $PCServiceType) {
                $loanProgramData[] = [
                    'id'   => $PCServiceType['LMRClientType'],
                    'text' => $PCServiceType['serviceType'],
                ];
            }
            HTTP::ExitJSON($loanProgramData);
        }

    } elseif ($action == 'remove') {
        $PCID = cypher::myDecryption(Request::GetClean('PCID'));
        $id = $_POST['id'];
        $params = ['PCID' => $PCID, 'id' => $id];
        echo htmlspecialchars(deleteAutomatedRules::getReport($params));
    } elseif ($action == 'update') {
        $PCID = $_POST['PCID'];
        $id = $_POST['id'];
        $params = ['PCID' => $PCID, 'id' => $id];
        echo htmlspecialchars(updateAutomatedRuleStatus::getReport($params));
    } elseif ($action == 'check') {
        $PCID = $_POST['PCID'];
        $id = $_POST['id'];
        $params = ['PCID' => $PCID, 'id' => $id];
        checkAutomatedRuleJobEnqueue::getReport($params);
    }
} else {
// form submit data here
//Instant Action(s)
    $action = '';
    $tasksId = '';
    $emailsId = '';
    $webhooksId = '';
//Schedule Action(s)
    $schaction = '';
    $schtasksId = '';
    $schemailsId = '';
    $schwebhooksId = '';
//Update Status
    $statusUpdateId = '';
    $noOfDayStatus = '';

    $userGroup = Request::GetClean('userGroup') ?? '';
    $userNumber = Request::GetClean('userNumber') ?? '';
    $userName = Request::GetClean('userName') ?? '';

    $id = Request::GetClean('id') ?? 0;
    $isRuleCondUpdated = Request::GetClean('isRuleCondUpdated') ?? 0;
    $ruleFileType = Request::GetClean('ruleFileType') ?? '';
    $ruleName = Request::GetClean('ruleName') ?? '';
    $ruleDescription = Request::GetClean('ruleDescription') ?? '';
    $repeatRule = $_POST['repeatRule'] ?? 0;
//Instant Action(s)
    if (isset($_POST['event'])) $action = Request::GetClean('event');
    if (isset($_POST['tasksId'])) $tasksId = Request::GetClean('tasksId');
    if (isset($_POST['emailsId'])) $emailsId = Request::GetClean('emailsId');
    if (isset($_POST['webhooksId'])) $webhooksId = Request::GetClean('webhooksId');
//Schedule Action(s)
    if (isset($_POST['schevent'])) $schaction = Request::GetClean('schevent');
    if (isset($_POST['schtasksId'])) $schtasksId = Request::GetClean('schtasksId');
    if (isset($_POST['schemailsId'])) $schemailsId = Request::GetClean('schemailsId');
    if (isset($_POST['schwebhooksId'])) $schwebhooksId = Request::GetClean('schwebhooksId');
    if (isset($_POST['contEventStatus'])) $contEventStatus = Request::GetClean('contEventStatus');
//Update Status
    if (isset($_POST['statusUpdateId'])) $statusUpdateId = Request::GetClean('statusUpdateId');
    if (isset($_POST['noOfDayStatus'])) $noOfDayStatus = Request::GetClean('noOfDayStatus');
    $employeesId = Request::isset('usersId') ? Request::GetClean('usersId') : '';
    //new logic - details in tooltip
    if($noOfDayStatus) $repeatRule = 0;
    $params = [
        'id' => $id,
        'PCID' => $PCID,
        'fileType' => $ruleFileType,
        'ruleName' => $ruleName,
        'ruleDescription' => $ruleDescription,
        'repeatRule' => $repeatRule,
        'dateTimeNow' => $currentTimeStamp,
        'userGroup' => $userGroup,
        'userNumber' => $userNumber,
        'userName' => $userName,
        'isRuleCondUpdated' => $isRuleCondUpdated,
        'action' => $action,
        'tasksId' => $tasksId,
        'emailsId' => $emailsId,
        'webhooksId' => $webhooksId,
        'schaction' => $schaction,
        'schtasksId' => $schtasksId,
        'schemailsId' => $schemailsId,
        'schwebhooksId' => $schwebhooksId,
        'statusUpdateId' => $statusUpdateId,
        'noOfDayStatus' => $noOfDayStatus,
        'employeesId' => $employeesId,
    ];
    if ($id > 0) { // Update
        $sessMsg = 'Automated Rule Updated Successfully';
    } else { // Add
        $sessMsg = 'Automated Rule Added Successfully';
    }
    saveAutomatedRules::getReport($params);

    $redirect = strtok($_SERVER['HTTP_REFERER'], '?') . '?userGroup=' . $userGroup . '&tabNumb=4&view=list';
    Strings::SetSess('msg', $sessMsg);
    //header('Location: ' . $redirect);
    HTTP::redirect($redirect);
    exit;
}


