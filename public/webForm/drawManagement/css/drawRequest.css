/* Hide number input spinners/steppers */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
input[type="number"] {
    -moz-appearance: textfield;
    appearance: textfield;
}

/* Amount badges styling */
.amount-badge {
    font-size: 1rem;
    font-weight: 600;
    margin-right: 0.5rem;
}

.btnSave {
    left: 50%;
    transform: translateX(-50%);
}
