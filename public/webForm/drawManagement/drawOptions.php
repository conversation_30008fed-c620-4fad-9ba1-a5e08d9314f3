<?php

use models\composite\oDrawManagement\DrawRequest;
use models\lendingwise\db\tblFileDrawRequests_db;
use models\Request;

if($_SERVER['REQUEST_METHOD'] === 'POST') {
    if(isset($_POST[tblFileDrawRequests_db::COLUMN_STATUS])) {
        $status = Request::GetClean(tblFileDrawRequests_db::COLUMN_STATUS);
        $sowApproved = Request::GetClean(tblFileDrawRequests_db::COLUMN_SOWAPPROVED);
        $isDrawRequest = Request::GetClean(tblFileDrawRequests_db::COLUMN_ISDRAWREQUEST);
        $drawRequest->updateDrawRequestStatus($status, $sowApproved, $isDrawRequest);
        header("Refresh:0");
        exit;
    }
}
?>

<div class="card-body">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0 text-dark">Draw Options</h2>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="d-flex flex-column flex-md-row justify-content-center gap-3">
                <!-- New Draw Request Button -->
                <div class="text-center mb-3 mb-md-0">
                    <form action="" method="post">
                        <button type="submit"
                                id="btnNewDrawRequest"
                                class="btn btn-primary btn-lg px-4 py-3"
                                style="min-width: 200px;">
                            <i class="fas fa-plus-circle me-2"></i>
                            New Draw Request
                        </button>
                        <input type="hidden" name="<?= tblFileDrawRequests_db::COLUMN_STATUS; ?>" value="<?= DrawRequest::STATUS_NEW; ?>" />
                    </form>
                </div>
                <span class="">&nbsp;&nbsp;&nbsp;&nbsp;</span>
                <!-- Request Revision Button -->
                <div class="text-center">
                    <form action="" method="post">
                        <button type="submit"
                                id="btnRequestRevision"
                                class="btn btn-warning btn-lg px-4 py-3"
                                style="min-width: 200px;">
                            <i class="fas fa-edit me-2"></i>
                            Request Revision
                        </button>
                        <input type="hidden" name="<?= tblFileDrawRequests_db::COLUMN_STATUS; ?>" value="<?= DrawRequest::STATUS_NEW; ?>" />
                        <input type="hidden" name="<?= tblFileDrawRequests_db::COLUMN_SOWAPPROVED; ?>" value="0" />
                        <input type="hidden" name="<?= tblFileDrawRequests_db::COLUMN_ISDRAWREQUEST; ?>" value="0" />
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
