$(document).ready(function () {
    let BOSSLURL = $('#bosslurl').val();
    let PCID = $('#PCID').val();
    $('[data-toggle="popover"]').popover();
    $('#noOfDayStatus').inputmask({
        'mask': 999, 'placeholder': ''
    });
    $('select.js-example-basic-multiple').select2();

    $('#ruleFileType').change(function () {
        let ruleFileType = $(this).val();
        //hide the rule for
        //reset the values
        $('.ruleFor').each(function () {
            $(this).val('');
        });
        if ($('#parameterOptions_1').length > 0) {
            $('#parameterOptions_1').val(null).trigger('change');
        }
        if ($('#parameterOptions_2').length > 0) {
            $('#parameterOptions_2').val(null).trigger('change');
        }
        if ($('#parameterOptions_3').length > 0) {
            $('#parameterOptions_3').val(null).trigger('change');
        }

        if (ruleFileType) { // show parameter 1
            //show conditions
            $('#conditions').removeClass('hidden');
            $('#parameter_1').removeClass('hidden');
        } else { //hide parameter div (hide all)
            $('#conditions').addClass('hidden');
            $('#parameter_1').addClass('hidden');
        }
    });

    //get the rule id
    // this is only for update
    let ruleId = $('#id').val();
    if (ruleId) {
        filterRuleFor();
    }

    //get PFS, FSS & Workflow params values
    $(document).on('change', '.ruleFor', function () {
        if (ruleId) {
            filterRuleFor();
        }
        let id = $(this).attr('id').split('_');
        let rowId = parseInt(id[1]); // 1,2,3.
        let fileType = $("#ruleFileType").val();
        let ruleFor = $(this).val();
        if (fileType && ruleFor) {
            // get the conditions options
            if(ruleFor === 'FCU') {
                $.ajax({
                    type: "POST",
                    url: BOSSLURL + 'automatedRulesController.php',
                    data: {action: "conditions", PCID: PCID, fileType: fileType, ruleFor: ruleFor, ruleName: 'FCU'},
                    beforeSend: function () {
                    },
                    success: function (data) {
                        data = JSON.parse(data);
                        if (data) {
                            $('#parameterOptions_' + rowId).html(''); // empty before appending
                            $.each(data, function (key, value) {
                                $('#parameterOptions_' + rowId)
                                    .append($("<option></option>")
                                        .attr("value", key)
                                        .text(value));
                            });
                            $('#parameterOptions_' + rowId).trigger('change.select2');
                        }
                    }
                });
            }
            else if (ruleFor === 'PFS') { // PFS
                $.ajax({
                    type: "POST",
                    url: BOSSLURL + 'automatedRulesController.php',
                    data: {action: "conditions", PCID: PCID, fileType: fileType, ruleFor: ruleFor, ruleName: 'PFS'},
                    beforeSend: function () {
                    },
                    success: function (data) {
                        data = JSON.parse(data);
                        if (data) {
                            $('#parameterOptions_' + rowId).html(''); // empty before appending
                            $.each(data, function (key, value) {
                                $('#parameterOptions_' + rowId)
                                    .append($("<option></option>")
                                        .attr("value", value.PSID)
                                        .text(value.primaryStatus));
                            });
                            $('#parameterOptions_' + rowId).trigger('change.select2');
                        }
                        //show no of days - status
                        if (rowId === 1) {
                            $('.noOfDayStatusDiv').removeClass('hidden');
                        }
                    }
                });
            }
            else if (ruleFor === 'FSS') { // FSS
                $.ajax({
                    type: "POST",
                    url: BOSSLURL + 'automatedRulesController.php',
                    data: {action: "conditions", PCID: PCID, fileType: fileType, ruleFor: ruleFor, ruleName: 'FSS'},
                    beforeSend: function () {
                    },
                    success: function (resp) {
                        resp = JSON.parse(resp);
                        $('#parameterOptions_' + rowId).html(''); // empty before appending
                        let jsonObj = [];
                        if (resp) {
                            $.each(resp, function (catId, fss) {
                                let jsonObj2 = [];
                                $.each(fss, function (j, fssParam) {
                                    let fssData;
                                    fssData = {
                                        'id': fssParam['PFSID'], 'text': fssParam['substatus']
                                    }
                                    jsonObj2.push(fssData);
                                });
                                let rowData = {
                                    "id": catId, "text": fss[0]['category'], "children": jsonObj2
                                };
                                jsonObj.push(rowData);
                            });
                            $("#parameterOptions_" + rowId).select2({
                                data: jsonObj
                            });
                            $('#parameterOptions_' + rowId).trigger('change.select2');
                        }
                    }
                });

            }
            else if (ruleFor === 'Workflow') { // Workflow
                $.ajax({
                    type: "POST", url: BOSSLURL + 'automatedRulesController.php', data: {
                        action: "conditions", PCID: PCID, fileType: fileType, ruleFor: ruleFor, ruleName: 'Workflow'
                    }, beforeSend: function () {
                    }, success: function (r) {
                        let resp = JSON.parse(r);
                        $('#parameterOptions_' + rowId).html('');// empty before appending
                        let jsonObj = [];
                        if (resp) {
                            $.each(resp, function (wfId, wfValue) {
                                let jsonObj2 = [];
                                $.each(wfValue, function (i, wfsValue) {
                                    let wfsData;
                                    wfsData = {
                                        'id': wfsValue['WFSID'], 'text': wfsValue['steps']
                                    }
                                    jsonObj2.push(wfsData);
                                });
                                let rowData = {
                                    "id": wfId, "text": wfValue[0]['WFName'], "children": jsonObj2
                                };
                                jsonObj.push(rowData);
                            });
                            $("#parameterOptions_" + rowId).select2({
                                data: jsonObj
                            });
                            $('#parameterOptions_' + rowId).trigger('change.select2');
                        }
                    }
                });

            }
            else if(ruleFor === 'Branch') {
                $.ajax({
                    type: "POST",
                    url: BOSSLURL + 'automatedRulesController.php',
                    data: {action: "conditions", PCID: PCID, fileType: fileType, ruleFor: ruleFor, ruleName: 'Branch'},
                    beforeSend: function () {
                    },
                    success: function (data) {
                        if (data) {
                            $('#parameterOptions_' + rowId).html(''); // empty before appending
                            $.each(data, function (key, value) {
                                $('#parameterOptions_' + rowId)
                                    .append($("<option></option>")
                                        .attr("value", value.id)
                                        .text(value.text));
                            });
                            $('#parameterOptions_' + rowId).trigger('change.select2');
                        }
                    }
                });

            }
            else if(ruleFor === 'Broker') {
                $.ajax({
                    type: "POST",
                    url: BOSSLURL + 'automatedRulesController.php',
                    data: {action: "conditions", PCID: PCID, fileType: fileType, ruleFor: ruleFor, ruleName: 'Broker'},
                    beforeSend: function () {
                    },
                    success: function (data) {
                        if (data) {
                            $('#parameterOptions_' + rowId).html(''); // empty before appending
                            $.each(data, function (key, value) {
                                $('#parameterOptions_' + rowId)
                                    .append($("<option></option>")
                                        .attr("value", value.id)
                                        .text(value.text));
                            });
                            $('#parameterOptions_' + rowId).trigger('change.select2');
                        }
                    }
                });
            }
            else if(ruleFor === 'LO') {
                $.ajax({
                    type: "POST",
                    url: BOSSLURL + 'automatedRulesController.php',
                    data: {action: "conditions", PCID: PCID, fileType: fileType, ruleFor: ruleFor, ruleName: 'LO'},
                    beforeSend: function () {
                    },
                    success: function (data) {
                        if (data) {
                            $('#parameterOptions_' + rowId).html(''); // empty before appending
                            $.each(data, function (key, value) {
                                $('#parameterOptions_' + rowId)
                                    .append($("<option></option>")
                                        .attr("value", value.id)
                                        .text(value.text));
                            });
                            $('#parameterOptions_' + rowId).trigger('change.select2');
                        }
                    }
                });
            }
            else if(ruleFor === 'BorrowerStatus') {
                $.ajax({
                    type: "POST",
                    url: BOSSLURL + 'automatedRulesController.php',
                    data: {action: "conditions", PCID: PCID, fileType: fileType, ruleFor: ruleFor, ruleName: 'BorrowerStatus'},
                    beforeSend: function () {
                    },
                    success: function (data) {
                        if (data) {
                            $('#parameterOptions_' + rowId).html(''); // empty before appending
                            $.each(data, function (key, value) {
                                $('#parameterOptions_' + rowId)
                                    .append($("<option></option>")
                                        .attr("value", value.id)
                                        .text(value.text));
                            });
                            $('#parameterOptions_' + rowId).trigger('change.select2');
                        }
                    }
                });
            }
            else if(ruleFor === 'BorrowerType') {
                $.ajax({
                    type: "POST",
                    url: BOSSLURL + 'automatedRulesController.php',
                    data: {action: "conditions", PCID: PCID, fileType: fileType, ruleFor: ruleFor, ruleName: 'BorrowerType'},
                    beforeSend: function () {
                    },
                    success: function (data) {
                        if (data) {
                            $('#parameterOptions_' + rowId).html(''); // empty before appending
                            $.each(data, function (key, value) {
                                $('#parameterOptions_' + rowId)
                                    .append($("<option></option>")
                                        .attr("value", value.id)
                                        .text(value.text));
                            });
                            $('#parameterOptions_' + rowId).trigger('change.select2');
                        }
                    }
                });
            }
            else if(ruleFor === 'LoanProgram') {
                $.ajax({
                    type: "POST",
                    url: BOSSLURL + 'automatedRulesController.php',
                    data: {action: "conditions", PCID: PCID, fileType: fileType, ruleFor: ruleFor, ruleName: 'LoanProgram'},
                    beforeSend: function () {
                    },
                    success: function (data) {
                        if (data) {
                            $('#parameterOptions_' + rowId).html(''); // empty before appending
                            $.each(data, function (key, value) {
                                $('#parameterOptions_' + rowId)
                                    .append($("<option></option>")
                                        .attr("value", value.id)
                                        .text(value.text));
                            });
                            $('#parameterOptions_' + rowId).trigger('change.select2');
                        }
                    }
                });
            }
            else if(ruleFor === 'InitialSOW' || ruleFor === 'DrawRequest' || ruleFor === 'Revision') {
                $.ajax({
                    type: "POST",
                    url: BOSSLURL + 'automatedRulesController.php',
                    data: {action: "conditions", PCID: PCID, fileType: fileType, ruleFor: ruleFor, ruleName: ruleFor},
                    beforeSend: function () {
                    },
                    success: function (data) {
                        if (data) {
                            $('#parameterOptions_' + rowId).html('');
                            $.each(data, function (key, value) {
                                $('#parameterOptions_' + rowId)
                                    .append($("<option></option>")
                                        .attr("value", value.id)
                                        .text(value.text));
                            });
                            $('#parameterOptions_' + rowId).trigger('change.select2');
                        }
                    }
                });
            }

            $('#isDiv_1').removeClass('hidden');
            $('#parameterValue_1').removeClass('hidden');
            $('#plusMinus_1').removeClass('hidden');
            if (!ruleId) {
                $('#doneDiv').removeClass('hidden');
            }
            //IS-IS NOT
            $('#isIsNot_1').val('IS'); //static
            $('#isIsNotId_1').val('IS'); //static
        } else {
            $('#parameterOptions_' + rowId).html('');
            $('#doneDiv').addClass('hidden');
        }
    });

    //clone
    $(document).on('click', '.plus', function () {
        let plusIdSplit = $(this).attr('id').split('_');
        let plusId = parseInt(plusIdSplit[1]);
        let no = $('.parameterDivs').length; // clone section count
        if (no < 3) { //PFS, FSS, Workflow
            $('select.js-example-basic-multiple').select2("destroy");
            //add condition only if another param row added
            $('#andOr_' + plusId).removeClass('hidden');
            $('#conditionType_' + plusId).val('AND');//static
            $('#andOrTxt_' + plusId).text('AND');//static
            let clone = $('.parameterDivs:last').clone(true);
            let id = $('.parameterDivs:last').attr('id').split('_'); // get the last id to increment
            let lastId = id[1];
            let nextId = parseInt(lastId) + 1;
            let ParentRow = $(".parameterDivs:last");

            $(clone).find(':input, i').each(function (i) {
                let idArr;
                let elmId = this.id;
                idArr = elmId.split('_');
                let dropdidid;
                let newdropdidid;
                if (idArr.length > 0) {
                    if (this.type != 'radio') {
                        $(this).val('');
                        $(this).attr('id', idArr[0] + "_" + nextId);
                        if (this.type == 'select-multiple') {
                            $(this).html(''); // empty before appending
                            dropdidid = this.id;
                            jQuery(clone).find('div.js-example-basic-multiple').remove();
                            newdropdidid = idArr[0] + "_" + nextId;
                            $(this).attr('name', idArr[0] + "_" + nextId + '[]');
                            $(this).removeClass();
                            $(this).addClass('js-example-basic-multiple parameterValue');
                            $(this).attr('id', newdropdidid);
                        }
                    }
                }
            });

            //$('#newParameterDivAppendHere').append(clone);
            clone.clone(true).insertAfter(ParentRow);
            $('.andOr').removeClass('hidden');
            $('.andOr:last').addClass('hidden');
            //plusMinus
            $('.plusMinus:last').attr('id', 'plusMinus_' + nextId);
            $('.plus:last').attr('id', 'plus_' + nextId);
            $('.minus:last').attr('id', 'minus_' + nextId);
            $('.andOrTxt:last').attr('id', 'andOrTxt_' + nextId);
            //isIsNot
            $('.isIsNotVal:last').attr('id', 'isIsNot_' + nextId);
            $('.isIsNot:last').attr('id', 'isIsNotId_' + nextId);

            $('.noOfDayStatusDiv:last').attr('id', 'noOfDayStatusDiv_' + nextId);
            //get the last id to increment
            $('.parameterDivs:last').attr('id', 'newParameterDiv_' + nextId);

            //empty values for new child
            formValue.clearFormElementsValues("#newParameterDiv_" + nextId);

            //these lines fix the select2 clone issues
            $('select.js-example-basic-multiple').select2();
            $('select.js-example-basic-multiple').select2({allowClear: true});

            //remove the noOfDayStatusDiv
            if ($('.noOfDayStatusDiv').length > 1) {
                $('.noOfDayStatusDiv:last').remove();
            }


            //remove rule 1 value
            if ($('#ruleFor_2').length > 0) {
                let rule1 = $('#ruleFor_1').val();
                //$("#ruleFor_2 option[value='" + rule1 + "']").remove();
            }
            //remove rule 2 value
            if ($('#ruleFor_3').length > 0) {
                let rule2 = $('#ruleFor_2').val();
                //$("#ruleFor_3 option[value='" + rule2 + "']").remove();
            }
            //assign AND-OR value
            if ($('#conditionType_1').length > 0) {
                $('#conditionType_2').val($('#conditionType_1').val());
                $('#andOrTxt_2').text($('#conditionType_1').val());
            }
            //assign IS-IS NOT value
            $('#isIsNot_' + nextId).val('IS');
            $('#isIsNotId_' + nextId).text('IS');
            //show Minus only for last
            //deleting of conditions should be in desc order (1 by 1)
            $('.minus').addClass('hidden');
            $('.minus:last').removeClass('hidden');

        } else {
            let msg = "Max parameters limit reached";
            toastrNotification(msg, 'error');
            return false;
        }

    });

    //remove parameter div(s)
    $(document).on('click', '.minus', function () {
        let id = $(this).attr('id').split('_');
        let no = id[1];
        let conf = 'Are you sure you want to delete?';
        $.confirm({
            icon: 'fas fa-exclamation-triangle text-danger',
            closeIcon: true,
            title: 'Confirm',
            content: conf,
            type: 'red',
            backgroundDismiss: true,
            buttons: {
                yes: function () {
                    $('#newParameterDiv_' + no).remove();
                    if (no > 1) { //we cant zero it
                        var z = parseInt(no) - 1;
                        $('#conditionType_' + z).val('');
                        $('#andOrTxt_' + z).text('');
                        if ($('.minus').length > 1) {
                            //show Minus only for last
                            //deleting of conditions should be in desc order (1 by 1)
                            $('.minus').addClass('hidden');
                            $('.minus:last').removeClass('hidden');
                        }
                    }

                }, cancel: function () {

                },
            },
            onClose: function () {

            },
        });
    });

    //show Minus only for last / edit page load
    //deleting of conditions should be in desc order (1 by 1)
    if (ruleId > 0 && $('.minus').length > 0) {
        if ($('.minus').length === 1) {
            $('.minus').addClass('hidden'); // hide delete for first row
        } else {
            $('.minus').addClass('hidden');
            $('.minus:last').removeClass('hidden');
        }
    }

    //show actions div
    $(document).on('click', '#done', function () {
        //validate no of days
        let noOfDayStatus = $('#noOfDayStatus').val();
        /*if (!noOfDayStatus) {
            $('#noOfDayStatus').val(''); //No of Days field
            $('#statusUpdateId').val(''); //Status update, hidden fields, post value
            let msg = 'No of days should be greater than 0';
            //toastrNotification(msg, 'error', '', 'noOfDayStatus');
            if ($('#changeStatusDiv').length > 0) {
                $('#changeStatusDiv').addClass('hidden'); //hide the Change Status text
            }
            if ($('#actionStatusLink').length > 0) {
                $('#actionStatusLink').hide(); //hide the Change Status link
            }
            //return false;
        }*/
        //$('#changeStatusDiv').removeClass('hidden');
        /*if (noOfDayStatus) {
            //$('#noOfDayStatusTxt').text(noOfDayStatus);
            $('#actionStatusLink').show();
        } else {
            $('#changeStatusDiv').addClass('hidden');
            $('#actionStatusLink').hide();
        }*/
        let pfsVal = $('#parameterOptions_1').val();
        if (pfsVal) {
            //get the PFS to view
            $('#actions,#btnSaveCancel').removeClass('hidden');
        } else {
            let ruleFor = $("#ruleFor_1 option:selected").text();
            let msg = 'Please select ' + ruleFor;
            toastrNotification(msg, 'error', '', 'parameterOptions_1');
            return false;
        }
    });

    // AND OR
    $(".andOrTxt").click(function () {
        let data = $(this).attr('id').split('_');
        let no = data[1];
        $(this).text(function (i, text) {
            let txt = text === "AND" ? "OR" : "AND";
            $('#conditionType_' + no).val(txt);
            return txt;
        });
    });
    $(".andOrTxt").addClass('text-primary');
    // IS-IS NOT
    $('.isIsNot').click(function () {
        let data = $(this).attr('id').split('_');
        let no = data[1];
        $(this).text(function (i, text) {
            let txt = text === "IS" ? "IS NOT" : "IS";
            $('#isIsNot_' + no).val(txt);
            return txt;
        });
    });
    $(".isIsNot").addClass('text-primary');
    //Instant Action(s)
    $(document).on('click', '.actionStatus', function () {
        //assign to the hidden field
        $('#action').val('Status');
    });
    $(document).on('click', '.actionTask', function () {
        //assign to the hidden field
        $('#action').val('Task');
    });
    $(document).on('click', '.actionEmail', function () {
        //assign to the hidden field
        $('#action').val('Email');
    });
    $(document).on('click', '.actionWebhook', function () {
        //assign to the hidden field
        $('#action').val('Webhook');
    });
    $(document).on('click', '.actionUser', function () {
        //assign to the hidden field
        $('#action').val('User');
    });

    //Schedule Action(s)
    $(document).on('click', '.actionScheduleTask', function () {
        //assign to the hidden field
        $('#schaction').val('Task');
    });
    $(document).on('click', '.actionScheduleEmail', function () {
        //assign to the hidden field
        $('#schaction').val('Email');
    });
    $(document).on('click', '.actionScheduleWebhook', function () {
        //assign to the hidden field
        $('#schaction').val('Webhook');
    });
    $(document).on('change','.user', function() {
        let usersId = $('#usersId');
        let value = $(this).val();
        let haystack = usersId.val() ? usersId.val().split(",") : [];
        if ($(this).is(':checked')) {
            if (!haystack.includes(value)) {
                haystack.push(value);
            }
        } else {
            haystack = haystack.filter(item => item !== value);
        }
        usersId.val(haystack.join(","));
    });

    //assign instant actions to the rules
    $(document).on('click', '#saveAction', function () {
        let ids = [];
        let noOfDays = '';
        let action = $('#action').val(); //task/email/webhook/status
        let oldStatusIds = $('#parameterOptions_1').val();
        let chStatusId = $('#changeStatusId').val();
        let fileType = $('#ruleFileType').val();
        if (action === 'Status') {
            noOfDays = $('#noOfDayStatus').val();
            if (chStatusId) {
                $('#statusUpdateId').val(chStatusId);
            } else {
                $('#statusUpdateId').val('');
            }
        } else if (action === 'Task') {
            $('#tasksId').val('');
            $('.task:checkbox:checked').each(function (i) {
                if ($('#tasksId').val()) { // append value(s)
                    ids[i] = $('#tasksId').val() + "," + $(this).val();
                } else { // first value
                    ids[i] = $(this).val();
                }
            });
            $('#tasksId').attr("value", ids);
        } else if (action === 'Email') {
            $('#emailsId').val('');
            $('.email:checkbox:checked').each(function (i) {
                if ($('#emailsId').val()) { // append value(s)
                    ids[i] = $('#emailsId').val() + "," + $(this).val();
                } else { // first value
                    ids[i] = $(this).val();
                }
            });
            $('#emailsId').attr("value", ids);
        } else if (action === 'Webhook') {
            $('#webhooksId').val('');
            $('.webhook:checkbox:checked').each(function (i) {
                if ($('#webhooksId').val()) { // append value(s)
                    ids[i] = $('#webhooksId').val() + "," + $(this).val();
                } else { // first value
                    ids[i] = $(this).val();
                }
            });
            $('#webhooksId').attr("value", ids);
        } else if (action === 'User') {
         /*   $('#usersId').val('');
            $('.user:checkbox:checked').each(function (i) {
                if ($('#usersId').val()) { // append value(s)
                    ids[i] = $('#usersId').val() + "," + $(this).val();
                } else { // first value
                    ids[i] = $(this).val();
                }
            });
            $('#usersId').attr("value", ids);*/
        }
        //close the popup
        $("#exampleModal1").modal("hide");
        //get the Instant Actions to view on the rules page
        let tasksId = $('#tasksId').val();
        let emailsId = $('#emailsId').val();
        let webhooksId = $('#webhooksId').val();
        let usersId = $('#usersId').val();
        $.ajax({
            type: "POST", url: BOSSLURL + 'automatedRulesActionAjax.php', data: {
                action: "instant",
                PCID: PCID,
                id: ruleId,
                tasksId: tasksId,
                emailsId: emailsId,
                webhooksId: webhooksId,
                oldStatusIds: oldStatusIds,
                statusId: chStatusId,
                fileType: fileType,
                noOfDays: noOfDays,
                usersId: usersId
            }, beforeSend: function () {

            }, success: function (resp) {
                $("#instantActionDiv").html(resp);
            }
        });
    });
    //assign schedule actions to the rules
    $(document).on('click', '#saveSchAction', function () {
        let ids = [];
        let action = $('#schAction').val(); //Schedule - task/email/webhook
        if (action === 'Task') {
            $('#schtasksId').val('');
            $('.schtask:checkbox:checked').each(function (i) {
                if ($('#schtasksId').val()) { // append value(s)
                    ids[i] = $('#schtasksId').val() + "," + $(this).val();
                } else { // first value
                    ids[i] = $(this).val();
                }
            });
            $('#schtasksId').attr("value", ids);
        } else if (action === 'Email') {
            $('#schemailsId').val('');
            $('.schemail:checkbox:checked').each(function (i) {
                if ($('#schemailsId').val()) { // append value(s)
                    ids[i] = $('#schemailsId').val() + "," + $(this).val();
                } else { // first value
                    ids[i] = $(this).val();
                }
            });
            $('#schemailsId').attr("value", ids);
        } else if (action === 'Webhook') {
            $('#schwebhooksId').val('');
            $('.schwebhook:checkbox:checked').each(function (i) {
                if ($('#schwebhooksId').val()) { // append value(s)
                    ids[i] = $('#schwebhooksId').val() + "," + $(this).val();
                } else { // first value
                    ids[i] = $(this).val();
                }
            });
            $('#schwebhooksId').attr("value", ids);
        }
        //close the popup
        $("#exampleModal1").modal("hide");
        //get the Schedule Actions to view on the rules page
        let schtasksId = $('#schtasksId').val();
        let schemailsId = $('#schemailsId').val();
        let schwebhooksId = $('#schwebhooksId').val();
        $.ajax({
            type: "POST", url: BOSSLURL + 'automatedRulesActionAjax.php', data: {
                action: "schedule",
                PCID: PCID,
                id: ruleId,
                schtasksId: schtasksId,
                schemailsId: schemailsId,
                schwebhooksId: schwebhooksId
            }, beforeSend: function () {

            }, success: function (resp) {
                $("#scheduleActionDiv").html(resp);
                //get the red text and checked values
                schActContEvent();
            }
        });
    });

    //Update Rule Status
    $(document).on('click', '.updateStatus', function () {
        let data = $(this).attr('id').split('~');
        let PCID = data[0];
        let id = data[1];
        let conf = "Are you sure to update the Rule Status?";

        $.confirm({
            icon: 'fas fa-exclamation-triangle text-danger',
            closeIcon: true,
            title: 'Confirm',
            content: conf,
            type: 'red',
            backgroundDismiss: true,
            buttons: {
                yes: function () {
                    $.ajax({
                        type: "POST",
                        url: BOSSLURL + 'automatedRulesController.php',
                        data: {action: "update", PCID: PCID, id: id},
                        beforeSend: function () {

                        },
                        success: function () {
                            let msg = "Rule status updated successfully";
                            toastrNotification(msg, 'success');
                            window.location.reload();
                        }
                    });
                }, cancel: function () {

                },
            },
            onClose: function () {

            },
        });
    });

    //Cancel /Back to list view
    $('#cancel').click(function () {
        document.getElementById('back').click();
    });

    /*let noOfDayStatus = $('#noOfDayStatus').val();
    if (!noOfDayStatus) {
        //hide the Change Status link
        //$('#actionStatusLink').hide();
        $('#statusUpdateId').val('');
    }*/
    //auto trigger the Done btn click
    $(document).on('keyup', '#noOfDayStatus', function () {
        let noOfDayStatus = $('#noOfDayStatus').val(); //get the value again to check
        if (noOfDayStatus) {
            $('#done').click(); //this will view the Status Change Dropdown
        }
        /*else {
            $('#statusUpdateId').val(''); //Status update, hidden fields, post value
            $('.newStatusTxt').text(''); //New Primary Status Text UI
            if ($('#changeStatusDiv').length > 0) {
                $('#changeStatusDiv').addClass('hidden'); //hide the Change Status text
            }
            if ($('#actionStatusLink').length > 0) {
                $('#actionStatusLink').hide(); //hide the Change Status link
            }
        }*/
    });

    //Check if the rule has any existing jobs in the queue table
    //before you update the rule
    $("#automatedRulesForm").submit(function (e) {
        let id = $('#id').val();//Rule id
        let PCID = $('#PCID').val();//PCID
        let clearJobEnqueue = $('#clearJobEnqueue').val();//check Clear Job Enqueue
        let noOfDayStatus = $('#noOfDayStatus').val(); //# of days
        let statusUpdateId = $('#statusUpdateId').val(); // new status value
        //Form Validation
        var ruleFileType = $("#ruleFileType").val();
        if (!ruleFileType) {
            var msg = "Please Select The File Type / Module";
            toastrNotification(msg, 'error', '', 'ruleFileType');
            return false;
        }
        let ruleName = $("#ruleName").val();
        if (!ruleName) {
            var msg = "Please Add Rule Name";
            toastrNotification(msg, 'error', '', 'ruleName');
            return false;
        }
        //Rule Conditions Validation
        let pv = 0;
        $('.parameterValue').each(function () {
            var pid = $(this).attr('id');
            var pVal = $('#' + pid).val();
            if (pVal.length == 0) { // fail
                pv = pv + 1;
            }
        });
        if (pv > 0) {
            var msg = 'Please complete the "Rule triggered when" section';
            toastrNotification(msg, 'error');
            return false;
        }
        //validation for no of days
        let no = 0;
        //Automated Actions - Instant/Schedule/PFS (No of Days) Validation
        let fields = [
            'tasksId'
            , 'emailsId'
            , 'webhooksId'
            , 'schtasksId'
            , 'schemailsId'
            , 'schwebhooksId'
            , 'usersId'
            , 'statusUpdateId'
        ];
        $.each(fields, function (k, v) {
            if ($('#' + v).val() != '') { // we have comma separate values
                no = no + 1;
            }
        });
        if (no == 0) {
            var msg = "Please Assign Instant Action(s) Or Schedule Automated Action(s) To This Rule.";
            toastrNotification(msg, 'error');
            return false;
        }
    });

    //Check if the rule conditions are updated
    $(document).on('change', '.ruleFor, .parameterValue', function () {
        $("#isRuleCondUpdated").val("Yes");
    });
    //Continue Scheduled Event Status Enabled/Disabled
    $(document).on('click', '.contEventStatus', function () {
        $("#schaction").val("Yes");
        let ids = [];
        let actTypeSplit = $(this).attr('id').split('_');
        let actType = actTypeSplit[1];
        if (actType == 'ts') { //Task
            $('#schTsCe').val('');
            $('.schTsCe:checkbox:checked').each(function (i) {
                if ($('#schTsCe').val() != '') { // append value(s)
                    ids[i] = $('#schTsCe').val() + "," + $(this).val();
                } else { // first value
                    ids[i] = $(this).val();
                }
            });
            $('#schTsCe').attr("value", ids);
        } else if (actType == 'em') { //Email
            $('#schEmCe').val('');
            $('.schEmCe:checkbox:checked').each(function (i) {
                if ($('#schEmCe').val() != '') { // append value(s)
                    ids[i] = $('#schEmCe').val() + "," + $(this).val();
                } else { // first value
                    ids[i] = $(this).val();
                }
            });
            $('#schEmCe').attr("value", ids);

        } else if (actType == 'wh') { //Webhook
            $('#schWhCe').val('');
            $('.schWhCe:checkbox:checked').each(function (i) {
                if ($('#schWhCe').val() != '') { // append value(s)
                    ids[i] = $('#schWhCe').val() + "," + $(this).val();
                } else { // first value
                    ids[i] = $(this).val();
                }
            });
            $('#schWhCe').attr("value", ids);
        }
    });
    //toggle the red text
    $(document).on('click', '.clRedTxt', function () {
        let id = $(this).attr('id');
        let redTxt = '(continue event after conditions no longer match)';
        $('#txt_' + id).text(redTxt);
        if ($(this).is(':checked')) {
            $('#txt_' + id).show();
        } else {
            $('#txt_' + id).hide();
        }
    });
});
//show tooltips, popovers after ajax calls
$(document).ajaxSuccess(function () {
    $("[data-toggle=popover]").popover();
    $("[data-toggle=tooltip]").tooltip();
    // any other code
});
//remove the already selected options
//from the next select list
function filterRuleFor() {
    console.log({
        func: 'filterRuleFor',
    });
    /*if($('#ruleFor_1').val() !== 'PFS') {
        if ($('#actionStatusLink').length > 0) {
            $('#actionStatusLink').hide(); //hide the Change Status link
        }
    }*/
    //remove rule 1 value
    if ($('#ruleFor_2').length > 0) {
        var rule1 = $('#ruleFor_1').val();
        $("#ruleFor_2 option[value='" + rule1 + "']").remove();
    }
    //remove rule 2 value
    if ($('#ruleFor_3').length > 0) {
        var rule2 = $('#ruleFor_2').val();
        $("#ruleFor_3 option[value='" + rule2 + "']").remove();
    }
}

//get the red text and checked values Ajax Call / Append
function schActContEvent() {
    console.log({
        func: 'schActContEvent',
    });

    let schTsCe = $('#schTsCe').val();
    let schEmCe = $('#schEmCe').val();
    let schWhCe = $('#schWhCe').val();
    let redTxt = '(continue event after conditions no longer match)';
    //Schedule Task
    let schTsCeVal;
    if (schTsCe) {
        schTsCeVal = schTsCe.split(',');
        $.each(schTsCeVal, function (index, val) {
            try {
                $('#sch_ts_' + val).attr('checked', 'checked');
                $('#txt_sch_ts_' + val).text(redTxt);
            } catch (e) {

            }
        });
    }
    //Schedule Email
    let schEmCeVal;
    if (schEmCe) {
        schEmCeVal = schEmCe.split(',');
        $.each(schEmCeVal, function (index2, val2) {
            try {
                $('#sch_em_' + val2).attr('checked', 'checked');
                $('#txt_sch_em_' + val2).text(redTxt);
            } catch (e) {

            }
        });
    }
    //Schedule Webhook
    let schWhCeVal;
    if (schWhCe) {
        schWhCeVal = schWhCe.split(',');
        $.each(schEmCeVal, function (index3, val3) {
            try {
                $('#sch_wh_' + val3).attr('checked', 'checked');
                $('#txt_sch_wh_' + val3).text(redTxt);
            } catch (e) {

            }
        });
    }
}

function toggleSwitch(divName, fldName, on, off) {
    console.log({
        func: 'toggleSwitch',
    });

    let t1 = document.getElementById(fldName).value;
    if (t1 == on) {
        document.getElementById(fldName).value = off;
        document.getElementById(divName).className = "switch-off";
    } else {
        document.getElementById(fldName).value = on;
        document.getElementById(divName).className = "switch-on";

    }
}

function showEditSwitch(targetName, parent) {
    console.log({
        func: 'showEditSwitch',
    });
    let pVal = parseInt($('#' + parent).val());
    if (pVal === 1) {
        $("." + targetName).css("display", "block");
    } else {
        $("." + targetName).css("display", "none");
    }
}
