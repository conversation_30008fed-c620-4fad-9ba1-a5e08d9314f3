/* eslint-disable no-redeclare */
/* eslint-disable no-global-assign */
/* eslint-disable no-empty */
/* eslint-disable no-unused-vars */

/* eslint-disable no-undef */
/**

 ** Description  : Added the New Section of the Own the Property Info If only Shown the Hard / Private Money LOS Module is selected
 ** Developer    : Venkatesh
 ** Author       : AwataSoftSys
 ** Date         : Nov 18, 2016

 **/

// eslint-disable-next-line no-unused-vars
function showAndHideHMLOPropertyInfo(fldValue, clsName) {
    console.log({
        func: 'showAndHideHMLOPropertyInfo',
    });
    if (fldValue == 'Yes') {
        $('.' + clsName + fldValue).css("display", "block");
        $('.' + clsName + 'TD' + fldValue).css("display", "table-cell");
        $('.' + clsName + 'TDNo').css("display", "none");
        $('.' + clsName + 'No').css("display", "none");
    } else {
        $('.' + clsName + fldValue).css("display", "block");
        $('.' + clsName + 'TD' + fldValue).css("display", "table-cell");
        $('.' + clsName + 'TDYes').css("display", "none");
        $('.' + clsName + 'Yes').css("display", "none");
    }
}

function showAndHideHMLOCostDiv(fldValue, clsName) {
    console.log({
        func: 'showAndHideHMLOCostDiv',
    });
    if (fldValue == 'Yes') {
        $('.' + clsName + fldValue).css("display", "block");
        $('.' + clsName + 'No').css("display", "none");
        $('.' + clsName + 'TDNo').css("display", "table-cell");
    } else {
        $('.' + clsName + fldValue).css("display", "block");
        $('.' + clsName + 'Yes').css("display", "none");
        $('.' + clsName + 'TD' + fldValue).css("display", "none");
    }
}

function showAndHideHMLOExpBorDiv(fldValue, clsName) {
    console.log({
        func: 'showAndHideHMLOExpBorDiv',
    });
    if (fldValue == 'Yes') {
        $('.' + clsName + fldValue).css("display", "block");
        $('.' + clsName + 'No').css("display", "none");
    } else {
        $('.' + clsName + fldValue).css("display", "block");
        $('.' + clsName + 'Yes').css("display", "none");
    }
}

function showAndHideHMLOExpCoBorDiv(fldValue, clsName) {
    console.log({
        func: 'showAndHideHMLOExpCoBorDiv',
    });
    if (fldValue == 'Yes') {
        $('.' + clsName + fldValue).css("display", "block");
        $('.' + clsName + 'No').css("display", "none");
    } else {
        $('.' + clsName + fldValue).css("display", "block");
        $('.' + clsName + 'Yes').css("display", "none");
    }
}

/**

 ** Description  : Hide and Show the Co-Borrower Section in Web Form
 ** Developer    : Venkatesh
 ** Author       : AwataSoftSys
 ** Date         : Nov 29, 2016

 **/

function toggleSwitch(divName, fldName, on, off) {
    console.log({
        func: 'toggleSwitch 1',
        divName: divName,
        fldName: fldName,
        on: on,
        off: off
    });

    let div = $('#' + divName);
    let fld = $('#' + fldName);

    if (div.is(':checked')) {
        fld.val(on);
        div.addClass('switch-off');
        div.removeClass('switch-on');
    } else {
        fld.val(off);
        div.addClass('switch-on');
        div.removeClass('switch-off');
    }
}

function showHMLOCoBorrowerDiv(divId, noOfDiv) {
    console.log({
        func: 'showHMLOCoBorrowerDiv',
    });
    var opt = 0;
    try {
        opt = document.HMLOWebForm.isCoBorrower.value;
    } catch (e) {
    }

    if (noOfDiv > 0) {
        for (var i = 1; i <= noOfDiv; i++) {
            try {
                if (opt == '1') {
                    eval("document.getElementById('" + divId + i + "').style.display = 'block'");
                } else {
                    eval("document.getElementById('" + divId + i + "').style.display = 'none'");
                }
            } catch (e) {
            }
        }
    } else {
        try {
            if (opt == '1') {
                document.getElementById(divId).style.display = 'block';
            } else {
                document.getElementById(divId).style.display = 'none';
            }
        } catch (e) {
        }
    }

}

function brokerValidation() {
    console.log({
        func: 'Calling brokerValidation',
    });
    let _publicUser = parseInt($('#publicUser').val());
    let _LMRBroker = $('#LMRBroker');
    let brokerNumber = $('#agentId').val();
    if (_publicUser) {
        let isBrokerFile = $('input[name="REBroker"]:checked').val();
        let brokerMandatoryFields = $('#BrokerInfoDiv :input[type=text],#BrokerInfoDiv :input[type=email]');
        if (isBrokerFile === 'Yes' && brokerNumber === '3178f12db7e77c19') {
            let selectBroker = _LMRBroker.val() ?? '';
            brokerMandatoryFields.addClass(' brokerMandatoryField ');

            if (selectBroker === '') {
                brokerMandatoryFields.addClass(' brokerMandatoryField ');
                brokerMandatoryFields.removeAttr('disabled');
            } else {
                brokerMandatoryFields.removeClass('brokerMandatoryField');
                brokerMandatoryFields.attr('disabled', 'disabled');
            }
        } else if (isBrokerFile === 'Yes' && brokerNumber !== '3178f12db7e77c19') {
            brokerMandatoryFields.removeClass('brokerMandatoryField');
        } else {
            brokerMandatoryFields.removeClass('brokerMandatoryField');
        }
    }
}


/**
 ** Description  : Agent/Broker Section.
 ** Developer    : Suresh
 **/
function showAndHideBrokerInfo(fldValue, divId) {
    console.log({
        func: 'showAndHideBrokerInfo',
    });
    var defaultAgentId = $('#defaultAgentId').val();
    var wfOpt = $('#wfOpt').val();
    var hideBorrowerInfo = 0;
    var propDetailsProcess = '';
    var pcid = $('#FPCID').val();
    hideBorrowerInfo = $('#hideBorrowerInfo').val();
    propDetailsProcess = $('#propDetailsProcess').val();

    if (fldValue == 'Yes') {
        document.getElementById(divId).style.display = 'block';
        $('#' + divId).css("display", "block");
        $('.LmbInfo').show();
        $('.loanInfoLPSection').show();
        //$('.HMLOLoanInfoSections').show();
        enableBrokerFormFields('DD');
        clear_form_elements('brokerSection');
        checkdisable_form_elements("brokerSection", 1);

        if (hideBorrowerInfo == 1 && wfOpt == 'aa4465703ef4b17e') {
            $('.borrowerInfoSection').hide();
        }
        if ((propDetailsProcess == 'Looking for General Info' || propDetailsProcess == 'Actively Looking For Property')
            && hideBorrowerInfo) {
            $('.borrowerHideSection').hide();
        }
        $('.expLabel').html("Does borrower");
        $('.span_is').html("Is borrower");
        $('.expLabel2').html("does borrower");
    } else {
        document.getElementById(divId).style.display = 'none';
        $('#agentId').val(defaultAgentId);
        clear_form_elements('brokerSection');
        checkdisable_form_elements("brokerSection", 0);
        $('.loanInfoLPSection').show();
        $('.borrowerInfoSection').show();
        $('.borrowerHideSection').show();

        $('.expLabel').html("Do You");
        $('.span_is').html("Are you");
        $('.expLabel2').html("do you");

    }
    $('.lmrClientTypeDisp').show();
    if ($('#LMRClientType > option').length == 2) {
        $("#LMRClientType").val($("#LMRClientType option:eq(1)").val());
        if ($('#tabOpt').val() == '1003') {
            load1003Fields();
        } else {
            formControl.controlFormFields('fileModule', '', 'LMRClientType', 'loanProgram');
            try {
                customDev();
            } catch (e) {

            }
            showAndHideLandFieldsNew($("#LMRClientType option:eq(1)").val());
            allowToEditDisabledFields(this.value, '');
            getPCMinMaxLoanGuidelines('loanModForm', pcid);
            populatePCBasicLoanInfo('loanModForm', $("#LMRClientType option:eq(1)").val(), pcid, 'loc');
        }
    }
    brokerValidation();
}


function showAndHideLoanofficerInfo(fldValue, divId) {
    console.log({
        func: 'showAndHideLoanofficerInfo',
    });
    var defaultAgentId = $('#defaultAgentId').val();
    var wfOpt = $('#wfOpt').val();
    var hideBorrowerInfo = 0;
    var propDetailsProcess = '';

    hideBorrowerInfo = $('#hideBorrowerInfo').val();
    propDetailsProcess = $('#propDetailsProcess').val();

    if (fldValue == 'Yes') {
        document.getElementById(divId).style.display = 'block';
        $('#' + divId).css("display", "block");
        //$('#agentId').val('0');
        $('.LmbInfo').show();
        $('.loanInfoLPSection').show();
        //$('.HMLOLoanInfoSections').show();
        //enableBrokerFormFields('DD');


        clear_form_elements('loanofficerSection');
        checkdisable_form_elements("loanofficerSection", 1);

        if (hideBorrowerInfo == 1 && wfOpt == 'aa4465703ef4b17e') {
            $('.borrowerInfoSection').hide();
        }
        if ((propDetailsProcess == 'Looking for General Info' || propDetailsProcess == 'Actively Looking For Property')
            && hideBorrowerInfo) {
            $('.borrowerHideSection').hide();
        }
        $('.expLabel').html("Does borrower");
        $('.span_is').html("Is borrower");
        $('.expLabel2').html("does borrower");
    } else {
        document.getElementById(divId).style.display = 'none';
        //$('#agentId').val(defaultAgentId);


        clear_form_elements('loanofficerSection');
        checkdisable_form_elements("loanofficerSection", 0);
        $('.loanInfoLPSection').show();
        $('.borrowerInfoSection').show();
        $('.borrowerHideSection').show();

        $('.expLabel').html("Do You");
        $('.span_is').html("Are you");
        $('.expLabel2').html("do you");

    }
}

/**

 ** Description  : Check the Broker Information
 ** Developer    : Viji & Venkatesh
 ** Author       : AwataSoftSys
 ** Date         : Nov 30, 2016

 **/

function checkREBrokerEmailExist(formName) {
    console.log({
        func: 'checkREBrokerEmailExist',
    });
    if ($('#REBrokerEmail').val() != '') {
        $('#LMRBroker').removeClass('mandatory');

    }
    setTimeout("populateBrokerInfo('" + formName + "', '', '')", 200);
}

function populateBrokerInfo(formName, agentNumber, opt) {
    console.log({
        func: 'populateBrokerInfo',
    });

    clearHMLOBrokerInfoAsWebform(formName, opt);

    let _REBrokerEmail = $("form[name='" + formName + "'] input[name='REBrokerEmail']");
    let _LMRBroker = $("form[name='" + formName + "'] input[name='LMRBroker']");
    let _executiveId = $("form[name='" + formName + "'] input[name='executiveId']");
    let _FPCID = $("form[name='" + formName + "'] input[name='FPCID']");

    let brokerEmail = _REBrokerEmail.length > 0 ? _REBrokerEmail.val() : '';
    let LMRBroker = _LMRBroker.length > 0 ? _LMRBroker.val() : '';
    let executiveId = _executiveId.length > 0 ? parseInt(_executiveId.val()) : 0;
    let PCID = _FPCID.length > 0 ? parseInt(_FPCID.val()) : 0;
    brokerEmail = trim(brokerEmail);
    if (!brokerEmail && !agentNumber) {
        return false;
    }

    if (opt !== 'DD') {
        _LMRBroker.val('');
    }
    if (opt === 'DD') {
        _REBrokerEmail.val('');
    }

    HTTP.Post(siteSSLUrl + "backoffice/getAreYouBroker.php", {
        email: brokerEmail,
        eId: executiveId,
        PCID: PCID,
        aId: agentNumber,
        externalBroker: 0,
    }, function (data) {
        let agentPCID = data.hasOwnProperty('agentPCID') ? parseInt(data.agentPCID) : 0;
        if ((PCID === agentPCID) && (agentPCID > 0)) {
            let status = data.hasOwnProperty('status') ? data.status : '';
            let REBrokerNumber = data.hasOwnProperty('encBrokerNumber') ? data.encBrokerNumber : '';
            let brokerNumber = data.hasOwnProperty('brokerNumber') ? data.brokerNumber : '';
            let REBrokerFName = data.hasOwnProperty('brokerName') ? data.brokerName : '';
            let REBrokerCompany = data.hasOwnProperty('brokerCompany') ? data.brokerCompany : '';
            let REBrokerLName = data.hasOwnProperty('brokerLName') ? data.brokerLName : '';
            let REBrokerEmail = data.hasOwnProperty('brokerEmail') ? data.brokerEmail : '';
            let phoneNumber = data.hasOwnProperty('phoneNumber') ? data.phoneNumber : '';
            let alertBrMsg = data.hasOwnProperty('alertBrMsg') ? data.alertBrMsg : '';
            let externalBroker = data.hasOwnProperty('externalBroker') ? data.externalBroker : '';
            if (parseInt(externalBroker) === 1) {
                let _REBrokerEmail = $('#REBrokerEmail');
                toastrNotification('Email "' + _REBrokerEmail.val() + '" is Assigned to Loan Officer', 'error');
                _REBrokerEmail.val('');
                return false;
            }
            assignFieldValue(REBrokerEmail, 'REBrokerEmail');
            assignFieldValue(REBrokerCompany, 'REBrokerCompany');
            assignFieldValue(REBrokerFName, 'REBrokerFirstName');
            assignFieldValue(REBrokerLName, 'REBrokerLastName');
            assignFieldValue(phoneNumber, 'brokerPhone');
            $('form[name="' + formName + '"] [name="agentId"]').val(REBrokerNumber);
            allowToEditDisabledFields('', 'agentInfoCls');

            if (brokerEmail || opt === 'DD') {
                if (alertBrMsg) {
                    disableBrokerFormFields(opt);
                } else {
                    enableBrokerFormFields(opt);
                    let _REBrokerFirstName = $("form[name='" + formName + "'] input[name='REBrokerFirstName']");
                    if (_REBrokerFirstName.length > 0) {
                        _REBrokerFirstName.focus();
                    }
                }
            }
            brokerValidation();
            if (brokerNumber) {
                disableBrokerFormFields('DD');
                let brokerMandatoryFields = $('#BrokerInfoDiv :input[type=text],#BrokerInfoDiv :input[type=email]');
                brokerMandatoryFields.removeClass('brokerMandatoryField');
            }
        }
    });

}


function checkLoanofficerEmailExist(formName) {
    console.log({
        func: 'checkLoanofficerEmailExist',
    });
    if ($('#RELoanofficerEmail').val() != '') {
        $('#LMRLoanofficer').removeClass('mandatory');
    }
    setTimeout("populateLoanofficerInfo('" + formName + "', '', '')", 200);
}

function populateLoanofficerInfo(formName, agentNumber, opt) {
    console.log({
        func: 'populateLoanofficerInfo',
    });

    clearHMLOLoanOfficerInfoAsWebform(formName, opt);

    let _LMRLoanofficer = $("form[name='" + formName + "'] input[name='LMRLoanofficer']");
    let _RELoanofficerEmail = $("form[name='" + formName + "'] input[name='RELoanofficerEmail']");
    let _executiveId = $("form[name='" + formName + "'] input[name='executiveId']");
    let _FPCID = $("form[name='" + formName + "'] input[name='FPCID']");

    let loanofficerEmail = _RELoanofficerEmail.length > 0 ? _RELoanofficerEmail.val() : '';
    let LMRLoanofficer = _LMRLoanofficer.length > 0 ? _LMRLoanofficer.val() : '';
    let executiveId = _executiveId.length > 0 ? parseInt(_executiveId.val()) : 0;
    let PCID = _FPCID.length > 0 ? parseInt(_FPCID.val()) : 0;
    if (!loanofficerEmail && !agentNumber) {
        return false;
    }

    if (opt !== 'DD') {
        _LMRLoanofficer.val('');
    }
    if (opt === 'DD') {
        _RELoanofficerEmail.val('');
    }

    if (loanofficerEmail !== "" || agentNumber !== '') {

        HTTP.Post(siteSSLUrl + "backoffice/getAreYouBroker.php", {
            email: loanofficerEmail,
            eId: executiveId,
            PCID: PCID,
            aId: agentNumber,
            externalBroker: 1,
        }, function (data) {
            let agentPCID = data.hasOwnProperty('agentPCID') ? parseInt(data.agentPCID) : 0;
            if ((PCID === agentPCID) && (agentPCID > 0)) {

                let status = data.hasOwnProperty('status') ? data.status : '';
                let loanofficerNumber = data.hasOwnProperty('encBrokerNumber') ? data.encBrokerNumber : '';
                let loanofficerfname = data.hasOwnProperty('brokerName') ? data.brokerName : '';
                let loanofficerCompany = data.hasOwnProperty('brokerCompany') ? data.brokerCompany : '';
                let loanofficerlname = data.hasOwnProperty('brokerLName') ? data.brokerLName : '';
                let loanofficerEmail = data.hasOwnProperty('brokerEmail') ? data.brokerEmail : '';
                let phoneNumber = data.hasOwnProperty('phoneNumber') ? data.phoneNumber : '';
                let alertBrMsg = data.hasOwnProperty('alertBrMsg') ? data.alertBrMsg : '';
                let externalBroker = data.hasOwnProperty('externalBroker') ? data.externalBroker : '';
                if (externalBroker === 0) {
                    let _RELoanofficerEmail = $('#RELoanofficerEmail');
                    toastrNotification('Email "' + _RELoanofficerEmail.val() + '" is Assigned to Broker', 'error');
                    _RELoanofficerEmail.val('');
                    return false;
                }
                assignFieldValue(loanofficerEmail, 'RELoanofficerEmail');
                assignFieldValue(loanofficerCompany, 'RELoanofficerCompany');
                assignFieldValue(loanofficerfname, 'RELoanofficerFirstName');
                assignFieldValue(loanofficerlname, 'RELoanofficerLastName');
                assignFieldValue(phoneNumber, 'LoanofficerPhone');
                $('form[name="' + formName + '"] [name="secondaryAgentId"]').val(loanofficerNumber);

                if (loanofficerEmail || opt === 'DD') {
                    let _LoanofficerInfoDiv = $('#LoanofficerInfoDiv :input');
                    if (alertBrMsg) {
                        // disableBrokerFormFields(opt);
                        _LoanofficerInfoDiv.not("[name=LMRLoanofficer]").attr('disabled', true);
                        // $('.LmbInfo').hide();
                    } else {
                        //$('.LmbInfo').show();
                        _LoanofficerInfoDiv.attr('disabled', false);
                        // enableBrokerFormFields(opt);
                        let _RELoanofficerFirstName = $("form[name='" + formName + "'] input[name='RELoanofficerFirstName']");
                        _RELoanofficerFirstName.length > 0 ? _RELoanofficerFirstName.focus() : '';
                    }
                }

            }
        });
    }

}


/*
 * Auto Populate Borrower mailing Address as Co-Borrower mailing Address.
 */

function autoPopulateHMLOMailingAddressAsFile(formName) {
    console.log({
        func: 'autoPopulateHMLOMailingAddressAsFile',
    });
    var opt = 0;

    try {
        opt = document.HMLOWebForm.mailingAddressAsBorrower.value;
    } catch (e) {
    }
    if (opt == 1) {

        try {
            eval("document." + formName + ".coBorrowerMailingAddress.value = document." + formName + ".mailingAddress.value");
        } catch (e) {
        }
        try {
            eval("document." + formName + ".coBorrowerMailingCity.value = document." + formName + ".mailingCity.value");
        } catch (e) {
        }
        try {
            eval("document." + formName + ".coBorrowerMailingState.value = document." + formName + ".mailingState.value");
        } catch (e) {
        }
        try {
            eval("document." + formName + ".coBorrowerMailingZip.value = document." + formName + ".mailingZip.value");
        } catch (e) {
        }

    } else {
        clearHMLOMailingAddressAsFile(formName);
    }
}

/*
 *  Clear Co-Borrower mailing Address.
 */
function clearHMLOMailingAddressAsFile(formName) {
    console.log({
        func: 'clearHMLOMailingAddressAsFile',
    });
    try {
        eval("document." + formName + ".coBorrowerMailingAddress.value = ''");
    } catch (e) {
    }
    try {
        eval("document." + formName + ".coBorrowerMailingCity.value = ''");
    } catch (e) {
    }
    try {
        eval("document." + formName + ".coBorrowerMailingState.value = ''");
    } catch (e) {
    }
    try {
        eval("document." + formName + ".coBorrowerMailingZip.value = ''");
    } catch (e) {
    }

}


function autoPopulateHMLOPresentAdd() {
    console.log({
        func: 'autoPopulateHMLOPresentAdd',
    });
    var opt = 0;
    try {
        opt = document.HMLOWebForm.mailingAddrAsPresent.value;
    } catch (e) {
    }

    if (opt == 1) {
        try {
            document.HMLOWebForm.presentAddress.value = document.HMLOWebForm.mailingAddress.value;
        } catch (e) {
        }

        try {
            document.HMLOWebForm.presentCity.value = document.HMLOWebForm.mailingCity.value;
        } catch (e) {
        }
        try {
            document.HMLOWebForm.presentState.value = document.HMLOWebForm.mailingState.value;
        } catch (e) {
        }
        try {
            document.HMLOWebForm.presentZip.value = document.HMLOWebForm.mailingZip.value;
        } catch (e) {
        }

    } else {
        clearHMLOPresentAdd();
    }
}

function clearHMLOPresentAdd() {
    console.log({
        func: 'clearHMLOPresentAdd',
    });
    try {
        document.HMLOWebForm.presentAddress.value = "";
    } catch (e) {
    }
    try {
        document.HMLOWebForm.presentCity.value = "";
    } catch (e) {
    }
    try {
        document.HMLOWebForm.presentState.value = "";
    } catch (e) {
    }
    try {
        document.HMLOWebForm.presentZip.value = "";
    } catch (e) {
    }
}

function autoPopulateHMLOCoBPresentAddr() {
    console.log({
        func: 'autoPopulateHMLOCoBPresentAddr',
    });
    var opt = 0;
    try {
        opt = document.HMLOWebForm.coBorMailingAddrAsPresent.value;
    } catch (e) {
    }

    if (opt == 1) {
        try {
            document.HMLOWebForm.coBPresentAddress.value = document.HMLOWebForm.coBorrowerMailingAddress.value;
        } catch (e) {
        }

        try {
            document.HMLOWebForm.coBPresentCity.value = document.HMLOWebForm.coBorrowerMailingCity.value;
        } catch (e) {
        }
        try {
            document.HMLOWebForm.coBPresentState.value = document.HMLOWebForm.coBorrowerMailingState.value;
        } catch (e) {
        }
        try {
            document.HMLOWebForm.coBPresentZip.value = document.HMLOWebForm.coBorrowerMailingZip.value;
        } catch (e) {
        }

    } else {
        clearHMLOCoBPresentAdd();
    }
}

function clearHMLOCoBPresentAdd() {
    console.log({
        func: 'clearHMLOCoBPresentAdd',
    });
    try {
        document.HMLOWebForm.coBPresentAddress.value = "";
    } catch (e) {
    }
    try {
        document.HMLOWebForm.coBPresentCity.value = "";
    } catch (e) {
    }
    try {
        document.HMLOWebForm.coBPresentState.value = "";
    } catch (e) {
    }
    try {
        document.HMLOWebForm.coBPresentZip.value = "";
    } catch (e) {
    }
}

/*
function validateHMLOWebForm(formName, agentAviOpt, opt) {
    var trueCount   = 0; var REBroker = ''; var checkMandatoryFields = 0; var LMRId = 0; 

    eval("REBroker = document."+formName+".REBroker.value");                                            // Get Are Boroker Yes or No
    eval("loanPurpose = document."+formName+".typeOfHMLOLoanRequesting.value");                         // Get Transactional Type/ Loan purpose
    eval("checkMandatoryFields = document."+formName+".checkMandatoryFields.value");                    // Customized PC 
    eval("LMRId = document."+formName+".LMRId.value");                                                  // Check Saved Existing File or Not

    var trueCount   = 0; var checkMandatoryFields = 0; var LMRId = 0; var REBroker = '';
    try {
        eval("REBroker      = document."+formName+".REBroker.value");
    } catch(e){}

    eval("loanPurpose   = document."+formName+".typeOfHMLOLoanRequesting.value");
    eval("checkMandatoryFields  = document."+formName+".checkMandatoryFields.value");
    eval("LMRId         = document."+formName+".LMRId.value");

    if(isDefaultAgent == 1 && LMRId == 0) {
        if(chkIsBlank(formName, 'REBroker', 'Please check the Agent/Broker?')) {
            if(REBroker == 'No'){
               if(chkIsBlank(formName, 'LMRBroker', 'Please select your Broker.')) {
                    trueCount  = 1;
               }
            } else {
                if(isEmailOk(formName, 'REBrokerEmail') && chkIsBlank(formName, 'REBrokerFirstName','Please enter the Agent/Broker First Name')
                    && chkIsBlank(formName, 'REBrokerLastName','Please enter the Agent/Broker Last Name')&&
                    chkIsBlank(formName, 'REBrokerCompany','Please enter the Agent/Broker Company Name') &&
                    isPhoneNumber(formName, 'brokerPhone', 'bPhNo1', 'bPhNo2' ,'bPhNo3')) {
                    trueCount  = 1;
                }
            }
        }
    } else {
        trueCount  = 1;
    }

    if(opt != 'QuickForm' && trueCount == 1){
        if(chkIsBlank(formName, 'LMRClientType', 'Please Select Loan Program.') &&
            checkMandatoryFieldsForPC(formName, checkMandatoryFields, 'serviceProvider')) {
            trueCount  = 1;
        }else{
            trueCount  = 0;
        }

    }

    if (opt != 'QuickForm' && trueCount == 1) {
        if (isCheck('loanModForm','agreeTC') ){
            trueCount  = 1;
        } else {
            trueCount  = 0;
        }
    }

    if(REBroker == 'Yes' && isDefaultAgent != 1 && trueCount == 1 && LMRId == 0) {
        if(isEmailOk(formName,'REBrokerEmail') &&
        chkIsBlank(formName,'REBrokerFirstName','Please enter the Agent/Broker First Name')&&
        chkIsBlank(formName,'REBrokerLastName','Please enter the Agent/Broker Last Name')&&
        chkIsBlank(formName,'REBrokerCompany','Please enter the Agent/Broker Company Name')&&
        isPhoneNumber(formName, 'brokerPhone', 'REBrokerPhnNo1', 'REBrokerPhnNo2' ,'REBrokerPhnNo3')
        ) {
            trueCount = 1;
        } else {
            trueCount = 0;
        }
    }

    if((REBroker == 'No' || REBroker == 'Yes' || REBroker == '') && trueCount == 1 && opt == 'QuickForm') {
        if (chkIsBlank(formName, 'LMRClientType', 'Please Select Loan Program.') &&
            chkIsBlank(formName,'borrowerFName','Please enter the first name') &&
            chkIsBlank(formName,'borrowerLName','Please enter the last name') &&
            checkValidEmailId(formName,'borrowerEmail') &&
            isPhoneNumber(formName, 'phoneNumber', 'phNo1', 'phNo2', 'phNo3') &&
            checkMandatoryFieldsForPC(formName, checkMandatoryFields, 'serviceProvider')&&
            chkIsBlank(formName,'purchaseCloseDate','Please type the Target Closing Date') &&
            chkIsBlank(formName,'typeOfHMLOLoanRequesting','Please select Loan Purpose')&&
            chkIsBlank(formName,'loanTerm','Please select Loan Term') &&
            chkIsBlank(formName,'propertyAddress','Please enter the Address') &&
            chkIsBlank(formName,'propertyCity','Please enter the City') &&
            chkIsBlank(formName,'propertyState','Please select the State') &&
            chkIsBlank(formName,'propertyZip','Please enter the Zip') &&
            isCheck(formName,'agreeTC'))
        {
            trueCount = 1;
        } else {
            trueCount = 0;
        }
    }

    chkIsBlank(formName,'mortgageNotes','Please enter the Borrower Notes') // Remove Borrower Notes Mandatory check - (Pivotal #154481964)
    if((loanPurpose == 'Purchase' || loanPurpose == 'Bridge Loan' || loanPurpose == 'Blanket Loan' || loanPurpose == 'Commercial Purchase' || loanPurpose == 'Line of Credit') && trueCount == 1 && formName != 'loanModForm') {
        eval("rehabCon   = document."+formName+".propertyNeedRehab.value");

        if(isRadioSelected('HMLOWebForm','propertyNeedRehab','Please select property need repairs, rehab, or construction?')) {
            if(rehabCon == 'Yes') {
                if (chkIsBlank('HMLOWebForm','rehabCost','Please enter the Rehab / Construction Cost')&&
                    chkIsBlank('HMLOWebForm','rehabValByBor','Please enter the After Repair / Resale Value')&&
                    chkIsBlank('HMLOWebForm','rehabDuration','Please enter the How long will Rehab / Construction Take in Months?') ) {
                    trueCount = 1;
                } else {
                    trueCount = 0;
                }
            }
        } else {
            trueCount = 0;
        }

    }

    if((loanPurpose == 'Purchase' || loanPurpose == 'Bridge Loan' || loanPurpose == 'Blanket Loan' || loanPurpose == 'Commercial Purchase' || loanPurpose == 'Line of Credit'|| loanPurpose == 'Transactional') && trueCount == 1 && formName != 'loanModForm') {
        if (isRadioSelected('HMLOWebForm','acceptedPurchase','Please select distressed sale') ) {
            trueCount = 1;
        } else {
            trueCount = 0;
        }
    }
    if((loanPurpose == 'Purchase' || loanPurpose == 'Cash-Out / Refinance' || loanPurpose == 'Commercial Purchase' || loanPurpose == 'Line of Credit' || loanPurpose == 'Commercial Cash Out Refinance') && trueCount == 1 && opt == 'QuickForm') {
        eval("rehabCon   = document."+formName+".propertyNeedRehab.value");

        if(isRadioSelected('HMLOWebForm','propertyNeedRehab','Please select property need repairs, rehab, or construction?')) {
            if(rehabCon == 'Yes') {
                if (chkIsBlank('HMLOWebForm','rehabCost','Please enter the Rehab / Construction Cost')&&
                    chkIsBlank('HMLOWebForm','rehabValByBor','Please enter the After Repair / Resale Value')&&
                    chkIsBlank('HMLOWebForm','rehabDuration','Please enter the How long will Rehab / Construction Take in Months?') ) {
                    trueCount = 1;
                } else {
                    trueCount = 0;
                }
            }
        } else {
            trueCount = 0;
        }

    }

    if((loanPurpose == 'Purchase' || loanPurpose == 'Commercial Purchase' || loanPurpose == 'Transactional') && trueCount == 1 && opt == 'QuickForm') {
        if (isRadioSelected('HMLOWebForm','acceptedPurchase','Please select distressed sale') ) {
            trueCount = 1;
        } else {
            trueCount = 0;
        }
    }

    if(trueCount == 1){
        return true;
    } else {
        return false;

    }
}
*/
/**

 Description : Make the SMS Servicer provider mandatory in all HMLO versions of their webforms.
 Date        : Dec 16, 2017
 Function    : checkMandatoryFieldsForPC(formName, checkMandatoryFields, fieldName);
 Developer   : Suresh

 * 820  = Dave PC
 * 2    = AWATA PC
 * 3093 = North By NorthEast Lending PC

 **/

function checkMandatoryFieldsForPC(formName, checkMandatoryFields, fieldName) {
    console.log({
        func: 'checkMandatoryFieldsForPC',
    });
    if (checkMandatoryFields == 1) {
        if (chkIsBlank(formName, fieldName, 'Please select service provider.')) return true;
    } else {
        return true;
    }
}

function deleteWebFormCollateralPropertyInfo(LMRId, CID) {
    console.log({
        func: 'deleteWebFormCollateralPropertyInfo',
    });
    var cfmMsg = "Are you sure you want to delete?";
    if (confirm(cfmMsg)) {
        var delCnt = 0, url = "", qstr = "";
        url = "backoffice/deleteCollateralPropertyInfo.php";
        qstr = "CID=" + CID;
        try {
            xmlDoc = getXMLDoc(url, qstr);
        } catch (e) {
        }
        try {
            delCnt = xmlDoc.getElementsByTagName("delCnt")[0].firstChild.nodeValue;
        } catch (e) {
        }
        if (delCnt > 0) {
            showWebFormCollateralPropertyInfo(LMRId);
        }
    }
}

function validateWebFormCollateralInfo(formName) {
    console.log({
        func: 'validateWebFormCollateralInfo',
    });
    if (chkIsBlank(formName, 'collateralName', 'Please enter Collateral Name')
    ) {
        saveWebFormCollateralPropertyInfo();
    } else {
        return false;
    }
}

function saveWebFormCollateralPropertyInfo() {
    console.log({
        func: 'saveWebFormCollateralPropertyInfo',
    });

    var collateralName = "", collateralAddress = "", collateralCity = 0, collateralState = '',
        collateralZip = '', LMRId = 0;
    CID = 0, collateralOccupied1 = '';
    var OPStatus = "", collateralPropertyType = "", collateralUnits = 0, collateralOccupied = "",
        collateralLendableEquity = "", collateralLien1 = "", collateralLienPosition = "";

    LMRId = $('#LMRId').val();
    CID = $('#CID').val();
    encryptedLMRId = document.HMLOWebForm.lId.value;
    collateralName = $('#collateralName').val();
    collateralAddress = $('#collateralAddress').val();
    collateralCity = $('#collateralCity').val();
    collateralState = $('#collateralState').val();
    collateralZip = $('#collateralZip').val();
    collateralPropertyType = $('#collateralPropertyType').val();
    collateralUnits = $('#collateralUnits').val();
    collateralOccupied = document.collateralPropertyInfoForm.collateralOccupied.value;
    collateralLendableEquity = $('#collateralLendableEquity').val();
    collateralLien1 = $('#collateralLien1').val();
    collateralLienPosition = $('#collateralLienPosition').val();


    url = "pops/collateralPropertyInfoSave.php";
    qstr = "LMRId=" + LMRId + "&CID=" + CID + "&collateralName=" + collateralName + "&collateralAddress=" + collateralAddress + "&collateralCity=" + collateralCity + "&collateralState=" + collateralState + "&collateralZip=" + collateralZip + "&collateralPropertyType=" + collateralPropertyType + "&collateralUnits=" + collateralUnits + "&collateralOccupied=" + collateralOccupied + "&collateralLendableEquity=" + collateralLendableEquity + "&collateralLien1=" + collateralLien1 + "&collateralLienPosition=" + collateralLienPosition;
    try {
        xmlDoc = getXMLDoc(url, qstr);
    } catch (e) {
    }
    try {
        cnt = xmlDoc.getElementsByTagName("updateCnt")[0].firstChild.nodeValue;
    } catch (e) {
    }
    showWebFormCollateralPropertyInfo(encryptedLMRId);

}

function showWebFormCollateralPropertyInfo(LMRId) {
    console.log({
        func: 'showWebFormCollateralPropertyInfo',
    });

    var url = "", qstr = "";
    url = "backoffice/getCollateralPropertyInfo.php";
    qstr = "LMRId=" + LMRId + "&opt=WF";
    var displayList = "";
    try {
        displayList = getResponse(url, qstr);
    } catch (e) {
    }
    try {
        document.getElementById("showCollateralPropertyHistory").innerHTML = displayList;
    } catch (e) {
    }
    eval("ContactPop.init('" + POPSURL + "addCollateralPropertyInfo.php', 'addCollateralPropertyInfo.php', 'Add Collateral Property Info', '" + POPSURL + "','collateralPropertyInfoSave.php' , 500, 250)");
    try {
        ContactPop.hideOverlay(); /** Close- Popup **/
    } catch (e) {
    }
}

/**

 ** Description  : Validate the HMLO Property Insurance
 ** Date         : Jan 20, 2017

 **/

function validatePropertyInsurance() {
    console.log({
        func: 'validatePropertyInsurance',
    });
    if (chkIsBlank('HMLOPropInsForm', 'proInsName', 'Please enter Carrier Name')) {
        return true;
    } else {
        return false;
    }
}

/**

 ** Description  : Validate the HMLO Appraiser info
 ** Date         : Jan 21, 2017

 **/

function validateAppraiserInfo(opt) {
    console.log({
        func: 'validateAppraiserInfo',
    });

    if (opt == 'appraiser2') {
        if (chkIsBlank('HMLOAppraiserForm', 'appraiser2', 'Please enter Appraiser 2 Name')) {
            return true;
        } else {
            return false;
        }
    } else {
        if (chkIsBlank('HMLOAppraiserForm', 'appraiser1', 'Please enter Appraiser Name')) {
            return true;
        } else {
            return false;
        }
    }
}

/**

 ** Description  : Validate the HMLO Realtor info
 ** Date         : Jan 21, 2017

 **/

function validateRealtorInfo(opt) {
    console.log({
        func: 'validateRealtorInfo',
    });

    if (opt == 'realtor3') {
        if (chkIsBlank('HMLORealtorForm', 'BPO3', 'Please enter Realtor 3 Name')) {
            return true;
        } else {
            return false;
        }
    } else if (opt == 'realtor2') {
        if (chkIsBlank('HMLORealtorForm', 'BPO2', 'Please enter Realtor 2 Name')) {
            return true;
        } else {
            return false;
        }
    } else {
        if (chkIsBlank('HMLORealtorForm', 'BPO1', 'Please enter Realtor Name')) {
            return true;
        } else {
            return false;
        }
    }
}

function showAndHideHMLOLoanPurposeDiv(fldValue, divId, divId2) {
    console.log({
        func: 'showAndHideHMLOLoanPurposeDiv',
    });
    var temprehabCon = '';
    try {
        temprehabCon = document.HMLOWebForm.propertyNeedRehab.value;
        hideAndShowBlanketLoan(temprehabCon, 'doesPropertyNeedRehabDispDiv');
    } catch (e) {
    }

    if (fldValue == 'Rate & Term Refinance' || fldValue == 'Commercial Rate / Term Refinance' || fldValue == 'Transactional') {
        $('.doesPropertyNeedRehabDispDiv').hide();
        $('#' + divId).css("display", "none");
    } else {
        $('#' + divId).css("display", "block");
    }

    if (fldValue == 'Purchase' || fldValue == 'New Construction - Existing Land' || fldValue == 'Transactional') {
        $('#' + divId2).css("display", "block");
    } else {
        $('#' + divId2).css("display", "none");
    }
}

function autoPopulateHMLOPresentAdd(formName, targetDiv) {
    console.log({
        func: 'autoPopulateHMLOPresentAdd',
    });
    var opt = 0;
    if (targetDiv == 'mailingAddrAsPresent') {
        try {
            eval("opt = document." + formName + "." + targetDiv + ".value");
        } catch (e) {
        }

        if (opt == 1) {
            $(".hideMailingAddr").css("display", "none");
        } else {
            $(".hideMailingAddr").css("display", "block");
        }
    } else {
        try {
            eval("opt = document." + formName + "." + targetDiv + ".value");
        } catch (e) {
        }

        if (opt == 1) {
            $(".hideCoMailingAddr").css("display", "none");
        } else {
            $(".hideCoMailingAddr").css("display", "block");
        }
    }
}

/* Common method to show / hide a div based on the input switch */
function showOrHideDiv(fldValue, divID) {
    console.log({
        func: 'showOrHideDiv',
    });
    var fv = '';
    try {
        fv = $('#' + fldValue).val();
    } catch (e) {
    }
    if (fv == 'Yes') {
        $('#' + divID).css("display", "block");
    } else {
        $('#' + divID).css("display", "none");
    }
}

function showAndHideIsTaxesInsEscrowed(fldValue, divID) {
    console.log({
        func: 'showAndHideIsTaxesInsEscrowed',
    });
    var taxes = 0;
    var activeTab = '';
    var tableRow = 'block';

    try {
        activeTab = $('#activeTab').val();
    } catch (e) {
    }

    if (activeTab == 'HMLI') {
    } else {
        var tableRow = 'block';
    }

    try {
        taxes = $('#taxes1').val();
    } catch (e) {
    }

    if (fldValue == 'Yes') {
        $('.' + divID).css("display", tableRow);
        $('.' + divID).removeClass("secHide");
    } else {
        $('.' + divID).css("display", "none");
    }
    calculateHMLORealEstateTaxes(taxes);
}

function hideAndShowPropertyNeedRehab(fldValue, className) {
    console.log({
        func: 'hideAndShowPropertyNeedRehab',
    });
    if (fldValue == 'Yes') {
        $('.' + className).css("display", "block");
        try {
            $('.propertyNeedRehabinitialTddisp').css("display", "block");
            $('.propertyNeedRehabFootageTddisp').css("display", "block");
        } catch (e) {
        }
    } else {
        $('.' + className).css("display", "none");
        try {
            $('.propertyNeedRehabinitialTddisp').css("display", "none");
            $('.propertyNeedRehabFootageTddisp').css("display", "none");
        } catch (e) {
        }
    }
    var isGroundChecked;
    isGroundChecked = $('input[name=isThisGroundUpConstruction]:checked').val();
    if (isGroundChecked != 'Yes') {
        $('.groundUpFields').hide();
    }
    $('#rehabCostFinanced').val('');
    clear_form_elements('doesPropertyNeedRehabDispDiv');
    updateLoanDetail();
}

function hideAndShowPropertyNeedRehabNew(fldValue, className) {
    console.log({
        func: 'hideAndShowPropertyNeedRehabNew',
    });
    if (fldValue == 'Yes') {
        $('.' + className).css("display", "block");
        try {
            $('.propertyNeedRehabinitialTddisp').css("display", "block");
        } catch (e) {
        }
    } else {
        $('.' + className).css("display", "none");
        try {
            $('.propertyNeedRehabinitialTddisp').css("display", "none");
        } catch (e) {
        }
    }
    var rCostPerFin = $('#rehabCostPercentageFinanced').val();
    $('#rehabCostFinanced').val('');
    clear_form_elements('doesPropertyNeedRehabDispDiv');
    calculateHMLOFeeCostTotalLoanAmount('loanModForm', 'totalLoanAmount');
    //calculateTotalProjectCost('loanModForm', 'totalProjectCost');
    if (rCostPerFin == '' || rCostPerFin == 'undefind' || rCostPerFin == 0) rCostPerFin = 100;
    $('#rehabCostPercentageFinanced').val(rCostPerFin);
}

function additionalPropertyRestrictionsHideShow(fldValue, className, bootOpt) {
    console.log({
        func: 'additionalPropertyRestrictionsHideShow',
    });
    if (fldValue == 'Yes') {
        if (bootOpt == 1) {
            $('.' + className).css("display", "block");
        } else {
            $('.' + className).css("display", "block");
        }
    } else {
        $('.' + className).css("display", "none");
    }
}

function exitStrategyHideShow(fldValue, className, bootOpt) {
    console.log({
        func: 'exitStrategyHideShow',
    });
    var parentValues = ["AirBnb", "Long Term Rental", "Short Term Rental", "Fix & Hold"];
    if ($.inArray(fldValue, parentValues) != -1) {
        if (bootOpt == 1) {
            $('.rentalIncomePerMonthField').show();
            $('.exitStrategyExplain').show();
        } else {
            $('.rentalIncomePerMonthField').css("display", "block");
            $('.exitStrategyExplain').css("display", "block");
        }
        //$('.exitStrategyExplain').hide();
    } else if (fldValue != "") {
        if (bootOpt == 1) {
            $('.exitStrategyExplain').show();
        } else {
            $('.exitStrategyExplain').css("display", "block");
        }
        $('.rentalIncomePerMonthField').hide();
    } else {
        $('.exitStrategyExplain').hide();
        $('.rentalIncomePerMonthField').hide();
    }
}

function clear_form_elements(class_name) {
    console.log({
        func: 'clear_form_elements',
    });
    jQuery("." + class_name).find(':input').each(function () {
        switch (this.type) {
            case 'select-one':
                if (this.classList.contains('chzn-select')) {
                    $(this).val('').trigger('chosen:updated');
                } else {
                    $(this).val('');
                }
                break;
            case 'password':
            case 'text':
            case 'textarea':
            case 'hidden':
            case 'file':
            case 'select-multiple':
            case 'date':
            case 'number':
            case 'tel':
            case 'email':
                jQuery(this).val('');
                break;
            case 'checkbox':
            case 'radio':
                this.checked = false;
                break;
        }
    });
}

function interestRateDualPopulation(formName, targetName) {
    console.log({
        func: 'interestRateDualPopulation',
    });
    try {
        eval("lien1Rate     = document." + formName + ".lien1Rate.value");
    } catch (e) {
    }

    try {
        eval("document.getElementById('" + targetName + "').innerHTML = '" + lien1Rate + "'");
    } catch (e) {
    }
    calculateTotalDailyInterestCharge(formName, 'totalDailyInterestCharge');

    try {
        eval("document.getElementById('" + targetName + "').innerHTML = '" + totalNetOperatingIncome + "'");
    } catch (e) {
    }
}

function updateExitPlan(loanProgram, PCID) {

    console.log({
        func: 'loanInfoV2Form.updateExitPlan',
    });

    HTTP.Get('/backoffice/api_v2/exitPlan', {
        loanProgram: loanProgram,
        PCID: PCID,
    }, function (data) {

        const applicationExitOptions = data.hasOwnProperty('applicationExitOptions') ? data.applicationExitOptions : null;

        if (applicationExitOptions) {
            const selectElement = $('#applicationLoanExitPlan');
            const selectedVal = selectElement.val();
            //const applicationLoanExitPlanIsDisabled = selectElement.prop('disabled');
            selectElement.empty(); // Remove existing options
            globalJS.highlightField(selectElement);
            selectElement.append(`<option value=" "></option>`);
            $.each(applicationExitOptions, function (value, text) {
                selectElement.append(`<option value="${value}">${text}</option>`);
            });
            selectElement.val(selectedVal);
            //selectElement.prop('disabled', applicationLoanExitPlanIsDisabled);
            selectElement.trigger("chosen:updated");
        }

    });
}


/**
 Description     : Fetch the auto suggested the both Fees & cost and Loan Terms section in Loan Info Tab .
 Authors         : Viji, Venkatesh, Suresh.
 Developer       : Venkatesh.
 Date            : August 22, 2017.
 **/
function populatePCBasicLoanInfo(formName, loanPgm, PCID, ft, tabOpt = '') {
    console.log({
        func: 'populatePCBasicLoanInfo',
        formName: formName,
        loanPgm: loanPgm,
        PCID: PCID,
        ft: ft,
        tabOpt: tabOpt,
    });

    let _fileModule = $('#fileModule').val();
    if (!ft && _fileModule) {
        ft = _fileModule.join();
    }

    console.log({
        func: 'populatePCBasicLoanInfo',
        ft: ft,
        _fileModule: _fileModule,
    });

    HTTP.Post(siteSSLUrl + 'backoffice/getPCHMLOBasicLoanInfo.php', {
        'loanPgm': loanPgm,
        'PCID': PCID,
        'ft': ft
    }, function (data) {
        populateLoanGuidelines(data, tabOpt);
    });
    updateExitPlan(loanPgm, PCID);
}

function populateLoanGuidelines(obj, tabOpt = '') {
    console.log({
        func: 'populateLoanGuidelines:start',
        obj: obj,
        tabOpt: tabOpt,
    });

    let HMLOPCBasicLoanInfo = obj.hasOwnProperty('HMLOPCBasicLoanInfo') ? obj.HMLOPCBasicLoanInfo[0] : null;

    let fileType = obj.fileType;
    let customLoanGuidelinesFuturePropertyType = obj.hasOwnProperty('customLoanGuidelinesFuturePropertyType') ? obj.customLoanGuidelinesFuturePropertyType : null;
    let HMLOPCTransactionType = obj.hasOwnProperty('HMLOPCTransactionType') ? obj.HMLOPCTransactionType : null;
    let HMLOPCLoanTerm = obj.hasOwnProperty('HMLOPCLoanTerm') ? obj.HMLOPCLoanTerm : null;
    let HMLOPCExtnOption = obj.hasOwnProperty('HMLOPCExtnOption') ? obj.HMLOPCExtnOption : null;
    let HMLOPCOccupancy = obj.hasOwnProperty('HMLOPCOccupancy') ? obj.HMLOPCOccupancy : null;
    let glHMLOExtensionOption = obj.hasOwnProperty('glHMLOExtensionOption') ? obj.glHMLOExtensionOption : null;
    let HMLOPCState = obj.hasOwnProperty('HMLOPCState') ? obj.HMLOPCState : null;
    let HMLOPCPropertyType = obj.hasOwnProperty('HMLOPCPropertyType') ? obj.HMLOPCPropertyType : null;
    let propertyType = obj.propertyType;
    let useGLTT = obj.hasOwnProperty('useGLTT') ? parseInt(obj.useGLTT) : null;
    let useGLEO = obj.hasOwnProperty('useGLEO') ? parseInt(obj.useGLEO) : null;
    let useGLAmortization = obj.hasOwnProperty('useGLAmortization') ? parseInt(obj.useGLAmortization) : null;
    let useGLLT = obj.hasOwnProperty('useGLLT') ? parseInt(obj.useGLLT) : null;
    let useGLO = obj.hasOwnProperty('useGLO') ? parseInt(obj.useGLO) : null;
    let usePROST = obj.hasOwnProperty('usePROST') ? obj.usePROST : null;
    let usePTY = obj.hasOwnProperty('usePTY') ? parseInt(obj.usePTY) : null;
    let useGLCS = obj.hasOwnProperty('useGLCS') ? parseInt(obj.useGLCS) : null;
    let glHMLOCreditScore = obj.hasOwnProperty('glHMLOCreditScore') ? obj.glHMLOCreditScore : null;
    let HMLOPCAmortization = obj.hasOwnProperty('HMLOPCAmortization') ? obj.HMLOPCAmortization : null;
    let HMLOPCEntityType = obj.hasOwnProperty('HMLOPCEntityType') ? obj.HMLOPCEntityType : null;
    let HMLOPCBasicRateLockPeriodInfo = obj.hasOwnProperty('HMLOPCBasicRateLockPeriodInfo') ? obj.HMLOPCBasicRateLockPeriodInfo : null;
    let customLoanGuidelinesExitStrategy = obj.hasOwnProperty('customLoanGuidelinesExitStrategy') ? obj.customLoanGuidelinesExitStrategy : null;

    assignFieldValue('', 'rehabCostPercentageFinanced');      // First empty this field on selecting the loan program
    assignFieldValue('', 'setRehabDefaultVal');               // First empty this field on selecting the loan program
    assignFieldValue('', 'survey');      // First empty this field on selecting the loan program
    assignFieldValue('', 'wholeSaleAdminFee');

    let _borCreditScoreRange = $('#borCreditScoreRange');

    _borCreditScoreRange.empty();
    _borCreditScoreRange.append('<option value="">- Select -</option>');
    $('#coBorCreditScoreRange').empty();
    $('#coBorCreditScoreRange').append('<option value="">- Select -</option>');
    if (useGLCS) {
        $.each(glHMLOCreditScore, function (key, value) {
            $('#borCreditScoreRange').append('<option value="' + value + '">' + value + '</option>');
            $('#coBorCreditScoreRange').append('<option value="' + value + '">' + value + '</option>');
        });
    } else {
        $.each(HMLOPCBasicLoanInfo.PCBorrCreditScoreRange.split(','), function (key, value) {
            $('#borCreditScoreRange').append('<option value="' + value + '">' + value + '</option>');
            $('#coBorCreditScoreRange').append('<option value="' + value + '">' + value + '</option>');
        });
    }

    let _typeOfHMLOLoanRequesting = $('#typeOfHMLOLoanRequesting');
    let brTR = _typeOfHMLOLoanRequesting.val();
    if (!brTR) {
        brTR = 'Purchase';
    }
    if (HMLOPCTransactionType.length > 0) {
        if ($.inArray('Purchase', HMLOPCTransactionType) === -1) {
            if (useGLTT) {
                if (!brTR) {
                    brTR = HMLOPCTransactionType[0];
                }
            } else {
                if (!brTR) {
                    brTR = HMLOPCTransactionType[0]['transactionType'];
                }
            }
        }
        /** Input Field Value assign. Client Info **/
        $('#typeOfHMLOLoanRequesting option[value!=""]').remove();
        for (let j = 0; j < HMLOPCTransactionType.length; j++) {
            let tt;
            if (useGLTT) {
                tt = HMLOPCTransactionType[j];
            } else {
                tt = HMLOPCTransactionType[j]['transactionType'];
            }
            _typeOfHMLOLoanRequesting.append($('<option>', {text: tt, value: tt}));
        }
        if (_typeOfHMLOLoanRequesting.attr('data-value') !== undefined) {
            _typeOfHMLOLoanRequesting.val(_typeOfHMLOLoanRequesting.attr('data-value'));
        }
        if (_typeOfHMLOLoanRequesting.hasClass('chzn-select')) {
            _typeOfHMLOLoanRequesting.trigger("chosen:updated");
        }
    }
    if (fileType !== 'loc') {
        _typeOfHMLOLoanRequesting.val(brTR);
    }
    if (tabOpt !== 'LIV2') {
        showAndHideCommercialFields(brTR);
    }
    if (HMLOPCLoanTerm.length > 0) {
        let _loanTerm = $('#loanTerm');
        $('#loanTerm option[value!=""]').remove();
        for (let j = 0; j < HMLOPCLoanTerm.length; j++) {
            let LT;
            if (useGLLT) {
                LT = HMLOPCLoanTerm[j];
            } else {
                LT = HMLOPCLoanTerm[j]['loanTerm'];
            }
            $('#loanTerm').append($('<option>', {text: LT, value: LT}));
        }
        if (_loanTerm.attr('data-value') !== undefined) {
            _loanTerm.val(_loanTerm.attr('data-value'));
            if (_loanTerm.hasClass('chzn-select')) {
                _loanTerm.trigger("chosen:updated");
            }
        }

    }

    if (HMLOPCOccupancy.length > 0) {
        $('#isHouseProperty option[value!=""]').remove();
        for (let j = 0; j < HMLOPCOccupancy.length; j++) {
            let Occp;
            if (useGLO) {
                Occp = HMLOPCOccupancy[j];
            } else {
                Occp = HMLOPCOccupancy[j]['occupancy'];
            }
            $('#isHouseProperty').append($('<option>', {text: Occp, value: Occp}));
        }
    }

    if (HMLOPCState.length > 0) {
        /** Property State And Property Type added BY Suresh K - PT: 150301036 **/
        $('#propertyState_1 option[value!=""]').remove();
        for (let j = 0; j < HMLOPCState.length; j++) {
            $('#propertyState_1').append($('<option>', {
                text: HMLOPCState[j]['stateName'],
                value: HMLOPCState[j]['stateCode'],
            }));
        }
    }

    let _propertyTypeElement = $('#propertyType');
    let propertyVal = _propertyTypeElement.val();
    let propertyText = $('#propertyType option:selected').text();

    $('#propertyType option[value!=""]').remove();
    console.log({
        HMLOPCPropertyType: HMLOPCPropertyType,
        usePTY: usePTY,
    })
    if (usePTY) {
        for (let prop in HMLOPCPropertyType) {
            if (parseInt(prop) === 1000) {
                _propertyTypeElement.append('<option disabled style="color:white;background-color: rgb(0, 130, 187);">---Residential---</option>');
            } else if (parseInt(prop) === 1001) {
                _propertyTypeElement.append('<option disabled style="color:white;background-color: rgb(0, 130, 187);">---Commercial---</option>');
            } else {
//                _propertyTypeElement.append($('<option>', {text: HMLOPCPropertyType[prop], value: prop.trim()}));
                _propertyTypeElement.append('<option value="' + prop.trim() + '">' + HMLOPCPropertyType[prop] + '</option>');

            }
        }
    } else {
        for (let prop in HMLOPCPropertyType) {
            let PTC = HMLOPCPropertyType[prop]['propertyType'];
            let PTV = propertyType[PTC];

            if (parseInt(PTC) === 1000) {
                _propertyTypeElement.append('<option disabled style="color:white;background-color: rgb(0, 130, 187);">---Residential---</option>');
            } else if (parseInt(PTC) === 1001) {
                _propertyTypeElement.append('<option disabled style="color:white;background-color: rgb(0, 130, 187);">---Commercial---</option>');
            } else {
                _propertyTypeElement.append('<option value="' + PTC.trim() + '">' + PTV + '</option>');
            }
        }
    }
    _propertyTypeElement.val(propertyVal);
    if (!_propertyTypeElement.val()) {
        _propertyTypeElement.append('<option class="text-danger" selected value="' + propertyVal + '">' + propertyText + '</option>');
        _propertyTypeElement.parents('.col-md-7').find('.fa-info-circle').remove();
    } else {
        _propertyTypeElement.parents('.col-md-7').find('.fa-info-circle').remove();
    }
    _propertyTypeElement.trigger("chosen:updated");

    let _futurePropertyType = $('#futurePropertyType');
    let futurePropertyTypeVal = _futurePropertyType.val();
    $('#futurePropertyType option[value!=""]').remove();
    for (let prop in customLoanGuidelinesFuturePropertyType) {
        let futurePropertyType = customLoanGuidelinesFuturePropertyType[prop];

        if (parseInt(prop) === 1000) {
            _futurePropertyType.append('<option disabled style="color:white;background-color: rgb(0, 130, 187);">---Residential---</option>');
        } else if (parseInt(prop) === 1001) {
            _futurePropertyType.append('<option disabled style="color:white;background-color: rgb(0, 130, 187);">---Commercial---</option>');
        } else {
            _futurePropertyType.append('<option value="' + prop.trim() + '">' + futurePropertyType + '</option>');
        }
    }
    _futurePropertyType.trigger("chosen:updated");

    $('#extensionOption option[value!=""]').remove();
    if (useGLEO) {
        for (let eID in HMLOPCExtnOption) {
            $('#extensionOption').append($('<option>', {
                text: HMLOPCExtnOption[eID],
                value: eID
            }));
        }
    } else {
        console.log({
            func: 'populateLoanGuidelines',
            HMLOPCExtnOption: HMLOPCExtnOption,
            glHMLOExtensionOption: glHMLOExtensionOption,
        });
        for (let j = 0; j < HMLOPCExtnOption.length; j++) {
            for (let eID in glHMLOExtensionOption) {
                if (eID === HMLOPCExtnOption[j]['extnOption']) {
                    $('#extensionOption').append($('<option>', {
                        text: glHMLOExtensionOption[eID],
                        value: HMLOPCExtnOption[j]['extnOption']
                    }));
                }
            }
        }
    }


    console.log({
        HMLOPCAmortization: HMLOPCAmortization,
        useGLAmortization: useGLAmortization,
    });
    /** Amortization **/
    $('#lien1Terms option[value!=""]').remove();
    if (useGLAmortization) {
        for (let eID in HMLOPCAmortization) {
            $('#lien1Terms').append($('<option>', {
                text: HMLOPCAmortization[eID],
                value: HMLOPCAmortization[eID]
            }));
        }
    } else {
        for (let j = 0; j < HMLOPCAmortization.length; j++) {
            $('#lien1Terms').append($('<option>', {
                text: HMLOPCAmortization[j]['AmortizationVal'],
                value: HMLOPCAmortization[j]['AmortizationVal']
            }));
        }
    }
    if ($('#lien1Terms option:contains("Interest Only")').length > 0) {
        $('#lien1Terms').val('Interest Only');
    }
    /**End of Amortization **/

    /** Rate Lock Period **/
    $('#rateLockPeriod option[value!=""]').remove();
    if (HMLOPCBasicRateLockPeriodInfo) {
        for (let j in HMLOPCBasicRateLockPeriodInfo) {
            let rateLockPeriod = HMLOPCBasicRateLockPeriodInfo[j]['rateLockPeriod'];
            $('#rateLockPeriod').append($('<option>', {
                text: rateLockPeriod,
                value: rateLockPeriod
            }));
        }
    }
    /** End of Rate Lock Period **/


    if (HMLOPCEntityType.length > 0) {
        $('#entityType option[value!=""]').remove();
        for (let j in HMLOPCEntityType) {
            let HMLOPCEntityTypeName = HMLOPCEntityType[j];
            $('#entityType').append($('<option>', {
                text: HMLOPCEntityTypeName,
                value: HMLOPCEntityTypeName
            }));
        }
    }

    if (customLoanGuidelinesExitStrategy) {
        const selectedVal = $('#exitStrategy_mirror').val();
        $('select.exitStrategy_mirror').each(function () {
            const selectElement = $(this);
            selectElement.empty(); // Remove existing options
            globalJS.highlightField(selectElement);
            selectElement.append('<option value=" "></option>');

            $.each(customLoanGuidelinesExitStrategy, function (value, text) {
                selectElement.append(`<option value="${value}">${text}</option>`);
            });
            selectElement.val(selectedVal);
            selectElement.trigger("chosen:updated");
        });
    }

    if (HMLOPCBasicLoanInfo) {
        if ((parseFloat(HMLOPCBasicLoanInfo.originationPointsRate) > 0 || parseFloat(HMLOPCBasicLoanInfo.brokerPointsRate) > 0) && tabOpt === 'HMLI') {
            $.confirm({
                icon: 'fa fa-warning',
                closeIcon: true,
                title: 'Confirm',
                content: "Do you want to update the points and fees on this loan for a " + $("#LMRClientType option:selected").text(),
                type: 'green',
                backgroundDismiss: true,
                buttons: {
                    yes: function () {
                        assignAmountValue(HMLOPCBasicLoanInfo.originationPointsRate, 'originationPointsRate');
                        assignAmountValue(HMLOPCBasicLoanInfo.originationPointsValue, 'originationPointsValue');
                        assignAmountValue(HMLOPCBasicLoanInfo.brokerPointsRate, 'brokerPointsRate');
                        assignAmountValue(HMLOPCBasicLoanInfo.brokerPointsValue, 'brokerPointsValue');
                    },
                    No: function () {
                    },
                },
                onClose: function () {
                },
            });
        }
        assignAmountValue(HMLOPCBasicLoanInfo.applicationFee, 'applicationFee');
        assignAmountValue(HMLOPCBasicLoanInfo.processingFee, 'processingFee');
        assignAmountValue(HMLOPCBasicLoanInfo.appraisalFee, 'appraisalFee');
        assignAmountValue(HMLOPCBasicLoanInfo.drawsSetUpFee, 'drawsSetUpFee');
        assignAmountValue(HMLOPCBasicLoanInfo.drawsFee, 'drawsFee');
        assignAmountValue(HMLOPCBasicLoanInfo.miscellaneousFee, 'miscellaneousFee');
        assignAmountValue(HMLOPCBasicLoanInfo.closingCostFinanced, 'closingCostFinanced');
        assignAmountValue(HMLOPCBasicLoanInfo.minRate, 'minRate');
        assignAmountValue(HMLOPCBasicLoanInfo.maxRate, 'maxRate');
        assignAmountValue(HMLOPCBasicLoanInfo.estdTitleClosingFee, 'estdTitleClosingFee');
        assignAmountValue(HMLOPCBasicLoanInfo.wireFee, 'wireFee');

        assignAmountValue(HMLOPCBasicLoanInfo.valuationBPOFee, 'valuationBPOFee');
        assignAmountValue(HMLOPCBasicLoanInfo.valuationAVMFee, 'valuationAVMFee');
        assignAmountValue(HMLOPCBasicLoanInfo.creditReportFee, 'creditReportFee');
        assignAmountValue(HMLOPCBasicLoanInfo.backgroundCheckFee, 'backgroundCheckFee');
        assignAmountValue(HMLOPCBasicLoanInfo.floodCertificateFee, 'floodCertificateFee');
        assignAmountValue(HMLOPCBasicLoanInfo.documentPreparationFee, 'documentPreparationFee');
        assignAmountValue(HMLOPCBasicLoanInfo.servicingSetUpFee, 'servicingSetUpFee');
        assignAmountValue(HMLOPCBasicLoanInfo.taxServiceFee, 'taxServiceFee');
        assignAmountValue(HMLOPCBasicLoanInfo.floodServiceFee, 'floodServiceFee');
        assignAmountValue(HMLOPCBasicLoanInfo.inspectionFees, 'inspectionFees');
        assignAmountValue(HMLOPCBasicLoanInfo.projectFeasibility, 'projectFeasibility');
        assignAmountValue(HMLOPCBasicLoanInfo.dueDiligence, 'dueDiligence');
        assignAmountValue(HMLOPCBasicLoanInfo.UccLienSearch, 'UccLienSearch');
        assignAmountValue(HMLOPCBasicLoanInfo.otherFee, 'otherFee');
        assignAmountValue(HMLOPCBasicLoanInfo.taxImpoundsMonth, 'taxImpoundsMonth');
        assignAmountValue(HMLOPCBasicLoanInfo.taxImpoundsMonthAmt, 'taxImpoundsMonthAmt');
        assignAmountValue(HMLOPCBasicLoanInfo.taxImpoundsFee, 'taxImpoundsFee');
        assignAmountValue(HMLOPCBasicLoanInfo.insImpoundsMonthAmt, 'insImpoundsMonthAmt');
        assignAmountValue(HMLOPCBasicLoanInfo.insImpoundsFee, 'insImpoundsFee');
        assignAmountValue(HMLOPCBasicLoanInfo.thirdPartyFees, 'thirdPartyFees');
        assignAmountValue(HMLOPCBasicLoanInfo.insImpoundsMonth, 'insImpoundsMonth');
        assignAmountValue(HMLOPCBasicLoanInfo.escrowFees, 'escrowFees');
        assignAmountValue(HMLOPCBasicLoanInfo.recordingFee, 'recordingFee');
        assignAmountValue(HMLOPCBasicLoanInfo.underwritingFees, 'underwritingFees');
        assignAmountValue(HMLOPCBasicLoanInfo.propertyTax, 'propertyTax');
        assignAmountValue(HMLOPCBasicLoanInfo.travelNotaryFee, 'travelNotaryFee');
        assignAmountValue(HMLOPCBasicLoanInfo.bufferAndMessengerFee, 'bufferAndMessengerFee');
        assignAmountValue(HMLOPCBasicLoanInfo.prePaidInterest, 'prePaidInterest');
        assignAmountValue(HMLOPCBasicLoanInfo.realEstateTaxes, 'realEstateTaxes');
        assignAmountValue(HMLOPCBasicLoanInfo.insurancePremium, 'insurancePremium');
        assignAmountValue(HMLOPCBasicLoanInfo.closingCostFinancingFee, 'closingCostFinancingFee');
        assignAmountValue(HMLOPCBasicLoanInfo.attorneyFee, 'attorneyFee');
        assignFieldValue(HMLOPCBasicLoanInfo.downPaymentPercentage, 'downPaymentPercentage');
        assignFieldValue(HMLOPCBasicLoanInfo.rehabCostPercentageFinanced, 'rehabCostPercentageFinanced');
        assignFieldValue(HMLOPCBasicLoanInfo.totalLTC, 'LGMaxLTC');
        assignFieldValue(HMLOPCBasicLoanInfo.rehabCostPercentageFinanced, 'setRehabDefaultVal'); //set the rehab percentage default value acc to loan guide

        if (HMLOPCBasicLoanInfo.minRate > 0 || HMLOPCBasicLoanInfo.maxRate > 0) {
            $(".InRateRange").html('Optimal ' + HMLOPCBasicLoanInfo.minRate + " - " + HMLOPCBasicLoanInfo.maxRate);
        }
        assignAmountValue(HMLOPCBasicLoanInfo.survey, 'survey');
        assignAmountValue(HMLOPCBasicLoanInfo.wholeSaleAdminFee, 'wholeSaleAdminFee');
        if (tabOpt !== 'LIV2') {
            updateLoanDetail();
        }

    } else {
        try {
            document.loanModForm.minRate.value = "";
        } catch (e) {
        }
        try {
            document.loanModForm.maxRate.value = "";
        } catch (e) {
        }
        try {
            //document.loanModForm.lien1Rate.value = "0.00";
        } catch (e) {
        }

        clear_form_elements('feesCostDiv');
    }
    console.log({
        func: 'populateLoanGuidelines:end',
    });
}

function populateDualDateForHMLONewLoan(val, formName, targetFld) {
    console.log({
        func: 'populateDualDateForHMLONewLoan',
    });
    try {
        eval("document." + formName + "." + targetFld + ".value = val");
    } catch (e) {
    }
}

function updateBrokerNo(val, formName = null) {
    console.log({
        func: 'updateBrokerNo',
    });
    try {
        $('#agentId').val(val);
        if (formName) {
            $("form[name='" + formName + "'] input[name='REBrokerEmail']").val('');
        }
    } catch (e) {
    }
}

function updateLoanofficerNo(val, formName = null) {
    console.log({
        func: 'updateLoanofficerNo',
    });
    try {
        $('#secondaryAgentId').val(val);
        if (formName) {
            $("form[name='" + formName + "'] input[name='LMRLoanofficer']").val('');
        }
    } catch (e) {
    }
}

/*
* Description   : Tax Impounds Calculation
* Function      : calculateTaxImpoundsFee(formName, targetName)
* Formula       : Tax impounds Months * Tax Impounds Months Amt
* Developer     : Suresh K (SK)
*/
function calculateTaxImpoundsFee(formName, targetName) {
    console.log({
        func: 'calculateTaxImpoundsFee',
    });

    var taxImpoundsFee = 0;
    var taxImpoundsMonth = 0;
    var taxImpoundsMonthAmt = 0;

    eval("taxImpoundsMonth      = document." + formName + ".taxImpoundsMonth.value");
    eval("taxImpoundsMonthAmt   = document." + formName + ".taxImpoundsMonthAmt.value");

    try {
        taxImpoundsMonth = replaceCommaValues(taxImpoundsMonth);
        taxImpoundsMonthAmt = replaceCommaValues(taxImpoundsMonthAmt);
    } catch (e) {
    }

    if (taxImpoundsMonth == "") taxImpoundsMonth = 0;
    if (taxImpoundsMonthAmt == "") taxImpoundsMonthAmt = 0;

    taxImpoundsFee = parseFloat(taxImpoundsMonth) * parseFloat(taxImpoundsMonthAmt);
    taxImpoundsFee = autoNumericConverter(taxImpoundsFee.toFixed(2));

    try {
        eval("document.getElementById('" + targetName + "').value = '" + taxImpoundsFee + "'");
    } catch (e) {
    }

    if (formName != 'addBasicLoanTermForm') {
        calculateTotalFeesAndCost();
    }
}

function hideAndShowBlanketLoan(fldValue, className) {
    console.log({
        func: 'hideAndShowBlanketLoan',
    });

    var activeTab = '';
    var tableRow = 'flex';
    var tableCell = 'table-cell';

    try {
        activeTab = $('#activeTab').val();
    } catch (e) {
    }

    if (activeTab == 'HMLI') {
    } else {
        var tableRow = 'flex';
        var tableCell = 'flex';
    }

    if (fldValue == 'Yes') {
        if (className == 'isBlanketLoan' && $('#noOfPropertiesAcquiring_mirror').val() > 1 &&
            ($('.proploc.secShow').length > 0 || $('.propchar.secShow').length > 0 || $('.propdetails.secShow').length > 0
                || $('.propaccess.secShow').length > 0 || $('.rentRollSecFields.secShow').length > 0)) {
            $('#addSubpropDiv').show();
        }
        if (className == 'isEFBlanketLoan') {
            $('.' + className).css("display", tableCell);
        } else {
            $('.' + className).css("display", tableRow);
        }
    } else {
        $('.' + className).css("display", "none");
        if (className == 'isBlanketLoan') {
            $('#addSubpropDiv').hide();
        }
        $(".multipleSubProp").empty();

    }
}

function showAndHideBorSquareFootage(fldValue, className) {
    console.log({
        func: 'showAndHideBorSquareFootage',
    });
    if (fldValue == 'Yes') {
        $('.' + className).css("display", "block");
    } else {
        $('.' + className).css("display", "none");
    }
}

function showAndHideBorSquareFootageNew(fldValue, className) {
    console.log({
        func: 'showAndHideBorSquareFootageNew',
    });
    if (fldValue == 'Yes') {
        $('.' + className).css("display", "block");
    } else {
        $('.' + className).css("display", "none");
    }
}

function showAndHideInterestReserve(fldValue, className) {
    console.log({
        func: 'showAndHideInterestReserve',
    });
    if (fldValue !== 'Yes') {
        $('#interestOnInterestReserveFee').html('');
        $('#totalLoanAmountIOIReserveAmt').html('');
        $('input[name="addToTotalProjectValue"]').prop('checked', false);
        $('input[name="addToTotalProjectValue"][value="No"]').prop('checked', true);
        $('.addToTotalProjectValueClass').hide();
        calculateTotalProjectCost('loanModForm', 'totalProjectCost');
    } else {
        $('.addToTotalProjectValueClass').show();
    }
    updateLoanDetail();
}

/**
 * Descrition: New Supporting fields for Interest Rate.
 * Date      : Jan 04, 2018
 * PT #      : 153961351
 * Added By  : Viji
 **/
function populateCostOfCaptial(formName, lien1Rate) {
    console.log({
        func: 'populateCostOfCaptial',
    });

    try {
        lien1Rate = replaceCommaValues(lien1Rate);
    } catch (e) {
    }

    if (lien1Rate == "") lien1Rate = 0;

    lien1Rate = parseFloat(lien1Rate).toFixed(3);

    /*try {
        eval("document." + formName + ".costOfCapital.value = '" + lien1Rate + "'");
    } catch (e) {
    }*/

    try {
        var costOfCapital = document.getElementById("costOfCapital").value;
        if (costOfCapital == "") costOfCapital = 0;
        var yieldSpread = lien1Rate - costOfCapital;
        yieldSpread = parseFloat(yieldSpread).toFixed(3);
        eval("document." + formName + ".yieldSpread.value = '" + yieldSpread + "'");
    } catch (e) {
    }
}

function populateYieldSpread(formName, costOfCapital) {   //clubhouse 22278/update-the-order-of-how-yield-spread-cost-of-capital-get-calculated
    console.log({
        func: 'populateYieldSpread',
    });
    lien1Rate = document.getElementById("lien1Rate").value;
    try {
        lien1Rate = replaceCommaValues(lien1Rate);
    } catch (e) {
    }
    if (lien1Rate == "") lien1Rate = 0;

    lien1Rate = parseFloat(lien1Rate).toFixed(3);
    try {
        eval("document." + formName + ".yieldSpread.value = lien1Rate - costOfCapital");
    } catch (e) {
    }
}

/**
 * Descrition: Mandotory Checlist Items Availablity Check HMLO Web Forms.
 * Date      : Dec 17, 2017
 * PT #      : 153211423
 * Added By  : Suresh K
 **/
function checkMandatoryChecklistItemsWebForms(reqCnt, vaiFCls, formName, webFormType) {
    console.log({
        func: 'checkMandatoryChecklistItemsWebForms',
    });
    var branchReferralCode = agentReferralCode = '';
    var docUCnt = 0;
    var lId = 0;

    jQuery("." + vaiFCls).each(function () {
        if ($(this).val() == 1) {
            docUCnt++;
        }
    });

    if (reqCnt == docUCnt) {
        eval("branchReferralCode    = document." + formName + ".branchReferralCode.value");
        eval("agentReferralCode     = document." + formName + ".agentReferralCode.value");
        eval("lId                   = document." + formName + ".lId.value");
        eval("fOpt                  = document." + formName + ".fOpt.value");
        if (webFormType == 'LV') {
            var Url = siteSSLUrl + "backoffice/thankHMLOWebForm.php?bRc=" + branchReferralCode + "&aRc=" + agentReferralCode + "&fOpt=" + fOpt + "&lid=" + lId;
        } else {
            var Url = siteSSLUrl + "backoffice/thankHMLOQuickWebForm.php?bRc=" + branchReferralCode + "&aRc=" + agentReferralCode + "&fOpt=" + fOpt + "&lid=" + lId;
        }
        window.location.href = Url;
    } else {
        // alert('Please upload document for required docs.');
        toastrNotification('Please upload document for required docs.', 'error');
    }
}

/**
 * Descrition: "HMLO Web Forms" tab switching control for "Short Version"
 * Date      : Dec 17, 2017
 * PT #      : 153211423
 * Added By  : Suresh K
 **/
function showTabForShortVersion(LMRId, tabOpt, formName, aRefCode) {
    console.log({
        func: 'showTabForShortVersion',
    });
    var branchReferralCode = agentReferralCode = fOpt = lId = '';

    var wfOpt = $('#wfOpt').val();
    if (LMRId > 0 && tabOpt != 'CL') {
        eval("branchReferralCode    = document." + formName + ".branchReferralCode.value");
        eval("agentReferralCode     = document." + formName + ".agentReferralCode.value");
        eval("fOpt                  = document." + formName + ".fOpt.value");
        eval("lId                   = document." + formName + ".lId.value");

        if (aRefCode == 1) {
            var Url = siteSSLUrl + "HMLOWebForm.php?bRc=" + branchReferralCode + "&fOpt=" + fOpt + "&tabOpt=" + tabOpt + "&lid=" + lId + "&op=" + wfOpt;
        } else {
            var Url = siteSSLUrl + "HMLOWebForm.php?bRc=" + branchReferralCode + "&aRc=" + agentReferralCode + "&fOpt=" + fOpt + "&tabOpt=" + tabOpt + "&lid=" + lId + "&op=" + wfOpt;
        }
        window.location.href = Url;
    } else {
        //if(validateHMLOQuickWebForm(formName, fOpt)) {
        var x = document.getElementsByName(formName);
        x[0].submit();
        //}
    }
}

/**
 * Descrition: "HMLO Web Forms" tab switching control for "Long Version"
 * Date      : Dec 17, 2017
 * PT #      : 153211423
 * Added By  : Suresh K
 **/
function showTabForLongVersion(LMRId, tabOpt, formName, aRefCode) {
    console.log({
        func: 'showTabForLongVersion',
    });
    var branchReferralCode = agentReferralCode = fOpt = lId = '', allowToEdit = 0;

    eval("allowToEdit = document." + formName + ".allowToEdit.value");
    eval("branchReferralCode    = document." + formName + ".branchReferralCode.value");
    eval("agentReferralCode     = document." + formName + ".agentReferralCode.value");
    eval("fOpt                  = document." + formName + ".fOpt.value");
    eval("lId                   = document." + formName + ".lId.value");

    if (LMRId > 0 && tabOpt != 'CL') {
        if (aRefCode == 1) {
            var Url = siteSSLUrl + "HMLOWebForm.php?bRc=" + branchReferralCode + "&fOpt=" + fOpt + "&tabOpt=" + tabOpt + "&lid=" + lId;
        } else {
            var Url = siteSSLUrl + "HMLOWebForm.php?bRc=" + branchReferralCode + "&aRc=" + agentReferralCode + "&fOpt=" + fOpt + "&tabOpt=" + tabOpt + "&lid=" + lId;
        }
        window.location.href = Url;
    } else {
        if (allowToEdit == 1) {
            if (validateHMLOLongWebForm(formName, fOpt)) {
                var x = document.getElementsByName(formName);
                x[0].submit();
            }
        } else {
            if (aRefCode == 1) {
                var Url = siteSSLUrl + "HMLOWebForm.php?bRc=" + branchReferralCode + "&fOpt=" + fOpt + "&tabOpt=" + tabOpt + "&lid=" + lId;
            } else {
                var Url = siteSSLUrl + "HMLOWebForm.php?bRc=" + branchReferralCode + "&aRc=" + agentReferralCode + "&fOpt=" + fOpt + "&tabOpt=" + tabOpt + "&lid=" + lId;
            }
            window.location.href = Url;
        }
    }
}

/**
 * Descrition: Get available Mandatory required docs for both long & short version web forms. (Not Used Right Now.)
 * Date      : Dec 18, 2017
 * PT #      : 153211423
 * Functions : getChecklistMandatoryForWebForms(formName, opt, moduleType, serviceType).
 * JQFiles : : getChecklistMandatoryForWebForms.php.
 * Added By  : Suresh K
 **/
function getChecklistMandatoryForWebForms(formName, opt, moduleType, serviceType) {
    console.log({
        func: 'getChecklistMandatoryForWebForms',
    });
    var executiveId = FPCID = branchReferralCode = agentReferralCode = defaultAgentId = '';
    eval("executiveId           = document." + formName + ".branchId.value");
    eval("FPCID                 = document." + formName + ".FPCID.value");
    eval("branchReferralCode    = document." + formName + ".branchReferralCode.value");
    eval("agentReferralCode     = document." + formName + ".agentReferralCode.value");
    eval("defaultAgentId        = document." + formName + ".defaultAgentId.value");
    eval("fOpt                  = document." + formName + ".fOpt.value");

    $('#checklistLoader').show();
    $.ajax({
        type: 'POST',
        url: siteSSLUrl + 'JQFiles/getChecklistMandatoryForWebForms.php',
        data: jQuery.param({
            'executiveId': executiveId, 'agentNo': defaultAgentId, 'allowStatus': 1, 'opt': opt, 'PCID': FPCID,
            'moduleType': moduleType, 'serviceType': serviceType, 'fOpt': fOpt
        }),
        contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
        success: function (checklistItems) {
            $('#checklistLoader').hide();
            if (checklistItems != '') {
                $('.checklistItemsSection').show();
                $('#checklistItems').html(checklistItems);
            } else {
                $('.checklistItemsSection').hide();
                $('#checklistItems').html(checklistItems);
            }
        }
    });
}

/**
 * Descrition: Multiple file upload for webforms
 * Date      : Dec 18, 2017
 * PT #      : 153211423
 * Functions : handleFileSelect(e).
 * Added By  : Suresh K
 **/
function handleFileSelect(e) {
    console.log({
        func: 'handleFileSelect',
    });
    $('#documentLoader').show();
    var s = 1;
    if (!e.target.files || !window.FileReader) return;
    var files = e.target.files;
    var filesArr = Array.prototype.slice.call(files);
    filesArr.forEach(function (f) {
        var reader = new FileReader();
        reader.onload = function (e) {
            var preview = '';
            var name = f.name.split(".");
            var docName = name[0];
            var doctype = name[1];
            if (f.type.match("pdf.*")) {
                preview = "<i class=\"fa fa-file-pdf-o fa-3x pad10\" style=\"color:red\" aria-hidden=\"true\"></i>";
            } else if (doctype == "doc" || doctype == "docx") {
                preview = "<i class=\"fa fa-file-word-o fa-3x pad10\" style=\"color:blue\" aria-hidden=\"true\"></i>";
            } else if (f.type.match("excel.*") || doctype == "xlsx") {
                preview = "<i class=\"fa fa-file-excel-o fa-3x pad10\" style=\"color:green\" aria-hidden=\"true\"></i>";
            } else if (f.type.match("text.*") || f.type.match("csv.*")) {
                preview = "<i class=\"fa fa-file-text-o fa-3x pad10\" style=\"color:blue\" aria-hidden=\"true\"></i>";
            } else if (f.type.match("zip.*") || f.type.match("rar.*")) {
                preview = "<i class=\"fa fa-file-archive-o fa-3x pad10\" style=\"color:blue\" aria-hidden=\"true\"></i>";
            } else {
                preview = "<img src=\"" + e.target.result + "\" width=\"50\" height=\"50\">";
            }

            var html = "<tr><td><input type=\"text\" name=\"docCategory_" + s + "\" id=\"docCategory_" + s + "\" style=\"width:160px;\"></td><td><input type=\"text\" name=\"docName_" + s + "\" id=\"docName_" + s + "\" size=\"25\" value=\"" + docName + "\"></td><td>" + preview + "&nbsp;&nbsp;" + f.name + "</td></tr>";
            $('#uploadDocTable').append(html);
            s++;
        }
        reader.readAsDataURL(f);
    });
    setInterval(function () {
        $('#documentLoader').hide();
    }, 1000);
}

function handleFileSelectLMR(e) {
    console.log({
        func: 'handleFileSelectLMR',
    });
    var s = 1;
    var attr = e.target.attributes;
    var PCMIDId = attr.id.nodeValue;
    var PCMIDArr = PCMIDId.split("_");
    var PCMID = PCMIDArr[1];
    $('#documentLoader_' + PCMID).show();
    $('#uploadDocTable_' + PCMID).show();
    $('#tr_' + PCMID).addClass('chkDarkGrn');
    if (!e.target.files || !window.FileReader) return;
    var files = e.target.files;
    var filesArr = Array.prototype.slice.call(files);
    filesArr.forEach(function (f) {
        var reader = new FileReader();
        reader.onload = function (e) {
            var preview = '';
            var name = f.name.split(".");
            var docName = name[0];
            var doctype = name[1];
            if (f.type.match("pdf.*")) {
                preview = "<i class=\"fa fa-file-pdf-o fa-3x text-danger pad10\" aria-hidden=\"true\"></i>";
            } else if (doctype == "doc" || doctype == "docx") {
                preview = "<i class=\"fa fa-file-word-o fa-3x text-primary pad10\" aria-hidden=\"true\"></i>";
            } else if (f.type.match("excel.*") || doctype == "xlsx") {
                preview = "<i class=\"fa fa-file-excel-o fa-3x text-success pad10\" aria-hidden=\"true\"></i>";
            } else if (f.type.match("text.*") || f.type.match("csv.*")) {
                preview = "<i class=\"fa fa-file-text-o fa-3x text-primary pad10\" aria-hidden=\"true\"></i>";
            } else if (f.type.match("zip.*") || f.type.match("rar.*")) {
                preview = "<i class=\"fa fa-file-archive-o fa-3x text-primary pad10\" aria-hidden=\"true\"></i>";
            } else {
                preview = "<img src=\"" + e.target.result + "\" width=\"50\" height=\"50\">";
            }

            var html = "<div class=\"col-md-12\"><input type=\"text\" class=\"form-control col-md-12 upl_" + PCMID + "\" name=\"docName_" + PCMID + "[]\" id=\"docName_" + s + "\" value=\"" + docName + "\"></div><div class=\"clear\"></div>";
            $('#uploadDocTable_' + PCMID).append(html);
            s++;
        }
        reader.readAsDataURL(f);
    });
    setInterval(function () {
        $('#documentLoader_' + PCMID).hide();
    }, 1000);
}

function handleFileSelectTest(e) {
    console.log({
        func: 'handleFileSelectTest',
    });
    var s = 1;
    var clone;
    var attr = e.target.attributes;
    var PCMIDId = attr.id.nodeValue;
    var PCMIDArr = PCMIDId.split("_");
    var PCMID = PCMIDArr[1];
    $('#documentLoader_' + PCMID).show();
    $('#uploadDocTable_' + PCMID).show();
    $('#tr_' + PCMID).addClass('chkDarkGrn');

    if (!e.target.files || !window.FileReader) return;               // | to make sure the user select file/files

    var files = e.target.files;
    var filesArr = Array.prototype.slice.call(files);
    if (filesArr.length > 0) {
        filesArr.forEach(function (f) {
            var reader = new FileReader();
            reader.onload = function (e) {
                var preview = '';
                var name = f.name.split(".");
                var docName = name[0];
                var doctype = name[1];
                if (f.type.match("pdf.*")) {
                    preview = "<i class=\"fa fa-file-pdf-o fa-3x pad10\" style=\"color:red\" aria-hidden=\"true\"></i>";
                } else if (doctype == "doc" || doctype == "docx") {
                    preview = "<i class=\"fa fa-file-word-o fa-3x pad10\" style=\"color:blue\" aria-hidden=\"true\"></i>";
                } else if (f.type.match("excel.*") || doctype == "xlsx") {
                    preview = "<i class=\"fa fa-file-excel-o fa-3x pad10\" style=\"color:green\" aria-hidden=\"true\"></i>";
                } else if (f.type.match("text.*") || f.type.match("csv.*")) {
                    preview = "<i class=\"fa fa-file-text-o fa-3x pad10\" style=\"color:blue\" aria-hidden=\"true\"></i>";
                } else if (f.type.match("zip.*") || f.type.match("rar.*")) {
                    preview = "<i class=\"fa fa-file-archive-o fa-3x pad10\" style=\"color:blue\" aria-hidden=\"true\"></i>";
                } else {
                    preview = "<img src=\"" + e.target.result + "\" width=\"50\" height=\"50\">";
                }

                var html = "<div class=\"col-md-12\"><input type=\"text\" class=\"form-control input-sm col-md-8 upl_" + PCMID + "\" name=\"docName_" + PCMID + "[]\" id=\"docName_" + s + "\" value=\"" + docName + "\"></div><div class=\"clear\"></div>";
                $('#uploadDocTable_' + PCMID).append(html);
                s++;
            }

            reader.readAsDataURL(f);
        });
    }
    setInterval(function () {
        $('#documentLoader_' + PCMID).hide();
    }, 1000);
}

function calculateHMLORealEstateTaxes(taxes) {
    console.log({
        func: 'calculateHMLORealEstateTaxes',
    });
    var HMLORealEstateTaxes = 0;
    var isTaxesInsEscrowed = '';
    var activeTab = '';

    isTaxesInsEscrowed = $('input[name=isTaxesInsEscrowed]:checked', '#loanModForm').val();

    try {
        taxes = replaceCommaValues(taxes);
        isTaxesInsEscrowed = trim(isTaxesInsEscrowed);
    } catch (e) {
    }

    if (taxes == '') taxes = 0;
    if (isTaxesInsEscrowed == '') isTaxesInsEscrowed = '';

    if (isTaxesInsEscrowed == 'Yes') {
        HMLORealEstateTaxes = taxes;
    } else {
        HMLORealEstateTaxes = taxes / 12;
    }
    HMLORealEstateTaxes = parseFloat(HMLORealEstateTaxes);
    HMLORealEstateTaxes = autoNumericConverter(HMLORealEstateTaxes.toFixed(2));
    $('#HMLORealEstateTaxes').val(HMLORealEstateTaxes);
    $('#HMLORealEstateTaxesLabel').html('<h5>' + HMLORealEstateTaxes + '</h5>');

    activeTab = $('#activeTab').val();

    if (activeTab == 'HMLI') {
    } else {
        calculatePrimaryTotalHouseHoldExpenses(HMLORealEstateTaxes);
    }
}

/*function showDialogBox(tabOpt) {
    $("#warning").dialog({
        title: '',
        autoOpen: true,
        closeOnEscape: false,
        draggable: false,
        width: 460,
        minHeight: 50,
        modal: true, buttons: {
            Yes: function() {
                if(confirmAndNavigateTab(tabOpt)) {showLoader(); } else {hideLoader();}
                $(this).dialog( "close" );
            },
            No: function() {
                goToNextTab(tabOpt);
                $(this).dialog( "close" );
                showLoader();
        },
            Cancel: function() {  $(this).dialog( "close" ); }
        },
        resizable: false
    });
    $('#warning').html('Would you like to save the data before leaving this tab?');
}*/


function validateHMLOQuickWebForm(formName, fOpt) {
    console.log({
        func: 'validateHMLOQuickWebForm',
    });
    var trueCount = 0;
    var REBroker = '';
    var checkMandatoryFields = 0;
    var LMRId = 0, propDetailsProcess = '';

    eval("loanPurpose = document." + formName + ".typeOfHMLOLoanRequesting.value");                         // Get Transactional Type/ Loan purpose
    eval("checkMandatoryFields = document." + formName + ".checkMandatoryFields.value");                    // Customized PC
    eval("LMRId = document." + formName + ".LMRId.value");                                                  // Check Saved Existing File or Not

    eval("propDetailsProcess = document." + formName + ".propDetailsProcess.value");

    if (fOpt == 'agent' || LMRId > 0) {
        trueCount = 1;
    } else {
        eval("REBroker = document." + formName + ".REBroker.value");                                        // Get Are Boroker Yes or No
        eval("agentId = document." + formName + ".agentId.value");

        if (chkIsBlank(formName, 'REBroker', 'Are you a Loan Officer/Mortgage Broker or working with one?')) {
            if (REBroker == 'Yes' && agentId == 0) {
                if (isEmailOk(formName, 'REBrokerEmail') &&
                    chkIsBlank(formName, 'REBrokerFirstName', 'Please enter the Loan Officer/Mortgage Broker First Name') &&
                    chkIsBlank(formName, 'REBrokerLastName', 'Please enter the Loan Officer/Mortgage Broker Last Name') &&
                    chkIsBlank(formName, 'REBrokerCompany', 'Please enter the Loan Officer/Mortgage Broker Company Name') &&
                    isPhoneNumber(formName, 'brokerPhone', 'bPhNo1', 'bPhNo2', 'bPhNo3')) {
                    trueCount = 1;
                }
            } else {
                trueCount = 1;
            }
        }
    }

    if (trueCount == 1) {
        if (chkIsBlank(formName, 'LMRClientType', 'Please Select Loan Program.') &&
            chkIsBlank(formName, 'borrowerFName', 'Please enter the first name') &&
            chkIsBlank(formName, 'borrowerLName', 'Please enter the last name') &&
            checkValidEmailId(formName, 'borrowerEmail') &&
            isPhoneNumber(formName, 'phoneNumber', 'phNo1', 'phNo2', 'phNo3') &&
            checkMandatoryFieldsForPC(formName, checkMandatoryFields, 'serviceProvider')) {
            trueCount = 1;
        } else {
            trueCount = 0;
        }
    }

    if (trueCount == 1) {
        if (propDetailsProcess == 'Identified a Property' || propDetailsProcess == 'Signed Contract' || propDetailsProcess == 'I Own the Property') {
            if (chkIsBlank(formName, 'propertyAddress', 'Please enter the Address') &&
                chkIsBlank(formName, 'propertyCity', 'Please enter the City') &&
                chkIsBlank(formName, 'propertyState', 'Please select the State') &&
                chkIsBlank(formName, 'propertyZip', 'Please enter the Zip')) {
                trueCount = 1;
            } else {
                trueCount = 0;
            }
        } else {
            trueCount = 1;
        }
    }

    if (trueCount == 1) {
        //chkIsBlank(formName,'purchaseCloseDate','Please type the Target Closing Date') &&
        if (chkIsBlank(formName, 'loanTerm', 'Please select Loan Term') &&
            isCheck(formName, 'agreeTC')) {

            trueCount = 1;
        } else {
            trueCount = 0;
        }
    }

    if (trueCount == 1) {
        allowFormSubmit = 0;
        var allowFormSubmit = $("#allowFormSubmit").val();
        if (allowFormSubmit == 0) {
            return validateMinMaxLoanGuidelines();
        } else {
            return true;
        }
    } else {
        return false;
    }
}

function allowToEditDisabledFields(ltValue, className) {
    console.log({
        func: 'allowToEditDisabledFields',
    });
    var releseOpt = true;
    if (ltValue != '') releseOpt = false;
    if (className == '') {
        className = 'loanModForm';
    }
    jQuery("." + className).find(':input').each(function () {

        if (this.id != 'LMRClientType') {
            if (ltValue != '') {
                $(this).removeClass('disabledKeyFields');
            } else {
                $(this).addClass('disabledKeyFields');
            }
            switch (this.type) {
                case 'text':
                case 'textarea':
                case 'file':
                case 'submit':
                case 'select-one':
                    this.disabled = releseOpt;
                    break;
                case 'select-multiple':
                    this.disabled = releseOpt;
                    break;
                case 'date':
                case 'number':
                case 'tel':
                case 'email':
                    this.disabled = releseOpt;
                    break;
                case 'checkbox':
                case 'radio':
                    this.disabled = releseOpt;
                    break;
            }
        }
    });
}

function enableBrokerFormFields(opt) {
    console.log({func: 'enableBrokerFormFields'});

    let fields = [
        "REBrokerEmail", "REBrokerCompany", "REBrokerFirstName", "REBrokerLastName", "brokerPhone"
    ];

    fields.forEach(field => {
        let _field = $("form[name='loanModForm'] input[name='" + field + "']");
        if (_field.length > 0) {
            _field.prop('disabled', false);
        }
    });

    let targetField = (opt === 'DD') ? "REBrokerEmail" : "LMRBroker";
    let specificField = $("form[name='loanModForm'] input[name='" + targetField + "']");
    if (specificField.length > 0) {
        specificField.prop('disabled', false);
    }

    $(".agentInfoCls").each(function () {
        $(this).removeClass('disabledKeyFields');
    });
}


function disableBrokerFormFields(opt) {
    console.log({func: 'disableBrokerFormFields'});

    let brokerEmail = "";
    let publicUser = 0;

    let publicUserField = $("form[name='loanModForm'] input[name='publicUser']");
    if (publicUserField.length > 0) {
        publicUser = parseInt(publicUserField.val());
    }

    let brokerEmailField = $("form[name='loanModForm'] input[name='REBrokerEmail']");
    if (brokerEmailField.length > 0) {
        brokerEmail = $.trim(brokerEmailField.val());
    }

    if (brokerEmail !== "") {
        let brokerEmailInput = $("form[name='loanModForm'] input[name='brokerEmail']");
        if (brokerEmailInput.length > 0) {
            brokerEmailInput.prop('disabled', true);
        }

        if (publicUser === 0) {
            let btnSave = $("#btnSave");
            if (btnSave.length > 0) {
                btnSave.hide();
            }
        }
    }

    let brokerFields = [
        "REBrokerCompany", "REBrokerFirstName", "REBrokerLastName", "brokerPhone"
    ];

    brokerFields.forEach(field => {
        let _field = $("form[name='loanModForm'] input[name='" + field + "']");
        if (_field.length > 0) {
            _field.prop('disabled', true);
        }
    });

    let targetField = (opt === 'DD') ? "REBrokerEmail" : "LMRBroker";
    let specificField = $("form[name='loanModForm'] input[name='" + targetField + "']");
    if (specificField.length > 0) {
        specificField.prop('disabled', true);
    }
    enableTabs(true);

    $(".agentInfoCls").each(function () {
        $(this).addClass('disabledKeyFields');
    });
}


function enableTabs(enableOpt) {
    console.log({
        func: 'enableTabs',
    });
    try {
        document.getElementById('overlayout').className = "";
    } catch (e) {
    }
}

/*
 *  Clear Broker / Agent Info.
 */
function clearHMLOBrokerInfoAsWebform(formName, opt) {
    console.log({
        func: 'clearHMLOBrokerInfoAsWebform',
    });
    try {
        eval("document." + formName + ".brokerNumber.value = 0");
    } catch (e) {
    }
    try {
        eval("document." + formName + ".REBrokerCompany.value = ''");
    } catch (e) {
    }
    try {
        eval("document." + formName + ".REBrokerFirstName.value = ''");
    } catch (e) {
    }
    try {
        eval("document." + formName + ".REBrokerLastName.value = ''");
    } catch (e) {
    }
    try {
        eval("document." + formName + ".bPhNo1.value = ''");
    } catch (e) {
    }
    try {
        eval("document." + formName + ".bPhNo2.value = ''");
    } catch (e) {
    }
    try {
        eval("document." + formName + ".bPhNo3.value = ''");
    } catch (e) {
    }
    try {
        eval("document." + formName + ".bExt.value = ''");
    } catch (e) {
    }
    try {
        eval("document." + formName + ".brokerPhone.value = ''");
    } catch (e) {
    }
    enableBrokerFormFields(opt)
}

function clearHMLOLoanOfficerInfoAsWebform(formName, opt) {
    console.log({
        func: 'clearHMLOLoanOfficerInfoAsWebform',
    });
    try {
        eval("document." + formName + ".loanofficerNumber.value = 0");
    } catch (e) {
    }
    try {
        eval("document." + formName + ".RELoanofficerCompany.value = ''");
    } catch (e) {
    }
    try {
        eval("document." + formName + ".RELoanofficerFirstName.value = ''");
    } catch (e) {
    }
    try {
        eval("document." + formName + ".RELoanofficerLastName.value = ''");
    } catch (e) {
    }
    try {
        eval("document." + formName + ".bPhNo1.value = ''");
    } catch (e) {
    }
    try {
        eval("document." + formName + ".bPhNo2.value = ''");
    } catch (e) {
    }
    try {
        eval("document." + formName + ".bPhNo3.value = ''");
    } catch (e) {
    }
    try {
        eval("document." + formName + ".bExt.value = ''");
    } catch (e) {
    }

    try {
        eval("document." + formName + ".LoanofficerPhone.value = ''");
    } catch (e) {
    }
}

/**
 Description     : Fetch min Max Loan Amt and Max LTV for Loan Terms section in Loan Info Tab .
 Developer       : Viji, Venkatesh.
 Date            : Feb 06, 2018.
 **/
function getPCMinMaxLoanGuidelines(formName, PCID) {
    console.log({
        func: 'getPCMinMaxLoanGuidelines',
    });
    var loanPurpose = loanPgm = loanPgmDetailsBase64 = '';
    try {
        eval("loanPurpose = document." + formName + ".typeOfHMLOLoanRequesting.value");
    } catch (e) {
    }

    try {
        eval("loanPgm = document." + formName + ".LMRClientType.value");
    } catch (e) {
    }

    $.ajax({
        type: 'POST',
        url: siteSSLUrl + 'backoffice/getPCMinMaxLoanGuidelines.php',
        data: jQuery.param({'loanPgm': loanPgm, 'loanPurpose': loanPurpose, 'PCID': PCID}),
        contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
        success: function (myData) {
            var obj = $.parseJSON(myData);
            var maxLTV = obj.maxLTV;
            var minLoanAmount = obj.minLoanAmount;
            var maxLoanAmount = obj.maxLoanAmount;
            var maxARV = obj.maxLTVAfterRehab;
            var minRate = obj.minRate;
            var maxRate = obj.maxRate;
            var LGMaxLTC = obj.totalLTC;
            var LGMinMidfico = obj.minMidFico;
            var LGMaxMidfico = obj.maxMidFico;
            var LGMaxGround = obj.maxPropertyForGrndConst;
            var LGMinGround = obj.minPropertyForGrndConst;
            var LGMaxFixFlop = obj.maxPropertyForFixFlop;
            var LGMinFixFlop = obj.minPropertyForFixFlop;
            var LGMaxOrgPoints = obj.maxPoints;
            var LGMinOrgPoints = obj.minPoints;
            var LGDownPaymentPerc = obj.downPaymentPercentage;
            var LGElgibleState = obj.elgibleState;
            //var loanPgmDetails = obj.loanPgmDetails;
            var loanPgmDetailsBase64 = obj.loanPgmDetailsBase64;
            var reqForLoanProUnderwriting = obj.reqForLoanProUnderwriting;

            let MinSeasoningBusinessBankruptcyVal = parseInt(obj.MinSeasoningBusinessBankruptcyVal);
            let MinSeasoningForeclosureVal = parseInt(obj.MinSeasoningForeclosureVal);
            let MinSeasoningPersonalBankruptcyVal = parseInt(obj.MinSeasoningPersonalBankruptcyVal);
            let minTimeVal = parseInt(obj.minTimeVal);

            let MinSeasoningBusinessBankruptcyValText = obj.MinSeasoningBusinessBankruptcyValText;
            let MinSeasoningForeclosureValText = obj.MinSeasoningForeclosureValText;
            let MinSeasoningPersonalBankruptcyValText = obj.MinSeasoningPersonalBankruptcyValText;
            let minTimeValText = obj.minTimeValText;

            let loanGuideLineId = obj.hasOwnProperty('BLID') && !isNaN(parseInt(obj.BLID))
                ? parseInt(obj.BLID, 10)
                : null;
            $('#loanGuideLineId').val(loanGuideLineId);

            if (MinSeasoningBusinessBankruptcyVal > -1) {
                $('#MinSeasoningBusinessBankruptcyVal').val(MinSeasoningBusinessBankruptcyVal);
                $('#MinSeasoningBusinessBankruptcyValText').val(MinSeasoningBusinessBankruptcyValText);
            }
            if (MinSeasoningForeclosureVal > -1) {
                $('#MinSeasoningForeclosureVal').val(MinSeasoningForeclosureVal);
                $('#MinSeasoningForeclosureValText').val(MinSeasoningForeclosureValText);
            }
            if (MinSeasoningPersonalBankruptcyVal > -1) {
                $('#MinSeasoningPersonalBankruptcyVal').val(MinSeasoningPersonalBankruptcyVal);
                $('#MinSeasoningPersonalBankruptcyValText').val(MinSeasoningPersonalBankruptcyValText);
            }

            if (minTimeVal > -1) {
                $('#minTimeVal').val(minTimeVal);
                $('#minTimeValText').val(minTimeValText);
            }

            if (maxLTV > 0) {
                $('#LGMaxLTV').val(maxLTV);    // LG -> Means Loan Guidelines
            } else {
                $('#LGMaxLTV').val(0);
            }

            if (minLoanAmount > 0) {
                $('#LGMinLoanAmount').val(minLoanAmount);
            } else {
                $('#LGMinLoanAmount').val(0);
            }

            if (maxLoanAmount > 0) {
                $('#LGMaxLoanAmount').val(maxLoanAmount);
            } else {
                $('#LGMaxLoanAmount').val(0);
            }

            if (maxARV > 0) {
                $('#LGMaxARV').val(maxARV);
            } else {
                $('#LGMaxARV').val(0);
            }

            if (minRate > 0) {
                $('#minRate').val(minRate);
            } else {
                $('#minRate').val(0);
            }

            if (maxRate > 0) {
                $('#maxRate').val(maxRate);
            } else {
                $('#maxRate').val(0);
            }

            if (LGMaxLTC > 0) {
                $('#LGMaxLTC').val(LGMaxLTC);
            } else {
                $('#LGMaxLTC').val(0);
            }

            if (LGMinMidfico > 0) {
                $('#LGMinMidfico').val(LGMinMidfico);
            } else {
                $('#LGMinMidfico').val(0);
            }
            if (LGMaxMidfico > 0) {
                $('#LGMaxMidfico').val(LGMaxMidfico);
            } else {
                $('#LGMaxMidfico').val(0);
            }

            if (LGMinFixFlop > 0) {
                $('#LGMinFixFlop').val(LGMinFixFlop);
            } else {
                $('#LGMinFixFlop').val(0);
            }

            if (LGMaxFixFlop > 0) {
                $('#LGMaxFixFlop').val(LGMaxFixFlop);
            } else {
                $('#LGMaxFixFlop').val(0);
            }
            if (LGMinGround > 0) {
                $('#LGMinGround').val(LGMinGround);
            } else {
                $('#LGMinGround').val(0);
            }
            if (LGMaxGround > 0) {
                $('#LGMaxGround').val(LGMaxGround);
            } else {
                $('#LGMaxGround').val(0);
            }

            if (LGMaxOrgPoints > 0) {
                $('#LGMaxOrgPoints').val(LGMaxOrgPoints);
            } else {
                $('#LGMaxOrgPoints').val(0);
            }
            if (LGMinOrgPoints > 0) {
                $('#LGMinOrgPoints').val(LGMinOrgPoints);
            } else {
                $('#LGMinOrgPoints').val(0);
            }

            if (LGDownPaymentPerc > 0) {
                $('#LGDownPaymentPerc').val(LGDownPaymentPerc);
            } else {
                $('#LGDownPaymentPerc').val(0);
            }
            $('#LGElgibleState').val(LGElgibleState);
            if (reqForLoanProUnderwriting != '') {
                try {
                    tinymce.get('expectForDueDiligence').setContent(nl2br(reqForLoanProUnderwriting));
                } catch (e) {
                }
            }
            if (typeof loanPgmDetailsBase64 === "undefined") {
                loanPgmDetailsBase64 = '';
            }
            if (loanPgmDetailsBase64 != '') {
                if ($('#loanprogramtooltip').length > 0) {
                    prg = window.atob(loanPgmDetailsBase64);
                    $('#loanprogramtooltip').show();
                    $('#loanprogramtooltip').attr('title', prg);
                    $('#loanprogramtooltip').attr('data-original-title', prg);
                }
            } else {
                if ($('#loanprogramtooltip').length > 0) {
                    $('#loanprogramtooltip').hide();
                }
            }
            validateMinMaxLoanGuidelines();
        }
    });
}

function nl2br(str, is_xhtml) {
    console.log({
        func: 'nl2br',
    });
    if (typeof str === 'undefined' || str === null) {
        return '';
    }
    var breakTag = (is_xhtml || typeof is_xhtml === 'undefined') ? '<br />' : '<br>';
    return (str + '').replace(/([^>\r\n]?)(\r\n|\n\r|\r|\n)/g, '$1' + breakTag + '$2');
}

function pushOrAppend(array, key, value) {
    // Check if the key already exists in the array
    let existing = $.grep(array, function (item) {
        return item.key === key;
    });

    if (existing.length > 0) {
        // If the key exists, append the value to the existing key's value
        existing[0].value += `, ${value}`;
    } else {
        // If the key doesn't exist, push a new key-value pair to the array
        array.push({key: key, value: value});
    }
}

function combinedGuideLines(loanAmountGuidelines = []) {
    return loanAmountGuidelines.length > 0 ? '<br>' + $.map(loanAmountGuidelines, function (item) {
        return item.key + item.value;
    }).join('<br>') : '';
}


function validateMinMaxLoanGuidelines(scrollingEnabled) {
    console.log({
        func: 'validateMinMaxLoanGuidelines:start',
    });
    if (!scrollingEnabled) scrollingEnabled = 'no';

    let _divGuidelinesErrorMsg = $('#divGuidelinesErrorMsg');
    let loanGuideLineId = parseInt($('#loanGuideLineId').val());
    if (isNaN(loanGuideLineId) || loanGuideLineId === 0) {
        _divGuidelinesErrorMsg.html('');
        _divGuidelinesErrorMsg.hide();
        $("#allowFormSubmit").val(1);
        return true;
    }
    // Initialize variables with default values
    let j = 0,
        l = 0,
        msg = '',
        interestRate = 0,
        maxLTV = 0,
        minLoanAmount = 0,
        maxLoanAmount = 0,
        maxARV = 0,
        minRate = 0,
        maxRate = 0,
        LGMaxLTC = 0,
        chkAcquisitionLTV = 0,
        ARV = 0,
        chkTotalLoanAmount = 0,
        LGMaxMidfico = 0,
        LGMinMidfico = 0,
        LGMaxGround = 0,
        LGMinGround = 0,
        LGMaxFixFlop = 0,
        LGMinFixFlop = 0,
        LGMaxOrgPoints = 0,
        LGMinOrgPoints = 0,
        LGDownPaymentPerc = 0,
        currentLoanToCost = 0,
        loanPurpose = '';

    let publicUser = 0;
    let userRole = '';
    let LMRInternalLoanGuidelines = '';
    let LMRInternalLoanGuidelinesData = '';

    // Retrieve values using jQuery
    maxLTV = $('#LGMaxLTV').val();
    minLoanAmount = $('#LGMinLoanAmount').val();
    maxLoanAmount = $('#LGMaxLoanAmount').val();
    maxARV = $('#LGMaxARV').val();
    minRate = $('#minRate').val();
    maxRate = $('#maxRate').val();
    LGMaxLTC = $('#LGMaxLTC').val();
    LGMaxMidfico = $('#LGMaxMidfico').val();
    LGMinMidfico = $('#LGMinMidfico').val();
    LGMaxGround = $('#LGMaxGround').val();
    LGMinGround = $('#LGMinGround').val();
    LGMaxFixFlop = $('#LGMaxFixFlop').val();
    LGMinFixFlop = $('#LGMinFixFlop').val();
    LGMaxOrgPoints = $('#LGMaxOrgPoints').val();
    LGMinOrgPoints = $('#LGMinOrgPoints').val();
    LGDownPaymentPerc = $('#LGDownPaymentPerc').val();
    let LGElgibleState = $('#LGElgibleState').val();
    let activetab = $('#activetab').val();
    let userGroup = $('#userGroup').val();
    let currentDSCR = getFieldsValue('debtServiceRatio');
    let minDSCR = getFieldsValue('minDSCR');

    let _publicUser = $('#loanModForm [name="publicUser"]');
    publicUser = _publicUser.length > 0 ? parseInt(_publicUser.val()) : 0;

    let _userRole = $('#loanModForm [name="userRole"]');
    userRole = _userRole.length > 0 ? _userRole.val() : '';

    let _chkAcquisitionLTV = $('#acquisitionLTV');
    chkAcquisitionLTV = _chkAcquisitionLTV.length > 0 ? _chkAcquisitionLTV.html() : '';

    let _currentLoanToCost = $('#Loan-to-Cost');
    currentLoanToCost = _currentLoanToCost.length > 0 ? _currentLoanToCost.html() : '';

    ARV = getTextValue('ARV');

    let _loanPurpose = $('#loanModForm [name="typeOfHMLOLoanRequesting"]');
    loanPurpose = _loanPurpose.length > 0 ? _loanPurpose.val() : '';

    let _inVal = $('#loanModForm [name="lien1Rate"]');
    interestRate = _inVal.length > 0 ? _inVal.val() : 0;


    if (loanPurpose === 'Cash-Out / Refinance'
        || loanPurpose === 'Commercial Cash Out Refinance'
        || loanPurpose === 'Refinance'
        || loanPurpose === 'Delayed Purchase') {
        let _chkTotalLoanAmount = $('#loanModForm [name="CORTotalLoanAmt"]');
        if (_chkTotalLoanAmount.length > 0) {
            chkTotalLoanAmount = _chkTotalLoanAmount.val();
        } else {
            _chkTotalLoanAmount = $('#coTotalAmt');
            if (_chkTotalLoanAmount.length > 0) {
                chkTotalLoanAmount = getFieldsValue('coTotalAmt');
            }
        }
    } else if (loanPurpose === 'Line of Credit') {
        let _chkTotalLoanAmount = $('#loanModForm [name="LOCTotalLoanAmt"]');
        if (_chkTotalLoanAmount.length > 0) {
            chkTotalLoanAmount = _chkTotalLoanAmount.val();
        }
    } else {
        let _chkTotalLoanAmount = $('#totalLoanAmount1');
        if (_chkTotalLoanAmount.length > 0) {
            chkTotalLoanAmount = _chkTotalLoanAmount.val();
        }
    }

// Utility function to get value or default to 0 if empty or invalid


// Use the utility function for all checks
    let currentMidFico = getValueOrDefault('#midFicoScore');
    let currentFixflipProp = getValueOrDefault('#borNoOfREPropertiesCompleted');
    let chkGround = getValueOrDefault('#borRehabPropCompleted');
    let currentOriginationPointsRate = getValueOrDefault('#originationPointsRate');
    let chkDownPaymentPerc = getValueOrDefault('#downPaymentPercentage');

    if (chkAcquisitionLTV === '') chkAcquisitionLTV = 0;
    if (maxARV === '') maxARV = 0;
    if (currentLoanToCost === '') currentLoanToCost = 0;
    interestRate = (interestRate === '' || isNaN(interestRate)) ? 0 : parseFloat(interestRate);

    try {
        chkTotalLoanAmount = replaceCommaValues(chkTotalLoanAmount);
        chkAcquisitionLTV = replaceCommaValues(chkAcquisitionLTV);
        currentLoanToCost = replaceCommaValues(currentLoanToCost);
        ARV = replaceCommaValues(ARV);
    } catch (e) {
    }
    //inVal               = replaceCommaValues(inVal);
    LMRInternalLoanGuidelines = $('#LMRInternalLoanGuidelines').val();
    if (typeof (LMRInternalLoanGuidelines) !== "undefined") {
        LMRInternalLoanGuidelinesData = $.parseJSON(LMRInternalLoanGuidelines);
        //console.log(LMRInternalLoanGuidelinesData);
    }

    let internalService;

    if (activetab !== 'CI') {
        if (chkTotalLoanAmount > 0) {
            if (parseFloat(minLoanAmount) > 0 && parseFloat(chkTotalLoanAmount) < parseFloat(minLoanAmount)) {
                msg = "* The Loan Amount Range Set In This Program's Guidelines Is Between " + convertInputToAbsoluteValueWithDollar(minLoanAmount) + ' and ' + convertInputToAbsoluteValueWithDollar(maxLoanAmount) + '. The Current Loan Amount is ' + convertInputToAbsoluteValueWithDollar(chkTotalLoanAmount) + '.';
                j++;
            } else if (parseFloat(maxLoanAmount) > 0 && parseFloat(chkTotalLoanAmount) > parseFloat(maxLoanAmount)) {
                msg = "* The Loan Amount Range Set In This Program's Guidelines Is Between " + convertInputToAbsoluteValueWithDollar(minLoanAmount) + ' and ' + convertInputToAbsoluteValueWithDollar(maxLoanAmount) + '. The Current Loan Amount is ' + convertInputToAbsoluteValueWithDollar(chkTotalLoanAmount) + '.';
                j++;
            }
            if (userGroup === 'Employee') {
                /* Internal Loan CustomGuidelines */
                let loanAmountGuidelines = [];
                $.each(LMRInternalLoanGuidelinesData, function (key, customData) {

                    minLoanAmount = customData.minLoanAmount;
                    maxLoanAmount = customData.maxLoanAmount;
                    internalService = ' [' + customData.serviceType + ']';

                    if (parseFloat(minLoanAmount) > 0 && parseFloat(chkTotalLoanAmount) < parseFloat(minLoanAmount)
                        || (parseFloat(maxLoanAmount) > 0 && parseFloat(chkTotalLoanAmount) > parseFloat(maxLoanAmount))
                    ) {
                        let warningMsg = "* The Loan Amount Range Set In This Program's Guidelines Is Between " +
                            convertInputToAbsoluteValueWithDollar(minLoanAmount) +
                            " and " +
                            convertInputToAbsoluteValueWithDollar(maxLoanAmount) +
                            ". The Current Loan Amount is " +
                            convertInputToAbsoluteValueWithDollar(chkTotalLoanAmount);

                        pushOrAppend(loanAmountGuidelines, warningMsg, internalService);
                        j++;
                    }
                });
                msg += combinedGuideLines(loanAmountGuidelines);
                /*End OF Internal Loan CustomGuidelines */
            }
        }

        if (interestRate > 0) {
            if ((parseFloat(minRate) > 0
                    && parseFloat(interestRate) < parseFloat(minRate))
                || (parseFloat(maxRate) > 0 && parseFloat(interestRate) > parseFloat(maxRate))
            ) {
                if (j > 0) {
                    msg += ' <br> ';
                }
                msg += "* The Interest Rate Range Set In This Program's Guidelines is between " + minRate + ' and ' + maxRate + '. The Current Interest Rate is ' + interestRate + '.';
                j++;
            }

            if (userGroup === 'Employee') {
                /* Internal Loan CustomGuidelines */
                let loanAmountGuidelines = [];
                $.each(LMRInternalLoanGuidelinesData, function (key, customData) {

                    minRate = customData.minRate;
                    maxRate = customData.maxRate;
                    internalService = ' [' + customData.serviceType + ']';

                    if ((parseFloat(minRate) > 0 && parseFloat(interestRate) < parseFloat(minRate))
                        || (parseFloat(maxRate) > 0 && parseFloat(interestRate) > parseFloat(maxRate))) {

                        let warningMsg = "* The Interest Rate Range Set In This Program's Guidelines is between " + minRate + ' and ' + maxRate + '. The Current Interest Rate is ' + interestRate;
                        pushOrAppend(loanAmountGuidelines, warningMsg, internalService);
                        j++;
                    }
                });
                msg += combinedGuideLines(loanAmountGuidelines);
                /*End OF Internal Loan CustomGuidelines */
            }

        }

        if (parseFloat(currentOriginationPointsRate) > 0) {
            if ((parseFloat(LGMinOrgPoints) > 0 && parseFloat(currentOriginationPointsRate) < parseFloat(LGMinOrgPoints))
                || (parseFloat(LGMaxOrgPoints) > 0 && parseFloat(currentOriginationPointsRate) > parseFloat(LGMaxOrgPoints))) {
                if (j > 0) {
                    msg += ' <br> ';
                }
                msg += "* The Origination Points Range Set In This Program's Guidelines is between " + LGMinOrgPoints + " and " + LGMaxOrgPoints + ". The Current Origination Points is " + currentOriginationPointsRate + ".";
                j++;
            }

            if (userGroup === 'Employee') {
                /* Internal Loan CustomGuidelines */
                let loanAmountGuidelines = [];
                $.each(LMRInternalLoanGuidelinesData, function (org, customData) {

                    LGMinOrgPoints = customData.minPoints;
                    LGMaxOrgPoints = customData.maxPoints;
                    internalService = ' [' + customData.serviceType + ']';

                    if ((parseFloat(LGMinOrgPoints) > 0 && parseFloat(currentOriginationPointsRate) < parseFloat(LGMinOrgPoints))
                        || (parseFloat(LGMaxOrgPoints) > 0 && parseFloat(currentOriginationPointsRate) > parseFloat(LGMaxOrgPoints))) {

                        let warningMsg = "* The Origination Points Range Set In This Program's Guidelines is between " + LGMinOrgPoints + " and " + LGMaxOrgPoints + ". The Current Origination Points is " + currentOriginationPointsRate;
                        pushOrAppend(loanAmountGuidelines, warningMsg, internalService);
                        j++;
                    }
                });
                msg += combinedGuideLines(loanAmountGuidelines);
                /*End OF Internal Loan CustomGuidelines */
            }
        }

        if (currentLoanToCost > 0) {
            if (parseFloat(LGMaxLTC) > 0 && parseFloat(currentLoanToCost) > parseFloat(LGMaxLTC) && parseFloat(currentLoanToCost) != parseFloat(LGMaxLTC)) {
                if (j > 0) {
                    msg += ' <br> ';
                }
                msg += "* The Maximum Loan-to-Cost (LTC) Set In This Program's Guidelines Is " + LGMaxLTC + "% . The Current Loan-to-Cost(LTC) Is " + currentLoanToCost + "%.";
                j++;
            }
            if (userGroup === 'Employee') {
                /* Internal Loan CustomGuidelines */
                let loanAmountGuidelines = [];
                $.each(LMRInternalLoanGuidelinesData, function (ltc, customData) {

                    LGMaxLTC = customData.totalLTC;
                    internalService = ' [' + customData.serviceType + ']';

                    if (parseFloat(LGMaxLTC) > 0
                        && parseFloat(currentLoanToCost) > parseFloat(LGMaxLTC)
                        && parseFloat(currentLoanToCost) != parseFloat(LGMaxLTC)) {
                        let warningMsg = "* The Maximum Loan-to-Cost (LTC) Set In This Program's Guidelines Is " + LGMaxLTC + "% . The Current Loan-to-Cost(LTC) Is " + currentLoanToCost;
                        pushOrAppend(loanAmountGuidelines, warningMsg, internalService);
                        j++;
                    }
                });
                msg += combinedGuideLines(loanAmountGuidelines);
                /*End OF Internal Loan CustomGuidelines */
            }

        }

        if (chkDownPaymentPerc > 0) {
            if (parseFloat(LGDownPaymentPerc) > 0 && parseFloat(chkDownPaymentPerc) < parseFloat(LGDownPaymentPerc)) {
                if (j > 0) {
                    msg += ' <br> ';
                }
                msg += "* The Minimum Down Payment Set In This Program's Guidelines is " + LGDownPaymentPerc + ". The Current Down Payment Is " + chkDownPaymentPerc + ".";
                j++;
            }

            if (userGroup === 'Employee') {
                /* Internal Loan CustomGuidelines */
                let loanAmountGuidelines = [];

                $.each(LMRInternalLoanGuidelinesData, function (key, customData) {

                    LGDownPaymentPerc = customData.downPaymentPercentage;
                    internalService = ' [' + customData.serviceType + ']';

                    if (parseFloat(LGDownPaymentPerc) > 0 && parseFloat(chkDownPaymentPerc) < parseFloat(LGDownPaymentPerc)) {

                        let warningMsg = "* The Minimum Down Payment Set In This Program's Guidelines is " + LGDownPaymentPerc + ". The Current Down Payment Is " + chkDownPaymentPerc;
                        pushOrAppend(loanAmountGuidelines, warningMsg, internalService);

                        j++;
                    }
                });
                msg += combinedGuideLines(loanAmountGuidelines);
                /*End OF Internal Loan CustomGuidelines */
            }

        }

        if (loanPurpose === 'Purchase' || loanPurpose === 'New Construction - Existing Land' || loanPurpose === 'Commercial Purchase') {
            if (parseFloat(maxLTV) > 0 && parseFloat(chkAcquisitionLTV) > parseFloat(maxLTV) && parseFloat(chkAcquisitionLTV) != parseFloat(maxLTV)) {
                if (j > 0) {
                    msg += ' <br> ';
                }
                msg += "* The Maximum LTV Set In This Program's Guidelines Is " + maxLTV + ". The Current LTV Is " + chkAcquisitionLTV + ".";
                j++;
            }

            if (parseFloat(maxARV) > 0 && parseFloat(ARV) > parseFloat(maxARV) && parseFloat(ARV) != parseFloat(maxARV)) {
                if (j > 0) {
                    msg += ' <br> ';
                }
                msg += " * The Maximum ARV Set In This Program's Guidelines Is " + maxARV + ". The Current ARV is " + ARV + ".";
                j++;
            }

            if (userGroup === 'Employee') {
                let loanAmountGuidelines = [];

                /* Internal Loan CustomGuidelines */
                $.each(LMRInternalLoanGuidelinesData, function (ltv, customData) {

                    maxLTV = customData.maxLTV;
                    maxARV = customData.maxLTVAfterRehab;
                    internalService = ' [' + customData.serviceType + ']';

                    if (parseFloat(maxLTV) > 0 && parseFloat(chkAcquisitionLTV) > parseFloat(maxLTV) && parseFloat(chkAcquisitionLTV) != parseFloat(maxLTV)) {

                        let warningMsg = "* The Maximum LTV Set In This Program's Guidelines Is " + maxLTV + ". The Current LTV Is " + chkAcquisitionLTV;
                        pushOrAppend(loanAmountGuidelines, warningMsg, internalService);
                        j++;
                    }
                    if (parseFloat(maxARV) > 0 && parseFloat(ARV) > parseFloat(maxARV) && parseFloat(ARV) != parseFloat(maxARV)) {
                        let warningMsg = "* The Maximum ARV Set In This Program's Guidelines Is " + maxARV + ". The Current ARV is " + ARV;
                        pushOrAppend(loanAmountGuidelines, warningMsg, internalService);
                        j++;
                    }
                });
                msg += combinedGuideLines(loanAmountGuidelines);
                /*End OF Internal Loan CustomGuidelines */
            }
        }

    }

    if (parseFloat(currentFixflipProp) > 0) {
        //|| (parseFloat(LGMaxFixFlop) > 0 && parseFloat(currentFixflipProp) > parseFloat(LGMaxFixFlop))
        if ((parseFloat(LGMinFixFlop) > 0 && parseFloat(currentFixflipProp) < parseFloat(LGMinFixFlop))) {
            if (j > 0) {
                msg += ' <br> ';
            }
            msg += "* The Minimum Fix and Flip Experience Set In This Program's Guidelines Is A Minimum Of " + LGMinFixFlop + " Properties. The Current Fix And Flip Construction Experience Is " + currentFixflipProp + ".";
            j++;
        }
        if (userGroup === 'Employee') {
            /* Internal Loan CustomGuidelines */
            let loanAmountGuidelines = [];
            $.each(LMRInternalLoanGuidelinesData, function (key, customData) {

                LGMinFixFlop = customData.minPropertyForFixFlop;
                LGMaxFixFlop = customData.maxPropertyForFixFlop;
                internalService = ' [' + customData.serviceType + ']';
//|| (parseFloat(LGMaxFixFlop) > 0 && parseFloat(currentFixflipProp) > parseFloat(LGMaxFixFlop))
                if ((parseFloat(LGMinFixFlop) > 0 && parseFloat(currentFixflipProp) < parseFloat(LGMinFixFlop))) {

                    let warningMsg = "* The Minimum Fix and Flip Experience Set In This Program's Guidelines Is A Minimum Of " + LGMinFixFlop + " Properties.  The Current Fix And Flip Construction Experience Is " + currentFixflipProp;
                    pushOrAppend(loanAmountGuidelines, warningMsg, internalService);

                    j++;
                }
            });
            msg += combinedGuideLines(loanAmountGuidelines);
            /*End OF Internal Loan CustomGuidelines */
        }

    }

    if (parseFloat(chkGround) > 0) {
        //|| (parseFloat(LGMaxGround) > 0 && parseFloat(chkGround) > parseFloat(LGMaxGround))
        if ((parseFloat(LGMinGround) > 0 && parseFloat(chkGround) < parseFloat(LGMinGround))) {
            if (j > 0) {
                msg += ' <br> ';
            }
            msg += "* The Minimum Ground Up Construction Experience Set In This Program's Guidelines Is " + LGMinGround + " Properties. The Current Ground Up Construction Experience Is " + chkGround + " Properties.";
            j++;
        }

        if (userGroup === 'Employee') {
            /* Internal Loan CustomGuidelines */
            let loanAmountGuidelines = [];
            $.each(LMRInternalLoanGuidelinesData, function (key, customData) {

                LGMinGround = customData.minPropertyForGrndConst;
                LGMaxGround = customData.maxPropertyForGrndConst;
                internalService = ' [' + customData.serviceType + ']';

                //|| (parseFloat(LGMaxGround) > 0 && parseFloat(chkGround) > parseFloat(LGMaxGround))
                if ((parseFloat(LGMinGround) > 0 && parseFloat(chkGround) < parseFloat(LGMinGround))) {

                    let warningMsg = "* The Minimum Ground Up Construction Experience Set In This Program's Guidelines Is " + LGMinGround + " Properties. The Current Ground Up Construction Experience Is " + chkGround;
                    pushOrAppend(loanAmountGuidelines, warningMsg, internalService);
                    j++;
                }
            });
            msg += combinedGuideLines(loanAmountGuidelines);
            /*End OF Internal Loan CustomGuidelines */
        }

    }

    if (parseFloat(currentMidFico) > 0) {
        if ((parseFloat(LGMinMidfico) > 0 && parseFloat(currentMidFico) < parseFloat(LGMinMidfico))) {
            if (j > 0) {
                msg += ' <br> ';
            }
            msg += "* The Minimum Mid FICO Score Set In This Program's Guidelines is " + LGMinMidfico + ". The Current Mid FICO score is " + currentMidFico + ".";
            j++;
        }

        if (userGroup === 'Employee') {
            /* Internal Loan CustomGuidelines */
            let loanAmountGuidelines = [];
            $.each(LMRInternalLoanGuidelinesData, function (key, customData) {

                LGMinMidfico = customData.minMidFico;
                LGMaxMidfico = customData.maxMidFico;
                internalService = ' [' + customData.serviceType + ']';

                if ((parseFloat(LGMinMidfico) > 0 && parseFloat(currentMidFico) < parseFloat(LGMinMidfico))) {

                    let warningMsg = "* The Minimum Mid FICO Score Set In This Program's Guidelines is " + LGMinMidfico + ". The Current Mid FICO score is " + currentMidFico;
                    pushOrAppend(loanAmountGuidelines, warningMsg, internalService);

                    j++;
                }
            });
            msg += combinedGuideLines(loanAmountGuidelines);
            /*End OF Internal Loan CustomGuidelines */
        }
    }

    let isDebtServiceRatioHidden = $('.debtServiceRatio_disp').is(":hidden");
    if ((parseFloat(currentDSCR) < parseFloat(minDSCR)) && !isDebtServiceRatioHidden) {
        if (j > 0) {
            msg += ' <br> ';
        }
        msg += "* The Minimum DSCR Set In This Program's Guidelines is " + minDSCR + ". The Current DSCR is " + currentDSCR + ".";
        j++;
    }
    if (userGroup === 'Employee' && !isDebtServiceRatioHidden) {
        /* Internal Loan CustomGuidelines */
        let loanAmountGuidelines = [];
        $.each(LMRInternalLoanGuidelinesData, function (key, customData) {

            minDSCR = customData.minDSCR;
            internalService = ' [' + customData.serviceType + ']';

            if ((parseFloat(currentDSCR) < parseFloat(minDSCR))) {

                let warningMsg = "* The Minimum DSCR Set In This Program's Guidelines is " + minDSCR + ". The Current DSCR is " + currentDSCR;
                pushOrAppend(loanAmountGuidelines, warningMsg, internalService);

                j++;
            }
        });
        msg += combinedGuideLines(loanAmountGuidelines);
        /*End OF Internal Loan CustomGuidelines */
    }

    let _filePropertyState = $('#filePropertyState');
    if (LGElgibleState !== '' && _filePropertyState.length > 0) {

        let eligibleStates = LGElgibleState.split(',');
        let propertyStateVal = _filePropertyState.val();
        let propertyStateText = $("#filePropertyStateFull").val();

        if (!eligibleStates.includes(propertyStateVal) && propertyStateVal !== '') {
            msg += '<br>';
            msg += "* " + propertyStateText + " Is An Ineligible State, ";
            j++;
        }
    }


    if ($('#minTimeInBusiness').length) {
        let minTimeVal = $('#minTimeVal').val();
        let minTimeValText = $('#minTimeValText').val();
        let minTimeInBusiness = $('#minTimeInBusiness:enabled').val();
        if (parseInt(minTimeInBusiness) < parseInt(minTimeVal) && minTimeVal !== '' && minTimeInBusiness !== '') {
            msg += '<br>';
            msg += "* The minimum Time in Business is " + minTimeValText + " or greater\n";
            j++;
        }
    }
    if ($('#personalBankruptcy').length) {
        let MinSeasoningPersonalBankruptcyVal = $('#MinSeasoningPersonalBankruptcyVal').val();
        let MinSeasoningPersonalBankruptcyValText = $('#MinSeasoningPersonalBankruptcyValText').val();
        let personalBankruptcy = $('#personalBankruptcy:enabled').val();
        if (parseInt(personalBankruptcy) < parseInt(MinSeasoningPersonalBankruptcyVal) && personalBankruptcy !== '' && MinSeasoningPersonalBankruptcyVal !== '') {
            msg += '<br>';
            msg += "* The minimum seasoning for Personal Bankruptcy is " + MinSeasoningPersonalBankruptcyValText + " or greater\n";
            j++;
        }
    }

    if ($('#businessBankruptcy').length) {
        let MinSeasoningBusinessBankruptcyVal = $('#MinSeasoningBusinessBankruptcyVal').val();
        let MinSeasoningBusinessBankruptcyValText = $('#MinSeasoningBusinessBankruptcyValText').val();
        let businessBankruptcy = $('#businessBankruptcy:enabled').val();
        if (parseInt(businessBankruptcy) < parseInt(MinSeasoningBusinessBankruptcyVal) && businessBankruptcy !== '' && MinSeasoningBusinessBankruptcyVal !== '') {
            msg += '<br>';
            msg += "* The minimum seasoning for Business Bankruptcy is " + MinSeasoningBusinessBankruptcyValText + " or greater\n";
            j++;
        }
    }
    if ($('#statusForeclosure').length) {
        let MinSeasoningForeclosureVal = $('#MinSeasoningForeclosureVal').val();
        let MinSeasoningForeclosureValText = $('#MinSeasoningForeclosureValText').val();
        let statusForeclosure = $('#statusForeclosure:enabled').val();
        if (parseInt(statusForeclosure) < parseInt(MinSeasoningForeclosureVal) && statusForeclosure !== '' && MinSeasoningForeclosureVal !== '') {
            msg += '<br>';
            msg += "* The minimum seasoning for Foreclosure is " + MinSeasoningForeclosureValText + " or greater\n";
            j++;
        }
    }


    if (j > 0) {
        _divGuidelinesErrorMsg.html(
            "<div class='form-group'>" +
            "<div class='alert alert-custom alert-default p-5 bg-danger-o-30'>" +
            "<div class=\"alert-text \">" +
            "<div class='h4 text-center mb-4'>Guideline Warnings</div>" +
            "<div class='text-danger h5' style='line-height: 1.8;'>" + msg + "</div></div>" +
            "</div> " +
            "</div>");
        _divGuidelinesErrorMsg.show();
        if (scrollingEnabled === 'yes') {
            $('html,body').animate({
                scrollTop: _divGuidelinesErrorMsg.offset().top - 45
            }, 'slow');
        }
        return true;
    } else {
        _divGuidelinesErrorMsg.html('');
        _divGuidelinesErrorMsg.hide();
        $("#allowFormSubmit").val(1);
        return true;
    }
    console.log({
        func: 'validateMinMaxLoanGuidelines:end',
    });
}

function getValueOrDefault(selector, defaultValue = 0) {
    let value = $(selector + ':enabled').val();
    return (value === '' || value === undefined || value === null || isNaN(value)) ? defaultValue : parseFloat(value);
}

function showGuidelinesDialogBox(msg) {
    console.log({
        func: 'showGuidelinesDialogBox',
    });
    $("#warning").dialog({
        title: '',
        autoOpen: true,
        closeOnEscape: false,
        draggable: false,
        width: 550,
        minHeight: 180,
        modal: true,
        buttons: {
            'Ignore & Save': function () {
                $("#isSave").val(1);
                $("#allowFormSubmit").val(1);
                document.getElementById("loanModForm").submit();
                $('#loanModForm input[type="submit"]').attr('disabled', 'disabled');
                $(this).dialog("close");
            },
            'Ignore/Save & Next': function () {
                $("#isSave").val(0);
                $("#allowFormSubmit").val(1);
                var goToTabLI = $('#goToTabLI').val();
                if (goToTabLI == '') {
                } else {
                    $('#goToTab').val(goToTabLI);
                }                                   /* Go to Tab from Loan Info */
                document.getElementById("loanModForm").submit();
                $('#loanModForm input[type="submit"]').attr('disabled', 'disabled');
                $(this).dialog("close");
            },
            'Close / Edit': function () {
                $(this).dialog("close");
                $('#loanModForm input[type="submit"]').removeAttr('disabled');
            }
        },
        resizable: false
    });
    $('#warning').html('<div class="pad5 talign-center">' + msg + '</div>');
    //$('.ui-dialog').css({top:'100px', position:'fixed'});
}

function showAndHideLandFields(val) {
    console.log({
        func: 'showAndHideLandFields',
    });
    var loanPurpose = $('#typeOfHMLOLoanRequesting').val();
    if (val == 'CONS') {
        $('.landValueCls').css("display", "table-cell");
        $('.propertyNeedRehabinitialTddisp').css("display", "table-cell");
        $('.doesPropertyNeedRehabDispDiv').show();
        $("[name=propertyNeedRehab]").val(["Yes"]);
    } else {
        $('.landValueCls').css("display", "none");
        $('.propertyNeedRehabinitialTddisp').css("display", "none");
        $('.doesPropertyNeedRehabDispDiv').hide();
        $("[name=propertyNeedRehab]").val([""]);
    }

    if (loanPurpose == 'New Construction - Existing Land') $('.landValueCls').css("display", "table-cell");

    if (val == 'CONS' && (loanPurpose == 'Cash-Out / Refinance' || loanPurpose == 'Commercial Cash Out Refinance' || loanPurpose == 'Refinance' || loanPurpose == 'Delayed Purchase')) {
        $('.landValueExtraCls').css("display", "table-cell");
    } else {
        $('.landValueExtraCls').css("display", "none");
    }

}

function showAndHideLandFieldsNew(val) {
    console.log({
        func: 'showAndHideLandFieldsNew',
        val: val,
    });
    let ft = $('#ft').val();
    $('.HMLOLoanInfoSections').show();

    if (ft === 'loc') {
        $('.hideLOC').hide();
    }
    let wfOpt = $('#wfOpt').val();

    let _propDetailsProcess = $('#propDetailsProcess');

    if (_propDetailsProcess.prop('disabled')) {
        $('.borrowerActiveSection').show();
    } else {
        let pDPro = _propDetailsProcess.val();
        if ((pDPro === 'Looking for General Info' || pDPro === 'Actively Looking For Property')) {
            $('.borrowerActiveSection').hide();
        } else {
            $('.borrowerActiveSection').show();
        }
    }

    if (val === 'CONS') {
        $('.landValueCls').css("display", "block");
        $('.propertyNeedRehabinitialTddisp').css("display", "block");
        $('.doesPropertyNeedRehabDispDiv').show();
        $("[name=propertyNeedRehab]").val(["Yes"]);
    } else {
        $('.landValueCls').css("display", "none");
        $('.propertyNeedRehabinitialTddisp').css("display", "none");
        $('.doesPropertyNeedRehabDispDiv').hide();
        $("[name=propertyNeedRehab]").val([""]);
    }

    let hideBorrowerInfo = parseInt($('#hideBorrowerInfo').val());
    let REBroker = $('#REBroker:checked').val();

    if (REBroker === 'Yes' && hideBorrowerInfo === 1 && wfOpt === 'aa4465703ef4b17e') {
        $('.borrowerInfoSection').hide();
    } else {
        $('.borrowerInfoSection').show();
    }

    let loanPurpose = $('#loanModForm #typeOfHMLOLoanRequesting').val();
    console.log({
        loanPurpose: loanPurpose,
    });

    if (val === 'CONS' && (loanPurpose === 'Cash-Out / Refinance' || loanPurpose === 'Commercial Cash Out Refinance' || loanPurpose === 'Refinance' || loanPurpose == 'Delayed Purchase')) {
        $('.landValueExtraCls').css("display", "block");
    } else {
        $('.landValueExtraCls').css("display", "none");
    }

}

function hideAndShowAcceptPurchaseAgreement(fldValue, className) {
    console.log({
        func: 'hideAndShowAcceptPurchaseAgreement',
    });
    if (fldValue == 'Yes') {
        $('.' + className).css("display", "block");
    } else {
        $('.' + className).css("display", "none");
    }
}

function showGuidelinesDialogBoxWF(msg) {
    console.log({
        func: 'showGuidelinesDialogBoxWF',
    });
    $("#warningLG").dialog({
        title: '',
        autoOpen: true,
        closeOnEscape: false,
        draggable: false,
        width: 550,
        minHeight: 180,
        modal: true,
        buttons: {
            'Ignore/Save & Next': function () {
                $("#allowFormSubmit").val(1);
                //$('#loanModForm').trigger('submit');
                document.getElementById("loanModForm").submit();
                //$("#loanModForm").submit();
                $(this).dialog("close");
                //return true;
            },
            'Close / Edit': function () {
                $(this).dialog("close");
            }
        },
        resizable: false
    });
    $('#warningLG').html('<div class="pad5 talign-center">' + msg + '</div>');
    $('.ui-dialog').css({top: '100px', position: 'fixed'});
}

function populatePropertyDetails(val) {
    console.log({
        func: 'populatePropertyDetails',
    });

    if (val == 'Looking for General Info' || val == 'Actively Looking For Property' || val == 'Need a General Pre-Approval Letter') {
        //$('.propAddress').hide();
        hideBorrowerInfo = $('#hideBorrowerInfo').val();
        REBroker = $('#REBroker:checked').val();
        if (REBroker == 'Yes' && hideBorrowerInfo == 1) $('.borrowerHideSection').hide();
        $('.borrowerActiveSection').hide();
    } else {
        hideBorrowerInfo = $('#hideBorrowerInfo').val();
        REBroker = $('#REBroker:checked').val();
        if (REBroker == 'Yes' && hideBorrowerInfo == 1) $('.borrowerHideSection').show();
        $('.borrowerActiveSection').show();
    }
}


/*
* Description   : Ins Impounds Calculation
* Function      : calculateInsImpoundsFee(formName, targetName)
* Formula       : Ins impounds Months * Ins Impounds Months Amt
* Developer     : Valarmathi
*/
function calculateInsImpoundsFee(formName, targetName) {
    console.log({
        func: 'calculateInsImpoundsFee',
    });
    var insImpoundsFee = 0;
    var insImpoundsMonth = 0;
    var insImpoundsMonthAmt = 0;

    eval("insImpoundsMonth      = document." + formName + ".insImpoundsMonth.value");
    eval("insImpoundsMonthAmt   = document." + formName + ".insImpoundsMonthAmt.value");

    try {
        insImpoundsMonth = replaceCommaValues(insImpoundsMonth);
        insImpoundsMonthAmt = replaceCommaValues(insImpoundsMonthAmt);
    } catch (e) {
    }

    if (insImpoundsMonth == "") insImpoundsMonth = 0;
    if (insImpoundsMonthAmt == "") insImpoundsMonthAmt = 0;

    insImpoundsFee = parseFloat(insImpoundsMonth) * parseFloat(insImpoundsMonthAmt);
    insImpoundsFee = autoNumericConverter(insImpoundsFee.toFixed(2));

    try {
        eval("document.getElementById('" + targetName + "').value = '" + insImpoundsFee + "'");
    } catch (e) {
    }

    if (formName != 'addBasicLoanTermForm') {
        calculateTotalFeesAndCost();
    }
}


function showAndHidePrePaymentPenalty(fldValue, idName) {
    console.log({
        func: 'showAndHidePrePaymentPenalty',
    });
    if (fldValue == 'Yes') {
        $('.' + idName).show();
        $('.' + idName).removeClass('d-none');
    }
    if (fldValue == 'No') {
        $('.' + idName).hide();
        if (idName === 'prePaymentPenaltyDisOpt') {
            $('#prePaymentPenaltyPercentage').val('');
            $('#prePaymentPenalty').val('');
            $('#prePaymentSelectVal').val('');
            $("#prePaymentSelectVal").trigger("chosen:updated");
        }
    }
}

/*
* Description   : Extension Option Calculation
* Function      : calculatePercentageExtensionOption(formName, targetName)
* Formula       : extensionOptionPercentage * totalLoanAmount / 100 * extensionOption
* Developer     : Berin
*/
function calculatePercentageExtensionOption(formName, _this) {
    console.log({
        func: 'calculatePercentageExtensionOption',
    });

    let _id = $(_this).attr('id');
    let targetNameArray = _id.split('_');
    let targetNameCnt = targetNameArray[1];
    var extensionOptionPercentage = 0;
    var totalLoanAmount = 0;
    var extensionOption = 0;
    var extensionOptionsAmt = 0;

    extensionOptionPercentage = getFieldsValue(_id);
    totalLoanAmount = getFieldsValue('totalLoanAmount1');
    if (extensionOptionPercentage > 0) {

        extensionOptionsAmt = ((parseFloat(extensionOptionPercentage) * parseFloat(totalLoanAmount)) / 100);
        extensionOptionsAmt = autoNumericConverter(extensionOptionsAmt.toFixed(2));
    }
    try {
        eval("document.getElementById('extensionOptionsAmt_' + targetNameCnt).innerHTML = '" + extensionOptionsAmt + "'");
    } catch (e) {
    }

}

function populateDualFieldForHMLONewLoan(val, formName, targetFld) {
    console.log({
        func: 'populateDualFieldForHMLONewLoan',
        formName: formName,
        targetFld: targetFld,
    });
    $('#' + targetFld).html(val);
}

/*
* Description   : Hide and show subsections
* Function      : hideAndShowSection(formName, targetName)
*/

function hideAndShowSection(inVal, acVal, trCls) {
    console.log({
        func: 'hideAndShowSection',
        inVal: inVal,
        acVal: acVal,
        trCls: trCls,

    });
    if (inVal == acVal) {
        $('.' + trCls).show();
        if (trCls == 'summonDateDiv') {
            $('.summonDate_disp').show();
            $('.summonDate_disp').removeClass('secHide');
            $('#summonDate').removeClass('secHide');
            $('#summonDate').removeAttr('disabled');
        }
        if (trCls == 'receiveOfferOnPropertyDiv') {
            $('.receiveOfferOnPropertyDiv').removeClass('secHide');
        }
        if (trCls == 'saleByOwnerDiv') {
            $('.saleByOwnerDiv').removeClass('secHide');
        }

    } else {
        $('.' + trCls).hide();
    }
    //only for PM Section
    /*if(trCls == 'pmSubjectMarketAreaExpl') {
        if(inVal == 'Yes') { // hide
            $('.pmRealEstatePropertyManagement_disp').hide();
        } else { // show
            $('.pmRealEstatePropertyManagement_disp').show();
        }
    }*/
}

function monthlyInterestRate(nper, pmt, pv, fv, type, guess) {  //alert(nper+'==='+pmt+'==='+pv);
    console.log({
        func: 'monthlyInterestRate',
    });
    // Sets default values for missing parameters
    fv = typeof fv !== 'undefined' ? fv : 0;
    type = typeof type !== 'undefined' ? type : 0;
    guess = typeof guess !== 'undefined' ? guess : 0.1;
    var finalres = 0;
    if (nper == 0) {
        var res = ($('#loanTerm').val()).split(" ");
        nper = res[0];
    } else {
        var res = (nper).split(" ");
        nper = res[0];
    }
    if (pmt == 0) {
        try {
            pmt = -($('#lien1Payment').val()).replace(',', '');
        } catch (e) {
        }
    }
    if (pv == 0) pv = ($('#acquisitionPriceFinanced').html()).replace(',', ''); // alert(nper+'==='+pmt+'==='+pv);
    // Sets the limits for possible guesses to any
    // number between 0% and 100%
    var lowLimit = 0;
    var highLimit = 1;

    // Defines a tolerance of up to +/- 0.00005% of pmt, to accept
    // the solution as valid.
    var tolerance = Math.abs(0.00000005 * pmt);

    // Tries at most 40 times to find a solution within the tolerance.
    for (var i = 0; i < 40; i++) {
        // Resets the balance to the original pv.
        var balance = pv;

        // Calculates the balance at the end of the loan, based
        // on loan conditions.
        for (var j = 0; j < nper; j++) {
            if (type == 0) {
                // Interests applied before payment
                balance = balance * (1 + guess) + pmt;
            } else {
                // Payments applied before insterests
                balance = (balance + pmt) * (1 + guess);
            }
        }

        // Returns the guess if balance is within tolerance.  If not, adjusts
        // the limits and starts with a new guess.
        if (Math.abs(balance + fv) < tolerance) {  //var finalres = guess*12*100 ; alert(finalres);  alert(finalres.toFixed(2));
            finalres = ((guess * ($('#paymentFrequency').val()) * 100).toFixed(2));
            $('#InrBasedOnMonthlyPayment').val(finalres);
        } else if (balance + fv > 0) {
            // Sets a new highLimit knowing that
            // the current guess was too big.
            highLimit = guess;
        } else {
            // Sets a new lowLimit knowing that
            // the current guess was too small.
            lowLimit = guess;
        }

        // Calculates the new guess.
        guess = (highLimit + lowLimit) / 2;
    }

    // Returns null if no acceptable result was found after 40 tries.
    return 0;
};




