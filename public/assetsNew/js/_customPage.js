function BlockDiv(blockID) {
    KTApp.block('#' + blockID, {
        overlayColor: '#000000',
        state: 'danger',
        message: 'Please wait...'
    });
}

function UnBlockDiv(blockID) {
    KTApp.unblock('#' + blockID);
}

let LWControls = function () {
    let _maxLength = function () {
        console.log({
            func: 'Max Length'
        });
        $('.validateMaxLength[maxlength]').maxlength({
            alwaysShow: true,
            warningClass: "label label-success label-inline",
            limitReachedClass: "label label-danger label-inline",
            twoCharLinebreak: false,
        });
    }
    return {
        // public functions
        init: function () {
            _maxLength();
        }
    };
}();


$(document).ready(function () {
    let _body = $("body");
    let _exampleModal1 = $('#exampleModal1');


    $('.status-infos span:has(.result-block)').each(function () {
        let _slideOpen = $(this).find('.result-block').data('slide-to');
        if (!_slideOpen) {
            let leftPosition = parseInt($(this).position().left);
            let rightPosition = parseInt($(document).width()) - parseInt($(this).position().left);
            if (leftPosition > rightPosition) {
                _slideOpen = 'left';
            } else {
                _slideOpen = 'right';
            }
            $(this).find('.result-block').data('slide-to', _slideOpen);
        }
    });


    _body.on("mouseenter", ".status-infos span:has(.result-block)", function () {

        let _alignPosition;
        let _slideOpen;
        _alignPosition = $(this).find('.result-block').data('slide-to');

        if (typeof _alignPosition === "undefined") {
            let leftPosition = parseInt($(this).position().left)
            let rightPosition = parseInt($(document).width()) - parseInt($(this).position().left);
            if (leftPosition > rightPosition) {
                _slideOpen = 'right';
            } else {
                _slideOpen = 'left';
            }
        } else {
            (_alignPosition !== '') ? ((_alignPosition === 'left') ? _slideOpen = 'right' : _slideOpen = 'left') : _slideOpen = '';
        }
        $(this).find('.result-block').css(_slideOpen, '0px');
        $(this).find('.result-block').children('span.arrow').css(_slideOpen, '0px');

        $(this).find('.result-block').stop(true).css('display', 'none').fadeIn('normal', function () {
            $(this).css('opacity', '');
        });

    }).on("mouseleave", ".status-infos span:has(.result-block)", function () {
        $(this).find('.result-block').stop(true).css('display', 'block').fadeOut('normal', function () {
            $(this).css('opacity', '');
        });
    });

    $(".manualPopover").popover({
        trigger: "manual", html: true, sanitize: false, animation: true,
        boundary: 'window',
        template: '\
            <div class="popover popOverManual p-0" role="tooltip">\
                <div class="arrow"></div>\
                <h3 class="popover-header"></h3>\
                <div class="popover-body p-2"></div>\
            </div>'
    }).on("mouseenter", function () {
        let _this = this;
        $(this).popover("show");

        $(".popover").on("mouseleave", function () {
            $(_this).popover('hide');
        });
    }).on("mouseleave", function () {
        let _this = this;
        setTimeout(function () {
            if (!$(".popover:hover").length) {
                $(_this).popover("hide");
            }
        }, 300);
    });


    $(".popoverClass").popover({
        trigger: "manual", html: true, sanitize: false, animation: true,
        boundary: 'window',
        template: '<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header text-center "></h3><div class="popover-body text-center"></div></div>'
    }).on("mouseenter", function () {
        let _this = this;
        $(this).popover("show");
        $(".popover").on("mouseleave", function () {
            $(_this).popover('hide');
        });
    }).on("mouseleave", function () {
        let _this = this;
        setTimeout(function () {
            if (!$(".popover:hover").length) {
                $(_this).popover("hide");
            }
        }, 300);
    });

    $('.chzn-select-bootstrap').select2({
        theme: "bootstrap",
    });

    // $(".chzn-select").chosen({
    //     allow_single_deselect: true,
    //     search_contains: true
    // });  // With Red Close --Add Option Available

    // $(".chzn-select1").chosen({
    //     allow_single_deselect: true,
    //     search_contains: true
    // });  // With Red Close --Add Option Available

    $('.chzn-selectShowSelectAll').selectpicker({
        width: '100%',
        virtualScroll: false,
        sanitize: true,
    });


    /* Popup modal Start */
    _body.on("click", "[data-toggle='modal']", function () {
        console.log({
            func: '_body.on',
        })
        _exampleModal1 = $(this).data('target') === '#exampleModal1' ? $('#exampleModal1') : $($(this).data('target'));
        let callback = $(this).data('callback');
        let query_string = $(this).data('query_string');
        let elem = this;

        if (typeof showModalSaveButton === "function") {
            showModalSaveButton();
        }
        $(".manualPopover").popover('hide');
        $(".popoverClass").popover('hide');
        $(".tooltipAjax").tooltip('hide');
        $('.result-block').hide();

        _exampleModal1.modal('show');

        let modalTitle = $(this).data("name");
        let modalSize = (typeof $(this).data('wsize') === 'undefined') ? 'modal-lg' : $(this).data('wsize');

        _exampleModal1.find('.modal-dialog').removeClass("modal-sm modal-lg modal-xl").addClass(modalSize);
        _exampleModal1.find('.modal-title').text(modalTitle)

        let footerhide = $(this).data('footerhide');
        if (typeof footerhide !== typeof undefined && footerhide !== false) {
            if (footerhide === 'hide') {
                _exampleModal1.find('.modal-footer').hide();
            }
        }
        let modalTitleIcon = $(this).data('icon-txt');
        if (typeof modalTitleIcon !== typeof undefined && modalTitleIcon !== false) {
            if (modalTitleIcon) {
                _exampleModal1.find('.modal-title-icon').attr('data-content', modalTitleIcon);
                _exampleModal1.find('.modal-title-icon ').css('display', 'block');
            }
        }

        let modalRemoteUrl;
        if ($(this).data('id') !== '' && typeof ($(this).data('id')) != 'undefined') {
            modalRemoteUrl = ($(this).data('href') !== '') ? $(this).data('href') + "?" + $(this).data('id') : '';
        } else {
            modalRemoteUrl = $(this).data('href');
        }
        if (!modalRemoteUrl) {
            return;
        }
        $.ajax({
            type: "POST",
            url: modalRemoteUrl,
            data: {serialized: query_string},
            dataType: 'html',
            success: function (res) {
                _exampleModal1.find('.modal-body').html(res);
               // attachCopyButtons(_exampleModal1[0]);
                if (callback) {
                    let fn = window[callback];
                    if (typeof fn === "function") {
                        fn(elem);
                    }
                }
                _exampleModal1.draggable({
                    handle: ".modal-header,.modal-footer",
                    cursor: "move",
                });
                $('#modal-content-id').resizable({});
                // UnBlockDiv('modal-content-id');
            },
            error: function () {
                toastrNotification("Error In Fetching.", "error");
            }
        });
    });
    // modal size  modal-sm, modal-lg,modal-xl
    _exampleModal1.on('show.bs.modal', function (event) {
        _exampleModal1.css({
            left: 0,
            right: 0,
            top: 0
        });
    });
    $(".popupSubmit").click(function () {
        let dataLocSelector = $('#dataLoc');
        if (dataLocSelector.length && dataLocSelector.val() === 'myPipeline') { // If location is myPipeline
            $(this).prop('disabled', true); // Disable the button / allow only single submit
        }
        $('.modalBody  input[type="submit"]').click();
    });
    _exampleModal1.on('shown.bs.modal', function (event) {

    });
    _exampleModal1.on('hidden.bs.modal', function () {
        let modal = $(this);
        modal.find('.modal-title').text('');
        modal.find('.modal-body').html('<div class="progress-bar rounded-right progress-bar-striped progress-bar-animated p-2 bg-gray-500" role="progressbar" style="width: 50%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>');
        _exampleModal1.find('.modal-footer').show();
        modal.find('.modal-content').css('width', '100%');
        //$('#EditNoteModal .modal-content').css('width')
    });
    _exampleModal1.on('hide.bs.modal', function () {
        tinymce.editors = [];
        //    $('#exampleModal1 .modal-footer').show();  //disable lets see if ok because i need this fix. If no problems we will remove later. nov11,2021-Dave
        _exampleModal1.find('.modal-title-icon ').css('display', 'none');
    });


    function closeModal() {
        try {
            _exampleModal1.modal('toggle');
        } catch (e) {
        }
    }

    /* Popup modal End */
    $("#kt_aside_toggle").click(function () {
        let kt_aside_toggleVal;
        if ($(this).hasClass("active")) {
            kt_aside_toggleVal = 'aside-minimize';
        } else {
            kt_aside_toggleVal = '';
        }
        HTTP.Post('/backoffice/api_v2/cookie', {
            cookieId: 'side_menu',
            cookieVal: kt_aside_toggleVal,
        });
    });

    function showAndHideNotifyUsers() {
        let notifyUsers = parseInt($("#notifyUsers").val());
        if (notifyUsers === 1) {
            $(".notifyUsers").removeClass('hide');
        } else {
            $(".notifyUsers").addClass('hide');
        }
    }

    $(".showDisabledNotification").on("mouseenter", function () {
        let _this = this;
        $(this).attr('title', 'This Field is Disabled in Form Field. Please clear data to remove From Form');
        $(this).tooltip("show");
        $(".popover").on("mouseleave", function () {
            $(_this).tooltip('hide');
        });
    }).on("mouseleave", function () {
        let _this = this;
        $(_this).tooltip("hide");
    });


    $('.showDisabledNotification').hover(function () {
        $(this).attr('title', 'This Field is Disabled in Form Field. Please clear data to remove From Form '); // Set the title attribute for the tooltip
    }, function () {
        $(this).removeAttr('title'); // Remove the title attribute when mouse leaves
    });

    $('.tooltipClass').tooltip({
        boundary: 'window',
    });
    $('.tooltipAjax').tooltip({
        boundary: 'window',
    });
    $('.tip-bottom').tooltip({
        boundary: 'window',
    });

    $(document).on('click', 'a.tooltipClass,span.tooltipClass', function () {
        $(this).tooltip('hide');
    });
    $(document).on('mouseenter', 'a.tooltipClass,[data-toggle="tooltip"],span.tooltipClass', function () {
        $(this).tooltip({
            boundary: 'window',
        }).tooltip('show');
    });


    $('[data-switch=true]').bootstrapSwitch();

    // tinymce.init({
    //     selector: '.tinyMceClass',
    //     forced_root_block: 'div',
    //     content_style: "body {font-size: 12pt;}",
    //     plugins: 'advlist autolink link image paste lists charmap print preview code table hr',
    //     menubar: false,
    //     toolbar: ['styleselect | fontselect | fontsizeselect | undo redo | cut copy paste | bold italic ForeColor BackColor | link image | alignleft aligncenter alignright alignjustify | bullist numlist | outdent indent | blockquote subscript superscript | advlist | autolink | lists charmap | print preview | code | table tabledelete | tableprops tablerowprops tablecellprops | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol | hr'],
    //     browser_spellcheck: true,
    //     contextmenu: false,
    //     //contextmenu false - this makes it so you can right click on misspelled and get correction suggestion, without this tinymce wants to make it a hyperlink
    //     image_title: true,
    //     file_picker_types: 'image',
    //     width: '100%',
    //     height: 200,
    //     autoresize_min_height: 200,
    //     autoresize_max_height: 400,
    //     relative_urls: false,
    //     remove_script_host: false,
    //     branding: false,
    //     block_unsupported_drop: true,
    //     images_file_types: 'jpeg,jpg,png,gif,JPEG,JPG,PNG,GIF',
    //     images_upload_url: '/backoffice/api_v2/tinymce_upload',
    //     paste_data_images: true,
    //     //LoanInfoTab
    //     //Additional Terms & Requirements for Loan Processing & Underwriting
    //     setup: function (editor) {
    //         editor.on('input', function () {
    //             let submit = $('input[type=submit]');
    //             if (submit.length > 0) {
    //                 submit.prop('disabled', false);
    //             }
    //         });
    //     }
    // });
    /*  enable the insert or edit link start*/
    let modal = $('.modal:visible');
    modal.one('hidden.bs.modal', function () {
        tinymce.remove('textarea.mce-small');
    });
    $(document).off('.tinymodal').on('focusin.tinymodal', function (e) {
        let dialog = $(e.target).closest(".tox-dialog");
        if (dialog.length && modal.find(dialog).length === 0) {
            let wrapper = $('.tox-tinymce-aux');
            modal.append(wrapper);
        }
    });
    /*  enable the insert or edit link end*/

    $(function () {
        // $('[data-toggle="table"]').DataTable();

        /*      $('.bootstrapDataTable').DataTable({
                  language: {search: "", searchPlaceholder: "Search"},
                  "sDom": 'lfr<"toolbar">tip',
                  "scrollX": true,
                  //scrollY: '50vh',
                  "scrollY": 600,
                  "paging": false,
                  "info": true,
                  "searching": true,
                  //retrieve: true,
                  //"select":true,
                  "order": [],
              });
  */
    });

    _body.on("click", ".divOpenClose", function () {
        if ($(this).children('i').hasClass('ki-arrow-up')) {
            $(this).children('i').removeClass('ki-arrow-up').addClass('ki-arrow-down');
        } else if ($(this).children('i').hasClass('ki-arrow-down')) {
            $(this).children('i').removeClass('ki-arrow-down').addClass('ki-arrow-up');
        }
        //ki ki-arrow-down
        $("#" + $(this).data('body-id')).toggle();
        //$().toggle();
    });


    _body.on("click", "[data-card-tool='toggle']", function () {
        if ($(this).children('i').hasClass('ki-arrow-up')) {
            $(this).children('i').removeClass('ki-arrow-up').addClass('ki-arrow-down');
        } else if ($(this).children('i').hasClass('ki-arrow-down')) {
            $(this).children('i').removeClass('ki-arrow-down').addClass('ki-arrow-up');
        }
        $(this).parent().parent().siblings(".card-body").toggle();
        /*    $('html, body').animate({
                scrollTop: $(this).parent().parent().parent().offset().top + ($(this).parent().parent().parent().height() / 2)
            }, 3000);*/
    });

    toastr.options = {
        "closeButton": true,
        "positionClass": "toast-center-center",
    };

    $(document).ready(function () {
        const forbiddenChars = /[\/\\:*?"<>|]/; // Forbidden characters pattern

        // For text inputs with the picsOfProp class
        $(document).on('mouseout', '.picsOfProp, .taskDocName', function () {
            var fileName = $(this).val();

            // Check for two consecutive periods (..)
            if (fileName.indexOf('..') !== -1) {
                toastr.error("Invalid file name: '..' is not allowed.", "Error", {
                    timeOut: 3000,
                    closeButton: true,
                    progressBar: true,
                });
                $(this).val(''); // Clear the field
                return;
            }

            // Check for forbidden characters
            if (forbiddenChars.test(fileName)) {
                toastr.error("Invalid file name: The file name cannot contain any of the following characters: / \\ : * ? \" < > |", "Error", {
                    timeOut: 3000,
                    closeButton: true,
                    progressBar: true,
                });
                $(this).val(''); // Clear the field
                return;
            }
        });

        // For file chooser inputs with multiple classes
        $(document).on('change', '.memberDocsChooser, .entityDocsChooser, .borrowerProfileDoc, .filelevelDocChooser, .pmRealEstatePropertyManagementDocumentationFileChooser, .agentLogo, .agentAvatar, .branchLogo, .branchAvatar, .employeeAvatar, .offerFile, .taskDocFile, .picsOfPropFileChooser, .rentRollFileChooser, .appraiserFileChooser, .appraiser1FileChooser, .AVM1UploadFileChooser, .AVM2UploadFileChooser, .AVM3UploadFileChooser, .BPO1UploadFileChooser, .BPO2UploadFileChooser, .BPO3UploadFileChooser, .titleReportFileChooser, .propertyInsuranceCoverageFileChooser , .fileUploadValidation', function () {
            // Check if a file is selected
            if (this.files && this.files[0]) {
                var fileName = this.files[0].name;

                // Check for two consecutive periods using regex
                if (/\.\./.test(fileName)) {
                    toastr.error("Error: The file name cannot contain consecutive periods. Please rename your file and try again.");
                    $(this).val(''); // Clear the input to force re-selection
                    return;
                }
            }
        });
    });


    $(document).on('click', '.copy-link', function (e) {
        e.preventDefault();

        // Prefer data-id; fall back to id (you already use data-id elsewhere)
        const copyText = $(this).data('id') || this.id;

        // Use the async clipboard API when in a secure context (https or localhost)
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(copyText)
                .then(() => toastrNotification('Link Copied!', 'success', 1500))
                .catch(() => legacyCopy(copyText));
        } else {
            legacyCopy(copyText);
        }

        function legacyCopy(text) {
            const ta = document.createElement('textarea');
            ta.value = text;
            ta.style.position = 'fixed';
            ta.style.opacity = '0';
            document.body.appendChild(ta);
            ta.focus();
            ta.select();
            try { document.execCommand('copy'); }
            finally { document.body.removeChild(ta); }
            toastrNotification('Link Copied!', 'success', 1500);
        }
    });

    _body.on("focus", ".dateNewClass", function () {

        if ($(this).data('datepicker')) { // If datepicker already created ignore
            return;
        }
        // $(this).removeClass('is-invalid');
        $(this).datepicker({
            orientation: 'bottom',
            autoclose: true,
            todayHighlight: true,
            forceParse: false,
            dateFormat: 'mm/dd/yy',
            keyboardNavigation: false,
            changeMonth: true,
            changeYear: true,
            maxViewMode: 3
        }).on('show', function (e) {
            if ($(this).datepicker('getStartDate') === -Infinity) {
                $(this).datepicker('setStartDate', '01/01/1900');
            }
            /*
                let _sDT = $(this).data('dateStartDate');
                if (_sDT !== undefined) {
                    if ($(this).datepicker('getStartDate') === '-Infinity') {
                        $(this).datepicker('setStartDate', _sDT);
                    }
                } else {
                    if ($(this).datepicker('getStartDate') === '-Infinity') {
                        $(this).datepicker('setStartDate', '01/01/1900');
                    }
                }

                let _eDT = $(this).data('dateEndDate');
                if (_sDT !== undefined) {
                    if ($(this).datepicker('setStartDate') === '-Infinity') {
                        $(this).datepicker('setEndDate', _eDT);
                    }
                }*/
        }).on('changeDate', function (e) {
            dateClass.validateDate(this);
        }).on('input', function () {
            dateClass.validateDate(this);
        });
        /*
            $(this).on('change', function () {
                dateClass.validateDate(this);
            });
        */

    });
    _body.on("focus", ".dateTimeClass", function () {

        if (!$(this).data('datetimepicker')) { // If datepicker already created ignore
            console.log('Creating Date Tim Picker');

            $(this).datetimepicker({
                autoclose: true,
                //calendarWeeks : true,
                clearBtn: true,
                showButtonPanel: true,
                minDate: moment('01/01/1900'),
                format: 'MM/DD/YYYY hh:mm A',
                todayHighlight: true,
                keepInvalid: true,
                focusOnShow: true,
                useStrict: true,
                update: false
            });
            $(this).on("change.datetimepicker", function (e) {
                //console.log('Changed');
                dateClass.validateDateTime(this);
            });
            $(this).on("hide.datetimepicker", function (e) {
                //console.log('Hide');
                dateClass.validateDateTime(this);
            });
            $(this).on("input", function (e) {
                //console.log('input');
                dateClass.validateDateTime(this);
            });
        }
        $(this).datetimepicker('show');
    });
    LWControls.init();
});


//alert(window.top.innerHeight);
//Automation Popup
function automationPopup(PCID) {
    //var text = 'You need to upgrade your plan for this feature. Please contact us to upgrade.';
    let text = 'This feature is not enabled on your current plan. To enable it, please ' +
        '<a href="/subscriptions/modifyPlan.php?pcId=' + PCID + '" target="_blank">upgrade your plan.</a>' +
        '<Br><Br>For more info on upgrading your plan <a href="https://help.lendingwise.com/knowledge/managing-plans-billing" target="_blank">Click here!</a>';
    $.confirm({
        closeIcon: true,
        columnClass: 'col-md-6',
        title: "<b>Automation</b>",
        content: text,
        type: 'blue',
        backgroundDismiss: true,
        draggable: false,
        smoothContent: true,
        html: true,
        buttons: {
            ok: {
                text: 'OK',
                btnClass: 'btn-primary',
                action: function () {
                }
            },
        }
    });
}

/*
$('.moneyFormat').mask('000,000,000,000,000.00', {
    reverse: true
});*/

/*    $('.dateFormat').mask('00/00/0000', {
        placeholder: "dd/mm/yyyy"
    });*/


class dateClass {

    static init(ele) {
        //ele.removeClass('is-invalid');
        ele.datepicker({
            autoclose: true,
            todayHighlight: true,
            forceParse: false,
            dateFormat: 'mm/dd/yy',
            keyboardNavigation: false,
            changeMonth: true,
            changeYear: true,
        }).on('show', function (e) {
            if ($(this).datepicker('getStartDate') === -Infinity) {
                $(this).datepicker('setStartDate', '01/01/1900');
            }
            /*
                let _sDT = $(this).data('dateStartDate');
                if (_sDT !== undefined) {
                    if ($(this).datepicker('getStartDate') === '-Infinity') {
                        $(this).datepicker('setStartDate', _sDT);
                    }
                } else {
                    if ($(this).datepicker('getStartDate') === '-Infinity') {
                        $(this).datepicker('setStartDate', '01/01/1900');
                    }
                }

                let _eDT = $(this).data('dateEndDate');
                if (_sDT !== undefined) {
                    if ($(this).datepicker('setStartDate') === '-Infinity') {
                        $(this).datepicker('setEndDate', _eDT);
                    }
                }*/
        })/*.on('changeDate', function (e) {
    validateDate(this)
}).on('hide', function (e) {
    validateDate(this)
})*/;
        ele.change(function () {
            dateClass.validateDate(this)
        });
    }

    static validateDate(element) {
        let _isValidDt = true;
        let _st = $(element).val();
        const regex = /^(0[1-9]|1[0-2])\/(0[1-9]|[12][0-9]|3[01])\/(\d{4}|\d{2})$/;
        let msg = '';

        if (_st) {
            if (regex.test(_st)) {
                const dataAttributes = $(element).data();
                let _sDT = dataAttributes.dateStartDate || dataAttributes.dateDobStartDate || dataAttributes.startDate || '01/01/1900';
                let _eDT = dataAttributes.dateEndDate || dataAttributes.dateDobEndDate || dataAttributes.endDate;

                if (dataAttributes.beforeCreationDate) {
                    msg = "Date entered cannot be before loan creation date.";
                }
                if (dataAttributes.futureDate) {
                    msg = $(element).attr('id') === 'desiredClosingDate' ? "Desired closing date cannot be older than today's date." : "Date cannot be today's date or older";
                }

                if (new Date(_sDT) > new Date(_st)) {
                    _isValidDt = false;
                    if (dataAttributes.dateDobStartDate !== undefined) {
                        if ($(element).attr('id') === 'borrowerDOB') {
                            msg = (msg ? '<br>' : '') + "Borrower's Age should be between 18 and 120 years old.<br> Borrowers under 18 years old are not eligible.";
                        } else if ($(element).attr('id') === 'coBorrowerDOB') {
                            msg = (msg ? '<br>' : '') + "Co-Borrower's Age should be between 18 and 120 years old.<br>Co-Borrowers under 18 years old are not eligible.";
                        } else {
                            msg = (msg ? '<br>' : '') + 'Date cannot exceed 120 years in the past (maximum age of 120)';
                        }
                    }
                }

                if (_eDT !== undefined && new Date(_eDT) < new Date(_st)) {
                    _isValidDt = false;
                    if (dataAttributes.dateDobEndDate !== undefined) {
                        if ($(element).attr('id') === 'borrowerDOB') {
                            msg = (msg ? '<br>' : '') + "Borrower's Age should be between 18 and 120 years old.<br>Borrowers under 18 years old are not eligible. ";
                        } else if ($(element).attr('id') === 'coBorrowerDOB') {
                            msg = (msg ? '<br>' : '') + "Co-Borrower's Age should be between 18 and 120 years old. <br>Co-Borrowers under 18 years old are not eligible. ";
                        } else {
                            msg = (msg ? '<br>' : '') + 'Date cannot exceed 120 years in the past (maximum age of 120)';
                        }
                    }
                }
            } else {
                _isValidDt = false;
            }

            if (_isValidDt) {
                $(element).removeClass('is-invalid').parent().find('em').remove();
            } else {
                const errorMessage = `<span class="error invalid-feedback">${msg || 'Please enter valid date'}</span>`;
                $(element).addClass('is-invalid').parent().find('.invalid-feedback').remove().end().append(errorMessage);
            }
        } else {
            $(element).removeClass('is-invalid').parent().find('em').remove();
        }
    }


    static validateDateTime(element, returnVal = false) {
        let _isValidDt = true;
        let _st = $(element).val();
        let regex = /^(0[1-9]|1[0-2])\/(0[1-9]|[12][0-9]|3[01])\/\d{4} (1[0-2]|0?[1-9]):([0-5][0-9]) (AM|PM)$/;

        //console.log(_st);
        if (_st) {
            if (regex.test(_st)) {
                let dateParts = _st.split(' ');
                _st = dateParts[0];

                let _sDT = $(element).data('dateStartDate');
                let _eDT = $(element).data('dateEndDate');
                if (!(_sDT !== undefined)) {
                    _sDT = '01/01/1900';
                }
                if (new Date(_sDT) > new Date(_st)) {
                    _isValidDt = false;
                }
                if (_eDT !== undefined && new Date(_eDT) < new Date(_st)) {
                    _isValidDt = false;
                }
            } else {
                _isValidDt = false;
            }
        }
        if (!returnVal) {
            if (_isValidDt) {
                $(element).removeClass('is-invalid');
                $(element).parent().find('em').remove();
            } else {
                let errorMessage = '<span class="error invalid-feedback">Please Enter Valid Date</span>';
                $(element).addClass('is-invalid').parent().find('.invalid-feedback').remove().end().append(errorMessage);
            }
        } else {
            return _isValidDt;
        }
    }
}

//let _dateNewClass = $('.dateNewClass');
//dateClass.init(_dateNewClass);

let yearClassElement = $('.yearClass');
yearClassElement.datepicker({
    format: "yyyy",
    viewMode: "years",
    minViewMode: "years",
    autoclose: true,
    startDate: '1600',
});
// yearClassElement.mask('9999', {
//     placeholder: "____"
// });

function getCountyByStateCode(parentClass, stateCode, targetId) {
    HTTP.Post('/api/getCountyByStateCode.php', {
        stateCode: stateCode
    }, function (resObj) {
        if (parseInt(resObj.success) === 1) {
            let countyArray = resObj.data;
            let countyHtml = '<option value=""></option>';
            let targetElement = $('.' + parentClass + ' #' + targetId);
            $.each(countyArray, function (key, value) {
                countyHtml += '<option value="' + value + '">' + value + '</option>';
            });
            targetElement.html(countyHtml);
            if (targetElement.hasClass('chzn-select')) {
                targetElement.trigger("chosen:updated");
            }
        }
    });

}

class globalJS {
    static highlightField(element) {
        element.addClass('highlightField');
        setTimeout(function () {
            element.removeClass('highlightField');
        }, 500);
    }

    static toggleSections(ele) {
        let _openCloseBtn = $(ele);
        let action = _openCloseBtn.data('action');
        if (action === 'open') {
            _openCloseBtn.data('action', 'close');
            _openCloseBtn.find('i').removeClass('ki-arrow-down').addClass('ki-arrow-up');
        } else if (action === 'close') {
            _openCloseBtn.data('action', 'open');
            _openCloseBtn.find('i').removeClass('ki-arrow-up').addClass('ki-arrow-down');
        }
        let toggles = $("#loanModForm").find('[data-card-tool="toggle"]');
        toggles.each(function () {
            let _cardbody = $(this).parents('.card').first().find('.card-body')
            if (action === 'open') {
                _cardbody.hide();
            } else if (action === 'close') {
                _cardbody.show();
            }
        });
    }
}
