DROP TABLE IF EXISTS `tblDrawRequestLineItemDocs`;

CREATE TABLE IF NOT EXISTS `tblDrawRequestLineItemDocs`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `lineItemId` INT NOT NULL,
    `docID` INT UNSIGNED NOT NULL,
    `uploadedBy` INT UNSIGNED NULL,
    `uploadedDate` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `uploaderType` ENUM('borrower', 'lender') NOT NULL DEFAULT 'borrower',
    `activeStatus` TINYINT(1) NOT NULL DEFAULT 1,
    `createdAt` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updatedAt` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_line_item_doc` (`lineItemId`, `docID`),
    <PERSON><PERSON>Y `idx_lineItemId` (`lineItemId`),
    <PERSON><PERSON>Y `idx_docId` (`docID`),
    CONSTRAINT `fk_lineItemDocs_lineItemId` FOREIGN KEY (`lineItemId`) REFERENCES `tblDrawRequestLineItems` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `fk_lineItemDocs_docID` FOREIGN KEY (`docID`) REFERENCES `tblLMRFileDocs` (`docID`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
