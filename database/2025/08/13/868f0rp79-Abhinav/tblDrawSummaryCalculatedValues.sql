DROP TABLE IF EXISTS `tblDrawSummaryCalculatedValues`;

CREATE TABLE `tblDrawSummaryCalculatedValues` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `LMRId` int(11) UNSIGNED NOT NULL,
    `drawRequestId` int(11) NOT NULL,
    `totalLoanAmount` decimal(18, 2) DEFAULT NULL,
    `rehabCost` decimal(18, 2) DEFAULT NULL,
    `rehabCostFinanced` decimal(18, 2) DEFAULT NULL,
    `currentLoanBalance` decimal(18, 2) DEFAULT NULL,
    `totalDrawsFunded` decimal(18, 2) DEFAULT NULL,
    `holdBackAmountRemaining` decimal(18, 2) DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `IX_DrawSummaryCalculatedValues_LMRId` (`LMRId`),
    KEY `IX_DrawSummaryCalculatedValues_DrawRequestId` (`drawRequestId`),
    CONSTRAINT `FK_DrawSummaryCalculatedValues_File` FOREIGN KEY (`LMRId`) REFERENCES `tblFile` (`LMRId`) ON DELETE CASCADE,
    CONSTRAINT `FK_DrawSummaryCalculatedValues_DrawRequest` FOREIGN KEY (`drawRequestId`) REFERENCES `tblFileDrawRequests` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
