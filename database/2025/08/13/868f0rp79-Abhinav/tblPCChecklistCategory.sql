ALTER TABLE `tblPCChecklistCategory` ADD COLUMN `order` INT(11) DEFAULT 0;

INSERT INTO `tblPCChecklistCategory` (`id`, `PCID`, `categoryName`, `activeStatus`)
VALUES (6, NULL, 'Budget Docs', 1);

SET SQL_SAFE_UPDATES = 0;
UPDATE tblPCChecklistCategory AS t
JOIN (
  SELECT id, ROW_NUMBER() OVER (ORDER BY id) AS seq
  FROM `tblPCChecklistCategory`
) AS r ON r.id = t.id
SET t.`order` = r.seq
WHERE t.id = r.id AND `categoryName` != 'Other Docs';

SET @order = (SELECT COALESCE(MAX(`order`), 0) + 1
               FROM `tblPCChecklistCategory`);
UPDATE `tblPCChecklistCategory`
SET `order` = @order
WHERE `categoryName` = 'Other Docs';
SET SQL_SAFE_UPDATES = 1;

