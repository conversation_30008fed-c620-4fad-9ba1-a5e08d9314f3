import { devices, PlaywrightTestConfig } from "@playwright/test";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

interface EnvConfig {
  UserTwo: { username: any; password: any };
  UserOne: { username: any; password: any };
  baseUrl: string;
  expectedUserName: string;
}

interface TestConfig extends PlaywrightTestConfig {
  envConfig?: EnvConfig;
}
const environment = process.env.TEST_ENV || "dev";

// ✅ Build path to JSON config
const envConfigPath = path.resolve(__dirname, `config/${environment}.json`);
if (!fs.existsSync(envConfigPath)) {
  throw new Error(
    `Config file for environment "${environment}" not found at ${envConfigPath}`
  );
}

const envConfig: EnvConfig = JSON.parse(
  fs.readFileSync(envConfigPath, "utf-8")
);

const defaultConfig: PlaywrightTestConfig = {
  timeout: 90 * 10000,
  expect: { timeout: 2000 },

  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 0 : 0,

  workers: 1,
  outputDir: "./report/test-results",
  snapshotDir: "./fixture/screenshots/",
  reporter: [
    ["html", { open: "never", outputFolder: "./report/html" }],
    ["junit", { outputFile: "./report/results.xml" }],
    ["allure-playwright", { outputFolder: "./report/allure-results" }],
  ],

  use: {
    headless: true,
    ...devices["Desktop Chrome"],
    deviceScaleFactor: undefined,
    viewport: { width: 1900, height: 1080 },
    // launchOptions: {
    //   args: ["--start-maximized"],
    // },
    video: {
      mode: "on",
      size: { width: 1020, height: 1920 },
    },
    trace: "on",
    baseURL: envConfig.baseUrl,
  },

  projects: [
    {
      name: "login",
      testMatch: "**/*.setup.ts",
      use: {
        headless: true,
        ...devices["Desktop Chrome"],
        deviceScaleFactor: undefined,
        video: {
          mode: "on",
          size: { width: 1900, height: 1080 },
        },
        trace: "on",
        baseURL: envConfig.baseUrl,
      },
    },
    {
      name: "tests",
      dependencies: ["login"],
      testDir: "./e2e/functional-tests",
      testMatch: ["**/*.spec.ts"],
      use: {
        headless: true,
        ...devices["Desktop Chrome"],
        deviceScaleFactor: undefined,
        viewport: { width: 1900, height: 1080 },
        video: {
          mode: "on",
          size: { width: 1920, height: 1080 },
        },
        trace: "on",
        baseURL: envConfig.baseUrl,
        storageState: "./fixture/.auth/userOne.json",
      },
    },
    {
      name: "visual-check",
      dependencies: ["login", "tests"],
      testDir: "./e2e/visual-tests",
      testMatch: ["**/*.spec.ts"],
      use: {
        headless: true,
        ...devices["Desktop Chrome"],
        deviceScaleFactor: undefined,
        viewport: { width: 1900, height: 1080 },
        video: {
          mode: "on",
          size: { width: 1020, height: 1920 },
        },
        trace: "on",
        baseURL: envConfig.baseUrl,
        storageState: "./fixture/.auth/userOne.json",
      },
    },
    {
      name: "visual-check-cv3",
      dependencies: ["login"],
      testDir: "./e2e/visual-tests",
      testMatch: ["cv3-visual-check-tests.spec.ts"],
      use: {
        headless: true,
        ...devices["Desktop Chrome"],
        deviceScaleFactor: undefined,
        viewport: { width: 1900, height: 1080 },
        video: {
          mode: "on",
          size: { width: 1000, height: 1920 },
        },
        trace: "on",
        baseURL: envConfig.baseUrl,
        storageState: "./fixture/.auth/userTwo.json",
      },
    },
  ],
};

const config: TestConfig = {
  ...defaultConfig,
  envConfig,
};

export default config;
