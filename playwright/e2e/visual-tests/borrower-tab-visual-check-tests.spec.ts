import test from "playwright/test"
import { AppManager } from "../../support/AppManager/PageObjectManager";

test.describe('first', () => {
    let loanFile: string = "/backoffice/LMRequest.php?eId=c20e03e74919f6c3&lId=4b649505adadf7c5&rId=75559c0d13eb0afe&tabOpt=QAPP&op=51130a1642421fcc&supp=VIPhelp";

    test.only('Validate loan file and visual checks', async ({ page }) => {
        const app = new AppManager(page);

        const steps = [
            { name: "Quick App", click: () => app.loanFile.clickOnQuickAppTab(), screenshot: "quickAppFullSection" },
            { name: "Full App", click: () => app.loanFile.clickOnFullAppTab(), screenshot: "fullAppFullSection" },
            { name: "Borrower Info", click: () => app.loanFile.clickOnBorrowerInfoTab(), screenshot: "borrowerInfoFullSection" },
            { name: "Property Info", click: () => app.loanFile.clickOnPropertyInfoTab(), screenshot: "propertyInfoFullSection" },
            { name: "Loan Info", click: () => app.loanFile.clickOnLoanInfoPercentageTab(), screenshot: "loanInfoFullSection" },
            { name: "Personal Inc & Exp", click: () => app.loanFile.clickOnPersonalIncAndExpSectionTab(), screenshot: "personalIncExpSection" },
            { name: "Assets & Liabilities", click: () => app.loanFile.clickOnAssetsAndLiabilitiesTab(), screenshot: "assetsAndLiabilitiesSection" },
            { name: "Billing & Com", click: () => app.loanFile.clickOnBillingAndComTab(), screenshot: "billingAndComSection" },
            { name: "Docs", click: () => app.loanFile.clickOnDocsTab(), screenshot: "docsSection" },
            { name: "Workflow", click: () => app.loanFile.clickOnWorkflowTab(), screenshot: "workflowSection" },
            { name: "Task", click: () => app.loanFile.clickOnTaskTab(), screenshot: "taskSection" },
            { name: "Credit Memo", click: () => app.loanFile.clickOnCreateMemoTab(), screenshot: "creditMemoSection" },
            { name: "Admin", click: () => app.loanFile.clickOnAdminTab(), screenshot: "adminSection" },
            { name: "1003", click: () => app.loanFile.clickOn1003Tab(), screenshot: "oneZeroZeroThreeSection" },
            { name: "HMDA", click: () => app.loanFile.clickOnHMDATab(), screenshot: "hmdaSection" },
            { name: "HUD", click: () => app.loanFile.clickOnHUDTab(), screenshot: "hudSection" },
            { name: "Deal Analysis", click: () => app.loanFile.clickOnDataAnalysisTab(), screenshot: "dealAnalysisSection" },
            { name: "Credit Decision", click: () => app.loanFile.clickOnCreditDecisionTab(), screenshot: "creditDecisionSection" },
            { name: "Adverse Action", click: () => app.loanFile.clickOnAdverseActionTab(), screenshot: "adverseActionSection" },
            { name: "Contacts", click: () => app.loanFile.clickOnContactsTab(), screenshot: "contactsSection" },
            { name: "Offers", click: () => app.loanFile.clickOnOffersTab(), screenshot: "offersSection" },
            { name: "Change Log", click: () => app.loanFile.clickOnChangeLogTab(), screenshot: "changeLogSection" },
            { name: "Clear Loan Form", click: () => app.loanFile.clickOnClearLoanFormTab(), screenshot: "clearLoanFormSection" },
            { name: "Post-Closing/Loan Trade", click: () => app.loanFile.clickOnPostClosingLoanTradeTab(), screenshot: "postClosingLoanTradeSection" },

            // having UI changes 

            // { name: "Servicing 2.0", click: () => app.loanFile.clickOnServicing2Point0Tab(), screenshot: "servicing2Section" },
            // { name: "3rd Party Integrations", click: () => app.loanFile.clickOnThirdPartyIntegrationsTab(), screenshot: "thirdPartyIntegrationSection" },
            // { name: "Rule Check", click: () => app.loanFile.clickOnRuleCheckTab(), screenshot: "ruleCheckSection" },
            // { name: "Fill Loan Form A", click: () => app.loanFile.clickOnFillLoanFormATab(), screenshot: "fillLoanFormASection" },
            // { name: "Fill Loan Form B", click: () => app.loanFile.clickOnFillLoanFormBTab(), screenshot: "fillLoanFormBSection" },
        ];

        for (const step of steps) {
            await test.step(`Validate ${step.name} tab and take screenshot`, async () => {
                if (step.screenshot == "fullAppFullSection") {
                    await app.borrowerInfoTab.openLoanFile(loanFile);
                    app.utilities.logStep("Loan file is open correctly");
                    app.utilities.logStep(`Clicking on ${step.name} tab`);
                    await step.click();
                    await page.waitForLoadState('networkidle');
                    app.utilities.logStep("Clicked on tab correctly");
                    await app.loanFile.clickOncloseAllOpenTabsButton();
                    app.utilities.logStep("Close all open section for selected tab");
                    await app.loanFile.takeScreenShotOfAdminInfoSectionAndValidate(step.screenshot, page);
                    app.utilities.logStep("Screen shot is taken correctly");
                } else {
                    await app.borrowerInfoTab.openLoanFile(loanFile);
                    app.utilities.logStep("Loan file is open correctly");
                    app.utilities.logStep(`Clicking on ${step.name} tab`);
                    await step.click();
                    await page.waitForLoadState('networkidle');
                    app.utilities.logStep("Clicked on tab correctly");
                    await app.loanFile.takeScreenShotOfAdminInfoSectionAndValidate(step.screenshot, page);
                    app.utilities.logStep("Screen shot is taken correctly");
                }
            });
        }
    });


    test.skip('Validate Borrower Info Tab UI admin section', async ({ page }) => {
        const app = new AppManager(page);
        await test.step('Validate Borrower Info Tab UI', async () => {
            await app.borrowerInfoTab.openLoanFile(loanFile);
            app.utilities.logStep("Validate the borrower info tab for admin info section UI changes");
            await app.adminInfoSection.takeScreenShotOfAdminInfoSectionAndValidate("adminInfoSection", page);
            await app.adminInfoSection.clickOncloseAllOpenTabsButton();
            await app.adminInfoSection.openAdminInfoSection();
            await app.adminInfoSection.takeScreenShotOfAdminInfoSectionAndValidate("adminInfoSection", page);
        });
    });

    test.skip('Validate Borrower Info Tab UI borrower info section', async ({ page }) => {
        const app = new AppManager(page);
        await test.step('Validate Borrower Info Tab UI', async () => {
            await app.borrowerInfoTab.openLoanFile(loanFile);
            app.utilities.logStep("Validate the borrower info tab for borrower Info section UI changes");
            await app.adminInfoSection.takeScreenShotOfAdminInfoSectionAndValidate("borrowerInfoSection", page);
            await app.adminInfoSection.clickOncloseAllOpenTabsButton();
            await app.adminInfoSection.openBorrowerInfoSection();
            await app.adminInfoSection.takeScreenShotOfAdminInfoSectionAndValidate("borrowerInfoSection", page);
        });
    });

    test.skip('Validate Borrower Info Tab UI borrower entity section', async ({ page }) => {
        const app = new AppManager(page);
        await test.step('Validate Borrower Info Tab UI', async () => {
            await app.borrowerInfoTab.openLoanFile(loanFile);
            app.utilities.logStep("Validate the borrower info tab for borrower Entity Section UI changes");
            await app.adminInfoSection.takeScreenShotOfAdminInfoSectionAndValidate("borrowerEntitySection", page);
            await app.adminInfoSection.clickOncloseAllOpenTabsButton();
            await app.adminInfoSection.openBorrowerEntitySection();
            await app.adminInfoSection.takeScreenShotOfAdminInfoSectionAndValidate("borrowerEntitySection", page);
        });
    });

    test.skip('Validate Borrower Info Tab UI borrower Background section', async ({ page }) => {
        const app = new AppManager(page);
        await test.step('Validate Borrower Background Tab UI', async () => {
            await app.borrowerInfoTab.openLoanFile(loanFile);
            app.utilities.logStep("Validate the borrower info tab for borrower Background Section UI changes");
            await app.adminInfoSection.takeScreenShotOfAdminInfoSectionAndValidate("borrowerBackgroundSection", page);
            // await app.adminInfoSection.clickOncloseAllOpenTabsButton();
            // await app.adminInfoSection.openBorrowerBackgroundSection();
            // await app.adminInfoSection.takeScreenShotOfAdminInfoSectionAndValidate("borrowerBackgroundSection");
        });
    });

    test.skip('Validate Borrower Info Tab UI SBA Questions section', async ({ page }) => {
        const app = new AppManager(page);
        await test.step('Validate Borrower Background Tab UI', async () => {
            await app.borrowerInfoTab.openLoanFile(loanFile);
            app.utilities.logStep("Validate the borrower info tab for SBA Questions Section UI changes");
            await app.adminInfoSection.takeScreenShotOfAdminInfoSectionAndValidate("SBAQuestionsSection", page);
            await app.adminInfoSection.clickOncloseAllOpenTabsButton();
            await app.adminInfoSection.openSBAQuestionsSection();
            await app.adminInfoSection.takeScreenShotOfAdminInfoSectionAndValidate("SBAQuestionsSection", page);
        });
    });

    test.skip('Validate Borrower Info Tab UI Borrower Experience section', async ({ page }) => {
        const app = new AppManager(page);
        await test.step('Validate Borrower Background Tab UI', async () => {
            await app.borrowerInfoTab.openLoanFile(loanFile);
            app.utilities.logStep("Validate the borrower info tab for Borrower Experience Section UI changes");
            await app.adminInfoSection.takeScreenShotOfAdminInfoSectionAndValidate("borrowerExperience", page);
            await app.adminInfoSection.clickOncloseAllOpenTabsButton();
            await app.adminInfoSection.openBorrowerExperienceSection();
            await app.adminInfoSection.takeScreenShotOfAdminInfoSectionAndValidate("borrowerExperience", page);
        });
    });

    test.skip('Validate Borrower Info Tab UI Additional Guarantors section', async ({ page }) => {
        const app = new AppManager(page);
        await test.step('Validate Borrower Background Tab UI', async () => {
            await app.borrowerInfoTab.openLoanFile(loanFile);
            app.utilities.logStep("Validate the borrower info tab for Additional Guarantors Section UI changes");
            await app.adminInfoSection.takeScreenShotOfAdminInfoSectionAndValidate("additionalGuarantors", page);
            await app.adminInfoSection.clickOncloseAllOpenTabsButton();
            await app.adminInfoSection.openAdditionalGuarantorsSection();
            await app.adminInfoSection.takeScreenShotOfAdminInfoSectionAndValidate("additionalGuarantors", page);
        });
    });
});