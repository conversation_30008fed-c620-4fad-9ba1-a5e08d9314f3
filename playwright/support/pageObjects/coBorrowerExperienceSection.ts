import { expect, Locator, <PERSON> } from "@playwright/test";
import { AppManager } from "../AppManager/PageObjectManager";

export class coBorrowerExperienceSection {
    private page: Page;
    private fixFlipYes: any;
    private fixFlipNo: any;
    private fixFlipYears: any;
    private fixFlipPropertiesCompleted: any;
    private fixFlipPastDeals: any;
    private fixFlipAddress1: any;
    private fixFlipAddress2: any;
    private fixFlipOutcome1: any;
    private fixFlipOutcome2: any;
    private fixFlipOutcome3: any;
    private groundUpYes: any;
    private groundUpNo: any;
    private groundUpYears: any;
    private groundUpPropertiesCompleted: any;
    private groundUpPastDeals: any;
    private groundUpAddress1: any;
    private groundUpAddress2: any;
    private groundUpOutcome1: any;
    private groundUpOutcome2: any;
    private groundUpOutcome3: any;
    private sellingYes: any;
    private sellingNo: any;
    private sellingYears: any;
    private sellingPropertiesCompleted: any;
    private sellingPastDeals: any;
    private sellingAddress1: any;
    private sellingAddress2: any;
    private sellingOutcome1: any;
    private sellingOutcome2: any;
    private sellingOutcome3: any
    private projectsYes: any;
    private projectsNo: any;
    private projectsHowMany: any;
    private investmentYes: any;
    private investmentNo: any;
    private investmentHowMany: any;
    private clubYes: any;
    private clubNo: any;
    private clubName: any;
    private licenseYes: any;
    private licenseNo: any;
    private licenseSelect: any;
    private licenseNumberField: any;
    private fullTimeYes: any;
    private fullTimeNo: any;
    private liquidReservesField: any;
    private primaryStrategyfield: any;
    private primaryStrategySelect: any;
    private primaryStrategyExplain: any;
    private closestatus: any;
    private coBorrowerexpcard: any


    constructor(page: Page) {
        this.page = page;
        this.fixFlipYes = page.locator("//label[@for='haveCoBorREInvestmentExperience_1']");
        this.fixFlipNo = page.locator("//label[@for='haveCoBorREInvestmentExperience_2']");
        this.fixFlipYears = page.locator('//*[@id="coBorNoOfFlippingExperience"]');
        this.fixFlipPropertiesCompleted = page.locator('//*[@id="coBorNoOfREPropertiesCompleted"]');
        this.fixFlipPastDeals = page.locator('//*[@id="coBorREAddress1"]');
        this.fixFlipAddress1 = page.locator('//*[@id="coBorREAddress2"]');
        this.fixFlipAddress2 = page.locator('//*[@id="coBorREAddress3"]');
        this.fixFlipOutcome1 = page.locator('//*[@id="coBorOutcomeRE1"]');
        this.fixFlipOutcome2 = page.locator('//*[@id="coBorOutcomeRE2"]');
        this.fixFlipOutcome3 = page.locator('//*[@id="coBorOutcomeRE3"]');
        this.groundUpYes = page.locator("//label[contains(@for,'haveCoBorRehabConstructionExperience_1')]");
        this.groundUpNo = page.locator("//label[@for='haveCoBorRehabConstructionExperience_2']");
        this.groundUpYears = page.locator('//*[@id="coBorNoOfYearRehabExperience"]');
        this.groundUpPropertiesCompleted = page.locator('//*[@id="coBorRehabPropCompleted"]');
        this.groundUpPastDeals = page.locator('//*[@id="coBorRCAddress1"]');
        this.groundUpAddress1 = page.locator('//*[@id="coBorRCAddress2"]');
        this.groundUpAddress2 = page.locator('//*[@id="coBorRCAddress3"]');
        this.groundUpOutcome1 = page.locator('//*[@id="coBorRCOutcome1"]');
        this.groundUpOutcome2 = page.locator('//*[@id="coBorRCOutcome2"]');
        this.groundUpOutcome3 = page.locator('//*[@id="coBorRCOutcome3"]');
        this.sellingYes = page.locator("//label[contains(@for,'haveCoBorSellPropertieyes')]");
        this.sellingNo = page.locator("//label[@for='haveCoBorSellPropertieNo']");
        this.sellingYears = page.locator('//*[@id="coBorNoOfProSellExperience"]');
        this.sellingPropertiesCompleted = page.locator('//*[@id="coBorNoOfProSellCompleted"]');
        this.sellingPastDeals = page.locator('//*[@id="coBorSellAddress1"]');
        this.sellingAddress1 = page.locator('//*[@id="coBorSellAddress2"]');
        this.sellingAddress2 = page.locator('//*[@id="coBorSellAddress3"]');
        this.sellingOutcome1 = page.locator('//*[@id="coBorSellOutcome1"]');
        this.sellingOutcome2 = page.locator('//*[@id="coBorSellOutcome2"]');
        this.sellingOutcome3 = page.locator('//*[@id="coBorSellOutcome3"]');
        this.projectsYes = page.locator("//label[@for='haveCoBorProjectCurrentlyInProgress_1']");
        this.projectsNo = page.locator("//label[@for='haveCoBorProjectCurrentlyInProgress_2']");
        this.projectsHowMany = page.locator('//*[@id="coBorNoOfProjectCurrently"]');
        this.investmentYes = page.locator("//label[contains(@for,'haveCoBorOwnInvestmentProperties_1')]");
        this.investmentNo = page.locator("//label[@for='haveCoBorOwnInvestmentProperties_2']");
        this.investmentHowMany = page.locator('//*[@id="coBorNoOfOwnProp"]');
        this.clubYes = page.locator("//label[contains(@for,'areCoBorMemberOfInvestmentClub_1')]");
        this.clubNo = page.locator("//label[@for='areCoBorMemberOfInvestmentClub_2']");
        this.clubName = page.locator('//*[@id="coBorClubName"]');
        this.licenseYes = page.locator("//label[@for='haveCoBorProfLicences_1']");
        this.licenseNo = page.locator("//label[@for='haveCoBorProfLicences_2']");
        this.licenseSelect = page.locator('select#coBorProfLicence');
        this.licenseNumberField = page.locator('//*[@id="coBorLicenseNo"]');
        this.fullTimeYes = page.locator("//label[contains(@for,'coFullTimeRealEstateInvestor_1')]");
        this.fullTimeNo = page.locator("//label[@for='coFullTimeRealEstateInvestor_2']");
        this.liquidReservesField = page.locator('//*[@id="coBorliquidReserves"]');
        this.primaryStrategyfield = page.locator('//*[@id="coBorPrimaryInvestmentStrategy_chosen"]');
        this.primaryStrategySelect = page.locator('.chosen-drop .chosen-results li');
        this.primaryStrategyExplain = page.locator('//*[@id="coBorPrimaryInvestmentStrategyExplain"]');
        this.closestatus = page.locator('//*[@id="coBorPrimaryInvestmentStrategy_chosen"]/ul/li[1]/a');
        this.coBorrowerexpcard = page.locator('//*[@id="loanModForm"]/div[17]/div[1]')

    }

    async checkcoBorrowerexpvisibility() {
        await expect(this.coBorrowerexpcard).toBeVisible({ timeout: 5000 });
        await this.coBorrowerexpcard.scrollIntoViewIfNeeded();
    }

    async fillFixFlipExperience(data: {
        hasExperience: boolean,
        years?: string,
        propertiesCompleted?: string,
        pastDeals?: string,
        address1?: string,
        address2?: string,
        outcome1?: string,
        outcome2?: string,
        outcome3?: string
    }): Promise<void> {

        const selectYesNo = async (yesLocator: any, noLocator: any, value: boolean) => {
            if (value) await yesLocator.check();
            else await noLocator.check();
        };

        await selectYesNo(this.fixFlipYes, this.fixFlipNo, data.hasExperience);

        if (data.hasExperience) {
            await this.fixFlipYears.fill(data.years || '');
            await this.fixFlipPropertiesCompleted.fill(data.propertiesCompleted || '');
            await this.fixFlipPastDeals.fill(data.pastDeals || '');
            await this.fixFlipAddress1.fill(data.address1 || '');
            await this.fixFlipAddress2.fill(data.address2 || '');
            await this.fixFlipOutcome1.fill(data.outcome1 || '');
            await this.fixFlipOutcome2.fill(data.outcome2 || '');
            await this.fixFlipOutcome3.fill(data.outcome3 || '');
        }
    }

    async fillGroundUpExperience(data: {
        hasExperience: boolean,
        years?: string,
        propertiesCompleted?: string,
        pastDeals?: string,
        address1?: string,
        address2?: string,
        outcome1?: string,
        outcome2?: string,
        outcome3?: string
    }): Promise<void> {

        const selectYesNo = async (yesLocator: any, noLocator: any, value: boolean) => {
            if (value) await yesLocator.check();
            else await noLocator.check();
        };

        await selectYesNo(this.groundUpYes, this.groundUpNo, data.hasExperience);

        if (data.hasExperience) {
            await this.groundUpYears.fill(data.years || '');
            await this.groundUpPropertiesCompleted.fill(data.propertiesCompleted || '');
            await this.groundUpPastDeals.fill(data.pastDeals || '');
            await this.groundUpAddress1.fill(data.address1 || '');
            await this.groundUpAddress2.fill(data.address2 || '');
            await this.groundUpOutcome1.fill(data.outcome1 || '');
            await this.groundUpOutcome2.fill(data.outcome2 || '');
            await this.groundUpOutcome3.fill(data.outcome3 || '');
        }
    }

    async fillSellingWithoutConstruction(data: {
        hasExperience: boolean,
        years?: string,
        propertiesCompleted?: string,
        pastDeals?: string,
        address1?: string,
        address2?: string,
        outcome1?: string,
        outcome2?: string,
        outcome3?: string
    }): Promise<void> {
        const selectYesNo = async (yesLocator: any, noLocator: any, value: boolean) => {
            if (value) await yesLocator.check();
            else await noLocator.check();
        };

        await selectYesNo(this.sellingYes, this.sellingNo, data.hasExperience);

        if (data.hasExperience) {
            await this.sellingYears.fill(data.years || '');
            await this.sellingPropertiesCompleted.fill(data.propertiesCompleted || '');
            await this.sellingPastDeals.fill(data.pastDeals || '');
            await this.sellingAddress1.fill(data.address1 || '');
            await this.sellingAddress2.fill(data.address2 || '');
            await this.sellingOutcome1.fill(data.outcome1 || '');
            await this.sellingOutcome2.fill(data.outcome2 || '');
            await this.sellingOutcome3.fill(data.outcome3 || '');
        }
    }

    async fillProjectsInProgress(data: { hasProjects: boolean, howMany?: string }): Promise<void> {
        await (data.hasProjects ? this.projectsYes.check() : this.projectsNo.check());

        if (data.hasProjects) {
            await this.projectsHowMany.fill(data.howMany || '');
        }
    }

    async fillInvestmentProperties(data: { hasProperties: boolean, howMany?: string }): Promise<void> {
        await (data.hasProperties ? this.investmentYes.check() : this.investmentNo.check());

        if (data.hasProperties) {
            await this.investmentHowMany.fill(data.howMany || '');
        }
    }

    async fillInvestmentClub(data: { isMember: boolean, groupName?: string }): Promise<void> {
        await (data.isMember ? this.clubYes.check() : this.clubNo.check());

        if (data.isMember) {
            await this.clubName.fill(data.groupName || '');
        }
    }

    async fillProfessionalLicenses(data: {
        hasLicenses: boolean,
        licenseType?: string,
        licenseNumber?: string,
        isFullTimeInvestor?: boolean
    }): Promise<void> {
        console.log("Filling Professional Licenses...");

        const selectYesNo = async (yesLocator: any, noLocator: any, value: boolean) => {
            if (value) await yesLocator.check();
            else await noLocator.check();
        };

        await selectYesNo(this.licenseYes, this.licenseNo, data.hasLicenses);

        if (data.hasLicenses) {

            // Fill License Type
            if (data.licenseType) {
                await this.licenseSelect.selectOption({ value: data.licenseType });
                await expect(this.licenseSelect).toHaveValue(data.licenseType);
            }

            // Fill License Number
            if (data.licenseNumber) {
                await this.licenseNumberField.clear();
                await this.fillAndValidate(this.licenseNumberField, data.licenseNumber);
            }

            // Full-Time Investor
            if (data.isFullTimeInvestor !== undefined) {
                if (data.isFullTimeInvestor) {
                    await this.fullTimeYes.click();
                } else {
                    await this.fullTimeNo.click();
                }
            }
        } else {
            // Select No
            await this.licenseNo.click();
        }

        console.log(`Professional Licenses filled with option: ${data.hasLicenses ? 'Yes' : 'No'}`);
    }


    async setLiquidReserves(amount: string): Promise<void> {
        await this.liquidReservesField.clear();
        await this.fillAndValidate(this.liquidReservesField, amount);
    }

    async setPrimaryInvestmentStrategy(value: string) {
        const app = new AppManager(this.page);

        let statusVisible = await app.utilities.isElementVisible(this.page, this.closestatus)
        if (statusVisible) {
            console.log("Entered IF block");
            await this.closestatus.click();
        }
        else {
            console.log("Entered ELSE block");
            await this.primaryStrategyfield.click();
            const optionToSelect = this.primaryStrategySelect.filter({ hasText: value });
            optionToSelect.waitFor({ state: "visible", timeout: 5000 });
            await optionToSelect.click();
        }
    }

    async setPrimaryInvestmentStrategyExplain(text: string): Promise<void> {
        await this.primaryStrategyExplain.clear();
        await this.fillAndValidate(this.primaryStrategyExplain, text);
    }

    async fillAndValidate(locator: Locator, value: string) {
        await locator.clear();
        await locator.fill(value);
        await expect(locator).toHaveValue(value);
    }

    async validateFixFlipExperience(data: {
        hasExperience: boolean,
        years?: string,
        propertiesCompleted?: string,
        pastDeals?: string,
        address1?: string,
        address2?: string,
        outcome1?: string,
        outcome2?: string,
        outcome3?: string
    }): Promise<void> {
        if (data.hasExperience) {
            await expect(this.fixFlipYes).toBeChecked();
            if (data.years) await expect(this.fixFlipYears).toHaveValue(data.years);
            if (data.propertiesCompleted) await expect(this.fixFlipPropertiesCompleted).toHaveValue(data.propertiesCompleted);
            if (data.pastDeals) await expect(this.fixFlipPastDeals).toHaveValue(data.pastDeals);
            if (data.address1) await expect(this.fixFlipAddress1).toHaveValue(data.address1);
            if (data.address2) await expect(this.fixFlipAddress2).toHaveValue(data.address2);
            if (data.outcome1) await expect(this.fixFlipOutcome1).toHaveValue(data.outcome1);
            if (data.outcome2) await expect(this.fixFlipOutcome2).toHaveValue(data.outcome2);
            if (data.outcome3) await expect(this.fixFlipOutcome3).toHaveValue(data.outcome3);
        } else {
            await expect(this.fixFlipNo).toBeChecked();
        }
    }

    async validateGroundUpExperience(data: any): Promise<void> {
        if (data.hasExperience) {
            await expect(this.groundUpYes).toBeChecked();
            if (data.years) await expect(this.groundUpYears).toHaveValue(data.years);
            if (data.propertiesCompleted) await expect(this.groundUpPropertiesCompleted).toHaveValue(data.propertiesCompleted);
            if (data.pastDeals) await expect(this.groundUpPastDeals).toHaveValue(data.pastDeals);
            if (data.address1) await expect(this.groundUpAddress1).toHaveValue(data.address1);
            if (data.address2) await expect(this.groundUpAddress2).toHaveValue(data.address2);
            if (data.outcome1) await expect(this.groundUpOutcome1).toHaveValue(data.outcome1);
            if (data.outcome2) await expect(this.groundUpOutcome2).toHaveValue(data.outcome2);
            if (data.outcome3) await expect(this.groundUpOutcome3).toHaveValue(data.outcome3);
        } else {
            await expect(this.groundUpNo).toBeChecked();
        }
    }

    async validateSellingWithoutConstruction(data: any): Promise<void> {
        if (data.hasExperience) {
            await expect(this.sellingYes).toBeChecked();
            if (data.years) await expect(this.sellingYears).toHaveValue(data.years);
            if (data.propertiesCompleted) await expect(this.sellingPropertiesCompleted).toHaveValue(data.propertiesCompleted);
            if (data.pastDeals) await expect(this.sellingPastDeals).toHaveValue(data.pastDeals);
            if (data.address1) await expect(this.sellingAddress1).toHaveValue(data.address1);
            if (data.address2) await expect(this.sellingAddress2).toHaveValue(data.address2);
            if (data.outcome1) await expect(this.sellingOutcome1).toHaveValue(data.outcome1);
            if (data.outcome2) await expect(this.sellingOutcome2).toHaveValue(data.outcome2);
            if (data.outcome3) await expect(this.sellingOutcome3).toHaveValue(data.outcome3);
        } else {
            await expect(this.sellingNo).toBeChecked();
        }
    }

    async validateProjectsInProgress(data: any): Promise<void> {
        if (data.hasProjects) {
            await expect(this.projectsYes).toBeChecked();
            if (data.howMany) await expect(this.projectsHowMany).toHaveValue(data.howMany);
        } else {
            await expect(this.projectsNo).toBeChecked();
        }
    }

    async validateInvestmentProperties(data: any): Promise<void> {
        if (data.hasProperties) {
            await expect(this.investmentYes).toBeChecked();
            if (data.howMany) await expect(this.investmentHowMany).toHaveValue(data.howMany);
        } else {
            await expect(this.investmentNo).toBeChecked();
        }
    }

    async validateInvestmentClub(data: any): Promise<void> {
        if (data.isMember) {
            await expect(this.clubYes).toBeChecked();
            if (data.groupName) await expect(this.clubName).toHaveValue(data.groupName);
        } else {
            await expect(this.clubNo).toBeChecked();
        }
    }

    async validateProfessionalLicenses(data: any): Promise<void> {
        if (data.hasLicenses) {
            await expect(this.licenseYes).toBeChecked();
            if (data.licenseType) await expect(this.licenseSelect).toHaveValue(data.licenseType);
            if (data.licenseNumber) await expect(this.licenseNumberField).toHaveValue(data.licenseNumber);
            if (data.isFullTimeInvestor !== undefined) {
                if (data.isFullTimeInvestor) await expect(this.fullTimeYes).toBeChecked();
                else await expect(this.fullTimeNo).toBeChecked();
            }
        } else {
            await expect(this.licenseNo).toBeChecked();
        }
    }

    async validateLiquidReserves(value: string): Promise<void> {
        await expect(this.liquidReservesField).toHaveValue(value);
    }

    async validatePrimaryStrategy(value: string, explain: string): Promise<void> {
        await expect(this.primaryStrategyfield).toContainText(value);
        await expect(this.primaryStrategyExplain).toHaveValue(explain);
    }

    async validateAllFields(data: any): Promise<void> {
        await this.validateFixFlipExperience(data.fixFlipExperience);
        await this.validateGroundUpExperience(data.groundUpExperience);
        await this.validateSellingWithoutConstruction(data.sellingWithoutConstruction);
        await this.validateProjectsInProgress(data.projectsInProgress);
        await this.validateInvestmentProperties(data.investmentProperties);
        await this.validateInvestmentClub(data.investmentClub);
        await this.validateProfessionalLicenses(data.professionalLicenses);
        await this.validateLiquidReserves(data.liquidReserves);
        await this.validatePrimaryStrategy(data.primaryStrategy, data.primaryStrategyExplain);
    }

    async setAllExperienceFieldsToNo(): Promise<void> {
        await this.fixFlipNo.click();

        await this.groundUpNo.click();

        await this.sellingNo.click();

        await this.projectsNo.click();

        await this.investmentNo.click();

        await this.clubNo.click();

        await this.licenseNo.click();

        await this.liquidReservesField.click();
        await this.liquidReservesField.clear();

        await this.closestatus.click();

        await this.primaryStrategyExplain.click();
        await this.primaryStrategyExplain.clear();

    }

    private async expectRadio(yesLocator: Locator, noLocator: Locator, expected: boolean, fieldName: string): Promise<void> {
        if (expected) {
            await expect(yesLocator).toBeChecked();
            await expect(noLocator).not.toBeChecked();
        } else {
            await expect(noLocator).toBeChecked();
            await expect(yesLocator).not.toBeChecked();
        }
    }

    async validateAllExperienceFieldsNo(): Promise<void> {
        console.log("Validating all Co-Borrower Experience fields = NO...");

        await this.expectRadio(this.fixFlipYes, this.fixFlipNo, false, "Fix & Flip");
        await this.expectRadio(this.groundUpYes, this.groundUpNo, false, "Ground Up");
        await this.expectRadio(this.sellingYes, this.sellingNo, false, "Selling Without Construction");
        await this.expectRadio(this.projectsYes, this.projectsNo, false, "Projects In Progress");
        await this.expectRadio(this.investmentYes, this.investmentNo, false, "Investment Properties");
        await this.expectRadio(this.clubYes, this.clubNo, false, "Investment Club");
        await this.expectRadio(this.licenseYes, this.licenseNo, false, "Professional Licenses");
        await expect(this.liquidReservesField).toHaveValue('');
        await expect(this.primaryStrategyfield).toContainText("");
        await expect(this.primaryStrategyExplain).toHaveValue('');

    }
}
