import { expect, Locator, <PERSON> } from "@playwright/test";
import { AppManager } from "../AppManager/PageObjectManager";

export class borrowerInfoSection {
    private page: Page;
    private saveButton: Locator

    private borrowerInfoCard: Locator;
    private borrowerInfoFields: Locator;
    private borrowerEmail: any;
    private cellCarrier: Locator;
    private isCoBo: Locator;
    private presentCounty: Locator;
    private borResidedPresentAddrYes: Locator;
    private borResidedPresentAddrNo: Locator;
    private mailingAddressSameasCurrentAddress: Locator;
    private alternateNamesSection: Locator;
    private alternateNamesSectionCount: Locator;
    private alternateNameAddButton: Locator;
    private alternateSection2: Locator;
    private alternateSectionRemoveButton: Locator;
    private dateOfDivorce: Locator;
    private citizenshipUS: Locator;
    private citizenshipPermResident: Locator;
    private citizenshipNonPermResident: Locator;
    private servicingMemberNo: Locator;
    private servicingMemberInfoDropdown: Locator;
    private servicingMemberInfoSearchInput: Locator;
    private servicingMemberInfoOptions: Locator;
    private serviceExpirationDateInput: Locator;
    private serviceExpirationDate: Locator;
    private authorizationStatusDropdown: Locator;
    private authorizationStatusOptions: Locator;
    private createLoanMenu: Locator;
    private quickAppMenu: Locator;
    private borrowerFName: Locator;
    private borrowerLName: Locator;
    private fileNameHeader: Locator;
    private borrowerMName: any;
    private borrowerSecondaryEmail: any;
    private phoneNumber: any;
    private cellNo: any;
    private borrFax: any;
    private listValueOfInputFields: Locator;
    private methodOfContact: Locator;
    private preferredCommOptions: Locator;
    private allPreferredCommOptions: Locator;
    private altPhoneNumber: Locator;
    private workNumber: Locator;
    // Address
    private presentAddress: Locator;
    private presentUnit: Locator;
    private presentCity: Locator;
    private presentState: Locator;
    private presentZip: Locator;
    private presentCountry: Locator;
    // Property Info
    private presentPropLengthTime: Locator;
    private presentPropLengthMonths: Locator;
    private borPresentPropType: Locator;
    private currentRPM: Locator;
    // Former Address Fields
    private hasresidedlessthan2: Locator;
    private formerAddressSection: Locator;
    private formerAddress: Locator;
    private formerUnit: Locator;
    private formerCity: Locator;
    private formerState: Locator; // dropdown
    private formerZip: Locator;
    private formerCountry: Locator; // dropdown
    private formerRentOrOwn: Locator; // dropdown
    private lengthOfTimePrevPropYears: Locator; // dropdown
    private lengthOfTimePrevPropMonths: Locator; // dropdown
    private rentPerMonthPrevAddr: Locator;
    // Mailing address section
    private mailingAddressInput: Locator;
    private mailingUnitInput: Locator;
    private mailingCityInput: Locator;
    private mailingStateDropdown: Locator;
    private mailingZipInput: Locator;
    private mailingCountryDropdown: Locator;
    // Alternate names section
    private alternateFirstNameInput: Locator;
    private alternateMiddleNameInput: Locator;
    private alternateLastNameInput: Locator;
    // Personal Info
    private borrowerDOBInput: Locator;
    private borrowerPOBInput: Locator;
    private ssnInput: Locator;
    private driverLicenseStateDropdown: Locator;
    private driverLicenseNumberInput: Locator;
    private driverLicenseIssuanceDateInput: Locator;
    private driverLicenseExpirationDateInput: Locator;
    private maritalStatusUnmarriedRadio: Locator;
    private maritalStatusMarriedRadio: Locator;
    private maritalStatusSeparatedRadio: Locator;
    // Marital Info
    private marriageDateInput: Locator;
    private divorceDateInput: Locator;
    private maidenNameInput: Locator;
    private spouseNameInput: Locator;
    // Citizenship (Radio Buttons)
    private citizenshipForeignNational: Locator;
    // Armed Forces Service
    private servicingMemberYes: Locator;
    // Dependents
    private agesOfDependentInput: Locator;
    private numberOfDependentsInput: Locator;
    // Credit Scores
    private midFicoScoreInput: Locator;
    private equifaxScoreInput: Locator;
    private transunionScoreInput: Locator;
    private experianScoreInput: Locator;
    // Credit Score Range
    private creditScoreRangeDropdown: Locator;
    // Authorization Status
    private authorizationStatusSelected: Locator;

    constructor(page: Page) {
        this.page = page;
        this.saveButton = page.locator("#saveBtn");
        this.authorizationStatusDropdown = page.locator("#authorizationStatus_chosen a");
        this.authorizationStatusOptions = page.locator("#authorizationStatus_chosen li");
        this.createLoanMenu = page.locator("#menu_createloan");
        this.quickAppMenu = page.locator("#menu_quickapp");
        this.servicingMemberNo = page.locator("label[for='isServicingMember_2']");
        this.servicingMemberInfoDropdown = page.locator("#servicingMemberInfo_chosen");
        this.servicingMemberInfoSearchInput = this.page.locator("#servicingMemberInfo_chosen .search-field input");
        this.servicingMemberInfoOptions = page.locator("#servicingMemberInfo_chosen .chosen-results li");
        this.serviceExpirationDateInput = page.locator("#serviceExpirationDate");
        this.serviceExpirationDate = page.locator("label[for='serviceExpirationDate']");
        this.dateOfDivorce = page.locator("label[for='divorceDate']");
        this.alternateSection2 = page.locator("#alternativeNameDiv_2");
        this.alternateSectionRemoveButton = page.locator("a.removeAlternateDetails");
        this.citizenshipUS = page.locator("label[for='borrowerCitizenship_0']");
        this.citizenshipPermResident = page.locator("label[for='borrowerCitizenship_1']");
        this.citizenshipNonPermResident = page.locator("label[for='borrowerCitizenship_3']");
        this.alternateNamesSection = page.locator("#alternateNamesSection");
        this.alternateNamesSectionCount = page.locator("#alternativeClass > label");
        this.alternateNameAddButton = page.locator("#alternameNameAddBtn");
        this.mailingAddressSameasCurrentAddress = page.locator("//*[@id='loanModForm']/div[3]/div/div[2]/div[4]/div[1]/div/div/span/label");
        this.borResidedPresentAddrYes = page.locator("//*[@id='loanModForm']/div[3]/div/div[2]/div[1]/div[27]/div/div/div/label[1]").first();
        this.borResidedPresentAddrNo = page.locator("//*[@id='loanModForm']/div[3]/div/div[2]/div[1]/div[27]/div/div/div/label[2]").first();
        this.presentCounty = page.locator("#presentCounty");
        this.borrowerEmail = page.locator("#borrowerEmail");
        this.cellCarrier = page.locator("#serviceProvider");
        this.isCoBo = page.locator("span > .font-weight-bold > span").first();
        this.borrowerInfoCard = page.locator("//h3[normalize-space()='Borrower Info']");
        this.borrowerInfoFields = page.locator(".card-body.BCICard_body");
        this.borrowerFName = page.locator("#borrowerFName");
        this.borrowerLName = page.locator("#borrowerLName");
        this.fileNameHeader = page.locator("h6");
        this.borrowerMName = page.locator("#borrowerMName");
        this.borrowerSecondaryEmail = page.locator("#borrowerSecondaryEmail");
        this.phoneNumber = page.locator("#phoneNumber").first();
        this.cellNo = page.locator("#cellNo");
        this.borrFax = page.locator("#borrFax");
        this.methodOfContact = page.locator("#methodOfContact_chosen");
        this.allPreferredCommOptions = page.locator("#methodOfContact_chosen .chosen-drop .chosen-results li");
        this.preferredCommOptions = page.locator("#methodOfContact_chosen .search-field input");
        this.altPhoneNumber = page.locator("#altPhoneNumber");
        this.workNumber = page.locator("#workNumber");
        this.listValueOfInputFields = page.locator(".chosen-drop .chosen-results li");
        // Address
        this.presentAddress = page.locator("#presentAddress");
        this.presentUnit = page.locator("#presentUnit");
        this.presentCity = page.locator("#presentCity");
        this.presentState = page.locator("#presentState");
        this.presentZip = page.locator("#presentZip");
        this.presentCountry = page.locator("#presentCountry");
        // Property Info
        this.presentPropLengthTime = page.locator("#presentPropLengthTime");
        this.presentPropLengthMonths = page.locator("#presentPropLengthMonths");
        this.borPresentPropType = page.locator("#borPresentPropType");
        this.currentRPM = page.locator("#currentRPM");
        // Former Address Locators
        this.hasresidedlessthan2 = page.locator("//label[@for='borResidedPresentAddrYes']");
        this.formerAddressSection = page.locator(".borResidedPresentAddrDispOpt");
        this.formerAddress = page.locator("#previousAddress");
        this.formerUnit = page.locator("#previousUnit");
        this.formerCity = page.locator("#previousCity");
        this.formerState = page.locator("#previousState");
        this.formerZip = page.locator("#previousZip");
        this.formerCountry = page.locator("#previousCountry");
        this.formerRentOrOwn = page.locator("#borFormerPropType");
        this.lengthOfTimePrevPropYears = page.locator("#previousPropLengthTime");
        this.lengthOfTimePrevPropMonths = page.locator("#previousPropLengthMonths");
        this.rentPerMonthPrevAddr = page.locator("#previousRPM");
        //Mailing address section
        this.mailingAddressInput = page.locator("#mailingAddress");
        this.mailingUnitInput = page.locator("#mailingUnit");
        this.mailingCityInput = page.locator("#mailingCity");
        this.mailingStateDropdown = page.locator("#mailingState");
        this.mailingZipInput = page.locator("#mailingZip");
        this.mailingCountryDropdown = page.locator("#mailingCountry");
        //Alternate names section
        this.alternateFirstNameInput = page.locator("#alternateFName_1");
        this.alternateMiddleNameInput = page.locator("#alternateMName_1");
        this.alternateLastNameInput = page.locator("#alternateLName_1");
        // Personal Info
        // Date of Birth
        this.borrowerDOBInput = page.locator("#borrowerDOB");
        this.borrowerPOBInput = page.locator("#borrowerPOB");
        this.ssnInput = page.locator("#ssn");
        this.driverLicenseStateDropdown = page.locator("#driverLicenseState");
        this.driverLicenseNumberInput = page.locator("#driverLicenseNumber");
        this.driverLicenseIssuanceDateInput = page.locator("#driverLicenseIssuanceDate");
        this.driverLicenseExpirationDateInput = page.locator("#driverLicenseExpirationDate");
        this.maritalStatusUnmarriedRadio = page.locator("label[for='maritalStatus_1']");
        this.maritalStatusMarriedRadio = page.locator("label[for='maritalStatus_2']");
        this.maritalStatusSeparatedRadio = page.locator("label[for='maritalStatus_3']");
        // Marital Info
        this.marriageDateInput = page.locator("#marriageDate");
        this.divorceDateInput = page.locator("#divorceDate");
        this.maidenNameInput = page.locator("#maidenName");
        this.spouseNameInput = page.locator("#spouseName");
        this.citizenshipForeignNational = page.locator("label[for='borrowerCitizenship_4']");
        // Armed Forces Service
        this.servicingMemberYes = page.locator("label[for='isServicingMember_1']");
        // Dependents
        this.agesOfDependentInput = page.locator("#agesOfDependent");
        this.numberOfDependentsInput = page.locator("#numberOfDependents");
        // Credit Scores
        this.midFicoScoreInput = page.locator("#midFicoScore");
        this.equifaxScoreInput = page.locator("#borEquifaxScore");
        this.transunionScoreInput = page.locator("#borTransunionScore");
        this.experianScoreInput = page.locator("#borExperianScore");
        // Credit Score Range
        this.creditScoreRangeDropdown = page.locator("#borCreditScoreRange");
        // Authorization Status
        this.authorizationStatusSelected = page.locator("#authorizationStatus_chosen span");
    }

    async selectPreferredCommunication() {
        await this.methodOfContact.click();
        const optionCount = await this.allPreferredCommOptions.count();

        for (let i = 0; i < optionCount; i++) {
            const optionText = (
                await this.allPreferredCommOptions.nth(i).innerText()
            ).trim();

            await this.selectFromMultiSelect(
                this.methodOfContact,
                optionText,
                this.allPreferredCommOptions,
                this.preferredCommOptions // search input
            );
        }
    }
    async getFileNameText(): Promise<string> {
        return (await this.fileNameHeader.innerText()).trim();
    }
    async checkBorrowerInfoFieldsVisibility() {
        await expect(this.borrowerInfoFields).toBeVisible();
    }
    async validateBorrowerInfoUpdated(expected: any) {
        this.page.waitForLoadState("networkidle");
        // Borrower Name + Email + Phones
        // await expect(this.borrowerFName).toHaveValue(expected.fileNameFirstName);
        // await expect(this.borrowerLName).toHaveValue(expected.fileNameLastName);
        await expect(this.borrowerMName).toHaveValue(expected.middleName);

        await expect(this.borrowerSecondaryEmail).toHaveValue(
            expected.secondaryEmail
        );
        await expect(this.phoneNumber).toHaveValue(expected.homePhone);
        await expect(this.cellNo).toHaveValue(expected.cellPhone);
        await expect(this.borrFax).toHaveValue(expected.fax);
        await expect(this.altPhoneNumber).toHaveValue(expected.alternatePhone);
        await expect(this.workNumber).toHaveValue(expected.workNumber);

        // Present Address
        await expect(this.presentAddress).toHaveValue(expected.presentAddress);
        await expect(this.presentUnit).toHaveValue(expected.presentUnit);
        await expect(this.presentCity).toHaveValue(expected.presentCity);
        await expect(this.presentState).toHaveValue(expected.presentState);
        await expect(this.presentZip).toHaveValue(expected.presentZip);
        await expect(this.presentCountry).toHaveValue(expected.presentCountry);

        await expect(this.presentPropLengthTime).toHaveValue(
            expected.presentPropLengthYears
        );
        await expect(this.presentPropLengthMonths).toHaveValue(
            expected.presentPropLengthMonths
        );
        await expect(this.borPresentPropType).toHaveValue(expected.rentOrOwn);
        await expect(this.currentRPM).toHaveValue(expected.currentRPM);

        // Former Address (if present)
        if (expected.formerAddress) {
            await expect(this.formerAddress).toHaveValue(expected.formerAddress);
            await expect(this.formerUnit).toHaveValue(expected.formerUnit);
            await expect(this.formerCity).toHaveValue(expected.formerCity);
            await expect(this.formerState).toHaveValue(expected.formerState);
            await expect(this.formerZip).toHaveValue(expected.formerZip);
            await expect(this.formerCountry).toHaveValue(expected.formerCountry);
            await expect(this.formerRentOrOwn).toHaveValue(expected.formerRentOrOwn);
            await expect(this.lengthOfTimePrevPropYears).toHaveValue(
                expected.lengthOfTimeYears
            );
            await expect(this.lengthOfTimePrevPropMonths).toHaveValue(
                expected.lengthOfTimeMonths
            );
            await expect(this.rentPerMonthPrevAddr).toHaveValue(
                expected.rentPerMonth
            );
        }

        // Mailing Address
        await expect(this.mailingAddressInput).toHaveValue(expected.mailingAddress);
        await expect(this.mailingUnitInput).toHaveValue(expected.mailingUnit);
        await expect(this.mailingCityInput).toHaveValue(expected.mailingCity);
        await expect(this.mailingStateDropdown).toHaveValue(expected.mailingState);
        await expect(this.mailingZipInput).toHaveValue(expected.mailingZip);
        await expect(this.mailingCountryDropdown).toHaveValue(
            expected.mailingCountry
        );

        // Alternate Names
        await expect(this.alternateFirstNameInput).toHaveValue(
            expected.alternateFirstName
        );
        await expect(this.alternateMiddleNameInput).toHaveValue(
            expected.alternateMiddleName
        );
        await expect(this.alternateLastNameInput).toHaveValue(
            expected.alternateLastName
        );

        // Personal Info
        await expect(this.borrowerDOBInput).toHaveValue(expected.dob);
        await expect(this.borrowerPOBInput).toHaveValue(expected.placeOfBirth);
        await expect(this.ssnInput).toHaveValue(expected.ssn);

        await expect(this.driverLicenseStateDropdown).toHaveValue(
            expected.driverLicenseState
        );
        await expect(this.driverLicenseNumberInput).toHaveValue(
            expected.driverLicenseNumber
        );
        await expect(this.driverLicenseIssuanceDateInput).toHaveValue(
            expected.driverLicenseIssuanceDate
        );
        await expect(this.driverLicenseExpirationDateInput).toHaveValue(
            expected.driverLicenseExpirationDate
        );

        // Marital Status
        if (expected.maritalStatusMarried)
            await expect(this.maritalStatusMarriedRadio).toBeChecked();
        else if (expected.maritalStatusUnmarried)
            await expect(this.maritalStatusUnmarriedRadio).toBeChecked();
        else if (expected.maritalStatusSeparated)
            await expect(this.maritalStatusSeparatedRadio).toBeChecked();

        if (expected.marriageDate)
            await expect(this.marriageDateInput).toHaveValue(expected.marriageDate);
        if (expected.divorceDate)
            await expect(this.divorceDateInput).toHaveValue(expected.divorceDate);
        if (expected.spouseName)
            await expect(this.spouseNameInput).toHaveValue(expected.spouseName);
        if (expected.maidenName)
            await expect(this.maidenNameInput).toHaveValue(expected.maidenName);

        // Citizenship
        if (expected.citizenshipForeignNational)
            await expect(this.citizenshipForeignNational).toBeChecked();

        // Servicing Member
        if (expected.servicingMemberYes)
            await expect(this.servicingMemberYes).toBeChecked();

        // Dependents
        await expect(this.agesOfDependentInput).toHaveValue(
            expected.agesOfDependent
        );
        await expect(this.numberOfDependentsInput).toHaveValue(
            expected.numberOfDependents
        );

        // Credit Scores
        await expect(this.midFicoScoreInput).toHaveValue(expected.midFicoScore);
        await expect(this.equifaxScoreInput).toHaveValue(expected.equifaxScore);
        await expect(this.transunionScoreInput).toHaveValue(
            expected.transunionScore
        );
        await expect(this.experianScoreInput).toHaveValue(expected.experianScore);

        await expect(this.creditScoreRangeDropdown).toHaveValue(
            expected.creditScoreRange
        );

        // Authorization Status single-select validation
        const lastSelected = expected.authorizationStatuses.at(-1);
        await expect(this.authorizationStatusSelected).toHaveText(lastSelected);
    }
    async checkBorrowerInfoHeadingVisibility() {
        await expect(this.borrowerInfoCard).toBeVisible();
    }
    async fillAndValidate(locator: Locator, value: string) {
        await locator.fill(value);
        await expect(locator).toHaveValue(value);
    }
    async fillAndValidateName(fName: string, lName: string) {
        // Step 1: Clear existing values
        await this.borrowerFName.fill("");
        await this.fillAndValidate(this.borrowerFName, fName);
        await this.borrowerLName.fill("");
        await this.fillAndValidate(this.borrowerLName, lName);
    }
    async clickSaveButton() {
        this.saveButton.click();
    }
    // Validate file name is fetched from Fname and Lname
    async validateFileNameUpdates(newFName: string, newLName: string) {
        const app = new AppManager(this.page);

        // Step 1: Read current fname + lname values
        const currentFName = await this.borrowerFName.inputValue();
        const currentLName = await this.borrowerLName.inputValue();

        // Step 2: Build full name
        const currentFullName = `${currentFName} ${currentLName}`;

        // Step 3: Fetch file name text
        let fileName = await this.getFileNameText();

        // Step 4: Validate
        if (!fileName.includes(currentFullName)) {
            throw new Error(
                `Initial validation failed. Expected file name to contain "${currentFullName}", but got: "${fileName}"`
            );
        }
        await app.utilities.logStep(`File name initially correct: ${fileName}`);

        // Step 5: Clear and update with new values
        await this.borrowerFName.fill("");
        await this.borrowerLName.fill("");
        await this.borrowerFName.fill(newFName);
        await this.borrowerLName.fill(newLName);

        // Step 6: Save and wait for full page reload
        await Promise.all([
            this.page.waitForNavigation({ waitUntil: "networkidle" }),
            this.clickSaveButton(),
        ]);

        // Step 7: Fetch file name again
        fileName = await this.getFileNameText();
        const newFullName = `${newFName} ${newLName}`;

        // Step 8: Validate again
        if (!fileName.includes(newFullName)) {
            throw new Error(
                `Updated validation failed. Expected file name to contain "${newFullName}", but got: "${fileName}"`
            );
        }
        await app.utilities.logStep(`File name updated correctly: ${fileName}`);
    }
    async updateBorrowerInfoWithNewData(newData: any) {
        // Borrower Name + Email + Phones
        await this.fillAndValidateName(
            newData.fileNameFirstName,
            newData.fileNameLastName
        );
        await this.enterBorrowerMiddleName(newData.middleName);
        await this.enterBorrowerSecondaryEmail(newData.secondaryEmail);

        await this.enterHomePhone(newData.homePhone);
        await this.enterCellPhone(newData.cellPhone);
        await this.enterBorrowerFax(newData.fax);

        await this.selectServiceProvider(newData.serviceProvider);

        await this.enterAltPhoneNumber(newData.alternatePhone);
        await this.enterWorkNumber(newData.workNumber);

        // Present Address
        await this.enterPresentAddress(newData.presentAddress);
        await this.enterPresentUnit(newData.presentUnit);
        await this.enterPresentCity(newData.presentCity);
        await this.selectPresentState(newData.presentState);
        await this.enterPresentZip(newData.presentZip);
        // await this.selectPresentCounty(newData.presentCounty);
        await this.selectPresentCountry(newData.presentCountry);

        await this.enterPresentPropLengthTime(newData.presentPropLengthYears);
        await this.enterPresentPropLengthMonths(newData.presentPropLengthMonths);
        await this.selectRentOrOwn(newData.rentOrOwn);
        await this.enterCurrentRPM(newData.currentRPM);



        // Mailing Address
        await this.fillMailingAddress(newData.mailingAddress);
        await this.fillMailingUnit(newData.mailingUnit);
        await this.fillMailingCity(newData.mailingCity);
        await this.selectMailingState(newData.mailingState);
        await this.fillMailingZip(newData.mailingZip);
        await this.selectMailingCountry(newData.mailingCountry);

        // Alternate Names
        await this.fillAlternateFirstName(newData.alternateFirstName);
        await this.fillAlternateMiddleName(newData.alternateMiddleName);
        await this.fillAlternateLastName(newData.alternateLastName);

        // Personal Info
        await this.fillBorrowerDOB(newData.dob);
        await this.fillPlaceOfBirth(newData.placeOfBirth);
        await this.fillSSN(newData.ssn);

        await this.selectDriverLicenseState(newData.driverLicenseState);
        await this.fillDriverLicenseNumber(newData.driverLicenseNumber);
        await this.fillDriverLicenseIssuanceDate(newData.driverLicenseIssuanceDate);
        await this.fillDriverLicenseExpirationDate(
            newData.driverLicenseExpirationDate
        );

        // Marital Status
        if (newData.maritalStatusMarried) await this.selectMaritalStatusMarried();
        else if (newData.maritalStatusUnmarried)
            await this.selectMaritalStatusUnmarried();
        else if (newData.maritalStatusSeparated)
            await this.selectMaritalStatusSeparated();

        if (newData.marriageDate) await this.fillMarriageDate(newData.marriageDate);
        if (newData.divorceDate) await this.fillDivorceDate(newData.divorceDate);
        if (newData.spouseName) await this.fillSpouseName(newData.spouseName);
        if (newData.maidenName) await this.fillMaidenName(newData.maidenName);

        // Citizenship
        // if (newData.citizenshipUS) await this.selectCitizenshipUS();
        if (newData.citizenshipPermResident)
            await this.selectCitizenshipPermResident();
        // if (newData.citizenshipNonPermResident)
        //   await this.selectCitizenshipNonPermResident();
        // if (newData.citizenshipForeignNational)
        //   await this.selectCitizenshipForeignNational();

        // Servicing Member
        await this.selectServicingMemberYes();
        await this.selectServicingMemberNo();
        await this.selectServicingMemberYes();
        await this.fillServiceExpirationDate(newData.serviceExpirationDate);
        await this.serviceExpirationDate.click(); // close datepicker if open

        // Dependents + Credit
        await this.fillAgesOfDependent(newData.agesOfDependent);
        await this.fillNumberOfDependents(newData.numberOfDependents);

        await this.fillMidFicoScore(newData.midFicoScore);
        await this.fillEquifaxScore(newData.equifaxScore);
        await this.fillTransunionScore(newData.transunionScore);
        await this.fillExperianScore(newData.experianScore);

        await this.selectCreditScoreRange(newData.creditScoreRange);

        for (const status of newData.authorizationStatuses) {
            await this.selectAuthorizationStatusByText(status);
        }

        // Save Form
        await this.saveButton.click();
    }
    async isThereCoBorrower() {
    const isChecked = await this.isCoBo.isChecked();
 
    if (isChecked === true) {
      await this.isCoBo.click();
      await this.page.waitForTimeout(300);
      await this.isCoBo.click();
      await this.page.waitForTimeout(300);
      const isChecked = await this.isCoBo.isChecked();
      expect(isChecked, "Co-Borrower switch should be ON").toBe(true);
    } else {
      await this.isCoBo.click();
      await this.page.waitForTimeout(300);
      expect(isChecked, "Co-Borrower switch should be OFF").toBe(false);
    }
  }
    async enterBorrowerMiddleName(mName: string) {
        const app = new AppManager(this.page);
        // await app.utilities.scrollToElement2(this.page, this.borrowerMName);
        await this.fillAndValidate(this.borrowerMName, mName);
    }
    async validateBorrowerEmailIsInactive() {
        await expect(this.borrowerEmail).toHaveAttribute("readonly", "");
    }
    async fillBorrowerEmail(email: string) {
        await this.fillAndValidate(this.borrowerEmail, email);
    }
    async enterBorrowerSecondaryEmail(secEmail: string) {
        await this.fillAndValidate(this.borrowerSecondaryEmail, secEmail);
    }
    async typeAndValidate(locator: Locator, value: string) {
        await locator.type(value);
        await expect(locator).toHaveValue(value, { timeout: 5000 });
    }
    async enterHomePhone(phone: string) {
        await this.phoneNumber.click();
        await this.phoneNumber.fill(""); // clear first
        await this.typeAndValidate(this.phoneNumber, phone);
    }
    async enterCellPhone(cell: string) {
        await this.cellNo.click();
        await this.cellNo.fill(""); // clear first
        await this.typeAndValidate(this.cellNo, cell);
    }
    async enterBorrowerFax(fax: string) {
        await this.borrFax.click();
        await this.borrFax.fill(""); // clear first
        await this.typeAndValidate(this.borrFax, fax);
    }
    async selectServiceProvider(provider: string) {
        const app = new AppManager(this.page);
        //await app.utilities.scrollToElement2(this.page, this.cellCarrier);
        await this.selectFromDropdownByValue(this.cellCarrier, provider, false);
    }
    async clickCoBorrowerButton() {
        // await this.isCoBo.click();
        const switchLocator = this.page.locator("(//label[normalize-space()='Is there a Co-borrower?']/..//div//span)[1]");
        await switchLocator.click();

    }
    async enterAltPhoneNumber(phone: string) {
        await this.altPhoneNumber.fill(""); // clear first
        await this.typeAndValidate(this.altPhoneNumber, phone);
    }
    async enterWorkNumber(workPhone: string) {
        await this.workNumber.fill(""); // clear first
        await this.typeAndValidate(this.workNumber, workPhone);
    }
    async enterPresentAddress(address: string) {
        await this.presentAddress.fill(""); // clear first
        await this.fillAndValidate(this.presentAddress, address);
    }
    async enterPresentUnit(unit: string) {
        await this.presentUnit.fill(""); // clear first
        await this.fillAndValidate(this.presentUnit, unit);
    }
    async enterPresentCity(city: string) {
        await this.presentCity.fill(""); // clear first
        await this.fillAndValidate(this.presentCity, city);
    }
    async selectPresentState(state: string) {
        this.selectFromDropdownByValue(this.presentState, state, false);
        // await this.presentState.selectOption({ label: state });
    }
    async enterPresentZip(zip: string) {
        await this.presentZip.fill(""); // clear first
        await this.fillAndValidate(this.presentZip, zip);
    }
    async selectPresentCounty(county: string) {
        this.selectFromDropdownByLabel(this.presentCounty, county, false);
        // await this.presentCounty.selectOption({ label: county });
    }
    async selectPresentCountry(country: string) {
        this.selectFromDropdownByValue(this.presentCountry, country, false);
        // await this.presentCountry.selectOption({ label: country });
    }
    async enterPresentPropLengthTime(years: string) {
        await this.fillAndValidate(this.presentPropLengthTime, years);
    }
    async enterPresentPropLengthMonths(months: string) {
        await this.fillAndValidate(this.presentPropLengthMonths, months);
    }
    async selectRentOrOwn(propType: string) {
        this.selectFromDropdownByLabel(this.borPresentPropType, propType, false);
        // await this.borPresentPropType.selectOption({ label: propType });
    }
    async enterCurrentRPM(rent: string) {
        await this.fillAndValidate(this.currentRPM, rent);
    }
    async selectBorResidedPresentAddr(
        option: "Yes" | "No",
        extraData?: {
            formerAddress?: string;
            formerUnit?: string;
            formerCity?: string;
            formerState?: string;
            formerZip?: string;
            formerCountry?: string;
            formerRentOrOwn?: string;
            lengthOfTimeYears?: string;
            lengthOfTimeMonths?: string;
            rentPerMonth?: string;
        }
    ): Promise<void> {
        if (option === "Yes") {
            // Click YES
            await this.borResidedPresentAddrYes.click();
            await this.formerAddressSection.waitFor({
                state: "visible",
                timeout: 10000,
            });
            if (extraData?.formerAddress) {
                await this.fillAndValidate(this.formerAddress, extraData.formerAddress);
            }

            if (extraData?.formerUnit) {
                await this.fillAndValidate(this.formerUnit, extraData.formerUnit);
            }

            if (extraData?.formerCity) {
                await this.fillAndValidate(this.formerCity, extraData.formerCity);
            }
            if (extraData?.formerState) {
                this.selectFromDropdownByValue(
                    this.formerState,
                    extraData?.formerState,
                    false
                );
                // await this.formerState.selectOption(extraData.formerState);
            }
            if (extraData?.formerZip) {
                await this.formerZip.fill(""); // clear first
                await this.fillAndValidate(this.formerZip, extraData.formerZip);
            }
            if (extraData?.formerCountry) {
                this.selectFromDropdownByValue(
                    this.formerCountry,
                    extraData?.formerCountry,
                    false
                );
                // await this.formerCountry.selectOption(extraData.formerCountry);
            }
            if (extraData?.lengthOfTimeYears) {
                await this.fillAndValidate(
                    this.lengthOfTimePrevPropYears,
                    extraData.lengthOfTimeYears
                );
            }

            if (extraData?.lengthOfTimeMonths) {
                await this.fillAndValidate(
                    this.lengthOfTimePrevPropMonths,
                    extraData.lengthOfTimeMonths
                );
            }
            if (extraData?.formerRentOrOwn) {
                this.selectFromDropdownByLabel(
                    this.formerRentOrOwn,
                    extraData?.formerRentOrOwn,
                    false
                );
                // await this.formerRentOrOwn.selectOption(extraData.formerRentOrOwn);
            }
            if (extraData?.rentPerMonth) {
                await this.fillAndValidate(
                    this.rentPerMonthPrevAddr,
                    extraData.rentPerMonth
                );
            }
        } else {
            await this.borResidedPresentAddrNo.click();
        }
    }
    async selectFromDropdownByValue(
        dropdownElement: any,
        optionValue: string,
        clearFirst: boolean = false
    ) {
        await expect(dropdownElement).toBeVisible();

        if (clearFirst) {
            await dropdownElement.selectOption("");
        }

        await dropdownElement.selectOption({ value: optionValue });

        const actualValue = await dropdownElement.inputValue();
        const actualText = (
            await dropdownElement.locator("option:checked").innerText()
        ).trim();

        // Assert by value
        await expect(actualValue).toBe(optionValue);

        // If you also want to assert by text, uncomment:
        // await expect(actualText).toBe(optionValue);

        await this.page.waitForTimeout(2000); // optional, can be removed
    }
    async clickMailingAddressSameasCurrent() {
        await this.page.waitForTimeout(1000);
        await this.mailingAddressSameasCurrentAddress.click();
    }
    async fillMailingAddress(address: string) {
        await this.fillAndValidate(this.mailingAddressInput, address);
    }
    async fillMailingUnit(unit: string) {
        await this.fillAndValidate(this.mailingUnitInput, unit);
    }
    async fillMailingCity(city: string) {
        await this.fillAndValidate(this.mailingCityInput, city);
    }
    async selectMailingState(stateCode: string) {
        this.selectFromDropdownByValue(this.mailingStateDropdown, stateCode, false);
        // await this.mailingStateDropdown.selectOption(stateCode);
    }
    async fillMailingZip(zip: string) {
        await this.mailingZipInput.fill(""); // clear first
        await this.fillAndValidate(this.mailingZipInput, zip);
    }
    async selectMailingCountry(countryCode: string) {
        this.selectFromDropdownByValue(
            this.mailingCountryDropdown,
            countryCode,
            false
        );
        // await this.mailingCountryDropdown.selectOption(countryCode);
    }
    async checkMailingAddressInputVisible() {
        await expect(this.mailingAddressInput).toBeVisible();
    }
    async verifyMailingAddressSameAsCurrent(
        expectedAddress: string,
        expectedUnit: string,
        expectedCity: string,
        expectedStateCode: string,
        expectedZip: string,
        expectedCountryCode: string
    ) {
        await this.page.waitForTimeout(5000);
        await expect(this.mailingAddressInput).toHaveValue(expectedAddress);
        await expect(this.mailingUnitInput).toHaveValue(expectedUnit);
        await expect(this.mailingCityInput).toHaveValue(expectedCity);
        await expect(this.mailingStateDropdown).toHaveValue(expectedStateCode);
        await expect(this.mailingZipInput).toHaveValue(expectedZip);
        await expect(this.mailingCountryDropdown).toHaveValue(expectedCountryCode);
    }
    async checkAlternateNamesSectionVisible() {
        await expect(this.alternateNamesSection).toBeVisible();
    }
    async checkAlternateNamesSectionCountVisible() {
        await expect(this.alternateNamesSectionCount).toBeVisible();
    }
    async fillAlternateFirstName(name: string) {
        await this.fillAndValidate(this.alternateFirstNameInput, name);
    }
    async fillAlternateMiddleName(name: string) {
        await this.fillAndValidate(this.alternateMiddleNameInput, name);
    }
    async fillAlternateLastName(name: string) {
        await this.fillAndValidate(this.alternateLastNameInput, name);
    }
    async clickOnAddAlternateNameButton() {
        await this.alternateNameAddButton.click();
    }
    async removeAlternateSection2AndVerify() {
        await expect(this.alternateSection2).toBeVisible();
        await this.alternateSectionRemoveButton.click();
        await expect(this.alternateSection2).not.toBeVisible();
    }
    async fillBorrowerDOB(date: string) {
        await this.fillAndValidate(this.borrowerDOBInput, date);
    }
    async fillPlaceOfBirth(place: string) {
        await this.borrowerPOBInput.click();
        await this.fillAndValidate(this.borrowerPOBInput, place);
    }
    async fillSSN(ssn: string) {
        await this.fillAndValidate(this.ssnInput, ssn);
    }
    async selectDriverLicenseState(state: string) {
        this.selectFromDropdownByValue(
            this.driverLicenseStateDropdown,
            state,
            false
        );
        // await this.driverLicenseStateDropdown.selectOption(state);
    }
    async fillDriverLicenseNumber(license: string) {
        await this.fillAndValidate(this.driverLicenseNumberInput, license);
    }
    async fillDriverLicenseIssuanceDate(date: string) {
        await this.fillAndValidate(this.driverLicenseIssuanceDateInput, date);
        await this.driverLicenseNumberInput.click();
    }
    async fillDriverLicenseExpirationDate(date: string) {
        await this.fillAndValidate(this.driverLicenseExpirationDateInput, date);
        await this.driverLicenseNumberInput.click();
    }
    async selectAndValidateRadio(locator: Locator) {
        await locator.click();
        await expect(locator).toBeChecked();
    }
    async selectMaritalStatusUnmarried() {
        await this.selectAndValidateRadio(this.maritalStatusUnmarriedRadio);
    }
    async selectMaritalStatusMarried() {
        await this.selectAndValidateRadio(this.maritalStatusMarriedRadio);
    }
    async selectMaritalStatusSeparated() {
        await this.selectAndValidateRadio(this.maritalStatusSeparatedRadio);
    }
    async fillMarriageDate(date: string) {
        await this.fillAndValidate(this.marriageDateInput, date);
        await this.dateOfDivorce.click();
    }
    async fillDivorceDate(date: string) {
        await this.fillAndValidate(this.divorceDateInput, date);
        await this.dateOfDivorce.click();
    }
    async fillMaidenName(name: string) {
        await this.maidenNameInput.click();
        await this.fillAndValidate(this.maidenNameInput, name);
    }
    async fillSpouseName(name: string) {
        await this.fillAndValidate(this.spouseNameInput, name);
    }
    async clickdateOfDivorce() {
        await this.dateOfDivorce.click();
    }
    async selectCitizenshipUS() {
        await this.selectAndValidateRadio(this.citizenshipUS);
    }
    async selectCitizenshipPermResident() {
        await this.selectAndValidateRadio(this.citizenshipPermResident);
    }
    async selectCitizenshipNonPermResident() {
        await this.selectAndValidateRadio(this.citizenshipNonPermResident);
    }
    async selectCitizenshipForeignNational() {
        await this.selectAndValidateRadio(this.citizenshipForeignNational);
    }
    async selectServicingMemberYes() {
        await this.selectAndValidateRadio(this.servicingMemberYes);
    }
    async selectServicingMemberNo() {
        await this.selectAndValidateRadio(this.servicingMemberNo);
    }
    async selectAllServicingMemberInfoOptions() {
        const optionCount = await this.servicingMemberInfoOptions.count();

        for (let i = 0; i < optionCount; i++) {
            const optionText = (
                await this.servicingMemberInfoOptions.nth(i).innerText()
            ).trim();

            // 🔑 reopen the dropdown for each selection
            await this.selectFromMultiSelect(
                this.servicingMemberInfoDropdown,
                optionText,
                this.servicingMemberInfoOptions,
                this.servicingMemberInfoSearchInput // if you have a <input> inside chosen
            );

            console.log(`Selected multi-option: ${optionText}`);
            await this.page.waitForTimeout(300);
        }
    }
    async selectFromMultiSelect(
        dropdownElement: any,
        optionText: string,
        customListLocator?: any,
        searchInput?: any
    ) {
        await expect(dropdownElement).toBeVisible({ timeout: 5000 });
        await dropdownElement.click();

        const listLocator =
            customListLocator || this.page.locator("ul.chosen-results li");

        if (searchInput) {
            await searchInput.fill("");
            await searchInput.fill(optionText);

            const match = listLocator.filter({ hasText: optionText }).first();
            await match.waitFor({ state: "visible", timeout: 5000 });
            await searchInput.press("Enter");
        } else {
            const match = listLocator.filter({ hasText: optionText }).first();
            await match.waitFor({ state: "visible", timeout: 5000 });
            await match.click();
        }

        // ✅ Robust validation: check the chip/tag inside .search-choice
        const selectedChip = dropdownElement.locator(".search-choice span", {
            hasText: optionText,
        });
        await expect(selectedChip).toBeVisible({ timeout: 10000 });
    }
    async selectServicingMemberInfoByText(optionText: string) {
        await this.selectFromMultiSelect(
            this.servicingMemberInfoDropdown,
            optionText,
            this.servicingMemberInfoOptions,
            this.servicingMemberInfoSearchInput
        );
    }
    async clickOnserviceExpirationDate() {
        await this.serviceExpirationDate.click();
    }
    async fillServiceExpirationDate(date: string) {
        await this.fillAndValidate(this.serviceExpirationDateInput, date);
    }
    async fillAgesOfDependent(age: string) {
        await this.fillAndValidate(this.agesOfDependentInput, age);
    }
    async fillNumberOfDependents(num: string) {
        await this.fillAndValidate(this.numberOfDependentsInput, num);
    }
    async fillMidFicoScore(score: string) {
        await this.fillAndValidate(this.midFicoScoreInput, score);
    }
    async fillEquifaxScore(score: string) {
        await this.fillAndValidate(this.equifaxScoreInput, score);
    }
    async fillTransunionScore(score: string) {
        await this.fillAndValidate(this.transunionScoreInput, score);
    }
    async selectFromDropdownByLabel(
        dropdownElement: any,
        optionLabel: string,
        clearFirst: boolean = false
    ) {
        await expect(dropdownElement).toBeVisible();

        if (clearFirst) {
            await dropdownElement.selectOption("");
        }

        await dropdownElement.selectOption({ label: optionLabel });

        const actualValue = await dropdownElement.inputValue();
        const actualText = (
            await dropdownElement.locator("option:checked").innerText()
        ).trim();

        await expect(actualText).toBe(optionLabel);

        await this.page.waitForTimeout(2000);
    }
    async fillExperianScore(score: string) {
        await this.fillAndValidate(this.experianScoreInput, score);
    }
    async selectCreditScoreRange(optionIndex: string) {
        this.selectFromDropdownByLabel(
            this.creditScoreRangeDropdown,
            optionIndex,
            false
        );
        // await this.creditScoreRangeDropdown.selectOption({ index: optionIndex });
    }
    async selectFromSingleSelect(
        dropdownElement: any,
        optionText: string,
        customListLocator?: any
    ) {
        await expect(dropdownElement).toBeVisible({ timeout: 5000 });
        await dropdownElement.click();

        const listLocator =
            customListLocator || this.page.locator("ul.chosen-results li");

        const match = listLocator.filter({ hasText: optionText }).first();
        await match.waitFor({ state: "visible", timeout: 5000 });
        await match.click();

        // validate selection
        const displayTextLocator = dropdownElement.locator("span");
        await expect(displayTextLocator).toHaveText(optionText, { timeout: 10000 });
    }
    async selectAuthorizationStatusByText(status: string) {
        await this.selectFromSingleSelect(
            this.authorizationStatusDropdown,
            status,
            this.authorizationStatusOptions
        );
    }
    // Pick all available statuses (loop)
    async selectAllAuthorizationStatuses() {
        const optionCount = await this.authorizationStatusOptions.count();

        for (let i = 0; i < optionCount; i++) {
            const optionText = (
                await this.authorizationStatusOptions.nth(i).innerText()
            ).trim();
            await this.selectAuthorizationStatusByText(optionText);
            await this.page.waitForTimeout(300); // pause for UI update
        }
    }
    async clickOnCreateLoanMenu() {
        await this.createLoanMenu.click();
    }
    async clickOnQuickAppMenu() {
        await this.quickAppMenu.click();
        await this.page.waitForTimeout(4000);
    }
    normalizePhone(value: string | undefined | null): string {
        if (!value) return ""; // handle null/undefined safely
        return value.replace(/\D/g, ""); // keep only digits
    }
    async expectPhone(locator: Locator, expected: string) {
        const actual = await locator.inputValue();
        expect(this.normalizePhone(actual)).toBe(this.normalizePhone(expected));
    }
    async validateAllBorrowerInfoFields(expectedData: any) {
        // this.page.waitForLoadState("networkidle");

        // await this.page.waitForTimeout(5000);
        // Remove comment when we create a new loan file
        // // Names
        // await expect(this.borrowerFName).toHaveValue(
        //   expectedData.fileNameFirstName
        // );
        // await expect(this.borrowerLName).toHaveValue(expectedData.fileNameLastName);
        await expect(this.borrowerMName).toHaveValue(expectedData.middleName);
        // Emails
        // await expect(this.borrowerEmail).toHaveValue(expectedData.primaryEmail);
        await expect(this.borrowerSecondaryEmail).toHaveValue(
            expectedData.secondaryEmail
        );

        // Phones
        await this.expectPhone(this.phoneNumber, expectedData.homePhone);
        await this.expectPhone(this.cellNo, expectedData.cellPhone);
        await this.expectPhone(this.borrFax, expectedData.fax);
        await this.expectPhone(this.altPhoneNumber, expectedData.alternatePhone);
        await this.expectPhone(this.workNumber, expectedData.workNumber);

        // Service Provider and Preferred Communication (assuming multi-select chips)
        // await expect(this.cellCarrier).toHaveValue(expectedData.serviceProvider);

        // For preferred communication, you can extend to check selected options as needed

        // Address
        await expect(this.presentAddress).toHaveValue(expectedData.presentAddress);
        await expect(this.presentUnit).toHaveValue(expectedData.presentUnit);
        await expect(this.presentCity).toHaveValue(expectedData.presentCity);
        await expect(this.presentState).toHaveValue(expectedData.presentState);
        await expect(this.presentZip).toHaveValue(expectedData.presentZip);
        await expect(this.presentCounty).toHaveValue(expectedData.presentCounty);
        await expect(this.presentCountry).toHaveValue(expectedData.presentCountry);

        // Property Info
        await expect(this.presentPropLengthTime).toHaveValue(
            expectedData.presentPropLengthYears
        );
        await expect(this.presentPropLengthMonths).toHaveValue(
            expectedData.presentPropLengthMonths
        );
        await expect(this.borPresentPropType).toHaveValue(expectedData.rentOrOwn);
        await expect(this.currentRPM).toHaveValue(expectedData.currentRPM);

        // Former Address (optional)
        if (expectedData.formerAddress) {
            await expect(this.formerAddress).toHaveValue(expectedData.formerAddress);
            await expect(this.formerUnit).toHaveValue(expectedData.formerUnit);
            await expect(this.formerCity).toHaveValue(expectedData.formerCity);
            await expect(this.formerState).toHaveValue(expectedData.formerState);
            await expect(this.formerZip).toHaveValue(expectedData.formerZip);
            await expect(this.formerCountry).toHaveValue(expectedData.formerCountry);
            await expect(this.formerRentOrOwn).toHaveValue(
                expectedData.formerRentOrOwn
            );
            await expect(this.lengthOfTimePrevPropYears).toHaveValue(
                expectedData.lengthOfTimeYears
            );
            await expect(this.lengthOfTimePrevPropMonths).toHaveValue(
                expectedData.lengthOfTimeMonths
            );
            await expect(this.rentPerMonthPrevAddr).toHaveValue(
                expectedData.rentPerMonth
            );
        }

        // Mailing Address
        await expect(this.mailingAddressInput).toHaveValue(
            expectedData.mailingAddress
        );
        await expect(this.mailingUnitInput).toHaveValue(expectedData.mailingUnit);
        await expect(this.mailingCityInput).toHaveValue(expectedData.mailingCity);
        await expect(this.mailingStateDropdown).toHaveValue(
            expectedData.mailingState
        );
        await expect(this.mailingZipInput).toHaveValue(expectedData.mailingZip);
        await expect(this.mailingCountryDropdown).toHaveValue(
            expectedData.mailingCountry
        );

        // Alternate Names
        await expect(this.alternateFirstNameInput).toHaveValue(
            expectedData.alternateFirstName
        );
        await expect(this.alternateMiddleNameInput).toHaveValue(
            expectedData.alternateMiddleName
        );
        await expect(this.alternateLastNameInput).toHaveValue(
            expectedData.alternateLastName
        );

        // Personal Info
        await expect(this.borrowerDOBInput).toHaveValue(expectedData.dob);
        await expect(this.borrowerPOBInput).toHaveValue(expectedData.placeOfBirth);
        await expect(this.ssnInput).toHaveValue(expectedData.ssn);
        await expect(this.driverLicenseStateDropdown).toHaveValue(
            expectedData.driverLicenseState
        );
        await expect(this.driverLicenseNumberInput).toHaveValue(
            expectedData.driverLicenseNumber
        );
        await expect(this.driverLicenseIssuanceDateInput).toHaveValue(
            expectedData.driverLicenseIssuanceDate
        );
        await expect(this.driverLicenseExpirationDateInput).toHaveValue(
            expectedData.driverLicenseExpirationDate
        );

        // Marital Status radios - check selected based on booleans
        if (expectedData.maritalStatusSeparated) {
            await expect(this.maritalStatusSeparatedRadio).toBeChecked();
            await expect(this.marriageDateInput).toHaveValue(
                expectedData.marriageDate
            );
            await expect(this.divorceDateInput).toHaveValue(expectedData.divorceDate);
            await expect(this.spouseNameInput).toHaveValue(expectedData.spouseName);
            await expect(this.maidenNameInput).toHaveValue(expectedData.maidenName);
        }

        if (expectedData.citizenshipForeignNational) {
            await expect(this.citizenshipForeignNational).toBeChecked();
        }

        // Armed Forces Service radios and multi-selects
        // if (expectedData.servicingMemberYes) {
        //   await expect(this.servicingMemberYes).toBeChecked();
        // }

        // const chips = await this.page
        //   .locator(
        //     "#servicingMemberInfo_chosen .chosen-choices li.search-choice span"
        //   )
        //   .allTextContents();

        // for (const option of expectedData.servicingMemberInfoOptions || []) {
        //   expect(chips).toContain(option);
        // }

        // Dependents
        await expect(this.agesOfDependentInput).toHaveValue(
            expectedData.agesOfDependent
        );
        await expect(this.numberOfDependentsInput).toHaveValue(
            expectedData.numberOfDependents
        );

        // Credit Scores
        await expect(this.midFicoScoreInput).toHaveValue(expectedData.midFicoScore);
        await expect(this.equifaxScoreInput).toHaveValue(expectedData.equifaxScore);
        await expect(this.transunionScoreInput).toHaveValue(
            expectedData.transunionScore
        );
        await expect(this.experianScoreInput).toHaveValue(
            expectedData.experianScore
        );

        // Credit Score Range dropdown
        await expect(this.creditScoreRangeDropdown).toHaveValue(
            expectedData.creditScoreRange
        );

        // Authorization Status single-select validation
        const lastSelected = expectedData.authorizationStatuses.at(-1); // "Have 2 & Need 2"

        await expect(this.authorizationStatusSelected).toHaveText(lastSelected);
    }
    async clearAllBorrowerFields() {
        const app = new AppManager(this.page);
        app.utilities.logStep("Clear Personal Info");
        await this.borrowerMName.fill("");
        app.utilities.logStep("Clear Emails");
        await this.borrowerSecondaryEmail.fill("");
        app.utilities.logStep("Clear Phones");
        await this.phoneNumber.fill("");
        await this.cellNo.fill("");
        await this.borrFax.fill("");
        await this.altPhoneNumber.fill("");
        await this.workNumber.fill("");
        app.utilities.logStep("Clear Dropdowns / selects (choose empty option)");
        await this.cellCarrier.selectOption("");
        // Need to fix later the dropdown clearing
        // Preferred Communication assumed multi-select chosen - deselect all selections
        // const preferredCommSelected = this.page.locator(
        //   "#methodOfContact_chosen .chosen-choices li.search-choice span"
        // );
        // const prefCommCount = await preferredCommSelected.count();
        // for (let i = 0; i < prefCommCount; i++) {
        //   await preferredCommSelected.nth(i).click();
        // }
        app.utilities.logStep("Clear Address");
        await this.presentAddress.fill("");
        await this.presentUnit.fill("");
        await this.presentCity.fill("");
        await this.presentState.selectOption("");
        await this.presentZip.fill("");
        await this.presentCounty.selectOption("");
        await this.presentCountry.selectOption("");
        app.utilities.logStep("Clear Property Info");
        await this.presentPropLengthTime.fill("");
        await this.presentPropLengthMonths.fill("");
        await this.borPresentPropType.selectOption("");
        await this.currentRPM.fill("");
        app.utilities.logStep("Clear Former Address");
        if(await this.hasresidedlessthan2.isChecked()){
        await this.formerAddress.fill("");
        await this.formerUnit.fill("");
        await this.formerCity.fill("");
        await this.formerState.selectOption("");
        await this.formerZip.fill("");
        await this.formerCountry.selectOption("");
        await this.formerRentOrOwn.selectOption("");
        await this.lengthOfTimePrevPropYears.fill("");
        await this.lengthOfTimePrevPropMonths.fill("");
        await this.rentPerMonthPrevAddr.fill("");
        }
        else{
        await this.hasresidedlessthan2.click();
        await this.formerAddress.fill("");
        await this.formerUnit.fill("");
        await this.formerCity.fill("");
        await this.formerState.selectOption("");
        await this.formerZip.fill("");
        await this.formerCountry.selectOption("");
        await this.formerRentOrOwn.selectOption("");
        await this.lengthOfTimePrevPropYears.fill("");
        await this.lengthOfTimePrevPropMonths.fill("");
        await this.rentPerMonthPrevAddr.fill("");
        }
        app.utilities.logStep("Clear Mailing Address");
        await this.mailingAddressInput.fill("");
        await this.mailingUnitInput.fill("");
        await this.mailingCityInput.fill("");
        await this.mailingStateDropdown.selectOption("");
        await this.mailingZipInput.fill("");
        await this.mailingCountryDropdown.selectOption("");
        app.utilities.logStep("Clear Alternate Names");
        await this.alternateFirstNameInput.fill("");
        await this.alternateMiddleNameInput.fill("");
        await this.alternateLastNameInput.fill("");
        app.utilities.logStep("Clear Personal Info (DOB, POB, SSN, DL)");
        await this.borrowerDOBInput.fill("");
        await this.borrowerDOBInput.click(); // close datepicker if open
        await this.borrowerPOBInput.fill("");
        await this.borrowerPOBInput.click(); // close any suggestions
        await this.ssnInput.fill("");
        await this.driverLicenseStateDropdown.selectOption("");
        await this.driverLicenseNumberInput.fill("");
        await this.driverLicenseIssuanceDateInput.fill("");
        await this.driverLicenseExpirationDateInput.fill("");
        // Marital Status radios - cannot deselect, select none
        // If no neutral radio, skip or set to some default unchecked state if possible
        app.utilities.logStep("Clear Marital Inputs clear");
        await this.marriageDateInput.fill("");
        await this.dateOfDivorce.click(); // close datepicker if open
        await this.spouseNameInput.fill("");
        await this.divorceDateInput.fill("");
        await this.dateOfDivorce.click(); // close datepicker if open
        await this.maidenNameInput.fill("");
        // Clear servicing member multi-select chips (chosen)
        // Pending fix
        // await this.clearAllFourSelections();// Pending fix
        // await this.serviceExpirationDateInput.fill("");
        await this.driverLicenseNumberInput.click(); // close datepicker if open
        app.utilities.logStep("Clear Dependents");
        await this.agesOfDependentInput.fill("");
        await this.numberOfDependentsInput.fill("");
        await app.utilities.scrollToElement2(this.page, this.midFicoScoreInput)
        app.utilities.logStep("Clear Credit Scores");
        await this.midFicoScoreInput.fill("");
        await this.equifaxScoreInput.fill("");
        await this.transunionScoreInput.fill("");
        await this.experianScoreInput.fill("");
        await this.creditScoreRangeDropdown.selectOption("");
        // await this.clearAuthorizationStatus();               [this is pending to fix]
    }
    async clearAuthorizationStatus() {
        const clearButton = this.page.locator("#authorizationStatus_chosen > a > abbr");
        await clearButton.click(); // Click the 'x' icon to clear selection
    }
    async clearAllFourSelections() {
        const closeBtns = this.page.locator(
            "#servicingMemberInfo_chosen .search-choice-close"
        );

        while (true) {
            const countBefore = await closeBtns.count();
            if (countBefore === 0) break; // ✅ all cleared

            // Ensure the first button is visible before trying
            await expect(closeBtns.first()).toBeVisible({ timeout: 2000 });

            // Try to remove the first chip
            await closeBtns.first().click({ force: true });

            // Wait for it to disappear and count to update
            await expect(closeBtns).toHaveCount(countBefore - 1, { timeout: 3000 });

            // Double-check that count actually decreased
            const countAfter = await closeBtns.count();
            if (countAfter >= countBefore) {
                throw new Error(
                    `❌ Tried to remove a selection, but it stayed at ${countBefore}`
                );
            }
        }

        // ✅ Final safeguard (should be 0 here anyway)
        const remaining = await closeBtns.count();
        if (remaining > 0) {
            throw new Error(
                `❌ Expected to clear all selections, but ${remaining} remain`
            );
        }
    }
    // borrower info section validation 
    async validateAllBorrowerFieldsCleared() {
        await this.page.waitForTimeout(15000);
        // Personal Info
        // await expect(this.borrowerLName).toHaveValue("");
        await expect(this.borrowerMName).toHaveValue("");

        // Emails
        await expect(this.borrowerSecondaryEmail).toHaveValue("");

        // Phones
        await expect(this.phoneNumber).toHaveValue("");
        await expect(this.cellNo).toHaveValue("");
        await expect(this.borrFax).toHaveValue("");
        await expect(this.altPhoneNumber).toHaveValue("");
        await expect(this.workNumber).toHaveValue("");

        // Dropdowns
        await expect(this.cellCarrier).toHaveValue("");

        // Address
        await expect(this.presentAddress).toHaveValue("");
        await expect(this.presentUnit).toHaveValue("");
        await expect(this.presentCity).toHaveValue("");
        await expect(this.presentState).toHaveValue("");
        await expect(this.presentZip).toHaveValue("");
        await expect(this.presentCounty).toHaveValue("");
        await expect(this.presentCountry).toHaveValue("");

        // Property Info
        await expect(this.presentPropLengthTime).toHaveValue("");
        await expect(this.presentPropLengthMonths).toHaveValue("0");
        await expect(this.borPresentPropType).toHaveValue("");
        await expect(this.currentRPM).toHaveValue("");

        // Former Address
        await expect(this.formerAddress).toHaveValue("");
        await expect(this.formerUnit).toHaveValue("");
        await expect(this.formerCity).toHaveValue("");
        await expect(this.formerState).toHaveValue("");
        await expect(this.formerZip).toHaveValue("");
        await expect(this.formerCountry).toHaveValue("");
        await expect(this.formerRentOrOwn).toHaveValue("");
        await expect(this.lengthOfTimePrevPropYears).toHaveValue("");
        await expect(this.lengthOfTimePrevPropMonths).toHaveValue("0");
        await expect(this.rentPerMonthPrevAddr).toHaveValue("");

        // Mailing Address
        await expect(this.mailingAddressInput).toHaveValue("");
        await expect(this.mailingUnitInput).toHaveValue("");
        await expect(this.mailingCityInput).toHaveValue("");
        await expect(this.mailingStateDropdown).toHaveValue("");
        await expect(this.mailingZipInput).toHaveValue("");
        await expect(this.mailingCountryDropdown).toHaveValue("");

        // Alternate Names
        // await expect(this.alternateFirstNameInput).toHaveValue("");
        // await expect(this.alternateMiddleNameInput).toHaveValue("");
        // await expect(this.alternateLastNameInput).toHaveValue("");

        // Personal Info (DOB, POB, SSN, DL)
        await expect(this.borrowerDOBInput).toHaveValue("");
        await expect(this.borrowerPOBInput).toHaveValue("");
        await expect(this.ssnInput).toHaveValue("");
        await expect(this.driverLicenseStateDropdown).toHaveValue("");
        await expect(this.driverLicenseNumberInput).toHaveValue("");
        await expect(this.driverLicenseIssuanceDateInput).toHaveValue("");
        await expect(this.driverLicenseExpirationDateInput).toHaveValue("");

        // Marital Inputs
        await expect(this.marriageDateInput).toHaveValue("");
        await expect(this.spouseNameInput).toHaveValue("");
        await expect(this.divorceDateInput).toHaveValue("");
        await expect(this.maidenNameInput).toHaveValue("");

        // Service / Dependents
        // await expect(this.serviceExpirationDateInput).toHaveValue("");
        await expect(this.agesOfDependentInput).toHaveValue("0");
        await expect(this.numberOfDependentsInput).toHaveValue("");

        // Credit Scores
        //await expect(this.midFicoScoreInput).toHaveValue("");
        await expect(this.equifaxScoreInput).toHaveValue("");
        await expect(this.transunionScoreInput).toHaveValue("");
        await expect(this.experianScoreInput).toHaveValue("");
        await expect(this.creditScoreRangeDropdown).toHaveValue("");
        // await expect(this.authorizationStatusDropdown).toHaveText("Please Select Authorization Status"); [this need to be fix]
    }
    async updateAndValidateField(
        type: "text" | "select" | "selectByValue" | "checkbox" | "multiSelect",
        locator: Locator,
        value: string | string[] | boolean
    ) {
        if (type === "text") {
            await locator.fill("");
            await locator.fill(value as string);
            await expect(locator).toHaveValue(value as string);
        } else if (type === "select") {
            // Select using visible label
            await locator.selectOption({ label: value as string });
            await expect(locator).toHaveValue(/.+/); // at least some value got picked
        } else if (type === "selectByValue") {
            // Select using option value
            await locator.selectOption({ value: value as string });
            await expect(locator).toHaveValue(value as string);
        } else if (type === "checkbox") {
            if (value) {
                await locator.check();
                await expect(locator).toBeChecked();
            } else {
                await locator.uncheck();
                await expect(locator).not.toBeChecked();
            }
        } else if (type === "multiSelect") {
            for (const option of value as string[]) {
                await locator.selectOption(option);
                await expect(locator).toContainText(option);
            }
        }
    }
}
