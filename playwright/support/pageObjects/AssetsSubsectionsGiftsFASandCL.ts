import { Page, Locator, expect } from "@playwright/test";
import { AppManager } from "../AppManager/PageObjectManager";

export class AssetsSubsectionsGiftsFASandCL {
  private page: Page;
  // --- GOG (Gifts or Grants) ---
  private ContigentLiabitiesSection: Locator;
  private assetTypeSelect: Locator;
  private assetTypeSelectInput: Locator;
  private giftsAndGrandsDropArrow: Locator;
  private giftsAndGrandsDropArrow1: Locator;

  private depositedYes: Locator;
  private depositedNo: Locator;
  private depositedNA: Locator;

  private depositedYesLabel: Locator;
  private depositedNoLabel: Locator;
  private depositedNALabel: Locator;
  private assetSourceMultiS: Locator;
  private assetSourcePrimaryOption: Locator;
  private assetSourceOptions: Locator;
  private depositedCard: Locator;
  private cashOrMarketValue: Locator;
  private addGiftsAndGrants: Locator;
  private assetTypeSelect1: Locator;
  private assetTypeSelectInput1: Locator;

  private depositedYes1: Locator;
  private depositedNo1: Locator;
  private depositedNA1: Locator;

  private depositedYesLabel1: Locator;
  private depositedNoLabel1: Locator;
  private depositedNALabel1: Locator;

  private assetSourceMultiS1: Locator;
  private assetSourcePrimaryOption1: Locator;
  private assetSourceOptions1: Locator;

  private cashOrMarketValue1: Locator;
  private removeGiftsAndGrants: Locator;

  // --- FAS (Financial Accounts & Securities) ---
  private finAccSecuDropArrow: Locator;
  private finAccSecuDropArrow1: Locator;
  private fasSection: Locator;
  private totalVerifiedAssets: Locator;
  private accountTypeSelect: Locator;
  private accountTypeInput: Locator;
  private nameofInstitution: Locator;
  private shareValueInBond: Locator;
  private nameofSecurity: Locator;
  private cost: Locator;
  private marketValueQuote: Locator;
  private dateofQuote: Locator;
  private accountTitledAs: Locator;
  private account: Locator;
  private balanceValue: Locator;
  private owners: Locator;
  private description: Locator;
  private statementDate: Locator;
  private pledgedYes: Locator;
  private pledgedNo: Locator;
  private pledgedY: Locator;
  private pledgedN: Locator;
  private accountTypeSelect1: Locator;
  private accountTypeInput1: Locator;
  private nameofInstitution1: Locator;
  private shareValueInBond1: Locator;
  private nameofSecurity1: Locator;
  private cost1: Locator;
  private marketValueQuote1: Locator;
  private dateofQuote1: Locator;
  private accountTitledAs1: Locator;
  private account1: Locator;
  private balanceValue1: Locator;
  private owners1: Locator;
  private description1: Locator;
  private statementDate1: Locator;
  private pledgedYes1: Locator;
  private pledgedNo1: Locator;
  private pledgedY1: Locator;
  private pledgedN1: Locator;
  private addFSButton: Locator;
  private removeFSButton: Locator;

  // --- CL (Contingent Liabilities) ---
  private contingentCardArrow: Locator;
  private typeOfLiabilitySelect: Locator;
  private typeOfLiabilityInput: Locator;
  private typeOfLiabilityOptions: Locator;
  private typeOfLiabilitySearch: Locator;
  private nameOfCompany: Locator;
  private CLmonthlyPayment: Locator;
  private monthsLeftToPay: Locator;
  private clBalance: Locator;
  private clAccount: Locator;
  private clTaxLiability: Locator;
  private clDescription: Locator;
  private addContLiabilites: Locator;
  private typeOfLiabilitySelect1: Locator;
  private typeOfLiabilityInput1: Locator;
  private typeOfLiabilityOptions1: Locator;
  private typeOfLiabilitySearch1: Locator;
  private nameOfCompany1: Locator;
  private CLmonthlyPayment1: Locator;
  private monthsLeftToPay1: Locator;
  private clBalance1: Locator;
  private clAccount1: Locator;
  private clTaxLiability1: Locator;
  private clDescription1: Locator;
  private removeContLiabilites: Locator;
  private contingentCardArrow1: Locator;
  private deleteYesConfirmButton: Locator;

  // Save button (generic)
  private saveButton: Locator;

  constructor(page: Page) {
    this.page = page;

    // GOG
    this.ContigentLiabitiesSection = page.locator(
      "//h3[normalize-space()='Contingent Liabilities']"
    );
    this.giftsAndGrandsDropArrow = page.locator(
      "//a[contains(@class,'gogtoolbar tooltipClass')]"
    );
    this.giftsAndGrandsDropArrow1 = page.locator(
      "(//a[contains(@class,'gogtoolbar tooltipClass')])[2]"
    );
    this.assetTypeSelect = page.locator("#assetType_1_chosen");
    this.assetTypeSelectInput = page.locator("#assetType_1_chosen li");
    this.depositedYes = page.locator(`#depositedYes`);
    this.depositedNo = page.locator("#depositedNo");
    this.depositedNA = page.locator("#depositedNA");
    this.depositedYesLabel = page.locator(
      "(//label[normalize-space()='Yes'])[1]"
    );
    this.depositedNoLabel = page.locator(
      "(//label[normalize-space()='No'])[1]"
    );
    this.depositedNALabel = page.locator("//label[normalize-space()='NA']");
    this.assetSourceMultiS = page.locator("#sourceType_1_chosen");
    this.assetSourcePrimaryOption = page.locator(
      "#sourceType_1_chosen .search-field input"
    );
    this.assetSourceOptions = page.locator(
      "#sourceType_1_chosen .chosen-results li.active-result"
    );

    this.cashOrMarketValue = page.locator("#cashOrMarketValue_1");
    this.addGiftsAndGrants = page.locator("//span[@id='gogAdd']");
    this.assetTypeSelect1 = page.locator("#assetType_2_chosen");
    this.assetTypeSelectInput1 = page.locator("#assetType_2_chosen li");
    this.depositedYes1 = page
      .locator("div.radio-inline")
      .nth(1)
      .locator("input#depositedYes");
    this.depositedNo1 = page
      .locator("div.radio-inline")
      .nth(1)
      .locator("input#depositedNo");
    this.depositedNA1 = page
      .locator("div.radio-inline")
      .nth(1)
      .locator("input#depositedNA");
    this.depositedYesLabel1 = page.locator(
      "(//label[normalize-space()='Yes'])[2]"
    );
    this.depositedNoLabel1 = page.locator(
      "(//label[normalize-space()='No'])[2]"
    );
    this.depositedNALabel1 = page.locator(
      "(//label[normalize-space()='NA'])[2]"
    );
    this.assetSourceMultiS1 = page.locator("#sourceType_2_chosen");
    this.assetSourcePrimaryOption1 = page.locator(
      "#sourceType_2_chosen .search-field input"
    );
    this.assetSourceOptions1 = page.locator(
      "#sourceType_2_chosen .chosen-results li.active-result"
    );

    this.cashOrMarketValue1 = page.locator("#cashOrMarketValue_2");
    this.removeGiftsAndGrants = page
      .locator("span.cursor-pointer.btn-danger.btn-icon")
      .nth(1);

    // FAS
    this.finAccSecuDropArrow = page.locator(
      "//a[contains(@class,'fastoolbar tooltipClass')]"
    );
    this.finAccSecuDropArrow1 = page.locator(
      "(//a[contains(@class,'fastoolbar tooltipClass')])[2]"
    );
    this.fasSection = page.locator(".FASSection");
    this.totalVerifiedAssets = page.locator("#totalVerifiedAssets");
    this.accountTypeSelect = page.locator("#accountType_1_chosen");
    this.accountTypeInput = page.locator("#accountType_1_chosen li");
    this.nameofInstitution = page.locator("#nameofInstitution_1");
    this.shareValueInBond = page.locator("#shareValueInBond_1");
    this.nameofSecurity = page.locator("#nameofSecurity_1");
    this.cost = page.locator("#cost_1");
    this.marketValueQuote = page.locator("#marketValueQuote_1");
    this.dateofQuote = page.locator("#dateofQuote_1");
    this.accountTitledAs = page.locator("#accountTitledAs_1");
    this.account = page.locator("#account_1");
    this.balanceValue = page.locator("#balanceValue_1");
    this.owners = page.locator("#owners_1");
    this.description = page.locator("#description_1");
    this.statementDate = page.locator("#statementDate_1");
    this.pledgedYes = page.locator(
      `//*[@id="fasBody_1"]/div[4]/div[5]/div/div/div/label[1]`
    );
    this.pledgedNo = page.locator(
      `//*[@id="fasBody_1"]/div[4]/div[5]/div/div/div/label[2]`
    );
    this.pledgedY = page.locator("#pledgedYes_1");
    this.pledgedN = page.locator("#pledgedNo_1");
    this.addFSButton = page.locator("//span[@id='fasAdd']");
    this.accountTypeSelect1 = page.locator("#accountType_2_chosen");
    this.accountTypeInput1 = page.locator("#accountType_2_chosen li");
    this.nameofInstitution1 = page.locator("#nameofInstitution_2");
    this.shareValueInBond1 = page.locator("#shareValueInBond_2");
    this.nameofSecurity1 = page.locator("#nameofSecurity_2");
    this.cost1 = page.locator("#cost_2");
    this.marketValueQuote1 = page.locator("#marketValueQuote_2");
    this.dateofQuote1 = page.locator("#dateofQuote_2");
    this.accountTitledAs1 = page.locator("#accountTitledAs_2");
    this.account1 = page.locator("#account_2");
    this.balanceValue1 = page.locator("#balanceValue_2");
    this.owners1 = page.locator("#owners_2");
    this.description1 = page.locator("#description_2");
    this.statementDate1 = page.locator("#statementDate_2");
    this.pledgedYes1 = page.locator(
      `//*[@id="fasBody_2"]/div[4]/div[5]/div/div/div/label[1]`
    );
    this.pledgedNo1 = page.locator(
      `//*[@id="fasBody_2"]/div[4]/div[5]/div/div/div/label[2]`
    );
    this.pledgedY1 = page.locator("#pledgedYes_12");
    this.pledgedN1 = page.locator("#pledgedNo_12");
    this.removeFSButton = page
      .locator("span.cursor-pointer.btn-danger.btn-icon")
      .nth(2);
    // CL
    this.contingentCardArrow = page.locator(
      "(//div[contains(@class,'card-toolbar')])[8]"
    );
    this.typeOfLiabilitySelect = page.locator("#typeOfLiability_1_chosen");
    this.typeOfLiabilityInput = page.locator("#typeOfLiability_1_chosen li");
    this.typeOfLiabilityOptions = page.locator(
      "#typeOfLiability_1_chosen .chosen-results li.active-result"
    );
    this.typeOfLiabilitySearch = page.locator(
      "#typeOfLiability_1_chosen .chosen-search input"
    );

    this.nameOfCompany = page.locator("#nameOfCompany_1");
    this.CLmonthlyPayment = page.locator("#CLmonthlyPayment_1");
    this.monthsLeftToPay = page.locator("#monthsLeftToPay_1");
    this.clBalance = page.locator("#clBalance_1");
    this.clAccount = page.locator("#clAccount_1");
    this.clTaxLiability = page.locator("#clTaxLiability_1");
    this.clDescription = page.locator("#clDescription_1");
    this.addContLiabilites = page.locator(
      "//span[contains(@data-clone-section,'contingentLiabilitiesSection')]"
    );
    this.typeOfLiabilitySelect1 = page.locator("#typeOfLiability_2_chosen");
    this.typeOfLiabilityInput1 = page.locator("#typeOfLiability_2_chosen li");
    this.typeOfLiabilityOptions1 = page.locator(
      "#typeOfLiability_2_chosen .chosen-results li.active-result"
    );
    this.typeOfLiabilitySearch1 = page.locator(
      "#typeOfLiability_2_chosen .chosen-search input"
    );

    this.nameOfCompany1 = page.locator("#nameOfCompany_2");
    this.CLmonthlyPayment1 = page.locator("#CLmonthlyPayment_2");
    this.monthsLeftToPay1 = page.locator("#monthsLeftToPay_2");
    this.clBalance1 = page.locator("#clBalance_2");
    this.clAccount1 = page.locator("#clAccount_2");
    this.clTaxLiability1 = page.locator("#clTaxLiability_2");
    this.clDescription1 = page.locator("#clDescription_2");
    this.removeContLiabilites = page
      .locator(
        "//a[contains(@class,'btn-icon ml-2 tooltipClass removeFromSection removeButton2')]"
      )
      .first();
    this.contingentCardArrow1 = page.locator(
      "//div[@id='contingentLiabilitiesSectionId_2']//div[contains(@class,'card-toolbar')]"
    );
    this.deleteYesConfirmButton = page.locator(
      "//button[normalize-space()='yes']"
    );

    // Generic save button — use label fallback to be robust
    this.saveButton = page.locator("#saveBtn");
  }

  // ---------- helpers ----------
  private async expectCleared(locator: Locator) {
    let value = (await locator.inputValue()).trim();

    // normalize zero-like values: "0", "0.0", "0.00", "0.000", "00" -> "0"
    if (/^0+(\.0+)?$/.test(value)) {
      value = "0";
    }

    if (value === "0") {
      console.warn(
        `⚠️ BUG: Field ${
          (await locator.evaluate(
            (el) => el.getAttribute("id") || el.getAttribute("name")
          )) ?? "unknown"
        } auto-populated with 0 after clearing`
      );
      return; // skip expect so test continues
    }

    expect(value).toBe("");
  }

  private async fillIfDefined(locator: Locator, value?: string) {
    if (value === undefined) return;
    await locator.waitFor({ state: "visible", timeout: 5000 }).catch(() => {});
    await locator.fill(String(value));
    // trigger onblur formatting if present by pressing Tab
    await locator.press("Tab").catch(() => {});
  }

  private async selectIfDefined(locator: Locator, value?: string | string[]) {
    if (value === undefined || value === "") return;
    if (Array.isArray(value)) {
      // map to labels
      await locator.selectOption(value.map((v) => ({ label: String(v) })));
    } else {
      await locator.selectOption({ label: String(value) });
    }
  }

  private async clickIfVisible(locator: Locator) {
    try {
      await locator.waitFor({ state: "visible", timeout: 3000 });
      await locator.click({ force: true });
    } catch {
      // best-effort fallback: try clicking underlying input if label provided
      try {
        const forAttr = await locator.getAttribute("for");
        if (forAttr) {
          const input = this.page.locator(`#${forAttr}`);
          await input.click({ force: true });
        }
      } catch {
        /* swallow - best-effort only */
      }
    }
  }

  private async clickDeposited(choice: "Yes" | "No" | "NA") {
    const map: Record<string, Locator> = {
      Yes: this.depositedYesLabel,
      No: this.depositedNoLabel,
      NA: this.depositedNALabel,
    };

    const target = map[choice];
    if (!target) throw new Error(`Invalid deposited choice: ${choice}`);

    await target.click({ force: true });
  }

  private async clickDeposited1(choice: "Yes" | "No" | "NA") {
    const map: Record<string, Locator> = {
      Yes: this.depositedYesLabel1,
      No: this.depositedNoLabel1,
      NA: this.depositedNALabel1,
    };

    const target = map[choice];
    if (!target) throw new Error(`Invalid deposited choice: ${choice}`);

    await target.click({ force: true });
  }

  private async clickPledgedYN(choice: "Yes" | "No") {
    const map: Record<string, Locator> = {
      Yes: this.pledgedYes,
      No: this.pledgedNo,
    };

    const target = map[choice];
    if (!target) throw new Error(`Invalid deposited choice: ${choice}`);

    await target.click({ force: true });
  }

  private async clickPledgedYN1(choice: "Yes" | "No") {
    const map: Record<string, Locator> = {
      Yes: this.pledgedYes1,
      No: this.pledgedNo1,
    };

    const target = map[choice];
    if (!target) throw new Error(`Invalid deposited choice: ${choice}`);

    await target.click({ force: true });
  }

  private async clickPledged(choice: boolean) {
    if (choice) {
      await this.pledgedYes.click();
    } else {
      await this.pledgedNo.click();
    }
  }

  // for radios where input name is known, verify the expected value is checked.
  private async expectRadioSelectedByName(
    name: string,
    expectedValue: string | undefined,
    valueMap?: Record<string, string>
  ) {
    if (expectedValue === undefined || expectedValue === "") return;
    const expected = valueMap
      ? valueMap[String(expectedValue)] ?? String(expectedValue)
      : String(expectedValue);
    // find checked input with that name and value
    const checked = this.page.locator(
      `input[name="${name}"][value="${expected}"]`
    );
    await expect(await checked.isChecked()).toBeTruthy();
  }

  // read an input value and normalize numeric strings (remove commas, dollar signs, trailing .00)
  private normalizeNumberString(s: string | undefined): string {
    if (s === undefined || s === null) return "";
    let t = String(s).trim();
    // remove $ and commas and spaces
    t = t.replace(/\$/g, "").replace(/,/g, "").replace(/\s+/g, "");
    // remove trailing .00
    t = t.replace(/\.00$/, "");
    return t;
  }

  private async expectTextValue(locator: Locator, expected?: string) {
    if (expected === undefined) return;
    const actual =
      (await locator.inputValue().catch(() => locator.textContent())) || "";
    expect(String(actual).trim()).toBe(String(expected).trim());
  }

  async selectFromSingleSelect(
    dropdownElement: any,
    optionText: string,
    customListLocator?: any
  ) {
    await expect(dropdownElement).toBeVisible({ timeout: 5000 });
    await dropdownElement.click();

    const listLocator =
      customListLocator || this.page.locator("ul.chosen-results li");

    const match = listLocator.filter({ hasText: optionText }).first();
    await match.waitFor({ timeout: 5000 });
    await match.click();

    // validate selection
    const displayTextLocator = dropdownElement.locator("span");
    await expect(displayTextLocator).toHaveText(optionText, { timeout: 10000 });
  }

  // new helper: matches <li> text after trimming & normalizing whitespace
  async selectFromSingleSelectNormalizeSpaces(
    dropdownElement: Locator,
    optionText: string,
    listLocator: Locator,
    searchInput?: Locator
  ) {
    // open dropdown
    await expect(dropdownElement).toBeVisible({ timeout: 5000 });
    await dropdownElement.click();

    // optionally type into filter/search box (for chosen widgets)
    if (searchInput) {
      await searchInput.fill(""); // clear
      await searchInput.type(optionText, { delay: 20 });
    }

    // wait for list items to appear
    await listLocator.first().waitFor({ state: "visible", timeout: 5000 });

    const targetText = optionText.trim();
    let found = false;

    const count = await listLocator.count();
    for (let i = 0; i < count; i++) {
      const li = listLocator.nth(i);
      // get visible text and normalize whitespace: convert sequences of whitespace -> single space, trim edges
      const raw = await li.innerText();
      const normalized = raw.replace(/\s+/g, " ").trim();

      if (normalized === targetText) {
        await li.click();
        found = true;
        break;
      }
    }

    // fallback: try substring match if exact normalized match not found
    if (!found) {
      for (let i = 0; i < count; i++) {
        const li = listLocator.nth(i);
        const raw = await li.innerText();
        const normalized = raw.replace(/\s+/g, " ").trim();
        if (normalized.includes(targetText)) {
          await li.click();
          found = true;
          break;
        }
      }
    }

    if (!found) {
      throw new Error(`Option not found in dropdown: "${optionText}"`);
    }

    // validate selection: normalize the displayed label too
    const displaySpan = dropdownElement.locator("span");
    const displayRaw = await displaySpan.innerText();
    const displayNormalized = displayRaw.replace(/\s+/g, " ").trim();
    expect(displayNormalized).toBe(targetText);
  }

  async selectFromMultiSelect(
    dropdownElement: Locator,
    optionTexts: string | string[], // can be single or multiple
    customListLocator?: Locator,
    searchInput?: Locator
  ) {
    await expect(dropdownElement).toBeVisible({ timeout: 5000 });

    const listLocator =
      customListLocator || this.page.locator("ul.chosen-results li");

    // normalize to array
    const options = Array.isArray(optionTexts) ? optionTexts : [optionTexts];

    for (const optionText of options) {
      // 🔑 reopen dropdown for each selection
      await dropdownElement.click();

      if (searchInput) {
        await searchInput.fill("");
        await searchInput.fill(optionText);

        const match = listLocator.filter({ hasText: optionText }).first();
        await match.waitFor({ state: "visible", timeout: 5000 });
        await searchInput.press("Enter");
      } else {
        const match = listLocator.filter({ hasText: optionText }).first();
        await match.waitFor({ state: "visible", timeout: 5000 });
        await match.click();
      }

      // ✅ validate that the chip/tag appears
      const selectedChip = dropdownElement.locator(".search-choice span", {
        hasText: optionText,
      });
      await expect(selectedChip).toBeVisible({ timeout: 10000 });
    }
  }

  private normalizeCurrency = (val: string): string => {
    return val.replace(/[,]/g, "").replace(/\.00$/, "");
  };

  private async expectNormalizedValue(locator: Locator, expected: any) {
    if (expected === undefined) return;
    const uiValue = await locator.inputValue();
    const normalizedUI = this.normalizeCurrency(uiValue);
    const normalizedExpected = this.normalizeCurrency(String(expected));
    expect(normalizedUI).toBe(normalizedExpected);
  }

  async selectAssetSource(optionTexts?: string | string[]) {
    if (!optionTexts || optionTexts.length === 0) return; // nothing to select

    await this.selectFromMultiSelect(
      this.assetSourceMultiS,
      optionTexts,
      this.assetSourceOptions,
      this.assetSourcePrimaryOption
    );
  }

  async selectAssetSource1(optionTexts?: string | string[]) {
    if (!optionTexts || optionTexts.length === 0) return; // nothing to select

    await this.selectFromMultiSelect(
      this.assetSourceMultiS1,
      optionTexts,
      this.assetSourceOptions1,
      this.assetSourcePrimaryOption1
    );
  }

  private async expectSelectHasLabels(
    locator: Locator,
    expected?: string | string[]
  ) {
    if (expected === undefined) return;
    // evaluate selected options labels
    const selectedLabels: string[] = await locator.evaluate((sel) =>
      Array.from((sel as HTMLSelectElement).selectedOptions).map((o) =>
        (o.textContent || "").trim()
      )
    );
    if (Array.isArray(expected)) {
      for (const e of expected) expect(selectedLabels).toContain(String(e));
    } else {
      expect(selectedLabels[0]).toBe(String(expected));
    }
  }

  // ---------- GOG CRUD ----------
  async createGiftsOrGrants(data: any) {
    const app = new AppManager(this.page);
    // assetType
    await app.utilities.scrollToElement2(
      this.page,
      this.ContigentLiabitiesSection
    );
    await this.giftsAndGrandsDropArrow.click();
    //Commented as we cant remove or change the values from Single select dropdown
    // if (data.assetType) {
    //   await this.selectFromSingleSelect(
    //     this.assetTypeSelect,
    //     data.assetType,
    //     this.assetTypeSelectInput
    //   );
    // }

    if (data.deposited) {
      await this.clickDeposited(data.deposited);
    }
    //Commented as we cant remove or change the values from multiselect dropdown
    // sourceType (multiple)
    // await this.selectAssetSource(data.sourceType);
    await this.fillIfDefined(this.cashOrMarketValue, data.cashOrMarketValue);

    await this.saveButton.click();
  }

  async validateCreateGiftsOrGrants(data: any) {
    await this.giftsAndGrandsDropArrow.click();
    await this.page.waitForLoadState();
    //Commented as we cant remove or change the values from Single select dropdown
    // if (data.assetType !== undefined) {
    //   const displayText = await this.assetTypeSelect
    //     .locator("span")
    //     .innerText();
    //   expect(displayText.trim()).toBe(data.assetType);
    // }

    if (data.deposited !== undefined) {
      const map: Record<string, string> = {
        Yes: "depositedYes",
        No: "depositedNo",
        NA: "depositedNA",
      };
      const expectedId = map[data.deposited];
      const radio = this.page.locator(`#${expectedId}`);
      await expect(radio).toBeChecked();
    }

    //Commented as we cant remove or change the values from multiselect dropdown

    // if (data.sourceType !== undefined) {
    //   const selectedChips = this.assetSourceMultiS.locator(
    //     ".search-choice span"
    //   );
    //   const chipsText = await selectedChips.allInnerTexts();
    //   for (const expected of data.sourceType) {
    //     expect(chipsText).toContain(expected);
    //   }
    // }

    if (data?.cashOrMarketValue !== undefined) {
      await this.expectNormalizedValue(
        this.cashOrMarketValue,
        data.cashOrMarketValue
      );
    }
  }

  async updateGiftsOrGrants(data: any) {
    //Commented as we cant remove or change the values from Single select dropdown
    // if (data.assetType) {
    //   await this.selectFromSingleSelect(
    //     this.assetTypeSelect,
    //     data.assetType,
    //     this.assetTypeSelectInput
    //   );
    // }

    if (data.deposited) {
      await this.clickDeposited(data.deposited);
    }
    //Commented as we cant remove or change the values from multiselect dropdown
    // sourceType (multiple)
    // await this.selectAssetSource(data.sourceType);
    await this.fillIfDefined(this.cashOrMarketValue, data.cashOrMarketValue);

    await this.saveButton.click();
  }

  async validateUpdateGiftsOrGrants(data: any) {
    await this.validateCreateGiftsOrGrants(data);
  }

  async clearGiftsOrGrants(data?: any) {
    // clear all inputs in the first row
    // await this.fillIfDefined(this.assetTypeSelect, data?.assetType || "");
    // attempt to clear selected multi selects by selecting empty
    // if (data?.sourceType === undefined) {
    //   // unselect all
    //   await this.page.evaluate(() => {
    //     const el = document.querySelector(
    //       "#sourceType_1"
    //     ) as HTMLSelectElement | null;
    //     if (el) {
    //       for (const opt of el.options) opt.selected = false;
    //       el.dispatchEvent(new Event("change"));
    //     }
    //   });
    // }
    await this.fillIfDefined(
      this.cashOrMarketValue,
      data?.cashOrMarketValue || ""
    );
    // clear deposited (select NA or nothing) — set to none by clicking NA if delete expects empty, else click none if present
    await this.saveButton.click();
  }

  async validateDeleteGiftsOrGrants(data?: any) {
    await this.giftsAndGrandsDropArrow.click();
    await this.page.waitForLoadState();
    // assetType may render as empty selection
    // if (data?.assetType === undefined) {
    //   // expect no selection
    //   await this.expectSelectHasLabels(this.assetTypeSelect, "");
    // } else {
    //   await this.expectSelectHasLabels(this.assetTypeSelect, data.assetType);
    // }

    // // deposited: expect not checked (we check NA or not)
    // if (!data?.deposited) {
    //   // try to assert no Yes/No checked
    //   const yesChecked = await this.page
    //     .locator('input[name="deposited_1"][value="1"]')
    //     .isChecked()
    //     .catch(() => false);
    //   const noChecked = await this.page
    //     .locator('input[name="deposited_1"][value="0"]')
    //     .isChecked()
    //     .catch(() => false);
    //   expect(yesChecked || noChecked).toBeFalsy();
    // }
    // if (data?.sourceType === undefined) {
    //   const selLabels = await this.sourceTypeSelect.evaluate((sel) =>
    //     Array.from((sel as HTMLSelectElement).selectedOptions).map(
    //       (o) => o.textContent || ""
    //     )
    //   );
    //   expect(selLabels.length).toBe(0);
    // }
    if (!data?.cashOrMarketValue) {
      const val = await this.cashOrMarketValue.inputValue().catch(() => "");
      expect(String(val || "").trim()).toBe("");
    }
  }

  async createGiftsOrGrantsPlusSection(data: any) {
    const app = new AppManager(this.page);

    // Scroll to section
    await app.utilities.scrollToElement2(
      this.page,
      this.ContigentLiabitiesSection
    );

    await this.addGiftsAndGrants.click();

    // assetType
    if (data.assetType) {
      await this.selectFromSingleSelect(
        this.assetTypeSelect1,
        data.assetType,
        this.assetTypeSelectInput1
      );
    }

    // deposited
    if (data.deposited) {
      await this.clickDeposited1(data.deposited);
    }

    // sourceType (multiple)
    await this.selectAssetSource1(data.sourceType);

    // cashOrMarketValue
    await this.fillIfDefined(this.cashOrMarketValue1, data.cashOrMarketValue);

    // save
    await this.saveButton.click();
  }

  async validateCreateGiftsOrGrantsPlusSection(data: any) {
    await this.giftsAndGrandsDropArrow1.click();
    await this.page.waitForLoadState();
    //Commented as we cant remove or change the values from Single select dropdown
    if (data.assetType !== undefined) {
      const displayText = await this.assetTypeSelect1
        .locator("span")
        .innerText();
      expect(displayText.trim()).toBe(data.assetType);
    }

    // Commented as the locator for radio buttons not found
    if (data.deposited !== undefined) {
      const map: Record<string, Locator> = {
        Yes: this.depositedYes1,
        No: this.depositedNo1,
        NA: this.depositedNA1,
      };

      const radio = map[data.deposited];
      if (!radio)
        throw new Error(`Invalid deposited choice: ${data.deposited}`);
      await expect(radio).toBeChecked();
    }

    if (data.sourceType !== undefined) {
      const selectedChips = this.assetSourceMultiS1.locator(
        ".search-choice span"
      );
      const chipsText = await selectedChips.allInnerTexts();
      for (const expected of data.sourceType) {
        expect(chipsText).toContain(expected);
      }
    }

    if (data?.cashOrMarketValue !== undefined) {
      await this.expectNormalizedValue(
        this.cashOrMarketValue1,
        data.cashOrMarketValue
      );
    }
  }

  async clearGiftsOrGrantsPlusSection() {
    await this.removeGiftsAndGrants.click({ force: true });
    await this.saveButton.click();
  }

  // ---------- FAS CRUD ----------
  async createFinancialAccountsAndSecurities(data: any) {
    // const app = new AppManager(this.page);
    // assetType
    // await app.utilities.scrollToElement2(
    //   this.page,
    //   this.ContigentLiabitiesSection
    // );
    await this.finAccSecuDropArrow.click();
    // no need to set totalVerifiedAssets (readonly) — but if passed, we won't fill readonly
    if (data.accountType) {
      await this.selectFromSingleSelect(
        this.accountTypeSelect,
        data.accountType,
        this.accountTypeInput
      );
    }
    await this.fillIfDefined(this.nameofInstitution, data.nameofInstitution);
    await this.fillIfDefined(this.shareValueInBond, data.shareValueInBond);
    await this.fillIfDefined(this.nameofSecurity, data.nameofSecurity);
    await this.fillIfDefined(this.cost, data.cost);
    await this.fillIfDefined(this.marketValueQuote, data.marketValueQuote);
    await this.fillIfDefined(this.dateofQuote, data.dateofQuote);
    await this.fillIfDefined(this.accountTitledAs, data.accountTitledAs);
    await this.fillIfDefined(this.account, data.account);
    await this.fillIfDefined(this.balanceValue, data.balanceValue);
    await this.fillIfDefined(this.owners, data.owners);
    await this.fillIfDefined(this.description, data.description);
    await this.fillIfDefined(this.statementDate, data.statementDate);

    // pledged radio (Yes/No)
    if (data.pledgedYN) {
      await this.clickPledgedYN(data.pledgedYN);
    }

    await this.saveButton.click();
  }

  async validateCreateFinancialAccountsAndSecurities(data: any) {
    await this.finAccSecuDropArrow.click();
    if (data.accountType !== undefined) {
      const displayText = await this.accountTypeSelect
        .locator("span")
        .innerText();
      expect(displayText.trim()).toBe(data.accountType);
    }
    if (data.nameofInstitution !== undefined)
      await this.expectTextValue(
        this.nameofInstitution,
        data.nameofInstitution
      );
    if (data.shareValueInBond !== undefined)
      await this.expectNormalizedValue(
        this.shareValueInBond,
        data.shareValueInBond
      );
    if (data.nameofSecurity !== undefined)
      await this.expectTextValue(this.nameofSecurity, data.nameofSecurity);
    if (data.cost !== undefined)
      await this.expectNormalizedValue(this.cost, data.cost);
    if (data.marketValueQuote !== undefined)
      await this.expectNormalizedValue(
        this.marketValueQuote,
        data.marketValueQuote
      );
    if (data.dateofQuote !== undefined)
      await this.expectTextValue(this.dateofQuote, data.dateofQuote);
    if (data.accountTitledAs !== undefined)
      await this.expectTextValue(this.accountTitledAs, data.accountTitledAs);
    if (data.account !== undefined)
      await this.expectTextValue(this.account, data.account);
    if (data.balanceValue !== undefined)
      await this.expectNormalizedValue(this.balanceValue, data.balanceValue);
    if (data.owners !== undefined)
      await this.expectTextValue(this.owners, data.owners);
    if (data.description !== undefined)
      await this.expectTextValue(this.description, data.description);
    if (data.statementDate !== undefined)
      await this.expectTextValue(this.statementDate, data.statementDate);
    if (data.pledgedYN !== undefined) {
      const map: Record<string, string> = {
        Yes: "pledgedYes_1",
        No: "pledgedNo_1",
      };
      const expectedId = map[data.pledgedYN];
      const radio = this.page.locator(`#${expectedId}`);
      await expect(radio).toBeChecked();
    }
  }

  async updateFinancialAccountsAndSecurities(data: any) {
    // await this.finAccSecuDropArrow.click();
    // no need to set totalVerifiedAssets (readonly) — but if passed, we won't fill readonly
    // if (data.accountType) {
    //   await this.selectFromSingleSelect(
    //     this.accountTypeSelect,
    //     data.accountType,
    //     this.accountTypeInput
    //   );
    // }
    await this.fillIfDefined(this.nameofInstitution, data.nameofInstitution);
    await this.fillIfDefined(this.shareValueInBond, data.shareValueInBond);
    await this.fillIfDefined(this.nameofSecurity, data.nameofSecurity);
    await this.fillIfDefined(this.cost, data.cost);
    await this.fillIfDefined(this.marketValueQuote, data.marketValueQuote);
    await this.fillIfDefined(this.dateofQuote, data.dateofQuote);
    await this.fillIfDefined(this.accountTitledAs, data.accountTitledAs);
    await this.fillIfDefined(this.account, data.account);
    await this.fillIfDefined(this.balanceValue, data.balanceValue);
    await this.fillIfDefined(this.description, data.description);
    await this.fillIfDefined(this.statementDate, data.statementDate);

    // pledged radio (Yes/No)
    if (data.pledged) {
      await this.clickPledged(data.pledged);
    }

    await this.saveButton.click();
  }

  async validateUpdateFinancialAccountsAndSecurities(data: any) {
    // await this.finAccSecuDropArrow.click();
    // if (data.accountType !== undefined) {
    //   const displayText = await this.accountTypeSelect
    //     .locator("span")
    //     .innerText();
    //   expect(displayText.trim()).toBe(data.accountType);
    // }
    if (data.nameofInstitution !== undefined)
      await this.expectTextValue(
        this.nameofInstitution,
        data.nameofInstitution
      );
    if (data.shareValueInBond !== undefined)
      await this.expectNormalizedValue(
        this.shareValueInBond,
        data.shareValueInBond
      );
    if (data.nameofSecurity !== undefined)
      await this.expectTextValue(this.nameofSecurity, data.nameofSecurity);
    if (data.cost !== undefined)
      await this.expectNormalizedValue(this.cost, data.cost);
    if (data.marketValueQuote !== undefined)
      await this.expectNormalizedValue(
        this.marketValueQuote,
        data.marketValueQuote
      );
    if (data.dateofQuote !== undefined)
      await this.expectTextValue(this.dateofQuote, data.dateofQuote);
    if (data.accountTitledAs !== undefined)
      await this.expectTextValue(this.accountTitledAs, data.accountTitledAs);
    if (data.account !== undefined)
      await this.expectTextValue(this.account, data.account);
    if (data.balanceValue !== undefined)
      await this.expectNormalizedValue(this.balanceValue, data.balanceValue);
    if (data.description !== undefined)
      await this.expectTextValue(this.description, data.description);
    if (data.statementDate !== undefined)
      await this.expectTextValue(this.statementDate, data.statementDate);

    if (data.pledgedYN !== undefined) {
      const map: Record<string, string> = {
        Yes: "pledgedYes_1",
        No: "pledgedNo_1",
      };
      const expectedId = map[data.pledgedYN];
      const radio = this.page.locator(`#${expectedId}`);
      await expect(radio).toBeChecked();
    }
  }

  async clearFinancialAccountsAndSecurities(data?: any) {
    await this.finAccSecuDropArrow.click();

    // clear inputs
    // await this.fillIfDefined(this.accountTypeSelect, data?.accountType || "");
    await this.fillIfDefined(
      this.nameofInstitution,
      data?.nameofInstitution || ""
    );
    await this.fillIfDefined(
      this.shareValueInBond,
      data?.shareValueInBond || ""
    );
    await this.fillIfDefined(this.nameofSecurity, data?.nameofSecurity || "");
    await this.fillIfDefined(this.cost, data?.cost || "");
    await this.fillIfDefined(
      this.marketValueQuote,
      data?.marketValueQuote || ""
    );
    await this.fillIfDefined(this.dateofQuote, data?.dateofQuote || "");
    await this.fillIfDefined(this.accountTitledAs, data?.accountTitledAs || "");
    await this.fillIfDefined(this.account, data?.account || "");
    await this.fillIfDefined(this.balanceValue, data?.balanceValue || "");
    await this.fillIfDefined(this.owners, data?.owners || "");
    await this.fillIfDefined(this.description, data?.description || "");
    await this.fillIfDefined(this.statementDate, data?.statementDate || "");
    await this.saveButton.click();
  }

  async validateDeleteFinancialAccountsAndSecurities(data?: any) {
    await this.finAccSecuDropArrow.click();
    await this.page.waitForLoadState();
    // expect empty or default values
    // if (!data?.accountType)
    //   await this.expectSelectHasLabels(this.accountTypeSelect, "");
    if (!data?.nameofInstitution) {
      const actual = (
        await this.nameofInstitution.inputValue().catch(() => "")
      ).trim();
      expect(actual).toBe("");
    }
    if (!data?.balanceValue) {
      const val = await this.balanceValue.inputValue().catch(() => "");
      expect(String(val || "").trim()).toBe("");
    }
  }

  async createFinancialAccountsAndSecuritiesPlus(data: any) {
    // const app = new AppManager(this.page);
    // assetType
    // await app.utilities.scrollToElement2(
    //   this.page,
    //   this.ContigentLiabitiesSection
    // );
    await this.page.reload();
    await this.addFSButton.click();
    // await this.accountTypeSelect1.click();

    // no need to set totalVerifiedAssets (readonly) — but if passed, we won't fill readonly
    if (data.accountType) {
      await this.selectFromSingleSelect(
        this.accountTypeSelect1,
        data.accountType,
        this.accountTypeInput1
      );
    }
    await this.fillIfDefined(this.nameofInstitution1, data.nameofInstitution);
    await this.fillIfDefined(this.shareValueInBond1, data.shareValueInBond);
    await this.fillIfDefined(this.nameofSecurity1, data.nameofSecurity);
    await this.fillIfDefined(this.cost1, data.cost);
    await this.fillIfDefined(this.marketValueQuote1, data.marketValueQuote);
    await this.fillIfDefined(this.dateofQuote1, data.dateofQuote);
    await this.fillIfDefined(this.accountTitledAs1, data.accountTitledAs);
    await this.fillIfDefined(this.account1, data.account);
    await this.fillIfDefined(this.balanceValue1, data.balanceValue);
    await this.fillIfDefined(this.owners1, data.owners);
    await this.fillIfDefined(this.description1, data.description);
    await this.fillIfDefined(this.statementDate1, data.statementDate);

    // pledged radio (Yes/No)
    if (data.pledgedYN) {
      await this.clickPledgedYN1(data.pledgedYN);
    }

    await this.saveButton.click();
  }

  async validateCreateFinancialAccountsAndSecuritiesPlus(data: any) {
    await this.finAccSecuDropArrow1.click();
    if (data.accountType !== undefined) {
      const displayText = await this.accountTypeSelect1
        .locator("span")
        .innerText();
      expect(displayText.trim()).toBe(data.accountType);
    }
    if (data.nameofInstitution !== undefined)
      await this.expectTextValue(
        this.nameofInstitution1,
        data.nameofInstitution
      );
    if (data.shareValueInBond !== undefined)
      await this.expectNormalizedValue(
        this.shareValueInBond1,
        data.shareValueInBond
      );
    if (data.nameofSecurity !== undefined)
      await this.expectTextValue(this.nameofSecurity1, data.nameofSecurity);
    if (data.cost !== undefined)
      await this.expectNormalizedValue(this.cost1, data.cost);
    if (data.marketValueQuote !== undefined)
      await this.expectNormalizedValue(
        this.marketValueQuote1,
        data.marketValueQuote
      );
    if (data.dateofQuote !== undefined)
      await this.expectTextValue(this.dateofQuote1, data.dateofQuote);
    if (data.accountTitledAs !== undefined)
      await this.expectTextValue(this.accountTitledAs1, data.accountTitledAs);
    if (data.account !== undefined)
      await this.expectTextValue(this.account1, data.account);
    if (data.balanceValue !== undefined)
      await this.expectNormalizedValue(this.balanceValue1, data.balanceValue);
    if (data.owners !== undefined)
      await this.expectTextValue(this.owners1, data.owners);
    if (data.description !== undefined)
      await this.expectTextValue(this.description1, data.description);
    if (data.statementDate !== undefined)
      await this.expectTextValue(this.statementDate1, data.statementDate);
    if (data.pledgedYN !== undefined) {
      const map: Record<string, string> = {
        Yes: "pledgedYes_2",
        No: "pledgedNo_2",
      };
      const expectedId = map[data.pledgedYN];
      const radio = this.page.locator(`#${expectedId}`);
      await expect(radio).toBeChecked();
    }
  }

  async clearFinancialAccountsAndSecuritiesPlus() {
    await this.removeFSButton.click();
    await this.saveButton.click();
  }

  // ---------- CL CRUD ----------
  async createContingentLiabilities(data: any) {
    // await this.contingentCardArrow.click();
    // await this.page.waitForTimeout(2000);
    if (data.typeOfLiability) {
      await this.selectFromSingleSelectNormalizeSpaces(
        this.typeOfLiabilitySelect,
        data.typeOfLiability,
        this.typeOfLiabilityOptions,
        this.typeOfLiabilitySearch
      );
    }
    await this.fillIfDefined(this.nameOfCompany, data.nameOfCompany);
    await this.fillIfDefined(this.CLmonthlyPayment, data.CLmonthlyPayment);
    await this.fillIfDefined(this.monthsLeftToPay, data.monthsLeftToPay);
    await this.fillIfDefined(this.clBalance, data.clBalance);
    await this.fillIfDefined(this.clAccount, data.clAccount);
    await this.fillIfDefined(this.clTaxLiability, data.clTaxLiability);
    await this.fillIfDefined(this.clDescription, data.clDescription);

    await this.saveButton.click();
  }

  async validateCreateContingentLiabilities(data: any) {
    await this.page.waitForLoadState();
    if (data.typeOfLiability !== undefined) {
      const displayText = await this.typeOfLiabilitySelect
        .locator("span")
        .innerText();
      expect(displayText.trim()).toBe(data.typeOfLiability);
    }
    if (data.nameOfCompany !== undefined)
      await this.expectTextValue(this.nameOfCompany, data.nameOfCompany);
    if (data.CLmonthlyPayment !== undefined)
      await this.expectNormalizedValue(
        this.CLmonthlyPayment,
        data.CLmonthlyPayment
      );
    if (data.monthsLeftToPay !== undefined)
      await this.expectTextValue(this.monthsLeftToPay, data.monthsLeftToPay);
    if (data.clBalance !== undefined)
      await this.expectNormalizedValue(this.clBalance, data.clBalance);
    if (data.clAccount !== undefined)
      await this.expectTextValue(this.clAccount, data.clAccount);
    if (data.clTaxLiability !== undefined)
      await this.expectNormalizedValue(
        this.clTaxLiability,
        data.clTaxLiability
      );
    if (data.clDescription !== undefined)
      await this.expectTextValue(this.clDescription, data.clDescription);
  }

  async updateContingentLiabilities(data: any) {
    if (data.typeOfLiability) {
      await this.selectFromSingleSelectNormalizeSpaces(
        this.typeOfLiabilitySelect,
        data.typeOfLiability,
        this.typeOfLiabilityOptions,
        this.typeOfLiabilitySearch
      );
    }
    await this.fillIfDefined(this.nameOfCompany, data.nameOfCompany);
    await this.fillIfDefined(this.CLmonthlyPayment, data.CLmonthlyPayment);
    await this.fillIfDefined(this.monthsLeftToPay, data.monthsLeftToPay);
    await this.fillIfDefined(this.clBalance, data.clBalance);
    await this.fillIfDefined(this.clAccount, data.clAccount);
    await this.fillIfDefined(this.clDescription, data.clDescription);

    await this.saveButton.click();
  }

  async validateUpdateContingentLiabilities(data: any) {
    await this.page.waitForLoadState();
    if (data.typeOfLiability !== undefined) {
      const displayText = await this.typeOfLiabilitySelect
        .locator("span")
        .innerText();
      expect(displayText.trim()).toBe(data.typeOfLiability);
    }
    if (data.nameOfCompany !== undefined)
      await this.expectTextValue(this.nameOfCompany, data.nameOfCompany);
    if (data.CLmonthlyPayment !== undefined)
      await this.expectNormalizedValue(
        this.CLmonthlyPayment,
        data.CLmonthlyPayment
      );
    if (data.monthsLeftToPay !== undefined)
      await this.expectTextValue(this.monthsLeftToPay, data.monthsLeftToPay);
    if (data.clBalance !== undefined)
      await this.expectNormalizedValue(this.clBalance, data.clBalance);
    if (data.clAccount !== undefined)
      await this.expectTextValue(this.clAccount, data.clAccount);
    if (data.clDescription !== undefined)
      await this.expectTextValue(this.clDescription, data.clDescription);
  }

  async clearContingentLiabilities(data?: any) {
    // await this.fillIfDefined(
    //   this.typeOfLiabilitySelect,
    //   data?.typeOfLiability || ""
    // );
    await this.fillIfDefined(this.nameOfCompany, data?.nameOfCompany || "");
    await this.fillIfDefined(
      this.CLmonthlyPayment,
      data?.CLmonthlyPayment || ""
    );
    await this.fillIfDefined(this.monthsLeftToPay, data?.monthsLeftToPay || "");
    await this.fillIfDefined(this.clBalance, data?.clBalance || "");
    await this.fillIfDefined(this.clAccount, data?.clAccount || "");
    // await this.fillIfDefined(this.clTaxLiability, data?.clTaxLiability || "");
    await this.fillIfDefined(this.clDescription, data?.clDescription || "");
    await this.saveButton.click();
  }

  async validateDeleteContingentLiabilities(data?: any) {
    await this.page.waitForLoadState();

    const getVal = async (locator: Locator) =>
      (await locator.inputValue().catch(() => "")).trim();

    expect(await getVal(this.nameOfCompany)).toBe(data?.nameOfCompany ?? "");
    await this.expectCleared(this.CLmonthlyPayment);
    await this.expectCleared(this.monthsLeftToPay);
    await this.expectCleared(this.clBalance);
    // expect(await getVal(this.clBalance)).toBe(data?.clBalance ?? "");
    expect(await getVal(this.clAccount)).toBe(data?.clAccount ?? "");
    expect(await getVal(this.clDescription)).toBe(data?.clDescription ?? "");
  }

  async createContingentLiabilitiesPlus(data: any) {
    await this.addContLiabilites.click();
    if (data.typeOfLiability) {
      await this.selectFromSingleSelectNormalizeSpaces(
        this.typeOfLiabilitySelect1,
        data.typeOfLiability,
        this.typeOfLiabilityOptions1,
        this.typeOfLiabilitySearch1
      );
    }
    await this.fillIfDefined(this.nameOfCompany1, data.nameOfCompany);
    await this.fillIfDefined(this.CLmonthlyPayment1, data.CLmonthlyPayment);
    await this.fillIfDefined(this.monthsLeftToPay1, data.monthsLeftToPay);
    await this.fillIfDefined(this.clBalance1, data.clBalance);
    await this.fillIfDefined(this.clAccount1, data.clAccount);
    // await this.fillIfDefined(this.clTaxLiability1, data.clTaxLiability);
    await this.fillIfDefined(this.clDescription1, data.clDescription);

    await this.saveButton.click();
  }

  async validateCreateContingentLiabilitiesPlus(data: any) {
    await this.page.waitForLoadState();
    if (data.typeOfLiability !== undefined) {
      const displayText = await this.typeOfLiabilitySelect1
        .locator("span")
        .innerText();
      expect(displayText.trim()).toBe(data.typeOfLiability);
    }
    if (data.nameOfCompany !== undefined)
      await this.expectTextValue(this.nameOfCompany1, data.nameOfCompany);
    if (data.CLmonthlyPayment !== undefined)
      await this.expectNormalizedValue(
        this.CLmonthlyPayment1,
        data.CLmonthlyPayment
      );
    if (data.monthsLeftToPay !== undefined)
      await this.expectTextValue(this.monthsLeftToPay1, data.monthsLeftToPay);
    if (data.clBalance !== undefined)
      await this.expectNormalizedValue(this.clBalance1, data.clBalance);
    if (data.clAccount !== undefined)
      await this.expectTextValue(this.clAccount1, data.clAccount);
    // if (data.clTaxLiability !== undefined)
    //   await this.expectNormalizedValue(
    //     this.clTaxLiability1,
    //     data.clTaxLiability
    //   );
    if (data.clDescription !== undefined)
      await this.expectTextValue(this.clDescription1, data.clDescription);
  }

  async clearContingentLiabilitiesPlus() {
    await this.removeContLiabilites.click();
    await this.deleteYesConfirmButton.click();
    await this.saveButton.click();
  }
}
