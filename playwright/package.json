{"name": "playwright", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"pl:pre-prod": "cross-env TEST_ENV=pre-prod npx playwright test", "pl:visual-check-cv3": "cross-env TEST_ENV=pre-prod npx playwright test --headed --project=visual-check-cv3", "pl:pre-prod-headed": "cross-env TEST_ENV=pre-prod npx playwright test --headed", "clean:report": "node -e \"require('fs').rmSync('./report/allure-report', { recursive: true, force: true })\"", "generate:report": "npm run clean:report && npx allure generate ./allure-results -o ./report/allure-report"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@faker-js/faker": "^9.9.0", "@playwright/test": "^1.54.2", "@types/node": "^24.2.1", "allure-commandline": "^2.34.1", "allure-playwright": "^3.3.3", "cross-env": "^10.0.0", "rimraf": "^6.0.1"}, "dependencies": {"pixelmatch": "^7.1.0", "sharp": "^0.34.3"}}