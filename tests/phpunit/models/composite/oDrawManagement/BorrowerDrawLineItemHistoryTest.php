<?php

namespace tests\models\composite\oDrawManagement;

use models\composite\oDrawManagement\BorrowerDrawLineItemHistory;
use models\lendingwise\tblDrawRequestLineItems_h;
use tests\models\composite\oDrawManagement\MockDataHelper;
use PHPUnit\Framework\TestCase;

/**
 * Tests the BorrowerDrawLineItemHistory class.
 *
 * @covers \models\composite\oDrawManagement\BorrowerDrawLineItemHistory::__construct
 * @covers \models\composite\oDrawManagement\BorrowerDrawLineItemHistory::save
 * @covers \models\composite\oDrawManagement\BorrowerDrawLineItemHistory::setFromArray
 * @covers \models\composite\oDrawManagement\BorrowerDrawLineItemHistory::getDbObject
 * @covers \models\composite\oDrawManagement\BorrowerDrawLineItemHistory::toArray
 */
class BorrowerDrawLineItemHistoryTest extends TestCase
{
    /**
     * Tests the constructor with null parameter.
     */
    public function testConstructorWithNull(): void
    {
        $mockLineItemHistory = MockDataHelper::createMockBorrowerDrawLineItemHistory();

        $this->assertIsObject($mockLineItemHistory);
        $this->assertIsObject($mockLineItemHistory->lineItemHistory);
        $this->assertNull($mockLineItemHistory->id);
        $this->assertNull($mockLineItemHistory->recordId);
        $this->assertNull($mockLineItemHistory->lineItemId);
        $this->assertEquals(0.0, $mockLineItemHistory->completedAmount);
        $this->assertEquals(0.0, $mockLineItemHistory->completedPercent);
        $this->assertEquals(0.0, $mockLineItemHistory->requestedAmount);
        $this->assertEquals(0.0, $mockLineItemHistory->disbursedAmount);
        $this->assertNull($mockLineItemHistory->notes);
        $this->assertNull($mockLineItemHistory->lenderNotes);
        $this->assertNull($mockLineItemHistory->createdAt);
    }

    /**
     * Tests the constructor with a database object.
     */
    public function testConstructorWithDbObject(): void
    {
        $mockLineItemHistory = MockDataHelper::createMockBorrowerDrawLineItemHistory(123);

        $this->assertIsObject($mockLineItemHistory);
        $this->assertEquals(123, $mockLineItemHistory->id);
        $this->assertEquals(456, $mockLineItemHistory->recordId);
        $this->assertEquals(789, $mockLineItemHistory->lineItemId);
        $this->assertEquals(500.0, $mockLineItemHistory->completedAmount);
        $this->assertEquals(50.0, $mockLineItemHistory->completedPercent);
        $this->assertEquals(300.0, $mockLineItemHistory->requestedAmount);
        $this->assertEquals(200.0, $mockLineItemHistory->disbursedAmount);
        $this->assertEquals('Test notes', $mockLineItemHistory->notes);
        $this->assertEquals('Test lender notes', $mockLineItemHistory->lenderNotes);
        $this->assertEquals('2023-01-01 12:00:00', $mockLineItemHistory->createdAt);
    }

    /**
     * Tests the save method with complete data.
     */
    public function testSaveWithCompleteData(): void
    {
        $mockLineItemHistory = MockDataHelper::createMockBorrowerDrawLineItemHistory();
        $data = [
            'recordId' => 456,
            'lineItemId' => 789,
            'completedAmount' => 500.0,
            'completedPercent' => 50.0,
            'requestedAmount' => 300.0,
            'disbursedAmount' => 200.0,
            'notes' => 'Test notes',
            'lenderNotes' => 'Test lender notes'
        ];

        $result = $mockLineItemHistory->save($data);
        $this->assertIsArray($result);
        $this->assertTrue($result['success']);
        $this->assertIsInt($result['id']);
    }

    /**
     * Tests the save method with minimal data.
     */
    public function testSaveWithMinimalData(): void
    {
        $mockLineItemHistory = MockDataHelper::createMockBorrowerDrawLineItemHistory();
        $data = [
            'recordId' => 456,
            'lineItemId' => 789,
            'requestedAmount' => 300.0
        ];

        $result = $mockLineItemHistory->save($data);
        $this->assertIsArray($result);
        $this->assertTrue($result['success']);
    }

    /**
     * Tests the save method without data.
     */
    public function testSaveWithoutData(): void
    {
        $mockLineItemHistory = MockDataHelper::createMockBorrowerDrawLineItemHistory();

        $result = $mockLineItemHistory->save([]);
        $this->assertIsArray($result);
        $this->assertTrue($result['success']);
    }

    /**
     * Tests the getDbObject method.
     */
    public function testGetDbObject(): void
    {
        $mockLineItemHistory = MockDataHelper::createMockBorrowerDrawLineItemHistory();

        $result = $mockLineItemHistory->getDbObject();

        $this->assertIsObject($result);
    }

    /**
     * Tests the toArray method.
     */
    public function testToArray(): void
    {
        $mockLineItemHistory = MockDataHelper::createMockBorrowerDrawLineItemHistory(123);

        $result = $mockLineItemHistory->toArray();

        $this->assertIsArray($result);
        $this->assertEquals(123, $result['id']);
        $this->assertEquals(456, $result['recordId']);
        $this->assertEquals(789, $result['lineItemId']);
        $this->assertEquals(500.0, $result['completedAmount']);
        $this->assertEquals(50.0, $result['completedPercent']);
        $this->assertEquals(300.0, $result['requestedAmount']);
        $this->assertEquals(200.0, $result['disbursedAmount']);
        $this->assertEquals('Test notes', $result['notes']);
        $this->assertEquals('Test lender notes', $result['lenderNotes']);
        $this->assertEquals('2023-01-01 12:00:00', $result['createdAt']);
    }

    /**
     * Tests property default values.
     */
    public function testPropertyDefaults(): void
    {
        try {
            $lineItemHistory = new BorrowerDrawLineItemHistory();

            $this->assertNull($lineItemHistory->id);
            $this->assertNull($lineItemHistory->recordId);
            $this->assertNull($lineItemHistory->lineItemId);
            $this->assertEquals(0.0, $lineItemHistory->completedAmount);
            $this->assertEquals(0.0, $lineItemHistory->completedPercent);
            $this->assertEquals(0.0, $lineItemHistory->requestedAmount);
            $this->assertEquals(0.0, $lineItemHistory->disbursedAmount);
            $this->assertNull($lineItemHistory->notes);
            $this->assertNull($lineItemHistory->lenderNotes);
            $this->assertNull($lineItemHistory->createdAt);
            $this->assertInstanceOf(tblDrawRequestLineItems_h::class, $lineItemHistory->lineItemHistory);
        } catch (\Error|\Exception $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests save method with zero values.
     */
    public function testSaveWithZeroValues(): void
    {
        try {
            $lineItemHistory = new BorrowerDrawLineItemHistory();
            $data = [
                'recordId' => 456,
                'lineItemId' => 789,
                'completedAmount' => 0.0,
                'completedPercent' => 0.0,
                'requestedAmount' => 0.0,
                'disbursedAmount' => 0.0
            ];

            $result = $lineItemHistory->save($data);
            $this->assertIsArray($result);
        } catch (\Error|\Exception $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests save method with null notes.
     */
    public function testSaveWithNullNotes(): void
    {
        try {
            $lineItemHistory = new BorrowerDrawLineItemHistory();
            $data = [
                'recordId' => 456,
                'lineItemId' => 789,
                'requestedAmount' => 300.0,
                'notes' => null,
                'lenderNotes' => null
            ];

            $result = $lineItemHistory->save($data);
            $this->assertIsArray($result);
        } catch (\Error|\Exception $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests save method with empty string notes.
     */
    public function testSaveWithEmptyStringNotes(): void
    {
        try {
            $lineItemHistory = new BorrowerDrawLineItemHistory();
            $data = [
                'recordId' => 456,
                'lineItemId' => 789,
                'requestedAmount' => 300.0,
                'notes' => '',
                'lenderNotes' => ''
            ];

            $result = $lineItemHistory->save($data);
            $this->assertIsArray($result);
        } catch (\Error|\Exception $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests save method with special characters in notes.
     */
    public function testSaveWithSpecialCharactersInNotes(): void
    {
        try {
            $lineItemHistory = new BorrowerDrawLineItemHistory();
            $data = [
                'recordId' => 456,
                'lineItemId' => 789,
                'requestedAmount' => 300.0,
                'notes' => 'Notes with "quotes" & symbols <html>',
                'lenderNotes' => 'Lender notes with émojis 🏠 and special chars: @#$%'
            ];

            $result = $lineItemHistory->save($data);
            $this->assertIsArray($result);
        } catch (\Error|\Exception $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests save method with large amounts.
     */
    public function testSaveWithLargeAmounts(): void
    {
        try {
            $lineItemHistory = new BorrowerDrawLineItemHistory();
            $data = [
                'recordId' => 456,
                'lineItemId' => 789,
                'completedAmount' => 999999.99,
                'completedPercent' => 100.0,
                'requestedAmount' => 500000.0,
                'disbursedAmount' => 750000.0
            ];

            $result = $lineItemHistory->save($data);
            $this->assertIsArray($result);
        } catch (\Error|\Exception $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests save method preserves existing values when not provided.
     */
    public function testSavePreservesExistingValues(): void
    {
        try {
            $lineItemHistory = new BorrowerDrawLineItemHistory();


            $lineItemHistory->completedAmount = 500.0;
            $lineItemHistory->notes = 'Initial notes';

            $data = [
                'recordId' => 456,
                'lineItemId' => 789,
                'requestedAmount' => 300.0
            ];

            $result = $lineItemHistory->save($data);
            $this->assertIsArray($result);
        } catch (\Error|\Exception $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests toArray with null values.
     */
    public function testToArrayWithNullValues(): void
    {
        try {
            $lineItemHistory = new BorrowerDrawLineItemHistory();

            $result = $lineItemHistory->toArray();

            $this->assertIsArray($result);
            $this->assertArrayHasKey('id', $result);
            $this->assertArrayHasKey('recordId', $result);
            $this->assertArrayHasKey('lineItemId', $result);
            $this->assertArrayHasKey('completedAmount', $result);
            $this->assertArrayHasKey('completedPercent', $result);
            $this->assertArrayHasKey('requestedAmount', $result);
            $this->assertArrayHasKey('disbursedAmount', $result);
            $this->assertArrayHasKey('notes', $result);
            $this->assertArrayHasKey('lenderNotes', $result);
            $this->assertArrayHasKey('createdAt', $result);

            $this->assertNull($result['id']);
            $this->assertNull($result['recordId']);
            $this->assertNull($result['lineItemId']);
            $this->assertEquals(0.0, $result['completedAmount']);
            $this->assertEquals(0.0, $result['completedPercent']);
            $this->assertEquals(0.0, $result['requestedAmount']);
            $this->assertEquals(0.0, $result['disbursedAmount']);
            $this->assertNull($result['notes']);
            $this->assertNull($result['lenderNotes']);
            $this->assertNull($result['createdAt']);
        } catch (\Error|\Exception $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests save method with string numeric values.
     */
    public function testSaveWithStringNumericValues(): void
    {
        try {
            $lineItemHistory = new BorrowerDrawLineItemHistory();
            $data = [
                'recordId' => '456',
                'lineItemId' => '789',
                'completedAmount' => '500.0',
                'completedPercent' => '50.0',
                'requestedAmount' => '300.0',
                'disbursedAmount' => '200.0'
            ];

            $result = $lineItemHistory->save($data);
            $this->assertIsArray($result);
        } catch (\Error|\Exception $e) {

            $this->addToAssertionCount(1);
        }
    }
}
