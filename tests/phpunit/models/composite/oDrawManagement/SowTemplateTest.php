<?php

namespace tests\models\composite\oDrawManagement;

use models\composite\oDrawManagement\SowTemplate;
use models\composite\oDrawManagement\SowCategory;
use models\lendingwise\tblProcessingCompanyDrawTemplateSettings;
use tests\models\composite\oDrawManagement\MockDataHelper;
use PHPUnit\Framework\TestCase;

/**
 * Tests the SowTemplate class.
 *
 * @covers \models\composite\oDrawManagement\SowTemplate::__construct
 * @covers \models\composite\oDrawManagement\SowTemplate::addCategory
 * @covers \models\composite\oDrawManagement\SowTemplate::toArray
 * @covers \models\composite\oDrawManagement\SowTemplate::save
 * @covers \models\composite\oDrawManagement\SowTemplate::getAllCategories
 * @covers \models\composite\oDrawManagement\SowTemplate::getCategoryById
 * @covers \models\composite\oDrawManagement\SowTemplate::deleteCategories
 * @covers \models\composite\oDrawManagement\SowTemplate::deleteLineItems
 */
class SowTemplateTest extends TestCase
{
    /**
     * Tests the constructor with a database object.
     */
    public function testConstructorWithDbObject(): void
    {
        $mockTemplate = MockDataHelper::createMockSowTemplate(123, 456);

        $this->assertIsObject($mockTemplate);
        $this->assertEquals(123, $mockTemplate->id);
        $this->assertEquals(456, $mockTemplate->PCID);
        $this->assertEquals('Test SOW Template', $mockTemplate->templateName);
        $this->assertIsString($mockTemplate->templateData);
        $this->assertIsArray($mockTemplate->categories);
    }

    /**
     * Tests the addCategory method.
     */
    public function testAddCategory(): void
    {
        $mockTemplate = MockDataHelper::createMockSowTemplate(123, 456);
        $mockCategory = MockDataHelper::createMockSowCategory(789);
        $mockCategory->categoryName = 'Test Category';

        $mockTemplate->addCategory($mockCategory);

        $this->assertCount(1, $mockTemplate->getAllCategories());
        $this->assertEquals($mockCategory, $mockTemplate->getCategoryById(789));
    }

    /**
     * Tests the getAllCategories method.
     */
    public function testGetAllCategories(): void
    {
        $mockTemplate = MockDataHelper::createMockSowTemplate(123, 456);

        $result = $mockTemplate->getAllCategories();

        $this->assertIsArray($result);
        $this->assertEmpty($result); // Initially empty
    }

    /**
     * Tests the getCategoryById method with non-existent category.
     */
    public function testGetCategoryByIdNonExistent(): void
    {
        $mockTemplate = MockDataHelper::createMockSowTemplate(123, 456);

        $result = $mockTemplate->getCategoryById(999);

        $this->assertNull($result);
    }

    /**
     * Tests the getCategoryById method with existing category.
     */
    public function testGetCategoryByIdExisting(): void
    {
        $mockTemplate = MockDataHelper::createMockSowTemplate(123, 456);
        $mockCategory = MockDataHelper::createMockSowCategory(789);
        $mockCategory->categoryName = 'Test Category';

        $mockTemplate->addCategory($mockCategory);

        $result = $mockTemplate->getCategoryById(789);

        $this->assertIsObject($result);
        $this->assertEquals('Test Category', $result->categoryName);
    }

    /**
     * Tests the toArray method.
     */
    public function testToArray(): void
    {
        $mockTemplate = MockDataHelper::createMockSowTemplate(123, 456);

        $result = $mockTemplate->toArray();

        $this->assertIsArray($result);
        $this->assertEquals(123, $result['id']);
        $this->assertEquals(456, $result['PCID']);
        $this->assertEquals('Test SOW Template', $result['templateName']);
        $this->assertIsString($result['templateData']);
    }

    /**
     * Tests the save method.
     */
    public function testSave(): void
    {
        $mockTemplate = MockDataHelper::createMockSowTemplate(123, 456);

        $data = [
            'allowBorrowersAddEditCategories' => 1,
            'allowBorrowersDeleteCategories' => 0,
            'allowBorrowersAddEditLineItems' => 1,
            'allowBorrowersDeleteLineItems' => 0,
            'allowBorrowersSOWRevisions' => 1,
            'allowBorrowersExceedFinancedRehabCostOnRevision' => 0,
            'drawFee' => 75.50
        ];

        $result = $mockTemplate->save($data);
        $this->assertIsArray($result);
        $this->assertTrue($result['success']);
        $this->assertEquals(123, $result['id']);
    }

    /**
     * Tests the save method with missing data.
     */
    public function testSaveWithMissingData(): void
    {
        $mockTemplate = MockDataHelper::createMockSowTemplate(123, 456);

        $data = [];

        $result = $mockTemplate->save($data);
        $this->assertIsArray($result);
        $this->assertTrue($result['success']);
    }

    /**
     * Tests the deleteCategories method with empty array.
     */
    public function testDeleteCategoriesEmpty(): void
    {
        $mockTemplate = MockDataHelper::createMockSowTemplate(123, 456);

        $result = $mockTemplate->deleteCategories([]);
        $this->assertTrue($result);
    }

    /**
     * Tests the deleteCategories method with non-existent IDs.
     */
    public function testDeleteCategoriesNonExistent(): void
    {
        $mockTemplate = MockDataHelper::createMockSowTemplate(123, 456);

        $result = $mockTemplate->deleteCategories([999, 888]);
        $this->assertTrue($result);
    }

    /**
     * Tests the deleteLineItems method with empty array.
     */
    public function testDeleteLineItemsEmpty(): void
    {
        $mockTemplate = MockDataHelper::createMockSowTemplate(123, 456);

        $result = $mockTemplate->deleteLineItems([]);
        $this->assertTrue($result);
    }

    /**
     * Tests the deleteLineItems method with non-existent IDs.
     */
    public function testDeleteLineItemsNonExistent(): void
    {
        $mockTemplate = MockDataHelper::createMockSowTemplate(123, 456);

        $result = $mockTemplate->deleteLineItems([999, 888]);
        $this->assertTrue($result);
    }

    /**
     * Tests property initialization.
     */
    public function testPropertyInitialization(): void
    {
        $mockTemplate = MockDataHelper::createMockSowTemplate(123, 456);

        $this->assertEquals(123, $mockTemplate->id);
        $this->assertEquals(456, $mockTemplate->PCID);
        $this->assertEquals('Test SOW Template', $mockTemplate->templateName);
        $this->assertIsString($mockTemplate->templateData);
        $this->assertIsArray($mockTemplate->categories);
    }

    /**
     * Tests save method with string values that should be converted.
     */
    public function testSaveWithStringValues(): void
    {
        $mockTemplate = MockDataHelper::createMockSowTemplate(123, 456);

        $data = [
            'allowBorrowersAddEditCategories' => '1',
            'allowBorrowersDeleteCategories' => '0',
            'allowBorrowersSOWRevisions' => '1',
            'allowBorrowersExceedFinancedRehabCostOnRevision' => '0',
            'drawFee' => '75.50'
        ];

        $result = $mockTemplate->save($data);
        $this->assertIsArray($result);
        $this->assertTrue($result['success']);
    }
}
