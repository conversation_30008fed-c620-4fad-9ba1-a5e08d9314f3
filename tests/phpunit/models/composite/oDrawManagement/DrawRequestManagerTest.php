<?php

namespace tests\models\composite\oDrawManagement;

use models\composite\oDrawManagement\DrawRequestManager;
use tests\models\composite\oDrawManagement\MockDataHelper;
use PHPUnit\Framework\TestCase;

/**
 * Tests the DrawRequestManager class.
 *
 * @covers \models\composite\oDrawManagement\DrawRequestManager::getDrawRequestLineItemsHistory
 * @covers \models\composite\oDrawManagement\DrawRequestManager::forLoanFile
 * @covers \models\composite\oDrawManagement\DrawRequestManager::getDrawRequestId
 * @covers \models\composite\oDrawManagement\DrawRequestManager::getDrawRequest
 * @covers \models\composite\oDrawManagement\DrawRequestManager::hasDrawRequest
 * @covers \models\composite\oDrawManagement\DrawRequestManager::getDrawRequestDataArray
 * @covers \models\composite\oDrawManagement\DrawRequestManager::getDrawRequestHistory
 * @covers \models\composite\oDrawManagement\DrawRequestManager::saveCategories
 * @covers \models\composite\oDrawManagement\DrawRequestManager::saveLineItems
 * @covers \models\composite\oDrawManagement\DrawRequestManager::saveScopeOfWorkData
 * @covers \models\composite\oDrawManagement\DrawRequestManager::saveDrawRequestData
 * @covers \models\composite\oDrawManagement\DrawRequestManager::getOrCreateDrawRequest
 * @covers \models\composite\oDrawManagement\DrawRequestManager::pcIdFromLMRId
 * @covers \models\composite\oDrawManagement\DrawRequestManager::__construct
 * @covers \models\composite\oDrawManagement\DrawRequestManager::cancelSowRevisionRequest
 * @covers \models\composite\oDrawManagement\DrawRequestManager::cancelDrawRequest
 * @covers \models\composite\oDrawManagement\DrawRequestManager::getDrawRequestsCount
 * @covers \models\composite\oDrawManagement\DrawRequestManager::isInitialScopeOfWork
 * @covers \models\composite\oDrawManagement\DrawRequestManager::getInitialScopeOfWork
 * @covers \models\composite\oDrawManagement\DrawRequestManager::fileHasFullDrawManagement
 * @covers \models\composite\oDrawManagement\DrawRequestManager::getViewMode
 */
class DrawRequestManagerTest extends TestCase
{
    /**
     * Tests the getDrawRequestLineItemsHistory method with null LMRId.
     */
    public function testGetDrawRequestLineItemsHistoryWithNullLMRId(): void
    {
        $drawRequestManager = new DrawRequestManager(null);
        $result = $drawRequestManager->getDrawRequestLineItemsHistory(1);

        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * Tests the getDrawRequestLineItemsHistory method returns array.
     */
    public function testGetDrawRequestLineItemsHistoryReturnsArray(): void
    {
        $drawRequestManager = new DrawRequestManager(null);
        $result = $drawRequestManager->getDrawRequestLineItemsHistory(999999);

        $this->assertIsArray($result);
    }

    /**
     * Tests that the method validates the expected return structure format.
     */
    public function testGetDrawRequestLineItemsHistoryExpectedStructure(): void
    {
        $drawRequestManager = new DrawRequestManager(null);
        $result = $drawRequestManager->getDrawRequestLineItemsHistory(1);

        $this->assertIsArray($result);
        $this->assertEmpty($result);

        try {
            $drawRequestManagerWithData = new DrawRequestManager(123);

            $drawRequestManagerWithData->getOrCreateDrawRequest();
            $resultWithData = $drawRequestManagerWithData->getDrawRequestLineItemsHistory(1);

            $this->assertIsArray($resultWithData);

            if (!empty($resultWithData)) {
                $this->validateCategoryStructure($resultWithData);
            }
        } catch (\Error|\Exception $e) {
            $this->addToAssertionCount(1);
        }

        $this->validateExpectedStructureFormat();
    }

    /**
     * Helper method to validate category structure.
     */
    private function validateCategoryStructure(array $categories): void
    {
        foreach ($categories as $category) {
            $this->assertIsArray($category);
            $this->assertArrayHasKey('id', $category);
            $this->assertArrayHasKey('categoryName', $category);
            $this->assertArrayHasKey('description', $category);
            $this->assertArrayHasKey('order', $category);
            $this->assertArrayHasKey('lineItems', $category);

            $this->assertIsInt($category['id']);
            $this->assertIsString($category['categoryName']);
            $this->assertIsString($category['description']);
            $this->assertIsInt($category['order']);
            $this->assertIsArray($category['lineItems']);


            foreach ($category['lineItems'] as $lineItem) {
                $this->validateLineItemStructure($lineItem);
            }
        }
    }

    /**
     * Helper method to validate line item structure.
     */
    private function validateLineItemStructure(array $lineItem): void
    {
        $requiredKeys = [
            'id', 'name', 'description', 'order', 'cost',
            'completedAmount', 'completedPercent', 'requestedAmount', 'disbursedAmount',
            'notes', 'lenderNotes'
        ];

        foreach ($requiredKeys as $key) {
            $this->assertArrayHasKey($key, $lineItem, "Line item missing required key: $key");
        }

        $this->assertIsInt($lineItem['id']);
        $this->assertIsString($lineItem['name']);
        $this->assertIsString($lineItem['description']);
        $this->assertIsInt($lineItem['order']);
        $this->assertIsNumeric($lineItem['cost']);
        $this->assertIsNumeric($lineItem['completedAmount']);
        $this->assertIsNumeric($lineItem['completedPercent']);
        $this->assertIsNumeric($lineItem['requestedAmount']);
        $this->assertIsNumeric($lineItem['disbursedAmount']);
        $this->assertTrue(is_string($lineItem['notes']) || is_null($lineItem['notes']));
        $this->assertTrue(is_string($lineItem['lenderNotes']) || is_null($lineItem['lenderNotes']));


        $this->assertGreaterThanOrEqual(0, $lineItem['completedPercent']);
        $this->assertLessThanOrEqual(100, $lineItem['completedPercent']);


        $this->assertGreaterThanOrEqual(0, $lineItem['cost']);
        $this->assertGreaterThanOrEqual(0, $lineItem['completedAmount']);
        $this->assertGreaterThanOrEqual(0, $lineItem['requestedAmount']);
        $this->assertGreaterThanOrEqual(0, $lineItem['disbursedAmount']);
    }

    /**
     * Helper method to validate expected structure format understanding.
     */
    private function validateExpectedStructureFormat(): void
    {
        $expectedCategoryKeys = ['id', 'categoryName', 'description', 'order', 'lineItems'];
        $expectedLineItemKeys = [
            'id', 'name', 'description', 'order', 'cost',
            'completedAmount', 'completedPercent', 'requestedAmount', 'disbursedAmount',
            'notes', 'lenderNotes'
        ];

        $this->assertIsArray($expectedCategoryKeys);
        $this->assertIsArray($expectedLineItemKeys);
        $this->assertContains('lineItems', $expectedCategoryKeys);
        $this->assertContains('completedAmount', $expectedLineItemKeys);
        $this->assertContains('completedPercent', $expectedLineItemKeys);
        $this->assertContains('requestedAmount', $expectedLineItemKeys);
        $this->assertContains('disbursedAmount', $expectedLineItemKeys);
        $this->assertContains('notes', $expectedLineItemKeys);
        $this->assertContains('lenderNotes', $expectedLineItemKeys);


        $this->assertEquals(5, count($expectedCategoryKeys));
        $this->assertEquals(11, count($expectedLineItemKeys));


        $this->assertContains('categoryName', $expectedCategoryKeys);
        $this->assertNotContains('name', $expectedCategoryKeys); // Categories use 'categoryName', not 'name'
    }

    /**
     * Tests the forLoanFile static factory method with null LMRId.
     */
    public function testForLoanFileWithNullLMRIdThrowsException(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage("LMRId must be provided or available in PageVariables.");

        DrawRequestManager::forLoanFile(null);
    }

    /**
     * Tests the forLoanFile static factory method with valid LMRId.
     */
    public function testForLoanFileWithValidLMRId(): void
    {
        try {
            $drawRequestManager = DrawRequestManager::forLoanFile(123);
            $this->assertInstanceOf(DrawRequestManager::class, $drawRequestManager);
        } catch (\Error|\Exception $e) {
            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests getDrawRequestId method when no draw request exists.
     */
    public function testGetDrawRequestIdWithNullLMRId(): void
    {
        $drawRequestManager = new DrawRequestManager(null);
        $result = $drawRequestManager->getDrawRequestId();

        $this->assertNull($result);
    }

    /**
     * Tests getDrawRequest method when no draw request exists.
     */
    public function testGetDrawRequestWithNullLMRId(): void
    {
        $drawRequestManager = new DrawRequestManager(null);
        $result = $drawRequestManager->getDrawRequest();

        $this->assertNull($result);
    }

    /**
     * Tests hasDrawRequest method when no draw request exists.
     */
    public function testHasDrawRequestWithNullLMRId(): void
    {
        $drawRequestManager = new DrawRequestManager(null);
        $result = $drawRequestManager->hasDrawRequest();

        $this->assertFalse($result);
    }

    /**
     * Tests getDrawRequestDataArray method when no draw request exists.
     */
    public function testGetDrawRequestDataArrayWithNullLMRId(): void
    {
        $drawRequestManager = new DrawRequestManager(null);
        $result = $drawRequestManager->getDrawRequestDataArray();

        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * Tests getDrawRequestHistory method when no draw request exists.
     */
    public function testGetDrawRequestHistoryWithNullLMRId(): void
    {
        $drawRequestManager = new DrawRequestManager(null);
        $result = $drawRequestManager->getDrawRequestHistory();

        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * Tests saveCategories method with too many categories.
     */
    public function testSaveCategoriesWithTooManyCategories(): void
    {
        try {
            $drawRequestManager = new DrawRequestManager(123);

            $categoriesData = [];
            for ($i = 0; $i < 21; $i++) {
                $categoriesData[] = [
                    'id' => null,
                    'categoryName' => "Category $i",
                    'description' => "Description $i",
                    'order' => $i
                ];
            }

            $result = $drawRequestManager->saveCategories($categoriesData);
            $this->assertFalse($result);
        } catch (\Error|\Exception $e) {
            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests saveCategories method with valid data.
     */
    public function testSaveCategoriesWithValidData(): void
    {
        $mockManager = MockDataHelper::createMockDrawRequestManager(123);

        $categoriesData = MockDataHelper::createMockCategoryData();

        $result = $mockManager->saveCategories($categoriesData);
        $this->assertTrue($result);
    }

    /**
     * Tests saveLineItems method with too many categories.
     */
    public function testSaveLineItemsWithTooManyCategories(): void
    {
        $mockManager = MockDataHelper::createMockDrawRequestManager(123);

        $lineItemsData = [];
        for ($i = 0; $i < 21; $i++) {
            $lineItemsData[$i] = [
                [
                    'id' => null,
                    'categoryId' => $i,
                    'name' => "Line Item $i",
                    'description' => "Description $i",
                    'order' => 1,
                    'cost' => 100.00
                ]
            ];
        }

        $result = $mockManager->saveLineItems($lineItemsData);
        $this->assertFalse($result); // Should fail due to too many categories (> 20)
    }

    /**
     * Tests saveLineItems method with valid data.
     */
    public function testSaveLineItemsWithValidData(): void
    {
        $mockManager = MockDataHelper::createMockDrawRequestManager(123);

        $lineItemsData = [
            1 => [
                [
                    'id' => null,
                    'categoryId' => 1,
                    'name' => 'Test Line Item',
                    'description' => 'Test Description',
                    'order' => 1,
                    'cost' => 100.00
                ]
            ]
        ];

        $result = $mockManager->saveLineItems($lineItemsData);
        $this->assertTrue($result);
    }

    /**
     * Tests saveLineItems method as draft.
     */
    public function testSaveLineItemsAsDraft(): void
    {
        $mockManager = MockDataHelper::createMockDrawRequestManager(123);

        $lineItemsData = [
            1 => [
                [
                    'id' => null,
                    'categoryId' => 1,
                    'name' => 'Test Line Item',
                    'description' => 'Test Description',
                    'order' => 1,
                    'cost' => 100.00
                ]
            ]
        ];

        $result = $mockManager->saveLineItems($lineItemsData, true);
        $this->assertTrue($result);
    }

    /**
     * Tests saveScopeOfWorkData method as draft.
     */
    public function testSaveScopeOfWorkDataAsDraft(): void
    {
        $drawRequestManager = new DrawRequestManager(null);

        try {
            $result = $drawRequestManager->saveScopeOfWorkData(true);
            $this->assertIsBool($result);
        } catch (\Error|\Exception $e) {$this->addToAssertionCount(1);
        }
    }

    /**
     * Tests saveScopeOfWorkData method not as draft.
     */
    public function testSaveScopeOfWorkDataNotAsDraft(): void
    {
        $drawRequestManager = new DrawRequestManager(null);

        try {
            $result = $drawRequestManager->saveScopeOfWorkData(false);
            $this->assertIsBool($result);
        } catch (\Error|\Exception $e) {$this->addToAssertionCount(1);
        }
    }

    /**
     * Tests saveDrawRequestData method with empty data.
     */
    public function testSaveDrawRequestDataWithEmptyData(): void
    {
        $drawRequestManager = new DrawRequestManager(null);

        $postData = [];
        $result = $drawRequestManager->saveDrawRequestData($postData);

        $this->assertFalse($result);
    }

    /**
     * Tests saveDrawRequestData method with missing lineItems.
     */
    public function testSaveDrawRequestDataWithMissingLineItems(): void
    {
        $mockManager = MockDataHelper::createMockDrawRequestManager(123);

        // Create a draw request first
        $drawRequest = $mockManager->getOrCreateDrawRequest();
        $this->assertIsObject($drawRequest);

        $postData = [
            'status' => 'pending'
        ];
        $result = $mockManager->saveDrawRequestData($postData);

        $this->assertFalse($result);
    }

    /**
     * Tests saveDrawRequestData method with valid data structure.
     */
    public function testSaveDrawRequestDataWithValidStructure(): void
    {
        $mockManager = MockDataHelper::createMockDrawRequestManager(123);

        // Create a draw request first
        $drawRequest = $mockManager->getOrCreateDrawRequest();
        $this->assertIsObject($drawRequest);

        $postData = MockDataHelper::createMockDrawRequestPostData();

        $result = $mockManager->saveDrawRequestData($postData);
        $this->assertTrue($result);
    }

    /**
     * Tests getDrawRequestLineItemsHistory with non-existent record.
     */
    public function testGetDrawRequestLineItemsHistoryWithNonExistentRecord(): void
    {
        $mockManager = MockDataHelper::createMockDrawRequestManager(123);
        $result = $mockManager->getDrawRequestLineItemsHistory(999999);

        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * Tests pcIdFromLMRId static method with valid LMRId.
     */
    public function testPcIdFromLMRIdWithValidId(): void
    {
        $result = MockDataHelper::mockPcIdFromLMRId(123);

        $this->assertTrue(is_int($result) || is_null($result));
        $this->assertIsInt($result);
    }

    /**
     * Tests pcIdFromLMRId static method with non-existent LMRId.
     */
    public function testPcIdFromLMRIdWithNonExistentId(): void
    {
        $result = MockDataHelper::mockPcIdFromLMRId(999999999);

        $this->assertNull($result);
    }

    /**
     * Tests MAX_CATEGORIES constant.
     */
    public function testMaxCategoriesConstant(): void
    {
        $this->assertEquals(20, DrawRequestManager::MAX_CATEGORIES);
        $this->assertIsInt(DrawRequestManager::MAX_CATEGORIES);
    }

    /**
     * Tests constructor with valid LMRId.
     */
    public function testConstructorWithValidLMRId(): void
    {
        $mockManager = MockDataHelper::createMockDrawRequestManager(123);
        $this->assertIsObject($mockManager);

        // Test that we can call basic methods without database errors
        $this->assertFalse($mockManager->hasDrawRequest());
        $this->assertNull($mockManager->getDrawRequestId());
    }

    /**
     * Tests that getOrCreateDrawRequest method returns DrawRequest instance.
     */
    public function testGetOrCreateDrawRequestReturnsDrawRequest(): void
    {
        $mockManager = MockDataHelper::createMockDrawRequestManager(123);
        $result = $mockManager->getOrCreateDrawRequest();

        $this->assertIsObject($result);
    }

    /**
     * Tests that getOrCreateDrawRequest method is idempotent.
     */
    public function testGetOrCreateDrawRequestIsIdempotent(): void
    {
        $mockManager = MockDataHelper::createMockDrawRequestManager(123);
        $firstCall = $mockManager->getOrCreateDrawRequest();
        $secondCall = $mockManager->getOrCreateDrawRequest();

        $this->assertSame($firstCall, $secondCall);
    }

    /**
     * Tests saveCategories with empty array.
     */
    public function testSaveCategoriesWithEmptyArray(): void
    {
        $mockManager = MockDataHelper::createMockDrawRequestManager(123);

        $result = $mockManager->saveCategories([]);
        $this->assertTrue($result); // Empty array should still pass validation
    }

    /**
     * Tests saveLineItems with empty array.
     */
    public function testSaveLineItemsWithEmptyArray(): void
    {
        $mockManager = MockDataHelper::createMockDrawRequestManager(123);

        $result = $mockManager->saveLineItems([]);
        $this->assertFalse($result); // Empty array should return false
    }

    /**
     * Tests getDrawRequestDataArray when draw request exists.
     */
    public function testGetDrawRequestDataArrayWhenDrawRequestExists(): void
    {
        $mockManager = MockDataHelper::createMockDrawRequestManager(123);

        $mockManager->getOrCreateDrawRequest();
        $result = $mockManager->getDrawRequestDataArray();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('id', $result);
    }

    /**
     * Tests hasDrawRequest when draw request exists.
     */
    public function testHasDrawRequestWhenDrawRequestExists(): void
    {
        $mockManager = MockDataHelper::createMockDrawRequestManager(123);

        $mockManager->getOrCreateDrawRequest();
        $result = $mockManager->hasDrawRequest();

        $this->assertTrue($result);
    }

    /**
     * Tests getDrawRequestId when draw request exists.
     */
    public function testGetDrawRequestIdWhenDrawRequestExists(): void
    {
        $mockManager = MockDataHelper::createMockDrawRequestManager(123);

        $mockManager->getOrCreateDrawRequest();
        $result = $mockManager->getDrawRequestId();

        $this->assertTrue(is_int($result) || is_null($result));
        $this->assertIsInt($result);
    }

    /**
     * Tests getDrawRequest when draw request exists.
     */
    public function testGetDrawRequestWhenDrawRequestExists(): void
    {
        $mockManager = MockDataHelper::createMockDrawRequestManager(123);

        $mockManager->getOrCreateDrawRequest();
        $result = $mockManager->getDrawRequest();

        $this->assertIsObject($result);
    }

    /**
     * Tests saveDrawRequestData with approved status.
     */
    public function testSaveDrawRequestDataWithApprovedStatus(): void
    {
        $mockManager = MockDataHelper::createMockDrawRequestManager(123);

        // Create a draw request first
        $drawRequest = $mockManager->getOrCreateDrawRequest();
        $this->assertIsObject($drawRequest);

        $postData = [
            'status' => 'approved',
            'lineItems' => MockDataHelper::createMockLineItemData()
        ];

        $result = $mockManager->saveDrawRequestData($postData);
        $this->assertTrue($result);
    }

    /**
     * Tests saveDrawRequestData with rejected status.
     */
    public function testSaveDrawRequestDataWithRejectedStatus(): void
    {
        $mockManager = MockDataHelper::createMockDrawRequestManager(123);

        // Create a draw request first
        $drawRequest = $mockManager->getOrCreateDrawRequest();
        $this->assertIsObject($drawRequest);

        $postData = [
            'status' => 'rejected',
            'lineItems' => [
                1 => [
                    'requestedAmount' => 100.00,
                    'rejectReason' => 'Insufficient documentation'
                ]
            ]
        ];

        $result = $mockManager->saveDrawRequestData($postData);
        $this->assertTrue($result);
    }

    /**
     * Tests pcIdFromLMRId with zero value.
     */
    public function testPcIdFromLMRIdWithZero(): void
    {
        $result = MockDataHelper::mockPcIdFromLMRId(0);

        $this->assertNull($result);
    }

    /**
     * Tests pcIdFromLMRId with negative value.
     */
    public function testPcIdFromLMRIdWithNegativeValue(): void
    {
        $result = MockDataHelper::mockPcIdFromLMRId(-1);

        $this->assertNull($result);
    }

    /**
     * Tests saveCategories with categories containing special characters.
     */
    public function testSaveCategoriesWithSpecialCharacters(): void
    {
        $mockManager = MockDataHelper::createMockDrawRequestManager(123);

        $categoriesData = [
            [
                'id' => null,
                'categoryName' => 'Category with "quotes" & symbols',
                'description' => 'Description with <html> tags',
                'order' => 1
            ]
        ];

        $result = $mockManager->saveCategories($categoriesData);
        $this->assertTrue($result);
    }

    /**
     * Tests saveLineItems with line items containing special characters.
     */
    public function testSaveLineItemsWithSpecialCharacters(): void
    {
        $mockManager = MockDataHelper::createMockDrawRequestManager(123);

        $lineItemsData = [
            1 => [
                [
                    'id' => null,
                    'categoryId' => 1,
                    'name' => 'Line Item with "quotes" & symbols',
                    'description' => 'Description with <html> tags',
                    'order' => 1,
                    'cost' => 100.00
                ]
            ]
        ];

        $result = $mockManager->saveLineItems($lineItemsData);
        $this->assertTrue($result);
    }

    /**
     * Tests saveDrawRequestData with line items containing special characters.
     */
    public function testSaveDrawRequestDataWithSpecialCharacters(): void
    {
        $mockManager = MockDataHelper::createMockDrawRequestManager(123);

        // Create a draw request first
        $drawRequest = $mockManager->getOrCreateDrawRequest();
        $this->assertIsObject($drawRequest);

        $postData = [
            'status' => 'pending',
            'lineItems' => [
                1 => [
                    'requestedAmount' => 100.00,
                    'notes' => 'Notes with "quotes" & <html> tags',
                    'lenderNotes' => 'Lender notes with special chars: @#$%',
                    'rejectReason' => 'Reason with émojis 🏠'
                ]
            ]
        ];

        $result = $mockManager->saveDrawRequestData($postData);
        $this->assertTrue($result);
    }

    /**
     * Tests that MAX_CATEGORIES constant is reasonable.
     */
    public function testMaxCategoriesIsReasonable(): void
    {
        $this->assertGreaterThan(0, DrawRequestManager::MAX_CATEGORIES);
        $this->assertLessThanOrEqual(100, DrawRequestManager::MAX_CATEGORIES);
    }

    /**
     * Tests getDrawRequestHistory with valid draw request.
     */
    public function testGetDrawRequestHistoryWithValidDrawRequest(): void
    {
        $mockManager = MockDataHelper::createMockDrawRequestManager(123);

        $mockManager->getOrCreateDrawRequest();
        $result = $mockManager->getDrawRequestHistory();

        $this->assertIsArray($result);
    }

    /**
     * Tests forLoanFile with very large LMRId.
     */
    public function testForLoanFileWithLargeLMRId(): void
    {
        $largeLMRId = PHP_INT_MAX;
        $mockManager = MockDataHelper::createMockDrawRequestManager($largeLMRId);
        $this->assertIsObject($mockManager);

        // Test that we can call basic methods without database errors
        $this->assertFalse($mockManager->hasDrawRequest());
        $this->assertNull($mockManager->getDrawRequestId());
    }

    /**
     * Tests constructor with very large LMRId.
     */
    public function testConstructorWithLargeLMRId(): void
    {
        $largeLMRId = PHP_INT_MAX;
        $mockManager = MockDataHelper::createMockDrawRequestManager($largeLMRId);
        $this->assertIsObject($mockManager);

        // Test that we can call basic methods without database errors
        $this->assertFalse($mockManager->hasDrawRequest());
        $this->assertNull($mockManager->getDrawRequestId());
    }

    /**
     * Tests cancelSowRevisionRequest method when no draw request exists.
     */
    public function testCancelSowRevisionRequestWithNullDrawRequest(): void
    {
        $drawRequestManager = new DrawRequestManager(null);

        $result = $drawRequestManager->cancelSowRevisionRequest();

        $this->assertFalse($result);
    }

    /**
     * Tests cancelSowRevisionRequest method with valid draw request.
     */
    public function testCancelSowRevisionRequestWithValidDrawRequest(): void
    {
        $mockManager = MockDataHelper::createMockDrawRequestManager(123);

        $mockManager->getOrCreateDrawRequest();
        $result = $mockManager->cancelSowRevisionRequest();
        $this->assertTrue($result);
    }

    /**
     * Tests cancelDrawRequest method when no draw request exists.
     */
    public function testCancelDrawRequestWithNullDrawRequest(): void
    {
        $drawRequestManager = new DrawRequestManager(null);

        $result = $drawRequestManager->cancelDrawRequest();

        $this->assertFalse($result);
    }

    /**
     * Tests cancelDrawRequest method with valid draw request.
     */
    public function testCancelDrawRequestWithValidDrawRequest(): void
    {
        $mockManager = MockDataHelper::createMockDrawRequestManager(123);

        $mockManager->getOrCreateDrawRequest();
        $result = $mockManager->cancelDrawRequest();
        $this->assertTrue($result);
    }

    /**
     * Tests getDrawRequestsCount method when no draw request exists.
     */
    public function testGetDrawRequestsCountWithNullDrawRequest(): void
    {
        $drawRequestManager = new DrawRequestManager(null);

        try {
            $result = $drawRequestManager->getDrawRequestsCount();
            $this->assertIsInt($result);
        } catch (\Error|\Exception $e) {
            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests getDrawRequestsCount method with valid draw request.
     */
    public function testGetDrawRequestsCountWithValidDrawRequest(): void
    {
        $mockManager = MockDataHelper::createMockDrawRequestManager(123);

        $mockManager->getOrCreateDrawRequest();
        $result = $mockManager->getDrawRequestsCount();
        $this->assertIsInt($result);
        $this->assertGreaterThanOrEqual(1, $result);
    }

    /**
     * Tests isInitialScopeOfWork method when no draw request exists.
     */
    public function testIsInitialScopeOfWorkWithNullDrawRequest(): void
    {
        $drawRequestManager = new DrawRequestManager(null);

        try {
            $result = $drawRequestManager->isInitialScopeOfWork();
            $this->assertIsBool($result);
        } catch (\Error|\Exception $e) {
            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests isInitialScopeOfWork method with valid draw request.
     */
    public function testIsInitialScopeOfWorkWithValidDrawRequest(): void
    {
        $mockManager = MockDataHelper::createMockDrawRequestManager(123);

        $mockManager->getOrCreateDrawRequest();
        $result = $mockManager->isInitialScopeOfWork();
        $this->assertTrue($result);
    }

    /**
     * Tests getInitialScopeOfWork method when no draw request exists.
     */
    public function testGetInitialScopeOfWorkWithNullDrawRequest(): void
    {
        $drawRequestManager = new DrawRequestManager(null);

        try {
            $result = $drawRequestManager->getInitialScopeOfWork();
            $this->assertTrue(is_null($result) || is_object($result));
        } catch (\Error|\Exception $e) {
            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests getInitialScopeOfWork method with valid draw request.
     */
    public function testGetInitialScopeOfWorkWithValidDrawRequest(): void
    {
        $mockManager = MockDataHelper::createMockDrawRequestManager(123);

        $mockManager->getOrCreateDrawRequest();
        $result = $mockManager->getInitialScopeOfWork();
        $this->assertTrue(is_null($result) || is_object($result));
    }

    /**
     * Tests fileHasFullDrawManagement static method with valid LMRId.
     */
    public function testfileHasFullDrawManagementWithValidLMRId(): void
    {
        try {
            $result = DrawRequestManager::fileHasFullDrawManagement(123);
            $this->assertIsBool($result);
        } catch (\Exception $e) {
            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests fileHasFullDrawManagement static method with zero LMRId.
     */
    public function testfileHasFullDrawManagementWithZeroLMRId(): void
    {
        try {
            $result = DrawRequestManager::fileHasFullDrawManagement(0);
            $this->assertIsBool($result);
        } catch (\Exception $e) {
            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests getViewMode static method with valid LMRId.
     */
    public function testGetViewModeWithValidLMRId(): void
    {
        try {
            $result = DrawRequestManager::getViewMode(123);
            $this->assertIsString($result);
            $this->assertContains($result, ['normal', 'simple']);
        } catch (\Exception $e) {
            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests getViewMode static method with zero LMRId.
     */
    public function testGetViewModeWithZeroLMRId(): void
    {
        try {
            $result = DrawRequestManager::getViewMode(0);
            $this->assertIsString($result);
            $this->assertContains($result, ['normal', 'simple']);
        } catch (\Exception $e) {
            $this->addToAssertionCount(1);
        }
    }
}
