<?php

namespace tests\models\composite\oDrawManagement;

use models\lendingwise\tblFileDrawRequests;
use models\lendingwise\tblFile;
use models\lendingwise\tblDrawRequestCategories;
use models\lendingwise\tblDrawRequestLineItems;
use models\lendingwise\tblDrawRequests_h;
use models\lendingwise\tblDrawRequestLineItems_h;
use models\lendingwise\tblDrawSummaryCalculatedValues;
use models\lendingwise\tblFileSimpleDrawRequests;
use models\lendingwise\tblProcessingCompany;
use models\lendingwise\tblProcessingCompanyDrawTemplateSettings;
use models\Database2;
use PHPUnit\Framework\MockObject\MockObject;

/**
 * Helper class to create mock data for draw management tests
 */
class MockDataHelper
{
    /**
     * Mock static method for DrawRequestManager::pcIdFromLMRId
     */
    public static function mockPcIdFromLMRId(int $LMRId): ?int
    {
        if ($LMRId === ********* || $LMRId <= 0) {
            return null;
        }
        return 1041;
    }

    /**
     * Create a mock tblFileDrawRequests object
     */
    public static function createMockDrawRequest(int $id = 123, int $LMRId = 123): object
    {
        $mock = new class {
            public ?int $id = null;
            public ?int $LMRId = null;
            public string $status = 'new';
            public int $sowApproved = 0;
            public int $isDrawRequest = 0;
            public ?string $updatedAt = null;

            public function save(): array
            {
                if (!$this->id) {
                    $this->id = 123;
                }
                return ['success' => true, 'id' => $this->id];
            }

            public function delete(): bool
            {
                return true;
            }
        };

        $mock->id = $id;
        $mock->LMRId = $LMRId;
        $mock->updatedAt = date('Y-m-d H:i:s');

        return $mock;
    }

    /**
     * Create a mock tblFile object
     */
    public static function createMockFile(int $LMRId = 123): object
    {
        return new class($LMRId) {
            public int $LMRId;
            public string $borrowerName = 'Test Borrower';
            public int $brokerNumber = 1;
            public int $FBRID = 1;
            public int $FPCID = 1041;

            public function __construct(int $LMRId)
            {
                $this->LMRId = $LMRId;
            }
        };
    }

    /**
     * Create a mock Database2 instance
     */
    public static function createMockDatabase(): object
    {
        return new class {
            public function queryData($query = null, $params = null): array
            {
                return [];
            }

            public function executeQuery($query = null, $params = null): array
            {
                return ['affected_rows' => 1];
            }

            public function insert($table = null, $data = null): array
            {
                return ['success' => true, 'id' => 123];
            }

            public function update($table = null, $data = null, $where = null): array
            {
                return ['success' => true];
            }

            public function delete($table = null, $where = null): array
            {
                return ['success' => true];
            }

            public function beginTransaction(): bool
            {
                return true;
            }

            public function commit(): bool
            {
                return true;
            }

            public function rollback(): bool
            {
                return true;
            }
        };
    }

    /**
     * Create mock category data
     */
    public static function createMockCategoryData(): array
    {
        return [
            [
                'id' => null,
                'categoryName' => 'Test Category 1',
                'description' => 'Test Description 1',
                'order' => 1
            ],
            [
                'id' => null,
                'categoryName' => 'Test Category 2',
                'description' => 'Test Description 2',
                'order' => 2
            ]
        ];
    }

    /**
     * Create mock line item data
     */
    public static function createMockLineItemData(): array
    {
        return [
            1 => [
                'requestedAmount' => 100.00,
                'requestedPercent' => 50.0,
                'notes' => 'Test notes',
                'lenderNotes' => 'Test lender notes'
            ],
            2 => [
                'requestedAmount' => 200.00,
                'requestedPercent' => 75.0,
                'notes' => 'Test notes 2',
                'lenderNotes' => 'Test lender notes 2'
            ]
        ];
    }

    /**
     * Create mock draw request post data
     */
    public static function createMockDrawRequestPostData(): array
    {
        return [
            'status' => 'pending',
            'lineItems' => self::createMockLineItemData()
        ];
    }

    /**
     * Create a mock processing company
     */
    public static function createMockProcessingCompany(int $PCID = 1041): object
    {
        return new class($PCID) {
            public int $PCID;
            public string $PCName = 'Test Processing Company';

            public function __construct(int $PCID)
            {
                $this->PCID = $PCID;
            }
        };
    }

    /**
     * Create mock template settings
     */
    public static function createMockTemplateSettings(): object
    {
        return new class {
            public int $id = 1;
            public int $PCID = 1041;
            public string $templateData = '{"categories": [], "lineItems": []}';
        };
    }

    /**
     * Create mock calculated values
     */
    public static function createMockCalculatedValues(int $LMRId = 123): object
    {
        return new class($LMRId) {
            public int $LMRId;
            public float $totalRehabCost = 10000.00;
            public float $totalRequestedAmount = 5000.00;
            public float $totalApprovedAmount = 4500.00;

            public function __construct(int $LMRId)
            {
                $this->LMRId = $LMRId;
            }

            public function save(): array
            {
                return ['success' => true];
            }
        };
    }

    /**
     * Create a mock draw request manager with stubbed dependencies
     */
    public static function createMockDrawRequestManager(int $LMRId = 123): object
    {
        return new class($LMRId) {
            private int $LMRId;
            private ?object $drawRequest = null;

            public function __construct(int $LMRId)
            {
                $this->LMRId = $LMRId;
            }

            public function getOrCreateDrawRequest(): object
            {
                if (!$this->drawRequest) {
                    $this->drawRequest = MockDataHelper::createMockDrawRequest(123, $this->LMRId);
                }
                return $this->drawRequest;
            }

            public function getDrawRequest(): ?object
            {
                return $this->drawRequest;
            }

            public function hasDrawRequest(): bool
            {
                return $this->drawRequest !== null;
            }

            public function getDrawRequestId(): ?int
            {
                return $this->drawRequest ? $this->drawRequest->id : null;
            }

            public function saveCategories(array $categories): bool
            {
                return count($categories) <= 20;
            }

            public function saveLineItems(array $lineItems, bool $isDraft = false): bool
            {
                $categoryIds = [];
                foreach ($lineItems as $categoryItems) {
                    foreach ($categoryItems as $item) {
                        if (isset($item['categoryId'])) {
                            $categoryIds[] = $item['categoryId'];
                        }
                    }
                }
                $uniqueCategories = array_unique($categoryIds);

                if (count($uniqueCategories) > 20) {
                    return false;
                }

                return !empty($lineItems);
            }

            public function saveDrawRequestData(array $postData): bool
            {
                return !empty($postData['lineItems']);
            }

            public function getDrawRequestDataArray(): array
            {
                return $this->drawRequest ? ['id' => $this->drawRequest->id] : [];
            }

            public function getDrawRequestHistory(): array
            {
                return [];
            }

            public function getDrawRequestLineItemsHistory(int $lineItemId): array
            {
                return [];
            }

            public static function forLoanFile(int $LMRId): self
            {
                return new self($LMRId);
            }

            public static function pcIdFromLMRId(int $LMRId): ?int
            {
                return 1041;
            }

            public function cancelSowRevisionRequest(): bool
            {
                return true;
            }

            public function cancelDrawRequest(): bool
            {
                return true;
            }

            public function getDrawRequestsCount(): int
            {
                return 1;
            }

            public function isInitialScopeOfWork(): bool
            {
                return true;
            }

            public function getInitialScopeOfWork(): ?object
            {
                return $this->drawRequest;
            }
        };
    }

    /**
     * Create a mock SowTemplate object
     */
    public static function createMockSowTemplate(int $id = 1, int $PCID = 1041): object
    {
        return new class($id, $PCID) {
            public int $id;
            public int $PCID;
            public string $templateName = 'Test SOW Template';
            public ?string $templateData = null;
            public array $categories = [];

            public function __construct(int $id, int $PCID)
            {
                $this->id = $id;
                $this->PCID = $PCID;
                $this->templateData = json_encode(['categories' => [], 'lineItems' => []]);
            }

            public function save(): array
            {
                return ['success' => true, 'id' => $this->id];
            }

            public function delete(): bool
            {
                return true;
            }

            public function addCategory(object $category): void
            {
                $this->categories[] = $category;
            }

            public function getAllCategories(): array
            {
                return $this->categories;
            }

            public function getCategoryById(int $id): ?object
            {
                foreach ($this->categories as $category) {
                    if ($category->id === $id) {
                        return $category;
                    }
                }
                return null;
            }

            public function toArray(): array
            {
                return [
                    'id' => $this->id,
                    'PCID' => $this->PCID,
                    'templateName' => $this->templateName,
                    'templateData' => $this->templateData
                ];
            }

            public function deleteCategories(array $categoryIds): bool
            {
                return true;
            }

            public function deleteLineItems(array $lineItemIds): bool
            {
                return true;
            }
        };
    }

    /**
     * Create a mock BorrowerDrawCategory object
     */
    public static function createMockBorrowerDrawCategory(int $id = 1): object
    {
        return new class($id) {
            public int $id;
            public string $categoryName = 'Test Category';
            public string $description = 'Test Description';
            public int $order = 1;
            public array $lineItems = [];

            public function __construct(int $id)
            {
                $this->id = $id;
            }

            public function save(): array
            {
                return ['success' => true, 'id' => $this->id];
            }

            public function delete(): bool
            {
                return true;
            }

            public function addLineItem(object $lineItem): void
            {
                $this->lineItems[] = $lineItem;
            }

            public function getAllLineItems(): array
            {
                return $this->lineItems;
            }

            public function toArray(): array
            {
                return [
                    'id' => $this->id,
                    'categoryName' => $this->categoryName,
                    'description' => $this->description,
                    'order' => $this->order
                ];
            }
        };
    }

    /**
     * Create a mock BorrowerDrawLineItem object
     */
    public static function createMockBorrowerDrawLineItem(int $id = 1): object
    {
        return new class($id) {
            public int $id;
            public int $categoryId = 1;
            public string $description = 'Test Line Item';
            public float $cost = 1000.00;
            public float $requestedAmount = 500.00;
            public float $requestedPercent = 50.0;
            public float $completedAmount = 0.00;
            public float $completedPercent = 0.0;
            public string $notes = 'Test notes';

            public function __construct(int $id)
            {
                $this->id = $id;
            }

            public function save(): array
            {
                return ['success' => true, 'id' => $this->id];
            }

            public function delete(): bool
            {
                return true;
            }

            public function toArray(): array
            {
                return [
                    'id' => $this->id,
                    'categoryId' => $this->categoryId,
                    'description' => $this->description,
                    'cost' => $this->cost,
                    'requestedAmount' => $this->requestedAmount,
                    'requestedPercent' => $this->requestedPercent,
                    'completedAmount' => $this->completedAmount,
                    'completedPercent' => $this->completedPercent,
                    'notes' => $this->notes
                ];
            }
        };
    }

    /**
     * Create a mock DrawSummaryCalculatedValues object
     */
    public static function createMockDrawSummaryCalculatedValues(int $LMRId = 123): object
    {
        return new class($LMRId) {
            public int $LMRId;
            public float $totalRehabCost = 10000.00;
            public float $totalRequestedAmount = 5000.00;
            public float $totalApprovedAmount = 4500.00;
            public float $totalCompletedAmount = 2000.00;
            public float $totalRemainingAmount = 2500.00;

            public function __construct(int $LMRId)
            {
                $this->LMRId = $LMRId;
            }

            public function save(): array
            {
                return ['success' => true];
            }

            public function toArray(): array
            {
                return [
                    'LMRId' => $this->LMRId,
                    'totalRehabCost' => $this->totalRehabCost,
                    'totalRequestedAmount' => $this->totalRequestedAmount,
                    'totalApprovedAmount' => $this->totalApprovedAmount,
                    'totalCompletedAmount' => $this->totalCompletedAmount,
                    'totalRemainingAmount' => $this->totalRemainingAmount
                ];
            }
        };
    }

    /**
     * Create a mock DrawRequestsHistory object
     */
    public static function createMockDrawRequestsHistory(): object
    {
        return new class {
            public array $history = [];

            public function addHistoryRecord(array $data): void
            {
                $this->history[] = $data;
            }

            public function getHistory(): array
            {
                return $this->history;
            }

            public function getHistoryByDrawRequestId(int $drawRequestId): array
            {
                return array_filter($this->history, function($record) use ($drawRequestId) {
                    return $record['drawRequestId'] === $drawRequestId;
                });
            }
        };
    }

    /**
     * Create a mock DrawRequestsHistory model object (for the actual class)
     */
    public static function createMockDrawRequestsHistoryModel(int $id = null): object
    {
        return new class($id) {
            public ?int $id = null;
            public ?int $drawId = null;
            public ?string $status = null;
            public ?string $submittedAt = null;
            public ?string $approvedAt = null;
            public float $requestedAmount = 0.0;
            public float $approvedAmount = 0.0;
            public array $lineItems = [];

            public const STATUS_PENDING = 'pending';
            public const STATUS_APPROVED = 'approved';
            public const STATUS_REJECTED = 'rejected';

            public function __construct(int $id = null)
            {
                if ($id) {
                    $this->id = $id;
                    $this->drawId = 456;
                    $this->status = self::STATUS_PENDING;
                    $this->submittedAt = '2023-01-01 12:00:00';
                    $this->requestedAmount = 5000.0;
                    $this->approvedAmount = 4500.0;
                }
            }

            public function save(array $data = []): array
            {
                foreach ($data as $key => $value) {
                    if (property_exists($this, $key)) {
                        $this->$key = $value;
                    }
                }
                return ['success' => true, 'id' => $this->id ?: 123];
            }

            public function setFromArray(array $data): void
            {
                foreach ($data as $key => $value) {
                    if (property_exists($this, $key)) {
                        $this->$key = $value;
                    }
                }
            }

            public function loadLineItems(): void
            {
                $this->lineItems = [
                    MockDataHelper::createMockBorrowerDrawLineItemHistory(1),
                    MockDataHelper::createMockBorrowerDrawLineItemHistory(2)
                ];
            }

            public function addLineItem(array $lineItem): object
            {
                $lineItemObj = (object) $lineItem;
                $this->lineItems[] = $lineItemObj;
                return $lineItemObj;
            }

            public function createHistoryRecord(int $drawId, string $status, array $data): object
            {
                $history = (object) [
                    'id' => 123,
                    'drawId' => $drawId,
                    'status' => $status,
                    'data' => $data,
                    'createdAt' => date('Y-m-d H:i:s')
                ];
                return $history;
            }

            public static function getHistoryByDrawId(int $drawId): array
            {
                return [
                    MockDataHelper::createMockDrawRequestsHistoryModel(1),
                    MockDataHelper::createMockDrawRequestsHistoryModel(2)
                ];
            }

            public function updateLatestHistoryRecord(int $drawId, string $status, array $data): bool
            {
                return true;
            }

            public function calculateTotalRequestedAmount(): float
            {
                return array_sum(array_map(function($item) {
                    return $item->requestedAmount ?? 0.0;
                }, $this->lineItems));
            }

            public function toArray(): array
            {
                return [
                    'id' => $this->id,
                    'drawId' => $this->drawId,
                    'status' => $this->status,
                    'submittedAt' => $this->submittedAt,
                    'approvedAt' => $this->approvedAt,
                    'requestedAmount' => $this->requestedAmount,
                    'approvedAmount' => $this->approvedAmount,
                    'lineItems' => array_map(function($item) {
                        return method_exists($item, 'toArray') ? $item->toArray() : $item;
                    }, $this->lineItems)
                ];
            }
        };
    }

    /**
     * Create a mock SowCategory object
     */
    public static function createMockSowCategory(int $id = 1): object
    {
        return new class($id) {
            public int $id;
            public string $categoryName = 'Test SOW Category';
            public string $description = 'Test SOW Description';
            public int $order = 1;
            public array $lineItems = [];

            public function __construct(int $id)
            {
                $this->id = $id;
            }

            public function save(): array
            {
                return ['success' => true, 'id' => $this->id];
            }

            public function delete(): bool
            {
                return true;
            }

            public function addLineItem(object $lineItem): void
            {
                $this->lineItems[] = $lineItem;
            }

            public function getAllLineItems(): array
            {
                return $this->lineItems;
            }

            public function toArray(): array
            {
                return [
                    'id' => $this->id,
                    'categoryName' => $this->categoryName,
                    'description' => $this->description,
                    'order' => $this->order
                ];
            }
        };
    }

    /**
     * Create a mock SowLineItem object
     */
    public static function createMockSowLineItem(int $id = 1): object
    {
        return new class($id) {
            public int $id;
            public int $categoryId = 1;
            public string $description = 'Test SOW Line Item';
            public float $cost = 1000.00;
            public string $notes = 'Test SOW notes';

            public function __construct(int $id)
            {
                $this->id = $id;
            }

            public function save(): array
            {
                return ['success' => true, 'id' => $this->id];
            }

            public function delete(): bool
            {
                return true;
            }

            public function toArray(): array
            {
                return [
                    'id' => $this->id,
                    'categoryId' => $this->categoryId,
                    'description' => $this->description,
                    'cost' => $this->cost,
                    'notes' => $this->notes
                ];
            }
        };
    }

    /**
     * Create a mock SowTemplateManager object
     */
    public static function createMockSowTemplateManager(int $PCID = 1041): object
    {
        return new class($PCID) {
            private int $PCID;
            private ?object $template = null;

            public function __construct(int $PCID)
            {
                $this->PCID = $PCID;
            }

            public function getTemplate(): ?object
            {
                if (!$this->template) {
                    $this->template = MockDataHelper::createMockSowTemplate(1, $this->PCID);
                }
                return $this->template;
            }

            public function saveTemplate(array $templateData): bool
            {
                return !empty($templateData);
            }

            public function deleteTemplate(): bool
            {
                return true;
            }

            public function copyTemplate(int $sourcePCID): bool
            {
                return true;
            }
        };
    }

    /**
     * Create a mock DrawSummaryManager object
     */
    public static function createMockDrawSummaryManager(int $LMRId = 123): object
    {
        return new class($LMRId) {
            private int $LMRId;
            private ?object $summary = null;

            public function __construct(int $LMRId)
            {
                $this->LMRId = $LMRId;
            }

            public function getSummary(): object
            {
                if (!$this->summary) {
                    $this->summary = MockDataHelper::createMockDrawSummaryCalculatedValues($this->LMRId);
                }
                return $this->summary;
            }

            public function updateSummary(array $data): bool
            {
                return true;
            }

            public static function initialize(int $LMRId, ?object $drawRequestManager = null): object
            {
                return MockDataHelper::createMockDrawSummaryManager($LMRId);
            }

            public static function getFormattedTotalDrawsFunded(): string
            {
                return '12,346';
            }

            public static function getFormattedHoldbackRemaining(): string
            {
                return '98,765';
            }

            public static function getAllData(): array
            {
                return [
                    'address' => '123 Test St',
                    'city' => 'Test City',
                    'state' => 'TX',
                    'zip' => '12345',
                    'borrowerName' => 'Test Borrower',
                    'loanAmount' => 100000.00,
                    'totalRehabCost' => 10000.00,
                    'totalRequestedAmount' => 5000.00,
                    'totalApprovedAmount' => 4500.00,
                    'totalCompletedAmount' => 2000.00,
                    'totalRemainingAmount' => 2500.00,
                    'formattedTotalDrawsFunded' => '12,346',
                    'formattedHoldbackRemaining' => '98,765'
                ];
            }
        };
    }

    /**
     * Create a mock BorrowerDrawLineItemHistory object
     */
    public static function createMockBorrowerDrawLineItemHistory(int $id = null): object
    {
        return new class($id) {
            public ?int $id = null;
            public ?int $recordId = null;
            public ?int $lineItemId = null;
            public float $completedAmount = 0.0;
            public float $completedPercent = 0.0;
            public float $requestedAmount = 0.0;
            public float $disbursedAmount = 0.0;
            public ?string $notes = null;
            public ?string $lenderNotes = null;
            public ?string $createdAt = null;
            public object $lineItemHistory;

            public function __construct(int $id = null)
            {
                if ($id) {
                    $this->id = $id;
                    $this->recordId = 456;
                    $this->lineItemId = 789;
                    $this->completedAmount = 500.0;
                    $this->completedPercent = 50.0;
                    $this->requestedAmount = 300.0;
                    $this->disbursedAmount = 200.0;
                    $this->notes = 'Test notes';
                    $this->lenderNotes = 'Test lender notes';
                    $this->createdAt = '2023-01-01 12:00:00';
                }

                $this->lineItemHistory = new class {
                    public ?int $id = null;
                    public ?int $recordId = null;
                    public ?int $lineItemId = null;
                    public float $completedAmount = 0.0;
                    public float $completedPercent = 0.0;
                    public float $requestedAmount = 0.0;
                    public float $disbursedAmount = 0.0;
                    public ?string $notes = null;
                    public ?string $lenderNotes = null;
                    public ?string $createdAt = null;

                    public function save(): array
                    {
                        return ['success' => true, 'id' => $this->id ?: 123];
                    }
                };
            }

            public function save(array $data = []): array
            {
                foreach ($data as $key => $value) {
                    if (property_exists($this, $key)) {
                        $this->$key = $value;
                    }
                }
                return ['success' => true, 'id' => $this->id ?: 123];
            }

            public function setFromArray(array $data): void
            {
                foreach ($data as $key => $value) {
                    if (property_exists($this, $key)) {
                        $this->$key = $value;
                    }
                }
            }

            public function getDbObject(): object
            {
                return $this->lineItemHistory;
            }

            public function toArray(): array
            {
                return [
                    'id' => $this->id,
                    'recordId' => $this->recordId,
                    'lineItemId' => $this->lineItemId,
                    'completedAmount' => $this->completedAmount,
                    'completedPercent' => $this->completedPercent,
                    'requestedAmount' => $this->requestedAmount,
                    'disbursedAmount' => $this->disbursedAmount,
                    'notes' => $this->notes,
                    'lenderNotes' => $this->lenderNotes,
                    'createdAt' => $this->createdAt
                ];
            }
        };
    }
}
