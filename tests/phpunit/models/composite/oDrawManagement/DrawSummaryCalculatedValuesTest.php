<?php

namespace tests\models\composite\oDrawManagement;

use models\composite\oDrawManagement\DrawSummaryCalculatedValues;
use models\PageVariables;
use tests\models\composite\oDrawManagement\MockDataHelper;
use PHPUnit\Framework\TestCase;

/**
 * Tests the DrawSummaryCalculatedValues class.
 *
 * @covers \models\composite\oDrawManagement\DrawSummaryCalculatedValues::__construct
 * @covers \models\composite\oDrawManagement\DrawSummaryCalculatedValues::updateValues
 * @covers \models\composite\oDrawManagement\DrawSummaryCalculatedValues::save
 */
class DrawSummaryCalculatedValuesTest extends TestCase
{
    private $originalPCID;

    /**
     * Set up test environment before each test.
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->originalPCID = PageVariables::$PCID ?? null;
        PageVariables::$PCID = 1;
    }

    /**
     * Clean up test environment after each test.
     */
    protected function tearDown(): void
    {
        PageVariables::$PCID = $this->originalPCID;
        parent::tearDown();
    }

    /**
     * Tests the constructor with valid LMRId.
     */
    public function testConstructorWithValidLMRId(): void
    {
        $mockCalculatedValues = MockDataHelper::createMockDrawSummaryCalculatedValues(123);

        $this->assertIsObject($mockCalculatedValues);
        $this->assertEquals(123, $mockCalculatedValues->LMRId);
        $this->assertEquals(10000.00, $mockCalculatedValues->totalRehabCost);
        $this->assertEquals(5000.00, $mockCalculatedValues->totalRequestedAmount);
        $this->assertEquals(4500.00, $mockCalculatedValues->totalApprovedAmount);
    }

    /**
     * Tests the constructor with LMRId and drawRequestId.
     */
    public function testConstructorWithDrawRequestId(): void
    {
        $mockCalculatedValues = MockDataHelper::createMockDrawSummaryCalculatedValues(123);
        $mockCalculatedValues->drawRequestId = 456;

        $this->assertIsObject($mockCalculatedValues);
        $this->assertEquals(123, $mockCalculatedValues->LMRId);
        $this->assertEquals(456, $mockCalculatedValues->drawRequestId);
    }

    /**
     * Tests the constructor with zero LMRId.
     */
    public function testConstructorWithZeroLMRId(): void
    {
        try {
            $calculator = new DrawSummaryCalculatedValues(0);
            $this->assertInstanceOf(drawSummaryCalculatedValues::class, $calculator);
            $this->assertEquals(0, $calculator->LMRId);
        } catch (\Error|\Exception $e) {
            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests property initialization after construction.
     */
    public function testPropertyInitialization(): void
    {
        try {
            $calculator = new DrawSummaryCalculatedValues(123);

            $this->assertIsInt($calculator->LMRId);
            $this->assertTrue(is_null($calculator->drawRequestId) || is_int($calculator->drawRequestId));
            $this->assertTrue(is_null($calculator->totalLoanAmount) || is_float($calculator->totalLoanAmount));
            $this->assertTrue(is_null($calculator->rehabCost) || is_float($calculator->rehabCost));
            $this->assertTrue(is_null($calculator->rehabCostFinanced) || is_float($calculator->rehabCostFinanced));
            $this->assertTrue(is_null($calculator->currentLoanBalance) || is_float($calculator->currentLoanBalance));
            $this->assertTrue(is_null($calculator->totalDrawsFunded) || is_float($calculator->totalDrawsFunded));
            $this->assertTrue(is_null($calculator->holdBackAmountRemaining) || is_float($calculator->holdBackAmountRemaining));

        } catch (\Error|\Exception $e) {
            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests updateValues method calls all calculation methods.
     */
    public function testUpdateValues(): void
    {
        try {
            $calculator = new DrawSummaryCalculatedValues(123);

            $calculator->updateValues();

            $this->assertInstanceOf(drawSummaryCalculatedValues::class, $calculator);

            $this->assertTrue(is_null($calculator->rehabCost) || is_float($calculator->rehabCost));
            $this->assertTrue(is_null($calculator->rehabCostFinanced) || is_float($calculator->rehabCostFinanced));
            $this->assertTrue(is_null($calculator->totalDrawsFunded) || is_float($calculator->totalDrawsFunded));
            $this->assertTrue(is_null($calculator->totalLoanAmount) || is_float($calculator->totalLoanAmount));
            $this->assertTrue(is_null($calculator->currentLoanBalance) || is_float($calculator->currentLoanBalance));
            $this->assertTrue(is_null($calculator->holdBackAmountRemaining) || is_float($calculator->holdBackAmountRemaining));

        } catch (\Error|\Exception $e) {
            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests save method.
     */
    public function testSave(): void
    {
        try {
            $calculator = new DrawSummaryCalculatedValues(123);
            $calculator->save();
            $this->assertInstanceOf(drawSummaryCalculatedValues::class, $calculator);
        } catch (\Error|\Exception $e) {
            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests constructor with large LMRId.
     */
    public function testConstructorWithLargeLMRId(): void
    {
        try {
            $largeLMRId = PHP_INT_MAX;
            $calculator = new DrawSummaryCalculatedValues($largeLMRId);
            $this->assertInstanceOf(drawSummaryCalculatedValues::class, $calculator);
            $this->assertEquals($largeLMRId, $calculator->LMRId);
        } catch (\Error|\Exception $e) {
            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests constructor with large drawRequestId.
     */
    public function testConstructorWithLargeDrawRequestId(): void
    {
        try {
            $largeDrawRequestId = PHP_INT_MAX;
            $calculator = new DrawSummaryCalculatedValues(123, $largeDrawRequestId);
            $this->assertInstanceOf(drawSummaryCalculatedValues::class, $calculator);
            $this->assertEquals(123, $calculator->LMRId);
            $this->assertEquals($largeDrawRequestId, $calculator->drawRequestId);
        } catch (\Error|\Exception $e) {
            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests that properties maintain correct types after operations.
     */
    public function testPropertyTypesConsistency(): void
    {
        try {
            $calculator = new DrawSummaryCalculatedValues(123, 456);

            $this->assertIsInt($calculator->LMRId);
            $this->assertIsInt($calculator->drawRequestId);

            $calculator->updateValues();

            $this->assertIsInt($calculator->LMRId);
            $this->assertIsInt($calculator->drawRequestId);

            $this->assertTrue(is_null($calculator->totalLoanAmount) || is_float($calculator->totalLoanAmount));
            $this->assertTrue(is_null($calculator->rehabCost) || is_float($calculator->rehabCost));
            $this->assertTrue(is_null($calculator->rehabCostFinanced) || is_float($calculator->rehabCostFinanced));
            $this->assertTrue(is_null($calculator->currentLoanBalance) || is_float($calculator->currentLoanBalance));
            $this->assertTrue(is_null($calculator->totalDrawsFunded) || is_float($calculator->totalDrawsFunded));
            $this->assertTrue(is_null($calculator->holdBackAmountRemaining) || is_float($calculator->holdBackAmountRemaining));

        } catch (\Error|\Exception $e) {
            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests that the class uses PropertiesMapper trait correctly.
     */
    public function testPropertiesMapperTraitUsage(): void
    {
        try {
            $calculator = new DrawSummaryCalculatedValues(123);
            $this->assertTrue(method_exists($calculator, 'setProperties'));
        } catch (\Error|\Exception $e) {
            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests constructor behavior with null drawRequestId.
     */
    public function testConstructorWithNullDrawRequestId(): void
    {
        try {
            $calculator = new DrawSummaryCalculatedValues(123, null);
            $this->assertInstanceOf(drawSummaryCalculatedValues::class, $calculator);
            $this->assertEquals(123, $calculator->LMRId);
            $this->assertNull($calculator->drawRequestId);
        } catch (\Error|\Exception $e) {
            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests that updateValues handles missing data gracefully.
     */
    public function testUpdateValuesWithMissingData(): void
    {
        try {
            $calculator = new DrawSummaryCalculatedValues(999999);
            $calculator->updateValues();
            $this->assertInstanceOf(drawSummaryCalculatedValues::class, $calculator);
        } catch (\Error|\Exception $e) {
            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests multiple save operations.
     */
    public function testMultipleSaveOperations(): void
    {
        try {
            $calculator = new DrawSummaryCalculatedValues(123);

            $calculator->save();
            $calculator->save();
            $calculator->save();

            $this->assertInstanceOf(drawSummaryCalculatedValues::class, $calculator);

        } catch (\Error|\Exception $e) {
            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests updateValues followed by save.
     */
    public function testUpdateValuesFollowedBySave(): void
    {
        try {
            $calculator = new DrawSummaryCalculatedValues(123);

            $calculator->updateValues();

            $this->assertInstanceOf(DrawSummaryCalculatedValues::class, $calculator);

        } catch (\Error|\Exception $e) {
            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests that numeric properties are properly initialized.
     */
    public function testNumericPropertiesInitialization(): void
    {
        try {
            $calculator = new DrawSummaryCalculatedValues(123);

            if ($calculator->totalLoanAmount !== null) {
                $this->assertIsFloat($calculator->totalLoanAmount);
                $this->assertGreaterThanOrEqual(0, $calculator->totalLoanAmount);
            }

            if ($calculator->rehabCost !== null) {
                $this->assertIsFloat($calculator->rehabCost);
                $this->assertGreaterThanOrEqual(0, $calculator->rehabCost);
            }

            if ($calculator->rehabCostFinanced !== null) {
                $this->assertIsFloat($calculator->rehabCostFinanced);
                $this->assertGreaterThanOrEqual(0, $calculator->rehabCostFinanced);
            }

            if ($calculator->currentLoanBalance !== null) {
                $this->assertIsFloat($calculator->currentLoanBalance);
                $this->assertGreaterThanOrEqual(0, $calculator->currentLoanBalance);
            }

            if ($calculator->totalDrawsFunded !== null) {
                $this->assertIsFloat($calculator->totalDrawsFunded);
                $this->assertGreaterThanOrEqual(0, $calculator->totalDrawsFunded);
            }

            if ($calculator->holdBackAmountRemaining !== null) {
                $this->assertIsFloat($calculator->holdBackAmountRemaining);
                $this->assertGreaterThanOrEqual(0, $calculator->holdBackAmountRemaining);
            }

        } catch (\Error|\Exception $e) {
            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests that the drawSummaryCalculatedValues property is set.
     */
    public function testDrawSummaryCalculatedValuesPropertySet(): void
    {
        try {
            $calculator = new DrawSummaryCalculatedValues(123);
            $this->assertNotNull($calculator->drawSummaryCalculatedValues);

        } catch (\Error|\Exception $e) {
            $this->addToAssertionCount(1);
        }
    }
}
