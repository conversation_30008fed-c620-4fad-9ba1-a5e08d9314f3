<?php

namespace tests\models\constants;

use models\constants\GpropertyTypeNumbArray;
use PHPUnit\Framework\TestCase;

/**
 * Tests the GpropertyTypeNumbArray class.
 *
 * Auto-generated docblock. Please refine descriptions as needed.
 *
 * @covers \models\constants\GpropertyTypeNumbArray::constants
 * @covers \models\constants\GpropertyTypeNumbArray::gpropertyTypeNumbArray
 * @covers \models\constants\GpropertyTypeNumbArray::gpropertyTypeNumbArray2
 * @covers \models\constants\GpropertyTypeNumbArray::globalPropertyTypeGroup
 */
class GpropertyTypeNumbArrayTest extends TestCase
{
  /**
   * Tests the constants method of the GpropertyTypeNumbArray class.
   *
   * Auto-generated docblock. Please update with test specifics.
   */
  public function testConstants()
  {
    $this->assertEquals(48, GpropertyTypeNumbArray::CONDO);
    $this->assertEquals(5, GpropertyTypeNumbArray::CONDO_HIGH_RISE);
    $this->assertEquals(4, GpropertyTypeNumbArray::CONDO_LOW_RISE);
  }

  /**
   * Tests the gpropertyTypeNumbArray method of the GpropertyTypeNumbArray class.
   *
   * Auto-generated docblock. Please update with test specifics.
   */
  public function testGpropertyTypeNumbArray()
  {
    $this->assertIsArray(GpropertyTypeNumbArray::$GpropertyTypeNumbArray);
    $this->assertArrayHasKey(48, GpropertyTypeNumbArray::$GpropertyTypeNumbArray);
    $this->assertEquals('Condo', GpropertyTypeNumbArray::$GpropertyTypeNumbArray[48]);
  }

  /**
   * Tests the gpropertyTypeNumbArray2 method of the GpropertyTypeNumbArray class.
   *
   * Auto-generated docblock. Please update with test specifics.
   */
  public function testGpropertyTypeNumbArray2()
  {
    $this->assertIsArray(GpropertyTypeNumbArray::$GpropertyTypeNumbArray2);
    $this->assertArrayHasKey('48 ', GpropertyTypeNumbArray::$GpropertyTypeNumbArray2);
    $this->assertEquals('Condo', GpropertyTypeNumbArray::$GpropertyTypeNumbArray2['48 ']);
  }

  /**
   * Tests the globalPropertyTypeGroup method of the GpropertyTypeNumbArray class.
   *
   * Auto-generated docblock. Please update with test specifics.
   */
  public function testGlobalPropertyTypeGroup()
  {
    $this->assertIsArray(GpropertyTypeNumbArray::$globalPropertyTypeGroup);
    $this->assertArrayHasKey('Residential Separator (Good to add)', GpropertyTypeNumbArray::$globalPropertyTypeGroup);
    $this->assertArrayHasKey(48, GpropertyTypeNumbArray::$globalPropertyTypeGroup['Residential Separator (Good to add)']);
    $this->assertEquals('Condo', GpropertyTypeNumbArray::$globalPropertyTypeGroup['Residential Separator (Good to add)'][48]);
  }
}
