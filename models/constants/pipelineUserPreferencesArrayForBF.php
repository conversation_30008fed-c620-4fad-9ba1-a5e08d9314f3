<?php

namespace models\constants;

class pipelineUserPreferencesArrayForBF
{
    public static array $pipelineUserPreferencesArrayForBF = [
        'Dates'               => [
            'recordDate'          => 'Date entered',
            'lastUpdatedDate'     => 'Last action',
            'receivedDate'        => 'Received Date',
            'closingDate'         => 'Actual Closing Date',
            'desiredClosingDate'  => 'Desired Closing Date',
            'borrowerCallBack'    => 'Client Call Back Date',
            'trialPaymentDate1'   => 'First Payment Due',
            'proInsPolicyExpDate' => 'Policy Expiration Date',
            'hearingDate'         => 'Hearing Date',
            'dateObtained'        => 'Appraisal 1 Received Date',
            'appraisal1OrderDate' => 'Appraisal 1 Order Date',
            'salesDate'           => 'Sale date',
        ],
        'Borrower Info'       => [
            'phoneNumber'                  => 'Phone',
            'cellNumber'                   => 'Cell #',
            'borrowerEmail'                => 'Email',
            'totalNetIncome'               => 'Total Net Income',
            'totalExpenses'                => 'Total Net Expenses',
            'disposableIncome'             => 'Disposable Income',
            'currentDTI'                   => 'Current DTI',
            'totalAssets'                  => 'Total Assets',
            'networthOfBusinessOwned'      => 'Net Worth',
            'borCreditScoreRange'          => 'Credit Score Range',
            'borExperianScore'             => 'Experian',
            'borEquifaxScore'              => 'Equifax',
            'borTransunionScore'           => 'Transunion',
            'borNoOfREPropertiesCompleted' => '# of properties completed',
            'borRehabPropCompleted'        => '# of New Construction done',
            'borNoOfOwnProp'               => '# of investment properties',
            'isBorUSCitizen'               => 'US Citizen',
            'entity'                       => 'Entity',
            'city'                         => 'City',
            'state'                        => 'State',
            'assetTotalCashBankAcc'        => 'Total Cash In Bank Accounts',
        ],
        'Entity Info'         => [
            'entityName'        => 'Entity Name',
            'entityType'        => 'Entity Type',
            'stateOfFormation'  => 'State Of Formation',
            'entityCity'        => 'Entity City',
            'entityState'       => 'Entity State',
            'propertyOwnership' => 'Property Ownership',
            'ofEmployees'       => '# Of Employees',
        ],
        'Business Financials' => [
            'avgMonthlyCreditCardSales' => 'Avg Monthly Credit Card Sales',
            'avgTotalMonthlySales'      => 'Avg Total Monthly Sales',
            'annualGrossProfit'         => 'Annual Gross Profit',
            'netBusinessIncome'         => 'Net Business Income',
        ],
        'Loan Info'           => [
            'seviceTypes'               => 'Loan Programs',
            'LMRInternalLoanProgram'    => 'Internal Loan Programs',
            'HMLOLender'                => 'Originator',
            'HMLOLoanType'              => 'Transaction Type',
            'HMLOTotalLoanAmt'          => 'Loan Amount',
            'HMLOLoanTerm'              => 'Loan Term',
            'purchasePrice'             => 'Purchase Price',
            'HMLOTotalPayment'          => 'Monthly Payment',
            'lien1Rate'                 => 'Interest Rate',
            'totalProjectCost'          => 'Total Project cost',
            'totalRehabCost'            => 'Total Rehab Cost',
            'rehabCostFinanced'         => 'Rehab Cost Financed',
            'monthlyLoanAmount'         => 'Payroll Loan',
            'LTC'                       => 'Loan-to-Cost',
            'ARV'                       => 'ARV %',
            'acquisitionLTV'            => 'Acquisition LTV',
            'marketLTV'                 => 'Market LTV',
            'perRehabCostFinanced'      => '% of Rehab Cost Financed',
            'exitStrategy'              => 'Exit Strategy',
            'isHouseProperty'           => 'Borrower Occupancy',
            'propertyNeedRehab'         => 'Rehab Required',
            'propertyConstructionLevel' => 'Property Construction Level',
            'lienPosition'              => 'Lien Position',
            'costOfCapital'             => 'Cost of Capital',
            'yieldSpread'               => 'Yield Spread',
            'projectName'               => 'Project Name',
            'assessedValue'             => 'After Repair Value',
            'availableBudget'           => 'Current Escrow Balance',
            'currentLoanBalance'        => 'Current Loan Balance',
            'initialLoanAmount'         => 'Initial Loan Amount',
        ],
        'Property Info'       => [
            'propertyCity'           => 'City',
            'propertyState'          => 'State',
            'propertyCounty'         => 'County',
            'propertySqFt'           => 'Sq Ft',
            'propertyCondition'      => 'Condition',
            'acres'                  => 'Lot Size',
            'propertyType'           => 'Property Type',
            'presentOccupancyStatus' => 'Present Occupancy Status',
            'bedrooms'               => '# of bedrooms',
            'bathrooms'              => '# of Baths',
            'appraiser1Value'        => 'Appraisal 1 Value',
            'AVM1'                   => 'AVM 1 Value',
            'BPO1'                   => 'Realtor 1 Value',
            // 'repairValue'                      => 'After Repair Value',    //Commented on 30-Aug-2018
            'ASISValue'              => 'As-is Value',
            'propertyURLLink'        => 'URL link to property',
            'taxes1'                 => 'Annual Property Tax',
        ],
        'Admin/Staff Details' => [
            'primaryStatus'    => 'Status',
            'fileVelocity'     => 'Days in primary file status',
            'subStatus'        => 'Sub-Status',
            'loanNumber'       => 'Loan #',
            'MERSID'           => 'MERS ID',
            'priorityLevel'    => 'Priority Level',
            'branch'           => 'Branch',
            'agent'            => 'Agent',
            'loanofficer'      => 'Loan Officer',
            'assignedEmployee' => 'Assigned Employee',
            'leadSource'       => 'Lead Source',
            'workflowEvents'   => 'WorkFlow(s)',
            'referringParty'   => 'Broker referring party',
            'lender'           => 'Lender',
            'servicer'         => 'Servicer',
            'trustee'          => 'Trustee',
            'investor'         => 'Investor',
        ],
        'Miscellaneous'       => [
            'insuranceCompName' => 'Insurance Co. Name',
            'titleCompanyInfo'  => 'Title Company Info',
            'typeOfSale'        => 'Type Of Sale',
            'maturityDate'      => 'Maturity Date',
            'servicingStatus'   => 'Servicing Status',
            'brokerPointsRate'  => 'Broker Points %',
            'brokerPointsValue' => 'Broker Points Value',
            'originationPointsRate'  => 'Origination Points %',
            'originationPointsValue' => 'Origination Points Value',
            'payOffDate'        => 'Pay Off Date',
            'loanSaleDate'      => 'Date Of Loan Sale Agreement',
            'fileId'            => 'File ID',
        ],
    ];
}