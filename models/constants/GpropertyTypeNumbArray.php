<?php

namespace models\constants;

class GpropertyTypeNumbArray
{
    /*
    IF YOU CHANGE GpropertyTypeNumbArray THEN MAKE SAME CHANGE TO GpropertyTypeNumbArray2 FOLLOWING its formatting.
    GpropertyTypeNumbArray2 is formatted as such to solve issues with browsers rearranging objects.  If a residential prop
    find function showAndHideZillowValue and add the new prop type there also... and probably also models/constants/ResidentialPropArr.php.
    Also add to tblHMLOPropertyType which is for import ibelieve

    */
    const CONDO = 48;
    const CONDO_HIGH_RISE = 5;
    const CONDO_LOW_RISE = 4;

    const GpropertyTypeNumbArray = [
        1000 => 'Residential Separator (Good to add)',
        1 => 'Single Family Home',
        48 => 'Condo',
        4 => 'Condo Low Rise',
        5 => 'Condo High Rise',
        12 => 'Loft',
        3 => 'TownHouse',
        64 => 'SRO',
        9 => 'Manufactured',
        6 => 'Duplex',
        7 => '3 Units',
        8 => '4 Units',
        94 => '2-4 Units',
        //    37 => 'Multi-Family',
        //    65 => 'Multi-Family-2',
        //    66 => 'Multi-Family-3',
        //    67 => 'Multi-Family-4',
        10 => 'Lot/Land',
        101 => 'Undeveloped Land',
        82 => 'Residential Portfolio',
        93 => 'AirBnB',
        98 => 'Detached Townhouse',
        //======================  //alphabetize commercial
        1001 => 'Commercial Separator (Good to add)',
        77 => 'Affordable Housing',
        62 => 'Apartment Complex',
        70 => 'Assisted Living Facility',
        58 => 'Auto Repair',
        78 => 'Bed & Breakfast',
        84 => 'Bio-Energy',
        76 => 'Boathouse',
        74 => 'Car Wash',
        72 => 'Church',
        33 => 'Co-Op',
        73 => 'Commercial Lot/Land',
        11 => 'Condotel',
        15 => 'Construction',
        60 => 'Daycare',
        49 => 'Developed Land',
        85 => 'Equipment',
        96 => 'Fractured Co-op',
        97 => 'Fractured Condo',
        61 => 'Funeral Homes',
        57 => 'Garage/Storage',
        79 => 'Gas Stations',
        89 => 'Hospital',
        45 => 'Hotel',
        100 => 'Industrial',
        88 => 'Infrastructure',
        75 => 'Laundromat',
        42 => 'Light Industrial',
        80 => 'Medical Cannabis',
        34 => 'Mixed Use',
        44 => 'Motel',
        68 => 'Multi-Family-5+',
        46 => 'Mobile Home Park',
        63 => 'Medical Office',
        52 => 'New Residential Development',
        53 => 'Owner Occupied Business',
        40 => 'Office Building',
        90 => 'Public Safety',
        2 => 'PUD',
        59 => 'Recreational Properties',
        83 => 'Renewable Energy',
        50 => 'Resorts',
        43 => 'Restaurant/Bar',
        39 => 'Retail',
        56 => 'Self-Storage',
        91 => 'Solar',
        54 => 'Special Purpose Properties',
        /*55 => 'SBA/Working Capital', - migrate any existing to other */
        41 => 'Strip Mall',
        81 => 'Student Housing',
        51 => 'Tavern',
        95 => 'Underlying Co-op',
        38 => 'Warehouse',
        86 => 'Wastewater Treatment',
        87 => 'Water Treatment',
        47 => 'Other',
    ];

    public static array $GpropertyTypeNumbArray = self::GpropertyTypeNumbArray;

    // for use in AJAX requests
    public static array $GpropertyTypeNumbArray2 = [
        '1000 ' => 'Resi Separator (Good to add)',
        '1 ' => 'Single Family Home',
        '48 ' => 'Condo',
        '4 ' => 'Condo Low Rise',
        '5 ' => 'Condo High Rise',
        '12 ' => 'Loft',
        '3 ' => 'TownHouse',
        '64 ' => 'SRO',
        '9 ' => 'Manufactured',
        '6 ' => 'Duplex',
        '7 ' => '3 Units',
        '8 ' => '4 Units',
        '94 ' => '2-4 Units',
//    '37 ' => 'Multi-Family',
//    '65 ' => 'Multi-Family-2',
//    '66 ' => 'Multi-Family-3',
//    '67 ' => 'Multi-Family-4',
        '10 ' => 'Lot/Land',
        '101 ' => 'Undeveloped Land',
        '82 ' => 'Residential Portfolio',
        '93 ' => 'AirBnB',
        '98 ' => 'Detached Townhouse',
//======================  //alphabetize commercial
        '1001 ' => 'Commercial Separator (Good to add)',
        '77 ' => 'Affordable Housing',
        '62 ' => 'Apartment Complex',
        '70 ' => 'Assisted Living Facility',
        '58 ' => 'Auto Repair',
        '78 ' => 'Bed & Breakfast',
        '84 ' => 'Bio-Energy',
        '76 ' => 'Boathouse',
        '74 ' => 'Car Wash',
        '72 ' => 'Church',
        '33 ' => 'Co-Op',
        '73 ' => 'Commercial Lot/Land',
        '11 ' => 'Condotel',
        '15 ' => 'Construction',
        '60 ' => 'Daycare',
        '49 ' => 'Developed Land',
        '85 ' => 'Equipment',
        '96 ' => 'Fractured Co-op',
        '97 ' => 'Fractured Condo',
        '61 ' => 'Funeral Homes',
        '57 ' => 'Garage/Storage',
        '79 ' => 'Gas Stations',
        '89 ' => 'Hospital',
        '45 ' => 'Hotel',
        '100 ' => 'Industrial',
        '88 ' => 'Infrastructure',
        '75 ' => 'Laundromat',
        '42 ' => 'Light Industrial',
        '80 ' => 'Medical Cannabis',
        '34 ' => 'Mixed Use',
        '44 ' => 'Motel',
        '68 ' => 'Multi-Family-5+',
        '46 ' => 'Mobile Home Park',
        '63 ' => 'Medical Office',
        '52 ' => 'New Residential Development',
        '53 ' => 'Owner Occupied Business',
        '40 ' => 'Office Building',
        '90 ' => 'Public Safety',
        '2 ' => 'PUD',
        '59 ' => 'Recreational Properties',
        '83 ' => 'Renewable Energy',
        '50 ' => 'Resorts',
        '43 ' => 'Restaurant/Bar',
        '39 ' => 'Retail',
        '56 ' => 'Self-Storage',
        '92 ' => 'Shopping Center',
        '91 ' => 'Solar',
        '54 ' => 'Special Purpose Properties',
        /*55 => 'SBA/Working Capital', - migrate any existing to other */
        '41 ' => 'Strip Mall',
        '81 ' => 'Student Housing',
        '51 ' => 'Tavern',
        '95 ' => 'Underlying Co-op',
        '38 ' => 'Warehouse',
        '86 ' => 'Wastewater Treatment',
        '87 ' => 'Water Treatment',
        '47 ' => 'Other',
    ];

    public static array $globalPropertyTypeGroup = [

        'Residential Separator (Good to add)' => [
            1 => 'Single Family Home',
            48 => 'Condo',
            4 => 'Condo Low Rise',
            5 => 'Condo High Rise',
            12 => 'Loft',
            3 => 'TownHouse',
            64 => 'SRO',
            9 => 'Manufactured',
            6 => 'Duplex',
            7 => '3 Units',
            8 => '4 Units',
            94 => '2-4 Units',
        //    37 => 'Multi-Family',
        //    65 => 'Multi-Family-2',
        //    66 => 'Multi-Family-3',
        //    67 => 'Multi-Family-4',
            10 => 'Lot/Land',
            101 => 'Undeveloped Land',
            82 => 'Residential Portfolio',
            93 => 'AirBnB',
        ],
        'Commercial Separator (Good to add)' => [
            77 => 'Affordable Housing',
            62 => 'Apartment Complex',
            70 => 'Assisted Living Facility',
            58 => 'Auto Repair',
            78 => 'Bed & Breakfast',
            84 => 'Bio-Energy',
            76 => 'Boathouse',
            74 => 'Car Wash',
            72 => 'Church',
            33 => 'Co-Op',
            73 => 'Commercial Lot/Land',
            11 => 'Condotel',
            15 => 'Construction',
            60 => 'Daycare',
            49 => 'Developed Land',
            85 => 'Equipment',
            96 => 'Fractured Co-op',
            97 => 'Fractured Condo',
            61 => 'Funeral Homes',
            57 => 'Garage/Storage',
            79 => 'Gas Stations',
            89 => 'Hospital',
            45 => 'Hotel',
            100 => 'Industrial',
            88 => 'Infrastructure',
            75 => 'Laundromat',
            42 => 'Light Industrial',
            80 => 'Medical Cannabis',
            34 => 'Mixed Use',
            44 => 'Motel',
            68 => 'Multi-Family-5+',
            46 => 'Mobile Home Park',
            63 => 'Medical Office',
            52 => 'New Residential Development',
            53 => 'Owner Occupied Business',
            40 => 'Office Building',
            90 => 'Public Safety',
            2 => 'PUD',
            59 => 'Recreational Properties',
            83 => 'Renewable Energy',
            50 => 'Resorts',
            43 => 'Restaurant/Bar',
            39 => 'Retail',
            56 => 'Self-Storage',
            91 => 'Solar',
            54 => 'Special Purpose Properties',
            /*55 => 'SBA/Working Capital', - migrate any existing to other */
            41 => 'Strip Mall',
            81 => 'Student Housing',
            51 => 'Tavern',
            95 => 'Underlying Co-op',
            38 => 'Warehouse',
            86 => 'Wastewater Treatment',
            87 => 'Water Treatment',
            47 => 'Other',
        ],
    ];
}
