<?php

namespace models\constants\gl;

use models\constants\loanProgram;
use models\constants\packageId;
use models\constants\gl\glBrokerPartnerTypeArray;

class glCustomJobForProcessingCompany
{
//    const PCID_RHINE_LEGACY_CAPITAL = 4846;
//    const PCID_TESTING_KRISH = 3476;
//    const PCID_MORTGAGE_ASSISTANCE = 1456;
//    const PCID_DEV_DAVE = 3363;

//    public static ?int $glCustomLenderRevenuePCID = 3187;
//    public static ?int $glCustomNotificationHideBackofficeEmailPCID = 1495; //https://app.shortcut.com/lendingwise/story/37999/adjustment-to-notification-for-company-aspire-processing

//    public static ?int $glCustomLoanTermSheetHideCCostsSectionPCID = 3198;  //Loan Term Sheet mod for Global Integrity Finance https://app.shortcut.com/lendingwise/story/38538/edit-to-document-loan-term-sheet-for-global-integrity-finance

    const PCID_MORTGAGE_ASSISTANCE = 1456;
    const PCID_DEV_DAVE = 3363;
    const PCID_4495 = 4495;

    const LOANINFOV2_FORM_FIELDS = [
        'loanInfoV2Section',
        'totalPropertiesLoanAmount',
        'propertyConstructionLevel',
        'constructionType',
        'constructionHardCost',
        'constructionSoftCost',
        'contingencyTypeOption',
        'contingencyPercentage',
        'contingencyAmount',
        'financedInterestReserve',
        'interestReserveType',
        'isPropertyHaveSubordinateFinancing',
        'subordinateFinancingAmount',
        'loanDetailsBridge',
        'loanDetailsBridgeRenovation',
        'loanDetailsRental',
        'loanDetailsGroundUpConstruction',
    ];
//    public static ?int $glCustomHideTimeStampAndNotesPCID = 4101; //https://app.shortcut.com/lendingwise/story/38662/customization-for-company-lionscove-doc-upload-portals
//    public static ?int $glCustomHideAdminTabFields = 4101; //https://www.pivotaltracker.com/story/show/*********

    public static function isCustomHideAdminTabFields(?int $PCID): bool
    {
        //https://www.pivotaltracker.com/story/show/*********
        return $PCID == glPCID::PCID_4101;
    }

    public static function isCustomHideTimeStampAndNotes(?int $PCID): bool
    {
        //https://app.shortcut.com/lendingwise/story/38662/customization-for-company-lionscove-doc-upload-portals
        return $PCID == glPCID::PCID_4101;
    }

    public static function isCustomLoanTermSheetHideCCostsSection(?int $PCID): bool
    {
        //Loan Term Sheet mod for Global Integrity Finance https://app.shortcut.com/lendingwise/story/38538/edit-to-document-loan-term-sheet-for-global-integrity-finance
        return in_array($PCID, [glPCID::PCID_3198,
            glPCID::PCID_SKM_CAPITAL,
            glPCID::PCID_DEV_DAVE
        ]);
    }

    public static function isCustomLoanTermSheetHideExtensionOption(?int $PCID): bool
    {
        return $PCID == glPCID::PCID_ALTISOURCE;
    }

    public static function isCustomLoanTermSheetAltCCostsSection(?int $PCID): bool
    {
        //Loan Term Sheet mod for Evergreen Equity Partners pivotal *********
        return $PCID == glPCID::PCID_EVERGREEN_EQUITY_PARTNERS;
    }

    public static function isCustomNotificationHideBackofficeEmail(?int $PCID): bool
    {
        //https://app.shortcut.com/lendingwise/story/37999/adjustment-to-notification-for-company-aspire-processing
        return $PCID == glPCID::PCID_1495;
    }

    public static function isCustomLenderRevenuePCIDs(): array
    {
        return [glPCID::PCID_3187];
    }

    public static function isCustomLenderRevenue(?int $PCID): bool
    {
        return in_array($PCID, self::isCustomLenderRevenuePCIDs());
    }

    public static function is(?int $PCID): bool
    {
        return in_array($PCID, [
            glPCID::PCID_2,
            glPCID::PCID_MORTGAGE_ASSISTANCE,
        ]);
    }

    public static function isHideBorrowerPortal(?int $PCID): bool
    {
        return in_array($PCID, [
            glPCID::PCID_RHINE_LEGACY_CAPITAL,
            //glPCID::PCID_DEV_DAVE,
        ]);
    }

    public static function isCustomRecurringFeeRecurringFee(?int $PCID): bool
    {
        return in_array($PCID, [
            glPCID::PCID_MORTGAGE_ASSISTANCE,
            //glPCID::PCID_DEV_DAVE,
        ]);
    }

    public static function isUsingPCCompanyNotBranchCompanyPackages(?int $PCID): bool
    {
        return in_array($PCID, [
            glPCID::PCID_3198,  //global integrity
            //glPCID::PCID_DEV_DAVE
        ]);
    }

//    public static ?array $glCustomRecurringFeeRecurringFee = [
//        glPCID::PCID_MORTGAGE_ASSISTANCE,
//        //glPCID::PCID_DEV_DAVE,
//    ];

    public static ?array $glCustomCollateralsHide = [
        glPCID::PCID_CRB,
        //glPCID::PCID_DEV_DAVE,
    ];

    public static function isPC_CRB(?int $PCID): bool
    {
        if (CONST_ENVIRONMENT == 'production') {
            return $PCID == glPCID::PCID_CRB;
        } else if (CONST_ENVIRONMENT == 'staging') {
            return in_array($PCID, [
                glPCID::PCID_TEST_DANIEL, //Daniel Test
            ]);
        }
        return false;
    }

    public static ?array $glCustomTabDealSizerCommercial = [
        glPCID::PCID_CRB,
        //      glPCID::PCID_LENDINGWISE,
//        glPCID::PCID_PROD_DAVE,
//        glPCID::PCID_DEV_DAVE,
    ];   //https://www.pivotaltracker.com/n/projects/2624420/stories/184481451


    public static ?array $glCustomHideCashFlowSection = [
        glPCID::PCID_CRB,
    ];

    public static ?array $glCustomHidePaymentBasedField = [
        glPCID::PCID_CRB,
    ];

    public static function canSearchMultipleFields(?int $PCID): bool
    {
        return $PCID != glPCID::PCID_3394;
    }


    public static function hideBrokerRegistrationPassword(?int $PCID): bool
    {
        return !in_array($PCID, [
            glPCID::PCID_CRE_FINANCE,
            //glPCID::PCID_DEV_DAVE
        ]);
    }

    public static function colorSalesdate(?int $PCID): bool
    {
        return in_array($PCID, [
            glPCID::PCID_SUMMIT_SOFTWARE_SYSTEMS,
            glPCID::PCID_1495,
            //glPCID::PCID_DEV_DAVE
        ]);
    }

    public static function getTermSheetPackageId(?string $loanProgram): int
    {
        $packageId = 0;
        switch ($loanProgram) {
            case loanProgram::BRIDGE_LOAN:
                $packageId = packageId::TERM_LETTER_BRIDGE;
                break;
            case loanProgram::BRIDGE_TO_PERM:
                $packageId = packageId::TERM_LETTER_HYBRID;
                break;
            case loanProgram::PERM_LOAN:
                $packageId = packageId::TERM_LETTER_PERM;
                break;
        }
        return $packageId;

    }

    public static function accessToExtensionOptions(?int $PCID, ?int $UserId): bool
    {
        if ($PCID == glPCID::PCID_ALTISOURCE) {
            if ($UserId == glUserId::USER_VARUN_SINGHAL_ALTISOURCE || $UserId == glUserId::USER_PERVEZ_AKHTAR_ALTISOURCE) {
                return true;
            } else {
                return false;
            }
        } else {
            return true;
        }
    }

    public static function showMortgageNotes(?int $PCID): bool
    {
        return in_array($PCID, [
            glPCID::PCID_4637,
            //glPCID::PCID_DEV_DAVE,
        ]);
    }

    public static function loanPassUrl(?int $PCID): bool
    {
        return in_array($PCID, [
            glPCID::PCID_PROD_CV3,
            glPCID::PCID_DOMINION,
            glPCID::PCID_RELENDING,
            glPCID::PCID_PIEDPIPER,
            glPCID::PCID_3198,
        ]);
    }

    public static function showLoanTermsToBusinessFunding(?int $PCID): bool
    {
        return in_array($PCID, [
            glPCID::PCID_2057,
            //glPCID::PCID_DEV_DAVE,
        ]);
    }

    public static function isCustomSendGridPCID(?int $PCID): bool
    {
        return in_array($PCID, [
            glPCID::PCID_CRB,  //CRB
        ]);
    }

    public static function getFromEmailByPCID(?int $PCID): string
    {
        switch ($PCID) {
            case glPCID::PCID_CRB:
                $fromEmail = glPCID::PCID_CRB_FROM_EMAIL;
                break;
            default:
                $fromEmail = CONST_EMAIL_FROM; //<EMAIL>
        }
        return $fromEmail;
    }


    public static function getSendGridAPIKeyByPCID(?int $PCID): string
    {
        switch ($PCID) {
            case glPCID::PCID_CRB:
                $sendgridAPIkey = glPCID::PCID_CRB_SENDGRID_API;
                break;
            default:
                $sendgridAPIkey = CONST_SENDGRID_API_KEY; //<EMAIL>
        }
        return $sendgridAPIkey;

    }

    public static function getSendGridMarketingAPIKeyByPCID(?int $PCID): string
    {
        switch ($PCID) {
            case glPCID::PCID_CRB:
                $sendgridAPIkey = glPCID::PCID_CRB_SENDGRID_API;
                break;
            default:
                $sendgridAPIkey = CONST_SENDGRID_MARKETING_API_KEY; //<EMAIL>
        }
        return $sendgridAPIkey;

    }

    public static function hideDataWorkFlow($PCID)
    {
        if (in_array($PCID, [
            glPCID::PCID_CRB,
            //glPCID::PCID_DEV_DAVE
        ])) {
            return ' d-none ';
        }
    }

    public static function readOnlyFieldLoanInfo($PCID, $activeTab)
    {
        if (in_array($PCID, [glPCID::PCID_CRB]) && $activeTab == 'HMLI') {
            return ' readonly ';
        }
    }

    public static function hideRateIndexFields($PCID)
    {
        if (in_array($PCID, self::$glCustomCollateralsHide)) {
            unset(glRateIndex::$glRateIndex[array_search('CME Term SOFR 3M', glRateIndex::$glRateIndex)]);
            unset(glRateIndex::$glRateIndex[array_search('SOFR', glRateIndex::$glRateIndex)]);
            unset(glRateIndex::$glRateIndex[array_search('US Treasury', glRateIndex::$glRateIndex)]);
            unset(glRateIndex::$glRateIndex[array_search('Farmer mac', glRateIndex::$glRateIndex)]);
            unset(glRateIndex::$glRateIndex[array_search('Libor', glRateIndex::$glRateIndex)]);
        }
        return glRateIndex::$glRateIndex;
    }

    public static ?array $glCustomNewFileHide = [
        glPCID::PCID_CRB,
        glPCID::PCID_PROD_CV3,
        //glPCID::PCID_DEV_DAVE,
    ];

    public static function hideDataInBorrowerPortal(?int $PCID): string
    {
        if (in_array($PCID, [
            glPCID::PCID_BFXV,
            //glPCID::PCID_DEV_DAVE,
        ])) {
            return 'd-none';
        } else {
            return '';
        }
    }

    public static function hideWizardDescription(?int $PCID): string
    {
        if (in_array($PCID, [
            glPCID::PCID_PROD_CV3,
            //glPCID::PCID_DEV_DAVE,
        ])) {
            return 'd-none';
        } else {
            return '';
        }
    }

    public static function hideTabsForBorrowerPortal(?int $PCID): bool
    {
        return in_array($PCID, [
            glPCID::PCID_BFXV,
            //glPCID::PCID_DEV_DAVE,
        ]);
    }

// deprecated
//    public static ?array $RulesV2Enabled = [
//        glPCID::PCID_CRB, // 4814
//        glPCID::PCID_BROKER_WISE, // 3310
//        glPCID::PCID_PROD_DAVE, // 3580
//        glPCID::PCID_LENDINGWISE, // 1652
//    ];

    public static function generateFileIDAsLoanNumber(?int $PCID): bool
    {
        return in_array($PCID, [
            glPCID::PCID_PROD_CV3,
        ]);
    }

    public static function renameQABrowserTab(?int $PCID): bool
    {
        return in_array($PCID, [
            glPCID::PCID_CRB,
            //glPCID::PCID_DEV_DAVE,
        ]);
    }

    public static function cv3FeesAndCostNewFields(?int $PCID): bool
    {
        return in_array($PCID, [
            glPCID::PCID_PROD_CV3,
            //glPCID::PCID_DEV_DAVE,
        ]);
    }

    public static function validateBrokerProfileDataInputs(?int $PCID, $externalBroker): string
    {
        $returnData = ' ignoreValidation ';
        if (in_array($PCID, [
                glPCID::PCID_PROD_CV3,
                //glPCID::PCID_DEV_DAVE,
            ]) && !$externalBroker) {
            $returnData = ' mandatory ';
        }
        return $returnData;
    }

    public static function hideThirdPartyServiceSendCCInfo(?int $PCID): bool
    {
        return in_array($PCID, [
            glPCID::PCID_PROD_CV3,
            //glPCID::PCID_DEV_DAVE,
        ]);
    }

    //Global Integrity Finance
    public static function isPC_GIF(?int $PCID): bool
    {
        return in_array($PCID, [
            glPCID::PCID_3198,
            glPCID::PCID_PROD_DAVE,
            //glPCID::PCID_DEV_DAVE,
        ]);
    }

    public static function isPC_CV3(?int $PCID): bool
    {
        return in_array($PCID, [
            glPCID::PCID_PROD_CV3,

        ]);
    }

    public static function removeAddOptionForPrePayment(?int $PCID): bool
    {
        if (in_array($PCID, [
            glPCID::PCID_PROD_CV3,
            //glPCID::PCID_DEV_DAVE,
        ])) {
            return true;
        } else {
            return false;
        }
    }

    public static function hideAnnouncements(?int $PCID): bool
    {
        return $PCID == glPCID::PCID_PROD_CV3;
    }

    public static function excludeDupicateBorrowerAlert(?int $PCID): bool
    {
        return !in_array($PCID, [
            glPCID::PCID_PROD_CV3,
            //glPCID::PCID_DEV_DAVE,
        ]);
    }

    public static function validateBrokerProfileBasedOnBrokerPartner(?int $PCID, $externalBroker, $brokerPartnerType): string
    {
        $returnData = ' ignoreValidation ';
        if (in_array($PCID, [
                glPCID::PCID_PROD_CV3,
                //glPCID::PCID_DEV_DAVE,
            ]) && !$externalBroker && ($brokerPartnerType != '2' && $brokerPartnerType != '5')) {
            $returnData = ' mandatory';
        }
        return $returnData;
    }

    public static function validateBrokerProfileForRefferalPartner(?int $PCID, $externalBroker, $brokerPartnerType): string
    {
        $returnData = ' ignoreValidation ';
        if (in_array($PCID, [
                glPCID::PCID_PROD_CV3,
                //glPCID::PCID_DEV_DAVE,
            ]) && !$externalBroker && ($brokerPartnerType != '5')) {
            $returnData = ' mandatory';
        }
        return $returnData;
    }

    public static function hideLicenseInWebForm(?int $PCID, $publicUser): string
    {
        if (in_array($PCID, [
                glPCID::PCID_PROD_CV3,
                glPCID::PCID_11_CAPITAL_FINANCE,
            ]) && $publicUser) {
            return 'd-none';
        } else {
            return '';
        }
    }

    public static function validateBrokerProfileLicence(?int $PCID, $externalBroker, $brokerPartnerType, $licenseDisp = ''): string
    {
        $returnData = ' ignoreValidation ';
        if (!$brokerPartnerType) {
            return $returnData;
        }

        if (!in_array($PCID, [
                glPCID::PCID_PROD_CV3,
                //glPCID::PCID_DEV_DAVE,
            ]) && !$externalBroker && ($brokerPartnerType != '5') && empty($licenseDisp)) {
            $returnData = ' mandatory ';
        }
        return $returnData;
    }


    public static function showLoanInfoV2(?int $PCID): bool
    {

        if (in_array($PCID, [
            glPCID::PCID_PROD_CV3,
            glPCID::PCID_LENDINGWISE,
            3383,
            glPCID::PCID_PROD_KIAVI
        ])) {
            return true;
        } else {
            return false;
        }
    }

    public static function hideAndShowStateLicense($externalBroker, $stateLicenseType): string
    {
        $returnData = ' ';
        if (!$externalBroker && $stateLicenseType != 'Yes') {
            $returnData = ' d-none';
        }
        return $returnData;
    }

    public static function validateBrokerProfileHasNMLSState(?int $PCID, $externalBroker, $brokerPartnerType): string
    {
        $returnData = ' ignoreValidation ';

        if (in_array($PCID, [
                glPCID::PCID_PROD_CV3,
            ]) && !$externalBroker && ($brokerPartnerType == '1' || $brokerPartnerType == '2')) {
            $returnData = ' mandatory ';
        }
        return $returnData;
    }

    public static function validateBrokerProfileStateLicense(?int $PCID, $externalBroker, $brokerPartnerType, $haveLicense)
    {
        $returnData = ' ignoreValidation ';

        if (in_array($PCID, [
                glPCID::PCID_PROD_CV3,
            ]) && !$externalBroker && ($brokerPartnerType == '1' || $brokerPartnerType == '2') && $haveLicense == 'Yes') {
            $returnData = ' mandatory ';
        }
        return $returnData;
    }

    public static function getCountOfPastDeals(?int $PCID): int
    {
        if (in_array($PCID, [
            glPCID::PCID_PROD_CV3,
//            glPCID::PCID_DEV_DAVE,
        ])) {
            return 5;
        } else {
            return 3;
        }
    }

    public static function getBrokerPartnerType(?int $PCID, $isPublicuser): array
    {
        if (in_array($PCID, [
                glPCID::PCID_PROD_CV3,
//                glPCID::PCID_DEV_DAVE,
            ]) && $isPublicuser) {
            return glBrokerPartnerTypeArray::$glBrokerPartnerTypeCV3Array;
        } else {
            return glBrokerPartnerTypeArray::$glBrokerPartnerTypeArray;
        }
    }

    public static function customDocPortalLink(?int $PCID): bool
    {
        if (in_array($PCID, [
            glPCID::PCID_PROD_CV3,
            //glPCID::PCID_DEV_DAVE,
        ])) {
            return true;
        }
        return false;
    }


    public static function isPCEnabledForMaxlength(?int $PCID)
    {

        if (in_array($PCID, [
            glPCID::PCID_PROD_CV3,
//            glPCID::PCID_DEV_DAVE
        ])) {
            return true;
        } else {
            return false;
        }

    }

    public static function showHideUpdateTier($loanProgram)
    {
        if (in_array($loanProgram, ['BRL', 'Bri879861', 'Bri996', 'Ren496'])) {
            return '';
        } else {
            return 'd-none';
        }
    }

    public static function isPCAllowedToUseFromAddress(?int $PCID): bool
    {
        return $PCID != glPCID::PCID_PROD_CV3;
    }

    public static function showLoanNumberInTitle(?int $PCID): bool
    {
        if (in_array($PCID, [
            glPCID::PCID_PROD_CV3,
//            glPCID::PCID_DEV_DAVE
        ])) {
            return true;
        } else {
            return false;
        }
    }

    public static function hideCheckBoxInTermsAndCondition(?int $PCID): bool
    {
        return $PCID != glPCID::PCID_PROD_CV3;
    }

    public static function showLastLoginColumn(?int $PCID): bool
    {
        if (in_array($PCID, [
            glPCID::PCID_PROD_CV3,
//            glPCID::PCID_DEV_DAVE,
        ])) {
            return false;
        } else {
            return true;
        }
    }

    public static function showExpirationDateColumn(?int $PCID): bool
    {
        if (in_array($PCID, [
            glPCID::PCID_PROD_CV3,
//            glPCID::PCID_DEV_DAVE,
        ])) {
            return true;
        } else {
            return false;
        }
    }

    public static function hideInitialLoanAmountCV3(?int    $PCID,
                                                    ?string $loanProgram = '',
                                                    ?string $transactionType = ''
    ): ?string
    {
        if ($PCID == glPCID::PCID_PROD_CV3
            && $loanProgram != 'Bri996'
            && $transactionType == 'Purchase'
        ) {
            return ' display:none; ';
        }
        return null;
    }

    public static function propertyAccordiansNotExpandedBO(?int $PCID): bool
    {
        return in_array($PCID, [
            glPCID::PCID_PROD_CV3,
//            glPCID::PCID_DEV_DAVE,
        ]);
    }

    /**
     * @param int|null $PCID
     * @return bool
     */
    public static function showCV3HUDFields(?int $PCID): bool
    {
        return in_array($PCID, [
            glPCID::PCID_PROD_CV3,
            //glPCID::PCID_DEV_DAVE,
        ]);
    }

    /**
     * @param $PCID
     * @return bool
     */
    public static function showLoanInfoV2TotalLoanAmount($PCID): bool
    {
        if ($PCID == glPCID::PCID_PROD_CV3) {
            return true;
        } else {
            return false;
        }
    }

    public static function hideAssignedEmployeeCellNumber(?int $PCID): bool
    {
        return in_array($PCID, [
            glPCID::PCID_PROD_CV3,
//            glPCID::PCID_DEV_DAVE,
        ]);
    }

    public static function hideWalkthrough(?int $PCID): bool
    {
        return in_array($PCID, [
            glPCID::PCID_PROD_CV3,
            //glPCID::PCID_DEV_DAVE,
        ]);
    }

    public static function hideSendDocumentUploadRequest(?int $PCID): bool
    {
        return !in_array($PCID, [
            glPCID::PCID_INVESTMENT_PROPERTY_LLC
        ]);
    }

    public static function showApplicationReceivedDate(?int $PCID): bool
    {
        return in_array($PCID, [
            glPCID::PCID_PROD_CV3,
//            glPCID::PCID_DEV_DAVE,
        ]);
    }

    public static function TaskReminderTab_Order_In_DashBoard(?int $PCID): bool
    {
        if (CONST_ENVIRONMENT == 'production') {
            return $PCID == glPCID::PCID_HERITAGE_BANK;
        } else if (CONST_ENVIRONMENT == 'staging') {
            return in_array($PCID, [
//                glPCID::PCID_DEV_DAVE,
                glPCID::PCID_PROD_DAVE,
            ]);
        }
        return false;
    }

    public static function hideFannieMaeTab($PCID): bool
    {
        return $PCID == glPCID::PCID_PROD_CV3;
    }

    public static function disableDocUploadBorrowerNotification($PCID): bool
    {
        if (CONST_ENVIRONMENT == 'production') {
            return $PCID == glPCID::PCID_LOAN_BUD;
        } else if (CONST_ENVIRONMENT == 'staging') {
            return in_array($PCID, [
//                glPCID::PCID_DEV_DAVE,
                glPCID::PCID_PROD_DAVE,
            ]);
        }
        return false;
    }


    public static function showPostClosingTab(?int $PCID): bool
    {
        if (CONST_ENVIRONMENT == 'production') {
            return in_array($PCID, [
                glPCID::PCID_PROD_CV3,
                glPCID::PCID_LENDINGWISE,
            ]);
        } else if (CONST_ENVIRONMENT == 'staging') {
            return in_array($PCID, [
                glPCID::PCID_PROD_CV3,
//                glPCID::PCID_DEV_DAVE,
                glPCID::PCID_PROD_DAVE,
                glPCID::PCID_LENDINGWISE,
            ]);
        }
        return false;
    }

    public static function allowToGenerate_PDF_MailHistory(?int $PCID): bool
    {
        if (CONST_ENVIRONMENT == 'production') {
            return in_array($PCID, [
                glPCID::PCID_PROD_CV3,
                glPCID::PCID_LENDINGWISE,
            ]);
        } else if (CONST_ENVIRONMENT == 'staging') {
            return in_array($PCID, [
                glPCID::PCID_PROD_CV3,
//                glPCID::PCID_DEV_DAVE,
                glPCID::PCID_PROD_DAVE,
                glPCID::PCID_LENDINGWISE,
            ]);
        }
        return false;
    }

    public static function hideShowHMDAFields(?int $PCID): bool
    {
        if (CONST_ENVIRONMENT == 'production') {
            return !(in_array($PCID, [
                glPCID::PCID_PROD_CV3,
            ]));
        } else if (CONST_ENVIRONMENT == 'staging') {
            return !(in_array($PCID, [
                glPCID::PCID_PROD_CV3,
//                glPCID::PCID_DEV_DAVE,
                glPCID::PCID_PROD_DAVE,
            ]));
        }
        return true;
    }

    public static function hideHUDPackageButton(?int $PCID): bool
    {
        if (CONST_ENVIRONMENT == 'production') {
            return ($PCID == glPCID::PCID_PROD_CV3);
        } else if (CONST_ENVIRONMENT == 'staging') {
            return ($PCID == glPCID::PCID_PROD_CV3);
        }
        return false;
    }


    public static function isHUDPayeeTitleEditable(?int $PCID): bool
    {
        if (CONST_ENVIRONMENT == 'production') {
            return !in_array($PCID, [glPCID::PCID_PROD_CV3]);
        } else if (CONST_ENVIRONMENT == 'staging') {
            return !in_array($PCID, [
//                glPCID::PCID_DEV_DAVE,
                glPCID::PCID_PROD_CV3]);
        }
        return false;
    }

    public static function hideActionDate(?int $PCID): bool
    {
        if (CONST_ENVIRONMENT == 'production') {
            return !in_array($PCID, [
                glPCID::PCID_PROD_CV3,
            ]);
        } else if (CONST_ENVIRONMENT == 'staging') {
            return !in_array($PCID, [
                glPCID::PCID_PROD_CV3,
//                glPCID::PCID_DEV_DAVE,
            ]);
        }
        return false;
    }

    public static function showHideLicenseFields(?int $PCID, $externalBroker, $brokerPartnerType): string
    {
        if (!in_array($PCID, [
                glPCID::PCID_PROD_CV3,
            ]) && !$externalBroker && ($brokerPartnerType != '1' && $brokerPartnerType != '2')) {
            return 'd-none';
        } else {
            return '';
        }
    }

    public static function suspiciousNameCheck(?int $PCID): bool
    {
        if (CONST_ENVIRONMENT == 'production') {
            return $PCID == glPCID::PCID_LULU;
        } else if (CONST_ENVIRONMENT == 'staging') {
            return in_array($PCID, [
//                glPCID::PCID_DEV_DAVE,
                glPCID::PCID_PROD_DAVE,
            ]);
        }
        return false;
    }


    public static function validateBrokerProfileFields(?int $PCID, $publicUser): string
    {
        $returnData = ' ignoreValidation ';
        if (CONST_ENVIRONMENT == 'production') {
            if (in_array($PCID, [
                    glPCID::PCID_LCD_COMMERCIAL,
                ]) && $publicUser) {
                $returnData = ' mandatory ';
            }
        } else if (CONST_ENVIRONMENT == 'staging') {
            if (in_array($PCID, [
//                    glPCID::PCID_DEV_DAVE,
                ]) && $publicUser) {
                $returnData = ' mandatory ';
            }
        }
        return $returnData;
    }

    public static function hideUpvotey(?int $PCID): bool
    {
        if (CONST_ENVIRONMENT == 'production') {
            return in_array($PCID, [
                glPCID::PCID_PROD_CV3,
            ]);
        } else if (CONST_ENVIRONMENT == 'staging') {
            return in_array($PCID, [
                glPCID::PCID_PROD_CV3,
            ]);
        }
        return false;
    }

    public static function isPC_CentrifundLLC(?int $PCID): bool
    {
        if (CONST_ENVIRONMENT == 'production') {
            return in_array($PCID, [
                glPCID::PCID_CENTRIFUND_LLC,
            ]);
        } else if (CONST_ENVIRONMENT == 'staging') {
            return in_array($PCID, [
                glPCID::PCID_DEV_DAVE,
            ]);
        }
        return false;
    }

    public static function hideBrokerRegImage(int $PCID): bool
    {
        if (CONST_ENVIRONMENT == 'production') {
            return in_array($PCID, [
                glPCID::PCID_LENDINGDECK,
            ]);
        } else if (CONST_ENVIRONMENT == 'staging') {
            return in_array($PCID, [
                glPCID::PCID_DEV_DAVE,
            ]);
        }
        return false;
    }

    public static function hideRequiredDocColumn(?int $PCID): string
    {
        if (CONST_ENVIRONMENT == 'production') {
            if (in_array($PCID, [glPCID::PCID_LENDING_DECK])) {
                return 'display:none;';
            }
        } else if (CONST_ENVIRONMENT == 'staging') {
            if (in_array($PCID, [glPCID::PCID_DEV_DAVE])) {
                return 'display:none;';
            }
        }
        return '';
    }

    public static function getXactusURL(?int $PCID): string
    {
        if (CONST_ENVIRONMENT === 'production' && $PCID != glPCID::PCID_PROD_DAVE) {
            $xactusBaseUrl = $_ENV['XACTUS_BASE_URL_PROD'];
        }
        return $xactusBaseUrl ?? $_ENV['XACTUS_BASE_URL_TEST'];
    }

    public static function isThisPCIDLendingDeck(?int $PCID): bool
    {
        if (CONST_ENVIRONMENT == 'production') {
            return $PCID === glPCID::PCID_LENDING_DECK;
        } else if (CONST_ENVIRONMENT == 'staging') {
            return $PCID === glPCID::PCID_TEST_DANIEL;
        }
        return false;
    }
}
