<?php

namespace models\constants\gl;

/**
 *
 */
class glPCHMLOExportData
{
    /**
     * For HMLO XLS export fields added By Suresh K - (PT #: 151305042)
     * Adds sections 'HMDA - (Borrower)' and 'HMDA - (Co-Borrower)' and it's fields to Excel
     * export filters.
     *
     *  DAVENOTE -  "borrowerlinkfa" => "Borrower Link FA"
     * to make it easier on brain the left key could be the right value after processing (remove spaces, all lowercase, etc), here is processing done in exportFiles.php
     * $selectedField = strtolower(str_replace(" ", "", $selectedField));
     * $selectedField = str_replace("/", "", str_replace("-", "", $selectedField));
     * $selectedField = str_replace("%", "", $selectedField);
     * another example where they are not the same:
     * "borRehabPropCompleted" => "No of rehab/Construction done"
     * then in exportClientfiles.php you will see
     * $noofrehabconstructiondone = $fileHMLOExperienceInfoArray[$LMRId]['borRehabPropCompleted'];
     * SO YOU SEE "No of rehab/Construction done" IS CLEANED UP TO MAKE THE VARIABLE "$noofrehabconstructiondone" IN EXPORTCLIENTFILES.PHP
     */
    public static array $Fields = [
        'Borrower Info' => [
            'borrowerName'                 => 'Borrower First Name',
            'borrowerLName'                => 'Borrower Last Name',
            'borPhone'                     => 'Borrower Phone Number',
            'borCell'                      => 'Borrower Cell Number',
            'borFax'                       => 'Borrower Fax Number',
            'serviceProvider'              => 'SMS Service Provider',
            'borrowerEmail'                => 'Borrower Email',
            'borrowerSecondaryEmail'       => 'Borrower Secondary Email',
            'ssnNumber'                    => 'Client SSN Last 4',
            'ssnFullNumber'                => 'Full SSN',
            'borrowerDOB'                  => 'Borrower Date Of Birth',
            'entityName'                   => 'Business Entity Name',
            'networthOfBusinessOwned'      => 'Net Worth',
            'borCreditScoreRange'          => 'Credit Score range',
            'borExperianScore'             => 'Experian',
            'borEquifaxScore'              => 'Equifax',
            'borTransunionScore'           => 'Transunion',
            'midFicoScore'                 => 'Mid Fico Score',
            'borNoOfREPropertiesCompleted' => 'No of properties completed in last 36 months',
            'borNoOfYearRehabExperience'   => 'Years of rehab/construction Exp',
            'borRehabPropCompleted'        => 'No of rehab/Construction done',
            'borNoOfOwnProp'               => 'No of investment properties',
            'isBorUSCitizen'               => 'US Citizen',
            'totalCashInBankAccounts'      => 'Total Cash In Bank Accounts',
            'liquidAssets'                 => 'Liquid Assets',
            'authorizationStatus'          => 'Authorization Status',
            'trackRecord'                  => 'Track Record',
        ],

        'Co-borrower info' => [
            'coBorrowerName'     => 'Co-borrower First Name',
            'coBorrowerMName'    => 'Co-borrower Middle Name',
            'coBorrowerLName'    => 'Co-borrower Last Name',
            'coBorPhone'         => 'Co-borrower Phone Number',
            'coBorCell'          => 'Co-borrower Cell Number',
            'coBorFax'           => 'Co-borrower Fax Number',
            'coBServiceProvider' => 'Co-borrower SMS Service Provider',
            'coBorrowerEmail'    => 'Co-borrower Email',
            'coBSsnNumber'       => 'Co-Borrower SSN Last 4',
            'coBFullSsnNumber'   => 'Co-Borrower Full SSN',
            'coBorrowerDOB'      => 'Co-Borrower DOB',
        ],

        'Borrower Address' => [
            'presentAddress' => 'Borrower Address',
            'presentCity'    => 'Borrower City',
            'presentState'   => 'Borrower State',
            'presentZip'     => 'Borrower Zip',
        ],

        'Mailing address'       => [
            'mailingAddress' => 'Mailing Address',
            'mailingCity'    => 'Mailing City',
            'mailingState'   => 'Mailing State',
            'mailingZip'     => 'Mailing Zip',
        ],
        'HMDA - (General Info)' => [
            'legalEntityIdentifier'                => 'Legal Entity Identifier (LEI)',
            'introductoryRatePeriod'               => 'Introductory Rate Period',
            'universalLoanIdentifier'              => 'Universal Loan Identifier (ULI)',
            'interestOnlyPayment'                  => 'Interest-Only Payments',
            'HMDALoanPurpose'                      => 'HMDA Loan Purpose',
            'HMDALoanAmount'                       => 'HMDA Loan Amount',
            'actionTaken'                          => 'Action Taken',
            'typeOfPurchaser'                      => 'Type Of Purchaser',
            'totalUnits'                           => 'Total Units',
            'reasonForDenial'                      => 'Reason For Denial',
            'submissionOfApplicationFrom'          => 'Submission Of Application From',
            'combinedLoanToValueRatio'             => 'Combined Loan-To-Value Ratio',
            'censusTract'                          => 'Census Tract',
            'manufacturedHomeSecuredPropertyType'  => 'Manufactured Home Secured Property Type',
            'manufacturedHomeLandPropertyInterest' => 'Manufactured Home Land Property Interest',
            'HMDALoanType'                         => 'HMDA Loan Type',
            'preapproval'                          => 'Preapproval',
            'multifamilyAffordableUnits'           => 'Multifamily Affordable Units',
            'constructionMethod'                   => 'Construction Method',
            'applicationChannel'                   => 'Application Channel',
            'initiallyPayableToYourInstitution'    => 'Initially Payable To Your Institution',
            'rateSpread'                           => 'Rate Spread',
            'HOEPAStatus'                          => 'HOEPA Status',
            'totalLoanCostOrTotalPointsAndFees'    => 'Total Loan Cost Or Total Points And Fees',
            'originationCharges'                   => 'Origination Charges',
            'discountPoints'                       => 'Discount Points',
            'lenderCredits'                        => 'Lender Credits',
            'HMDAPrepaymentPenalty'                => 'HMDA Prepayment Penalty',
            'debtToIncomeRatio'                    => 'Debt-To-Income Ratio',
            'balloonPayment'                       => 'Balloon Payment',
            'automatedUnderwritingSystem'          => 'Automated Underwriting System',
            'AUSFreeFormTextFieldForCode5'         => 'AUS: Free Form Text Field for Code 5',
            'automatedUnderwritingSystemResult'    => 'Automated Underwriting System Result',
            'AUSResultFreeFormTextFieldForCode16'  => 'AUS Result: Free Form Text Field for Code 16',
            'reverseMortgage'                      => 'Reverse Mortgage',
            'openEndLineOfCredit'                  => 'Open-End Line Of Credit',
            'businessOrCommercialPurpose'          => 'Business Or Commercial Purpose',
            'areThereInterestOnlyPayment'          => 'Are There Interest-Only Payments',
            'negativeAmortization'                 => 'Negative Amortization',
            'otherNonAmortizingFeatures'           => 'Other Non-Amortizing Features',
            'countyCode'                           => 'County Code',
        ],
        'HMDA - (Borrower)'     => [
            'PublishBInfo'                                         => 'Borrower Furnish this information',
            'BEthnicity'                                           => 'Borrower Ethnicity',
            'BEthnicitySub'                                        => 'Borrower Ethnicity Sub',
            'bFiEthnicitySubOther'                                 => 'Borrower Ethnicity Print Origin',
            'BRace'                                                => 'Borrower Race',
            'BRaceSub'                                             => 'Borrower Race Sub',
            'bFiRaceAsianOther'                                    => 'Borrower Asian Print Race',
            'bFiRacePacificOther'                                  => 'Borrower Pacific Print Race',
            'BGender'                                              => 'Borrower Sex',
            'BVeteran'                                             => 'Borrower Veteran',
            'borrowerCreditScoringModelOfApplicant'                => 'Name and Version of Credit Scoring Model',
            'borrowerCreditScoringModelConditionalFreeOfApplicant' => 'Name and Version of Credit Scoring Model: Conditional Free Form Text Field',
            'bFiEthnicity'                                         => 'Was the ethnicity of the Borrower collected on the basis of visual observation or surname',
            'bFiSex'                                               => 'Was the sex of the Borrower collected on the basis of visual observation or surname',
            'bFiRace'                                              => 'Was the race of the Borrower collected on the basis of visual observation or surname',
            'bDemoInfo'                                            => 'The Demographic Information was provided through',
            'actionDate'                                           => 'Action Date',
            'excludeFromHMDAReport'                                => 'Exclude from HMDA Reporting',
        ],

        'HMDA - (Co-Borrower)' => [
            'PublishCBInfo'                                          => 'Co-Borrower Furnish this information',
            'CBEthnicity'                                            => 'Co-Borrower Ethnicity',
            'CBEthnicitySub'                                         => 'Co-Borrower Ethnicity Sub',
            'CBEthnicitySubOther'                                    => 'Co-Borrower Ethnicity Print Origin',
            'CBRace'                                                 => 'Co-Borrower Race',
            'CBRaceSub'                                              => 'Co-Borrower Race Sub',
            'CBRaceAsianOther'                                       => 'Co-Borrower Asian Print Race',
            'CBRacePacificOther'                                     => 'Co-Borrower Pacific Print Race',
            'CBGender'                                               => 'Co-Borrower Sex',
            'CBVeteran'                                              => 'Co-Borrower Veteran',
            'coBorrowerCreditScoringModelOfApplicant'                => 'Co-Borrower Name and Version of Credit Scoring Model',
            'coBorrowerCreditScoringModelConditionalFreeOfApplicant' => 'Co-Borrower Name and Version of Credit Scoring Model: Conditional Free Form Text Field',
            'CBFiEthnicity'                                          => 'Was the ethnicity of the Co-Borrower collected on the basis of visual observation or surname',
            'CBFiGender'                                             => 'Was the sex of the Co-Borrower collected on the basis of visual observation or surname',
            'CBFiRace'                                               => 'Was the race of the Co-Borrower collected on the basis of visual observation or surname',
            'CBDDemoInfo'                                            => 'Co-Borrower Demographic Information was provided through',
        ],

        'Entity Info' => [
            'entityName'        => 'Entity Name',
            'dbaName'           => 'DBA Name',
            'ein'               => 'EIN',
            'entityType'        => 'Entity Type',
            'entityAddress'     => 'Entity Address',
            'entityCity'        => 'Entity City',
            'entityState'       => 'Entity State',
            'entityZip'         => 'Entity Zip',
            'ofEmployees'       => 'Of Employees',
            'propertyOwnership' => 'Property Ownership',
            'valueOfProperty'   => 'Value Of Property',
            'Naics'             => 'Naics Code',
        ],

        'Entity Members Info' => [
            'memberType'                => 'Member Type',
            'memberCategory'            => 'Member Category',
            'memberName'                => 'Member Name',
            'memberTitle'               => 'Member Title',
            'memberOwnership'           => 'Member Ownership',
            'memberAnnualSalary'        => 'Member Annual Salary',
            'memberAddress'             => 'Member Address',
            'memberPhone'               => 'Member Phone',
            'memberCell'                => 'Member Cell',
            'memberSSN'                 => 'Member SSN',
            'memberDOB'                 => 'Member Date Of Birth',
            'memberCreditScore'         => 'Member Credit Score',
            'memberEmail'               => 'Member Email',
            'memberDriversLicense'      => 'Member Drivers License',
            'memberDriversLicenseState' => 'Member Drivers License State',
            'memberTin'                 => 'Member TIN',
            'memberPersonalGuarantee'   => 'Member Personal Guarantee',
            'memberAuthorizedSigner'    => 'Member Authorized Signer',
            'memberCitizenship'         => 'Member Citizenship',
            'memberMaritalStatus'       => 'Member Marital Status',
            'memberMarriageDate'        => 'Member Marriage Date',
            'memberDivorceDate'         => 'Member Divorce Date',
            'memberMaidenName'          => 'Member Maiden Name',
            'memberSpouseName'          => 'Member Spouse Name',
        ],

        'Loan Info' => [
            'MERSID'                      => 'MERS ID',
            'HMLOLender'                  => 'Originator',
            'LOISentDate'                 => 'LOI Sent Date',
            'loanTermExpireDate'          => 'Loan Terms Expire Date',
            'closedDate'                  => 'Actual Closing Date',
            'targetClosingDate'           => 'Target Closing Date',
            'services'                    => 'Loan Program',
            'internalLoanProgram'         => 'Internal Loan Program',
            'typeOfHMLOLoanRequesting'    => 'Transaction Type',
            'borroweroccupancy'           => 'Borrower Occupancy',
            'lien1Payment'                => 'Mortgage payment',
            'taxes1'                      => 'Escrowed Taxes',
            'insurance1'                  => 'Escrowed Insurance',
            'totalMonthlyPaymentAmt'      => 'Monthly Payment',
            'netMonthlyPayment'           => 'Monthly Payment (PITIA) - Loan Info',
            'noOfPropertiesAcquiring'     => 'No of properties collateralized',
            'acquisitionPriceFinanced'    => 'Acquisition Price Financed',
            'maxAmtToPutDown'             => 'Acquisition Down Payment',
            'rehabCost'                   => 'Rehab/Construction Cost',
            'rehabcostfinanced'           => 'Rehab Cost Financed',
            'initialDrawAmount'           => 'Initial Draw Amount',
            'homeValue'                   => 'As is Value',
            'aggregateDSCR'               => 'Aggregate DSCR',
            'assessedValue'               => 'After Repair Value',
            'closingCostFinanced'         => 'Closing Cost Financed',
            'totalProjectCost'            => 'Total Project Cost',
            'simpleArv'                   => 'Simple ARV %',
            'fullArv'                     => 'Full ARV %',
            'acquisitionLTV'              => 'Acquisition LTV',
            'marketLTV'                   => 'Market LTV',
            'Loan-to-Cost'                => 'Loan To Cost',
            'perRehabCostFinanced'        => '% of Rehab Cost Financed',
            'totalCashToClose'            => 'Cash to Close',
            'totalRequiredReserves'       => 'Required Reserves',
            'HMLOEstateHeldIn'            => 'Estate held in',
            'lienPosition'                => 'Lien Position',
            'rehabrequired'               => 'Rehab Required',
            'totalRehabCost'              => 'Total Rehab Cost',
            'applicantConfirmation'       => 'Applicant Confirmation',
            'propertyConstructionLevel'   => 'Property Construction Level',
            'exitstrategy'                => 'Exit Strategy',
            'borComment'                  => 'Exit Strategy Explanation',
            'initialLoanAmount'           => 'Initial Loan Amount',
            'totalLoanAmount'             => 'Total Loan Amount',
            'currentRate'                 => 'Interest Rate',
            'costOfCapital'               => 'Cost of Capital',
            'yieldSpread'                 => 'Yield Spread',/* "proInsPolicyExpDate" =>"Policy Expiration Date", */
            'costBasis'                   => 'Purchase Price',
            'loanTerm'                    => 'Loan Term',
            'loanTermNumericalValue'      => 'Loan Term (Numerical value only)',
            'loanTermTextValue'           => 'Loan Term (Text value only)',
            'currentLoanBalance'          => 'Current Loan Balance',
            'currentEscrowBalance'        => 'Current Escrow Balance',
            'prepaidInterestReserve'      => 'Pre-paid Interest Reserve',
            'totalDrawsFunded'            => 'Total Draws Funded',
            'drawAmount'                  => 'Draw Amount',
            'drawDate'                    => 'Draw Date',
            'amortization'                => 'Amortization',
            'amortizationType'            => 'Amortization Type',
            'prePaymentPenaltyOptions'    => 'Pre-Payment Penalty Options',
            'prePaymentPenalty'           => 'Pre-Payment Penalty',
            'servicingNumber'             => 'Servicing Number',
            'desiredClosingDate'          => 'Desired Closing Date',
            'daysUntilClosing'            => 'Days Until Closing',
            'exitFeePoints'               => 'Exit Fee Points',
            'exitFeeAmount'               => 'Exit Fee Amount',
            'projectedforeclosuredate'    => 'Projected Foreclosure Date',
            'payoffamount'                => 'Pay Off Amount',
            'foreclosureDate'             => 'Foreclosure Date',
            'accrualType'                 => 'Accrual Type',
            'paymentFrequency'            => 'Payment Frequency',
            'paymentBased'                => 'Payment Based',
            'totalCashOut'                => 'Total Cash Out',
            'extensionOption'             => 'Extension Option',
            'extensionOptionPoints'       => 'Extension Option Points',
            'extensionOptionTerm'         => 'Extension Option Term',
            'extensionOptionRate'         => 'Extension Option Rate',
            'intAssRehabBudget'           => 'Rehab Budget',
            'lenderRevenue'               => 'Lender Revenue',
            'rateLockDate'                => 'Rate Lock Date',
            'rateLockPeriod'              => 'Rate Lock Period',
            'rateLockExpirationDate'      => 'Rate Lock Expiration Date',
            'PSAClosingDate'              => 'PSA Closing Date',
            'buildingAnalysisOutstanding' => 'Building Analysis Outstanding',
            'buildingAnalysisNeed'        => 'Building Analysis Need',
            'buildingAnalysisDueDate'     => 'Building Analysis Due Date',
            'VOMStatus'                   => 'VOM Status',
            'payoffStatus'                => 'Payoff Status',
            'costSpent'                   => 'Cost Spent',
        ],

        'Loan Setup' => [
            'maxLTVPercent'        => 'Max LTV%',
            'minDSCRRatio'         => 'Minimum Maintained DSCR Ratio',
            'minActBal'            => 'Minimum Account Balance with Lender',
            'floorRate'            => 'Floor Rate',
            'prepayLockOut'        => 'Prepayment Penalty Lockout Period in Months',
            'isAdjustable'         => 'Is this loan an adjustable or a fixed rate mortgage?',
            'isPreStabilized'      => 'Will there be a Pre-Stabilized and Post-Stabilized Period for this Loan?',
            'initialTermYears'     => 'Initial Term in Years',
            'initialRateMargin'    => 'Initial Interest Rate Margin',
            'initialRateIndex'     => 'Initial Interest Rate Index',
            /*            'initialRateFloor' => 'Initial Interest Rate Floor',*/
            'initialAmor'          => 'Initial Amortization',
            'initialTermAdjust'    => 'Initial Term Adjustment Period',
            'indicatedRateDate'    => 'Indicated Rate As Of',
            'indicatedRatePercent' => 'Indicated Rate%',
            'stabilizedrateMargin' => 'Post Stabilized Rate Margin',
            'stabilizedRateIndex'  => 'Post Stabilized Rate Index',
            'stabilizedRateFloor'  => 'Post Stabilized Rate Floor',
            'stabilizedAmor'       => 'Post Stabilized Amoritization',
            //'isSecondTerm'         => 'Will there be a 2nd Term to this loan?',
            'secondTermYears'      => '2nd Term in Years',
            'secondRateMargin'     => '2nd Rate Margin',
            'secondRateIndex'      => '2nd Rate Index',
            /*            'secondRateFloor' => '2nd Rate Floor',*/
            'secondAmor'           => '2nd Rate Amortization',
            'secondTermAdjust'     => '2nd Term Adjustment Period',
        ],

        'Property Info' => [
            'propertyAddress'       => 'Property Address',
            'propertyCity'          => 'Property City',
            'propertyState'         => 'Property State',
            'propertyZip'           => 'Property Zip',
            'propertyCounty'        => 'Property County',
            'presentOccupancy'      => 'Present Occupancy',
            'propertycondition'     => 'Property Condition',
            'propertyType'          => 'Property Type',
            'occupancyNotes'        => 'Occupancy Notes',
            'conditionNotes'        => 'Condition Notes',
            'propertyFeatures'      => 'Features',
            'yearBuilt'             => 'Year Built',
            'acres'                 => 'Lot Size',
            'totalsqft'             => 'Total Sq Ft',
            'howManyBedRoom'        => 'Bedrooms',
            'howManyBathRoom'       => 'Bathrooms',
            'taxYear'               => 'Tax Year',
            'legalDescription'      => 'Legal Description',
            'ulinktoproperty'       => 'URL link to property',
            'LBContactName'         => 'Property Access Contact Name',
            'LBContactPhone'        => 'Property Access Contact Phone',
            'LBInfo'                => 'Lock Box Info',
            'annualpropertytax'     => 'Annual Property Tax',
            'parcelNo'              => 'Parcel No',
            'currentADU'            => 'Current # of ADU(s)',
            'futureADU'             => 'Future # of ADU(s)',
            'ofUnits'               => '# of Units',
            'halfBaths'             => '# Half Baths',
            'hoaFees'               => 'Cash-Flow HOA Fees',
            'grossPotentialIncome'  => 'Gross Potential Income',
            'debtServiceRatio'      => 'Debt Service Ratio',
            'debtServiceRatioPITIA' => 'Debt Service Ratio PITIA',
            'hoaMonthlyFees'        => 'HOA Monthly Fees',
            'municipality'          => 'Municipality',
            'isBlanketLoan'         => 'Is this loan being cross collateralized?',
            'propertyLocation'      => 'Property Location',
            'futurePropertyType'    => 'Future property type',
            'futureNoOfUnits'       => 'Future # of units',
            'propertyAdjustedSqFt'  => 'Future square footage',
            'propertyFloodZone'     => 'Flood Zone',
        ],

        'Appraisal Info' => [
            'propertyAppraisalAsIsValue'       => 'Property Appraisal As Is Value',
            'propertyAppraisalRehabbedValue'   => 'Property Appraisal Rehabbed Value',
            'propertyAppraisalMonthlyRent'     => 'Property Appraisal Monthly Rent',
            'propertyAppraisalJobTypes'        => 'Property Appraisal Job Types',
            'propertyAppraisalDateObtained'    => 'Property Appraisal Date Obtained',
            'propertyAppraisalOrderDate'       => 'Property Appraisal Order Date',
            'propertyAppraisalComments'        => 'Property Appraisal Comments',
            'propertyAppraisalEffectiveDate'   => 'Property Appraisal Effective Date',
            'primaryAppraisalEcoaDeliveryDate' => 'Primary Appraisal Ecoa Delivery Date',
            'propertyAppraisalInspectionDate'  => 'Property Appraisal Inspection Date',
        ],

        'Property Valuation' => [
            'Property1Estimatesvalue' => 'Property 1 Estimates value',
            'Property1ZillowValue'    => 'Property 1 Zillow value',
            'zillowvalue'             => 'zillow Value',
            'zillowRentvalue'         => 'zillow Rent Value',

            'intAssAppraiserName' => 'Appraiser Name',
            'intAssPhone'         => 'Appraiser Phone Number',
            'intAssEmail'         => 'Appraiser Email',
            'AppraiserCompany'    => 'Appraiser Company',

            'appraisedValue'         => 'Appraised Value',
            'appraiserRehabbedValue' => 'Appraiser Rehabbed Value',
            'appraiserMonthlyRent'   => 'Appraiser Monthly Rent',
            'appraisalDate'          => 'Appraisal Date',

            'appraisal1OrderDate' => 'Appraiser Order Date',

            'avmasisvalue'    => 'AVM As Is Value',
            'avmamonthlyrent' => 'AVM Monthly Rent',

            'BPOValue'         => 'BPO Value',
            'BPORehabbedValue' => 'BPO Rehabbed Value',
            'BPOMonthlyRent'   => 'BPO Monthly Rent',
            'BPODateObtained'  => 'BPO Date Obtained',

            'costBasis'                => 'Purchase Price',
            'homeValue'                => 'Property Value As Is',
            //'intAssRehabbedValue' => 'Rehabbed value',
            'assessedmonthlyrent'      => 'Assessed Monthly Rent',
            'appraisalNotes'           => 'Valuation Notes',
            'requiredValuationMethods' => 'Required Valuation Methods',
            'requestedReturnDate'      => 'Requested Return Date',
        ],
        'Additional Questions' => [
            'acceptedPurchase'             => 'Is there an accepted purchase agreement?',
            'PAExpirationDate'             => 'Purchase Agreement Expiration Date',
            'loanGuarantee'                => 'Loan Guarantee',
            'involvedPurchase'             => 'Is there a wholesale fee involved with this purchase?',
            'wholesaleFee'                 => 'How much is the wholesale fee?',
            'exitStrategy'                 => 'Loan/Exit Plan',
            'borComment'                   => 'Loan/Exit Plan Explanation',
            'haveBorSquareFootage'         => 'Are you adding square footage?',
            'borNoOfSquareFeet'            => 'How many additional square feet are you adding?',
            'rehabToBeMade'                => 'Improvements to be Completed',
            'rehabTime'                    => 'Renovation Timeframe (months)',
            'isBorBorrowedDownPayment'     => 'Will there be a 2nd lien or borrowed money for down payment?',
            'secondaryHolderName'          => 'Jr. Lien Holder Name',
            'secondaryFinancingAmount'     => 'Jr. Lien Amount',
            'borBorrowedDownPaymentExpln'  => '(If Yes, Provide explanation.)',
            'purposeOfLoan'                => 'Purpose(s) of loan',
            'securityInstrument'           => 'Security Instrument',
            'HMLOEstateHeldIn'             => 'Estate will be held in',
            'isBorPersonallyGuaranteeLoan' => 'Will borrower personally guarantee this loan?',
            'balloonPayment'               => 'Is this Loan subject to a Balloon Payment?',
            'displayNotes'                 => 'Display Notes',
            'useOfFunds'                   => 'Use of Funds',
        ],
        'Title Info'         => [
            'titleOrderedDate'  => 'Title Ordered Date',
            'titleName'         => 'Name on Title',
            'contact'           => 'Title Rep First Name',
            'titleContactLName' => 'Title Rep Last Name',
            'titleCompany'      => 'Title Company',
            'titleEmail'        => 'Title Email',
            'titlePhone'        => 'Title Phone',
            'titleReportDate'   => 'Title Report Date',
        ],
        'Attorney Info'      => [
            'titleAttorneyName'        => 'Attorney Name',
            'titleAttorneyFirmName'    => 'Attorney Firm Name',
            'titleAttorneyEmail'       => 'Attorney Email',
            'titleAttorneyPhoneNumber' => 'Attorney Phone',
        ],

        /*    "Insurance Info" => array("proInsName" => "Name of Company", "proInsFirstName" => "Insurance Rep First Name",
         "proInsLastName" => "Insurance Rep Last Name", "proIncEmail" => "Insurance Rep Email", "proIncPhone" => "Insurance Rep Phone",
         "proIncCellNo" => "Insurance Rep Cell", "annualPremium" => "Annual Premium", "floodInsurance1" => "Annual Flood",
        "proInsPolicyNo" => "Insurance Policy Number", "proInsPolicyExpDate" => "Policy Expiration Date", "proInsCarrier" => "Name of Carrier", "proInsType" => "Types of Required Insurance"),*/

        'Insurance Rep Info' => [
            'proInsName'      => 'Name of Company',
            'proInsFirstName' => 'Insurance Rep First Name',
            'proInsLastName'  => 'Insurance Rep Last Name',
            'proIncEmail'     => 'Insurance Rep Email',
            'proIncPhone'     => 'Insurance Rep Phone',
            'proIncCellNo'    => 'Insurance Rep Cell',
            /* "policyAnnualPremiums" => "Annual Premium",/* "floodInsurance1" => "Annual Flood", "policyNumbers" => "Insurance Policy Number",
             "policyExpiryDates" => "Policy Expiration Date", "policyCarriers" => "Name of Carrier", "policyTypes" => "Types of Required Insurance" */
        ],


        'Insurance Info' => [
            'policyTypes'           => 'Types of Required Insurance',
            'policyCarriers'        => 'Name of Carrier',
            'policyName'            => 'Policy Name',
            'policyNumbers'         => 'Insurance Policy Number',
            'policyAnnualPremiums'  => 'Annual Premium',/* "floodInsurance1" => "Annual Flood",*/
            'policyExpiryDates'     => 'Policy Expiration Date',
            'policyEffectiveDates'  => 'Policy Effective Date',
            'insuranceDateReceived' => 'Insurance Date Received',
            'insuranceDateOrdered'  => 'Insurance Date Ordered',
        ],

        'Refinance Current Mortgage' => [
            'originalLienNumber'                => 'Original Lien Number',
            'originalPayOffAmount'              => 'Original Pay Off Amount',
            'originalPurchaseDate'              => 'Original Purchase Date',
            'refinanceCurrentLender'            => 'Refinance Current Lender',
            'refinanceCurrentLenderFullAddress' => 'Current Lender Full Address',
            'refinanceCurrentLenderEmail'       => 'Current Lender Email',
            'originalPurchasePrice'             => 'Original Purchase Price',
            'originalMaturityDate'              => 'Maturity Date of Loan',
            'originalPrepayPenalty'             => 'Prepayment Penalty $',
            'originalPrepayPercentage'          => 'Prepayment Penalty %',
            'costOfImprovementsMade'            => 'Cost of Improvements Made',
            'originalTaxesIncluded'             => 'Are taxes and insurance included in the payment?',
            'refinanceMonthlyPayment'           => 'Refinance Monthly Payment',
            'refinanceCurrentRate'              => 'Refinance Current Rate',
            'refinanceCurrentLoanBalance'       => 'Refinance Current Loan Balance',
            'goodThroughDate'                   => 'Good Through Date',
            'subjectOriginalBalance'            => 'Original Balance',

        ],

        'Personal Inc & Exp - Borrower' => [
            'occupation1'                 => 'Borrower Occupation',
            'boEmploymentType'            => 'Borrower Employment Type',
            'employer1'                   => 'Borrower Employer Name',
            'grossIncome1'                => 'Borrower Base Employment Income',
            'primTotalNetHouseHoldIncome' => 'Borrower Net Income',
            'lien1Payment'                => 'Borrower This Loan',
            'primTotalHouseHoldExpenses'  => 'Borrower Total Expenses',
        ],

        'Personal Inc & Exp - Co-Borrower' => [
            'occupation2'               => 'Co-Borrower Occupation',
            'coBoEmploymentType'        => 'Co-Borrower Employment Type',
            'employer1'                 => 'Co-Borrower Employer Name',
            'grossIncome2'              => 'Co-Borrower Base Employment Income',
            'coTotalNetHouseHoldIncome' => 'Co-Borrower Net Income',
            'coTotalHouseHoldExpenses'  => 'Co-Borrower Total Expenses',
        ],

        'Contact Info - Branch' => [
            'ExecName'      => 'Branch Name',
            'branchCompany' => 'Branch Company',
            'branchPhone'   => 'Branch Phone',
            'branchCell'    => 'Branch Cell',
            'branchFax'     => 'Branch Fax',
            'branchEmail'   => 'Branch Email',
        ],

        'Contact Info - Assigned Employee' => [
            'assignedEmployees' => 'Assigned Employees',
            'empName'           => 'Employee Name',
            'empPhone'          => 'Employee Phone',
            'empCell'           => 'Employee Cell',
            'empFax'            => 'Employee Fax',
            'empEmail'          => 'Employee Email',
        ],

        'Contact Info - Broker' => [
            'brokerFName'  => 'Agent First Name',
            'brokerLName'  => 'Agent Last Name',
            'agentCompany' => 'Agent Company',
            'agentPhone'   => 'Agent Phone',
            'agentCell'    => 'Agent Cell',
            'agentFax'     => 'Agent Fax',
            'agentEmail'   => 'Agent Email',
        ],

        'Contact Info - Loan Officer' => [
            'FName'   => 'LO First Name',
            'LName'   => 'LO Last Name',
            'Company' => 'LO Company',
            'Phone'   => 'LO Phone',
            'Cell'    => 'LO Cell',
            'Fax'     => 'LO Fax',
            'Email'   => 'LO Email',
        ],

        'Admin, Dates & Misc' => [
            'recordDate'                   => 'File Created Date',
            'createdDateWithTimestamp'     => 'Created Date With Timestamp',
            'lastUpdatedDate'              => 'Last Updated date',
            'lastUpdatedDateTime'          => 'Last Updated Date/Time',
            'receivedDate'                 => 'Received Date',
            'primeStatusId'                => 'Status',
            'substatus'                    => 'Sub Status',
            'daysincurrentstatus'          => 'Days In Current Status',
            'daysinpreviousstatus'         => 'Days In Previous Status',
            'loanNumber'                   => 'Loan Number',
            'closingDate1'                 => 'Closing Date',
            'trialPaymentDate1'            => 'Date of First payment due',
            'typeOfSale'                   => 'Type of Purchase',
            'bankNumber'                   => 'Bank Number',
            'lienAmount'                   => 'Lien Amount',
            'intAssRehabbedValue'          => 'Rehabbed value',
            'intAssRecommendedOffer'       => 'Recommended Offer',
            'maturityDate'                 => 'Maturity Date',
            'payOffDate'                   => 'Pay Off Date',
            'leadSource'                   => 'Lead Source',
            'referringParty'               => 'Referring Party',
            'loanSaleDate'                 => 'Date Of Loan Sale Agreement',
            'fundingDate'                  => 'Funding Date',
            'servicingstatus'              => 'Servicing Status',
            'workflow'                     => 'Workflow',
            'isBorPersonallyGuaranteeLoan' => 'Will borrower personally guarantee this loan?',
            'salesDate'                    => 'Sale date',
            'disclosureSentDate'           => 'Disclosure Sent Date',
            'borrowerCallBack'             => 'Borrower Call Back',
            'welcomeCallDate'              => 'Welcome Call Date',
            'requiredDocStatus'            => 'Required Doc Status',
            'targetSubmissionDate'         => 'Target Submission Date',
            'finalSubmissionDate'          => 'Final Submission Date',
            'initialCommentsDate'          => 'Initial Comments Date',
            'finalApprovalDate'            => 'Final Approval Date',
            'buildAnalysisDeliveredDate'   => 'Build Analysis Delivered Date',
            'welcomeCallStatus'            => 'Welcome Call',
            'propertyAppraisalStatus'      => 'Appraisal Status',
            'loanDocumentDate'             => 'Loan Document Date',
            'lenderInternalNotes'          => 'Lender Internal Notes',
        ],

        'Fees & Cost' => [
            'originationPointsRate'   => 'Origination Points Rate',
            'originationPointsValue'  => 'Origination Points Value',
            'brokerPointsRate'        => 'Broker Points Rate',
            'brokerPointsValue'       => 'Broker Points Value',
            'closingCostFinancingFee' => 'Closing Cost Financing fee',
            'attorneyFee'             => 'Attorney fee',
            'applicationFee'          => 'Application Fee',
            'appraisalFee'            => 'Appraisal Fee',
            'estdTitleClosingFee'     => 'Estimated Title Insurance Fees',
            'processingFee'           => 'Processing Fees',
            'drawsSetUpFee'           => 'Draws Set Up Fee',
            'drawsFee'                => 'Draws Fee',
            'valuationBPOFee'         => 'Valuation - BPO',
            'valuationAVMFee'         => 'Valuation - AVM',
            'creditReportFee'         => 'Credit Report',
            'backgroundCheckFee'      => 'Background Check',
            'taxServiceFee'           => 'Tax Service',
            'documentPreparationFee'  => 'Document Preparation',
            'wireFee'                 => 'Wire Fee',
            'servicingSetUpFee'       => 'Servicing Set Up Fee',
            'floodCertificateFee'     => 'Flood Certificate',
            'floodServiceFee'         => 'Flood Service',
            'inspectionFees'          => 'Inspection Fees',
            'projectFeasibility'      => 'Project Feasibility',
            'dueDiligence'            => 'Due Diligence',
            'UccLienSearch'           => 'Ucc/Lien Search',
            'thirdPartyFees'          => 'Lender Credit to Offset 3rd Party Fees',
            'otherFee'                => 'Other',
            'escrowFees'              => 'Escrow Fees',
            'recordingFee'            => 'Recording Fee',
            'underwritingFees'        => 'Underwriting Fees',
            'propertyTax'             => 'Property tax',
            'discountfee'             => 'Discount Fee',
            'travelNotaryFee'         => 'Travel Notary Fee',
            'prePaidInterest'         => 'Pre paid Interest',
            'realEstateTaxes'         => 'Real Estate Taxes',
            'insurancePremium'        => 'Insurance Premium',
            'payOffLiensCreditors'    => 'Pay Off Liens/Creditors',
            'wireTransferFeeToTitle'  => 'Wire Transfer Fee to Title',
            'wireTransferFeeToEscrow' => 'Wire Transfer Fee to Escrow',
            'pastDuePropertyTaxes'    => 'Past Due Property Taxes',
            'taxImpoundsFee'          => 'Tax impounds',
            'insImpoundsFee'          => 'Ins impounds',
            'earnestDeposit'          => 'Earnest Deposit',
            'refinancecurrentlender'  => 'Current Lender Info',
            'totalPaidPayables'       => 'Total Paid Payables',
            'brokerProcessingFee'     => 'Broker Processing Fee',
            'sellerCreditsFee'        => 'Seller Credits Fee',
        ],

        'HUD' => [
            'HUD1001InitialDepositforEscrowPaidFromBorrowerSettlement' => 'HUD 1001 Initial Deposit for Escrow Paid From Borrower Settlement',
            'HUD1001InitialDepositforEscrowPaidFromSellerSettlement'   => 'HUD 1001 Initial Deposit for Escrow Paid From Seller Settlement',
            'HUD1002HazardInsurancePaidFromBorrowerSettlement'         => 'HUD 1002 Hazard Insurance Paid From Borrower Settlement',
            'HUD1002HazardInsurancePaidFromSellerSettlement'           => 'HUD 1002 Hazard Insurance Paid From Seller Settlement',
            'HUD1003PropertyTaxesPaidFromBorrowerSettlement'           => 'HUD 1003 Property Taxes Paid From Borrower Settlement',
            'HUD1003PropertyTaxesPaidFromSellerSettlement'             => 'HUD 1003 Property Taxes Paid From Seller Settlement',
            'HUD1004FloodInsurancePaidFromBorrowerSettlement'          => 'HUD 1004 Flood Insurance Paid From Borrower Settlement',
            'HUD1004FloodInsurancePaidFromSellerSettlement'            => 'HUD 1004 Flood Insurance Paid From Seller Settlement',
            'HUD1005PaidFromBorrowerSettlement'                        => 'HUD 1005 Paid From Borrower Settlement',
            'HUD1005PaidFromSellerSettlement'                          => 'HUD 1005 Paid From Seller Settlement',
            'HUD1006PaidFromBorrowerSettlement'                        => 'HUD 1006 Paid From Borrower Settlement',
            'HUD1006PaidFromSellerSettlement'                          => 'HUD 1006 Paid From Seller Settlement',
            'HUD1007PaidFromBorrowerSettlement'                        => 'HUD 1007 Paid From Borrower Settlement',
            'HUD1007PaidFromSellerSettlement'                          => 'HUD 1007 Paid From Seller Settlement',
            'HUD1008PaidFromBorrowerSettlement'                        => 'HUD 1008 Paid From Borrower Settlement',
            'HUD1008PaidFromSellerSettlement'                          => 'HUD 1008 Paid From Seller Settlement',
            'HUD1009AggregatePaidFromBorrowerSettlement'               => 'HUD 1009 Aggregate Paid From Borrower Settlement',
            'HUD1009AggregatePaidFromSellerSettlement'                 => 'HUD 1009 Aggregate Paid From Seller Settlement',
        ],

        'File Links'    => [
            'backofficefilelink'                => 'Backoffice File Link',
            'fullappforborrower'                => 'Full App for Borrower',
            'quickappforborrower'               => 'Quick App for Borrower',
            'fullappforbroker'                  => 'Full App for Broker',
            'quickappforbroker'                 => 'Quick App for Broker',
            'uploadportalforborrower'           => 'Upload Portal for Borrower',
            'uploadportalforbroker'             => 'Upload Portal for Broker',
            'qaborrowerinforedacted'            => 'QA Borrower Info Redacted',
            'qaborrowerinforedactedandoffersub' => 'QA Borrower Info Redacted and Offer Sub',
            'qareadonly'                        => 'QA Read Only',
            'qareadonlyandoffersub'             => 'QA Read Only and Offer Sub',
            'qareadonlyanduploadportal'         => 'QA Read Only and Upload Portal',
            'qareadonlyuploadportalandoffersub' => 'QA Read Only Upload Portal and Offer Sub',
            'faborrowerinforedacted'            => 'FA Borrower Info Redacted',
            'faborrowerinforedactedandoffersub' => 'FA Borrower Info Redacted and Offer Sub',
            'fareadonly'                        => 'FA Read Only',
            'fareadonlyandoffersub'             => 'FA Read Only and Offer Sub',
            'fareadonlyanduploadportal'         => 'FA Read Only and Upload Portal',
            'fareadonlyuploadportalandoffersub' => 'FA Read Only Upload Portal and Offer Sub',
        ],
        'Loan Info V2'  => [
            'totalPropertiesLoanAmount' => 'Total Loan Amount V2',
            'totalPropertiesPITIA'      => 'PITIA - Loan Info V2',
            'rateLockExpirationDate'    => 'Lock Expiration Date',
        ],
      /*  'Custom Fields' => [
            'customFields' => 'All Custom Fields',
        ],*/
    ];
}
