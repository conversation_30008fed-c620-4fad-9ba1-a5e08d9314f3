<?php

namespace models\constants\gl;

use models\types\strongType;

class glPCID extends strongType
{
    const PCID_RHINE_LEGACY_CAPITAL = 4846;
    const PCID_TESTING_KRISH = 3476;
    const PCID_MORTGAGE_ASSISTANCE = 1456;
    const PCID_DEV_DAVE = 3363;
    const PCID_PROD_DAVE = 3580;
    const PCID_2 = 2;
    const PCID_3187 = 3187;
    const PCID_EVERGREEN_EQUITY_PARTNERS = 4451;
    const PCID_4423 = 4423;
    const PCID_3394 = 3394;

    //https://app.shortcut.com/lendingwise/story/37999/adjustment-to-notification-for-company-aspire-processing
    const PCID_1495 = 1495;

    //Loan Term Sheet mod for Global Integrity Finance https://app.shortcut.com/lendingwise/story/38538/edit-to-document-loan-term-sheet-for-global-integrity-finance
    const PCID_3198 = 3198;


    //https://app.shortcut.com/lendingwise/story/38662/customization-for-company-lionscove-doc-upload-portals
    //https://www.pivotaltracker.com/story/show/184193551A
    const PCID_4101 = 4101;

    const PCID_820 = 820;

    const PCID_HOUSE_MAX = 3138;

    const PCID_CRE_FINANCE = 4894;
    const PCID_SUMMIT_SOFTWARE_SYSTEMS = 1653;
    const PCID_CRB = 4814; //4814 CRB

    const PCID_CRB_FROM_EMAIL = '<EMAIL>';
    const PCID_CRB_SENDGRID_API = PCID_CRB_SENDGRID_API_KEY;
    const PCID_ALTISOURCE = 4637;

    const PCID_4637 = 4637;  //https://www.pivotaltracker.com/n/projects/2624420/stories/*********

    const PCID_2057 = 2057;
    const PCID_BFXV = 4929;
    const PCID_PROD_CV3 = 4975;
    const PCID_DOMINION = 5080;
    const PCID_DEV_CV3 = 3392;
    const PCID_BROKER_WISE = 3310;
    const PCID_LENDINGWISE = 1652;
    const PCID_G1CommMort = 4495;
    const PCID_CONSISTENT_CAPTIAL = 4997;
    const PCID_RBI_PRIVATE_LENDING = 4304;
    const PCID_BD_CAPTIAL = 4326;
    const PCID_CMG_FINANCIAL = 5202;
    const STAGING_MIGUEL_FEAL_PCID = 3387;
    const PCID_INVESTMENT_PROPERTY_LLC = 4496;
    const PCID_STAGE_MIGUEL = 3387;
    const PCID_HERITAGE_BANK = 5074;
    const PCID_LOAN_BUD = 4849;
    const PCID_RELENDING = 5132;
    const PCID_SPREO_CAPITAL = 5311;
    const PCID_SKM_CAPITAL = 5350;
    const PCID_BUILDERS_CAPITAL = 5317;
    const PCID_11_CAPITAL_FINANCE = 3934;
    const PCID_LULU = 5391;
    const PCID_LCD_COMMERCIAL = 5422;
    const PCID_PIEDPIPER = 4641;
    const PCID_LENDING_DECK = 5443;
    const PCID_PROD_OSKAR = 5474;
    const PCID_PROD_OSKAR_ELITE = 5484;
    const PCID_CENTRIFUND_LLC = 5384;
    const PCID_LENDINGDECK = 5443;

    const PCID_PROD_KIAVI = 4863;
    const PCID_TEST_DANIEL = 3400;

    const PCID_FYNF_LLC = 5027;
}
