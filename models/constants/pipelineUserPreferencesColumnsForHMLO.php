<?php

namespace models\constants;

/**
 *
 */
class pipelineUserPreferencesColumnsForHMLO
{
    public static array $columns = [
        'Dates'               => [
            'recordDate'               => 'Date entered',
            'createdDateWithTimestamp' => 'Created Date With Timestamp',
            'lastUpdatedDate'          => 'Last action',
            'receivedDate'             => 'Received Date',
            'closingDate'              => 'Actual Closing Date',
            'targetClosingDate'        => 'Target Closing Date',
            'desiredClosingDate'       => 'Desired Closing Date',
            'borrowerCallBack'         => 'Client Call Back Date',
            'trialPaymentDate1'        => 'First Payment Due',
            'proInsPolicyExpDate'      => 'Policy Expiration Date',
            'hearingDate'              => 'Hearing Date',
            'dateObtained'             => 'Appraisal 1 Received Date',
            'appraisal1OrderDate'      => 'Appraisal 1 Order Date',
            'salesDate'                => 'Sale date',
            'disclosureSentDate'       => 'Disclosure Sent Date',
            'titleOrderDate'           => 'Title Order Date',
            'fundingDate'              => 'Funding Date',
            'maturityDate'             => 'Maturity Date',
            'targetSubmissionDate'     => 'Target Submission Dated',
        ],
        'Borrower Info'       => [
            'phoneNumber'                  => 'Phone',
            'cellNumber'                   => 'Cell #',
            'borrowerEmail'                => 'Email',
            'totalNetIncome'               => 'Total Net Income',
            'totalExpenses'                => 'Total Net Expenses',
            'disposableIncome'             => 'Disposable Income',
            'currentDTI'                   => 'Current DTI',
            'totalAssets'                  => 'Total Assets',
            'networthOfBusinessOwned'      => 'Net Worth',
            'borCreditScoreRange'          => 'Credit Score Range',
            'borExperianScore'             => 'Experian',
            'borEquifaxScore'              => 'Equifax',
            'borTransunionScore'           => 'Transunion',
            'borNoOfREPropertiesCompleted' => '# of properties completed',
            'borRehabPropCompleted'        => '# of New Construction done',
            'borNoOfOwnProp'               => '# of investment properties',
            'isBorUSCitizen'               => 'US Citizen',
            'entity'                       => 'Entity',
            'city'                         => 'City',
            'state'                        => 'State',
            'assetTotalCashBankAcc'        => 'Total Cash In Bank Accounts',
            'coBorrowerName'               => 'Co-Borrower Full Name',
            'authorizationStatus'          => 'Authorization Status',
            'trackRecord'                  => 'Track Record',
        ],
        'Entity Info'         => [
            'entityName'        => 'Entity Name',
            'entityType'        => 'Entity Type',
            'stateOfFormation'  => 'State Of Formation',
            'entityCity'        => 'Entity City',
            'entityState'       => 'Entity State',
            'propertyOwnership' => 'Property Ownership',
            'ofEmployees'       => '# Of Employees',
        ],
        'Business Financials' => [
            'avgMonthlyCreditCardSales' => 'Avg Monthly Credit Card Sales',
            'avgTotalMonthlySales'      => 'Avg Total Monthly Sales',
            'annualGrossProfit'         => 'Annual Gross Profit',
            'netBusinessIncome'         => 'Net Business Income',
        ],
        'Loan Info'           => [
            'seviceTypes'                 => 'Loan Programs',
            'LMRInternalLoanProgram'      => 'Internal Loan Programs',
            'HMLOLender'                  => 'Originator',
            'HMLOLoanType'                => 'Transaction Type',
            'HMLOTotalLoanAmt'            => 'Loan Amount',
            'HMLOLoanTerm'                => 'Loan Term',
            'purchasePrice'               => 'Purchase Price',
            'HMLOTotalPayment'            => 'Monthly Payment',
            'netMonthlyPayment'           => 'PITIA - Loan Info',
            'lien1Rate'                   => 'Interest Rate',
            'totalProjectCost'            => 'Total Project cost',
            'totalRehabCost'              => 'Total Rehab Cost',
            'rehabCostFinanced'           => 'Rehab Cost Financed',
            'LTC'                         => 'Loan-to-Cost',
            'ARV'                         => 'ARV %',
            'acquisitionLTV'              => 'Acquisition LTV',
            'marketLTV'                   => 'Market LTV',
            'perRehabCostFinanced'        => '% of Rehab Cost Financed',
            'exitStrategy'                => 'Exit Strategy',
            'isHouseProperty'             => 'Borrower Occupancy',
            'propertyNeedRehab'           => 'Rehab Required',
            'propertyConstructionLevel'   => 'Property Construction Level',
            'lienPosition'                => 'Lien Position',
            'costOfCapital'               => 'Cost of Capital',
            'yieldSpread'                 => 'Yield Spread',
            'projectName'                 => 'Project Name',
            'assessedValue'               => 'After Repair Value',
            'availableBudget'             => 'Current Escrow Balance',
            'currentLoanBalance'          => 'Current Loan Balance',
            'initialLoanAmount'           => 'Initial Loan Amount',
            'brokerProcessingFee'         => 'Broker Processing Fee',
            'referralPoints'              => 'Referral Points',
            'rateLockPeriod'              => 'Rate Lock Extension Period',
            'rateLockDate'                => 'Rate Lock Date',
            'rateLockExpirationDate'      => 'Lock Expiration Date',
            'rateLockExtension'           => 'Rate Lock Extension',
            'rateLockNotes'               => 'Rate Lock Notes',
            'PSAClosingDate'              => 'PSA Closing Date',
            'buildingAnalysisOutstanding' => 'Building Analysis Outstanding',
            'buildingAnalysisNeed'        => 'Building Analysis Need',
            'buildingAnalysisDueDate'     => 'Building Analysis Due Date',
            'VOMStatus'                   => 'VOM Status',
            'payoffStatus'                => 'Payoff Status',
            'LOISentDate'                 => 'LOI Sent Date',
        ],
        'Property Info'       => [
            'propertyCity'            => 'City',
            'propertyState'           => 'State',
            'propertyCounty'          => 'County',
            'propertySqFt'            => 'Sq Ft',
            'propertyCondition'       => 'Condition',
            'acres'                   => 'Lot Size',
            'propertyType'            => 'Property Type',
            'presentOccupancyStatus'  => 'Present Occupancy Status',
            'bedrooms'                => '# of bedrooms',
            'bathrooms'               => '# of Baths',
            'appraiser1Value'         => 'Appraisal 1 Value',
            'AVM1'                    => 'AVM 1 Value',
            'BPO1'                    => 'Realtor 1 Value',
            // 'repairValue'                      => 'After Repair Value',    //Commented on 30-Aug-2018
            'ASISValue'               => 'As-is Value',
            'propertyURLLink'         => 'URL link to property',
            'taxes1'                  => 'Annual Property Tax',
            'requiredInsurance'       => 'Required Insurance',
            'propertyFloodZone'       => 'Flood Zone',
            'noOfPropertiesAcquiring' => 'Cross Properties',
        ],
        'Appraisal info'      => [
            'propertyAppraisalAsIsValue'                             => 'As Is Value',
            'propertyAppraisalRehabbedValue'                         => 'Rehabbed Value',
            'propertyAppraisalMonthlyRent'                           => 'Monthly Rent',
            'propertyAppraisalJobTypes'                              => 'Job Types',
            'propertyAppraisalDateObtained'                          => 'Date Obtained',
            'propertyAppraisalRequestedReturnDate'                   => 'Requested Return Date',
            'propertyAppraisalIsRushOrder'                           => 'Rush Order',
            'propertyAppraisalOrderDate'                             => 'Order Date',
            'propertyAppraisalComments'                              => 'Comments',
            'propertyAppraisalEffectiveDate'                         => 'Appraisal Effective Date',
            'primaryAppraisalEcoaDeliveryDate'                       => 'Primary Appraisal ECOA Delivery Date',
            'propertyAppraisalInspectionDate'                        => 'Appraisal Inspection Date',
            'propertyAppraisalExpectedDeliveryDate'                  => 'Expected Delivery Date',
            'propertyAppraisalExpectedDeliveryDelay'                 => 'Expected Delivery Delay #',
            'propertyAppraisalSupplementalProductFormType1'          => 'Supplemental Product Form Type 1',
            'propertyAppraisalSupplementalProductFormType2'          => 'Supplemental Product Form Type 2',
            'propertyAppraisalSupplementalProductFormType3'          => 'Supplemental Product Form Type 3',
            'propertyAppraisalSupplementalProductEffectiveDate1'     => 'Supplemental Product Effective Date 1',
            'propertyAppraisalSupplementalProductEffectiveDate2'     => 'Supplemental Product Effective Date 2',
            'propertyAppraisalSupplementalProductEffectiveDate3'     => 'Supplemental Product Effective Date 3',
            'propertyAppraisalSupplementalProductEcoaADeliveryDate1' => 'Supplemental Product ECOA Delivery Date 1',
            'propertyAppraisalSupplementalProductEcoaADeliveryDate2' => 'Supplemental Product ECOA Delivery Date 2',
            'propertyAppraisalSupplementalProductEcoaADeliveryDate3' => 'Supplemental Product ECOA Delivery Date 3',
            'propertyAppraisalStatus'                                => 'Appraisal Status',
        ],
        'Admin/Staff Details' => [
            'primaryStatus'       => 'Status',
            'fileVelocity'        => 'Days in primary file status',
            'subStatus'           => 'Sub-Status',
            'loanNumber'          => 'Loan #',
            'MERSID'              => 'MERS ID',
            'priorityLevel'       => 'Priority Level',
            'branch'              => 'Branch',
            'agent'               => 'Agent',
            'brokerPartnerType'   => 'Broker/Partner Type',
            'loanofficer'         => 'Loan Officer',
            'assignedEmployee'    => 'Assigned Employee',
            'leadSource'          => 'Lead Source',
            'workflowEvents'      => 'WorkFlow(s)',
            'referringParty'      => 'Broker referring party',
            'lender'              => 'Lender',
            'servicer'            => 'Servicer',
            'trustee'             => 'Trustee',
            'investor'            => 'Investor',
            'lenderInternalNotes' => 'Lender Internal Notes',
            'eCoaWaiverStatus'    => 'ECOA Waiver Status',
            'welcomeCallStatus'   => 'Welcome Call'

        ],
        'Miscellaneous'       => [
            'insuranceCompName' => 'Insurance Co. Name',
            'titleCompanyInfo'  => 'Title Company Info',
            'typeOfSale'        => 'Type Of Sale',
            'brokerPointsRate'  => 'Broker Points %',
            'brokerPointsValue' => 'Broker Points Value',
            'originationPointsRate'  => 'Origination Points %',
            'originationPointsValue' => 'Origination Points Value',
            'servicingStatus'   => 'Servicing Status',
            'payOffDate'        => 'Pay Off Date',
            'loanSaleDate'      => 'Date Of Loan Sale Agreement',
            'fileId'            => 'File ID',
        ],
        'Credit Decision'     => [
            'clearToCloseBy' => 'Clear to Close By',
        ],
        'Adverse Action/NOI'  => [
            'HMDAActionTaken' => 'HMDA Action Taken',
        ],
        'HUD'                 => [
            'warehouseInvestor' => 'Warehouse Investor',
            'wireAmountSent'    => 'Wire Amount Sent',
        ],
        'Loan Info V2'        => [
            'totalPropertiesLoanAmount' => 'Total Loan Amount',
            'totalPropertiesPITIA'      => 'PITIA - Loan Info V2',
        ],
    ];
}
