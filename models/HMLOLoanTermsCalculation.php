<?php

namespace models;

use models\composite\calEffectiveGrossIncome;
use models\composite\debtServiceRatioPITIA;
use models\composite\proposalFormula;
use models\constants\accrualTypes;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glPCID;
use models\constants\typeOfHMLOLoanRequesting;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\loanPropertySummary;
use models\Controllers\LMRequest\refinanceMortgage;
use models\HMLOLoanTermsCalculation\LoanVariables;
use models\loan_math\per_diem;
use models\myFileInfo\budgetAndDrawsInfo;
use models\pops\exportClientFiles\_TotalPITIA;
use models\servicing\LoanTerms;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;
use models\types\strongType;
use models\HMLOLoanTermsCalculation\LTC2Variables;

class HMLOLoanTermsCalculation extends strongType
{
    public static ?float $finalLoanAmt = null;
    public static ?float $ARV = null;
    public static ?float $costOfCapital = null;
    public static ?float $yieldSpread = null;
    public static ?int $noOfMonthsPrepaid = null;
    public static ?float $maxArvPer = null;
    public static ?float $maxLTCPer = null;
    public static ?float $LOCTotalLoanAmt = null;
    public static ?int $origination_based_on_total_loan_amt = null;
    public static ?int $broker_based_on_total_loan_amt = null;
    public static ?float $tempTotalLoanAmount = null;
    public static ?float $initialLoanAmount = null;
    public static ?float $LTC = null;
    public static ?float $marketLTV = null;
    public static ?float $paydownamount = null;
    public static ?int $intAmt = null;
    public static ?float $taxImpoundsFee = null;
    public static ?float $insImpoundsFee = null;
    public static ?float $extensionOptionsAmt = null;
    public static ?float $rehabCostPercentageFinanced = null;
    public static ?float $downPaymentPercentage = null;
    public static ?float $CORefiLTVPercentage = null;
    public static ?float $payOffAmount = null;
    public static ?float $NewLoanToCost = null;
    public static ?bool $URLPOSTING = null;
    public static ?float $insImpoundsMonth = null;
    public static ?float $maxAmtToPutDown = null;
    public static ?float $totalLoanAmount = null;
    public static ?string $typeOfHMLOLoanRequesting = null;
    public static ?float $rehabCostFinanced = null;
    public static ?float $CORTotalLoanAmt = null;
    public static ?float $prepaidInterestReserveForCal = null;
    public static ?float $totalDrawsFunded = null;
    public static ?float $currentLoanBalance = null;
    public static ?float $annualPremium = null;
    public static ?float $originationPointsValue = null;
    public static ?float $otherDownPayment = null;
    public static ?float $earnestDeposit = null;
    public static ?string $isLoanPaymentAmt = null; // ILA, TLA, etc
    public static ?string $autoCalcTLAARV = null;
    public static ?float $downPaymentPercentageNew = null;
    public static ?float $prepaidInterestReserve = null;
    public static ?float $acquisitionPriceFinanced = null;
    public static ?string $loanTerm = null;
    public static ?float $extensionOptionPercentage = null;
    public static ?float $totalMonthlyPayment = null;
    public static ?float $paymentReservesAmt = null;
    public static ?float $requiredConstructionAmt = null;
    public static ?int $pos = null;
    public static ?int $loanTerms = null;
    public static ?string $haveInterestreserve = null;
    public static ?float $contingencyReserveAmt = null;
    public static ?float $percentageTotalLoanAmount = null;
    public static ?string $paymentduedate = null;
    public static ?string $payOffDate = null;
    public static ?float $amountdue = null;
    public static ?float $grossProfit = null;
    public static ?float $grossProfitMargin = null;
    public static ?float $percentageTotalLoan = null;
    public static ?array $fileHMLOPropertyInfo = null;
    public static ?array $fileHMLONewLoanInfo = null;
    public static ?string $acceptedPurchaseDispOpt = null;
    public static ?float $lien1Payment = null;
    public static ?array $dailyEstPerDiemArray = null;
    public static ?float $perClosingCostFinanced = null;
    public static ?string $commercialFieldsTDDispOpt = null;
    public static ?array $acquisitionPriceFinancedArr = null;
    public static ?string $borBorrowedDownPaymentDispOpt = null;
    public static ?string $doesPropertyNeedRehabDispDiv = null;
    public static ?string $contigencyCls = null;
    public static ?string $additionalPropertyRestrictionsDispOpt = null;
    public static ?string $isTaxesInsEscrowedDispOpt = null;
    public static ?string $cashOutFields = null;
    public static ?string $maxLTCPerDisp = null;
    public static ?string $maxArvPerDisp = null;
    public static ?string $haveCurrentLoanBalDispOpt = null;
    public static ?string $doYouHaveInvoiceToFactorDispOpt = null;
    public static ?string $groundUpDispTDDiv = null;
    public static ?string $cashOutRefinanceDisp = null;
    public static ?string $commercialFieldsTDNDateCDispOpt = null;
    public static ?string $exitStrategyExplainDispOpt = null;
    public static ?string $doesPropertyNeedRehabFootageDispTDDiv = null;
    public static ?string $doesPropertyNeedRehabNoofFootageDispTDDiv = null;
    public static ?string $rentalIncomePerMonthFieldDispOpt = null;
    public static ?string $autoCalcRead = null;
    public static ?float $acquisitionLTV = null;
    public static ?float $perRehabCostFinanced = null;
    public static ?float $availableBudget = null;
    public static ?string $totalMonthlyPaymentTooltipWithValues = null;
    public static ?string $totalMonthlyPaymentTooltip = null;
    public static ?string $isLoTxt = null;
    public static ?float $netMonthlyPayment = null;
    public static ?string $netMonthlyPaymentTooltip = null;
    public static ?string $isBlanketLoanDispOpt = null;
    public static ?string $isEFBlanketLoanDispOpt = null;
    public static ?string $totProjectCostLbl = null;
    public static ?string $commercialFieldsDispOpt = null;
    public static ?string $subjectPropertySectionDispOpt = null;
    public static ?string $refinanceSectionDispOpt = null;
    public static ?string $transactionalFieldsDispOpt = null;
    public static ?string $blanketLoanFieldsDispOpt = null;
    public static ?string $doesPropertyNeedRehabSection = null;
    public static ?string $cashOutRefinanceFields = null;
    public static ?string $downPaymentFieldDispOpt = null;
    public static ?string $rehabConsCls = null;
    public static ?string $purchaseTPCCls = null;
    public static ?string $typeOfSaleTR = null;
    public static ?string $isBlanketLoanDiv = null;
    public static ?string $LOCTotalLoanAmtHideDispOpt = null;
    public static ?string $LOCTotalLoanAmtDispOpt = null;
    public static ?string $feeSectionTotalLoanAmtOpt = null;
    public static ?string $acquisitionLTVTD = null;
    public static ?string $cashOutTotalAmtTD = null;
    public static ?string $marketLTVTD = null;
    public static ?string $lineOfCreditProp = null;
    public static ?string $cashOutDiv = null;
    public static ?string $transCommercialFieldsDispOpt = null;
    public static ?string $propertyNeedRehab = null;
    public static ?string $newConDownPaymentDisp = null;
    public static ?string $autoCalcTLAARVDisp = null;
    public static ?string $autoCalARVToolTip = null;
    public static ?string $perDiemToolTip = null;
    public static ?string $TPCToolTip = null;
    public static ?string $TPCToolTipWithValues = null;
    public static ?string $LTCToolTip = null;
    public static ?string $LTCToolTipWithValues = null;
    public static ?string $LTCInitialLoanAmountToolTip = null;
    public static ?string $LTCInitialLoanAmountToolTipWithValues = null;
    public static ?float $LTCInitialLoanAmount = null;
    public static ?string $LTCMarketValueToolTip = null;
    public static ?string $LTCMarketValueToolTipWithValues = null;
    public static ?float $LTCMarketValue = null;
    public static ?string $LTCInitialLoanAmountCls = null;

    public static ?float $totalCashToClose = null;
    public static ?string $totalCashToCloseTooltip = null;
    public static ?string $totalCashToCloseTooltipWithValues = null;
    public static ?string $marketLTVToolTip = null;
    public static ?string $marketLTVToolTipWithValues = null;
    public static ?string $NewTPCToolTip = null;
    public static ?string $NewTPCToolTipWithValues = null;
    public static ?string $NewLTCToolTip = null;
    public static ?string $NewLTCToolTipWithValues = null;
    public static ?string $tLAToolTip = null;
    public static ?string $tLAToolTipWithValues = null;
    public static ?float $netLenderFundsToBorrower = null;
    public static ?float $totalRequiredReserves = null;
    public static ?string $totalRequiredReservesTooltip = null;
    public static ?string $totalRequiredReservesTooltipWithValues = null;

    public static ?float $totalCashOutAmt = null;
    public static ?float $simpleARV = null;
    public static ?float $InrBasedOnMonthlyPayment = null;
    public static ?float $NewTotalProjectCost = null;
    public static ?array $myFileInfo = null;
    public static ?string $isBorBorrowedDownPayment = null;
    public static ?string $haveBorSquareFootage = null;
    public static ?string $additionalPropertyRestrictions = null;
    public static ?string $exitStrategy = null;
    public static ?string $acceptedPurchase = null;
    public static ?string $haveCurrentLoanBal = null;
    public static ?string $doYouHaveInvoiceToFactor = null;
    public static ?float $closingCostFinanced = null;
    public static ?float $payOffMortgage1 = null;
    public static ?float $payOffMortgage2 = null;
    public static ?float $payOffOutstandingTaxes = null;
    public static ?float $payOffOtherOutstandingAmounts = null;
    public static ?float $cashOutAmt = null;
    public static ?array $filepaydownInfo = null; // unused
    public static ?string $lien1Terms = null;
    public static ?string $purchaseCloseDate = null;
    public static ?float $taxes1 = null;
    public static ?string $isBlanketLoan = null;
    public static ?string $isThisGroundUpConstruction = null;
    public static ?string $interestChargedFromDate = null;
    public static ?string $interestChargedEndDate = null;
    public static ?int $taxImpoundsMonth = null;
    public static ?float $taxImpoundsMonthAmt = null;
    public static ?float $insImpoundsMonthAmt = null;
    public static ?float $paymentReserves = null;
    public static ?float $requiredConstruction = null;
    public static ?float $contingencyReserve = null;
    public static ?string $isTaxesInsEscrowed = null;
    public static ?string $tableRow = null;
    public static ?string $tableRow2 = null;
    public static ?float $rehabCost = null;

    /* @var budgetAndDrawsInfo[] $budgetAndDrawsInfo */
    public static ?array $budgetAndDrawsInfo = null;
    public static ?float $homeValue = null;
    public static ?float $interestOnInterestReserveFeeAmt = null;
    public static ?float $costSpent = null;
    public static ?float $lien1Rate = null;
    public static ?string $accrualType = null;
    public static ?string $isLoanPaymentAmtTxt = null;
    public static ?float $brokerPointsValue = null;
    public static ?float $assessedValue = null;
    public static ?string $tableCell = null;
    public static ?float $costBasis = null;
    public static ?int $LMRId = null;
    public static ?int $lockOriginationValue = null; // may need to be updated in DB to float
    public static ?int $lockBrokerValue = null; // may need to be updated in DB to float
    public static ?array $payOffAmountArray = null;
    public static ?int $nper = null;
    public static ?float $pmt = null;
    public static ?float $pv = null;
    public static ?float $totalFeesAndCostNew = null;
    public static ?float $NewLTCCal = null;
    public static ?string $doesPropertyNeedRehabDispTDDiv = null;
    public static ?array $exitStrategyParentValues = null;
    public static ?float $totalProjectCost = null;
    public static ?int $paycount = null;
    public static ?array $CLBInArray = null;
    public static ?string $commercialFieldsTDNCDispOpt = null;
    public static ?string $diemDays = null;
    public static ?float $PerDiemTotalAmt = null;
    public static ?string $totalDailyInterestCharge = null;
    public static ?float $aggregateDSCR = null;
    public static ?int $interestDays = null;
    public static ?float $totalEstPerDiem = null;
    public static ?array $secondInArray = null;
    public static ?float $totalFeesAndCost = null;
    public static ?float $closingCostNotFinanced = null;
    public static ?array $netLenderFundsinputArray = null;
    public static ?array $taxImpoundsInArray = null;
    public static ?array $insImpoundsInArray = null;
    public static ?array $extensionOptionsAmtInputArray = null;
    public static ?array $sARVIP = null;
    public static ?string $LTCOriginalPurchasePriceToolTip = null;
    public static ?string $LTCOriginalPurchasePriceCls = ' d-none ';
    public static ?float $LTCOriginalPurchasePriceValue = null;
    public static ?string $LTVPercentageDisp = null;
    public static ?string $totalCashOutDisp = null;
    public static ?string $NewTotalProjectCostDisp = null;
    public static ?float $brokerProcessingFee = null;
    public static ?float $TotalAssets = null;
    public static ?float $TotalOwed = null;
    public static ?float $TotalNetWorth = null;
    public static ?float $TotalAutoMobiles = null;
    public static ?float $TotalRehabCost = null;
    public static ?int $ApplicationsSubmitted = null;
    public static ?int $ApplicationsApproved = null;
    public static ?float $TotalAmountApproved = null;
    public static ?float $netOperatingIncome = null;
    public static ?float $currentDTI = null;
    public static ?float $debtServiceRatio = null;
    public static ?float $debtServiceRatioPITIA = null;
    public static ?string $currentLoanBalanceTooltip = null;
    public static ?string $currentLoanBalanceTooltipWithValues = null;
    public static ?string $simpleARVTooltip = 'Simple ARV = Total Loan Amount - ((Origination Points + Broker Points + Prepaid Interest) / ARV)) * 100;<hr>Simple ARV does not factor the lender points, broker points or financed pre-paid Interest reserve';
    public static ?string $simpleARVTooltipWithValues = null;

    public static ?string $fullARVTooltip = "ARV = (Total Loan Amount / After Repair Value) * 100<hr>ARV stands for After repair value. Anything over 70% is typically considered too risky for most lenders";
    public static ?string $fullARVTooltipWithValues = null;

    public static ?string $acquisitionLTVTooltip = 'Acquisition LTV = (Initial Loan Amount + Closing Costs Financed) / Purchase Price * 100<hr>This a percentage of your loan to values based off the purchase price, not the market value.';

    public static ?string $acquisitionLTVTooltipWithValues = null;

    public static ?string $rehabCostFinancedTooltip = '% of Rehab Cost Financed = (Rehab Cost Financed / Rehab Cost) * 100';
    public static ?string $rehabCostFinancedTooltipWithValues = null;

    public static ?string $grossProfitTooltip = 'Gross profit is ARV- Purchase Price- Cost Spent- Renovation Budget';
    public static ?string $grossProfitTooltipWithValues = null;

    public static ?string $grossProfitMarginTooltip = 'Gross profit  margin is Total gross Profit / ARV';
    public static ?string $grossProfitMarginTooltipWithValues = null;

    public static ?string $lessPrePayInterestReserveToolTip = 'This amount is same as Pre-paid Interest Reserve IF Do you want to Finance Pre-paid Interest Reserve? = Yes';
    public static ?string $lessPrePayInterestReserveToolTipWithValues = null;

    public static ?string $totalInterestPaymentReserveRequiredToolTip = 'Total Interest Payment Reserve Required = Interest or Payment Reserves - Less Pre-paid interest Reserve (non negative value)';
    public static ?string $totalInterestPaymentReserveRequiredToolTipWithValues = null;
    public static ?string $totalInterestPaymentReserveRequired = null;

    public static ?string $netLenderFundsToBorrowerToolTip = null;
    public static ?string $netLenderFundsToBorrowerToolTipWithValues = null;
    public static ?string $totalCashOutToolTip = null;
    public static ?string $totalCashOutToolTipWithValues = null;
    public static ?string $hideFieldsFor_SPREO = null;
    public static ?string $contingencyReserveTooltip = null;
    public static ?string $contingencyReserveTooltipWithValues = null;

    public static ?string $closingCostNotFinancedTooltip = null;
    public static ?string $closingCostNotFinancedTooltipWithValues = null;

    public static ?string $downPaymentHtmlTooltip = null;
    public static ?string $downPaymentHtmlTooltipWithValues = null;
    public static ?int $LTVBasedOn = null;
    public static ?int $setManuallyTotalRequiredReserves = null;
    public static ?int $setManuallyTotalCashToClose = null;

    public static function InitForLMRId(int $LMRId): void
    {
        self::Clear();
        LMRequest::setLMRId($LMRId);

        self::Init(
            false,
            [],
            floatval(LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->insImpoundsMonth), //
            LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->taxImpoundsFee, //
            LMRequest::File()->getTblFileHMLOBackGround_by_fileID()->isBorBorrowedDownPayment, //
            LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->propertyNeedRehab,
            LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->haveBorSquareFootage, //
            LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->additionalPropertyRestrictions, //
            LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->exitStrategy,
            LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->acceptedPurchase,
            LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->haveCurrentLoanBal,
            LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->haveInvoiceToFactor,
            LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->maxAmtToPutDown,
            LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->closingCostFinanced,
            LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->payOffMortgage1,
            LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->payOffMortgage2,
            LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->payOffOutstandingTaxes,
            LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->payOffOtherOutstandingAmounts,
            LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->cashOutAmt,
            null, //
            LMRequest::File()->lien1Terms,
            LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->purchaseCloseDate, //
            Strings::replaceCommaValues(LMRequest::File()->getTblIncomeInfo_by_LMRId()->taxes1),
            LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->isBlanketLoan,
            LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->isThisGroundUpConstruction, //
            LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->interestChargedFromDate,
            LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->interestChargedEndDate,
            Strings::replaceCommaValues(LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->taxImpoundsMonth), // need to review this form field, lots of junk
            LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->taxImpoundsMonthAmt,
            LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->insImpoundsMonthAmt,
            LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->insImpoundsFee,
            LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->paymentReserves,
            LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->requiredConstruction,
            LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->contingencyReserve,
            null,
            null,
            $LMRId,
            null,
            null,
            true
        );
    }

    private static function setVarsFromLMRequest(): LoanVariables
    {
        $loanVariables = new LoanVariables();

        if (LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()) {
            $loanVariables->fileHMLOPropertyInfo = LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->toArray();
        }

        if (LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()) {
            $loanVariables->fileHMLONewLoanInfo = LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->toArray();
        }

        if (LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()) {
            $loanVariables->percentageTotalLoan = LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->percentageTotalLoan;
        }

        $loanVariables->costBasis = Strings::replaceCommaValues(LMRequest::myFileInfo()->listingRealtorInfo()->costBasis);
        $loanVariables->rehabCost = LMRequest::myFileInfo()->fileHMLOInfo()->rehabCost;
        $loanVariables->assessedValue = LMRequest::myFileInfo()->listingRealtorInfo()->assessedValue;
        $loanVariables->costSpent = Strings::replaceCommaValues(LMRequest::myFileInfo()->listingRealtorInfo()->costSpent);
        $loanVariables->homeValue = Strings::replaceCommaValues(LMRequest::myFileInfo()->LMRInfo()->homeValue);
        $loanVariables->lien1Payment = Strings::replaceCommaValues(LMRequest::myFileInfo()->LMRInfo()->lien1Payment);
        $loanVariables->loanTerm = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->loanTerm;
        $loanVariables->isLoanPaymentAmt = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->isLoanPaymentAmt;
        $loanVariables->maxArvPer = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->maxArvPer;
        $loanVariables->maxLTCPer = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->maxLTCPer;
        $loanVariables->aggregateDSCR = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->aggregateDSCR;
        $loanVariables->autoCalcTLAARV = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->autoCalcTLAARV;
        // $loanVariables->autoCalcTLAARV = 'No';//showField('autoCalcTLAARV', 'fileHMLONewLoanInfo');

        $loanVariables->extensionOptionPercentage = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->extensionOptionPercentage;
        $loanVariables->typeOfHMLOLoanRequesting = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typeOfHMLOLoanRequesting;
        $loanVariables->insImpoundsMonth = Strings::replaceCommaValues(LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->insImpoundsMonth);
        $loanVariables->LOCTotalLoanAmt = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->LOCTotalLoanAmt;
        $loanVariables->costOfCapital = Strings::replaceCommaValues(LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->costOfCapital);
        $loanVariables->yieldSpread = Strings::replaceCommaValues(LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->yieldSpread);
        $loanVariables->annualPremium = Strings::replaceCommaValues(LMRequest::myFileInfo()->fileHMLOPropertyInfo()->annualPremium);
        $loanVariables->origination_based_on_total_loan_amt = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->origination_based_on_total_loan_amt;
        $loanVariables->broker_based_on_total_loan_amt = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->broker_based_on_total_loan_amt;
        $loanVariables->lien1Rate = Strings::replaceCommaValues(LMRequest::myFileInfo()->LMRData()->lien1Rate);
        $loanVariables->accrualType = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->accrualType;
        if (!$loanVariables->accrualType) {
            $loanVariables->accrualType = accrualTypes::ACCRUAL_TYPE_30_360;
        }
        $loanVariables->perDiemAccrualType = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->perDiemAccrualType ?? null;

        $loanVariables->lockOriginationValue = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->lockOriginationValue;
        $loanVariables->lockBrokerValue = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->lockBrokerValue;

        if (LMRequest::myFileInfo()->getFileHMLONewLoanInfo()) {
            $loanVariables->initialLoanAmount = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->initialLoanAmount;
            $loanVariables->CORTotalLoanAmt = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->CORTotalLoanAmt;
            $loanVariables->CORefiLTVPercentage = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->CORefiLTVPercentage;
            $loanVariables->LTVBasedOn = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->LTVBasedOn;
            $loanVariables->rehabCostPercentageFinanced = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->rehabCostPercentageFinanced;
            $loanVariables->currentLoanBalance = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->currentLoanBalance;
            $loanVariables->acquisitionPriceFinanced = Strings::replaceCommaValues(LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->initialLoanAmount);
            $loanVariables->finalLoanAmt = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->finalLoanAmt;
            $loanVariables->taxImpoundsFee = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->taxImpoundsFee;
            $loanVariables->prepaidInterestReserve = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->prepaidInterestReserve;
            $loanVariables->prepaidInterestReserveForCal = $loanVariables->prepaidInterestReserve;
            $loanVariables->haveInterestreserve = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->haveInterestreserve;

            if ($loanVariables->haveInterestreserve == 'No' || $loanVariables->haveInterestreserve == '') {
                $loanVariables->prepaidInterestReserveForCal = 0;
            }
            $loanVariables->noOfMonthsPrepaid = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->noOfMonthsPrepaid;
            $loanVariables->otherDownPayment = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->otherDownPayment;
            $loanVariables->earnestDeposit = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->earnestDeposit;
            $loanVariables->isLoanPaymentAmt = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->isLoanPaymentAmt;
            $loanVariables->maxArvPer = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->maxArvPer;
            $loanVariables->maxLTCPer = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->maxLTCPer;
            $loanVariables->autoCalcTLAARV = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->autoCalcTLAARV;
            //$loanVariables->autoCalcTLAARV = 'No';//$fileHMLONewLoanInfo['autoCalcTLAARV'];
            $loanVariables->extensionOptionPercentage = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->extensionOptionPercentage;
            $loanVariables->insImpoundsMonth = Strings::replaceCommaValues(LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->insImpoundsMonth);
            $loanVariables->downPaymentPercentage = Strings::replaceCommaValues(LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->downPaymentPercentage);
            $loanVariables->LOCTotalLoanAmt = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->LOCTotalLoanAmt;
            $loanVariables->costOfCapital = Strings::replaceCommaValues(LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->costOfCapital);
            $loanVariables->yieldSpread = Strings::replaceCommaValues(LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->yieldSpread);
            $loanVariables->totalLoanAmount = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->totalLoanAmount;

            $loanVariables->origination_based_on_total_loan_amt = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->origination_based_on_total_loan_amt;
            $loanVariables->broker_based_on_total_loan_amt = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->broker_based_on_total_loan_amt;
            $loanVariables->isTaxesInsEscrowed = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->isTaxesInsEscrowed;
            $loanVariables->originationPointsValue = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->originationPointsValue;
            $loanVariables->brokerPointsValue = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->brokerPointsValue;
            $loanVariables->spcf_hoafees = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->spcf_hoafees;
            $loanVariables->brokerProcessingFee = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->brokerProcessingFee;
            $loanVariables->originationPointsRate = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->originationPointsRate;
            $loanVariables->brokerPointsRate = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->brokerPointsRate;
        }

        $loanVariables->budgetAndDrawsInfo = LMRequest::myFileInfo()->budgetAndDrawsInfo();

        if (LMRequest::myFileInfo()->fileHMLOPropertyInfo()) {
            $loanVariables->loanTerm = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->loanTerm;
            $loanVariables->typeOfHMLOLoanRequesting = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typeOfHMLOLoanRequesting;
            $loanVariables->annualPremium = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->annualPremium;
        }

        if (loanPropertySummary::showHidePropertyRehabCv3(LMRequest::$PCID, LMRequest::File()->getTblLMRClientType_by_LMRID()->ClientType)) {
            $loanVariables->propertyNeedRehab = 'Yes';
        }

        $loanVariables->payDownInfo = LMRequest::myFileInfo()->paydownInfo();

        $loanVariables->totalMonthlyPayment = LMRequest::myFileInfo()->LMRData()->lien1Payment;

        if (LMRequest::myFileInfo()->netOperatingIncome()) {
            $loanVariables->netOperatingIncome = LMRequest::myFileInfo()->netOperatingIncome()->netOperatingIncome;
        }

        $loanVariables->nonInclusivePerDiem = LMRequest::myFileInfo()->PCInfo(LMRequest::$PCID)->nonInclusivePerDiem;

        $loanVariables->paymentDue = trim(LMRequest::myFileInfo()->fileHMLOPropertyInfo()->paymentDue);
        $loanVariables->payOffDate = trim(LMRequest::myFileInfo()->fileHMLOPropertyInfo()->payOffDate);

        $loanVariables->amountPastDueOrOwed = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->amountPastDueOrOwed ?? 0;

        if (LMRequest::myFileInfo()->assetInfo()) {
            self::$TotalAssets = Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetCheckingAccounts)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetSavingMoneyMarket)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetStocks)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetIRAAccounts)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetESPOAccounts)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetHome)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetORE)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetSR)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetCars)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetLifeInsurance)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetOther)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetAccount)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetNonMarketableSecurities)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetTotalCashBankAcc)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetTotalRetirementValue)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetAvailabilityLinesCredit)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetCash)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->networthOfBusinessOwned)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->otherAssets)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->automobilesOwned3x);

            self::$TotalOwed = Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetStocksOwed)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetHomeOwed)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetOREOwed)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetSROwed)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetCarsOwed)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->otherAmtOwed)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->automobilesOwned3x1)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetAccountOwd)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetIRAAccountsOwed)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetSecNotesOwd)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetUnsecNotesOwd)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetAcctPayableOwd)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetMarginOwd)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetNonMarketableSecuritiesOwd)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetESPOAccountsOwed)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetLifeInsuranceOwed)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetAvailabilityLinesCreditOwed)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->notesPayableToBanksOthersOwed)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->installmentAccountOwed)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->revolvingDebtOwed)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->unpaidPayableTaxesOwed)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->otherLiabilitiesOwed);

            self::$TotalAutoMobiles = Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->automobilesOwned3x1)
                + Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetCars)
                - Strings::replaceCommaValues(LMRequest::myFileInfo()->assetInfo()->assetCarsOwed);
        }

        $repairs = LMRequest::File()->getTblFileHMLOListOfRepairs_by_fileID();
        if ($repairs) {
            self::$TotalRehabCost = (float)($repairs->architectFees
                + $repairs->permitsFees
                + $repairs->demolitionTrashDumpsters
                + $repairs->exteriorRepairs
                + $repairs->termiteInspectionTreatment
                + $repairs->foundationStructuralReport
                + $repairs->roofing
                + $repairs->windows
                + $repairs->doors
                + $repairs->siding
                + $repairs->carpentry
                + $repairs->deckPorch
                + $repairs->drivewayWalkwayPatio
                + $repairs->landscaping
                + $repairs->exteriorRepairsOther
                + $repairs->HVACRough
                + $repairs->HVACFinish
                + $repairs->plumbingRough
                + $repairs->plumbingFixtures
                + $repairs->plumbingFinish
                + $repairs->electricalRough
                + $repairs->electricalFixtures
                + $repairs->electricalFinish
                + $repairs->sheetRock
                + $repairs->interiorRepairsDoors
                + $repairs->interiorRepairsCarpentry
                + $repairs->interiorRepairsOther1
                + $repairs->interiorRepairsOther2
                + $repairs->interiorRepairsOther3
                + $repairs->kitchenCabinets
                + $repairs->kitchenCountertops
                + $repairs->kitchenAppliances
                + $repairs->bath1
                + $repairs->bath2
                + $repairs->bath3
                + $repairs->interiorPainting
                + $repairs->exteriorPainting
                + $repairs->flooringCarpetVinyl
                + $repairs->flooringTile
                + $repairs->flooringHardwood
                + $repairs->finalCleanupOther1
                + $repairs->finalCleanupOther2
                + $repairs->finalCleanupOther3
                + $repairs->finalCleanupOther4);
        }

        if (LMRequest::myFileInfo()->fileMFLoanTermsSummary()) {
            $loanVariables->ApplicationsSubmitted = LMRequest::myFileInfo()->fileMFLoanTermsSummary()->MFLoanTermsInfoCnt;
            $loanVariables->ApplicationsApproved = LMRequest::myFileInfo()->fileMFLoanTermsSummary()->noOfApprovedStatusCnt;
            $loanVariables->TotalAmountApproved = LMRequest::myFileInfo()->fileMFLoanTermsSummary()->totalApprovedLoanAmt;
        }

        $loanVariables->mortgageOwner1 = LMRequest::File()->mortgageOwner1;
        $loanVariables->insurance1 = LMRequest::File()->getTblIncomeInfo_by_LMRId()->insurance1;
        $loanVariables->HOAFees1 = LMRequest::File()->getTblIncomeInfo_by_LMRId()->HOAFees1;
        $loanVariables->mortgageInsurance1 = LMRequest::File()->getTblIncomeInfo_by_LMRId()->mortgageInsurance1;
        $loanVariables->floodInsurance = LMRequest::File()->getTblIncomeInfo_by_LMRId()->floodInsurance1;

        $loanVariables->effectiveGrossIncome = calEffectiveGrossIncome::getReport(null, LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID());


        return $loanVariables;

    }

    public static function Init(
        bool    $URLPOSTING,
        ?array  $myFileInfo,
        ?float  $insImpoundsMonth,
        ?float  $taxImpoundsFee,
        ?string $isBorBorrowedDownPayment,
        ?string $propertyNeedRehab,
        ?string $haveBorSquareFootage,
        ?string $additionalPropertyRestrictions,
        ?string $exitStrategy,
        ?string $acceptedPurchase,
        ?string $haveCurrentLoanBal,
        ?string $doYouHaveInvoiceToFactor,
        ?float  $maxAmtToPutDown,
        ?float  $closingCostFinanced,
        ?float  $payOffMortgage1,
        ?float  $payOffMortgage2,
        ?float  $payOffOutstandingTaxes,
        ?float  $payOffOtherOutstandingAmounts,
        ?float  $cashOutAmt,
        ?array  $filepaydownInfo,
        ?string $lien1Terms,
        ?string $purchaseCloseDate,
        ?float  $taxes1,
        ?string $isBlanketLoan,
        ?string $isThisGroundUpConstruction,
        ?string $interestChargedFromDate,
        ?string $interestChargedEndDate,
        ?int    $taxImpoundsMonth,
        ?float  $taxImpoundsMonthAmt,
        ?float  $insImpoundsMonthAmt,
        ?float  $insImpoundsFee,
        ?float  $paymentReserves,
        ?float  $requiredConstruction,
        ?float  $contingencyReserve,
        ?array  $fileHMLOPropertyInfo,
        ?array  $fileHMLONewLoanInfo,
        ?int    $LMRId,
        ?string $activeTab,
        ?float  $percentageTotalLoan,
        ?bool   $alreadyLoaded = null,
        ?bool   $fast = false
    )
    {

        self::$LMRId = $LMRId;

        $loanVariables = new LoanVariables();

        if ($LMRId) {
            if ($myFileInfo) {
                $loanVariables = LoanVariables::setVarsFromMyInfo($myFileInfo, self::$LMRId);
            } else {
                if (!$alreadyLoaded) {
                    LMRequest::setLMRId($LMRId, $fast);
                }
                $loanVariables = self::setVarsFromLMRequest();
            }
        }

        self::$ApplicationsSubmitted = $loanVariables->ApplicationsSubmitted;
        self::$ApplicationsApproved = $loanVariables->ApplicationsApproved;
        self::$TotalAmountApproved = $loanVariables->TotalAmountApproved;
        self::$TotalRehabCost = $loanVariables->TotalRehabCost;
        self::$TotalAutoMobiles = $loanVariables->TotalAutoMobiles;
        self::$TotalOwed = $loanVariables->TotalOwed;
        self::$TotalAssets = $loanVariables->TotalAssets;
        self::isFileSPREO($loanVariables->PCID);

        $isTaxesInsEscrowed = null;
        $tableRow = null;
        $tableRow2 = null;
        $typeOfHMLOLoanRequesting = null;
        $costOfCapital = null;
        $yieldSpread = null;
        $rehabCostFinanced = null;
        $rehabCost = null;
        $CORTotalLoanAmt = null;
        $prepaidInterestReserveForCal = null;
        $homeValue = null;
        $aggregateDSCR = null;
        $interestOnInterestReserveFeeAmt = null;
        $costSpent = null;
        $totalDrawsFunded = 0;
        $currentLoanBalance = null;
        $lien1Rate = null;
        $accrualType = null;
        $perDiemAccrualType = null;
        $annualPremium = null;
        $originationPointsValue = null;
        $brokerPointsValue = null;
        $assessedValue = null;
        $otherDownPayment = null;
        $earnestDeposit = null;
        $isLoanPaymentAmt = null;
        $tableCell = null;
        $autoCalcTLAARV = null;
        $rehabCostPercentageFinanced = null;
        $costBasis = null;
        $downPaymentPercentage = null;
        $downPaymentPercentageNew = null;
        $totalLoanAmount = null;
        $prepaidInterestReserve = null;
        $acquisitionPriceFinanced = null;
        $loanTerm = null;
        $CORefiLTVPercentage = null;
        $LTVBasedOn = null;
        $lien1Payment = null;
        $finalLoanAmt = null;
        $noOfMonthsPrepaid = null;
        $maxArvPer = null;
        $maxLTCPer = null;
        $LOCTotalLoanAmt = null;
        $origination_based_on_total_loan_amt = null;
        $extensionOptionPercentage = null;
        $broker_based_on_total_loan_amt = null;
        $totalMonthlyPaymentTooltip = null;
        $autoCalcRead = null;
        $fileTab = null;
        $brokerProcessingFee = null;
        $haveInterestreserve = null;
        $grossProfitMargin = null;
        $LTCMarketValue = null;
        $grossProfit = null;

        if ($activeTab == 'QAPP') $fileTab = 'QA';
        if ($activeTab == 'LI') $fileTab = 'FA';
        if (!$activeTab || !$fileTab) $fileTab = 'BO';

        $ACFsecArr = BaseHTML::sectionAccess(['sId' => 'ACF', 'opt' => $fileTab]);    // Section Array
        $LTsecArr = BaseHTML::sectionAccess(['sId' => 'LT', 'opt' => $fileTab]);    // Section Array
        $RCI_sectionArray = BaseHTML::sectionAccess(['sId' => 'RCI', 'opt' => $fileTab]);    // Section Array
        $RLRSecArr = BaseHTML::sectionAccess(['sId' => 'RLR', 'opt' => 'BO']);    // Section Array
        $CTCSecArr = BaseHTML::sectionAccess(['sId' => 'CTC', 'opt' => 'BO']);    // Section Array


        $lockOriginationValue = null;
        $lockBrokerValue = null;

        if ($loanVariables->fileHMLOPropertyInfo) {
            $fileHMLOPropertyInfo = $loanVariables->fileHMLOPropertyInfo;
        }


        if ($loanVariables->fileHMLONewLoanInfo) {
            $fileHMLONewLoanInfo = $loanVariables->fileHMLONewLoanInfo;
        }

        if ($fileHMLOPropertyInfo) {
            $percentageTotalLoan = $loanVariables->percentageTotalLoan;
        }

        if (!$URLPOSTING) {
            $CORTotalLoanAmt = 0.0;
            $CORefiLTVPercentage = 0.0;
            $rehabCostPercentageFinanced = 100;
            $rehabCostFinanced = 0;
            $loanTerm = '';
            $interestOnInterestReserveFeeAmt = 0;
            $prepaidInterestReserve = $prepaidInterestReserveForCal = 0;
            $earnestDeposit = 0;
            $otherDownPayment = 0;
            $autoCalcTLAARV = $isLoanPaymentAmt = 0;
            $tableRow = 'display: block;';
            $tableRow2 = 'display: contents;';
            $tableCell = 'display: block;';
            $extensionOptionPercentage = 0;
            $totalDrawsFunded = $currentLoanBalance = 0;
            $costOfCapital = 0;
            $yieldSpread = 0;
            $annualPremium = 0;
        }

        if (self::$LMRId) {
            $costBasis = $loanVariables->costBasis;
            $rehabCost = $loanVariables->rehabCost;
            $assessedValue = Strings::replaceCommaValues($loanVariables->assessedValue);
            $costSpent = Strings::replaceCommaValues($loanVariables->costSpent);
            $homeValue = Strings::replaceCommaValues($loanVariables->homeValue);
            $lien1Payment = Strings::replaceCommaValues($loanVariables->lien1Payment);
            $loanTerm = $loanVariables->loanTerm;
            $isLoanPaymentAmt = $loanVariables->isLoanPaymentAmt;
            $maxArvPer = $loanVariables->maxArvPer;
            $maxLTCPer = $loanVariables->maxLTCPer;
            $aggregateDSCR = $loanVariables->aggregateDSCR;
            $autoCalcTLAARV = $loanVariables->autoCalcTLAARV; //'No';//showField('autoCalcTLAARV', 'fileHMLONewLoanInfo');

            $extensionOptionPercentage = $loanVariables->extensionOptionPercentage;
            $typeOfHMLOLoanRequesting = $loanVariables->typeOfHMLOLoanRequesting;
            if (!$insImpoundsMonth) {
                $insImpoundsMonth = Strings::replaceCommaValues($loanVariables->insImpoundsMonth);
            }
            $LOCTotalLoanAmt = $loanVariables->LOCTotalLoanAmt;
            $costOfCapital = $loanVariables->costOfCapital;
            $yieldSpread = $loanVariables->yieldSpread;
            $annualPremium = $loanVariables->annualPremium;
            $origination_based_on_total_loan_amt = $loanVariables->origination_based_on_total_loan_amt;
            $broker_based_on_total_loan_amt = $loanVariables->broker_based_on_total_loan_amt;
            $lien1Rate = Strings::replaceCommaValues($loanVariables->lien1Rate);
            $accrualType = $loanVariables->accrualType;
            if (!$accrualType) {
                $accrualType = accrualTypes::ACCRUAL_TYPE_30_360;
            }
            $perDiemAccrualType = $loanVariables->perDiemAccrualType ?? null;

            $lockOriginationValue = $loanVariables->lockOriginationValue;
            $lockBrokerValue = $loanVariables->lockBrokerValue;
        }

        $spcf_hoafees = null;
        if ($loanVariables->fileHMLONewLoanInfo) {
            self::$setManuallyTotalRequiredReserves = $loanVariables->fileHMLONewLoanInfo['setManuallyTotalRequiredReserves'];
            self::$setManuallyTotalCashToClose = $loanVariables->fileHMLONewLoanInfo['setManuallyTotalCashToClose'];
            $CORTotalLoanAmt = $loanVariables->CORTotalLoanAmt;
            $CORefiLTVPercentage = $loanVariables->CORefiLTVPercentage;
            $LTVBasedOn = $loanVariables->LTVBasedOn;
            $rehabCostPercentageFinanced = $loanVariables->rehabCostPercentageFinanced;
            $currentLoanBalance = $loanVariables->currentLoanBalance;
            $acquisitionPriceFinanced = Strings::replaceCommaValues($loanVariables->initialLoanAmount);
            $finalLoanAmt = $loanVariables->finalLoanAmt;
            if (!$taxImpoundsFee) {
                $taxImpoundsFee = $loanVariables->taxImpoundsFee;
            }
            $prepaidInterestReserve = $prepaidInterestReserveForCal = $loanVariables->prepaidInterestReserve;
            HMLOLoanTermsCalculation::$lessPrePayInterestReserveToolTipWithValues = $prepaidInterestReserve;
            $haveInterestreserve = $loanVariables->haveInterestreserve;

            if ($haveInterestreserve == 'No' || $haveInterestreserve == '') {
                $prepaidInterestReserveForCal = 0;
            }
            $noOfMonthsPrepaid = $loanVariables->noOfMonthsPrepaid;
            $otherDownPayment = $loanVariables->otherDownPayment;
            $earnestDeposit = $loanVariables->earnestDeposit;
            $isLoanPaymentAmt = $loanVariables->isLoanPaymentAmt;
            $maxArvPer = $loanVariables->maxArvPer;
            $maxLTCPer = $loanVariables->maxLTCPer;
            $autoCalcTLAARV = $loanVariables->autoCalcTLAARV;//$fileHMLONewLoanInfo['autoCalcTLAARV'];
            $extensionOptionPercentage = $loanVariables->extensionOptionPercentage;
            if (!$insImpoundsMonth) {
                $insImpoundsMonth = Strings::replaceCommaValues($loanVariables->insImpoundsMonth);
            }
            $downPaymentPercentage = Strings::replaceCommaValues($loanVariables->downPaymentPercentage);
            $LOCTotalLoanAmt = $loanVariables->LOCTotalLoanAmt;
            $costOfCapital = Strings::replaceCommaValues($loanVariables->costOfCapital);
            $yieldSpread = Strings::replaceCommaValues($loanVariables->yieldSpread);
            $totalLoanAmount = $loanVariables->totalLoanAmount;

            $origination_based_on_total_loan_amt = $loanVariables->origination_based_on_total_loan_amt;
            $broker_based_on_total_loan_amt = $loanVariables->broker_based_on_total_loan_amt;
            $isTaxesInsEscrowed = $loanVariables->isTaxesInsEscrowed;
            $originationPointsValue = $loanVariables->originationPointsValue;
            $brokerPointsValue = $loanVariables->brokerPointsValue;
            $spcf_hoafees = $loanVariables->spcf_hoafees;
            $brokerProcessingFee = $loanVariables->brokerProcessingFee;
        }

        $budgetAndDrawsInfo = $loanVariables->budgetAndDrawsInfo;

        if (!$isLoanPaymentAmt) {
            $isLoanPaymentAmt = LoanTerms::TLA;
        } // ********* >> Loan Terms changes

        if ($fileHMLOPropertyInfo) {
            $loanTerm = $loanVariables->loanTerm;
            $typeOfHMLOLoanRequesting = $loanVariables->typeOfHMLOLoanRequesting;
            $annualPremium = $loanVariables->annualPremium;
        }

        if ($loanVariables->propertyNeedRehab) {
            $propertyNeedRehab = $loanVariables->propertyNeedRehab;
        }

        $borBorrowedDownPaymentDispOpt = 'display:none;';
        $doesPropertyNeedRehabDispDiv = 'display: none;';
        $contigencyCls = $tableCell;
        $additionalPropertyRestrictionsDispOpt = 'display: none;';
        $isTaxesInsEscrowedDispOpt = 'display: none;';
        $cashOutFields = '';
        $acceptedPurchaseDispOpt = $haveCurrentLoanBalDispOpt = $doYouHaveInvoiceToFactorDispOpt = 'display: none;';
        $doesPropertyNeedRehabDispTDDiv = 'display: none;';
        $groundUpDispTDDiv = 'display: none;';
        $cashOutRefinanceDisp = 'display: none;';
        $commercialFieldsTDNDateCDispOpt = 'display: none;';
        $exitStrategyExplainDispOpt = 'display: none;';
        $doesPropertyNeedRehabFootageDispTDDiv = 'display: none;';
        $doesPropertyNeedRehabNoofFootageDispTDDiv = 'display: none;';
        $rentalIncomePerMonthFieldDispOpt = 'display: none;';
        $maxArvPerDisp = $maxLTCPerDisp = 'display: none;';

        $refinanceOriginalPurchasePrice = refinanceMortgage::$refinanceMortgageInfo[0]->originalPurchasePrice ?? 0;

        if ($autoCalcTLAARV == 'Yes') {
            $maxArvPerDisp = $tableCell;
            $autoCalcRead = 'readonly';
        } elseif ($autoCalcTLAARV == 'LTC' || $autoCalcTLAARV == 'LTC2') {
            $maxLTCPerDisp = $tableCell;
        }

        if ($isBorBorrowedDownPayment == 'Yes') {
            $borBorrowedDownPaymentDispOpt = $tableRow;
        }

        if ($isTaxesInsEscrowed == 'Yes') {
            $isTaxesInsEscrowedDispOpt = $tableRow;
        }

        if ($propertyNeedRehab == 'Yes') {
            $doesPropertyNeedRehabDispDiv = $tableRow;
            $doesPropertyNeedRehabDispTDDiv = $tableCell;
            $doesPropertyNeedRehabFootageDispTDDiv = $tableRow;
        }

        if ($propertyNeedRehab == 'Yes' && $haveBorSquareFootage == 'Yes') {
            $doesPropertyNeedRehabNoofFootageDispTDDiv = $tableRow;
        }

        if ($additionalPropertyRestrictions == 'Yes') {
            $additionalPropertyRestrictionsDispOpt = $tableRow;
        }

        if ($exitStrategy) {
            $exitStrategyExplainDispOpt = $tableRow;
        }

        $exitStrategyParentValues = ['AirBnb', 'Long Term Rental', 'Short Term Rental', 'Fix & Hold'];
        if (in_array($exitStrategy, $exitStrategyParentValues)) {
            $rentalIncomePerMonthFieldDispOpt = $tableRow;
        }

        if ($acceptedPurchase == 'Yes') {
            $acceptedPurchaseDispOpt = $tableRow;
        }

        if ($haveCurrentLoanBal == 'Yes') {
            $haveCurrentLoanBalDispOpt = $tableRow;
        }

        if ($doYouHaveInvoiceToFactor == 'Yes') {
            $doYouHaveInvoiceToFactorDispOpt = $tableRow;
        }

//        if ($haveInterestreserve == 'Yes') {
//            //$haveInterestreserveDivDispOpt = $tableRow;
//        }

        if ($typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::DELAYED_PURCHASE) {
            $cashOutRefinanceDisp = $tableCell;
            $cashOutFields = 'background : #bbe1fe';
        }

//        if ($costOfCapital > 0 || $yieldSpread > 0) {
//            $intArray = [
//                'costOfCapital' => $costOfCapital,
//                'yieldSpread' => $yieldSpread
//            ];
//            //$lien1Rate = calculateInterestRate($intArray);/* story - 27199*/
//        }

        if ($rehabCostFinanced == 0) {
            $rehabCostFinanced = proposalFormula::calculateRehabCostFinancedByPercentage($rehabCost, $rehabCostPercentageFinanced);
        }

        self::initLTC2(
            $typeOfHMLOLoanRequesting,
            $autoCalcTLAARV,
            $rehabCost,
            $homeValue,
            $costBasis,
            LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->escrowFees,
            Strings::replaceCommaValues($loanVariables->downPaymentPercentage),
            $maxLTCPer,
            $closingCostFinanced,
            $prepaidInterestReserve,
            $loanVariables->originationPointsValue,
            $loanVariables->brokerPointsValue,
            LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->haveInterestreserve == 'Yes' ? $prepaidInterestReserve : 0,
            LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->haveInterestreserve == 'No' ? $prepaidInterestReserve : 0,
            $payOffMortgage1,
            $payOffMortgage2,
            $payOffOutstandingTaxes,
            $loanVariables->originationPointsRate,
            $loanVariables->brokerPointsRate,
            $loanVariables->lien1Rate,
            $loanVariables->noOfMonthsPrepaid,
            LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->haveInterestreserve
        );

        if (self::$isAutoCalcTotalLoanAmountBasedOnLTC2) {
            $maxAmtToPutDown = self::$downPaymentForLTC2;
        } else {
            if (Strings::replaceCommaValues($costBasis) > 0) {
                $downPaymentPercentageNew = ($maxAmtToPutDown / $costBasis) * 100;

                if ($downPaymentPercentageNew != $downPaymentPercentage) {
                    $downPaymentPercentage = $downPaymentPercentageNew;
                }
            }
            $maxAmtToPutDown = proposalFormula::calculateDownPaymentByPercentage($downPaymentPercentage, $costBasis);
        }
        $acquisitionPriceFinancedArr = proposalFormula::calculateInitialLoanAmount([
            'costBasis'                => $costBasis,
            'maxAmtToPutDown'          => $maxAmtToPutDown,
            'typeOfHMLOLoanRequesting' => $typeOfHMLOLoanRequesting,
            'totalLoanAmount'          => $totalLoanAmount,
            'rehabCostFinanced'        => $rehabCostFinanced,
            'closingCostFinanced'      => $closingCostFinanced,
            'prepaidInterestReserve'   => $prepaidInterestReserve,
        ]);
        if ($acquisitionPriceFinanced <= 0) {
            $acquisitionPriceFinanced = Strings::replaceCommaValues($acquisitionPriceFinancedArr['acquisitionPriceFinanced'] ?? 0);
        }

        if (self::$isAutoCalcTotalLoanAmountBasedOnLTC2) {
            if (self::isTransactionTypePurchaseCategory($typeOfHMLOLoanRequesting)
                && $isLoanPaymentAmt == LoanTerms::TLA) {
                $acquisitionPriceFinanced = self::$totalLoanAmountForLTC2 - $rehabCost;
            }
            if (self::isTransactionTypePurchaseCategory($typeOfHMLOLoanRequesting)
                && $isLoanPaymentAmt == LoanTerms::ILA) {
                $acquisitionPriceFinanced = self::$totalLoanAmountForLTC2 - $rehabCost;
            }
        } else {
            if ($typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::PURCHASE
                || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::COMMERCIAL_PURCHASE) {

                $downPaymentPercentage = Strings::replaceCommaValues($acquisitionPriceFinancedArr['downPaymentPercentage'] ?? 0); // Arrays::getArrayValue('downPaymentPercentage', $acquisitionPriceFinancedArr);
                $maxAmtToPutDown = Strings::replaceCommaValues($acquisitionPriceFinancedArr['maxAmtToPutDown'] ?? 0); // Arrays::getArrayValue('maxAmtToPutDown', $acquisitionPriceFinancedArr);
            }
        }

        $acquisitionLTV = proposalFormula::calculateAcquisitionLTV_New(
            $costBasis,
            $acquisitionPriceFinanced,
            $closingCostFinanced,
            $prepaidInterestReserveForCal
        );

        $perRehabCostFinanced = proposalFormula::calculatePercentageRehabCostFinanced($rehabCost, $rehabCostFinanced);

        $totalProjectCost = self::$isAutoCalcTotalLoanAmountBasedOnLTC2 ?
            self::$baseAmountForLTC2
            : proposalFormula::calculateTotalProjectCost_new(
                $rehabCost,
                $homeValue,
                $typeOfHMLOLoanRequesting,
                $costBasis,
                $closingCostFinanced,
                $prepaidInterestReserveForCal,
                $interestOnInterestReserveFeeAmt,
                $costSpent,
                LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->addToTotalProjectValue
            );

        if (self::$isAutoCalcTotalLoanAmountBasedOnLTC2) {
            $tempTotalLoanAmount = $totalLoanAmount = self::$totalLoanAmountForLTC2;
        } else {
            if ($typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::PURCHASE &&
                $typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::COMMERCIAL_PURCHASE) {
                $totalLoanAmount = proposalFormula::calculateHMLOFeeCostTotalLoanAmount(
                    $acquisitionPriceFinanced,
                    $rehabCostFinanced,
                    $closingCostFinanced,
                    $payOffMortgage1,
                    $payOffMortgage2,
                    $payOffOutstandingTaxes,
                    $payOffOtherOutstandingAmounts,
                    $cashOutAmt,
                    $typeOfHMLOLoanRequesting,
                    $prepaidInterestReserveForCal
                );
            }

            /* Total Loan Amount */
            $tempTotalLoanAmount = $totalLoanAmount;
            if ($typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::COMMERCIAL_CASH_OUT_REFINANCE
                || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE
                || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::DELAYED_PURCHASE
                || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::REFINANCE
                || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::COMMERCIAL_RATE_TERM_REFINANCE
                || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::RATE_TERM_REFINANCE) {

                $tempTotalLoanAmount = $CORTotalLoanAmt
                    + Strings::replaceCommaValues($rehabCostFinanced)
                    + Strings::replaceCommaValues($prepaidInterestReserveForCal);

                $totalLoanAmount = $tempTotalLoanAmount;

            } elseif ($typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::NEW_CONSTRUCTION_EXISTING_LAND) {

                $tempTotalLoanAmount = $CORTotalLoanAmt
                    + Strings::replaceCommaValues($rehabCostFinanced)
                    + Strings::replaceCommaValues($closingCostFinanced)
                    + Strings::replaceCommaValues($prepaidInterestReserveForCal);
                $totalLoanAmount = $tempTotalLoanAmount;

            } elseif ($typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::LINE_OF_CREDIT) {
                $tempTotalLoanAmount = $CORTotalLoanAmt
                    + Strings::replaceCommaValues($acquisitionPriceFinanced)
                    + Strings::replaceCommaValues($rehabCostFinanced)
                    + Strings::replaceCommaValues($closingCostFinanced)
                    + Strings::replaceCommaValues($prepaidInterestReserveForCal);
                $totalLoanAmount = $tempTotalLoanAmount;
            }
        }

        if (Strings::replaceCommaValues($totalLoanAmount) == 0) {
            $totalLoanAmount = $loanVariables->totalLoanAmount;
        }



        if (self::isTransactionTypeRefinance($typeOfHMLOLoanRequesting)
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::NEW_CONSTRUCTION_EXISTING_LAND
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::DELAYED_PURCHASE
        ) {
            $initialLoanAmount = $CORTotalLoanAmt;  // Acquisition / Purchase Price
        } else {
            $initialLoanAmount = $acquisitionPriceFinanced; //Initial Loan Amount
        }
        if ($typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::NEW_CONSTRUCTION_EXISTING_LAND) {
            $LTCInitialLoanAmountCls = '';
        } else {
            $LTCInitialLoanAmountCls = " d-none ";
        }
        if (BaseHTML::fieldAccess(['fNm' => 'costSpent', 'sArr' => $LTsecArr, 'opt' => 'D', 'tf' => 1])) {
            $LTCMarketValueToolTip = 'LTC - Market value = Initial Loan Amount / (Property Value (AS-Is) + Cost Spent) * 100 ';
            if ($homeValue + $costSpent) {
                $LTCMarketValue = ($initialLoanAmount / ($homeValue + $costSpent)) * 100;
            }
            self::$LTCMarketValueToolTipWithValues = "$initialLoanAmount / ".$homeValue + $costSpent." * 100 = ".Currency::formatDollarAmountWithDecimal($LTCMarketValue)." %";
        } else {
            $LTCMarketValueToolTip = 'LTC - Market value = Initial Loan Amount / (Property Value (AS-Is)) * 100 ';
            if ($homeValue) {
                $LTCMarketValue = ($initialLoanAmount / $homeValue) * 100.0;
            }
            self::$LTCMarketValueToolTipWithValues = "$initialLoanAmount / ".$homeValue ." * 100 = ".Currency::formatDollarAmountWithDecimal($LTCMarketValue)." %";
        }

        /*  Total Loan Amount Tooltip*/
        if (self::$isAutoCalcTotalLoanAmountBasedOnLTC2) {
            $tLAToolTip = self::$tLAToolTip;
        } else {
            $tLADynamicToolTip = '';
            if (BaseHTML::fieldAccess(['fNm' => 'rehabCostFinanced', 'sArr' => $LTsecArr, 'opt' => 'D', 'tf' => 1]) &&
                $typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::TRANSACTIONAL) {
                $tLADynamicToolTip .= '+ Rehab / Construction Cost Financed';
                self::$tLAToolTipWithValues .= " + $rehabCostFinanced";
            }
            if ($typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE &&
                $typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::DELAYED_PURCHASE &&
                $typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::COMMERCIAL_CASH_OUT_REFINANCE &&
                $typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::REFINANCE &&
                $typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::RATE_TERM_REFINANCE &&
                $typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::COMMERCIAL_RATE_TERM_REFINANCE) {
                if (BaseHTML::fieldAccess(['fNm' => 'closingCostFinanced', 'sArr' => $LTsecArr, 'opt' => 'D', 'tf' => 1])) {
                    $tLADynamicToolTip .= '+ Closing Costs Financed';
                    self::$tLAToolTipWithValues .= " + $closingCostFinanced";
                }
            }
            if (LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->haveInterestreserve == 'Yes' &&
                BaseHTML::fieldAccess(['fNm' => 'haveInterestreserve', 'sArr' => $LTsecArr, 'opt' => 'D', 'tf' => 1])) {
                $tLADynamicToolTip .= '+ Prepaid Interest Reserve Financed';
                self::$tLAToolTipWithValues .= " + $prepaidInterestReserve";
            }
            $tLAToolTip = "Total Loan Amount = Initial Loan Amount " . $tLADynamicToolTip;
            self::$tLAToolTipWithValues = " $initialLoanAmount  " . self::$tLAToolTipWithValues . " = $ " . Currency::formatDollarAmountWithDecimal($totalLoanAmount);
        }
        /*  Total Loan Amount */


        $LTCInitialLoanAmountToolTip = null;
        $LTCInitialLoanAmount = null;
        if (self::isTransactionTypeRefinance($typeOfHMLOLoanRequesting)) {
            $LTCInitialLoanAmountToolTip = ' LTC - Initial Loan Amount = Initial Loan Amount / (Original Purchase Price ' .
                ((BaseHTML::fieldAccess(['fNm' => 'costSpent', 'sArr' => $RCI_sectionArray, 'opt' => 'D', 'tf' => 1])) ? '+ Cost Spent' : '')
                . ') * 100  ';

            $a = Strings::replaceCommaValues($initialLoanAmount);
            $b = Strings::replaceCommaValues($refinanceOriginalPurchasePrice)
                + ((BaseHTML::fieldAccess(['fNm' => 'costSpent', 'sArr' => $RCI_sectionArray, 'opt' => 'D', 'tf' => 1])) ? Strings::replaceCommaValues($costSpent) : 0);

            $LTCInitialLoanAmount = $b != 0 ? $a / $b * 100.0 : 0;
            self::$LTCInitialLoanAmountToolTipWithValues = " $a/$b * 100 = " . Currency::formatDollarAmountWithDecimal($LTCInitialLoanAmount) . " %";;
        } elseif (self::isTransactionTypePurchase($typeOfHMLOLoanRequesting) || self::isTransactionTypeNewConstruction($typeOfHMLOLoanRequesting)) {

            $LTCInitialLoanAmountToolTip = ' LTC - Initial Loan Amount = Initial Loan Amount / (Acquisition Purchase Price  ' .
                ((BaseHTML::fieldAccess(['fNm' => 'costSpent', 'sArr' => $RCI_sectionArray, 'opt' => 'D', 'tf' => 1])) ? '+ Cost Spent' : '')
                . ') * 100  ';

            $a = Strings::replaceCommaValues($initialLoanAmount);
            $b = Strings::replaceCommaValues($costBasis)
                + ((BaseHTML::fieldAccess(['fNm' => 'costSpent', 'sArr' => $RCI_sectionArray, 'opt' => 'D', 'tf' => 1])) ? Strings::replaceCommaValues($costSpent) : 0);
            $LTCInitialLoanAmount = $b != 0 ? ($a / $b) * 100.0 : 0;
            self::$LTCInitialLoanAmountToolTipWithValues = " $a/$b * 100 = " . Currency::formatDollarAmountWithDecimal($LTCInitialLoanAmount) . " %";
        }


        if (in_array($typeOfHMLOLoanRequesting, typeOfHMLOLoanRequesting::REFINANCE_LIST)) {

            self::$LTCOriginalPurchasePriceCls = null;
            $refinanceInitialOriginalPurchasePrice = refinanceMortgage::$refinanceMortgageInfo[0]->originalPurchasePrice ?? 0;
            self::$LTCOriginalPurchasePriceValue = proposalFormula::calculateLTCOriginalPurchasePriceValue($initialLoanAmount, $refinanceInitialOriginalPurchasePrice, $costSpent);

        }
        self::$LTCOriginalPurchasePriceToolTip = 'Initial Loan Amount / (Original Purchase Price + Cost Spent) * 100. This LTC calculation is  for refinance transaction types only. It utilizes the "original purchase price" found in the section called "Current Mortgage Refinance Info" ';

        /*
        * Description   : Current Loan Balance.
        * Formula       : Current Loan Balance = Initial Loan Amount + Pre-paid Interest Reserves + Closing Costs + Funded Draws
        * Task 			: https://www.pivotaltracker.com/story/show/160959499
        * Budget & Draws Start
        */
        foreach ($budgetAndDrawsInfo as $item) {
            $totalDrawsFunded += $item->amountAddedToTotalDrawsFunded ?? 0;
        }
        $availableBudget = $rehabCostFinanced - $totalDrawsFunded;
//$currentLoanBalance = Strings::replaceCommaValues($tempTotalLoanAmount) - Strings::replaceCommaValues($availableBudget);

        $paydownamount = 0;
        $paycount = sizeof($loanVariables->payDownInfo ?? []);
        foreach ($loanVariables->payDownInfo as $item) {
            $paydownamount += $item->principalPayDownAmount;
        }

        $CLBInArray = [
            'initLAmt'         => $initialLoanAmount,
            'prePIR'           => $prepaidInterestReserveForCal,
            'closingCost'      => $closingCostFinanced,
            'funDraw'          => $totalDrawsFunded,
            'principalPayDown' => $paydownamount,
        ];

        if (self::$isFileSPREO) {
            $currentLoanBalance = proposalFormula::calculateCurrentLoanBalanceForSPREO($initialLoanAmount,
                $prepaidInterestReserve);
        } else {
            self::$currentLoanBalanceTooltip = "Current Loan Balance = Initial Loan Amount 
                        + Pre-paid Interest Reserves 
                        + Closing Costs 
                        + Funded Draws.<br> <b>For Refinance Transaction Types,</b> Current Loan Balance = Initial Loan Amount + Any Funded Draws";

            if ($currentLoanBalance <= 0) {
                $currentLoanBalance = proposalFormula::calculateCurrentLoanBalance($CLBInArray, $typeOfHMLOLoanRequesting);
            }
        }

        if ($isLoanPaymentAmt == LoanTerms::ILA) {
            $tempTotalLoanAmount = $currentLoanBalance; // https://www.pivotaltracker.com/story/show/160959499
            $totalMonthlyPaymentTooltip = 'If Amortization is <b>Interest Only</b><br>Loan Payment = (Current Loan Balance * Int. Rate) / 1200<br><br>Otherwise,<br>Loan Payment = Current Loan Balance * Int.  Rate / (1 - (1/ (1 + (Int. Rate / 1200))<sup>((Amortization * 12) / 1200)</sup>)';
        }
        if ($isLoanPaymentAmt == LoanTerms::TLA) {
            $totalMonthlyPaymentTooltip = 'If Amortization is <b>Interest Only</b><br>Loan Payment = (Total Loan Amount * Int. Rate) / 1200<br><br>Otherwise,<br>Loan Payment = Total Loan Amount * Int.  Rate / (1 - (1/ (1 + (Int. Rate / 1200))<sup>((Amortization * 12) / 1200)</sup>)';
        }
        if ($isLoanPaymentAmt == 'SMP') {
            $totalMonthlyPaymentTooltip = 'Enter payment amount manually';
        }
        $isLoTxt = 'Current Loan Balance';
        if ($typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::TRANSACTIONAL) {
            $isLoTxt = 'Initial Loan Amount';
            $tempTotalLoanAmount = $initialLoanAmount;
        }
        /* Budget & Draws End */

//https://app.shortcut.com/lendingwise/story/32289/accrual-types-verify-and-fix-any-calculations-add-additional-types-tooltip-explaining-the-formulas
//Monthly Payment Start **lien1Payment
        if ($isLoanPaymentAmt == LoanTerms::ILA || $isLoanPaymentAmt == LoanTerms::TLA) {
            $totalMonthlyPayment = proposalFormula::calculateHMLOPaymentValue(
                $tempTotalLoanAmount,
                $lien1Rate,
                $lien1Terms,
                $purchaseCloseDate,
                $perDiemAccrualType ?: $accrualType
            );
        } else {
            $totalMonthlyPayment = $loanVariables->totalMonthlyPayment;
        }

        $totalMonthlyPayment = Strings::replaceCommaValues($totalMonthlyPayment);

//Monthly Payment End **lien1Payment
        $tempTotalLoanAmount = $totalLoanAmount;
        $netMonthlyPayment = proposalFormula::calculateHMLONetMonthlyPayment(
            $totalMonthlyPayment,
            $taxes1,
            $annualPremium,
            $spcf_hoafees
        );

        $LTC = proposalFormula::calculateLoanToCost($tempTotalLoanAmount, $totalProjectCost);

        $marketLTV = proposalFormula::calculateMarketLTV_New(
            $acquisitionPriceFinanced,
            $homeValue,
            $typeOfHMLOLoanRequesting,
            $tempTotalLoanAmount,
            $closingCostFinanced,
            $prepaidInterestReserveForCal
        );

        if ($loanVariables->netOperatingIncome) {
            self::$netOperatingIncome = $loanVariables->netOperatingIncome;
        }

        // $netOperatingIncome = proposalFormula::calculateNetOperatingIncome($actualRentsInPlace, $lessActualExpenses);
        $debtServiceRatio = proposalFormula::calculateDebtServiceRatio($totalMonthlyPayment, self::$netOperatingIncome);

        $isEFBlanketLoanDispOpt = 'display: none;';
        $isBlanketLoanDispOpt = 'display: none;';
        if ($isBlanketLoan == 'Yes') {
            $isEFBlanketLoanDispOpt = $tableCell;
            $isBlanketLoanDispOpt = $tableRow;
        }

        //$subjectPropertySectionDispOpt = 'display: none;';

        $totProjectCostLbl = 'Total Project Cost';
        $commercialFieldsDispOpt = $tableRow;
        $subjectPropertySectionDispOpt = 'display: block;';
        $refinanceSectionDispOpt = 'display: none;';
        $transactionalFieldsDispOpt = 'display: none;';
        $blanketLoanFieldsDispOpt = 'display: none;';
        $doesPropertyNeedRehabSection = 'display: none;';
        $cashOutRefinanceFields = $downPaymentFieldDispOpt = 'display: none;';
        $commercialFieldsTDDispOpt = $tableCell;
        $rehabConsCls = 'display: none;';
        $purchaseTPCCls = 'display : none;';
        $typeOfSaleTR = 'display: none;';
        $isBlanketLoanDiv = $tableRow;
        $LOCTotalLoanAmtHideDispOpt = $tableCell;
        $LOCTotalLoanAmtDispOpt = 'display: none;';
        $feeSectionTotalLoanAmtOpt = $tableCell;
        $acquisitionLTVTD = 'display: none;';
        $cashOutTotalAmtTD = 'display: none;';
        $marketLTVTD = $tableCell;
        $commercialFieldsTDNCDispOpt = '';
        $lineOfCreditProp = '';

//$lineOfCreditProp = $tableRow;
        $LTVPercentageDisp = '';
        $totalCashOutDisp = '';
        $transCommercialFieldsDispOpt = '';

        if (
            $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::RATE_TERM_REFINANCE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::DELAYED_PURCHASE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::COMMERCIAL_RATE_TERM_REFINANCE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::COMMERCIAL_CASH_OUT_REFINANCE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::LINE_OF_CREDIT
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::REFINANCE
        ) {
            $doesPropertyNeedRehabSection = $tableCell;

            if ($typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::COMMERCIAL_CASH_OUT_REFINANCE
                || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE
                || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::DELAYED_PURCHASE
                || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::RATE_TERM_REFINANCE
                || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::COMMERCIAL_RATE_TERM_REFINANCE
                || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::REFINANCE
            ) {
                $cashOutDiv = $tableRow;
            } else {
                $cashOutDiv = 'display: none;';
            }

            if ($typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::COMMERCIAL_CASH_OUT_REFINANCE
                || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE
                || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::DELAYED_PURCHASE
                || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::REFINANCE
                || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::COMMERCIAL_RATE_TERM_REFINANCE
                || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::RATE_TERM_REFINANCE
            ) {
                $rehabConsCls = $tableRow;
                $cashOutTotalAmtTD = $tableCell;
                $LOCTotalLoanAmtHideDispOpt = 'display: none;';
            }

            if ($typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::LINE_OF_CREDIT) {
                $LOCTotalLoanAmtHideDispOpt = 'display: none;';
                $feeSectionTotalLoanAmtOpt = 'display: none;';
                $LOCTotalLoanAmtDispOpt = $tableCell;
                $rehabConsCls = 'display: none;';
                $lineOfCreditProp = $tableRow;
            } else {
                $refinanceSectionDispOpt = 'display: block;';

            }

            $cashOutRefinanceFields = $tableCell;
            $commercialFieldsDispOpt = 'display: none;';
            $commercialFieldsTDDispOpt = 'display: none;';
            if ($typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::DELAYED_PURCHASE) {
                $transCommercialFieldsDispOpt = 'display: none;';
            }

        } else if ($typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::TRANSACTIONAL) {

            $doesPropertyNeedRehabDispDiv = 'display: none;';
            $doesPropertyNeedRehabDispTDDiv = 'display: none;';
            $downPaymentFieldDispOpt = $tableRow2;
            $transactionalFieldsDispOpt = $tableRow;
            $transCommercialFieldsDispOpt = $tableCell;
            $commercialFieldsDispOpt = 'display: none;';
            /* $subjectPropertySectionDispOpt = 'display: none;'; */
            $contigencyCls = 'display: none;';
            $cashOutDiv = 'display: none;';
            $isBlanketLoanDiv = 'display: none;';
            $isBlanketLoanDispOpt = 'display: none;';
        } else {
            $transCommercialFieldsDispOpt = $tableCell;
            $doesPropertyNeedRehabSection = $tableCell;
            $cashOutDiv = 'display: none;';
            $acquisitionLTVTD = $tableCell;
            $downPaymentFieldDispOpt = $tableRow2;
            $typeOfSaleTR = $tableRow;

            if ($typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::NEW_CONSTRUCTION_EXISTING_LAND) {
                $commercialFieldsTDNDateCDispOpt = $tableRow;
                $propertyNeedRehab = 'Yes';
                $commercialFieldsTDNCDispOpt = 'display: none;';

                $rehabConsCls = '';
                $lineOfCreditProp = $tableRow;
                $LTVPercentageDisp = 'display: none;';
                $totalCashOutDisp = 'display: none;';
                $LOCTotalLoanAmtHideDispOpt = 'display: none;';

            }
        }
        if (($doesPropertyNeedRehabDispTDDiv == ''
                || trim($doesPropertyNeedRehabDispTDDiv) == trim('display: block;'))
            && $isThisGroundUpConstruction == 'Yes') {
            $groundUpDispTDDiv = $tableCell;
        }
        $newConDownPaymentDisp = $commercialFieldsTDNCDispOpt;

        if ($typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::COMMERCIAL_RATE_TERM_REFINANCE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::RATE_TERM_REFINANCE
        ) {
            doNothing();
            //  $currentLoandBalanceDisplay = 'display: none;';
        }

        if ($typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::NEW_CONSTRUCTION_EXISTING_LAND
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::DELAYED_PURCHASE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::COMMERCIAL_CASH_OUT_REFINANCE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::REFINANCE
        ) {
            $totProjectCostLbl = 'Total Project Value';
            $acquisitionLTVTD = 'display: none;';
        }
        if ($typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::NEW_CONSTRUCTION_EXISTING_LAND) {
            $marketLTVTD = 'display: none;';
        }

        $autoCalcTLAARVDisp = 'display : none;';
        $autoCalARVToolTip = '';
        if ($typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::PURCHASE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::COMMERCIAL_PURCHASE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::DELAYED_PURCHASE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::COMMERCIAL_CASH_OUT_REFINANCE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::REFINANCE
        ) {
            $autoCalcTLAARVDisp = $tableCell;
            if ($typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::PURCHASE ||
                $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::COMMERCIAL_PURCHASE
            ) {
                $autoCalARVToolTip = 'Rehab/Construction Cost Financed= Total Loan Amount - Initial Loan Amount - Pre-Paid Interest Reserve - Closing Cost Financed';
            } else {
                $autoCalARVToolTip = 'Rehab/Construction Cost Financed= Total Loan Amount - Initial Loan Amount';
            }
        }

        /* 159612553 >> Urgent Quick-Fix--> Per diem interest--> add to closing cost */

        $perDiem = per_diem::calc(
            $interestChargedFromDate,
            $interestChargedEndDate,
            $accrualType,
            $perDiemAccrualType,
            $isLoanPaymentAmt,
            Strings::Numeric($currentLoanBalance),
            Strings::Numeric($tempTotalLoanAmount),
            $lien1Rate,
            !$loanVariables->nonInclusivePerDiem
        );

        $diemDays = $perDiem->diemDays;

        $PerDiemTotalAmt = per_diem::perDiemLoanAmount(
            $isLoanPaymentAmt,
            Strings::Numeric($currentLoanBalance),
            Strings::Numeric($tempTotalLoanAmount)
        );

        $isLoanPaymentAmtTxt = per_diem::perDiemLoanAmountSource($isLoanPaymentAmt);

        $lien1Rate = floatval($lien1Rate ?: 0);
        $PerDiemTotalAmt = floatval($PerDiemTotalAmt ?: 0);

        $totalDailyInterestCharge = $perDiem->totalDailyInterestCharge;

        $interestDays = accrualTypes::getYearDays($perDiemAccrualType ?: $accrualType);

        $perDiemToolTip = 'Per Diem Interest = (Interest Rate /' . $interestDays . ') * ' . $isLoanPaymentAmtTxt . '  = ' . $totalDailyInterestCharge;

        $dailyEstPerDiemArray = [
            'diemDays'                 => $diemDays,
            'totalDailyInterestCharge' => $totalDailyInterestCharge,
        ];

        $totalEstPerDiem = $perDiem->totalEstPerDiem;

        /// end per Diem calculations

        $totalFeesAndCost = $fileHMLONewLoanInfo ? proposalFormula::calculateTotalFeesAndCostNew(
            $fileHMLONewLoanInfo,
            [
                'totalEstPerDiem'        => $totalEstPerDiem,
                'originationPointsValue' => $originationPointsValue,
                'brokerPointsValue'      => $brokerPointsValue,
            ]
        ) : 0;

        $perClosingCostFinanced = proposalFormula::calculatePercentageClosingCostFinanced($closingCostFinanced, $totalFeesAndCost);
        $ARV = proposalFormula::calculateARVPercentage($tempTotalLoanAmount, $assessedValue);
        $closingCostNotFinanced = proposalFormula::calculateClosingCostNotFinanced($totalFeesAndCost, $closingCostFinanced);


        if (self::$isAutoCalcTotalLoanAmountBasedOnLTC2) {
            $totalCashToClose = self::$totalCashToCloseLTC2;
        }
        if (self::$setManuallyTotalCashToClose) {
            $totalCashToClose = $loanVariables->fileHMLONewLoanInfo['totalCashToClose'];
        } else {
            $totalCashToClose = proposalFormula::calculateTotalCashToClose($closingCostNotFinanced,
                $maxAmtToPutDown,
                $otherDownPayment,
                $earnestDeposit,
                $CTCSecArr);
        }

        $NewTPCToolTip = '';

        if ($typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::DELAYED_PURCHASE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::COMMERCIAL_RATE_TERM_REFINANCE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::COMMERCIAL_CASH_OUT_REFINANCE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::NEW_CONSTRUCTION_EXISTING_LAND
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::REFINANCE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::RATE_TERM_REFINANCE
        ) {
            $TPCToolTip = 'Total Project Value = (Property Value(As-Is) + Rehab/Construction Cost + Cost Spent  + Pre-paid Interest Reserve* <br><b>*Pre-paid interest reserve only included, if Financing Pre-paid interest reserve= Yes and Add to Total Project Cost= Yes</b> ) ';

            $LTCToolTip = 'LTC - Total Loan Amount =  (Total Loan Amount / Project Value) * 100<hr>Loan-to-Cost Ratio (LTC) A ratio used in commercial real estate construction to compare the amount of the loan used to finance a project to the cost to build the project. If the project costs $1 million to complete and the borrower borrows $700,000, the loan-to-cost (LTC) ratio would be 70%.';
        } else if ($typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::LINE_OF_CREDIT) {
            $TPCToolTip = 'Total Project Cost = (Rehab/Construction Cost + Closing Cost Financed + Cost Spent  + Pre-paid Interest Reserve* <br><b>*Pre-paid interest reserve only included, if Financing Pre-paid interest reserve= Yes and Add to Total Project Cost= Yes</b> ) ';

            $LTCToolTip = 'LTC - Total Loan Amount =  (Total Loan Amount / Project Cost) * 100<hr>Loan-to-Cost Ratio (LTC) A ratio used in commercial real estate construction to compare the amount of the loan used to finance a project to the cost to build the project. If the project costs $1 million to complete and the borrower borrows $700,000, the loan-to-cost (LTC) ratio would be 70%.';
        } else {
            $TPCToolTip = 'Total Project Cost = (Acquisition / Purchase Price + Rehab/Construction Cost + Cost Spent + Pre-paid Interest Reserve* <br><b>*Pre-paid interest reserve only included, if Financing Pre-paid interest reserve= Yes and Add to Total Project Cost= Yes</b> ) ';

            $LTCToolTip = 'LTC - Total Loan Amount =  (Total Loan Amount / Project Cost) * 100<hr>Loan-to-Cost Ratio (LTC) A ratio used in commercial real estate construction to compare the amount of the loan used to finance a project to the cost to build the project. If the project costs $1 million to complete and the borrower borrows $700,000, the loan-to-cost (LTC) ratio would be 70%.';
            // When transaction type= Purchase or commercial purchase...
            $NewTPCToolTip = 'Total project soft & hard cost =  (Purchase price + Rehab/const cost + Total Fees & Costs + Cost Spent) <hr> Note: Do not include Accrued Per Diem Interest';
        }

        if (self::$isAutoCalcTotalLoanAmountBasedOnLTC2) {
            $TPCToolTip = HMLOLoanTermsCalculation::$TPCToolTip;
        }
        if (self::isTransactionTypeRefinance($typeOfHMLOLoanRequesting)) {
            $NewLTCToolTip = ' LTC w/Soft & Hard Cost  = Total Loan Amount / (Original Purchase Price + Rehab Cost + Cost Spent + Closing Costs not Financed) / 100 ';
        } else if (self::isTransactionTypePurchase($typeOfHMLOLoanRequesting) || self::isTransactionTypeNewConstruction($typeOfHMLOLoanRequesting)) {
            $NewLTCToolTip = ' LTC w/Soft & Hard Cost  = Total Loan Amount / (Purchase Price + Rehab Cost + Cost Spent + Closing Costs not Financed) / 100 ';
        } else {
            $NewLTCToolTip = ' LTC w/Soft & Hard Cost  = Total Loan Amount / (Original Purchase Price + Rehab Cost + Cost Spent + Closing Costs not Financed) / 100 ';
        }


        if ($typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::DELAYED_PURCHASE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::RATE_TERM_REFINANCE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::COMMERCIAL_RATE_TERM_REFINANCE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::COMMERCIAL_CASH_OUT_REFINANCE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::LINE_OF_CREDIT
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::NEW_CONSTRUCTION_EXISTING_LAND
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::REFINANCE
        ) {
            $marketLTVToolTip = 'Market LTV = (Total Loan Amount / Property Value(As-Is)) * 100<hr>This is a percentage of your loan to values based off the market value,  not the purchase price.';
        } else {
            $marketLTVToolTip = 'Market LTV = ( Initial Loan Amount + Closing Cost Financed ) / Assessed As-is Value) * 100<hr>This is a percentage of your loan to values based off the market value,  not the purchase price.';
        }

        $intAmt = 0;
        if ($typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::RATE_TERM_REFINANCE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::COMMERCIAL_RATE_TERM_REFINANCE) {

            $intAmt = proposalFormula::isEmptyFieldsCheck($payOffMortgage1)
                + proposalFormula::isEmptyFieldsCheck($payOffMortgage2)
                + proposalFormula::isEmptyFieldsCheck($payOffOutstandingTaxes);
        }


        $netLenderFundsinputArray = [
            'typeOfHMLOLoanRequesting'             => $typeOfHMLOLoanRequesting,
            'initialLoanAmount'                    => $initialLoanAmount,
            'totalLoanAmount'                      => $tempTotalLoanAmount,
            'totalFeesAndCost'                     => $totalFeesAndCost,
            'closingCostFinanced'                  => $closingCostFinanced,
            'rehabCostFinanced'                    => $rehabCostFinanced,
            'prepaidInterestReserve'               => $prepaidInterestReserve,
            'initialDrawAmount'                    => LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->initialAdvance,
            'isAutoCalcTotalLoanAmountBasedOnLTC2' => HMLOLoanTermsCalculation::$isAutoCalcTotalLoanAmountBasedOnLTC2,
            'escrowFees'                           => LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->escrowFees,
            'isTransactionTypePurchaseCategory'    => HMLOLoanTermsCalculation::isTransactionTypePurchaseCategory($typeOfHMLOLoanRequesting),
            'isTransactionTypeRefinanceCategory'   => HMLOLoanTermsCalculation::isTransactionTypeRefinanceCategory($typeOfHMLOLoanRequesting),
            'payOffMortgage1'                      => $payOffMortgage1,
            'payOffMortgage2'                      => $payOffMortgage2,
            'payOffOutstandingTaxes'               => $payOffOutstandingTaxes,
            'LTC2_additionalReserveInterest'       => LMRequest::myFileInfo()->getFileCalculatedValues()->LTC2_additionalReserveInterest,
            'LTC2_additionalOriginationInterest'   => LMRequest::myFileInfo()->getFileCalculatedValues()->LTC2_additionalOriginationInterest
        ];
//Net Lender Funds to Title Start **FieldId = netLenderFundsToBorrower
        $netLenderFundsToBorrower = proposalFormula::calculateNetLenderFundsToBorrower($netLenderFundsinputArray);

        if (HMLOLoanTermsCalculation::isTransactionTypePurchaseCategory($typeOfHMLOLoanRequesting)) {
            self::$netLenderFundsToBorrowerToolTip = 'Net Lender Funds to ' . (LMRequest::$PCID == 4268 ? 'Borrower' : 'Title') . ' = Total Loan Amount - Total Fees & Costs - Rehab Cost Financed - Prepaid Interest Reserve + Initial Draw Amount' . (self::$isAutoCalcTotalLoanAmountBasedOnLTC2 ? ' + Settlement or Closing/Escrow Fee - Additional Reserve Interest - Additional Origination Interest' : '') . '<hr>' . self::$netLenderFundsToBorrowerToolTipWithValues;
        } elseif (HMLOLoanTermsCalculation::isTransactionTypeRefinanceCategory($typeOfHMLOLoanRequesting)) {
            self::$netLenderFundsToBorrowerToolTip = 'Net Lender Funds to ' . (LMRequest::$PCID == 4268 ? 'Borrower' : 'Title') . ' = Total Loan Amount - Total Fees & Costs - Rehab Cost Financed - Payoff Mortgage 1 - Payoff Mortgage 2 - Payoff Taxes - Prepaid Interest Reserve + Initial Draw Amount ' . (self::$isAutoCalcTotalLoanAmountBasedOnLTC2 ? ' + Settlement or Closing/Escrow Fee' : '') . '<hr>' . self::$netLenderFundsToBorrowerToolTipWithValues;
        }


        $taxImpoundsInArray = [
            'taxImpoundsMonth'    => $taxImpoundsMonth,
            'taxImpoundsMonthAmt' => $taxImpoundsMonthAmt,
        ];
        if ($taxImpoundsFee <= 0) {
            $taxImpoundsFee = proposalFormula::calculateTaxImpoundsFee($taxImpoundsInArray);
        }
        $insImpoundsInArray = [
            'insImpoundsMonth'    => $insImpoundsMonth,
            'insImpoundsMonthAmt' => $insImpoundsMonthAmt,
        ];
        if ($insImpoundsFee <= 0) {
            $insImpoundsFee = proposalFormula::calculateInsImpoundsFee($insImpoundsInArray);
        }

        $extensionOptionsAmtInputArray = [
            'totalLoanAmount'           => $tempTotalLoanAmount,
            'extensionOptionPercentage' => $extensionOptionPercentage,
        ];
        $extensionOptionsAmt = proposalFormula::calculatePercentageExtensionOption($extensionOptionsAmtInputArray);
        /**
         ** Description    : Required Reserves Section Calculation Methods
         ** Developer    : Venkatesh
         ** Date            : Oct 12, 2017
         **/
        //$paymentReservesAmt = proposalFormula::calculatePaymentReserves($paymentReserves, $totalMonthlyPayment);

        //Include Tax, Insurance and HOA
        $includeTaxesInsuranceHOA = LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->includeTaxesInsuranceHOA ?? 0;
        if (glCustomJobForProcessingCompany::isPC_CV3(LMRequest::$PCID) || !$includeTaxesInsuranceHOA) { //Old code
            $paymentReservesAmt = proposalFormula::calculatePaymentReserves($paymentReserves, $totalMonthlyPayment);
        } else {
            $paymentReservesAmt = proposalFormula::calculatePaymentReserves($paymentReserves, $netMonthlyPayment);
        }
        $requiredConstructionAmt = proposalFormula::calculateRequiredConstruction($requiredConstruction, $rehabCost, $rehabCostFinanced);

        $percentageTotalLoanAmount = proposalFormula::calculatePercentageTotalLoanAmount($percentageTotalLoan, $totalLoanAmount);

        self::$totalInterestPaymentReserveRequired = self::calculateTotalInterestPaymentReserveRequired($haveInterestreserve, $paymentReservesAmt, $prepaidInterestReserve, $RLRSecArr);


        $contingencyReserveAmt = proposalFormula::calculateContingencyReserve($contingencyReserve, $rehabCost);

        if (self::$setManuallyTotalRequiredReserves) {
            $totalRequiredReserves = $loanVariables->fileHMLONewLoanInfo['totalRequiredReserves'];
        } else {
            $totalRequiredReserves = proposalFormula::calculateTotalRequiredReserves(self::$totalInterestPaymentReserveRequired,
                $requiredConstructionAmt,
                $contingencyReserveAmt,
                $percentageTotalLoanAmount,
                $RLRSecArr);
        }


        if ($rehabCostPercentageFinanced == 0) {
            $rehabCostPercentageFinanced = proposalFormula::calculateRehabCostPercentageFinanced($rehabCostFinanced, $rehabCost);
        }
        if ($downPaymentPercentage == 0) {
            $downPaymentPercentage = proposalFormula::calculateDownPaymentPercentage($maxAmtToPutDown, $costBasis);
        }

        if (self::$isFileSPREO) {
            $totalCashOutAmt = proposalFormula::calculateTotalCashOutForSPREO(
                self::isTransactionTypeRefinanceCategory($typeOfHMLOLoanRequesting),
                $CORTotalLoanAmt,
                $closingCostFinanced,
                $payOffMortgage1,
                $payOffMortgage2,
                $payOffOutstandingTaxes
            );
        } elseif (self::$isAutoCalcTotalLoanAmountBasedOnLTC2
            && self::isTransactionTypeRefinanceCategory($typeOfHMLOLoanRequesting)) {
            $totalCashOutAmt = self::$totalCashOutForLTC2;
        } else {
            $totalCashOutAmt = proposalFormula::calculateTotalCashOut(
                $initialLoanAmount,
                $payOffMortgage1,
                $payOffMortgage2,
                $payOffOutstandingTaxes,
                $closingCostFinanced,
                $rehabCostFinanced,
                $typeOfHMLOLoanRequesting,
                $prepaidInterestReserveForCal
            );
        }

        if (!$CORTotalLoanAmt) {
            $CORTotalLoanAmt = proposalFormula::calculateCORefiLoanAmtByLTVPercentage($CORefiLTVPercentage, $homeValue);
        }

        if (!$CORefiLTVPercentage) {
            $CORefiLTVPercentage = proposalFormula::calculateCORefiLTVPercentage($CORTotalLoanAmt, $homeValue);
        }

        $sARVIP = [
            'totalLoanAmount'        => $tempTotalLoanAmount,
            'originationPointsValue' => $originationPointsValue,
            'brokerPointsValue'      => $brokerPointsValue,
            'prepaidInterestReserve' => $prepaidInterestReserveForCal,
            'assessedValue'          => $assessedValue,
        ];

        $simpleARV = proposalFormula::calculateSimpleARVPercentage($sARVIP);

        $paymentduedate = date('Y') . '-' . (date('m')) . '-' . trim($loanVariables->paymentDue ?? '');
        $paymentduedate = Dates::formatDateWithRE($paymentduedate, 'YMD', 'm/d/Y');

        $payOffDate = trim($loanVariables->payOffDate ?? '');
        if (Dates::IsEmpty($payOffDate)) {
            $payOffDate = '';
        } else {
            $payOffDate = Dates::formatDateWithRE($payOffDate, 'YMD', 'm/d/Y');
        }
        $amountdue = $loanVariables->amountPastDueOrOwed ?? 0;
        $payOffAmountArray = [
            'paymentduedate'           => $paymentduedate,
            'payOffDate'               => $payOffDate,
            'currentLoanBalance'       => $currentLoanBalance,
            'totalDailyInterestCharge' => $totalDailyInterestCharge,
            'totalMonthlyPayment'      => $totalMonthlyPayment,
            'amountdue'                => $amountdue,
        ];
        $payOffAmount = proposalFormula::calculatePayOffAmount($payOffAmountArray);

        $pos = strpos($loanTerm, ' ');
        $loanTerms = intval(substr($loanTerm, 0, $pos));
        $nper = $loanTerms;
        $pmt = -Strings::replaceCommaValues($totalMonthlyPayment);
        $pv = Strings::replaceCommaValues($initialLoanAmount);
        $InrBasedOnMonthlyPayment = round((proposalFormula::RATE($nper, $pmt, $pv, $fv ?? 0, $guess ?? 0.1) * 12 * 100), 2);

        $totalFeesAndCostNew = Strings::replaceCommaValues($totalFeesAndCost) - Strings::replaceCommaValues($totalEstPerDiem); //

        $NewTotalProjectCost = Strings::replaceCommaValues($costBasis)
            + Strings::replaceCommaValues($rehabCost)
            + Strings::replaceCommaValues($totalFeesAndCostNew)
            + Strings::replaceCommaValues($costSpent);

//        if (BaseHTML::fieldAccess(['fNm' => 'costSpent', 'sArr' => $LTsecArr, 'opt' => 'D', 'tf' => 1])) {
//            $NewLTCCal = Strings::replaceCommaValues($costBasis) + Strings::replaceCommaValues($rehabCost) + Strings::replaceCommaValues($closingCostNotFinanced) + Strings::replaceCommaValues($costSpent);
//        } else {
//            $NewLTCCal = Strings::replaceCommaValues($costBasis) + Strings::replaceCommaValues($rehabCost) + Strings::replaceCommaValues($closingCostNotFinanced);
//        }
//        $NewLoanToCost = Strings::replaceCommaValues($NewLTCCal) != 0 ? (Strings::replaceCommaValues($totalLoanAmount) / Strings::replaceCommaValues($NewLTCCal)) * 100 : 0;


        $costSpentValue = 0;
        if (BaseHTML::fieldAccess(['fNm' => 'costSpent', 'sArr' => $RCI_sectionArray, 'opt' => 'D', 'tf' => 1])) {
            $costSpentValue = Strings::replaceCommaValues($costSpent);
        }
        $softHardPurchasePrice = 0;
        if (self::isTransactionTypeRefinance($typeOfHMLOLoanRequesting)) {
            $softHardPurchasePrice = Strings::replaceCommaValues($refinanceOriginalPurchasePrice);
        } else if (self::isTransactionTypePurchase($typeOfHMLOLoanRequesting) || self::isTransactionTypeNewConstruction($typeOfHMLOLoanRequesting)) {
            $softHardPurchasePrice = Strings::replaceCommaValues($costBasis);
        } else {
            $softHardPurchasePrice = Strings::replaceCommaValues($refinanceOriginalPurchasePrice);
        }
        $NewLTCCal = $softHardPurchasePrice
            + Strings::replaceCommaValues($rehabCost)
            + Strings::replaceCommaValues($costSpentValue)
            + Strings::replaceCommaValues($closingCostNotFinanced);

        $NewLoanToCost = Strings::replaceCommaValues($NewLTCCal) != 0 ? (Strings::replaceCommaValues($totalLoanAmount) / Strings::replaceCommaValues($NewLTCCal)) * 100 : 0;

        self::$NewLTCToolTipWithValues = "$totalLoanAmount / ($softHardPurchasePrice + $rehabCost + $costSpentValue + $closingCostNotFinanced) * 100 = ".Currency::formatDollarAmountWithDecimal($NewLoanToCost).' %';

        $NewTotalProjectCostDisp = 'display:none;';

        if (($typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::PURCHASE ||
                $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::COMMERCIAL_PURCHASE ||
                $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::NEW_CONSTRUCTION_EXISTING_LAND) &&
            $propertyNeedRehab == 'Yes'
        ) {
            $NewTPCToolTip = 'Total project soft & hard cost =  (Purchase price + Rehab/const cost + Total Fees & Costs + Cost Spent) <hr> Note: Do not include Accrued Per Diem Interest ';
            self::$NewTPCToolTipWithValues = 'Total project soft & hard cost =  (Purchase price + Rehab/const cost + Total Fees & Costs + Cost Spent) <hr> Note: Do not include Accrued Per Diem Interest ' . '<hr>' . " $costBasis + $rehabCost + $totalFeesAndCostNew + $costSpent = $ " . Currency::formatDollarAmountWithDecimal($NewTotalProjectCost);
            $NewTotalProjectCostDisp = '';
        }

        if ($propertyNeedRehab == 'Yes') {
            if ($typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE ||
                $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::DELAYED_PURCHASE) {
                $originalPurchasePrice = 0;
                $refinanceMortgageInfo = refinanceMortgage::$refinanceMortgageInfo;
                if (sizeof($refinanceMortgageInfo ?? [])) {
                    $originalPurchasePrice = Strings::replaceCommaValues($refinanceMortgageInfo[0]->originalPurchasePrice);
                }
                $grossProfit = ($assessedValue - $originalPurchasePrice - $costSpentValue - $rehabCost);
                HMLOLoanTermsCalculation::$grossProfitTooltip = "ARV - Original Purchase Price - Cost Spent - Rehab/Construction Cost ";
                HMLOLoanTermsCalculation::$grossProfitTooltipWithValues = "$assessedValue - $originalPurchasePrice - $costSpentValue - $rehabCost = $ " . currency::formatDollarAmountWithDecimal($grossProfit);
            } else {
                $grossProfit = ($assessedValue - $costBasis - $costSpentValue - $rehabCost);
                HMLOLoanTermsCalculation::$grossProfitTooltip = "ARV - Purchase Price - Cost Spent - Rehab/Construction Cost  ";
                HMLOLoanTermsCalculation::$grossProfitTooltipWithValues = "$assessedValue - $costBasis - $costSpentValue - $rehabCost = $ " . currency::formatDollarAmountWithDecimal($grossProfit);
            }
            $grossProfitMargin = $assessedValue ? ($grossProfit / $assessedValue) * 100 : 0;
            HMLOLoanTermsCalculation::$grossProfitMarginTooltipWithValues = " ($grossProfit / $assessedValue) * 100 = " . Currency::formatDollarAmountWithDecimal($grossProfitMargin) . " %";
        }


        $mortgageOwner1 = $loanVariables->mortgageOwner1;
        $insurance1 = $loanVariables->insurance1;
        $HOAFees1 = $loanVariables->HOAFees1;
        $mortgageInsurance1 = $loanVariables->mortgageInsurance1;
        $floodInsurance = $loanVariables->floodInsurance;

        $_TotalPITIA = _TotalPITIA::getForLMRID(LMRequest::$LMRId);

        if ($mortgageOwner1 == 3) {
            /*** Include the mortgage insurance while calculating DTI if the mortgage type/owner is FHA - on May 04, 2016 ***/
            $tempLien1PaymentPITIA = proposalFormula::calculatePaymentPI($lien1Payment, $taxes1, $insurance1, $HOAFees1, $mortgageInsurance1, $floodInsurance);
        } else {
            $tempLien1PaymentPITIA = proposalFormula::calculatePaymentPI($lien1Payment, $taxes1, $insurance1, $HOAFees1, 0, $floodInsurance);
        }
        self::$currentDTI = proposalFormula::calculateDTI($tempLien1PaymentPITIA, 0, $_TotalPITIA ? $_TotalPITIA->disposableincome : 0);

        self::$debtServiceRatio = proposalFormula::calculateDebtServiceRatio($totalMonthlyPayment, self::$netOperatingIncome);

        $effectiveGrossIncome = $loanVariables->effectiveGrossIncome;
        self::$debtServiceRatioPITIA = debtServiceRatioPITIA::getReportParams(
            $effectiveGrossIncome,
            $netMonthlyPayment,
            $spcf_hoafees
        );

        self::$hideFieldsForLTC2 = self::$isAutoCalcTotalLoanAmountBasedOnLTC2 ? ' display:none; ' : null;

        self::$showDivForLTC2 = self::$isAutoCalcTotalLoanAmountBasedOnLTC2 && self::isTransactionTypePurchaseCategory(LTC2Variables::$typeOfHMLOLoanRequesting) ? null : ' display:none; ';
        self::$enableFieldsForLTC2 = self::$isAutoCalcTotalLoanAmountBasedOnLTC2 && self::isTransactionTypePurchaseCategory(LTC2Variables::$typeOfHMLOLoanRequesting) ? null : ' disabled ';

        self::$hideFieldsForLTC2_RefinanceCategory = (self::$isAutoCalcTotalLoanAmountBasedOnLTC2 && self::isTransactionTypeRefinanceCategory($typeOfHMLOLoanRequesting)) ? ' display:none; ' : null;
        self::$readyOnlyFieldsForLTC2_RefinanceCategory = (self::$isAutoCalcTotalLoanAmountBasedOnLTC2 && self::isTransactionTypeRefinanceCategory($typeOfHMLOLoanRequesting)) ? ' readonly ' : null;

        self::$TotalNetWorth = floatval(self::$TotalAssets) - floatval(self::$TotalOwed);
        self::$ARV = $ARV;
        self::$CORTotalLoanAmt = $CORTotalLoanAmt;
        self::$CORefiLTVPercentage = $CORefiLTVPercentage;
        self::$LTVBasedOn = $LTVBasedOn;
        self::$InrBasedOnMonthlyPayment = $InrBasedOnMonthlyPayment;
        self::$LOCTotalLoanAmt = $LOCTotalLoanAmt;
        self::$LOCTotalLoanAmtDispOpt = $LOCTotalLoanAmtDispOpt;
        self::$LOCTotalLoanAmtHideDispOpt = $LOCTotalLoanAmtHideDispOpt;
        self::$LTC = $LTC;
        self::$LTCToolTip = $LTCToolTip;
        self::$LTCInitialLoanAmountToolTip = $LTCInitialLoanAmountToolTip;
        self::$LTCInitialLoanAmount = $LTCInitialLoanAmount;
        self::$LTCInitialLoanAmountCls = $LTCInitialLoanAmountCls;
        self::$LTCMarketValueToolTip = $LTCMarketValueToolTip;
        self::$LTCMarketValue = $LTCMarketValue;
        self::$NewLTCToolTip = $NewLTCToolTip;
        self::$NewLoanToCost = $NewLoanToCost;
        self::$NewTPCToolTip = $NewTPCToolTip;
        self::$NewTotalProjectCost = $NewTotalProjectCost;
        self::$TPCToolTip = $TPCToolTip;
        self::$URLPOSTING = $URLPOSTING;
        self::$acceptedPurchase = $acceptedPurchase;
        self::$acceptedPurchaseDispOpt = $acceptedPurchaseDispOpt;
        self::$accrualType = $accrualType;
        self::$acquisitionLTV = $acquisitionLTV;
        self::$acquisitionLTVTD = $acquisitionLTVTD;
        self::$acquisitionPriceFinanced = $acquisitionPriceFinanced;
        self::$acquisitionPriceFinancedArr = $acquisitionPriceFinancedArr;
        self::$additionalPropertyRestrictions = $additionalPropertyRestrictions;
        self::$additionalPropertyRestrictionsDispOpt = $additionalPropertyRestrictionsDispOpt;
        self::$annualPremium = $annualPremium;
        self::$assessedValue = $assessedValue;
        self::$autoCalARVToolTip = $autoCalARVToolTip;
        self::$autoCalcRead = $autoCalcRead;
        self::$autoCalcTLAARV = $autoCalcTLAARV;
        self::$autoCalcTLAARVDisp = $autoCalcTLAARVDisp;
        self::$availableBudget = $availableBudget;
        self::$blanketLoanFieldsDispOpt = $blanketLoanFieldsDispOpt;
        self::$borBorrowedDownPaymentDispOpt = $borBorrowedDownPaymentDispOpt;
        self::$brokerPointsValue = $brokerPointsValue;
        self::$broker_based_on_total_loan_amt = $broker_based_on_total_loan_amt;
        self::$budgetAndDrawsInfo = $budgetAndDrawsInfo;
        self::$cashOutAmt = $cashOutAmt;
        self::$cashOutDiv = $cashOutDiv;
        self::$cashOutFields = $cashOutFields;
        self::$cashOutRefinanceDisp = $cashOutRefinanceDisp;
        self::$cashOutRefinanceFields = $cashOutRefinanceFields;
        self::$cashOutTotalAmtTD = $cashOutTotalAmtTD;
        self::$closingCostFinanced = $closingCostFinanced;
        self::$commercialFieldsDispOpt = $commercialFieldsDispOpt;
        self::$commercialFieldsTDDispOpt = $commercialFieldsTDDispOpt;
        self::$commercialFieldsTDNDateCDispOpt = $commercialFieldsTDNDateCDispOpt;
        self::$contigencyCls = $contigencyCls;
        self::$contingencyReserve = $contingencyReserve;
        self::$percentageTotalLoan = $percentageTotalLoan;
        self::$percentageTotalLoanAmount = $percentageTotalLoanAmount;
        self::$costBasis = $costBasis;
        self::$costOfCapital = $costOfCapital;
        self::$costSpent = $costSpent;
        self::$currentLoanBalance = $currentLoanBalance;
        self::$dailyEstPerDiemArray = $dailyEstPerDiemArray;
        self::$doYouHaveInvoiceToFactor = $doYouHaveInvoiceToFactor;
        self::$doYouHaveInvoiceToFactorDispOpt = $doYouHaveInvoiceToFactorDispOpt;
        self::$doesPropertyNeedRehabDispDiv = $doesPropertyNeedRehabDispDiv;
        self::$doesPropertyNeedRehabFootageDispTDDiv = $doesPropertyNeedRehabFootageDispTDDiv;
        self::$doesPropertyNeedRehabNoofFootageDispTDDiv = $doesPropertyNeedRehabNoofFootageDispTDDiv;
        self::$doesPropertyNeedRehabSection = $doesPropertyNeedRehabSection;
        self::$downPaymentFieldDispOpt = $downPaymentFieldDispOpt;
        self::$downPaymentPercentage = $downPaymentPercentage;
        self::$downPaymentPercentageNew = $downPaymentPercentageNew;
        self::$earnestDeposit = $earnestDeposit;
        self::$exitStrategy = $exitStrategy;
        self::$exitStrategyExplainDispOpt = $exitStrategyExplainDispOpt;
        self::$extensionOptionPercentage = $extensionOptionPercentage;
        self::$extensionOptionsAmt = $extensionOptionsAmt;
        self::$feeSectionTotalLoanAmtOpt = $feeSectionTotalLoanAmtOpt;
        self::$fileHMLONewLoanInfo = $fileHMLONewLoanInfo;
        self::$fileHMLOPropertyInfo = $fileHMLOPropertyInfo;
        self::$filepaydownInfo = $filepaydownInfo;
        self::$finalLoanAmt = $finalLoanAmt;
        self::$groundUpDispTDDiv = $groundUpDispTDDiv;
        self::$haveBorSquareFootage = $haveBorSquareFootage;
        self::$haveCurrentLoanBal = $haveCurrentLoanBal;
        self::$haveCurrentLoanBalDispOpt = $haveCurrentLoanBalDispOpt;
        self::$homeValue = $homeValue;
        self::$aggregateDSCR = $aggregateDSCR;
        self::$initialLoanAmount = $initialLoanAmount;
        self::$insImpoundsFee = $insImpoundsFee;
        self::$insImpoundsMonth = Strings::replaceCommaValues($insImpoundsMonth);
        self::$insImpoundsMonthAmt = $insImpoundsMonthAmt;
        self::$intAmt = $intAmt;
        self::$interestChargedEndDate = $interestChargedEndDate;
        self::$interestChargedFromDate = $interestChargedFromDate;
        self::$interestOnInterestReserveFeeAmt = $interestOnInterestReserveFeeAmt;
        self::$isBlanketLoan = $isBlanketLoan;
        self::$isBlanketLoanDispOpt = $isBlanketLoanDispOpt;
        self::$isBlanketLoanDiv = $isBlanketLoanDiv;
        self::$isBorBorrowedDownPayment = $isBorBorrowedDownPayment;
        self::$isEFBlanketLoanDispOpt = $isEFBlanketLoanDispOpt;
        self::$isLoTxt = $isLoTxt;
        self::$isLoanPaymentAmt = $isLoanPaymentAmt;
        self::$isLoanPaymentAmtTxt = $isLoanPaymentAmtTxt;
        self::$isTaxesInsEscrowed = $isTaxesInsEscrowed;
        self::$isTaxesInsEscrowedDispOpt = $isTaxesInsEscrowedDispOpt;
        self::$isThisGroundUpConstruction = $isThisGroundUpConstruction;
        self::$lien1Payment = $lien1Payment;
        self::$lien1Rate = $lien1Rate;
        self::$lien1Terms = $lien1Terms;
        self::$lineOfCreditProp = $lineOfCreditProp;
        //   self::$totalMonthlyPaymentTooltip = $totalMonthlyPaymentTooltip;
        self::$loanTerm = $loanTerm;
        self::$marketLTV = $marketLTV;
        self::$marketLTVTD = $marketLTVTD;
        self::$marketLTVToolTip = $marketLTVToolTip;
        self::$maxAmtToPutDown = $maxAmtToPutDown;
        self::$downPaymentHtmlTooltip = 'Down Payment';
        self::$downPaymentHtmlTooltipWithValues = $maxAmtToPutDown;
        self::$maxArvPer = $maxArvPer;
        self::$maxArvPerDisp = $maxArvPerDisp;
        self::$maxLTCPer = $maxLTCPer;
        self::$maxLTCPerDisp = $maxLTCPerDisp;
        self::$myFileInfo = $myFileInfo;
        self::$netLenderFundsToBorrower = $netLenderFundsToBorrower;
        self::$netMonthlyPayment = $netMonthlyPayment;
        self::$newConDownPaymentDisp = $newConDownPaymentDisp;
        self::$noOfMonthsPrepaid = $noOfMonthsPrepaid;
        self::$originationPointsValue = $originationPointsValue;
        self::$origination_based_on_total_loan_amt = $origination_based_on_total_loan_amt;
        self::$otherDownPayment = $otherDownPayment;
        self::$payOffAmount = $payOffAmount;
        self::$payOffMortgage1 = $payOffMortgage1;
        self::$payOffMortgage2 = $payOffMortgage2;
        self::$payOffOtherOutstandingAmounts = $payOffOtherOutstandingAmounts;
        self::$payOffOutstandingTaxes = $payOffOutstandingTaxes;
        self::$paydownamount = $paydownamount;
        self::$paymentReserves = $paymentReserves;
        self::$perClosingCostFinanced = $perClosingCostFinanced;
        self::$perDiemToolTip = $perDiemToolTip;
        self::$perRehabCostFinanced = $perRehabCostFinanced;
        self::$prepaidInterestReserve = $prepaidInterestReserve;
        self::$prepaidInterestReserveForCal = $prepaidInterestReserveForCal;
        self::$propertyNeedRehab = $propertyNeedRehab;
        self::$purchaseCloseDate = $purchaseCloseDate;
        self::$purchaseTPCCls = $purchaseTPCCls;
        self::$refinanceSectionDispOpt = $refinanceSectionDispOpt;
        self::$rehabConsCls = $rehabConsCls;
        self::$rehabCost = $rehabCost;
        self::$rehabCostFinanced = $rehabCostFinanced;
        self::$rehabCostPercentageFinanced = $rehabCostPercentageFinanced;
        self::$rentalIncomePerMonthFieldDispOpt = $rentalIncomePerMonthFieldDispOpt;
        self::$requiredConstruction = $requiredConstruction;
        self::$simpleARV = $simpleARV;
        self::$subjectPropertySectionDispOpt = $subjectPropertySectionDispOpt;
        self::$tLAToolTip = $tLAToolTip;
        self::$tableCell = $tableCell;
        self::$tableRow = $tableRow;
        self::$tableRow2 = $tableRow2;
        self::$taxImpoundsFee = $taxImpoundsFee;
        self::$taxImpoundsFee = $taxImpoundsFee;
        self::$taxImpoundsMonth = $taxImpoundsMonth;
        self::$taxImpoundsMonthAmt = $taxImpoundsMonthAmt;
        self::$taxes1 = $taxes1;
        self::$tempTotalLoanAmount = $tempTotalLoanAmount;
        self::$totProjectCostLbl = $totProjectCostLbl;
        self::$totalCashOutAmt = $totalCashOutAmt;
        self::$totalCashToClose = $totalCashToClose;
        self::$totalDrawsFunded = $totalDrawsFunded;
        self::$totalLoanAmount = $totalLoanAmount;
        self::$transCommercialFieldsDispOpt = $transCommercialFieldsDispOpt;
        self::$transactionalFieldsDispOpt = $transactionalFieldsDispOpt;
        self::$typeOfHMLOLoanRequesting = $typeOfHMLOLoanRequesting;
        self::$typeOfSaleTR = $typeOfSaleTR;
        self::$yieldSpread = $yieldSpread;
        self::$LMRId = $LMRId;
        self::$totalMonthlyPayment = $totalMonthlyPayment;

        self::$lockOriginationValue = $lockOriginationValue;
        self::$lockBrokerValue = $lockBrokerValue;

        self::$paymentReservesAmt = $paymentReservesAmt;
        self::$requiredConstructionAmt = $requiredConstructionAmt;


        //
        self::$URLPOSTING = $URLPOSTING; //  false
        self::$myFileInfo = $myFileInfo; //  array:287 [▶]
        self::$insImpoundsMonth = $insImpoundsMonth; //
        self::$taxImpoundsFee = $taxImpoundsFee; //  0.00
        self::$isBorBorrowedDownPayment = $isBorBorrowedDownPayment; //  NA
        self::$propertyNeedRehab = $propertyNeedRehab; //  Yes
        self::$haveBorSquareFootage = $haveBorSquareFootage; //
        self::$additionalPropertyRestrictions = $additionalPropertyRestrictions; //
        self::$exitStrategy = $exitStrategy; //
        self::$acceptedPurchase = $acceptedPurchase; //
        self::$haveCurrentLoanBal = $haveCurrentLoanBal; //
        self::$doYouHaveInvoiceToFactor = $doYouHaveInvoiceToFactor; //
        self::$maxAmtToPutDown = $maxAmtToPutDown; //  0
        self::$closingCostFinanced = $closingCostFinanced; //  0.0
        self::$payOffMortgage1 = $payOffMortgage1; //  0.0
        self::$payOffMortgage2 = $payOffMortgage2; //  0.0
        self::$payOffOutstandingTaxes = $payOffOutstandingTaxes; //  0.0
        self::$payOffOtherOutstandingAmounts = $payOffOtherOutstandingAmounts; //  0.0
        self::$cashOutAmt = $cashOutAmt; //  0.0
        self::$filepaydownInfo = $filepaydownInfo; //  []
        self::$lien1Terms = $lien1Terms; //  Interest Only
        self::$purchaseCloseDate = $purchaseCloseDate; //  11/05/2024
        self::$taxes1 = $taxes1; //  0.0
        self::$isBlanketLoan = $isBlanketLoan; //
        self::$isThisGroundUpConstruction = $isThisGroundUpConstruction; //
        self::$interestChargedFromDate = $interestChargedFromDate; //
        self::$interestChargedEndDate = $interestChargedEndDate; //
        self::$taxImpoundsMonth = $taxImpoundsMonth; //  0
        self::$taxImpoundsMonthAmt = $taxImpoundsMonthAmt; //  0.0
        self::$insImpoundsMonthAmt = $insImpoundsMonthAmt; //  0.0
        self::$insImpoundsFee = $insImpoundsFee; //  0.00
        self::$paymentReserves = $paymentReserves; //  1.0
        self::$requiredConstruction = $requiredConstruction; //  1.0
        self::$contingencyReserve = $contingencyReserve; //  2.0
        self::$fileHMLOPropertyInfo = $fileHMLOPropertyInfo; //  array:144 [▶]
        self::$fileHMLONewLoanInfo = $fileHMLONewLoanInfo; //  array:187 [▶]
        self::$LMRId = $LMRId; //  6311140
        self::$isTaxesInsEscrowed = $isTaxesInsEscrowed; //
        self::$tableRow = $tableRow; //  display: block;
        self::$tableRow2 = $tableRow2; //  display: contents;
        self::$typeOfHMLOLoanRequesting = $typeOfHMLOLoanRequesting; //
        self::$costOfCapital = $costOfCapital; //  0.000
        self::$yieldSpread = $yieldSpread; //  0.000
        self::$rehabCostFinanced = $rehabCostFinanced; //  5,644.00
        self::$rehabCost = $rehabCost; //  1542.0
        self::$CORTotalLoanAmt = $CORTotalLoanAmt; //  0
        self::$budgetAndDrawsInfo = $budgetAndDrawsInfo; //  []
        self::$prepaidInterestReserveForCal = $prepaidInterestReserveForCal; //  0
        self::$homeValue = $homeValue; //  0.0
        self::$interestOnInterestReserveFeeAmt = $interestOnInterestReserveFeeAmt; //  0
        self::$costSpent = $costSpent; //  0.0
        self::$totalDrawsFunded = $totalDrawsFunded; //  0
        self::$currentLoanBalance = $currentLoanBalance; //  6544.00
        self::$lien1Rate = $lien1Rate; //  6.0
        self::$accrualType = $accrualType; //  360
        self::$annualPremium = $annualPremium; //  3304.00
        self::$isLoanPaymentAmtTxt = $isLoanPaymentAmtTxt; //  Total Loan Amount
        self::$originationPointsValue = $originationPointsValue; //  1254.00
        self::$brokerPointsValue = $brokerPointsValue; //  2214.00
        self::$assessedValue = $assessedValue; //  5.0
        self::$otherDownPayment = $otherDownPayment; //  0.00
        self::$earnestDeposit = $earnestDeposit; //  0.00
        self::$isLoanPaymentAmt = $isLoanPaymentAmt; //  TLA
        self::$tableCell = $tableCell; //  display: block;
        self::$autoCalcTLAARV = $autoCalcTLAARV; //  No
        self::$rehabCostPercentageFinanced = $rehabCostPercentageFinanced; //  366.0181582
        self::$costBasis = $costBasis; //  0.0
        self::$downPaymentPercentage = $downPaymentPercentage; //  0
        self::$downPaymentPercentageNew = $downPaymentPercentageNew; //  null
        self::$totalLoanAmount = $totalLoanAmount; //  22,188.00
        self::$prepaidInterestReserve = $prepaidInterestReserve; //  0.00
        self::$acquisitionPriceFinanced = $acquisitionPriceFinanced; //  16544.00
        self::$loanTerm = $loanTerm; //  12 Months
        self::$CORefiLTVPercentage = $CORefiLTVPercentage; //  0
        self::$lien1Payment = $lien1Payment; //  110.94
        self::$finalLoanAmt = $finalLoanAmt; //  0.00
        self::$noOfMonthsPrepaid = $noOfMonthsPrepaid; //  0
        self::$maxArvPer = $maxArvPer; //  0.00
        self::$maxLTCPer = $maxLTCPer; //  0.00
        self::$LOCTotalLoanAmt = $LOCTotalLoanAmt; //  0.00
        self::$origination_based_on_total_loan_amt = $origination_based_on_total_loan_amt; //  0
        self::$extensionOptionPercentage = $extensionOptionPercentage; //  0.00
        self::$broker_based_on_total_loan_amt = $broker_based_on_total_loan_amt; //  0
        self::$totalMonthlyPaymentTooltip = $totalMonthlyPaymentTooltip; //  If Amortization is <b>Interest Only</b><br>Loan Payment = (Total Loan Amount * Int. Rate) / 1200<br><br>Otherwise,<br>Loan Payment = Total Loan Amount * Int.  R ▶
        self::$autoCalcRead = $autoCalcRead; //  null
        self::$borBorrowedDownPaymentDispOpt = $borBorrowedDownPaymentDispOpt; //  display:none;
        self::$doesPropertyNeedRehabDispDiv = $doesPropertyNeedRehabDispDiv; //  display: block;
        self::$contigencyCls = $contigencyCls; //  display: block;
        self::$additionalPropertyRestrictionsDispOpt = $additionalPropertyRestrictionsDispOpt; //  display: none;
        self::$isTaxesInsEscrowedDispOpt = $isTaxesInsEscrowedDispOpt; //  display: none;
        self::$cashOutFields = $cashOutFields; //
        self::$acceptedPurchaseDispOpt = $acceptedPurchaseDispOpt; //  display: none;
        self::$haveCurrentLoanBalDispOpt = $haveCurrentLoanBalDispOpt; //  display: none;
        self::$doYouHaveInvoiceToFactorDispOpt = $doYouHaveInvoiceToFactorDispOpt; //  display: none;
        self::$groundUpDispTDDiv = $groundUpDispTDDiv; //  display: none;
        self::$cashOutRefinanceDisp = $cashOutRefinanceDisp; //  display: none;
        self::$commercialFieldsTDNDateCDispOpt = $commercialFieldsTDNDateCDispOpt; //  display: none;
        self::$exitStrategyExplainDispOpt = $exitStrategyExplainDispOpt; //  display: none;
        self::$doesPropertyNeedRehabFootageDispTDDiv = $doesPropertyNeedRehabFootageDispTDDiv; //  display: block;
        self::$doesPropertyNeedRehabNoofFootageDispTDDiv = $doesPropertyNeedRehabNoofFootageDispTDDiv; //  display: none;
        self::$rentalIncomePerMonthFieldDispOpt = $rentalIncomePerMonthFieldDispOpt; //  display: none;
        self::$maxArvPerDisp = $maxArvPerDisp; //  display: none;
        self::$maxLTCPerDisp = $maxLTCPerDisp; //  display: none;
        self::$acquisitionPriceFinancedArr = $acquisitionPriceFinancedArr; //  array:3 [▶]
        self::$acquisitionLTV = $acquisitionLTV; //  0.00
        self::$perRehabCostFinanced = $perRehabCostFinanced; //  366.02
        self::$tempTotalLoanAmount = $tempTotalLoanAmount; //  22,188.00
        self::$initialLoanAmount = $initialLoanAmount; //  16544.00
        self::$availableBudget = $availableBudget; //  5644.0
        self::$paydownamount = $paydownamount; //  0
        self::$isLoTxt = $isLoTxt; //  Current Loan Balance
        self::$totalMonthlyPayment = $totalMonthlyPayment; //  110.94
        self::$netMonthlyPayment = $netMonthlyPayment; //  386.27
        self::$LTC = $LTC; //  1,438.91
        self::$marketLTV = $marketLTV; //  0
        self::$isEFBlanketLoanDispOpt = $isEFBlanketLoanDispOpt; //  display: none;
        self::$isBlanketLoanDispOpt = $isBlanketLoanDispOpt; //  display: none;
        self::$totProjectCostLbl = $totProjectCostLbl; //  Total Project Cost
        self::$commercialFieldsDispOpt = $commercialFieldsDispOpt; //  display: block;
        self::$subjectPropertySectionDispOpt = $subjectPropertySectionDispOpt; //  display: block;
        self::$refinanceSectionDispOpt = $refinanceSectionDispOpt; //  display: none;
        self::$transactionalFieldsDispOpt = $transactionalFieldsDispOpt; //  display: none;
        self::$blanketLoanFieldsDispOpt = $blanketLoanFieldsDispOpt; //  display: none;
        self::$doesPropertyNeedRehabSection = $doesPropertyNeedRehabSection; //  display: block;
        self::$cashOutRefinanceFields = $cashOutRefinanceFields; //  display: none;
        self::$downPaymentFieldDispOpt = $downPaymentFieldDispOpt; //  display: block;
        self::$commercialFieldsTDDispOpt = $commercialFieldsTDDispOpt; //  display: block;
        self::$rehabConsCls = $rehabConsCls; //  display: none;
        self::$purchaseTPCCls = $purchaseTPCCls; //  display : none;
        self::$typeOfSaleTR = $typeOfSaleTR; //  display: block;
        self::$isBlanketLoanDiv = $isBlanketLoanDiv; //  display: block;
        self::$LOCTotalLoanAmtHideDispOpt = $LOCTotalLoanAmtHideDispOpt; //  display: block;
        self::$LOCTotalLoanAmtDispOpt = $LOCTotalLoanAmtDispOpt; //  display: none;
        self::$feeSectionTotalLoanAmtOpt = $feeSectionTotalLoanAmtOpt; //  display: block;
        self::$acquisitionLTVTD = $acquisitionLTVTD; //  display: block;
        self::$cashOutTotalAmtTD = $cashOutTotalAmtTD; //  display: none;
        self::$marketLTVTD = $marketLTVTD; //  display: block;
        self::$lineOfCreditProp = $lineOfCreditProp; //
        self::$cashOutDiv = $cashOutDiv; //  display: none;
        self::$transCommercialFieldsDispOpt = $transCommercialFieldsDispOpt; //  display: block;
        self::$newConDownPaymentDisp = $newConDownPaymentDisp; //
        self::$autoCalcTLAARVDisp = $autoCalcTLAARVDisp; //  display : none;
        self::$autoCalARVToolTip = $autoCalARVToolTip; //
        self::$perDiemToolTip = $perDiemToolTip; //  Per Diem Interest = (Interest Rate / 100 /360) * Total Loan Amount  = 3.698
        self::$dailyEstPerDiemArray = $dailyEstPerDiemArray; //  array:2 [▶]
        self::$perClosingCostFinanced = $perClosingCostFinanced; //  0.00
        self::$ARV = $ARV; //  443,760.00
        self::$totalCashToClose = $totalCashToClose; //  8,922.00
        self::$NewTPCToolTip = $NewTPCToolTip; //  Total project soft & hard cost =  (Purchase price + Rehab/const cost + Total Fees & Costs) <hr> Note: Do not include Accrued Per Diem Interest
        self::$NewLTCToolTip = $NewLTCToolTip; //  LTC w/Soft & Hard Cost=  Total Loan Amount / (Purchase price + Rehab/const cost + Closing Costs not Financed) * 100
        self::$TPCToolTip = $TPCToolTip; //  Total Project Cost = (Acquisition / Purchase Price + Rehab/Construction Cost + Cost Spent)
        self::$LTCToolTip = $LTCToolTip; //  Loan-to-Cost (LTC) =  (Total Loan Amount / Project Cost) * 100<hr>Loan-to-Cost Ratio (LTC) A ratio used in commercial real estate construction to compare the am ▶
        self::$marketLTVToolTip = $marketLTVToolTip; //  Market LTV = (Initial Loan Amount / Assessed As-is Value) * 100<hr>This is a percentage of your loan to values based off the market value,  not the purchase pri ▶
        self::$intAmt = $intAmt; //  0
        self::$tLAToolTip = $tLAToolTip; //  Total Loan Amount = (Initial Loan Amount + Rehab/Construction Cost Financed + Closing Costs Financed + Pre-paid Interest Reserve)
        self::$netLenderFundsToBorrower = $netLenderFundsToBorrower; //  7,622.00
        self::$extensionOptionsAmt = $extensionOptionsAmt; //  0
        self::$paymentReservesAmt = $paymentReservesAmt; //  110.94
        self::$requiredConstructionAmt = $requiredConstructionAmt; //  -41.02
        self::$totalRequiredReserves = $totalRequiredReserves; //  100.76
        self::$totalCashOutAmt = $totalCashOutAmt; //  0.00
        self::$simpleARV = $simpleARV; //  374,400.00
        self::$payOffAmount = $payOffAmount; //  6544.0
        self::$InrBasedOnMonthlyPayment = $InrBasedOnMonthlyPayment; //  -318.68
        self::$NewTotalProjectCost = $NewTotalProjectCost; //  10464.0
        self::$NewLoanToCost = $NewLoanToCost; //  212.04128440367

        ////////
        ///

        self::$sARVIP = $sARVIP; //  array:5 [▶]
        self::$paymentduedate = $paymentduedate; //
        self::$payOffDate = $payOffDate; //  10/09/2023
        self::$amountdue = $amountdue; //
        self::$payOffAmountArray = $payOffAmountArray; //  array:6 [▶]
        self::$pos = $pos; //  2
        self::$loanTerms = $loanTerms; //  12
        self::$nper = $nper; //  12
        self::$pmt = $pmt; //  -110.94
        self::$pv = $pv; //  16544.0
        self::$totalFeesAndCostNew = $totalFeesAndCostNew; //  8922.0
        self::$NewLTCCal = $NewLTCCal; //  10464.0
        self::$haveInterestreserve = $haveInterestreserve; //  No
        self::$doesPropertyNeedRehabDispTDDiv = $doesPropertyNeedRehabDispTDDiv; //  display: block;
        self::$exitStrategyParentValues = $exitStrategyParentValues; //  array:4 [▶]
        self::$totalProjectCost = $totalProjectCost; //  1,542.00
        self::$paycount = $paycount; //  0
        self::$CLBInArray = $CLBInArray; //  array:5 [▶]
        self::$commercialFieldsTDNCDispOpt = $commercialFieldsTDNCDispOpt; //
        self::$diemDays = $diemDays; //  0
        self::$PerDiemTotalAmt = $PerDiemTotalAmt; //  22188.0
        self::$totalDailyInterestCharge = $totalDailyInterestCharge; //  3.698
        self::$interestDays = $interestDays; //  360
        self::$totalEstPerDiem = $totalEstPerDiem; //  0.0
        self::$totalFeesAndCost = $totalFeesAndCost; //  8,922.00
        self::$closingCostNotFinanced = $closingCostNotFinanced; //  8,922.00
        self::$netLenderFundsinputArray = $netLenderFundsinputArray; //  array:5 [▶]
        self::$taxImpoundsInArray = $taxImpoundsInArray; //  array:2 [▶]
        self::$insImpoundsInArray = $insImpoundsInArray; //  array:2 [▶]
        self::$extensionOptionsAmtInputArray = $extensionOptionsAmtInputArray; //  array:2 [▶]
        self::$contingencyReserveAmt = $contingencyReserveAmt; //  30.84
        self::$LTVPercentageDisp = $LTVPercentageDisp; //
        self::$totalCashOutDisp = $totalCashOutDisp; //
        self::$NewTotalProjectCostDisp = $NewTotalProjectCostDisp; //
        self::$grossProfit = $grossProfit; //  0.0
        self::$grossProfitMargin = $grossProfitMargin; //  0.0
        self::$brokerProcessingFee = $brokerProcessingFee; //  0.0

    }


    /**
     * @param $Value
     * @param $totalLoanAmount
     * @param $closingCostFinanced
     * @param $based_on_total_loan_amt
     * @return float
     */
    public static function setOriginationBrokerPoints($Value, $totalLoanAmount, $closingCostFinanced, $based_on_total_loan_amt): float
    {

        $pointsRate = 0;
        if ($based_on_total_loan_amt) {
            if ($totalLoanAmount > 0) {
                $pointsRate = $Value / $totalLoanAmount * 100;
                $pointsRate = round($pointsRate, 6);
            }
        } else {
            if ($totalLoanAmount - $closingCostFinanced > 0) {
                $pointsRate = $Value / ($totalLoanAmount - $closingCostFinanced) * 100;
                $pointsRate = round($pointsRate, 6);
            }
        }
        return $pointsRate;
    }


    /**
     * @param $points
     * @param $totalLoanAmount
     * @param $closingCostFinanced
     * @param $based_on_total_loan_amt
     * @return float
     */
    public static function setOriginationBrokerValue($points, $totalLoanAmount, $closingCostFinanced, $based_on_total_loan_amt): float
    {

        if ($based_on_total_loan_amt) {
            $value = ($points * $totalLoanAmount) / 100;
        } else {
            $value = $points * ($totalLoanAmount - $closingCostFinanced) / 100;
        }
        return round($value, 2);
    }

    public static function isTransactionTypeRefinance($typeOfHMLOLoanRequesting): bool
    {
        return in_array($typeOfHMLOLoanRequesting, typeOfHMLOLoanRequesting::REFINANCE_LIST);
    }

    public static function isTransactionTypePurchase($typeOfHMLOLoanRequesting): bool
    {
        return $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::PURCHASE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::COMMERCIAL_PURCHASE
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::TRANSACTIONAL
            || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::LINE_OF_CREDIT;
    }

    public static function isTransactionTypeNewConstruction($typeOfHMLOLoanRequesting): bool
    {
        return $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::NEW_CONSTRUCTION_EXISTING_LAND;
    }

    public static function cv3PointsCalculation($originationPointsRate, $cv3ReferralPoint): array
    {
        $originationPointsRate = Strings::replaceCommaValues($originationPointsRate);
        $cv3ReferralPoint = Strings::replaceCommaValues($cv3ReferralPoint);
        if ($cv3ReferralPoint) {
            $cv3OriginationPoint = $originationPointsRate - $cv3ReferralPoint;
        } else {
            $cv3OriginationPoint = $originationPointsRate;
            $cv3ReferralPoint = 0;
        }

        return [
            'cv3OriginationPoint' => $cv3OriginationPoint,
            'cv3ReferralPoint'    => $cv3ReferralPoint,
        ];
    }

    public static function cv3AmountCalculation($originationPointsValue, $cv3OriginationAmount, $cv3ReferralAmount): array
    {
        $originationPointsValue = Strings::replaceCommaValues($originationPointsValue);
        // $cv3OriginationAmount = Strings::replaceCommaValues($cv3OriginationAmount); // why is this even passed in?
        $cv3ReferralAmount = Strings::replaceCommaValues($cv3ReferralAmount);

        if ($cv3ReferralAmount) {
            $cv3OriginationAmount = $originationPointsValue - $cv3ReferralAmount;
        } else {
            $cv3OriginationAmount = $originationPointsValue;
        }

        return [
            'cv3OriginationAmount' => $cv3OriginationAmount,
            'cv3ReferralAmount'    => $cv3ReferralAmount,
        ];

    }

    /**
     * @param $totalLoanAmount
     * @param $extensionOptionPercentage
     * @return float
     */
    public static function calculatePercentageExtensionOption($totalLoanAmount, $extensionOptionPercentage): float
    {
        $extensionOptionPercentage = Strings::replaceCommaValues($extensionOptionPercentage);
        $totalLoanAmount = Strings::replaceCommaValues($totalLoanAmount);
        return round($totalLoanAmount * $extensionOptionPercentage / 100, 2);
    }

    /**
     * @param $haveInterestReserve
     * @param $paymentReserves
     * @param $lessPrePayInterestReserve
     * @return int|mixed
     */
    public static function calculateTotalInterestPaymentReserveRequired($haveInterestReserve, $paymentReserves, $lessPrePayInterestReserve, $RLRSecArr = [])
    {
        if (!BaseHTML::fieldAccess(['fNm' => 'paymentReserves', 'sArr' => $RLRSecArr, 'opt' => 'D', 'tf' => 1])) {
            $paymentReserves = 0;
        }
        if (!BaseHTML::fieldAccess(['fNm' => 'lessPrepaidInterestReserve', 'sArr' => $RLRSecArr, 'opt' => 'D', 'tf' => 1])) {
            $lessPrePayInterestReserve = 0;
        }

        $totalInterestPaymentReserveRequired = ($haveInterestReserve == 'Yes' && $paymentReserves > 0) ? max($paymentReserves - $lessPrePayInterestReserve, 0) : $paymentReserves;
        self::$totalInterestPaymentReserveRequiredToolTipWithValues = "($paymentReserves - $lessPrePayInterestReserve) = $totalInterestPaymentReserveRequired";
        return $totalInterestPaymentReserveRequired;
    }

    //LTC2 --custom Code
    public static bool $isAutoCalcTotalLoanAmountBasedOnLTC2 = false;
    public static ?float $baseAmountForLTC2 = null;

    public static function isAutoCalcTotalLoanAmountBasedOnLTC2($autoCalcTLAARV): void
    {
        self::$isAutoCalcTotalLoanAmountBasedOnLTC2 = ($autoCalcTLAARV == 'LTC2');
    }

    public static function isTransactionTypePurchaseCategory($typeOfHMLOLoanRequesting): bool
    {
        $purchaseTypes = [
            typeOfHMLOLoanRequesting::PURCHASE,
            typeOfHMLOLoanRequesting::DELAYED_PURCHASE,
            typeOfHMLOLoanRequesting::COMMERCIAL_PURCHASE,
            typeOfHMLOLoanRequesting::LINE_OF_CREDIT,
            typeOfHMLOLoanRequesting::TRANSACTIONAL,
            typeOfHMLOLoanRequesting::NEW_CONSTRUCTION_EXISTING_LAND,
        ];
        return in_array($typeOfHMLOLoanRequesting, $purchaseTypes, true);
    }

    public static function isTransactionTypeRefinanceCategory($typeOfHMLOLoanRequesting): bool
    {
        $refinanceTypes = [
            typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE,
            typeOfHMLOLoanRequesting::REFINANCE,
            typeOfHMLOLoanRequesting::RATE_TERM_REFINANCE,
            typeOfHMLOLoanRequesting::COMMERCIAL_CASH_OUT_REFINANCE,
            typeOfHMLOLoanRequesting::COMMERCIAL_RATE_TERM_REFINANCE,
        ];
        return in_array($typeOfHMLOLoanRequesting, $refinanceTypes, true);
    }

    public static function calculateBaseAmountForLTC2(
        $typeOfHMLOLoanRequesting,
        $autoCalcTLAARV,
        $rehabCost,
        $homeValue,
        $costBasis,
        $escrowFees
    ): ?array
    {
        $totalProjectCost = 0;
        $TPCFormula = '';

        if ($autoCalcTLAARV === 'LTC2') {
            $TPCToolTipWithValues = '';

            // Get field values
            //$rehabCost = getFieldValue('rehabCost'); // Rehab Budget
            //$assessedValue = getFieldValue('homeValue'); // Property value AS-IS
            //$costBasis = getFieldValue('costBasis'); // Purchase Price
            //$escrowFees = getFieldValue('escrowFees'); // Estimated Settlement or Closing/Escrow Fee

            // Check transaction type
            if (self::isTransactionTypePurchaseCategory($typeOfHMLOLoanRequesting)) {
                $totalProjectCost = $costBasis + $rehabCost + $escrowFees;
                $TPCFormula = "Total Project Value = Purchase Price " .
                    "+ Rehab/Construction Cost " .
                    "+ Estimated Settlement or Closing/Escrow Fee 111";
                $TPCToolTipWithValues = "$costBasis + $rehabCost + $escrowFees";
            } elseif (self::isTransactionTypeRefinanceCategory($typeOfHMLOLoanRequesting)) {
                $totalProjectCost = $homeValue + $rehabCost + $escrowFees;
                $TPCFormula = "Total Project Value = Property Value (As-Is) " .
                    "+ Rehab/Construction Cost " .
                    "+ Estimated Settlement or Closing/Escrow Fee ";
                $TPCToolTipWithValues = "$homeValue + $rehabCost + $escrowFees";
            }
            HMLOLoanTermsCalculation::$TPCToolTip = $TPCFormula;
            HMLOLoanTermsCalculation::$TPCToolTipWithValues = $TPCFormulaWithValues = "$TPCToolTipWithValues = $" . number_format($totalProjectCost, 2);

            self::$baseAmountForLTC2 = $totalProjectCost;

            return [
                'projectCost'          => $totalProjectCost,
                'TPCFormula'           => $TPCFormula,
                'TPCToolTipWithValues' => $TPCFormulaWithValues,
            ];
        }
        return null;
    }

    public static ?float $downPaymentForLTC2 = null;

    public static function calculateDownPaymentForLTC2($downPaymentPercentage): void
    {
        self::$downPaymentForLTC2 = $downPaymentPercentage ? (self::$baseAmountForLTC2 * $downPaymentPercentage / 100) : 0;
    }


    public static function calculateBaseLoanAmountForLTC2(): void
    {

        //if (self::isTransactionTypePurchaseCategory(LTC2Variables::$typeOfHMLOLoanRequesting)) {
        LTC2Variables::$LTC2_baseLoanAmountTooltip =
            "((LTC% * (Purchase Price + Rehab Budget + Estimated Settlement or Closing/Escrow Fee)) +  (Closing Cost Financed + Prepaid Interest Reserve (if financed)))";

        LTC2Variables::$LTC2_baseLoanAmount = (LTC2Variables::$LTC2Percentage / 100 * (LTC2Variables::$costBasis + LTC2Variables::$rehabCost + LTC2Variables::$escrowFees)) + (LTC2Variables::$closingCostFinanced + LTC2Variables::$prepaidInterestReserveFinanced);

        LTC2Variables::$LTC2_baseLoanAmountFormatted = round(LTC2Variables::$LTC2_baseLoanAmount, 2);

        LTC2Variables::$LTC2_baseLoanAmountTooltipWithValues =
            "(" . LTC2Variables::$LTC2Percentage . " / 100 * (" . LTC2Variables::$costBasis . " + " . LTC2Variables::$rehabCost . " + " . LTC2Variables::$escrowFees . ")) + (" . LTC2Variables::$closingCostFinanced . " + " . LTC2Variables::$prepaidInterestReserveFinanced . ")";
        //}
    }

    public static function calculateAdditionalReserveInterestForLTC2(): void
    {

        if (self::isTransactionTypeRefinanceCategory(LTC2Variables::$typeOfHMLOLoanRequesting)) {
            return;
        }
        LTC2Variables::$LTC2_additionalReserveInterestTooltip =
            '<b> IF (Pre-paid Interest reserve is YES) </b> <br> Interest Rate / 1200 *  ((LTC%*(Purchase Price + Rehab Budget + Estimated Settlement or Closing/Escrow Fee) + (Closing Cost Financed + Prepaid Interest Reserve IF Financed)) * noOfMonthsPrepaid) - Prepaid Interest Reserve if Financed  <br> <b>  Else  </b> <br> 0 ';

        LTC2Variables::$LTC2_additionalReserveInterest = LTC2Variables::$haveInterestreserve === 'Yes' ? (round((LTC2Variables::$lien1Rate / 12 / 100)
            * ((LTC2Variables::$LTC2_baseLoanAmount * LTC2Variables::$noOfMonthsPrepaid))
            - LTC2Variables::$prepaidInterestReserveFinanced, 2)) : 0;

        // LTC2Variables::$LTC2_additionalReserveInterestFormatted = autoNumericConverter(self::LTC2_baseLoanAmount(2));

        LTC2Variables::$LTC2_additionalReserveInterestTooltipWithValues =
            LTC2Variables::$haveInterestreserve === 'Yes' ? "(" . LTC2Variables::$lien1Rate . " / 12 / 100) * ((" . LTC2Variables::$LTC2_baseLoanAmountTooltipWithValues . ") * " . LTC2Variables::$noOfMonthsPrepaid . ") - " . LTC2Variables::$prepaidInterestReserveFinanced . '<hr>' . LTC2Variables::$LTC2_additionalReserveInterest : '0';

    }

    public static function calculateAdditionalOriginationInterestForLTC2(): void
    {

        if (self::isTransactionTypeRefinanceCategory(LTC2Variables::$typeOfHMLOLoanRequesting)) {
            return;
        }
        LTC2Variables::$LTC2_additionalOriginationInterestTooltip =
            '(Origination Point % + Broker Point % as decimals) * ((LTC%*(Purchase Price + Rehab Budget + Estimated Settlement or Closing/Escrow Fee)) + (Closing Cost Financed + Prepaid Interest Reserve(if financed))) - (Origination Fee + Broker Fee)';

        LTC2Variables::$LTC2_additionalOriginationInterest = round((LTC2Variables::$originationPointsRate / 100 + LTC2Variables::$brokerPointsRate / 100)
            * (LTC2Variables::$LTC2_baseLoanAmount)
            - (LTC2Variables::$originationPointsValue + LTC2Variables::$brokerPointsValue), 2);

        // LTC2Variables::$LTC2_additionalReserveInterestFormatted = autoNumericConverter(self::LTC2_baseLoanAmount(2));

        LTC2Variables::$LTC2_additionalOriginationInterestTooltipWithValues =
            "(" . LTC2Variables::$originationPointsRate . " / 100 + " . LTC2Variables::$brokerPointsRate . " / 100) * (" . LTC2Variables::$LTC2_baseLoanAmountTooltipWithValues . ") - (" . LTC2Variables::$originationPointsValue . " + " . LTC2Variables::$brokerPointsValue . ")" . "<hr>" . LTC2Variables::$LTC2_additionalOriginationInterest;
    }


    public static ?float $totalLoanAmountForLTC2 = null;
    public static ?string $hideFieldsForLTC2 = null;
    public static ?string $showDivForLTC2 = null;
    public static ?string $enableFieldsForLTC2 = null;
    public static ?string $hideFieldsForLTC2_RefinanceCategory = null;
    public static ?string $readyOnlyFieldsForLTC2_RefinanceCategory = null;

    public static function calculateTotalLoanAmountForLTC2($typeOfHMLOLoanRequesting,
                                                           $LTC2Percentage,
                                                           $costBasis,
                                                           $homeValue,
                                                           $rehabCost,
                                                           $escrowFees,
                                                           $closingCostFinanced,
                                                           $prepaidInterestReserve,
                                                           $prepaidInterestReserveFinanced,
                                                           $originationPointsRate,
                                                           $brokerPointsRate,
                                                           $lien1Rate,
                                                           $noOfMonthsPrepaid,
                                                           $haveInterestreserve,
                                                           $originationPointsValue,
                                                           $brokerPointsValue
    ): void
    {

        if (self::isTransactionTypePurchaseCategory($typeOfHMLOLoanRequesting)) {

            HMLOLoanTermsCalculation::$tLAToolTip = LTC2Variables::$LTC2_baseLoanAmountTooltip . ' + Additional Reserve Interest + Additional Origination Interest';

            self::$totalLoanAmountForLTC2 = LTC2Variables::$LTC2_baseLoanAmount
                + LTC2Variables::$LTC2_additionalReserveInterest
                + LTC2Variables::$LTC2_additionalOriginationInterest;

            HMLOLoanTermsCalculation::$tLAToolTipWithValues = LTC2Variables::$LTC2_baseLoanAmountTooltipWithValues
                . " + " . LTC2Variables::$LTC2_additionalReserveInterest
                . " + " . LTC2Variables::$LTC2_additionalOriginationInterest
                . " <hr> " . round(self::$totalLoanAmountForLTC2, 2);


        } elseif (self::isTransactionTypeRefinanceCategory($typeOfHMLOLoanRequesting)) {

            HMLOLoanTermsCalculation::$tLAToolTip = '(LTC% * (Property As-Is Value + Rehab Budget + Estimated Settlement or Closing/Escrow Fee)) + (Closing Costs Financed + Prepaid Interest Reserve(if financed)) ';

            self::$totalLoanAmountForLTC2 =
                ($LTC2Percentage / 100 * ($homeValue + $rehabCost + $escrowFees)) +
                ($closingCostFinanced + $prepaidInterestReserveFinanced);

            HMLOLoanTermsCalculation::$tLAToolTipWithValues = " ($LTC2Percentage / 100 * ($homeValue + $rehabCost + $escrowFees)) +
                ($closingCostFinanced + $prepaidInterestReserveFinanced) " .
                " <hr> " . round(self::$totalLoanAmountForLTC2, 2);


        }
    }

    public static function initLTC2($typeOfHMLOLoanRequesting,
                                    $autoCalcTLAARV,
                                    $rehabCost,
                                    $homeValue,
                                    $costBasis,
                                    $escrowFees,
                                    $downPaymentPercentage,
                                    $LTC2Percentage,
                                    $closingCostFinanced,
                                    $prepaidInterestReserve,
                                    $originationPointsValue,
                                    $brokerPointsValue,
                                    $prepaidInterestReserveFinanced,
                                    $prepaidInterestReserveNotFinanced,
                                    $payOffMortgage1,
                                    $payOffMortgage2,
                                    $payOffOutstandingTaxes,
                                    $originationPointsRate,
                                    $brokerPointsRate,
                                    $lien1Rate,
                                    $noOfMonthsPrepaid,
                                    $haveInterestreserve
    ): void
    {

        self::isAutoCalcTotalLoanAmountBasedOnLTC2($autoCalcTLAARV);
        if (self::$isAutoCalcTotalLoanAmountBasedOnLTC2) {

            LTC2Variables::$typeOfHMLOLoanRequesting = $typeOfHMLOLoanRequesting;
            LTC2Variables::$autoCalcTLAARV = $autoCalcTLAARV;
            LTC2Variables::$rehabCost = $rehabCost;
            LTC2Variables::$homeValue = $homeValue;
            LTC2Variables::$costBasis = $costBasis;
            LTC2Variables::$escrowFees = $escrowFees;
            LTC2Variables::$downPaymentPercentage = $downPaymentPercentage;
            LTC2Variables::$LTC2Percentage = $LTC2Percentage;
            LTC2Variables::$closingCostFinanced = $closingCostFinanced;
            LTC2Variables::$prepaidInterestReserve = $prepaidInterestReserve;
            LTC2Variables::$originationPointsValue = $originationPointsValue;
            LTC2Variables::$brokerPointsValue = $brokerPointsValue;
            LTC2Variables::$prepaidInterestReserveFinanced = $prepaidInterestReserveFinanced;
            LTC2Variables::$prepaidInterestReserveNotFinanced = $prepaidInterestReserveNotFinanced;
            LTC2Variables::$payOffMortgage1 = $payOffMortgage1;
            LTC2Variables::$payOffMortgage2 = $payOffMortgage2;
            LTC2Variables::$payOffOutstandingTaxes = $payOffOutstandingTaxes;
            LTC2Variables::$originationPointsRate = $originationPointsRate;
            LTC2Variables::$brokerPointsRate = $brokerPointsRate;
            LTC2Variables::$lien1Rate = $lien1Rate;
            LTC2Variables::$noOfMonthsPrepaid = $noOfMonthsPrepaid;
            LTC2Variables::$haveInterestreserve = $haveInterestreserve;

            LTC2Variables::$readOnlyForLTC2Fields = ' readonly ';

            self::calculateBaseAmountForLTC2(
                $typeOfHMLOLoanRequesting,
                $autoCalcTLAARV,
                $rehabCost,
                $homeValue,
                $costBasis,
                $escrowFees
            );
            self::calculateDownPaymentForLTC2($downPaymentPercentage);

            self::calculateBaseLoanAmountForLTC2();
            self::calculateAdditionalReserveInterestForLTC2();
            self::calculateAdditionalOriginationInterestForLTC2();
            self::calculateTotalLoanAmountForLTC2($typeOfHMLOLoanRequesting,
                $LTC2Percentage,
                $costBasis,
                $homeValue,
                $rehabCost,
                $escrowFees,
                $closingCostFinanced,
                $prepaidInterestReserve,
                $prepaidInterestReserveFinanced,
                $originationPointsRate,
                $brokerPointsRate,
                $lien1Rate,
                $noOfMonthsPrepaid,
                $haveInterestreserve,
                $originationPointsValue,
                $brokerPointsValue
            );
            self::calculateTotalCashToCloseForLTC2($typeOfHMLOLoanRequesting,
                $originationPointsValue,
                $brokerPointsValue,
                $prepaidInterestReserve,
                $prepaidInterestReserveFinanced,
                $prepaidInterestReserveNotFinanced,
                $payOffMortgage1,
                $payOffMortgage2,
                $payOffOutstandingTaxes,
                $closingCostFinanced,
                $escrowFees,
                $rehabCost);
            self::calculateTotalCashOutForLTC2($typeOfHMLOLoanRequesting,
                $originationPointsValue,
                $brokerPointsValue,
                $prepaidInterestReserve,
                $payOffMortgage1,
                $payOffMortgage2,
                $payOffOutstandingTaxes,
                $escrowFees,
                $rehabCost,
                $LTC2Percentage,
                $homeValue
            );
        }
    }

    public static ?float $totalCashToCloseLTC2 = null;

    /**
     * @param $typeOfHMLOLoanRequesting
     * @param $originationPointsValue
     * @param $brokerPointsValue
     * @param $prepaidInterestReserve
     * @param $prepaidInterestReserveFinanced
     * @param $prepaidInterestReserveNotFinanced
     * @param $payOffMortgage1
     * @param $payOffMortgage2
     * @param $payOffOutstandingTaxes
     * @param $closingCostFinanced
     * @param $escrowFees
     * @param $rehabCost
     * @return void
     */
    public static function calculateTotalCashToCloseForLTC2($typeOfHMLOLoanRequesting,
                                                            $originationPointsValue,
                                                            $brokerPointsValue,
                                                            $prepaidInterestReserve,
                                                            $prepaidInterestReserveFinanced,
                                                            $prepaidInterestReserveNotFinanced,
                                                            $payOffMortgage1,
                                                            $payOffMortgage2,
                                                            $payOffOutstandingTaxes,
                                                            $closingCostFinanced,
                                                            $escrowFees,
                                                            $rehabCost

    ): void
    {

        if (self::isTransactionTypePurchaseCategory($typeOfHMLOLoanRequesting)) {

            self::$totalCashToCloseLTC2 = self::$downPaymentForLTC2
                + $originationPointsValue
                + $brokerPointsValue
                + $prepaidInterestReserveNotFinanced
                - $closingCostFinanced//- $prepaidInterestReserveFinanced
            ;
            self::$totalCashToCloseTooltip = " Down Payment + Origination Points + Broker Points + Prepaid Interest Reserve (if not financed) - Closing Costs Financed ";
            self::$totalCashToCloseTooltipWithValues = self::$downPaymentForLTC2 . " + $originationPointsValue + $brokerPointsValue + $prepaidInterestReserveNotFinanced - $closingCostFinanced <hr> " . self::$totalCashToCloseLTC2;
        }
    }

    public static ?float $totalCashOutForLTC2 = null;

    /**
     * @param $typeOfHMLOLoanRequesting
     * @param $originationPointsValue
     * @param $brokerPointsValue
     * @param $prepaidInterestReserve
     * @param $payOffMortgage1
     * @param $payOffMortgage2
     * @param $payOffOutstandingTaxes
     * @param $escrowFees
     * @param $rehabCost
     * @return void
     */
    public static function calculateTotalCashOutForLTC2($typeOfHMLOLoanRequesting,
                                                        $originationPointsValue,
                                                        $brokerPointsValue,
                                                        $prepaidInterestReserve,
                                                        $payOffMortgage1,
                                                        $payOffMortgage2,
                                                        $payOffOutstandingTaxes,
                                                        $escrowFees,
                                                        $rehabCost,
                                                        $LTC2Percentage,
                                                        $homeValue

    ): void
    {

        if (self::isTransactionTypeRefinanceCategory($typeOfHMLOLoanRequesting)) {

            /*       self::$totalCashOutForLTC2 = self::$totalLoanAmountForLTC2
                       - $payOffMortgage1
                       - $payOffMortgage2
                       - $payOffOutstandingTaxes
                       - $escrowFees
                       - $originationPointsValue
                       - $brokerPointsValue
                       - $rehabCost
                       - $prepaidInterestReserve
                   ;*/

            self::$totalCashOutForLTC2 =
                ($LTC2Percentage / 100 * ($homeValue + $rehabCost + $escrowFees))
                - $payOffMortgage1
                - $payOffMortgage2
                - $payOffOutstandingTaxes
                - $escrowFees
                - $brokerPointsValue
                - $originationPointsValue
                - $rehabCost
                - $prepaidInterestReserve;
            self::$totalCashToCloseLTC2 = self::$totalCashOutForLTC2 < 0 ? abs(self::$totalCashOutForLTC2) : -self::$totalCashOutForLTC2;

            self::$totalCashOutToolTip = '((LTC%*(As-Is Value + Rehab Budget + Estimated Settlement or Closing/Escrow Fee)) - Pay-Off On Mortgage 1 - Pay-Off on Mortgage 2 - Pay-Off Outstanding Taxes - Estimated Settlement or Closing/Escrow Fee - Broker Points - Origination Points - Rehab Cost - Prepaid interest Reserve)';

            self::$totalCashOutToolTipWithValues = "($LTC2Percentage / 100 * ($homeValue + $rehabCost + $escrowFees))
                - $payOffMortgage1
                - $payOffMortgage2
                - $payOffOutstandingTaxes
                - $escrowFees
                - $brokerPointsValue
                - $originationPointsValue
                - $rehabCost
                - $prepaidInterestReserve <hr>  " . self::$totalCashOutForLTC2;

        }
    }

    public static bool $isFileSPREO = false;

    public static function isFileSPREO(?int $PCID): void
    {
        self::$isFileSPREO = ($PCID === glPCID::PCID_SPREO_CAPITAL);
    }

}
