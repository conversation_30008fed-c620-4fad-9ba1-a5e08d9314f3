<?php

namespace models\loan_math;

use models\constants\accrualTypes;
use models\servicing\LoanTerms;
use models\standard\Dates;
use models\types\strongType;

class per_diem extends strongType
{
    public ?string $diemDays = null;
    public ?float $totalEstPerDiem = null;
    public ?float $totalDailyInterestCharge = null;
    public ?array $monthsDays = null;
    public ?string $interestChargedFromDate = null;
    public ?string $interestChargedEndDate = null;
    public ?string $accrualType = null;
    public ?string $perDiemAccrualType = null;
    public ?string $LoanTerms = null;
    public ?float $currentLoanBalance = null;
    public ?float $totalLoanBalance = null;
    public ?float $interestRate = null;
    public ?bool $InclusivePerDiem = null;
    public ?float $perDiemLoanAmount = null;

    public static function getDailyInterest(
        ?string $accrualType,
        ?float  $interest,
        ?float  $totalLoanAmount
    ): float
    {
        $interest /= 100;

        switch ($accrualType) {
            case accrualTypes::ACCRUAL_TYPE_30_360:
            case accrualTypes::ACCRUAL_TYPE_ACTUAL_360:
                $interest /= 360;
                return $totalLoanAmount * $interest;

            case accrualTypes::ACCRUAL_TYPE_ACTUAL_365:
            case accrualTypes::ACCRUAL_TYPE_30_365:
                $interest /= 365;
                return $totalLoanAmount * $interest;
        }
        return 0.0;
    }

    public static function dailyPerDiemInterest(
        ?float  $perDiemLoanAmount,
        ?float  $lien1Rate,
        ?string $perDiemAccrualType,
        ?string $accrualType
    ): float
    {
        return self::getDailyInterest(
            $perDiemAccrualType ?: $accrualType,
            $lien1Rate,
            $perDiemLoanAmount
        );
    }

    public static function perDiemLoanAmount(
        ?string $LoanTerms,
        ?float  $currentLoanBalance,
        ?float  $totalLoanAmount
    )
    {
        if ($LoanTerms === LoanTerms::ILA) {
            return $currentLoanBalance;
        }

        if ($LoanTerms === LoanTerms::TLA) {
            return $totalLoanAmount;
        }

        return 0;
    }

    public static function perDiemLoanAmountSource(
        string $LoanTerms
    ): string
    {
        if ($LoanTerms == LoanTerms::ILA) {
            return 'Current Loan Balance';
        }

        if ($LoanTerms == LoanTerms::TLA) {
            return 'Total Loan Amount';
        }

        return '';
    }

    public static function calc(
        ?string $interestChargedFromDate,
        ?string $interestChargedEndDate,
        ?string $accrualType,
        ?string $perDiemAccrualType,
        ?string $LoanTerms,
        ?float  $currentLoanBalance,
        ?float  $totalLoanBalance,
        ?float  $interestRate,
        ?bool   $InclusivePerDiem
    ): per_diem
    {

        $perDiemLoanAmount = self::perDiemLoanAmount(
            $LoanTerms,
            $currentLoanBalance,
            $totalLoanBalance
        );

        $totalDailyInterestCharge = self::dailyPerDiemInterest(
            $perDiemLoanAmount,
            $interestRate,
            $accrualType, // loans can have different accrual types for per diem than for the payment
            $perDiemAccrualType,
        );

        if ($perDiemAccrualType) {
            $accrualType = $perDiemAccrualType;
        }


        $res = new self();
        $res->perDiemLoanAmount = $perDiemLoanAmount;
        $res->totalDailyInterestCharge = $totalDailyInterestCharge;
        $res->interestChargedFromDate = $interestChargedFromDate;
        $res->interestChargedEndDate = $interestChargedEndDate;
        $res->accrualType = $accrualType;
        $res->perDiemAccrualType = $perDiemAccrualType;
        $res->LoanTerms = $LoanTerms;
        $res->currentLoanBalance = $currentLoanBalance;
        $res->totalLoanBalance = $totalLoanBalance;
        $res->interestRate = $interestRate;
        $res->InclusivePerDiem = $InclusivePerDiem;

        /* Diem Days */
        if (!$interestChargedFromDate || !$interestChargedEndDate) {

            $res->diemDays = '';
            $res->totalEstPerDiem = 0;

            return $res;
        }

        $monthsDays = Dates::GetMonthsDays(
            $interestChargedFromDate,
            $interestChargedEndDate,
            $InclusivePerDiem
        );

        // console.log({
        //     func: 'dateDiffDiemDays',
        //     interestChargedFromDate: interestChargedFromDate,
        //     interestChargedEndDate: interestChargedEndDate,
        //     totalDailyInterestCharge: totalDailyInterestCharge,
        //     accrualType: accrualType,
        //     monthsDays: monthsDays,
        // });


        $diemDays = null;
        $totalEstPerDiem = null;

        switch ($accrualType) {
            case accrualTypes::ACCRUAL_TYPE_ACTUAL_365: // 365 is also actual
            case accrualTypes::ACCRUAL_TYPE_ACTUAL_360:
                $diemDays = round($monthsDays['actualDays']) . ' Day' . ($monthsDays['actualDays'] != 1 ? 's' : '');
                $totalEstPerDiem = $monthsDays['actualDays'] * $totalDailyInterestCharge;
                break;

            case accrualTypes::ACCRUAL_TYPE_30_360:
            case accrualTypes::ACCRUAL_TYPE_30_365:
                $diemDays = '';
                if ($monthsDays['months']) {
                    $diemDays .= $monthsDays['months'] . ' Month' . ($monthsDays['months'] !== 1 ? 's' : '');
                }
                if ($monthsDays['days']) {
                    if ($monthsDays['months']) {
                        $diemDays .= ' / ';
                    }
                    $diemDays .= $monthsDays['days'] . ' Day' . ($monthsDays['days'] !== 1 ? 's' : '');
                }
                if(!$diemDays) {
                    $diemDays = '0 Days';
                }
                $totalEstPerDiem = ($monthsDays['months'] * 30 + $monthsDays['days']) * $totalDailyInterestCharge;
                break;
        }


        $res->diemDays = $diemDays;
        $res->totalEstPerDiem = $totalEstPerDiem;
        $res->monthsDays = $monthsDays;

        return $res;
    }
}