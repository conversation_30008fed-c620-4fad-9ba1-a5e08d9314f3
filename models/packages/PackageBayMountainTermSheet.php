<?php

namespace models\packages;

use models\composite\proposalFormula;
use models\constants\typeOfHMLOLoanRequesting;
use models\Controllers\LMRequest\Property;
use models\HMLOLoanTermsCalculation;
use models\lendingwise\db\tblQAInfo_db;
use models\lendingwise\tblProperties;
use models\lendingwise\tblQAInfo;
use models\pdf\CustomTCPDF;
use models\pdf\BayMountainTermSheet;
use models\servicing\LoanTerms;
use models\standard\Arrays;
use models\standard\Dates;
use models\standard\Strings;

class PackageBayMountainTermSheet
{
    /**
     * @param CustomTCPDF|null $pdf
     * @return CustomTCPDF
     */
    public static function GeneratePDF($inArray, CustomTCPDF $pdf = null): CustomTCPDF
    {
        global $txnID, $glPositionArray;

        /** Page layout params **/
        $xVal = 10;
        $yVal = 30;

        $txnID = '';
        if (count($inArray) > 0) {
            if (array_key_exists('txnID', $inArray)) $txnID = trim($inArray['txnID']);
        }

        $pdf = new BayMountainTermSheet(PDF_PAGE_ORIENTATION, PDF_UNIT, NEW_PDF_PAGE_FORMAT, true, 'UTF-8', false);
        $pdf->SetFont('helvetica', 'BI', 12);

        $pdf->AddPage();
        $pdf->setLeftMargin($xVal);
        $pdf->setRightMargin($xVal);
        $pdf->setCellHeightRatio('1.1');
        $pdf->setAutoPageBreak(TRUE);

        //Get variables from DB
        $fileDetailsArray = $inArray['fileDetails'];
        $LMRId = $inArray['LMRId'];
        if (count($fileDetailsArray ?? []) > 0) {
            $PkgInfoArray = $fileDetailsArray[$LMRId];
        }

        $LMRInfoArray = [];
        $fileHMLONewLoanInfo = [];
        $fileHMLOEntityInfo = [];
        $fileHMLOPropertyInfo = [];
        $addGuarantorsInfoArray = [];
        $budgetAndDrawsInfo = [];
        $filepaydownInfo = [];
        $listingRealtorInfo = [];

        if (count($PkgInfoArray) > 0) {
            $LMRInfoArray = $PkgInfoArray['LMRInfo'];
            $fileHMLONewLoanInfo = $PkgInfoArray['fileHMLONewLoanInfo'];
            $fileHMLOEntityInfo = $PkgInfoArray['fileHMLOEntityInfo'];
            $fileHMLOPropertyInfo = $PkgInfoArray['fileHMLOPropertyInfo'];
            $addGuarantorsInfoArray = $PkgInfoArray['AddGuarantorsInfo'];
            $budgetAndDrawsInfo = $PkgInfoArray['budgetAndDrawsInfo'];
            $filepaydownInfo = $PkgInfoArray['paydownInfo'];
            $listingRealtorInfo = $PkgInfoArray['listingRealtorInfo'];
        }
        if (count($LMRInfoArray) > 0) {
            $borrowerFName = ucwords(trim($LMRInfoArray['borrowerName']));
		    $borrowerLName = ucwords(trim($LMRInfoArray['borrowerLName']));
            $coBorrowerFName = ucwords(trim($LMRInfoArray['coBorrowerFName']));
		    $coBorrowerLName = ucwords(trim($LMRInfoArray['coBorrowerLName']));
            $lien1Rate = $LMRInfoArray['lien1Rate'];
            $loanNumber = $LMRInfoArray['loanNumber'];
            $propertyAddress = trim(Property::$primaryPropertyInfo->propertyAddress);
            $propertyCity = trim(Property::$primaryPropertyInfo->propertyCity);
            $propertyState = trim(Property::$primaryPropertyInfo->propertyState);
            $propertyZip = trim(Property::$primaryPropertyInfo->propertyZipCode);

            $propertyAddress = $propertyAddress. ', ' .$propertyCity. ', ' .$propertyState. ' ' .$propertyZip;
        }

        $totalDrawsFunded = 0;
        if (count($budgetAndDrawsInfo) > 0) {
            for ($aGur = 0; $aGur < count($budgetAndDrawsInfo); $aGur++) {
                $totalDrawsFunded += Strings::replaceCommaValues($budgetAndDrawsInfo[$aGur]['amountAddedToTotalDrawsFunded']);
            }
        }
        $paydownamount = 0;
        if (count($filepaydownInfo) > 0) {
            for ($paycount = 0; $paycount < count($filepaydownInfo); $paycount++) {
                $paydownamount = $paydownamount + $filepaydownInfo[$paycount]['principalPayDownAmount'];
            }
        }
        $costBasis = 0;
        if (count($listingRealtorInfo) > 0) {
            $costBasis = $pdf->Formatted(trim($listingRealtorInfo['costBasis']));
        }

        if (count($fileHMLONewLoanInfo) > 0) {
            $totalLoanAmount = $pdf->Formatted(trim($fileHMLONewLoanInfo['totalLoanAmount']));
            $CORTotalLoanAmt = $pdf->Formatted(trim($fileHMLONewLoanInfo['CORTotalLoanAmt']));
            $processingFee = $pdf->Formatted(trim($fileHMLONewLoanInfo['processingFee']));
            $originationPointsRate = number_format(Strings::replaceCommaValues($fileHMLONewLoanInfo['originationPointsRate']),2);
            $originationPointsValue = $pdf->Formatted(trim($fileHMLONewLoanInfo['originationPointsValue']));
            $originationPoints = $originationPointsRate .' | $'. $originationPointsValue;
            $brokerPointsRate = number_format(Strings::replaceCommaValues($fileHMLONewLoanInfo['brokerPointsRate']),2);
            $brokerPointsValue = $pdf->Formatted(trim($fileHMLONewLoanInfo['brokerPointsValue']));
            $brokerPoints = $brokerPointsRate .' | $'. $brokerPointsValue;
            $drawsFee = $fileHMLONewLoanInfo['drawsFee'];
            $exitFeePoints = $fileHMLONewLoanInfo['exitFeePoints'];
            $exitFeeAmount = $pdf->Formatted(trim($fileHMLONewLoanInfo['exitFeeAmount']));
            $finalLoanAmtPkg = $pdf->Formatted(trim($fileHMLONewLoanInfo['finalLoanAmtPkg']));
//die($finalLoanAmtPkg);
            $rehabCost = $fileHMLONewLoanInfo['rehabCost'];
            $rehabCostPercentageFinanced = $fileHMLONewLoanInfo['rehabCostPercentageFinanced'];
            $rehabCostFinanced = $rehabCost * ($rehabCostPercentageFinanced / 100);
            $rehabCostFinanced = $pdf->Formatted($rehabCostFinanced);

            $typeOfHMLOLoanRequesting = trim($fileHMLOPropertyInfo['typeOfHMLOLoanRequesting']);
            $maxAmtToPutDown = trim($fileHMLOPropertyInfo['maxAmtToPutDown']);
            $interestChargedFromDate = $fileHMLONewLoanInfo['interestChargedFromDate'];
            $interestChargedEndDate = $fileHMLONewLoanInfo['interestChargedEndDate'];
            $prepaidInterestReserveForCal = $fileHMLONewLoanInfo['prepaidInterestReserve'];
            $closingCostFinanced = $fileHMLONewLoanInfo['closingCostFinanced'];
            $downPaymentPercentage = $fileHMLONewLoanInfo['downPaymentPercentage'];
            $isLoanPaymentAmt = $fileHMLONewLoanInfo['isLoanPaymentAmt'];
            if ($isLoanPaymentAmt == '') $isLoanPaymentAmt = LoanTerms::TLA;

            if (Strings::replaceCommaValues($costBasis) > 0) $downPaymentPercentageNew = (Strings::Numeric($maxAmtToPutDown) / $costBasis) * 100;
            if ($downPaymentPercentageNew != $downPaymentPercentage) $downPaymentPercentage = $downPaymentPercentageNew;

            $maxAmtToPutDown = proposalFormula::calculateDownPaymentByPercentage($downPaymentPercentage, $costBasis);

            $acquisitionPriceFinancedArr = proposalFormula::calculateInitialLoanAmount([
                'costBasis' => $costBasis,
                'maxAmtToPutDown' => $maxAmtToPutDown,
                'typeOfHMLOLoanRequesting' => $typeOfHMLOLoanRequesting,
                'totalLoanAmount' => $totalLoanAmount,
                'rehabCostFinanced' => $rehabCostFinanced,
                'closingCostFinanced' => $closingCostFinanced,
                'prepaidInterestReserve' => $prepaidInterestReserveForCal
            ]);
            $acquisitionPriceFinanced = Arrays::getArrayValue('acquisitionPriceFinanced', $acquisitionPriceFinancedArr);

            if (HMLOLoanTermsCalculation::isTransactionTypeRefinance($typeOfHMLOLoanRequesting)
                || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::NEW_CONSTRUCTION_EXISTING_LAND
                || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::DELAYED_PURCHASE) {
                $initialLoanAmount = $CORTotalLoanAmt;
            } else {
                $initialLoanAmount = $acquisitionPriceFinanced;
            }
            $CLBInArray = ['initLAmt' => $initialLoanAmount,
                           'prePIR' => $prepaidInterestReserveForCal,
                           'closingCost' => $closingCostFinanced,
                           'funDraw' => $totalDrawsFunded,
                           'principalPayDown' => $paydownamount
            ];
            $currentLoanBalance = proposalFormula::calculateCurrentLoanBalance($CLBInArray,$typeOfHMLOLoanRequesting);

            $diemDaysArray = ['startDate' => $interestChargedFromDate, 'endDate' => $interestChargedEndDate];
            $diemDays = Dates::calculateNoOfDaysForTwoDate($diemDaysArray);

            /* */
            $PerDeimTotalAmt = 0;
            if ($isLoanPaymentAmt == LoanTerms::ILA) {
                $PerDeimTotalAmt = $currentLoanBalance;
            }
            if ($isLoanPaymentAmt == LoanTerms::TLA) {
                $PerDeimTotalAmt = $totalLoanAmount;
            }

            $dailyInteresInArray = ['totalLoanAmount' => $PerDeimTotalAmt, 'lien1Rate' => $lien1Rate];
            $totalDailyInterestCharge = proposalFormula::calculateTotalDailyInterestCharge($dailyInteresInArray);

            $dailyEstPerDiemArray = ['diemDays' => $diemDays, 'totalDailyInterestCharge' => $totalDailyInterestCharge];
            $totalEstPerDiem = proposalFormula::calculateTotalEstPerDiem($dailyEstPerDiemArray);
            $fileHMLONewLoanInfo['totalEstPerDiem'] = $totalEstPerDiem;
            $otherLenderFees =  $fileHMLONewLoanInfo['travelNotaryFee'];
            $otherLenderFees += $fileHMLONewLoanInfo['closingCostFinancingFee'];
            $otherLenderFees += $fileHMLONewLoanInfo['valuationBPOFee'];
            $otherLenderFees += $fileHMLONewLoanInfo['creditReportFee'];
            $otherLenderFees += $fileHMLONewLoanInfo['floodCertificateFee'];
            $otherLenderFees += $fileHMLONewLoanInfo['valuationAVMFee'];
            $otherLenderFees += $fileHMLONewLoanInfo['escrowFees'];
            $otherLenderFees += $fileHMLONewLoanInfo['documentPreparationFee'];
            $otherLenderFees += $fileHMLONewLoanInfo['prePaidInterest'];
            $otherLenderFees += $fileHMLONewLoanInfo['projectFeasibility'];
            $otherLenderFees += $fileHMLONewLoanInfo['insurancePremium'];
            $otherLenderFees += $fileHMLONewLoanInfo['otherFee'];
            $otherLenderFees += $fileHMLONewLoanInfo['employmentVerificationFee'];
            $otherLenderFees += $fileHMLONewLoanInfo['propertyTax'];
            $otherLenderFees += $fileHMLONewLoanInfo['survey'];
            $otherLenderFees += $fileHMLONewLoanInfo['totalEstPerDiem'];

            if($otherLenderFees > 0) $otherLenderFees = $pdf->Formatted($otherLenderFees);
            else $otherLenderFees =  '0.00';
        }

        if (count($fileHMLOEntityInfo) > 0) {
            $entityName = ucwords(trim($fileHMLOEntityInfo['entityName']));
        }

        if (count($fileHMLOPropertyInfo) > 0) {
            $maxAmtToPutDown = Strings::Currency(trim($fileHMLOPropertyInfo['maxAmtToPutDown']));
            $typeOfHMLOLoanRequesting = trim($fileHMLOPropertyInfo['typeOfHMLOLoanRequesting']);
            $loanTerm = trim($fileHMLOPropertyInfo['loanTerm']);
            $extensionOption = trim($fileHMLONewLoanInfo['extensionOption']);
            switch ($extensionOption) {
                case 1:
                    $extOptionText = '1 Month with Lender Approval';
                    break;
                case 2:
                    $extOptionText = '3 Months with Lender Approval';
                    break;
                case 3:
                    $extOptionText = '6 Months with Lender Approval';
                    break;
                case 4:
                    $extOptionText = '9 Months with Lender Approval';
                    break;
                case 5:
                    $extOptionText = '12 Months with Lender Approval';
                    break;
                case 6:
                    $extOptionText = '2 Months with Lender Approval';
                    break;
                case 7:
                    $extOptionText = 'Available if Needed';
                    break;
                case 8:
                    $extOptionText = 'No Extension Options';
                    break;
            }
            $extensionOptionPercentage = trim($fileHMLONewLoanInfo['extensionOptionPercentage']);
            if($extOptionText=='No Extension Options'){
                $finalLoanTerm = "$loanTerm; $extOptionText Available";
            }
            elseif ($extOptionText=='Available if Needed'){
                $finalLoanTerm = "$loanTerm; Extension Options " .$extOptionText;
            }
            else {
                $finalLoanTerm = "$loanTerm; $extOptionText Extension Available for $extensionOptionPercentage PT(s) Each at Lender's Discretion";
            }
            $expectForDueDiligence = urldecode(trim($fileHMLOPropertyInfo['expectForDueDiligence']));
            if($expectForDueDiligence=='')$expectForDueDiligence = 'N/A';
        }

/*        if ($typeOfHMLOLoanRequesting == 'Cash-Out / Refinance' || $typeOfHMLOLoanRequesting == 'Commercial Cash out Refinance' || $typeOfHMLOLoanRequesting == 'Commercial Rate / Term Refinance' || $typeOfHMLOLoanRequesting == 'Rate & Term Refinance' || $typeOfHMLOLoanRequesting == 'Refinance') {
            $totalLoanAmountFinal = $CORTotalLoanAmt;
        } else {
            $totalLoanAmountFinal = $totalLoanAmount;
        }*/

        $borrowerName = $borrowerFName . ' ' . $borrowerLName;
//dd($addGuarantorsInfoArray);
        if (count($addGuarantorsInfoArray) > 0) {
            //print_r($addGuarantorsInfoArray[0]);die();
            for ($i = 0; $i < count($addGuarantorsInfoArray); $i++) {
                if ($addGuarantorsInfoArray[$i]['guarantorFName'] > '' || $addGuarantorsInfoArray[$i]['guarantorLName'] > '') {
                    $borrowerName .= ', ' . $addGuarantorsInfoArray[$i]['guarantorFName'] . ' ' . $addGuarantorsInfoArray[$i]['guarantorLName'];
                }

            }
        }
        $borrowerName = ucwords($borrowerName);
        $addCollateral = 0;
        foreach (Property::$blanketLoanPropertiesInfo as $blanketProperty) {
            $addCollateral += ($blanketProperty->getTblPropertiesDetails_by_propertyId()->propertyEstimatedValue);
        }
        $addCollateral = $pdf->Formatted($addCollateral);
        $security = "1st Lien Deed of Trust with Mortgagee's Title Insurance";

        $style = "style=\"border:0.5px solid black;\"";

        $tbl = <<<EOD
    <table width="100%" border="0" cellpadding="4">
        <tr>
            <td align="center" $style><b>Preliminary Terms ($loanTerm)</b>
            </td>
        </tr>
        <tr>
            <td width="25%" $style>Loan No</td><td width="75%" $style>$loanNumber</td>
        </tr>
        <tr>
            <td $style>Property Address</td><td $style>$propertyAddress</td>
        </tr>
        <tr>
            <td $style>Loan Amount</td><td $style>$$finalLoanAmtPkg</td>
        </tr>
        <tr>
            <td $style>Borrower</td><td $style>$entityName</td>
        </tr>
        <tr>
            <td $style>Co-Borrower/Guarantor</td><td $style>$borrowerName</td>
        </tr>
        <tr>
            <td $style>Security</td><td $style>$security</td>
        </tr>
        <tr>
            <td $style>Additional Collateral</td><td $style>$addCollateral</td>
        </tr>
        <tr>
            <td $style>Holdback For
            Construction/Rehab</td><td $style>$$rehabCostFinanced</td>
        </tr>
        <tr>
            <td $style>Term (Mos)</td><td $style>$finalLoanTerm</td>
        </tr>
        <tr>
            <td $style>Interest Rate per Annum</td><td $style>$lien1Rate%</td>
        </tr>
        <tr>
            <td $style>Origination Fee</td><td $style>$originationPoints</td>
        </tr>
        <tr>
            <td $style>Broker Fee</td><td $style>$brokerPoints</td>
        </tr>
        <tr>
            <td $style>Exit Fee at Payoff</td><td $style>$exitFeePoints | $$exitFeeAmount</td>
        </tr>
        <tr>
            <td $style>Processing Fee</td><td $style>$$processingFee</td>
        </tr>
        <tr>
            <td $style>Cash to Close Excluding Our Fees and Other Closing Costs</td><td $style>$maxAmtToPutDown</td>
        </tr>
        <tr>
            <td $style>Additional Terms</td><td $style>$expectForDueDiligence</td>
        </tr>
        <tr>
            <td $style>Draw Review Fee</td><td $style>$135-$250 per draw on complete work only</td>
        </tr>
        <tr>
            <td $style>Prepayment Penalty</td><td $style>N/A</td>
        </tr>
        <tr>
            <td $style>Debt Service Payments</td><td $style>Interest only on Loan Amount payable monthly</td>
        </tr>
        <tr>
            <td $style>Tax Escrow</td><td $style>At closing, a deposit based on closing date and state & local tax payment due date. Monthly escrow payments
            thereafter.</td>
        </tr>
        <tr>
            <td $style>Required Items</td>
            <td $style>1. Copy of fully executed purchase agreement, if applicable 
            <br />
            <br />
            2. Detailed Rehab/Construction Budget, if applicable
            <br />
            <br />
            3. Inspection of the property by BMC or its representative
            <br />
            <br />
            4. Evidence of Insurance naming Bay Mountain Fund I ISAOA as mortgagee
            <br />
            <br />
            5. Foundation work will require a bid from a licensed company and include warranty language; funds will not be
released without a paid receipt including transferable warranty, an engineer’s report, and lien waiver
            <br />
            <br />
            6. Roof work will require a bid from a licensed company and include warranty language; funds will not be
released without a paid receipt including transferable warranty and lien waiver
            <br />
            <br />
            7. Additional items as may be required by Lender
            </td>
        </tr>
        <tr>
            <td $style>Non-Binding Offer</td><td $style>No legal obligations are imposed on either party unless and until loan documents are executed and funding
            occurs.</td>
        </tr>
    </table>
EOD;
        $pdf->SetXY($xVal, $yVal-10);
        $pdf->SetFont('', '', 8);
        $pdf->writeHTML($tbl, true, 0, true, 0);

        $tbl = <<<EOD
    <table width="100%" border="0" cellpadding="4">
        <tr>
            <td align="center">3710 Rawlins Street, Suite 1250 | Dallas, TX | 75219</td>
        </tr>
        <tr>
            <td align="center">O: ************ | F: ************ | E: <EMAIL></td>
        </tr>
    </table>
EOD;
        $y = $pdf->GetY();
        $pdf->SetXY($xVal, $y);
        $pdf->SetFont('', '', 8);
        $pdf->writeHTML($tbl, true, 0, true, 0); 

        //===========ESIGN LOCATION FINDER===========
$tbl = <<<EOD
<table cellpadding="0" width="150px" style="text-align:justify;">
    <tr>
        <td style="height:23px;"></td>
	</tr>
    <tr>
        <td style="height:33px;background-color:red;">Esign will be here</td>
	</tr>	
</table>
EOD;

$yVal = $pdf->GetY();
//borrower esign location finder next 3 lines
$pdf->SetXY($xVal+130, $yVal-30);
$pdf->SetFont('helvetica', '', 10.3);    
//$pdf->writeHTML($tbl);
//coborrower esign location finder next 3 lines
$pdf->SetXY($xVal+105, $yVal);
$pdf->SetFont('helvetica', '', 10.3);    
//$pdf->writeHTML($tbl);

//===========ESIGN code===========
    $signY = $pdf->GetY();
    /*** Signature Get x and y position Start ***/
    $tempPosArray['pageNo'] = $pdf->PageNo();
    $tempPosArray['xPos'] = $xVal + 130;
    $tempPosArray['yPos'] = $signY-30;
    $tempPosArray['showTo'] = 'Client';
    $tempPosArray['width'] = 60;
    $tempPosArray['maxLen'] = 58;
    $tempPosArray['signField'] = 1;

    $glPositionArray[] = $tempPosArray;

    /*if ($isCoBorrower == 1) {

        /*** Get x and y position Start ***

        $tempPosArray['pageNo'] = $pdf->PageNo();
        $tempPosArray['xPos'] = $xVal + 120;
        $tempPosArray['yPos'] = $signY + 10;
        $tempPosArray['showTo'] = 'Co-Client';
        $tempPosArray['signField'] = 1;
        $tempPosArray['fldType'] = '';

        array_push($glPositionArray, $tempPosArray);
    }*/
//===========END of ESIGN code===========   

        return $pdf;
    }
}
