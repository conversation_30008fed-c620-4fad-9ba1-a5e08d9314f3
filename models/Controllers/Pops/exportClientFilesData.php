<?php

namespace models\Controllers\Pops;

use models\composite\calEffectiveGrossIncome;
use models\composite\debtServiceRatioPITIA;
use models\composite\oFile\getFileInfo\getFileDaysInPreviousStatus;
use models\composite\oFile\getFileInfo\getValuationMethodsGlobal;
use models\composite\oFile\getInsuranceTypes;
use models\composite\proposalFormula;
use models\constants\accrualTypes;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glHMLOExtensionOption;
use models\constants\gl\glHMLOLienPosition;
use models\constants\gl\glInitialTerms;
use models\constants\gl\glMortgageInvestorOwnerArray;
use models\constants\gl\glMortgageOwnerArray;
use models\constants\gl\glpaymentFrequency;
use models\constants\gl\glServicingSubStatus;
use models\constants\GpropertyTypeNumbArray;
use models\constants\HMDAActionTaken;
use models\constants\rehabRepairList;
use models\constants\SMSServiceProviderArray;
use models\constants\SMSServiceProviderDomainArray;
use models\Controllers\LMRequest\Property;
use models\HMLOLoanTermsCalculation;
use models\lendingwise\tblProperties;
use models\cypher;
use models\myFileInfo;
use models\servicing\LoanTerms;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Integers;
use models\standard\Strings;
use models\types\strongType;

class exportClientFilesData extends strongType
{
    public static array $manyToOneHeaders = [
        'Workflow',
        'Draw Amount',
        'Draw Date',
        'Appraised As Is Value',
        'Appraiser Rehabbed Value',
        'Appraiser Monthly Rent',
        'Appraisal Date',
        'Appraiser Order Date',
        'AVM Monthly Rent',
        'AVM As Is Value',
        'BPO Value',
        'Appraiser Name',
        'Appraiser Phone Number',
        'Appraiser Email',
        'Appraiser Company',
        'Appraised Value',
        'BPO Rehabbed Value',
        'BPO Monthly Rent',
        'BPO Date Obtained',
        //  'Insurance Rep Cell', // not a way to set this in the loan management section
        'Name of Carrier',
        'Types of Required Insurance',
        'Policy Name',
        'Insurance Policy Number',
        'Policy Expiration Date',
        'Rehabbed value', // verify
        'Recommended Offer', // verify
        'Rehab Budget', // verify
        'Estimated Title Insurance Fees', // verify
        'Days In Previous Status',
        'All Custom Fields',
    ];

    public static ?string $fileid = null;
    public static ?string $clientfirstname = null;
    public static ?string $clientlastname = null;
    public static ?string $clientemail = null;
    public static ?string $propertyaddress = null;
    public static ?string $propertycity = null;
    public static ?string $propertystate = null;
    public static ?string $propertyzip = null;


    /**
     * @param int $LMRId
     * @param int $PCID
     * @param string $fileType
     * @param string $userRole
     * @return array
     */
    public static function getCSVArray(int $LMRId, int $PCID, string $fileType, string $userRole): array
    {
        global $userRole, $fileType, $userTimeZone;

        $tArray = exportClientFiles::$tArray;

        exportClientFiles::$selectedFieldsValues = [];

        $SMSServiceProviderArray = SMSServiceProviderArray::$SMSServiceProviderArray;
        $glMortgageOwnerArray = glMortgageOwnerArray::$glMortgageOwnerArray;
        $glHMLOExtensionOption = glHMLOExtensionOption::$glHMLOExtensionOption;
        $glServicingSubStatus = glServicingSubStatus::$glServicingSubStatus;
        $glHMLOLienPosition = glHMLOLienPosition::$glHMLOLienPosition;
        $glMortgageInvestorOwnerArray = glMortgageInvestorOwnerArray::$glMortgageInvestorOwnerArray;

        $myFileInfo = exportClientFiles::$LMRDataArray[$LMRId];

        $clientfirstname = Strings::processStringForExport(trim($myFileInfo['borrowerName']));
        $clientlastname = Strings::processStringForExport(trim($myFileInfo['borrowerLName']));

        $clientssnlast4 = trim($myFileInfo['ssnNumber']);
        $coborrowerssnlast4 = trim($myFileInfo['coBSsnNumber']);

        $fullssn = trim($myFileInfo['ssnNumber']);
        $coborrowerfullssn = trim($myFileInfo['coBSsnNumber']);


        if (exportClientFiles::$PCID == 1159 || exportClientFiles::$PCID == 820) {  /* Customization for PC = The Lane Law Firm on March 8, 2016 Start */
            $clientssnlast4 = Strings::formatSSNNumber($clientssnlast4);
            $coborrowerssnlast4 = Strings::formatSSNNumber($coborrowerssnlast4);

            $fullssn = Strings::formatSSNNumber($fullssn);
            $coborrowerfullssn = Strings::formatSSNNumber($coborrowerfullssn);

        } else {
            $clientssnlast4 = substr($clientssnlast4, -4);
            $coborrowerssnlast4 = substr($coborrowerssnlast4, -4);
        }

        $smsserviceprovider = null;
        $clienttimezone = trim($myFileInfo['borrowerTimeZone']);
        $coborrowertimezone = trim($myFileInfo['coBorrowerTimeZone']);
        if (array_key_exists(trim($myFileInfo['serviceProvider']), $SMSServiceProviderArray)) {
            $smsserviceprovider = $SMSServiceProviderArray[trim($myFileInfo['serviceProvider'])];
        }

        $coborrowersmsserviceprovider = null;
        if (array_key_exists(trim($myFileInfo['coBServiceProvider']), $SMSServiceProviderArray)) {
            $coborrowersmsserviceprovider = $SMSServiceProviderArray[trim($myFileInfo['coBServiceProvider'])];
        }


        $clientfirstname = html_entity_decode(trim($clientfirstname), ENT_QUOTES, 'UTF-8');
        $clientlastname = html_entity_decode(trim($clientlastname), ENT_QUOTES, 'UTF-8');
        $clientphonenumber = Strings::formatPhoneNumber(trim($myFileInfo['phoneNumber']));
        $clientcellphonenumber = Strings::formatPhoneNumber(trim($myFileInfo['cellNumber']));
        $clientemail = trim($myFileInfo['borrowerEmail']);
        $borrowersecondaryemail = trim($myFileInfo['borrowerSecondaryEmail']);
        $clientdob = trim($myFileInfo['borrowerDOB']);
        $mailingaddress = trim($myFileInfo['mailingAddress']);
        $mailingcity = trim($myFileInfo['mailingCity']);
        $mailingstate = trim($myFileInfo['mailingState']);
        $mailingzip = trim($myFileInfo['mailingZip']);
        $clientaltphonenumber = Strings::formatPhoneNumber(trim($myFileInfo['altPhoneNumber']));
        $clientworkphonenumber = Strings::formatPhoneNumber(trim($myFileInfo['workNumber']));
        $clientfaxnumber = Strings::formatPhoneNumber(trim($myFileInfo['fax']));
        $branchID = trim($myFileInfo['FBRID']);
        $agentID = trim($myFileInfo['brokerNumber']);
        $LOId = trim($myFileInfo['secondaryBrokerNumber']);
        $LMRResponseId = trim($myFileInfo['LMRResponseId']);
        $recordDate = trim($myFileInfo['recordDate']);
        $filecreateddate = trim($myFileInfo['recordDate']);
        $saledate = trim($myFileInfo['salesDate']);
        $filereceiveddate = trim($myFileInfo['receivedDate']);
        $closeddate = trim($myFileInfo['closedDate']);
        $lastupdateddate = trim($myFileInfo['lastUpdatedDate']);
        $prioritystatus = trim($myFileInfo['priorityLevel']);
        $filenumber = trim($myFileInfo['fileNumber']);
        $leadsource = trim($myFileInfo['leadSource']);
        $borrowercallback = trim($myFileInfo['borrowerCallBack']);
        $lendercallback = trim($myFileInfo['lenderCallBack']);
        $hafadate = trim($myFileInfo['HAFADate']);
        $welcomecalldate = trim($myFileInfo['welcomeCallDate']);
        $bankcallcompleted = trim($myFileInfo['bankCallCompleted']);
        $appealdate = trim($myFileInfo['appealDate']);
        $datemodreceived = trim($myFileInfo['trialModReceivedDate']);
        $mod1stpayment = trim($myFileInfo['firstTrialPaymentDate']);
        $lendersubmissiondate = trim($myFileInfo['lenderSubmissionDate']);
        $resolutiontype = trim($myFileInfo['resolutionType']);
        $attorneyreviewdate = trim($myFileInfo['attorneyReviewDate']);
        $dateof1sttrialpayment = trim($myFileInfo['trialPaymentDate1']);
        $dateof2ndtrialpayment = trim($myFileInfo['trialPaymentDate2']);
        $dateof3rdtrialpayment = trim($myFileInfo['trialPaymentDate3']);
        $amountof1stmodpayment = trim($myFileInfo['firstModPaymentAmt']);
        $retainerdate = trim($myFileInfo['retainerDate']);
        $escalationdate = trim($myFileInfo['escalationDate']);
        $denialdate = trim($myFileInfo['denialDate']);
        $totalcallsplaced = trim($myFileInfo['totalCallsPlaced']);
        $aninumber = trim($myFileInfo['ANINo']);
        $primeStatusId = trim($myFileInfo['primeStatusId']);
        $dateoffirstpaymentdue = trim($myFileInfo['trialPaymentDate1']);

        $borrowerfirstname = $clientfirstname;
        $borrowerlastname = $clientlastname;
        $borrowerphonenumber = $clientphonenumber;
        $borrowercellnumber = $clientcellphonenumber;
        $borrowerfaxnumber = $clientfaxnumber;
        $borroweremail = $clientemail;
        $borrowerdateofbirth = $clientdob;
        $halfbaths = trim($myFileInfo['howManyHalfBathRoom']);

        $dateoffirstpaymentdue = Dates::formatDateWithRE(trim($myFileInfo['trialPaymentDate1']), 'YMD', 'm/d/Y');
        $borrowerdateofbirth = Dates::formatDateWithRE($borrowerdateofbirth, 'YMD', 'm/d/Y');

        $agentReferralCode = '';
        if (isset($agentPromoCodesArray[$agentID])) {
            $agentReferralCode = cypher::myEncryption($agentPromoCodesArray[$agentID]['brokerNumberPromoCode']);
        }


        $agentInfo = null;
        if (array_key_exists($agentID, exportClientFiles::$fileAgentInfo)) {
            $agentInfo = exportClientFiles::$fileAgentInfo[$agentID];
        }
        if ($agentInfo) {
            $agentfirstname = trim($agentInfo['brokerFName']);
            $agentlastname = trim($agentInfo['brokerLName']);
            $agentcompany = trim($agentInfo['company']);
            $agentemail = trim($agentInfo['email']);
            $agentcell = Strings::formatPhoneNumber(trim($agentInfo['cellNumber']));
            $agentfax = Strings::formatPhoneNumber(trim($agentInfo['fax']));
            $agentphone = Strings::formatPhoneNumber(trim($agentInfo['phoneNumber']));
        }

        $loInfo = null;
        if (array_key_exists($LOId, exportClientFiles::$fileLOInfo)) {
            $loInfo = exportClientFiles::$fileLOInfo[$LOId];
        }

        $branchReferralCode = '';
        if (isset(exportClientFiles::$branchPromoCodesArray[$branchID])) {
            $branchReferralCode = (exportClientFiles::$branchPromoCodesArray[$branchID]['branchNumberPromoCode']);
        }

        //This logic is to check the values that we get from assets section based on the fields enabled/disabled.

        $fileModuleCode = null;
        if (array_key_exists(trim($LMRId), exportClientFiles::$fileModuleInfoArray)) {
            $fileMC = [];
            $fileCT = '';
            $fileTypeArray = exportClientFiles::$fileModuleInfoArray[$LMRId];

            $aCm = '';
            for ($ct = 0; $ct < count($fileTypeArray); $ct++) {
                $fileCT .= $aCm . trim($fileTypeArray[$ct]['moduleCode']);
                $aCm = ',';
            }
            for ($ct = 0; $ct < count($fileTypeArray); $ct++) {
                $fileMC[] = trim($fileTypeArray[$ct]['moduleCode']);
            }
            $fileModuleCode = $fileMC[0];
        }

        $backofficefilelink = CONST_SITE_URL . 'backoffice/LMRequest.php?eId=' . cypher::myEncryption($branchID) . '&lId=' . cypher::myEncryption($LMRId) . '&rId=' . cypher::myEncryption($LMRResponseId) . '&op=a72f9e967052513d';
        $fullappforborrower = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRId) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('FA') . '&UType=' . cypher::myEncryption('Borrower');
        $quickappforborrower = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRId) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('QA') . '&UType=' . cypher::myEncryption('Borrower');

        $fullappforbroker = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&aRc=' . $agentReferralCode . '&fOpt=' . cypher::myEncryption('agent') . '&lid=' . cypher::myEncryption($LMRId) . '&ft=' . $fileModuleCode . '&opt=' . cypher::myEncryption('Email') . '&op=' . cypher::myEncryption('FA') . '&UType=' . cypher::myEncryption('Agent/Broker');

        $quickappforbroker = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&aRc=' . $agentReferralCode . '&fOpt=' . cypher::myEncryption('agent') . '&lid=' . cypher::myEncryption($LMRId) . '&ft=' . $fileModuleCode . '&opt=' . cypher::myEncryption('Email') . '&op=' . cypher::myEncryption('QA') . '&UType=' . cypher::myEncryption('Agent/Broker');

        $uploadportalforborrower = CONST_SITE_URL . 'backoffice/uploadLMRDocs.php?lid=' . cypher::myEncryption($LMRId) . '&ft=' . $fileModuleCode . '&UType=' . cypher::myEncryption('Borrower') . '&UName=' . cypher::myEncryption($clientfirstname) . '&email=' . cypher::myEncryption($clientemail);
        $uploadportalforbroker = CONST_SITE_URL . 'backoffice/uploadLMRDocs.php?lid=' . cypher::myEncryption($LMRId) . '&ft=' . $fileModuleCode . '&UType=' . cypher::myEncryption('Agent/Broker') . '&UName=' . cypher::myEncryption($agentfirstname) . '&email=' . cypher::myEncryption($agentemail);
        $faborrowerinforedacted = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRId) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink') . '&bir=' . cypher::myEncryption('yes');
        $faborrowerinforedactedandoffersub = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRId) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink') . '&bir=' . cypher::myEncryption('yes') . '&so=' . cypher::myEncryption('yes');
        $qaborrowerinforedacted = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRId) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('QA') . '&sl=' . cypher::myEncryption('shareLink') . '&bir=' . cypher::myEncryption('yes');
        $qaborrowerinforedactedandoffersub = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRId) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('QA') . '&sl=' . cypher::myEncryption('shareLink') . '&bir=' . cypher::myEncryption('yes') . '&so=' . cypher::myEncryption('yes');
        $qareadonlyanduploadportal = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRId) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('QA') . '&sl=' . cypher::myEncryption('shareLink') . '&aud=' . cypher::myEncryption('yes');
        $qareadonlyuploadportalandoffersub = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRId) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('QA') . '&sl=' . cypher::myEncryption('shareLink') . '&aud=' . cypher::myEncryption('yes') . '&so=' . cypher::myEncryption('yes');
        $fareadonly = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRId) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink');
        $fareadonlyandoffersub = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRId) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink') . '&so=' . cypher::myEncryption('yes');
        $fareadonlyanduploadportal = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRId) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink') . '&aud=' . cypher::myEncryption('yes');
        $fareadonlyuploadportalandoffersub = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRId) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink') . '&aud=' . cypher::myEncryption('yes') . '&so=' . cypher::myEncryption('yes');


        $coborrowerfirstname = trim($myFileInfo['coBorrowerFName']);
        $coborrowerlastname = trim($myFileInfo['coBorrowerLName']);
        $coborrowerphonenumber = Strings::formatPhoneNumber(trim($myFileInfo['coBPhoneNumber']));
        $coborroweraltphonenumber = Strings::formatPhoneNumber(trim($myFileInfo['coBAltPhoneNumber']));
        $coborrowercellnumber = Strings::formatPhoneNumber(trim($myFileInfo['coBCellNumber']));
        $coborrowerworknumber = Strings::formatPhoneNumber(trim($myFileInfo['coBorrowerWorkNumber']));
        $coborrowerfaxnumber = Strings::formatPhoneNumber(trim($myFileInfo['coBFax']));
        $coborroweremail = trim($myFileInfo['coBorrowerEmail']);
        $coborrowerdob = trim($myFileInfo['coBorrowerDOB']);


        $city = trim($myFileInfo['propertyCity']);
        $state = trim($myFileInfo['propertyState']);
        $zip = trim($myFileInfo['propertyZip']);
        $county = trim($myFileInfo['propertyCounty']);

        if (array_key_exists($myFileInfo['mortgageInvestor1'], $glMortgageInvestorOwnerArray)) {
            $mortgageinvestor1 = $glMortgageInvestorOwnerArray[$myFileInfo['mortgageInvestor1']];
        }
        if (array_key_exists($myFileInfo['mortgageInvestor2'], $glMortgageInvestorOwnerArray)) {
            $mortgageinvestor2 = $glMortgageInvestorOwnerArray[$myFileInfo['mortgageInvestor2']];
        }

        $occupancy = trim($myFileInfo['occupancy']);
        $propertytype = trim($myFileInfo['propertyType']);
        $noofbedroom = trim($myFileInfo['howManyBedRoom']);
        $noofbaths = trim($myFileInfo['howManyBathRoom']);
        $homevalue = trim($myFileInfo['homeValue']);
        $yearpurchased = trim($myFileInfo['yearPurchased']);
        $currentlender = trim($myFileInfo['servicer1']);
        $currentlender2 = trim($myFileInfo['servicer2']);
        $originallender = trim($myFileInfo['originalLender1']);
        $originallender2 = trim($myFileInfo['originalLender2']);
        $mortgageinvestorowner = trim($myFileInfo['mortgageInvestor1']);
        $loantypelien1 = trim($myFileInfo['loanType']);
        $currentrate = trim($myFileInfo['lien1Rate']);
        $unpaidbalance = trim($myFileInfo['lien1Amount']);
        $originalloanamount = trim($myFileInfo['lien1OriginalBalance']);
        $loannumber = trim($myFileInfo['loanNumber']);
        $amountpastdue = trim($myFileInfo['lien1BalanceDue']);
        $monthspastdue = trim($myFileInfo['noOfMonthsBehind1']);
        $mtgtype = trim($myFileInfo['mortgageOwner1']);
        $mtgpipayment = trim($myFileInfo['lien1Payment']);
        $loantypelien2 = trim($myFileInfo['loanType2']);
        $rate = trim($myFileInfo['lien2Rate']);
        $unpaidbalancelien2 = trim($myFileInfo['lien2Amount']);
        $originalloanamountlien2 = trim($myFileInfo['lien2OriginalBalance']);
        $paymentamount = trim($myFileInfo['lien2Payment']);
        $loannumberlien2 = trim($myFileInfo['loanNumber2']);
        $amountpastduelien2 = trim($myFileInfo['lien2BalanceDue']);
        $monthspastduelien2 = trim($myFileInfo['noOfMonthsBehind2']);
        $currentlenderservicer = trim($myFileInfo['servicer2']);
        $mtgnotes = trim($myFileInfo['mortgageNotes']);
        $closeddisposition = trim($myFileInfo['closedDisposition']);
        $loanamountrequested = trim($myFileInfo['lien1OriginalBalance']);
        $interestrate = trim($myFileInfo['lien1Rate']);
        $monthlypayment = trim($myFileInfo['lien1Payment']);
        $lien1Terms = trim($myFileInfo['lien1Terms']);
        $amortization = trim($myFileInfo['lien1Terms']);
        if ($amortization == '') {
            $amortization = LoanTerms::INTEREST_ONLY;
        }
        $bedroom = trim($myFileInfo['howManyBedRoom']);
        $bathroom = trim($myFileInfo['howManyBathRoom']);
        $homeValue = trim($myFileInfo['homeValue']);
        $propertyvalueasis = trim($homeValue);
        $lien1Payment = trim($myFileInfo['lien1Payment']);

        $bedrooms = trim($myFileInfo['howManyBedRoom']);
        $bathrooms = trim($myFileInfo['howManyBathRoom']);

        /** For HMLO XLS export fields added By Suresh K - (PT #: *********)**/

        $fileid = trim($LMRId);

        $subStatusInfo = null;

        if (array_key_exists($LMRId, exportClientFiles::$fileSubstatusInfo)) {
            $subStatusInfo = exportClientFiles::$fileSubstatusInfo[$LMRId];
        }

        $secondaryStatusInfo = [];
        if (array_key_exists($LMRId, exportClientFiles::$secondaryWFStatus)) $secondaryStatusInfo = exportClientFiles::$secondaryWFStatus[$LMRId];


        $coborrowerfirstname = trim($myFileInfo['coBorrowerFName']);
        $coborrowerlastname = trim($myFileInfo['coBorrowerLName']);
        $coborrowerphonenumber = Strings::formatPhoneNumber(trim($myFileInfo['coBPhoneNumber']));
        $coborrowercellnumber = Strings::formatPhoneNumber(trim($myFileInfo['coBCellNumber']));
        $coborrowerfaxnumber = Strings::formatPhoneNumber(trim($myFileInfo['coBFax']));
        $coborroweremail = trim($myFileInfo['coBorrowerEmail']);
        $coborrowerdob = trim($myFileInfo['coBorrowerDOB']);


        $propertytype = trim($myFileInfo['propertyType']);
        $homevalue = trim($myFileInfo['homeValue']);
        $mortgageinvestorowner = trim($myFileInfo['mortgageInvestor1']);
        $currentrate = trim($myFileInfo['lien1Rate']);
        $loannumber = trim($myFileInfo['loanNumber']);
        $mtgtype = trim($myFileInfo['mortgageOwner1']);
        $mtgpipayment = trim($myFileInfo['lien1Payment']);
        $paymentamount = trim($myFileInfo['lien2Payment']);
        $interestrate = trim($myFileInfo['lien1Rate']);
        $monthlypayment = trim($myFileInfo['lien1Payment']);
        $lien1Terms = trim($myFileInfo['lien1Terms']);
        $amortization = trim($myFileInfo['lien1Terms']);
        if ($amortization == '') {
            $amortization = LoanTerms::INTEREST_ONLY;
        }
        $homeValue = trim($myFileInfo['homeValue']);

        $bedrooms = trim($myFileInfo['howManyBedRoom']);
        $bathrooms = trim($myFileInfo['howManyBathRoom']);

        $mortgagepayment = $mtgpipayment;

        $coborrowerdob = Dates::formatDateWithRE($coborrowerdob, 'YMD', 'm/d/Y');
        $filecreateddate = Dates::formatDateWithRE($filecreateddate, 'YMD', 'm/d/Y');
        $saledate = Dates::formatDateWithRE($saledate, 'YMD', 'm/d/Y');

        if (array_key_exists($mtgtype, $glMortgageOwnerArray)) $mtgtype = $glMortgageOwnerArray[$mtgtype];
        if (array_key_exists($propertytype, GpropertyTypeNumbArray::$GpropertyTypeNumbArray)) $propertytype = GpropertyTypeNumbArray::$GpropertyTypeNumbArray[$propertytype];


        $closingdate = '';
        $desiredclosingdate = '';
        $borrowerveteran = '';
        $borrowersex = '';
        $borrowerrace = '';
        $borrowerethnicity = '';
        $borrowerfurnishthisinformation = '';
        $coborrowerfurnishthisinformation = '';
        $coborrowerethnicity = '';
        $coborrowerethnicitysub = '';
        $coborrowerrace = '';
        $coborrowerracesub = '';
        $coborrowersex = '';
        $coborrowerveteran = '';

        $borrowerethnicitysub = '';
        $borrowerracesub = '';
        $daysuntilclosing = '';
        $borrowerCreditScoringModelOfApplicant = '';
        $bDemoInfo = '';
        $coBorrowerCreditScoringModelOfApplicant = '';
        $CBDemoInfo = '';
        $actionTaken = '';
        $typeOfPurchaser = '';
        $reasonForDenial = '';

        $commissionInfo = exportClientFiles::$commissionInfo[$LMRId] ?? null;

        $QAInfo = null;
        if (array_key_exists($LMRId, exportClientFiles::$QAInfoArray)) {
            $QAInfo = exportClientFiles::$QAInfoArray[$LMRId];
        }

        if ($QAInfo) {
            $summondate = trim($QAInfo['summonDate']);
            $desiredclosingdate = trim($QAInfo['desiredClosingDate']); // For HMLOs


            $closingdate = Dates::formatDateWithRE(trim($QAInfo['closingDate']), 'YMD', 'm/d/Y');

            $desiredclosingdate = Dates::formatDateWithRE($desiredclosingdate, 'YMD', 'm/d/Y');

            /* Assign values to 'QA Info > Borrower's dynamic variables which are being created below while assigning values to Excel header fields. */
            if (isset($QAInfo['PublishBInfo'])) {
                $borrowerfurnishthisinformation = BaseHTML::getBorrowerInfoByIndex('furnishThisInformation', $QAInfo['PublishBInfo']);
            }
            if (isset($QAInfo['BEthnicity'])) {
                $borrowerethnicity = BaseHTML::getBorrowerInfoByIndex('ethnicity', $QAInfo['BEthnicity']);
            }
            if (isset($QAInfo['BRace'])) {
                $borrowerrace = BaseHTML::getBorrowerInfoByIndex('race', $QAInfo['BRace']);
            }
            if (isset($QAInfo['BGender'])) {
                $borrowersex = BaseHTML::getBorrowerInfoByIndex('sex', $QAInfo['BGender']);
            }
            //Borrower Veteran
            if (isset($QAInfo['BVeteran'])) {
                $borrowerveteran = BaseHTML::getBorrowerInfoByIndex('veteran', $QAInfo['BVeteran']);
            }
            //
            if (isset($QAInfo['bFiEthnicitySub'])) {
                $borrowerethnicitysub = BaseHTML::getBorrowerInfoByIndex('FIEthnicitySub', $QAInfo['bFiEthnicitySub']);
            }
            if (isset($QAInfo['bFiRaceSub'])) {
                $borrowerracesub = BaseHTML::getBorrowerInfoByIndex('FIRaceSub', $QAInfo['bFiRaceSub']);
            }
            if (isset($QAInfo['borrowerCreditScoringModelOfApplicant'])) {
                $borrowerCreditScoringModelOfApplicant = HMDAActionTaken::$creditScoringModel[$QAInfo['borrowerCreditScoringModelOfApplicant']] ?? '';
            }
            if (isset($QAInfo['actionTaken'])) {
                $actionTaken = HMDAActionTaken::$getHMDAActionTaken[$QAInfo['actionTaken']] ?? '';
            }
            if (isset($QAInfo['typeOfPurchaser'])) {
                $typeOfPurchaser = HMDAActionTaken::$typeOfPurchaser[$QAInfo['typeOfPurchaser']] ?? '';
            }

            $reasonForDenialArray = explode('~', $QAInfo['reasonForDenial']) ?? [];
            $seperator = '';
            foreach ($reasonForDenialArray as $value) {
                $reasonForDenial .= $seperator . (HMDAActionTaken::$reasonForDenial[$value] ?? '');
                $seperator = ' , ';
            }

            if (isset($QAInfo['bDemoInfo'])) {
                $bDemoInfo = BaseHTML::getBorrowerInfoByIndex('DemoInfo', $QAInfo['bDemoInfo']);
            }
            if (isset($QAInfo['coBorrowerCreditScoringModelOfApplicant'])) {
                $coBorrowerCreditScoringModelOfApplicant = HMDAActionTaken::$creditScoringModel[$QAInfo['coBorrowerCreditScoringModelOfApplicant']] ?? '';
            }
            if (isset($QAInfo['CBDDemoInfo'])) {
                $CBDemoInfo = BaseHTML::getCoBorrowerInfoByIndex('DemoInfo', $QAInfo['CBDDemoInfo']);

            }


            /* Assign values to 'QA Info > Co-Borrower's dynamic variables which are being created below while assigning values to Excel header fields. */
            if (isset($QAInfo['PublishCBInfo'])) {
                $coborrowerfurnishthisinformation = BaseHTML::getCoBorrowerInfoByIndex('furnishThisInformation', $QAInfo['PublishCBInfo']);
            }
            if (isset($QAInfo['CBEthnicity'])) {
                $coborrowerethnicity = BaseHTML::getCoBorrowerInfoByIndex('ethnicity', $QAInfo['CBEthnicity']);
            }
            if (isset($QAInfo['CBEthnicitySub'])) {
                $coborrowerethnicitysub = BaseHTML::getCoBorrowerInfoByIndex('FIEthnicitySub', $QAInfo['CBEthnicitySub']);
            }
            if (isset($QAInfo['CBRace'])) {
                $coborrowerrace = BaseHTML::getCoBorrowerInfoByIndex('race', $QAInfo['CBRace']);
            }
            if (isset($QAInfo['CBRaceSub'])) {
                $coborrowerracesub = BaseHTML::getCoBorrowerInfoByIndex('FIRaceSub', $QAInfo['CBRaceSub']);
            }
            if (isset($QAInfo['CBGender'])) {
                $coborrowersex = BaseHTML::getCoBorrowerInfoByIndex('sex', $QAInfo['CBGender']);
            }
            if (isset($QAInfo['CBVeteran'])) {
                $coborrowerveteran = BaseHTML::getCoBorrowerInfoByIndex('veteran', $QAInfo['CBVeteran']);
            }


            $dateDiff = [];
            $dateDiff['lastPaymentMade'] = date('Y-m-d');
            $dateDiff['futureDate'] = $closingdate;
            $daysuntilclosing = Integers::calculateNoOfDaysBehind($dateDiff);
            if ($daysuntilclosing < 0) {
                $daysuntilclosing = '';
            }
        }


        $branchInfo = null;
        if (array_key_exists($branchID, exportClientFiles::$fileBranchInfo)) {
            $branchInfo = exportClientFiles::$fileBranchInfo[$branchID];
        }
        if ($branchInfo) {
            $branchname = trim($branchInfo['LMRExecutive']);
            $branchcompany = trim($branchInfo['company']);
            $branchemail = trim($branchInfo['executiveEmail']);
            $branchcell = Strings::formatPhoneNumber(trim($branchInfo['cellNumber']));
            $branchfax = Strings::formatPhoneNumber(trim($branchInfo['fax']));
            $branchphone = Strings::formatPhoneNumber(trim($branchInfo['tollFree']));
        }

        $assignedStaff = null;
        if (array_key_exists($LMRId, exportClientFiles::$assignedStaffInfo ?? [])) {
            $assignedStaff = exportClientFiles::$assignedStaffInfo[$LMRId];
        }
        $employeesInfo = [];

        $employeeemail = null;
        $employeefax = null;
        $employeecell = null;
        $employeephone = null;
        $employeename = null;

        foreach ($assignedStaff ?? [] as $staff) {
            $employeeInfo = [];
            $empRole = trim($staff['role']);
            $employeename = trim($staff['processorName']);
            $employeephone = trim($staff['directPhone']);
            $employeecell = trim($staff['cellNumber']);
            $employeefax = trim($staff['fax']);
            $employeeemail = trim($staff['email']);

            if (in_array('Employee Name', exportClientFiles::$headerFieldsArray)) {
                $employeeInfo[] = $employeename;
            }
            if (in_array('Employee Phone', exportClientFiles::$headerFieldsArray)) {
                if (trim($employeephone)) {
                    $employeeInfo[] = 'Phone: ' . Strings::formatPhoneNumber($employeephone);
                    $employeephone = Strings::formatPhoneNumber($employeephone);
                }
            }
            if (in_array('Employee Cell', exportClientFiles::$headerFieldsArray)) {
                if (trim($employeecell)) {
                    $employeeInfo[] = 'Cell: ' . Strings::formatPhoneNumber($employeecell);
                    $employeecell = Strings::formatPhoneNumber($employeecell);
                }
            }
            if (in_array('Employee Fax', exportClientFiles::$headerFieldsArray)) {
                if (trim($employeefax)) {
                    $employeeInfo[] = 'Fax: ' . Strings::formatPhoneNumber($employeefax);
                    $employeefax = Strings::formatPhoneNumber($employeefax);
                }
            }
            if (in_array('Employee Email', exportClientFiles::$headerFieldsArray)) {
                if (trim($employeeemail)) {
                    $employeeInfo[] = 'Email: ' . $employeeemail;
                }
            }
            $employeesInfo[] = implode(',', $employeeInfo);
            /** Multiple employee info in one variable as assigned employee. **/
        }
        $assignedemployees = implode('; ', $employeesInfo);
        $propertyInfo = [];
        if (array_key_exists($LMRId, exportClientFiles::$filePropInfo)) {
            $propertyInfo = exportClientFiles::$filePropInfo[$LMRId];
        }

        exportClientFiles::$primaryPropertyInfo = Property::getPrimaryPropertyInfo($LMRId);
        // if(exportClientFiles::$primaryPropertyInfo){

        if (!exportClientFiles::$primaryPropertyInfo->propertyId) {
            exportClientFiles::$primaryPropertyInfo = new tblProperties();
        }

        $propertyaddress = exportClientFiles::$primaryPropertyInfo->propertyAddress;
        $propertycity = exportClientFiles::$primaryPropertyInfo->propertyCity;
        $propertystate = exportClientFiles::$primaryPropertyInfo->propertyState;
        $propertyzip = exportClientFiles::$primaryPropertyInfo->propertyZipCode;

        //  }

        $titleOrderedDate = '';
        if (count($propertyInfo) > 0) {

            $borroweroccupancy = trim($propertyInfo['isHouseProperty']);
            $annualpropertytax = trim($propertyInfo['annualPropTaxes1']);

            $titleOrderedDate = trim($propertyInfo['titleOrderedDate']);
            $titleOrderedDate = Dates::formatDateWithRE($titleOrderedDate, 'YMD', 'm/d/Y');
        }

        $sqft = exportClientFiles::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertySqFt;
        $totalsqft = $sqft;
        $listingRealtorInfo = null;
        if (array_key_exists(trim($LMRId), exportClientFiles::$listingRealtorInfoArray)) {
            $listingRealtorInfo = exportClientFiles::$listingRealtorInfoArray[$LMRId];
            $myFileInfo['listingRealtorInfo'] = $listingRealtorInfo;
        }

        if ($listingRealtorInfo) {
            $projectedforeclosuredate = $listingRealtorInfo['foreclosureDate'];
            $projectedforeclosuredate = Dates::formatDateWithRE($projectedforeclosuredate, 'YMD', 'm/d/Y');
        }

        $listingRealtorInfo2 = null;
        if (array_key_exists(trim($LMRId), exportClientFiles::$listingRealtorInfo2Array)) {
            $listingRealtorInfo2 = exportClientFiles::$listingRealtorInfo2Array[$LMRId];
        }

        $rehabbudget = '';
        $rehabbedvalue = 0;
        $recommendedoffer = 0;
        if ($listingRealtorInfo2) {
            $rehabbudget = Currency::formatDollarAmountWithDecimal($listingRealtorInfo2['intAssRehabBudget']);
            $rehabbedvalue = Currency::formatDollarAmountWithDecimal($listingRealtorInfo2['intAssRehabbedValue']);
            $recommendedoffer = Currency::formatDollarAmountWithDecimal($listingRealtorInfo2['intAssRecommendedOffer']);
        }

        $lien1Rate = $currentrate;
        $HMLOlistingRealtorInfo = null;
        if (array_key_exists(trim($LMRId), exportClientFiles::$HMLOlistingRealtorInfoArray)) {
            $HMLOlistingRealtorInfo = exportClientFiles::$HMLOlistingRealtorInfoArray[$LMRId];
        }

        $zillowvalue = null;
        $rehabValue3 = null;
        $BPO1Value = null;
        if ($HMLOlistingRealtorInfo) {
            $appraiser1Value = trim($HMLOlistingRealtorInfo['appraiser1Value']);
            $appraiser2Value = trim($HMLOlistingRealtorInfo['appraiser2Value']);
            $rehabValue = trim($HMLOlistingRealtorInfo['rehabValue']);
            $rehabValue2 = trim($HMLOlistingRealtorInfo['rehabValue2']);
            $rehabValue3 = Currency::formatDollarAmountWithDecimal(trim($HMLOlistingRealtorInfo['rehabValue3']));
            $rehabValue4 = Currency::formatDollarAmountWithDecimal(trim($HMLOlistingRealtorInfo['rehabValue4']));
            $rehabValue5 = Currency::formatDollarAmountWithDecimal(trim($HMLOlistingRealtorInfo['rehabValue5']));
            $BPO1Value = trim($HMLOlistingRealtorInfo['BPO1Value']);
            $BPO2Value = trim($HMLOlistingRealtorInfo['BPO2Value']);
            $BPO3Value = trim($HMLOlistingRealtorInfo['BPO3Value']);

            $purchaseprice = trim($HMLOlistingRealtorInfo['costBasis']);
            $assessedasisvalue = trim($HMLOlistingRealtorInfo['assessedValue']);
            $zillowvalue = trim($HMLOlistingRealtorInfo['zillowValue']);
        }
        $property1zillowvalue = $zillowvalue;

        $valBreak = '';
        if ($appraiser1Value && $appraiser1Value != '0.00') {
            $appraisalasisvalue = $appraiser1Value;
            $valBreak = ' , ';
        }
        if ($appraiser2Value && $appraiser2Value != '0.00') {
            $appraisalasisvalue .= $valBreak . $appraiser2Value;
        }

        $valBreak = '';
        if ($rehabValue && $rehabValue != '0.00') {
            $appraisalrehabbedvalue = $rehabValue;
            $valBreak = ' , ';
        }

        $valBreak = '';
        if ($rehabValue3 && $rehabValue3 != '0.00') {
            $realtorbporehabbedvalue = $rehabValue3;
            $valBreak = ' , ';
        }
        if ($rehabValue4 && $rehabValue4 != '0.00') {
            $realtorbporehabbedvalue .= $valBreak . $rehabValue4;
            $valBreak = ' , ';
        }

        $valBreak = '';
        if ($BPO1Value && $BPO1Value != '0.00') {
            $realtorbpoasisvalue = $BPO1Value;
            $valBreak = ' , ';
        }
        if ($BPO2Value && $BPO2Value != '0.00') {
            $realtorbpoasisvalue .= $valBreak . $BPO2Value;
            $valBreak = ' , ';
        }
        if ($BPO3Value && $BPO3Value != '0.00') {
            $realtorbpoasisvalue .= $valBreak . $BPO3Value;
        }

        $HMLOPropInfo = null;
        if (array_key_exists(trim($LMRId), exportClientFiles::$HMLOPropInfoArray)) {
            $HMLOPropInfo = exportClientFiles::$HMLOPropInfoArray[$LMRId];
        }

        $HMLOInsuranceInfo = null;
        if (array_key_exists(trim($LMRId), exportClientFiles::$HMLOInsuranceInfoArray)) {
            $HMLOInsuranceInfo = exportClientFiles::$HMLOInsuranceInfoArray[$LMRId];
        }

        $budgetAndDrawsInfo = null;
        if (array_key_exists(trim($LMRId), exportClientFiles::$budgetAndDrawsInfoArray)) {
            $budgetAndDrawsInfo = exportClientFiles::$budgetAndDrawsInfoArray[$LMRId];
            $myFileInfo['budgetAndDrawsInfo'] = $budgetAndDrawsInfo; // for calculations
        }

        $filepaydownInfo = null;
        if (array_key_exists(trim($LMRId), exportClientFiles::$paydownInfoArray)) {
            $filepaydownInfo = exportClientFiles::$paydownInfoArray[$LMRId];
        }
        $fileInternalLP = null;
        if (array_key_exists(trim($LMRId), exportClientFiles::$internalLPArray)) {
            $fileInternalLP = exportClientFiles::$internalLPArray[$LMRId];
        }

        $exitstrategy = null;
        $isBlanketLoan = null;
        $paymentReserves = null;
        $requiredConstruction = null;
        $contingencyReserve = null;
        $annualPremium = null;

        if ($HMLOPropInfo) {
            $transactionaltype = trim($HMLOPropInfo['typeOfHMLOLoanRequesting'] ?? 0);
            $approvedloanamount = trim($HMLOPropInfo['approvedLoanAmt'] ?? 0);
            $maxAmtToPutDown = trim($HMLOPropInfo['maxAmtToPutDown'] ?? 0);
            $annualPremium = trim($HMLOPropInfo['annualPremium'] ?? 0);
            $paymentReserves = trim($HMLOPropInfo['paymentReserves'] ?? 0);
            $requiredConstruction = trim($HMLOPropInfo['requiredConstruction'] ?? 0);
            $contingencyReserve = trim($HMLOPropInfo['contingencyReserve'] ?? 0);
            $percentageTotalLoan = trim($HMLOPropInfo['percentageTotalLoan'] ?? 0);
            $isBlanketLoan = trim($HMLOPropInfo['isBlanketLoan']);
            $isthisloanbeingcrosscollateralized = trim($HMLOPropInfo['isBlanketLoan']);
            $transactiontype = $transactionaltype;
            $spcf_annualPremium_cal = Strings::replaceCommaValues(trim($HMLOPropInfo['annualPremium']));
            $escrowedinsurance = $annualPremium;

            $templienposition = trim($HMLOPropInfo['lienPosition']);
            $exitstrategy = trim($HMLOPropInfo['exitStrategy']);
            $rehabrequired = trim($HMLOPropInfo['propertyNeedRehab']);
            $estateheldin = trim($HMLOPropInfo['HMLOEstateHeldIn']);
            $loanterm = trim($HMLOPropInfo['loanTerm']);
            if ($loanterm == 'Revolving') {
                doNothing();
            } else if ($loanterm) {
                $loanTermArray = explode(' ', $loanterm, 2);
            }
            $servicingnumber = trim($HMLOPropInfo['servicingNumber']);
            $payoffdate = Dates::formatDateWithRE($HMLOPropInfo['payOffDate'], 'YMD', 'm/d/Y');
            $loansaleagreement = Dates::formatDateWithRE($HMLOPropInfo['loanSaleDate'], 'YMD', 'm/d/Y');
            $foreclosuredate = trim($HMLOPropInfo['foreclosureDate']);
            $foreclosuredate = Dates::formatDateWithRE($foreclosuredate, 'YMD', 'm/d/Y');


            $accrualtype = trim($HMLOPropInfo['accrualType']);
            if ($accrualtype == accrualTypes::ACCRUAL_TYPE_30_360) {
                $accrualtype = accrualTypes::ACCRUAL_TYPE_VALUE_30_360;
            } elseif ($accrualtype == accrualTypes::ACCRUAL_TYPE_ACTUAL_365) {
                $accrualtype = accrualTypes::ACCRUAL_TYPE_ACTUAL_VALUE_365;
            }
        }


        $acquisitiondownpayment = $maxAmtToPutDown;
        if ($transactionaltype != 'Blanket Loan') {
            if (array_key_exists($LMRId, exportClientFiles::$incomeInfoArray)) {
                $annualpropertytax = exportClientFiles::$incomeInfoArray[$LMRId]['taxes1'];
            }
        }

        $fileHMLOInfo = null;
        if (array_key_exists(trim($LMRId), exportClientFiles::$fileHMLOInfoArray)) {
            $fileHMLOInfo = exportClientFiles::$fileHMLOInfoArray[$LMRId];
            $myFileInfo['fileHMLOInfo'] = $fileHMLOInfo;
        }

        $creditscorerange = null;
        if ($fileHMLOInfo > 0) {
            $rehabconstructioncost = trim($fileHMLOInfo['rehabCost']);
            $creditscorerange = trim($fileHMLOInfo['borCreditScoreRange']);
            $experian = trim($fileHMLOInfo['borExperianScore']);
            $midficoscore = trim($fileHMLOInfo['midFicoScore']);
            $equifax = trim($fileHMLOInfo['borEquifaxScore']);
            $transunion = trim($fileHMLOInfo['borTransunionScore']);
        }


        $assetsInfo = $fileLOAssetsInfo = [];
        if (array_key_exists(trim($LMRId), exportClientFiles::$fileAssetsInfoArray)) {
            $assetsInfo = exportClientFiles::$fileAssetsInfoArray[$LMRId];
        }

        if (count(exportClientFiles::$fileLOAssetsInfoArray) > 0) {
            if (array_key_exists(trim($LMRId), exportClientFiles::$fileLOAssetsInfoArray)) {
                $fileLOAssetsInfo = exportClientFiles::$fileLOAssetsInfoArray[trim($LMRId)];
            }
        }

        $assetCheckingAccounts = $assetSavingMoneyMarket = $assetStocks = $assetIRAAccounts = $assetESPOAccounts = $assetHome = $assetORE = 0;
        $assetSR = $assetCars = $assetLifeInsurance = $assetTotalCashBankAcc = $assetTotalRetirementValue = $assetAvailabilityLinesCredit = $totalcashinbankaccounts = 0;
        $assetOther = $assetCash = $assetHomeOwed = $assetOREOwed = $assetSROwed = $assetCarsOwed = $otherAmtOwed = 0;
        $totalAssetsOwed = 0;
        $networthOfBusinessOwned = $otherAssets = $automobilesOwned3x1 = $automobilesOwned3x = 0;

        if (count($fileLOAssetsInfo) > 0) {
            $networthOfBusinessOwned = trim($fileLOAssetsInfo['networthOfBusinessOwned']);
            $automobilesOwned3x = trim($fileLOAssetsInfo['automobilesOwned3x']);
            $automobilesOwned3x1 = trim($fileLOAssetsInfo['automobilesOwned3x1']);
            $otherAssets = trim($fileLOAssetsInfo['otherAssets']);
        }

        if (count($assetsInfo) > 0) {
            $assetCheckingAccounts = trim($assetsInfo['assetCheckingAccounts']);
            $assetSavingMoneyMarket = trim($assetsInfo['assetSavingMoneyMarket']);
            $assetStocks = trim($assetsInfo['assetStocks']);
            $assetIRAAccounts = trim($assetsInfo['assetIRAAccounts']);
            $assetESPOAccounts = trim($assetsInfo['assetESPOAccounts']);
            $assetHome = trim($assetsInfo['assetHome']);
            $assetORE = trim($assetsInfo['assetORE']);
            $assetSR = trim($assetsInfo['assetSR']);
            $assetCars = trim($assetsInfo['assetCars']);
            $assetLifeInsurance = trim($assetsInfo['assetLifeInsurance']);
            $assetTotalCashBankAcc = trim($assetsInfo['assetTotalCashBankAcc']);
            $totalcashinbankaccounts = trim($assetsInfo['assetTotalCashBankAcc']);
            $assetTotalRetirementValue = trim($assetsInfo['assetTotalRetirementValue']);
            $assetAvailabilityLinesCredit = trim($assetsInfo['assetAvailabilityLinesCredit']);
            $assetOther = trim($assetsInfo['assetOther']);
            $assetCash = trim($assetsInfo['assetCash']);
            $assetHomeOwed = trim($assetsInfo['assetHomeOwed']);
            $assetOREOwed = trim($assetsInfo['assetOREOwed']);
            $assetSROwed = trim($assetsInfo['assetSROwed']);
            $assetCarsOwed = trim($assetsInfo['assetCarsOwed']);
            $otherAmtOwed = trim($assetsInfo['otherAmtOwed']);
        }

        $totalAssets = Strings::replaceCommaValues($assetCheckingAccounts) + Strings::replaceCommaValues($assetSavingMoneyMarket);
        $totalAssets += Strings::replaceCommaValues($assetStocks) + Strings::replaceCommaValues($assetIRAAccounts);
        $totalAssets += Strings::replaceCommaValues($assetESPOAccounts) + Strings::replaceCommaValues($assetHome);
        $totalAssets += Strings::replaceCommaValues($assetORE) + Strings::replaceCommaValues($assetSR) + Strings::replaceCommaValues($assetCars);
        $totalAssets += Strings::replaceCommaValues($assetLifeInsurance) + Strings::replaceCommaValues($assetOther);
        $totalAssets += Strings::replaceCommaValues($assetTotalCashBankAcc) + Strings::replaceCommaValues($assetTotalRetirementValue) + Strings::replaceCommaValues($assetAvailabilityLinesCredit);
        $totalAssets += Strings::replaceCommaValues($assetCash) + Strings::replaceCommaValues($networthOfBusinessOwned) + Strings::replaceCommaValues($otherAssets) + Strings::replaceCommaValues($automobilesOwned3x);
        $totalAssets = round($totalAssets, 2);
        $totalAssetsOwed += Strings::replaceCommaValues($assetHomeOwed);
        $totalAssetsOwed += Strings::replaceCommaValues($assetOREOwed) + Strings::replaceCommaValues($assetSROwed) + Strings::replaceCommaValues($assetCarsOwed) + Strings::replaceCommaValues($otherAmtOwed) + Strings::replaceCommaValues($automobilesOwned3x1);
        $totalAssetsOwed = round($totalAssetsOwed, 2);
        $totalAssetsNetValue = Strings::replaceCommaValues($totalAssets) - Strings::replaceCommaValues($totalAssetsOwed);
        $totalAutoMobiles = Strings::replaceCommaValues($automobilesOwned3x1) + Strings::replaceCommaValues($assetCars) + Strings::replaceCommaValues($assetCarsOwed);
        $totalAssetsNetValue = round($totalAssetsNetValue, 2);

        $networth = $totalAssetsNetValue;
        $willborrowerpersonallyguaranteethisloan = '';
        if (array_key_exists(trim($LMRId), exportClientFiles::$fileHMLOBackGroundInfoArray)) {
            $willborrowerpersonallyguaranteethisloan = exportClientFiles::$fileHMLOBackGroundInfoArray[$LMRId]['isBorPersonallyGuaranteeLoan'];
        }

        $fileHMLONewLoanInfo = null;
        if (array_key_exists(trim($LMRId), exportClientFiles::$fileHMLONewLoanInfoArray)) {
            $fileHMLONewLoanInfo = exportClientFiles::$fileHMLONewLoanInfoArray[$LMRId];
            ksort($fileHMLONewLoanInfo);
        }

        $spcf_hoafees = 0;
        $closingCostFinanced = null;
        $payOffMortgage1 = null;
        $payOffMortgage2 = null;
        $payOffOutstandingTaxes = null;
        $payOffOtherOutstandingAmounts = null;
        $cashOutAmt = null;
        $interestChargedFromDate = null;
        $interestChargedEndDate = null;
        $taxImpoundsMonth = null;
        $taxImpoundsMonthAmt = null;
        $paymentbased = 'Total Loan Amount';

        if ($fileHMLONewLoanInfo) {
            $originator = trim($fileHMLONewLoanInfo['HMLOLender']);
            $closingCostFinanced = trim($fileHMLONewLoanInfo['closingCostFinanced']);
            $payOffMortgage1 = trim($fileHMLONewLoanInfo['payOffMortgage1']);
            $payOffMortgage2 = trim($fileHMLONewLoanInfo['payOffMortgage2']);
            $payOffOutstandingTaxes = trim($fileHMLONewLoanInfo['payOffOutstandingTaxes']);
            $payOffOtherOutstandingAmounts = trim($fileHMLONewLoanInfo['payOffOtherOutstandingAmounts']);
            $cashOutAmt = trim($fileHMLONewLoanInfo['cashOutAmt']);
            $actualRentsInPlace = Strings::replaceCommaValues(trim($fileHMLONewLoanInfo['actualRentsInPlace']));
            $lessActualExpenses = trim($fileHMLONewLoanInfo['lessActualExpenses']);
            $vacancyFactor = Strings::replaceCommaValues(trim($fileHMLONewLoanInfo['vacancyFactor']));

            $originationpointsrate = Currency::formatDollarAmountWithDecimal(trim($fileHMLONewLoanInfo['originationPointsRate']));
            $originationpointsvalue = trim($fileHMLONewLoanInfo['originationPointsValue']);
            $brokerpointsrate = Currency::formatDollarAmountWithDecimal(trim($fileHMLONewLoanInfo['brokerPointsRate']));
            $brokerpointsvalue = trim($fileHMLONewLoanInfo['brokerPointsValue']);
            $interestChargedFromDate = trim($fileHMLONewLoanInfo['interestChargedFromDate']);

            //Fix for the calculation in $totalFeesAndCost to match the loan file value
            if (Dates::IsEmpty($interestChargedFromDate)) {
                $interestChargedFromDate = '';
            }
            $interestChargedEndDate = trim($fileHMLONewLoanInfo['interestChargedEndDate']);
            if (Dates::IsEmpty($interestChargedEndDate)) {
                $interestChargedEndDate = '';
            }
            $thirdPartyFees = trim($fileHMLONewLoanInfo['thirdPartyFees']);
            $taxImpoundsMonth = trim($fileHMLONewLoanInfo['taxImpoundsMonth']);
            $taxImpoundsMonthAmt = trim($fileHMLONewLoanInfo['taxImpoundsMonthAmt']);
            $loantermsexpiredate = Dates::formatDateWithRE($fileHMLONewLoanInfo['loanTermExpireDate'], 'YMD', 'm/d/Y');
            $noofpropertiescollateralized = trim($fileHMLONewLoanInfo['noOfPropertiesAcquiring']);
            $initialdrawamount = trim($fileHMLONewLoanInfo['initialAdvance']);
            $closingcostfinancingfee = trim($fileHMLONewLoanInfo['closingCostFinancingFee']);
            $attorneyfee = trim($fileHMLONewLoanInfo['attorneyFee']);
            $applicationfee = trim($fileHMLONewLoanInfo['applicationFee']);
            $appraisalfee = trim($fileHMLONewLoanInfo['appraisalFee']);
            $processingfees = trim($fileHMLONewLoanInfo['processingFee']);
            $drawsfee = trim($fileHMLONewLoanInfo['drawsFee']);
            $drawssetupfee = trim($fileHMLONewLoanInfo['drawsSetUpFee']);
            $valuationbpo = trim($fileHMLONewLoanInfo['valuationBPOFee']);
            $valuationavm = trim($fileHMLONewLoanInfo['valuationAVMFee']);
            $creditreport = trim($fileHMLONewLoanInfo['creditReportFee']);
            $backgroundcheck = trim($fileHMLONewLoanInfo['backgroundCheckFee']);
            $taxservice = trim($fileHMLONewLoanInfo['taxServiceFee']);
            $documentpreparation = trim($fileHMLONewLoanInfo['documentPreparationFee']);
            $wirefee = trim($fileHMLONewLoanInfo['wireFee']);
            $servicingsetupfee = trim($fileHMLONewLoanInfo['servicingSetUpFee']);
            $floodcertificate = trim($fileHMLONewLoanInfo['floodCertificateFee']);
            $floodservice = trim($fileHMLONewLoanInfo['floodServiceFee']);
            $inspectionfees = trim($fileHMLONewLoanInfo['inspectionFees']);
            $projectfeasibility = trim($fileHMLONewLoanInfo['projectFeasibility']);
            $duediligence = trim($fileHMLONewLoanInfo['dueDiligence']);
            $uccliensearch = trim($fileHMLONewLoanInfo['UccLienSearch']);
            $lendercredittooffset3rdpartyfees = trim($fileHMLONewLoanInfo['thirdPartyFees']);
            $other = trim($fileHMLONewLoanInfo['otherFee']);
            $elevatorMaintenance = trim($fileHMLONewLoanInfo['elevatorMaintenance']);
            $replacementReserves = trim($fileHMLONewLoanInfo['replacementReserves']);
            $commonAreaUtilities = trim($fileHMLONewLoanInfo['commonAreaUtilities']);
            $escrowfees = trim($fileHMLONewLoanInfo['escrowFees']);
            $recordingfee = trim($fileHMLONewLoanInfo['recordingFee']);
            $underwritingfees = trim($fileHMLONewLoanInfo['underwritingFees']);
            $propertytax = trim($fileHMLONewLoanInfo['propertyTax']);
            $discountfee = trim($fileHMLONewLoanInfo['bufferAndMessengerFee']);
            $travelnotaryfee = trim($fileHMLONewLoanInfo['travelNotaryFee']);
            $prepaidinterest = trim($fileHMLONewLoanInfo['prePaidInterest']);
            $realestatetaxes = trim($fileHMLONewLoanInfo['realEstateTaxes']);
            $wiretransferfeetotitle = trim($fileHMLONewLoanInfo['wireTransferFeeToTitle']);
            $insurancepremium = trim($fileHMLONewLoanInfo['insurancePremium']);
            $payofflienscreditors = trim($fileHMLONewLoanInfo['payOffLiensCreditors']);
            $ofunits = trim($fileHMLONewLoanInfo['noUnitsOccupied']);
            $isLoanPaymentAmt = trim($fileHMLONewLoanInfo['isLoanPaymentAmt']);
            $amortizationtype = trim($fileHMLONewLoanInfo['amortizationType']);
            if ($isLoanPaymentAmt == 'SMP') {
                $amortization = '';
            }

            switch ($isLoanPaymentAmt) {
                case 'SMP':
                    $paymentbased = 'Set Manual Payment';
                    break;
                case LoanTerms::ILA:
                    $paymentbased = 'Current Loan Balance';
                    break;
                case LoanTerms::TLA:
                default:
                    $paymentbased = 'Total Loan Amount';
                    break;
            }
            $cashflowhoafees = Currency::formatDollarAmountWithDecimal(trim($fileHMLONewLoanInfo['spcf_hoafees']));
            $grosspotentialincome = Currency::formatDollarAmountWithDecimal(trim($fileHMLONewLoanInfo['actualRentsInPlace']));
            $prepaymentpenaltyoptions = trim($fileHMLONewLoanInfo['prePaymentSelectVal']);
            $vacancy = ($vacancyFactor * $actualRentsInPlace) / 100;
            $exitfeepoints = Currency::formatDollarAmountWithDecimal(trim($fileHMLONewLoanInfo['exitFeePoints']));
            $exitfeeamount = Currency::formatDollarAmountWithDecimal(trim($fileHMLONewLoanInfo['exitFeeAmount']));
            $bufferAndMessengerFee = trim($fileHMLONewLoanInfo['bufferAndMessengerFee']);
            $valuationAVMFee = trim($fileHMLONewLoanInfo['valuationAVMFee']);
            $floodCertificateFee = trim($fileHMLONewLoanInfo['floodCertificateFee']);
            $backgroundCheckFee = trim($fileHMLONewLoanInfo['backgroundCheckFee']);
            $creditReportFee = trim($fileHMLONewLoanInfo['creditReportFee']);

            $extensionoptionterms = (trim($fileHMLONewLoanInfo['extensionOption']));
            $extensionoptionrate = (trim($fileHMLONewLoanInfo['extensionRatePercentage']));
            $spcf_hoafees = (trim($fileHMLONewLoanInfo['spcf_hoafees']));
            $paymentFrequency = glpaymentFrequency::$glpaymentFrequency[$fileHMLONewLoanInfo['paymentFrequency']] ?? '';
        }

        $afterrepairvalue = Currency::formatDollarAmountWithDecimal($assessedasisvalue);
        $asisvalue = Currency::formatDollarAmountWithDecimal($homevalue);

        if (array_key_exists(trim($LMRId), exportClientFiles::$HMLOListOfRepairsArray)) {
            $HMLOListOfRepairs = exportClientFiles::$HMLOListOfRepairsArray[$LMRId];
        }
        $fileHMLOEntityInfo = null;
        if (array_key_exists(trim($LMRId), exportClientFiles::$fileHMLOEntityInfoArray)) {
            $fileHMLOEntityInfo = exportClientFiles::$fileHMLOEntityInfoArray[$LMRId];
        }

        $isBorBorrowedDownPayment = null;
        $insImpoundsMonth = null;
        $taxImpoundsFee = null;
        $propertyNeedRehab = null;
        $haveBorSquareFootage = null;
        $additionalPropertyRestrictions = null;
        $acceptedPurchase = null;
        $haveCurrentLoanBal = null;
        $doYouHaveInvoiceToFactor = null;
        $purchaseCloseDate = null;
        $isThisGroundUpConstruction = null;
        $insImpoundsMonthAmt = null;
        $insImpoundsFee = null;
        $activeTabExport = null;

        HMLOLoanTermsCalculation::Clear();
        HMLOLoanTermsCalculation::Init(
            isset($URLPOSTING),
            exportClientFiles::$fileInfoArray,
            Strings::replaceCommaValues($insImpoundsMonth),
            Strings::replaceCommaValues($taxImpoundsFee),
            $isBorBorrowedDownPayment,
            $propertyNeedRehab,
            $haveBorSquareFootage,
            $additionalPropertyRestrictions,
            $exitstrategy,
            $acceptedPurchase,
            $haveCurrentLoanBal,
            $doYouHaveInvoiceToFactor,
            Strings::Numeric($maxAmtToPutDown),
            Strings::replaceCommaValues($closingCostFinanced),
            Strings::replaceCommaValues($payOffMortgage1),
            Strings::replaceCommaValues($payOffMortgage2),
            Strings::replaceCommaValues($payOffOutstandingTaxes),
            Strings::replaceCommaValues($payOffOtherOutstandingAmounts),
            Strings::replaceCommaValues($cashOutAmt),
            $filepaydownInfo,
            $lien1Terms,
            $purchaseCloseDate,
            Strings::replaceCommaValues(exportClientFiles::$incomeInfoArray[$LMRId]['taxes1']),
            $isBlanketLoan,
            $isThisGroundUpConstruction,
            $interestChargedFromDate,
            $interestChargedEndDate,
            Strings::replaceCommaValues($taxImpoundsMonth),
            Strings::replaceCommaValues($taxImpoundsMonthAmt),
            Strings::replaceCommaValues($insImpoundsMonthAmt),
            Strings::replaceCommaValues($insImpoundsFee),
            Strings::replaceCommaValues($paymentReserves),
            Strings::replaceCommaValues($requiredConstruction),
            Strings::replaceCommaValues($contingencyReserve),
            $HMLOPropInfo,
            $fileHMLONewLoanInfo,
            $LMRId,
            $activeTabExport,
            Strings::replaceCommaValues($percentageTotalLoan)
        );


        $totalListOfRepairs = 0;
        foreach (rehabRepairList::getCostFields() as $rehabCost) {
            $totalListOfRepairs += Strings::replaceCommaValues($HMLOListOfRepairs[$rehabCost] ?? 0);
        }

        $totalestimatedrehabcost = round(Strings::replaceCommaValues($totalListOfRepairs), 2);
        if ($assessedasisvalue > 0) {
            $tempTotal = Strings::replaceCommaValues($approvedloanamount) / Strings::replaceCommaValues($assessedasisvalue);
        }

        $paymentduedate = date('Y') . '-' . date('m') . '-' . trim($HMLOPropInfo['paymentDue']);
        $paymentduedate = Dates::formatDateWithRE($paymentduedate, 'YMD', 'm/d/Y');
        $amountdue = $HMLOPropInfo['amountPastDueOrOwed'];

        $dailyInteresInArray = [
            'totalLoanAmount' => HMLOLoanTermsCalculation::$totalLoanAmount,
            'lien1Rate' => $lien1Rate,
        ];
        $totalDailyInterestCharge = proposalFormula::calculateTotalDailyInterestCharge($dailyInteresInArray);

        $payOffAmountArray = [
            'paymentduedate' => $paymentduedate,
            'payOffDate' => $payoffdate,
            'currentLoanBalance' => HMLOLoanTermsCalculation::$currentLoanBalance,
            'totalDailyInterestCharge' => $totalDailyInterestCharge,
            'totalMonthlyPayment' => HMLOLoanTermsCalculation::$totalMonthlyPayment,
            'amountdue' => $amountdue,
        ];
        $payoffamount = proposalFormula::calculatePayOffAmount($payOffAmountArray);
        $payoffamount = Currency::formatDollarAmountWithDecimal($payoffamount);

        /** For HMLO XLS export fields added By Suresh K - (PT #: *********)**/
        /** Hard Money Module Data Export Changes END **/

        $shortSaleInfo = null;
        if (array_key_exists($LMRId, exportClientFiles::$shortSaleInfoArray)) {
            $shortSaleInfo = exportClientFiles::$shortSaleInfoArray[$LMRId];
        }

        if ($shortSaleInfo) {
            $zillowrentvalue = Currency::formatDollarAmountWithDecimal($shortSaleInfo['zillowRentValue']);
            $listingdate = $shortSaleInfo['listingDate'];
            $contractdate1 = $shortSaleInfo['contractDate1'];
            $closingdate1 = $shortSaleInfo['closingDate1'];
            $contractdate2 = $shortSaleInfo['contractDate2'];
            $closingdate2 = $shortSaleInfo['closingDate2'];
            $contractdate3 = $shortSaleInfo['contractDate3'];
            $closingdate3 = $shortSaleInfo['closingDate3'];
        }

        if (array_key_exists($LMRId, exportClientFiles::$CMAAnalysisInfoArray)) {
            exportClientFiles::$CMAAnalysisInfo = exportClientFiles::$CMAAnalysisInfoArray[$LMRId];
        }
        $estimatedValue = '';
        $highPrice = '';
        $suggestedListPrice = '';
        $quickResalePrice = '';
        $salePrice = '';
        $monthsontheMLS = '';
        $averageListingPrice = '';
        $cmaDescription = '';
        foreach (exportClientFiles::$CMAAnalysisInfo as $cm => $cmainfo) {

            $estimatedValue = trim($cmainfo['cmaEstimatedValue']);
            $highPrice = trim($cmainfo['cmaHighPrice']);
            $suggestedListPrice = trim($cmainfo['cmaSuggListPrice']);
            $quickResalePrice = trim($cmainfo['cmaQuickResalePrice']);
            $salePrice = trim($cmainfo['cmaSalePrice']);
            $monthsontheMLS = trim($cmainfo['cmaMonthsOnMLS']);
            $averageListingPrice = trim($cmainfo['cmaAverageListingPrice']);
            $cmaDescription = trim($cmainfo['cmaDesc']);

            $appEmp = '';
            if ($cm > 0) {
                $cmaanalysis .= '<br><br>';
            }

            if (in_array('Estimated Value', exportClientFiles::$selectedFieldsArray)) {
                $cmaanalysis .= $appEmp . 'Estimated Value: ' . $estimatedValue;
                $appEmp = ', ';
            }
            if (in_array('High Price', exportClientFiles::$selectedFieldsArray)) {
                $cmaanalysis .= $appEmp . 'High Price: ' . $highPrice;
                $appEmp = ', ';
            }
            if (in_array('Suggested List Price', exportClientFiles::$selectedFieldsArray)) {
                $cmaanalysis .= $appEmp . 'Suggested List Price: ' . $suggestedListPrice;
                $appEmp = ', ';
            }
            if (in_array('Quick Resale Price', exportClientFiles::$selectedFieldsArray)) {
                $cmaanalysis .= $appEmp . 'Quick Resale Price: ' . $quickResalePrice;
                $appEmp = ', ';
            }
            if (in_array('Sale Price', exportClientFiles::$selectedFieldsArray)) {
                $cmaanalysis .= $appEmp . 'Sale Price: ' . $salePrice;
                $appEmp = ', ';
            }
            if (in_array('Months on the MLS', exportClientFiles::$selectedFieldsArray)) {
                $cmaanalysis .= $appEmp . 'Months on the MLS: ' . $monthsontheMLS;
                $appEmp = ', ';
            }
            if (in_array('Average Listing Price', exportClientFiles::$selectedFieldsArray)) {
                $cmaanalysis .= $appEmp . 'Average Listing Price: ' . $averageListingPrice;
                $appEmp = ', ';
            }
            if (in_array('CMA Description', exportClientFiles::$selectedFieldsArray)) {
                $cmaanalysis .= $appEmp . 'Description: ' . $cmaDescription;
            }
        }

        $listinghistory = '';
        if (array_key_exists($LMRId, exportClientFiles::$listingHistoryInfoArray ?? [])) {
            $listingHistoryInfo = exportClientFiles::$listingHistoryInfoArray[$LMRId];
        }
        foreach ($listingHistoryInfo ?? [] as $tl => $info) {
            $mlsNo = trim($info['mlsNo']);
            $listingDate = trim($info['listingDate']);
            $listingPrice = trim($info['listingPrice']);
            $listingNotes = trim($info['listingNotes']);

            $listingDate = Dates::formatDateWithRE($listingDate, 'YMD', 'm/d/Y');


            $appEmp = '';
            if ($tl > 0) {
                $listinghistory .= '<br><br>';
            }

            if (in_array('MLS Number', exportClientFiles::$selectedFieldsArray)) {
                $listinghistory .= $appEmp . 'MLS #: ' . $mlsNo;
                $appEmp = ', ';
            }
            if (in_array('Listing History Date', exportClientFiles::$selectedFieldsArray)) {
                $listinghistory .= $appEmp . 'Listing Date: ' . $listingDate;
                $appEmp = ', ';
            }
            if (in_array('Listing History Price', exportClientFiles::$selectedFieldsArray)) {
                $listinghistory .= $appEmp . 'Listing Price: ' . $listingPrice;
                $appEmp = ', ';
            }
            if (in_array('Listing History Notes', exportClientFiles::$selectedFieldsArray)) {
                $listinghistory .= $appEmp . 'Listing Notes: ' . $listingNotes;
                $appEmp = ', ';
            }
        }
        $services = '';
        $ll1 = 0;
        $servicesArray = [];
        if (array_key_exists($LMRId, exportClientFiles::$fileClientTypeArray)) {
            $clientTypeArray = exportClientFiles::$fileClientTypeArray[$LMRId];
        }
        foreach ($clientTypeArray ?? [] as $l1 => $clientType) {
            if ($ll1 > 0) $services .= ', ';
            if (array_key_exists($clientType['ClientType'], exportClientFiles::$glLMRClientTypeArray)) {
                $services .= exportClientFiles::$glLMRClientTypeArray[trim($clientType['ClientType'])];
                $servicesArray[] = trim($clientType['ClientType']);
                $ll1++;
            }
        }

        $incomeInfo = null;
        if (array_key_exists($LMRId, exportClientFiles::$incomeInfoArray)) {
            $incomeInfo = exportClientFiles::$incomeInfoArray[$LMRId];
        }

        $totalpitia = 0;
        $taxes1 = null;
        $borrowertotalexpenses = null;
        $coborrowernetincome = null;
        $coborrowertotalexpenses = null;
        if ($incomeInfo) {

            $borroweroccupation = trim($incomeInfo['occupation1']);
            $coborroweroccupation = trim($incomeInfo['occupation2']);
            $borroweremployername = trim($incomeInfo['employer1']);
            $coborroweremployername = trim($incomeInfo['employer2']);

            $grossIncome1 = trim($incomeInfo['grossIncome1']);
            $grossIncome2 = trim($incomeInfo['grossIncome2']);
            $borrowernetemployeeincome = trim($incomeInfo['netMonthlyIncome1']);
            $coborrowernetemployeeincome = trim($incomeInfo['netMonthlyIncome2']);
            $taxes = trim($incomeInfo['taxes1']);
            $insurance = trim($incomeInfo['insurance1']);
            $insurance1 = trim($incomeInfo['insurance1']);
            $floodinsurance = trim($incomeInfo['floodInsurance1']);
            $mtginsurance = trim($incomeInfo['mortgageInsurance1']);
            $HOAfees = trim($incomeInfo['HOAFees1']);
            $borroweremploymenttype = trim($incomeInfo['employedInfo1']);
            $coborroweremploymenttype = trim($incomeInfo['employedInfo2']);

            $commissionOrBonus1 = trim($incomeInfo['commissionOrBonus1']);
            $overtime1 = trim($incomeInfo['overtime1']);
            $tipsMiscIncome1 = trim($incomeInfo['tipsMiscIncome1']);

            $commissionOrBonus2 = trim($incomeInfo['commissionOrBonus2']);
            $overtime2 = trim($incomeInfo['overtime2']);
            $tipsMiscIncome2 = trim($incomeInfo['tipsMiscIncome2']);

            $socialSecurity1 = trim($incomeInfo['socialSecurity1']);
            $netSocialSecurity1 = trim($incomeInfo['netSocialSecurity1']);
            $pensionOrRetirement1 = trim($incomeInfo['pensionOrRetirement1']);
            $netPensionOrRetirement1 = trim($incomeInfo['netPensionOrRetirement1']);
            $disability1 = trim($incomeInfo['disability1']);
            $netDisability1 = trim($incomeInfo['netDisability1']);
            $rental1 = trim($incomeInfo['rental1']);
            $netRental1 = trim($incomeInfo['netRental1']);
            $earnedInterest1 = trim($incomeInfo['earnedInterest1']);
            $netEarnedInterest1 = trim($incomeInfo['netEarnedInterest1']);
            $unemployment1 = trim($incomeInfo['unemployment1']);
            $netUnemployment1 = trim($incomeInfo['netUnemployment1']);
            $roomRental1 = trim($incomeInfo['roomRental1']);
            $netRoomRental1 = trim($incomeInfo['netRoomRental1']);
            $secondJobIncome1 = trim($incomeInfo['secondJobIncome1']);
            $netSecondJobIncome1 = trim($incomeInfo['netSecondJobIncome1']);
            $sonOrDaughter1 = trim($incomeInfo['sonOrDaughter1']);
            $parents1 = trim($incomeInfo['parents1']);
            $childSupportOrAlimony1 = trim($incomeInfo['childSupportOrAlimony1']);
            $otherHouseHold1 = trim($incomeInfo['otherHouseHold1']);
            $foodStampWelfare1 = trim($incomeInfo['foodStampWelfare1']);

            $socialSecurity2 = trim($incomeInfo['socialSecurity2']);
            $netSocialSecurity2 = trim($incomeInfo['netSocialSecurity2']);
            $pensionOrRetirement2 = trim($incomeInfo['pensionOrRetirement2']);
            $netPensionOrRetirement2 = trim($incomeInfo['netPensionOrRetirement2']);
            $disability2 = trim($incomeInfo['disability2']);
            $netDisability2 = trim($incomeInfo['netDisability2']);
            $rental2 = trim($incomeInfo['rental2']);
            $netRental2 = trim($incomeInfo['netRental2']);
            $earnedInterest2 = trim($incomeInfo['earnedInterest2']);
            $netEarnedInterest2 = trim($incomeInfo['netEarnedInterest2']);
            $unemployment2 = trim($incomeInfo['unemployment2']);
            $netUnemployment2 = trim($incomeInfo['netUnemployment2']);
            $roomRental2 = trim($incomeInfo['roomRental2']);
            $netRoomRental2 = trim($incomeInfo['netRoomRental2']);
            $secondJobIncome2 = trim($incomeInfo['secondJobIncome2']);
            $netSecondJobIncome2 = trim($incomeInfo['netSecondJobIncome2']);
            $sonOrDaughter2 = trim($incomeInfo['sonOrDaughter2']);
            $parents2 = trim($incomeInfo['parents2']);
            $childSupportOrAlimony2 = trim($incomeInfo['childSupportOrAlimony2']);
            $otherHouseHold2 = trim($incomeInfo['otherHouseHold2']);
            $foodStampWelfare2 = trim($incomeInfo['foodStampWelfare2']);


            $studentLoansBalance1 = trim($incomeInfo['studentLoansBalance1']);
            $studentLoansBalance2 = trim($incomeInfo['studentLoansBalance2']);

            $otherMortgage1 = $incomeInfo['otherMortgage1'];
            $creditCards1 = $incomeInfo['creditCards1'];
            $autoLoan1 = $incomeInfo['autoLoan1'];
            $childSupportOrAlimonyMonthly1 = $incomeInfo['childSupportOrAlimonyMonthly1'];
            $unsecuredLoans1 = $incomeInfo['unsecuredLoans1'];
            $studentLoans1 = $incomeInfo['studentLoans1'];
            $studentLoans2 = $incomeInfo['studentLoans2'];

            $careAmt1 = $incomeInfo['careAmt1'];
            $careAmt1 = $incomeInfo['careAmt1'];
            $allInsurance1 = $incomeInfo['allInsurance1'];
            $groceries1 = $incomeInfo['groceries1'];
            $carExpenses1 = $incomeInfo['carExpenses1'];
            $medicalBill1 = $incomeInfo['medicalBill1'];
            $entertainment1 = $incomeInfo['entertainment1'];
            $other1 = $incomeInfo['other1'];
            $cable1 = $incomeInfo['cable1'];
            $natural1 = $incomeInfo['natural1'];
            $water1 = $incomeInfo['water1'];
            $internet1 = $incomeInfo['internet1'];
            $utilityOther1 = $incomeInfo['utilityOther1'];
            $electricity1 = $incomeInfo['electricity1'];
            $primaryBorrowerPhone = $incomeInfo['primaryBorrowerPhone'];
            $donation1 = $incomeInfo['donation1'];
            $pets1 = $incomeInfo['pets1'];
            $monthlyParking1 = $incomeInfo['monthlyParking1'];
            $unionDues1 = $incomeInfo['unionDues1'];
            $personalLoan1 = $incomeInfo['personalLoan1'];
            $dryCleaning1 = $incomeInfo['dryCleaning1'];
            $lunchPurchased1 = $incomeInfo['lunchPurchased1'];
            $primaryMortgage1 = $incomeInfo['primaryMortgage1'];

            $otherMortgage2 = $incomeInfo['otherMortgage2'];
            $creditCards2 = $incomeInfo['creditCards2'];
            $autoLoan2 = $incomeInfo['autoLoan2'];
            $childSupportOrAlimonyMonthly2 = $incomeInfo['childSupportOrAlimonyMonthly2'];
            $unsecuredLoans2 = $incomeInfo['unsecuredLoans2'];
            $studentLoans2 = $incomeInfo['studentLoans2'];
            $careAmt2 = $incomeInfo['careAmt2'];
            $studentLoans2 = $incomeInfo['studentLoans2'];
            $careAmt2 = $incomeInfo['careAmt2'];
            $allInsurance2 = $incomeInfo['allInsurance2'];
            $groceries2 = $incomeInfo['groceries2'];
            $carExpenses2 = $incomeInfo['carExpenses2'];
            $medicalBill2 = $incomeInfo['medicalBill2'];
            $entertainment2 = $incomeInfo['entertainment2'];
            $other2 = $incomeInfo['other2'];
            $cable2 = $incomeInfo['cable2'];
            $natural2 = $incomeInfo['natural2'];
            $water2 = $incomeInfo['water2'];
            $internet2 = $incomeInfo['internet2'];
            $utilityOther2 = $incomeInfo['utilityOther2'];
            $electricity2 = $incomeInfo['electricity2'];
            $coBorrowerPhone = $incomeInfo['coBorrowerPhone'];
            $donation2 = $incomeInfo['donation2'];
            $pets2 = $incomeInfo['pets2'];
            $monthlyParking2 = $incomeInfo['monthlyParking2'];
            $unionDues2 = $incomeInfo['unionDues2'];
            $personalLoan2 = $incomeInfo['personalLoan2'];
            $dryCleaning2 = $incomeInfo['dryCleaning2'];
            $lunchPurchased2 = $incomeInfo['lunchPurchased2'];
            $primaryMortgage2 = $incomeInfo['primaryMortgage2'];
            $rentalExp1 = $incomeInfo['rentalExp1'];
            $rentalExp2 = $incomeInfo['rentalExp2'];
            $addGrossedUp1 = trim($incomeInfo['addGrossedUp1']);
            $addGrossedUp2 = trim($incomeInfo['addGrossedUp2']);
            $escrowedtaxes = trim($incomeInfo['taxes1']);
            $taxes1 = trim($incomeInfo['taxes1']);
            $escrowedinsurance = $insurance;

            $creditcards = Strings::replaceCommaValues($creditCards1) + Strings::replaceCommaValues($creditCards2);
            $creditcardbalance = Strings::replaceCommaValues(trim($incomeInfo['creditCardsBalance1'])) + Strings::replaceCommaValues(trim($incomeInfo['creditCardsBalance2']));

            $borrowerstudentloantuitions = Strings::replaceCommaValues($studentLoans1);
            $coborrowerstudentloantuitions = Strings::replaceCommaValues($studentLoans2);

            $borrowertotalstudentloanbalance = Strings::replaceCommaValues($studentLoansBalance1);
            $coborrowertotalstudentloanbalance = Strings::replaceCommaValues($studentLoansBalance2);

            $borrowergrossemployeeincome = Strings::replaceCommaValues($grossIncome1)
                + Strings::replaceCommaValues($commissionOrBonus1)
                + Strings::replaceCommaValues($overtime1)
                + Strings::replaceCommaValues($tipsMiscIncome1);

            $borrowertotalgrossincome = Strings::replaceCommaValues($borrowergrossemployeeincome);
            if ($addGrossedUp1 > 0) {
                $borrowertotalgrossincome += Strings::replaceCommaValues($socialSecurity1 * 1.25);
            } else {
                $borrowertotalgrossincome += Strings::replaceCommaValues($socialSecurity1);
            }

            $borrowertotalgrossincome += Strings::replaceCommaValues($pensionOrRetirement1)
                + Strings::replaceCommaValues($disability1)
                + Strings::replaceCommaValues($childSupportOrAlimony1)
                + Strings::replaceCommaValues($rental1)
                + Strings::replaceCommaValues($earnedInterest1)
                + Strings::replaceCommaValues($sonOrDaughter1)
                + Strings::replaceCommaValues($parents1)
                + Strings::replaceCommaValues($unemployment1)
                + Strings::replaceCommaValues($otherHouseHold1)
                + Strings::replaceCommaValues($roomRental1)
                + Strings::replaceCommaValues($secondJobIncome1)
                + Strings::replaceCommaValues($foodStampWelfare1);

            $borrowertotalnetincome = Strings::replaceCommaValues($borrowernetemployeeincome)
                + Strings::replaceCommaValues($netSocialSecurity1)
                + Strings::replaceCommaValues($netPensionOrRetirement1)
                + Strings::replaceCommaValues($netDisability1)
                + Strings::replaceCommaValues($netRental1)
                + Strings::replaceCommaValues($netEarnedInterest1)
                + Strings::replaceCommaValues($netUnemployment1)
                + Strings::replaceCommaValues($netRoomRental1)
                + Strings::replaceCommaValues($netSecondJobIncome1)
                + Strings::replaceCommaValues($sonOrDaughter1)
                + Strings::replaceCommaValues($parents1)
                + Strings::replaceCommaValues($childSupportOrAlimony1)
                + Strings::replaceCommaValues($otherHouseHold1)
                + Strings::replaceCommaValues($foodStampWelfare1);

            $coborrowergrossemployeeincome = Strings::replaceCommaValues($grossIncome2)
                + Strings::replaceCommaValues($commissionOrBonus2)
                + Strings::replaceCommaValues($overtime2)
                + Strings::replaceCommaValues($tipsMiscIncome2);

            $coborrowertotalgrossincome = Strings::replaceCommaValues($coborrowergrossemployeeincome);
            if ($addGrossedUp2 > 0) {
                $coborrowertotalgrossincome += Strings::replaceCommaValues($socialSecurity2 * 1.25);
            } else {
                $coborrowertotalgrossincome += Strings::replaceCommaValues($socialSecurity2);
            }
            $coborrowertotalgrossincome += Strings::replaceCommaValues($pensionOrRetirement2)
                + Strings::replaceCommaValues($disability2)
                + Strings::replaceCommaValues($childSupportOrAlimony2)
                + Strings::replaceCommaValues($rental2)
                + Strings::replaceCommaValues($earnedInterest2)
                + Strings::replaceCommaValues($sonOrDaughter2)
                + Strings::replaceCommaValues($parents2)
                + Strings::replaceCommaValues($unemployment2)
                + Strings::replaceCommaValues($otherHouseHold2)
                + Strings::replaceCommaValues($roomRental2)
                + Strings::replaceCommaValues($secondJobIncome2)
                + Strings::replaceCommaValues($foodStampWelfare2);
            $coborrowertotalnetincome = Strings::replaceCommaValues($coborrowernetemployeeincome)
                + Strings::replaceCommaValues($netSocialSecurity2)
                + Strings::replaceCommaValues($netPensionOrRetirement2)
                + Strings::replaceCommaValues($netDisability2)
                + Strings::replaceCommaValues($netRental2)
                + Strings::replaceCommaValues($netEarnedInterest2)
                + Strings::replaceCommaValues($netUnemployment2)
                + Strings::replaceCommaValues($netRoomRental2)
                + Strings::replaceCommaValues($netSecondJobIncome2)
                + Strings::replaceCommaValues($sonOrDaughter2)
                + Strings::replaceCommaValues($parents2)
                + Strings::replaceCommaValues($childSupportOrAlimony2)
                + Strings::replaceCommaValues($otherHouseHold2)
                + Strings::replaceCommaValues($foodStampWelfare2);

            $totalgrossincome = $borrowertotalgrossincome + $coborrowertotalgrossincome;
            $totalnetincome = $borrowertotalnetincome + $coborrowertotalnetincome;

            if ($addGrossedUp1 > 0) {
                $borrowergrosssocialsecurityincome = Currency::formatDollarAmountWithDecimal(Strings::replaceCommaValues($socialSecurity1 * 1.25));
            } else {
                $borrowergrosssocialsecurityincome = Currency::formatDollarAmountWithDecimal(Strings::replaceCommaValues($socialSecurity1));
            }
            if ($addGrossedUp2 > 0) {
                $coborrowergrosssocialsecurityincome = Currency::formatDollarAmountWithDecimal(Strings::replaceCommaValues($socialSecurity2 * 1.25));
            } else {
                $coborrowergrosssocialsecurityincome = Currency::formatDollarAmountWithDecimal(Strings::replaceCommaValues($socialSecurity2));
            }

            $primTotalHouseHoldExpenses = Strings::replaceCommaValues($mtgpipayment)
                + Strings::replaceCommaValues($HOAfees)
                + Strings::replaceCommaValues($taxes)
                + Strings::replaceCommaValues($insurance)
                + Strings::replaceCommaValues($otherMortgage1)
                + Strings::replaceCommaValues($unsecuredLoans1)
                + Strings::replaceCommaValues($creditCards1)
                + Strings::replaceCommaValues($studentLoans1)
                + Strings::replaceCommaValues($childSupportOrAlimonyMonthly1)
                + Strings::replaceCommaValues($other1)
                + Strings::replaceCommaValues($paymentamount)
                + Strings::replaceCommaValues($primaryMortgage1)
                + Strings::replaceCommaValues($primaryMortgage2)
                + Strings::replaceCommaValues($autoLoan1)
                + Strings::replaceCommaValues($careAmt1)
                + Strings::replaceCommaValues($allInsurance1)
                + Strings::replaceCommaValues($groceries1)
                + Strings::replaceCommaValues($carExpenses1)
                + Strings::replaceCommaValues($medicalBill1)
                + Strings::replaceCommaValues($entertainment1)
                + Strings::replaceCommaValues($cable1)
                + Strings::replaceCommaValues($natural1)
                + Strings::replaceCommaValues($water1)
                + Strings::replaceCommaValues($internet1)
                + Strings::replaceCommaValues($utilityOther1)
                + Strings::replaceCommaValues($electricity1)
                + Strings::replaceCommaValues($primaryBorrowerPhone)
                + Strings::replaceCommaValues($mtginsurance)
                + Strings::replaceCommaValues($donation1)
                + Strings::replaceCommaValues($pets1)
                + Strings::replaceCommaValues($monthlyParking1)
                + Strings::replaceCommaValues($unionDues1)
                + Strings::replaceCommaValues($personalLoan1)
                + Strings::replaceCommaValues($dryCleaning1)
                + Strings::replaceCommaValues($floodinsurance)
                + Strings::replaceCommaValues($lunchPurchased1)
                + Strings::replaceCommaValues($rentalExp1);

            $coTotalHouseHoldExpenses = Strings::replaceCommaValues($otherMortgage2)
                + Strings::replaceCommaValues($creditCards2)
                + Strings::replaceCommaValues($autoLoan2)
                + Strings::replaceCommaValues($childSupportOrAlimonyMonthly2)
                + Strings::replaceCommaValues($unsecuredLoans2)
                + Strings::replaceCommaValues($studentLoans2)
                + Strings::replaceCommaValues($careAmt2)
                + Strings::replaceCommaValues($allInsurance2)
                + Strings::replaceCommaValues($groceries2)
                + Strings::replaceCommaValues($carExpenses2)
                + Strings::replaceCommaValues($medicalBill2)
                + Strings::replaceCommaValues($entertainment2)
                + Strings::replaceCommaValues($other2)
                + Strings::replaceCommaValues($cable2)
                + Strings::replaceCommaValues($natural2)
                + Strings::replaceCommaValues($water2)
                + Strings::replaceCommaValues($internet2)
                + Strings::replaceCommaValues($utilityOther2)
                + Strings::replaceCommaValues($electricity2)
                + Strings::replaceCommaValues($coBorrowerPhone)
                + Strings::replaceCommaValues($donation2)
                + Strings::replaceCommaValues($pets2)
                + Strings::replaceCommaValues($monthlyParking2)
                + Strings::replaceCommaValues($unionDues2)
                + Strings::replaceCommaValues($personalLoan2)
                + Strings::replaceCommaValues($dryCleaning2)
                + Strings::replaceCommaValues($lunchPurchased2)
                + Strings::replaceCommaValues($rentalExp2);

            /** For HMLO XLS export fields added By Suresh K (PT # *********)**/
            $HMLORealEstateTaxes = 0;
            $totalInsurance = 0;
            $borrowerbaseemploymentincome = $grossIncome1;
            $coborrowerbaseemploymentincome = $grossIncome2;
            $borrowernetincome = Strings::replaceCommaValues($borrowergrossemployeeincome) + Strings::replaceCommaValues($netRental1) + Strings::replaceCommaValues($otherHouseHold1) + Strings::replaceCommaValues($netEarnedInterest1);
            $coborrowernetincome = Strings::replaceCommaValues($coborrowergrossemployeeincome) + Strings::replaceCommaValues($netRental2) + Strings::replaceCommaValues($otherHouseHold2) + Strings::replaceCommaValues($netEarnedInterest2);
            $HMLORealEstateTaxes = proposalFormula::calculateHMLORealEstateTaxes($taxes);
            $totalInsurance = proposalFormula::calculateTotalInsurance($floodinsurance, $annualPremium);

            $borrowertotalexpenses = Strings::replaceCommaValues($mtgpipayment)
                + Strings::replaceCommaValues($HOAfees)
                + Strings::replaceCommaValues($otherMortgage1)
                + Strings::replaceCommaValues($unsecuredLoans1)
                + Strings::replaceCommaValues($creditCards1)
                + Strings::replaceCommaValues($studentLoans1)
                + Strings::replaceCommaValues($childSupportOrAlimonyMonthly1)
                + Strings::replaceCommaValues($other1)
                + Strings::replaceCommaValues($HMLORealEstateTaxes)
                + Strings::replaceCommaValues($totalInsurance);
            $coborrowertotalexpenses = $coTotalHouseHoldExpenses;
            /** For HMLO XLS export fields added By Suresh K (PT # *********)**/

            $totalHouseHoldExpenses = $primTotalHouseHoldExpenses + $coTotalHouseHoldExpenses;
            $disposableincome = $totalnetincome - $totalHouseHoldExpenses;

            $tempLien1PaymentPITIA = '';
            if ($LMRId > 0) {
                $tempLien1PaymentPITIA = proposalFormula::calculatePaymentPI($mtgpipayment, $taxes, $insurance, $HOAfees, $mtginsurance, $floodinsurance);
                $currentdti = proposalFormula::calculateDTI($tempLien1PaymentPITIA, 0, $totalgrossincome);
            }
            $totalpitia = Strings::replaceCommaValues($mtgpipayment) + Strings::replaceCommaValues($taxes);
            $totalpitia += Strings::replaceCommaValues($insurance) + Strings::replaceCommaValues($mtginsurance);
            $totalpitia += Strings::replaceCommaValues($HOAfees);
            $totalpitia += Strings::replaceCommaValues($floodinsurance);
        }
        $actualRentsInPlaceCommercial = Strings::replaceCommaValues(trim($fileHMLONewLoanInfo['actualRentsInPlaceCommercial']));
        $vacancyFactorCommercial = Strings::replaceCommaValues(trim($fileHMLONewLoanInfo['vacancyFactorCommercial']));

        $waterSewer = Strings::replaceCommaValues(trim($fileHMLONewLoanInfo['waterSewer']));
        $waterSewer = floatval($waterSewer);

        $electricity = Strings::replaceCommaValues(trim($fileHMLONewLoanInfo['electricity']));

        $gas = Strings::replaceCommaValues(trim($fileHMLONewLoanInfo['gas']));

        $repairsMaintenance = Strings::replaceCommaValues(trim($fileHMLONewLoanInfo['repairsMaintenance']));

        $legal = Strings::replaceCommaValues(trim($fileHMLONewLoanInfo['legal']));

        $payroll = Strings::replaceCommaValues(trim($fileHMLONewLoanInfo['payroll']));
        $misc = Strings::replaceCommaValues(trim($fileHMLONewLoanInfo['misc']));
        $tenantReimursements = Strings::replaceCommaValues(trim($fileHMLONewLoanInfo['tenantReimursements']));

        $managementExpense = Strings::replaceCommaValues(trim($fileHMLONewLoanInfo['managementExpense']));
        $otherIncome = Strings::replaceCommaValues(trim($fileHMLONewLoanInfo['otherIncome']));
        $tenantContribution = Strings::replaceCommaValues(trim($fileHMLONewLoanInfo['tenantContribution']));
        $otherIncomeVacancyRate = Strings::replaceCommaValues(trim($fileHMLONewLoanInfo['otherIncomeVacancyRate']));
        $tenantContributionVacancyRate = Strings::replaceCommaValues(trim($fileHMLONewLoanInfo['tenantContributionVacancyRate']));

        $spcf_hoafees_cal = Strings::replaceCommaValues(trim($fileHMLONewLoanInfo['spcf_hoafees']));
        $vacancyCommercial = ($vacancyFactorCommercial) * ($actualRentsInPlaceCommercial) / 100.0;

        $spcf_taxes1_cal = Strings::replaceCommaValues($taxes1);
        $vacancyOtherIncome = ($otherIncome * $otherIncomeVacancyRate) / 100.0;
        $vacancyTenantContribution = ($tenantContribution * $tenantContributionVacancyRate) / 100.0;

        $netOperatingIncome = proposalFormula::calculateNetOperatingIncome($actualRentsInPlace,
            $actualRentsInPlaceCommercial,
            $tenantContribution,
            $otherIncome,
            $vacancy,
            $vacancyCommercial,
            $vacancyOtherIncome,
            $vacancyTenantContribution,
            $lessActualExpenses,
            $waterSewer,
            $electricity,
            $gas,
            $repairsMaintenance,
            $legal,
            $payroll,
            $misc,
            $commonAreaUtilities,
            $elevatorMaintenance,
            $replacementReserves,
            $other,
            $tenantReimursements,
            $managementExpense,
            $spcf_taxes1_cal,
            $spcf_annualPremium_cal,
            $spcf_hoafees_cal);

        $netOperatingIncome = Strings::replaceCommaValues($netOperatingIncome);

//        $netOperatingIncome = $actualRentsInPlace
//            + $actualRentsInPlaceCommercial
//            + $tenantContribution
//            + $otherIncome
//            - $vacancy
//            - $vacancyCommercial
//            - $vacancyOtherIncome
//            - $vacancyTenantContribution
//            - $lessActualExpenses
//            - $waterSewer
//            - $electricity
//            - $gas
//            - $repairsMaintenance
//            - $legal
//            - $payroll
//            - $misc
//            - $commonAreaUtilities
//            - $elevatorMaintenance
//            - $replacementReserves
//            - Strings::replaceCommaValues($other)
//            - $tenantReimursements
//            - $managementExpense
//            - $spcf_taxes1_cal
//            - $spcf_annualPremium_cal
//            - $spcf_hoafees_cal;

        $debtserviceratio = proposalFormula::calculateDebtServiceRatio($monthlypayment, $netOperatingIncome);
        $debtserviceratio .= '%';
        $noofpendingtasks = 0;
        $daysincurrentstatus = '';

        $netMonthlyPayment = proposalFormula::calculateHMLONetMonthlyPayment($monthlypayment, $taxes1, $annualPremium, $spcf_hoafees);


        $effectiveGrossIncome = calEffectiveGrossIncome::getEffectiveGrossIncome($actualRentsInPlace,
            $actualRentsInPlaceCommercial,
            $otherIncome,
            $tenantContribution,
            $vacancy,
            $vacancyCommercial,
            $vacancyOtherIncome,
            $vacancyTenantContribution
        );

        $debtserviceratiopitia = Currency::formatDollarAmountWithDecimalZeros(debtServiceRatioPITIA::getReportParams(
            $effectiveGrossIncome,
            $netMonthlyPayment,
            $spcf_hoafees
        ));


        if (count(exportClientFiles::$pendingTaskArray) > 0) {
            if (array_key_exists($LMRId, exportClientFiles::$pendingTaskArray)) $noofpendingtasks = (int)(trim(exportClientFiles::$pendingTaskArray[$LMRId]['noOfPendingTask']));
        }

        if (count(exportClientFiles::$WFTaskArray) > 0) {
            if (array_key_exists($LMRId, exportClientFiles::$WFTaskArray)) $noofpendingtasks += (int)(trim(exportClientFiles::$WFTaskArray[$LMRId]['noOfPendingTask']));
        }
        if (array_key_exists($LMRId, exportClientFiles::$noOfDaysInCurrentStatus)) {
            if (array_key_exists('noOfDays', exportClientFiles::$noOfDaysInCurrentStatus[$LMRId])) {
                $daysincurrentstatus = exportClientFiles::$noOfDaysInCurrentStatus[$LMRId]['noOfDays'];
            }
        }

        if (array_key_exists($LMRId, exportClientFiles::$loanOriginationInfo)) {
            $loanoriginationdate = trim(exportClientFiles::$loanOriginationInfo[$LMRId]['noteDate']);
        }

        $LMRClientTypeInfo = [];

        if (array_key_exists($LMRId, exportClientFiles::$LMRClientTypeInfoArray)) $LMRClientTypeInfo = exportClientFiles::$LMRClientTypeInfoArray[$LMRId];

        if (array_key_exists($LMRId, exportClientFiles::$LMRCheckListInfoArray)) $LMRChecklistTypeInfo = exportClientFiles::$LMRCheckListInfoArray[$LMRId];

        $tt = null;
        $pp = null;
        $cc = null;
        $notEligibleStatus = null;

        if (in_array('Missing Docs - Checked off', exportClientFiles::$selectedFieldsArray)
            || in_array('Missing Docs - Not Checked off', exportClientFiles::$selectedFieldsArray)
            || in_array('No of Missing Docs - Checked off', exportClientFiles::$selectedFieldsArray)
            || in_array('No of Missing Docs - Not Checked off', exportClientFiles::$selectedFieldsArray)
            || in_array('Checklist Status', exportClientFiles::$selectedFieldsArray)) {

            $l = 0;
            $ll = 0;
            $m = 0;
            $s = 0;
            $tt = 0;
            $pp = 0;
            $ii = 0;
            $cc = 0;
            $nn = 0;
            for ($s1 = 0; $s1 < count(exportClientFiles::$LMRChecklistTypeInfo); $s1++) {
                $tempClientArray = [];
                $j = 0;
                $jj = 0;
                $t = 0;
                $p = 0;
                $co = 0;
                $notR = 0;

                $moduleCode = trim(exportClientFiles::$LMRChecklistTypeInfo[$s1]['moduleCode']);
                if (array_key_exists($moduleCode, exportClientFiles::$bothPCFileChecklistArray)) {
                    $tempClientArray = exportClientFiles::$bothPCFileChecklistArray[$moduleCode];
                }

                for ($d1 = 0; $d1 < count($tempClientArray); $d1++) {
                    $showDoc2 = 1;
                    $notReq = 0;
                    $notEli = 0;
                    $pendReview = 0;
                    $com = 0;
                    $notRec = 0;
                    $CLType = 'PCL';

                    if (in_array(trim($tempClientArray[$d1]['serviceType']), $servicesArray)) {
                        if (array_key_exists('FMID', $tempClientArray[$d1])) {
                            $clientDocVal2 = trim($tempClientArray[$d1]['FMID']);
                            $CLType = 'FCL';
                        } else {
                            $clientDocVal2 = trim($tempClientArray[$d1]['PCMID']);
                        }

                        if (count(exportClientFiles::$missingDocInfoArray) > 0) {
                            if (array_key_exists($LMRId, exportClientFiles::$missingDocInfoArray)) {
                                $tempMissDocArray = exportClientFiles::$missingDocInfoArray[$LMRId];
                                $tempMissDocCLTypeArray = [];
                                if (array_key_exists($CLType, $tempMissDocArray)) $tempMissDocCLTypeArray = $tempMissDocArray[$CLType];

                                for ($k = 0; $k < count($tempMissDocCLTypeArray); $k++) {
                                    if (trim($clientDocVal2) == trim($tempMissDocCLTypeArray[$k]['docId'])) {
                                        if (trim($tempMissDocCLTypeArray[$k]['docStatus']) == 3) {
                                            $notEli = 1;
                                        } else if (trim($tempMissDocCLTypeArray[$k]['docStatus']) == 4) {
                                            $pendReview = 1;
                                        } else if (trim($tempMissDocCLTypeArray[$k]['docStatus']) == 0 || trim($tempMissDocCLTypeArray[$k]['docStatus']) == 1 || trim($tempMissDocCLTypeArray[$k]['docStatus']) == 6) {
                                            $com = 1;
                                        } else if (trim($tempMissDocCLTypeArray[$k]['docStatus']) != 2 || trim($tempMissDocCLTypeArray[$k]['docStatus']) == 7) {
                                            $notRec = 1;
                                        }
                                        $showDoc2 = 0;
                                    }
                                }
                            }
                        }
                        if (count(exportClientFiles::$cklistNotRequiredArray) > 0) {
                            if (array_key_exists($LMRId, exportClientFiles::$cklistNotRequiredArray)) {
                                $tempChklistNotRequiredArray = [];
                                $tempChklistArray = exportClientFiles::$cklistNotRequiredArray[$LMRId];

                                if (array_key_exists($CLType, $tempChklistArray)) $tempChklistNotRequiredArray = $tempChklistArray[$CLType];

                                for ($k = 0; $k < count($tempChklistNotRequiredArray); $k++) {
                                    if (trim($clientDocVal2) == trim($tempChklistNotRequiredArray[$k]['CID'])) {
                                        $notReq = 1;
                                    }
                                }

                            }
                        }
                        if ($notReq == 0) {
                            if ($showDoc2 == 0) {
                                if ($j == 0) {
                                    if ($l > 0) {
                                        $missingdocscheckedoff .= ') ';
                                    }
                                    $missingdocscheckedoff .= trim(exportClientFiles::$glLMRClientTypeArray[$moduleCode]) . ' (';
                                } else {
                                    $missingdocscheckedoff .= ', ';
                                }
                                $missingdocscheckedoff .= $tempClientArray[$d1]['docName'];
                                if ($notEli == 1) {
                                    if ($t == 0) {
                                        if ($tt > 0) {
                                            $notEligibleStatus .= ') ';
                                        }
                                        $notEligibleStatus .= trim(exportClientFiles::$glLMRClientTypeArray[$moduleCode]) . ' (';
                                    } else {
                                        $notEligibleStatus .= ', ';
                                    }
                                    $notEligibleStatus .= $tempClientArray[$d1]['docName'];
                                    $t++;
                                    $tt++;
                                }
                                if ($pendReview == 1) {

                                    if ($p == 0) {
                                        if ($pp > 0) {
                                            $pendingReviewStatus .= ') ';
                                        }
                                        $pendingReviewStatus .= trim(exportClientFiles::$glLMRClientTypeArray[$moduleCode]) . ' (';
                                    } else {
                                        $pendingReviewStatus .= ', ';
                                    }
                                    $pendingReviewStatus .= $tempClientArray[$d1]['docName'];
                                    $p++;
                                    $pp++;
                                }
                                if ($notRec == 1) {
                                    if ($notR == 0) {
                                        if ($nn > 0) {
                                            $notReceivedStatus .= ') ';
                                        }
                                        $notReceivedStatus .= trim(exportClientFiles::$glLMRClientTypeArray[$moduleCode]) . ' (';
                                    } else {
                                        $notReceivedStatus .= ', ';
                                    }
                                    $notReceivedStatus .= $tempClientArray[$d1]['docName'];
                                    $notR++;
                                    $nn++;
                                }

                                if ($com == 1) {

                                    if ($co == 0) {
                                        if ($cc > 0) {
                                            $completeStatus .= ') ';
                                        }
                                        $completeStatus .= trim(exportClientFiles::$glLMRClientTypeArray[$moduleCode]) . ' (';
                                    } else {
                                        $completeStatus .= ', ';
                                    }
                                    $completeStatus .= $tempClientArray[$d1]['docName'];
                                    $co++;
                                    $cc++;
                                }

                                $j++;
                                $l++;
                                $m++;
                            } else {
                                if ($jj == 0) {
                                    if ($ll > 0) {
                                        $missingdocsnotcheckedoff .= ') ';
                                    }
                                    $missingdocsnotcheckedoff .= trim(exportClientFiles::$glLMRClientTypeArray[$moduleCode]) . ' (';
                                } else {
                                    $missingdocsnotcheckedoff .= ', ';
                                }
                                $missingdocsnotcheckedoff .= $tempClientArray[$d1]['docName'];
                                $jj++;
                                $ll++;
                                $s++;
                            }
                        }

                    }
                } // END for loop tempClientArray
            }
            if ($l > 0) {
                if ($tt > 0) {
                    $notEligibleStatus .= ') ';
                }
                if ($pp > 0) {
                    $pendingReviewStatus .= ') ';
                }
                if ($cc > 0) {
                    $completeStatus .= ') ';
                }

            }
            if ($ll > 0) {
                $missingdocsnotcheckedoff .= ') ';
            }
            $noofmissingdocsnotcheckedoff = $s;

        }

        $noticeofacceleration = '';
        $transferofservicingdate = '';


        $prequalifiername = '';

        if (array_key_exists('file2Info', exportClientFiles::$fileInfoArray)) {
            $file2Info = exportClientFiles::$fileInfoArray['file2Info'];
        }

        if ($file2Info) {
            if (array_key_exists($LMRId, $file2Info)) {
                $noticeofacceleration = trim($file2Info[$LMRId]['noticeAccelerationDate']);
                $transferofservicingdate = trim($file2Info[$LMRId]['transferOfServicingDate']);
                $borroweraddress = trim($file2Info[$LMRId]['presentAddress']);
                $borrowercity = trim($file2Info[$LMRId]['presentCity']);
                $borrowerstate = trim($file2Info[$LMRId]['presentState']);
                $borrowerzip = trim($file2Info[$LMRId]['presentZip']);

                $tempFileServiceType = '';
                $clientTypeArray = [];
                if (array_key_exists($LMRId, exportClientFiles::$fileClientTypeArray)) {
                    $clientTypeArray = exportClientFiles::$fileClientTypeArray[$LMRId];
                }
                for ($s1 = 0; $s1 < count($clientTypeArray); $s1++) {
                    $tempFileServiceType = trim($clientTypeArray[$s1]['ClientType']);
                    if ($tempFileServiceType == 'SLM') {
                        $prequalifiername = trim($file2Info[$LMRId]['preQualifierName']);
                        break;
                    }
                }

            }
        }

        $clientsmsnumber = '';
        if (trim($smsserviceprovider)) {
            $clientsmsnumber = preg_replace('/[^0-9]/', '', trim($myFileInfo['cellNumber'])) . '@' . SMSServiceProviderDomainArray::$SMSServiceProviderDomainArray[trim($myFileInfo['serviceProvider'])];
        }

        $cfpbsubmissiondate = null;

        if ($userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager' || $fileType == 'CFPB') {
            if (array_key_exists($LMRId, exportClientFiles::$CFPBAuditInfoArray)) {
                $CFPBAuditInfo = exportClientFiles::$CFPBAuditInfoArray[$LMRId];
                if (count($CFPBAuditInfo) > 0) {
                    $cfpbsubmissiondate = trim($CFPBAuditInfo['submittedDate']);
                }
            }
        }
        if (Dates::IsEmpty($cfpbsubmissiondate)) {
            doNothing();
        } else {
            $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
            $ipArray['outputZone'] = $userTimeZone;
            $ipArray['inputTime'] = $cfpbsubmissiondate;
            $cfpbsubmissiondate = Dates::timeZoneConversion($ipArray);

            $cfpbsubmissiondate = Dates::formatDateWithRE($cfpbsubmissiondate, 'YMD_HMS', 'm/d/Y h:i A');
        }

        if (in_array('Workflow', exportClientFiles::$selectedFieldsArray)) {
            self::getWorkflow($LMRId, $tArray, $secondaryStatusInfo);
        }


        $coborrowersmsnumber = '';
        if (trim($coborrowersmsserviceprovider)) {
            $coborrowersmsnumber = preg_replace('/[^0-9]/', '', trim($myFileInfo['coBCellNumber'])) . '@' . SMSServiceProviderDomainArray::$SMSServiceProviderDomainArray[trim($myFileInfo['coBServiceProvider'])];
        }


        $bpo1 = '';
        $bpo2 = '';
        $bpo3 = '';
        $costbasis = '';
        $appraiser1 = null;
        $appraiser2 = null;
        $projectedforeclosuredate = '';
        if ($listingRealtorInfo) {
            $appraiser1 = trim($listingRealtorInfo['appraiser1']);
            $appraiser2 = trim($listingRealtorInfo['appraiser2']);
            $bpo1 = trim($listingRealtorInfo['BPO1']);
            $bpo2 = trim($listingRealtorInfo['BPO2']);
            $bpo3 = trim($listingRealtorInfo['BPO3']);
            $costbasis = trim($listingRealtorInfo['costBasis']);
            $projectedforeclosuredate = $listingRealtorInfo['foreclosureDate'];
            if (Dates::IsEmpty($projectedforeclosuredate)) {
                $projectedforeclosuredate = '';
            } else {
                $projectedforeclosuredate = Dates::formatDateWithRE($projectedforeclosuredate, 'YMD', 'm/d/Y');
            }
        }

        $trusteecompany = '';
        $trusteename = '';
        $trusteephone = '';
        $trusteeemail = '';
        $trusteefax = '';
        $trusteecell = '';

        $fileContacts = null;
        if (array_key_exists('fileContacts', exportClientFiles::$fileInfoArray)) {
            $fileContacts = exportClientFiles::$fileInfoArray['fileContacts'];
        }

        if ($fileContacts) {
            if (array_key_exists($LMRId, $fileContacts)) {
                $fileContactsInfo = $fileContacts[$LMRId];
                $bankAttorneyInfo = null;
                $titleInfo = null;
                $attorneyInfo = null;
                $insuranceInfo = null;
                $investor = null;
                $BPO1 = null;
                $HOA1 = null;
                $HOA2 = null;
                $lender = null;
                $accountant = null;
                $financialAdvisor = null;
                $escrow = null;
                $trustee = null;
                $servicer = null;
                $GeneralContractor = null;


                foreach ($fileContactsInfo as $fc => $contact) {
                    if (!$contact['cRole']) {
                        continue;
                    }
                    switch ($contact['cRole']) {
                        case 'General Contractor':
                            $GeneralContractor = null;
                            break;
                        case 'Servicer':
                            $servicer = $contact;
                            break;
                        case 'Trustee':
                            $trustee = $contact;
                            break;

                        case 'Escrow':
                            $escrow = $contact;
                            break;
                        case 'Financial Advisor':
                            $financialAdvisor = $contact;
                            break;
                        case 'Accountant':
                            $accountant = $contact;
                            break;
                        case 'Investor':
                            $investor = $contact;
                            break;
                        case 'BPO 1':
                            $BPO1 = $contact;
                            break;
                        case 'HOA1':
                            $HOA1 = $contact;
                            break;
                        case 'HOA2':
                            $HOA2 = $contact;
                            break;
                        case 'Lender':
                            $lender = $contact;
                            break;
                        case 'Appraiser':
                        case 'Appraiser 1':
                            $appraiser1 = $contact;
                            break;
                        case 'Appraiser 2':
                            $appraiser2 = $contact;
                            break;
                        case 'Bank Attorney':
                            $bankAttorneyInfo = $contact;
                            break;
                        case 'Title Rep':
                            $titleInfo = $contact;
                            break;
                        case 'Attorney':
                            $attorneyInfo = $contact;
                            break;
                        case 'Insurance Rep':
                            $insuranceInfo = $contact;
                            break;
                        case 'Broker':
                        case 'Seller Attorney':
                            break;
                        default:
                            // Debug('unknown role for contact: ', $contact);
                            // this can be user entered, so we don't need to check for anything we aren't explicitly handling - 2023-03-14 bdk
                    }
                }
                if ($bankAttorneyInfo) {
                    $trusteecompany = trim($bankAttorneyInfo['companyName']);
                    $trusteename = trim($bankAttorneyInfo['contactName']);
                    $trusteephone = Strings::formatPhoneNumber(trim($bankAttorneyInfo['phone']));
                    $trusteeemail = trim($bankAttorneyInfo['email']);
                    $trusteefax = Strings::formatPhoneNumber(trim($bankAttorneyInfo['fax']));
                    $trusteecell = Strings::formatPhoneNumber(trim($bankAttorneyInfo['cell']));
                }

                /** For HMLO XLS export fields added By Suresh K - (PT #: *********)**/
                if ($titleInfo) {
                    $titlecompany = $titleInfo['companyName'];
                    $titlerepresentative = $titleInfo['contactName'];
                    $titlephone = Strings::formatPhoneNumber($titleInfo['phone']);
                    $titlefax = Strings::formatPhoneNumber($titleInfo['fax']);
                    $titleemail = $titleInfo['email'];
                    $titlereplastname = trim($titleInfo['contactLName']);
                }
                if ($attorneyInfo) {
                    $attorneyname = $attorneyInfo['contactName'];
                    $attorneyfirmname = $attorneyInfo['companyName'];
                    $attorneyemail = $attorneyInfo['email'];
                    $attorneyphone = Strings::formatPhoneNumber($attorneyInfo['phone']);
                }
                if ($insuranceInfo) {
                    //   $nameofcarrier              = $insuranceInfo["companyName"];
                    $nameofcompany = $insuranceInfo['companyName'];
                    $insurancerepfirstname = $insuranceInfo['contactName'];
                    $insurancereplastname = $insuranceInfo['contactLName'];
                    $insurancerepemail = $insuranceInfo['email'];
                    $insurancerepphone = Strings::formatPhoneNumber($insuranceInfo['phone']);
                    $insurancerepcell = Strings::formatPhoneNumber($insuranceInfo['cell']);
                }
                /** For HMLO XLS export fields added By Suresh K - (PT #: *********)**/
            }
        }

        $notesInfo = null;
        $notes = '';
        $fileNotesInfo = exportClientFiles::$fileInfoArray['notesInfo'];
        if ($fileNotesInfo && array_key_exists($LMRId, $fileNotesInfo)) {
            $notesInfo = $fileNotesInfo[$LMRId];
        }
        if ($notesInfo) {
            foreach ($notesInfo as $tc => $item) {
                $processorComments = '';
                $notesDate = '';
                $commentsBy = '';
                $notes = '';
                $notesEmpId = 0;
                $notesExeId = 0;
                $notesAgentId = 0;
                $notesClientId = 0;
                $signExecutiveName = '';
                $tempNotesDate = '';
                $notesPrivacy = '';
                $notesTypeData = '';

                $processorComments = Strings::replaceProcessedHeader(trim($item['processorComments']));
                $notesDate = trim($item['notesDate']);
                $signExecutiveName = trim($item['signExecutiveName']);
                $notesEmpId = trim($item['employeeId']);
                $notesExeId = trim($item['executiveId']);
                $notesAgentId = trim($item['brokerNumber']);
                $notesClientId = trim($item['clientId']);
                $notesPrivacy = trim($item['private']);
                $notesTypeData = trim($item['notesType']);

                if ($notesEmpId > 0) {
                    if (array_key_exists($notesEmpId, exportClientFiles::$notesEmpInfo)) $commentsBy = trim(exportClientFiles::$notesEmpInfo[$notesEmpId]['processorName']);
                } elseif ($notesExeId > 0) {
                    if (array_key_exists($notesExeId, exportClientFiles::$notesBranchInfo)) $commentsBy = trim(exportClientFiles::$notesBranchInfo[$notesExeId]['LMRExecutive']);
                } elseif ($notesAgentId > 0) {
                    if (array_key_exists($notesAgentId, exportClientFiles::$notesAgentInfo)) $commentsBy = trim(exportClientFiles::$notesAgentInfo[$notesAgentId]['agentName']);
                } elseif ($notesClientId > 0) {
                    if (array_key_exists($notesClientId, exportClientFiles::$notesClientInfo)) $commentsBy = trim(exportClientFiles::$notesClientInfo[$notesClientId]['clientName']);
                } else {
                    $commentsBy = 'Admin';
                }

                if (Dates::IsEmpty($notesDate)) {
                    $notesDate = '';
                } else {
                    $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
                    $ipArray['outputZone'] = $userTimeZone;
                    $ipArray['inputTime'] = $notesDate;
                    $notesDate = Dates::timeZoneConversion($ipArray);
                    $tempNotesDate = Dates::formatDateWithRE($notesDate, 'YMD_HMS', 'm/d/Y h:i A');
                    $notesDate = Dates::formatDateWithRE($notesDate, 'YMD_HMS', 'M j, Y h:i A') . ' - ' . $userTimeZone;
                }

                if ($item['displayIn'] == 'NH' || $item['displayIn'] == 'BO' || $item['displayIn'] == 'SH') {
                    if (trim($notes) != '') $notes .= "\n";
                    $notes .= $processorComments . "\n- " . $commentsBy . ' (' . $notesDate . ')';
                }
            }
        }

        $shortSaleInfo = null;
        $shortSaleInfoArray = exportClientFiles::$fileInfoArray['shortSaleInfo'];
        if ($shortSaleInfoArray && array_key_exists($LMRId, $shortSaleInfoArray)) {
            $shortSaleInfo = $shortSaleInfoArray[$LMRId];
        }
        if ($shortSaleInfo) {
            $listingrealtor = $shortSaleInfo['realtor'];
            $listingprice = $shortSaleInfo['listingPrice'];
            $listingdate = $shortSaleInfo['listingDate'];
            $listingaddress = $shortSaleInfo['realtorAddress'];
            $listingphone = Strings::formatPhoneNumber($shortSaleInfo['realtorPhoneNumber']);
            $listingcell = Strings::formatPhoneNumber($shortSaleInfo['sales1CellNo']);
            $listingagency = $shortSaleInfo['agency'];
            $listingfax = Strings::formatPhoneNumber($shortSaleInfo['sales1Fax']);
            $listingemail = $shortSaleInfo['realtorEmail'];
            $titlecompany = $shortSaleInfo['titleCompany'];
            $titlerepresentative = $shortSaleInfo['contact'];
            $titlephone = Strings::formatPhoneNumber($shortSaleInfo['titleCompanyPhoneNumber']);
            $titlefax = Strings::formatPhoneNumber($shortSaleInfo['sales2Fax']);
            $titleemail = $shortSaleInfo['titleCompanyEmail'];

            $buyerdeal1 = $shortSaleInfo['buyer1Deal'];
            $firstbuyername = $shortSaleInfo['buyerName1'];
            $firstcobuyername = $shortSaleInfo['coBuyerName1'];
            $firstbuyerphone = Strings::formatPhoneNumber($shortSaleInfo['firstBuyerPhone']);
            $firstbuyeremail = $shortSaleInfo['firstBuyerEmail'];
            $offer1 = $shortSaleInfo['offer1'];
            $sqft1 = $shortSaleInfo['sqft1'];
            $contractdate1 = $shortSaleInfo['contractDate1'];
            $closingdate1 = $shortSaleInfo['closingDate1'];
            $buyeragentname1 = $shortSaleInfo['buyer1AgentName'];
            $firstbuyeragency = $shortSaleInfo['buyer1AgencyName'];
            $buyerphone1 = Strings::formatPhoneNumber($shortSaleInfo['buyer1Phone']);
            $buyercell1 = Strings::formatPhoneNumber($shortSaleInfo['buyer1Cell']);
            $buyerfax1 = Strings::formatPhoneNumber($shortSaleInfo['buyer1Fax']);
            $buyeremail1 = $shortSaleInfo['buyer1Email'];
            $firstbuyersloanofficername = $shortSaleInfo['buyer1LOName'];
            $firstbuyersloanofficercompany = $shortSaleInfo['buyer1LOCompany'];
            $firstbuyerslophone = Strings::formatPhoneNumber($shortSaleInfo['buyer1LOPhone']);
            $firstbuyerslocell = Strings::formatPhoneNumber($shortSaleInfo['buyer1LOCell']);
            $firstbuyerslofax = Strings::formatPhoneNumber($shortSaleInfo['buyer1LOFax']);
            $firstbuyersloemail = $shortSaleInfo['buyer1LOEmail'];
            $relationshiptoseller1 = $shortSaleInfo['buyer1RelToSeller'];
            $offernotes1 = $shortSaleInfo['buyer1Notes'];

            $buyerdeal2 = $shortSaleInfo['buyer2Deal'];
            $secondbuyername = $shortSaleInfo['buyerName2'];
            $secondcobuyername = $shortSaleInfo['coBuyerName2'];
            $secondbuyerphone = Strings::formatPhoneNumber($shortSaleInfo['secondBuyerPhone']);
            $secondbuyeremail = $shortSaleInfo['secondBuyerEmail'];
            $offer2 = $shortSaleInfo['offer2'];
            $sqft2 = $shortSaleInfo['sqft2'];
            $contractdate2 = $shortSaleInfo['contractDate2'];
            $closingdate2 = $shortSaleInfo['closingDate2'];
            $buyeragentname2 = $shortSaleInfo['buyer2AgentName'];
            $secondbuyeragency = $shortSaleInfo['buyer2AgencyName'];
            $buyerphone2 = Strings::formatPhoneNumber($shortSaleInfo['buyer2Phone']);
            $buyercell2 = Strings::formatPhoneNumber($shortSaleInfo['buyer2Cell']);
            $buyerfax2 = Strings::formatPhoneNumber($shortSaleInfo['buyer2Fax']);
            $buyeremail2 = $shortSaleInfo['buyer2Email'];
            $secondbuyersloanofficername = $shortSaleInfo['buyer2LOName'];
            $secondbuyersloanofficercompany = $shortSaleInfo['buyer2LOCompany'];
            $secondbuyerslophone = Strings::formatPhoneNumber($shortSaleInfo['buyer2LOPhone']);
            $secondbuyerslocell = Strings::formatPhoneNumber($shortSaleInfo['buyer2LOCell']);
            $secondbuyerslofax = Strings::formatPhoneNumber($shortSaleInfo['buyer2LOFax']);
            $secondbuyersloemail = $shortSaleInfo['buyer2LOEmail'];
            $relationshiptoseller2 = $shortSaleInfo['buyer2RelToSeller'];
            $offernotes2 = $shortSaleInfo['buyer2Notes'];

            $buyerdeal3 = $shortSaleInfo['buyer3Deal'];
            $thirdbuyername = $shortSaleInfo['buyerName3'];
            $thirdcobuyername = $shortSaleInfo['coBuyerName3'];
            $thirdbuyerphone = Strings::formatPhoneNumber($shortSaleInfo['thirdBuyerPhone']);
            $thirdbuyeremail = $shortSaleInfo['thirdBuyerEmail'];
            $offer3 = $shortSaleInfo['offer3'];
            $sqft3 = $shortSaleInfo['sqft3'];
            $contractdate3 = $shortSaleInfo['contractDate3'];
            $closingdate3 = $shortSaleInfo['closingDate3'];
            $buyeragentname3 = $shortSaleInfo['buyer3AgentName'];
            $thirdbuyeragency = $shortSaleInfo['buyer3AgencyName'];
            $buyerphone3 = Strings::formatPhoneNumber($shortSaleInfo['buyer3Phone']);
            $buyercell3 = Strings::formatPhoneNumber($shortSaleInfo['buyer3Cell']);
            $buyerfax3 = Strings::formatPhoneNumber($shortSaleInfo['buyer3Fax']);
            $buyeremail3 = $shortSaleInfo['buyer3Email'];
            $thirdbuyersloanofficername = $shortSaleInfo['buyer3LOName'];
            $thirdbuyersloanofficercompany = $shortSaleInfo['buyer3LOCompany'];
            $thirdbuyerslophone = Strings::formatPhoneNumber($shortSaleInfo['buyer3LOPhone']);
            $thirdbuyerslocell = Strings::formatPhoneNumber($shortSaleInfo['buyer3LOCell']);
            $thirdbuyerslofax = Strings::formatPhoneNumber($shortSaleInfo['buyer3LOFax']);
            $thirdbuyersloemail = $shortSaleInfo['buyer3LOEmail'];
            $relationshiptoseller3 = $shortSaleInfo['buyer3RelToSeller'];
            $offernotes3 = $shortSaleInfo['buyer3Notes'];
            $bpovalue = Currency::formatDollarAmountWithDecimal($shortSaleInfo['BPO1Value']);

            if (Dates::IsEmpty($listingdate)) {
                $listingdate = '';
            } else {
                $listingdate = Dates::formatDateWithRE($listingdate, 'YMD', 'm/d/Y');
            }
            if (Dates::IsEmpty($contractdate1)) {
                $contractdate1 = '';
            } else {
                $contractdate1 = Dates::formatDateWithRE($contractdate1, 'YMD', 'm/d/Y');
            }
            if (Dates::IsEmpty($closingdate1)) {
                $closingdate1 = '';
            } else {
                $closingdate1 = Dates::formatDateWithRE($closingdate1, 'YMD', 'm/d/Y');
            }
            if (Dates::IsEmpty($contractdate2)) {
                $contractdate2 = '';
            } else {
                $contractdate2 = Dates::formatDateWithRE($contractdate2, 'YMD', 'm/d/Y');
            }
            if (Dates::IsEmpty($closingdate2)) {
                $closingdate2 = '';
            } else {
                $closingdate2 = Dates::formatDateWithRE($closingdate2, 'YMD', 'm/d/Y');
            }
            if (Dates::IsEmpty($contractdate3)) {
                $contractdate3 = '';
            } else {
                $contractdate3 = Dates::formatDateWithRE($contractdate3, 'YMD', 'm/d/Y');
            }
            if (Dates::IsEmpty($closingdate3)) {
                $closingdate3 = '';
            } else {
                $closingdate3 = Dates::formatDateWithRE($closingdate3, 'YMD', 'm/d/Y');
            }
        }

        if (in_array('Types of Required Insurance', exportClientFiles::$selectedFieldsArray)) {
            $getInsuranceTypesGlobal = getInsuranceTypes::getReport();
        }
        for ($insEach = 0; $insEach <= 4; $insEach++) {
            $inScountVa = $insEach + 1;
            if (in_array('Types of Required Insurance', exportClientFiles::$selectedFieldsArray)) {
                self::addHeader(self::getColumnOrder('Types of Required Insurance', $inScountVa) . 'Types of Required Insurance ' . $inScountVa);
                $policyTypes = [];
                if ($HMLOInsuranceInfo[$insEach]['policyType'] ?? null) {
                    $policyTypesArray = explode(',', $HMLOInsuranceInfo[$insEach]['policyType']);
                    foreach ($policyTypesArray as $eachPolicyType) {
                        $policyTypes[] = $getInsuranceTypesGlobal[trim($eachPolicyType)];
                    }
                }
                exportClientFiles::$selectedFieldsValues[self::getColumnOrder('Types of Required Insurance', $inScountVa) . 'Types of Required Insurance ' . $inScountVa] = $HMLOInsuranceInfo[$insEach]['policyType'] ?? null ? implode(',', $policyTypes) : '';
            }
            if (in_array('Name of Carrier', exportClientFiles::$selectedFieldsArray)) {
                self::addHeader(self::getColumnOrder('Name of Carrier', $inScountVa) . 'Name of Carrier ' . $inScountVa);
                exportClientFiles::$selectedFieldsValues[self::getColumnOrder('Name of Carrier', $inScountVa) . 'Name of Carrier ' . $inScountVa] = $HMLOInsuranceInfo[$insEach]['policyCarrier'] ?? '';
            }
            if (in_array('Policy Name', exportClientFiles::$selectedFieldsArray)) {
                self::addHeader(self::getColumnOrder('Policy Name', $inScountVa) . 'Policy Name ' . $inScountVa);
                exportClientFiles::$selectedFieldsValues[self::getColumnOrder('Policy Name', $inScountVa) . 'Policy Name ' . $inScountVa] = $HMLOInsuranceInfo[$insEach]['policyName'] ?? '';
            }
            if (in_array('Insurance Policy Number', exportClientFiles::$selectedFieldsArray)) {
                self::addHeader(self::getColumnOrder('Insurance Policy Number', $inScountVa) . 'Policy Number ' . $inScountVa);
                exportClientFiles::$selectedFieldsValues[self::getColumnOrder('Insurance Policy Number', $inScountVa) . 'Policy Number ' . $inScountVa] = $HMLOInsuranceInfo[$insEach]['policyNumber'] ?? '';
            }
            if (in_array('Annual Premium', exportClientFiles::$selectedFieldsArray)) {
                self::addHeader(self::getColumnOrder('Annual Premium', $inScountVa) . 'Annual Premium ' . $inScountVa);
                exportClientFiles::$selectedFieldsValues[self::getColumnOrder('Annual Premium', $inScountVa) . 'Annual Premium ' . $inScountVa] = $HMLOInsuranceInfo[$insEach]['policyAnnualPremium'] ?? '';
            }
            if (in_array('Policy Expiration Date', exportClientFiles::$selectedFieldsArray)) {
                self::addHeader(self::getColumnOrder('Policy Expiration Date', $inScountVa) . 'Policy Expiry Date ' . $inScountVa);
                exportClientFiles::$selectedFieldsValues[self::getColumnOrder('Policy Expiration Date', $inScountVa) . 'Policy Expiry Date ' . $inScountVa] = isset($HMLOInsuranceInfo[$insEach]['policyExpDate']) ? Dates::formatDateWithRE($HMLOInsuranceInfo[$insEach]['policyExpDate'], 'YMD', 'm/d/Y') : '';
            }
        }

        for ($insEach = 0; $insEach < exportClientFiles::$drawaloopcount; $insEach++) {
            $inScountVal = $insEach + 1;
            if (in_array(self::getColumnOrder('Draw Amount', $inScountVal) . 'Draw Amount', exportClientFiles::$selectedFieldsArray)) {
                self::addHeader(self::getColumnOrder('Draw Amount', $inScountVal) . 'Draw Amount ' . $inScountVal);
                exportClientFiles::$selectedFieldsValues[self::getColumnOrder('Draw Amount', $inScountVal) . 'Draw Amount ' . $inScountVal] = $budgetAndDrawsInfo[$insEach]['drawFunded'] ?? 0;
            }
            if (in_array('Draw Date', exportClientFiles::$selectedFieldsArray)) {
                self::addHeader(self::getColumnOrder('Draw Date', $inScountVal) . 'Draw Date ' . $inScountVal);
                exportClientFiles::$selectedFieldsValues[self::getColumnOrder('Draw Date', $inScountVal) . 'Draw Date ' . $inScountVal] = isset($budgetAndDrawsInfo[$insEach]['dateFunded']) ? Dates::formatDateWithRE(($budgetAndDrawsInfo[$insEach]['dateFunded']), 'YMD', 'm/d/Y') : '';
            }
        }

        for ($insEach = 0; $insEach < 1; $insEach++) {
            $inScountVal = $insEach + 1;
            if (in_array('Appraiser Name', exportClientFiles::$selectedFieldsArray)) {
                self::addHeader(self::getColumnOrder('Appraiser Name', $inScountVal) . 'Appraiser Name ' . $inScountVal);
                if ($insEach == 0) {
                    exportClientFiles::$selectedFieldsValues[self::getColumnOrder('Appraiser Name', $inScountVal) . 'Appraiser Name ' . $inScountVal] = $appraiser1['contactName'] ?? '';
                } else {
                    exportClientFiles::$selectedFieldsValues[self::getColumnOrder('Appraiser Name', $inScountVal) . 'Appraiser Name ' . $inScountVal] = $appraiser2['contactName'] ?? '';
                }
            }

            if (in_array('Appraiser Phone Number', exportClientFiles::$selectedFieldsArray)) {
                self::addHeader(self::getColumnOrder('Appraiser Phone Number', $inScountVal) . 'Appraiser Phone Number ' . $inScountVal);
                if ($insEach == 0) {
                    exportClientFiles::$selectedFieldsValues[self::getColumnOrder('Appraiser Phone Number', $inScountVal) . 'Appraiser Phone Number ' . $inScountVal] = Strings::formatPhoneNumber($appraiser1['phone'] ?? '');
                } else {
                    exportClientFiles::$selectedFieldsValues[self::getColumnOrder('Appraiser Phone Number', $inScountVal) . 'Appraiser Phone Number ' . $inScountVal] = Strings::formatPhoneNumber($appraiser2['phone'] ?? '');
                }
            }

            if (in_array('Appraiser Email', exportClientFiles::$selectedFieldsArray)) {
                self::addHeader(self::getColumnOrder('Appraiser Email', $inScountVal) . 'Appraiser Email ' . $inScountVal);
                if ($insEach == 0) {
                    exportClientFiles::$selectedFieldsValues[self::getColumnOrder('Appraiser Email', $inScountVal) . 'Appraiser Email ' . $inScountVal] = ($appraiser1['email'] ?? '');
                } else {
                    exportClientFiles::$selectedFieldsValues[self::getColumnOrder('Appraiser Email', $inScountVal) . 'Appraiser Email ' . $inScountVal] = ($appraiser2['email'] ?? '');
                }
            }

            if (in_array('Appraiser Company', exportClientFiles::$selectedFieldsArray)) {
                self::addHeader(self::getColumnOrder('Appraiser Company', $inScountVal) . 'Appraiser Company ' . $inScountVal);
                if ($insEach == 0) {
                    exportClientFiles::$selectedFieldsValues[self::getColumnOrder('Appraiser Company', $inScountVal) . 'Appraiser Company ' . $inScountVal] = ($appraiser1['companyName'] ?? '');
                } else {
                    exportClientFiles::$selectedFieldsValues[self::getColumnOrder('Appraiser Company', $inScountVal) . 'Appraiser Company ' . $inScountVal] = ($appraiser2['companyName'] ?? '');
                }
            }

            if (in_array('Appraised Value', exportClientFiles::$selectedFieldsArray)) {
                self::addHeader(self::getColumnOrder('Appraised Value', $inScountVal) . 'Appraised As Is Value ' . $inScountVal);
                if ($insEach == 0) {
                    exportClientFiles::$selectedFieldsValues[self::getColumnOrder('Appraised Value', $inScountVal) . 'Appraised As Is Value ' . $inScountVal] = isset($HMLOlistingRealtorInfo['appraiser1Value']) ? (Currency::formatDollarAmountWithDecimal($HMLOlistingRealtorInfo['appraiser1Value'])) : '';
                } else {
                    exportClientFiles::$selectedFieldsValues[self::getColumnOrder('Appraised Value', $inScountVal) . 'Appraised As Is Value ' . $inScountVal] = isset($HMLOlistingRealtorInfo['appraiser2Value']) ? (Currency::formatDollarAmountWithDecimal($HMLOlistingRealtorInfo['appraiser2Value'])) : '';
                }
            }

            if (in_array('Appraiser Rehabbed Value', exportClientFiles::$selectedFieldsArray)) {
                self::addHeader(self::getColumnOrder('Appraiser Rehabbed Value', $inScountVal) . 'Appraisal Rehabbed Value ' . $inScountVal);
                if ($insEach == 0) {
                    exportClientFiles::$selectedFieldsValues[self::getColumnOrder('Appraiser Rehabbed Value', $inScountVal) . 'Appraisal Rehabbed Value ' . $inScountVal] = isset($HMLOlistingRealtorInfo['rehabValue']) ? (Currency::formatDollarAmountWithDecimal($HMLOlistingRealtorInfo['rehabValue'])) : '';
                } else {
                    exportClientFiles::$selectedFieldsValues[self::getColumnOrder('Appraiser Rehabbed Value', $inScountVal) . 'Appraisal Rehabbed Value ' . $inScountVal] = isset($HMLOlistingRealtorInfo['rehabValue2']) ? (Currency::formatDollarAmountWithDecimal($HMLOlistingRealtorInfo['rehabValue2'])) : '';
                }
            }
            if (in_array('Appraiser Monthly Rent', exportClientFiles::$selectedFieldsArray)) {
                self::addHeader(self::getColumnOrder('Appraiser Monthly Rent', $inScountVal) . 'Appraisal Monthly Rent ' . $inScountVal);
                if ($insEach == 0) {
                    exportClientFiles::$selectedFieldsValues[self::getColumnOrder('Appraiser Monthly Rent', $inScountVal) . 'Appraisal Monthly Rent ' . $inScountVal] = isset($listingRealtorInfo2['appraiser1MonthlyRent']) ? (Currency::formatDollarAmountWithDecimal($listingRealtorInfo2['appraiser1MonthlyRent'])) : '';
                } else {
                    exportClientFiles::$selectedFieldsValues[self::getColumnOrder('Appraiser Monthly Rent', $inScountVal) . 'Appraisal Monthly Rent ' . $inScountVal] = isset($listingRealtorInfo2['appraiser2MonthlyRent']) ? (Currency::formatDollarAmountWithDecimal($listingRealtorInfo2['appraiser2MonthlyRent'])) : '';
                }
            }

            if (in_array('Appraisal Date', exportClientFiles::$selectedFieldsArray)) {
                self::addHeader(self::getColumnOrder('Appraisal Date', $inScountVal) . 'Appraisal Date Obtained ' . $inScountVal);
                if ($insEach == 0) {
                    exportClientFiles::$selectedFieldsValues[self::getColumnOrder('Appraisal Date', $inScountVal) . 'Appraisal Date Obtained ' . $inScountVal] = isset($HMLOlistingRealtorInfo['dateObtained']) ? Dates::formatDateWithRE(($HMLOlistingRealtorInfo['dateObtained']), 'YMD', 'm/d/Y') : '';
                } else {
                    exportClientFiles::$selectedFieldsValues[self::getColumnOrder('Appraisal Date', $inScountVal) . 'Appraisal Date Obtained ' . $inScountVal] = isset($HMLOlistingRealtorInfo['dateObtained2']) ? Dates::formatDateWithRE(($HMLOlistingRealtorInfo['dateObtained2']), 'YMD', 'm/d/Y') : '';
                }
            }

            if (in_array('Appraiser Order Date', exportClientFiles::$selectedFieldsArray)) {
                self::addHeader(self::getColumnOrder('Appraiser Order Date', $inScountVal) . 'Appraisal Order Date ' . $inScountVal);
                if ($insEach == 0) {
                    exportClientFiles::$selectedFieldsValues[self::getColumnOrder('Appraiser Order Date', $inScountVal) . 'Appraisal Order Date ' . $inScountVal] = isset($HMLOlistingRealtorInfo['appraisal1OrderDate']) ? Dates::formatDateWithRE(($HMLOlistingRealtorInfo['appraisal1OrderDate']), 'YMD', 'm/d/Y') : '';
                } else {
                    exportClientFiles::$selectedFieldsValues[self::getColumnOrder('Appraiser Order Date', $inScountVal) . 'Appraisal Order Date ' . $inScountVal] = isset($HMLOlistingRealtorInfo['appraisal2OrderDate']) ? Dates::formatDateWithRE(($HMLOlistingRealtorInfo['appraisal2OrderDate']), 'YMD', 'm/d/Y') : '';
                }
            }
        }

        $lenderrevenue = 0;
        if (glCustomJobForProcessingCompany::isCustomLenderRevenue(exportClientFiles::$PCID)) {
            $inAddArray = [
                'originationPointsValue' => $originationpointsvalue, // Origination Fee
                'applicationFee' => $applicationfee, // Application Fee
                'processingFee' => $processingfees, // Processing Fee
                'underwritingFees' => $underwritingfees, // Underwriting Fee
                'bufferAndMessengerFee' => $bufferAndMessengerFee, // Discount Fee
                'drawsSetUpFee' => $drawssetupfee, // Draw Set Up Fee
                'drawsFee' => $drawsfee, // Draw Fee
                'valuationAVMFee' => $valuationAVMFee, //Valuation – AVM
                'wireFee' => $wirefee, //Wire Fee
                'servicingSetUpFee' => $servicingsetupfee, //Servicing Set Up Fee
                'floodCertificateFee' => $floodCertificateFee, //Flood Certificate
                'backgroundCheckFee' => $backgroundCheckFee, //Background Check
                'creditReportFee' => $creditReportFee, //Credit Report
                'projectFeasibility' => $projectfeasibility, //Project Feasibility
                'appraisalFee' => $appraisalfee, //Appraisal Fee
            ];
            $inSubArray = [
                'thirdPartyFees' => $thirdPartyFees, //Lender Credit to Offset 3rd Party Fees
            ];

            $lenderrevenue = proposalFormula::getLenderRevenue($inAddArray, $inSubArray);
        }

        for ($insEach = 0; $insEach < 1; $insEach++) {
            $inScountVal = $insEach + 1;
            if (in_array('AVM Monthly Rent', exportClientFiles::$selectedFieldsArray)) {
                self::addHeader(self::getColumnOrder('AVM Monthly Rent', $inScountVal) . 'AVM Monthly Rent ' . $inScountVal);
                exportClientFiles::$selectedFieldsValues[self::getColumnOrder('AVM Monthly Rent', $inScountVal) . 'AVM Monthly Rent ' . $inScountVal] = isset($listingRealtorInfo2['AVM' . $inScountVal . 'MonthlyRent']) ? ($listingRealtorInfo2['AVM' . $inScountVal . 'MonthlyRent']) : '';
            }
            if (in_array('AVM As Is Value', exportClientFiles::$selectedFieldsArray)) {
                self::addHeader(self::getColumnOrder('AVM As Is Value', $inScountVal) . 'AVM As Is Value ' . $inScountVal);
                exportClientFiles::$selectedFieldsValues[self::getColumnOrder('AVM As Is Value', $inScountVal) . 'AVM As Is Value ' . $inScountVal] = $shortSaleInfo['AVM' . $inScountVal . 'Value'] ?? '';
            }

            if (in_array('BPO Value', exportClientFiles::$selectedFieldsArray)) {
                self::addHeader(self::getColumnOrder('BPO Value', $inScountVal) . 'BPO Value ' . $inScountVal);
                exportClientFiles::$selectedFieldsValues[self::getColumnOrder('BPO Value', $inScountVal) . 'BPO Value ' . $inScountVal] = $shortSaleInfo['BPO' . $inScountVal . 'Value'] ?? '';
            }

            if (in_array('BPO Rehabbed Value', exportClientFiles::$selectedFieldsArray)) {
                self::addHeader(self::getColumnOrder('BPO Rehabbed Value', $inScountVal) . 'BPO Rehabbed Value ' . $inScountVal);
                exportClientFiles::$selectedFieldsValues[self::getColumnOrder('BPO Rehabbed Value', $inScountVal) . 'BPO Rehabbed Value ' . $inScountVal] = $shortSaleInfo['rehabValue3'] ?? '';
            }
            if (in_array('BPO Monthly Rent', exportClientFiles::$selectedFieldsArray)) {
                self::addHeader(self::getColumnOrder('BPO Monthly Rent', $inScountVal) . 'BPO Monthly Rent ' . $inScountVal);
                exportClientFiles::$selectedFieldsValues[self::getColumnOrder('BPO Monthly Rent', $inScountVal) . 'BPO Monthly Rent ' . $inScountVal] = $shortSaleInfo['twelveMonthRent'] ?? '';
            }
            if (in_array('BPO Date Obtained', exportClientFiles::$selectedFieldsArray)) {
                self::addHeader(self::getColumnOrder('BPO Date Obtained', $inScountVal) . 'BPO Date Obtained ' . $inScountVal);
                exportClientFiles::$selectedFieldsValues[self::getColumnOrder('BPO Date Obtained', $inScountVal) . 'BPO Date Obtained ' . $inScountVal] = $shortSaleInfo['dateObtained3'] ?? '';
            }
        }


        exportClientFilesData::$fileid = $fileid;
        exportClientFilesData::$clientfirstname = $clientfirstname;
        exportClientFilesData::$clientlastname = $clientlastname;
        exportClientFilesData::$clientemail = $clientemail;

        exportClientFilesData::$propertyaddress = exportClientFiles::$primaryPropertyInfo->propertyAddress;
        exportClientFilesData::$propertycity = exportClientFiles::$primaryPropertyInfo->propertyCity;
        exportClientFilesData::$propertystate = exportClientFiles::$primaryPropertyInfo->propertyState;
        exportClientFilesData::$propertyzip = exportClientFiles::$primaryPropertyInfo->propertyZipCode;

        $loanSetupInfoArray = exportClientFiles::$loanSetupInfoArray[$LMRId]['loanTerms'] ?? [];
        $initialLoanTerms = $loanSetupInfoArray[0] ?? '';
        $secondaryLoanTerms = $loanSetupInfoArray[1] ?? '';

        $myFileInfoObject = new myFileInfo();
        $myFileInfoObject->LMRId = $LMRId;

        $fileAdminInfo = $myFileInfoObject->getFileAdminInfo();

        $csvArray = [];
        foreach (exportClientFiles::$selectedFieldsArray as $header) {
            if (in_array($header, self::$manyToOneHeaders)) {
                continue;
            }

            switch ($header) {
                case 'File ID':
                    $csvArray[$header] = $LMRId;
                    break;
                case 'Assigned Employees':
                    $csvArray[$header] = $assignedemployees;
                    break;

                case 'Borrower First Name':
                    $csvArray[$header] = $borrowerfirstname;
                    break;
                case 'Borrower Last Name':
                    $csvArray[$header] = $borrowerlastname;
                    break;
                case 'Borrower Phone Number':
                    $csvArray[$header] = $borrowerphonenumber;
                    break;
                case 'Borrower Cell Number':
                    $csvArray[$header] = $borrowercellnumber;
                    break;
                case 'Borrower Fax Number':
                    $csvArray[$header] = $borrowerfaxnumber;
                    break;

                case 'SMS Service Provider':
                    $csvArray[$header] = $SMSServiceProviderArray[trim($myFileInfo['serviceProvider'])] ?? '';
                    break;
                case 'Borrower Email':
                    $csvArray[$header] = $borroweremail;
                    break;
                case 'Borrower Secondary Email':
                    $csvArray[$header] = $borrowersecondaryemail;
                    break;
                case 'Client SSN Last 4':
                    $csvArray[$header] = $clientssnlast4;
                    break;
                case 'Full SSN':
                    $csvArray[$header] = $fullssn;
                    break;
                case 'Borrower Date Of Birth':
                    $csvArray[$header] = $borrowerdateofbirth;
                    break;
                case 'Business Entity Name':
                    $csvArray[$header] = $fileHMLOEntityInfo['entityName'];
                    break;
                case 'Net Worth':
                    $csvArray[$header] = $networth;
                    break;
                case 'Credit Score range':
                    $csvArray[$header] = $creditscorerange;
                    break;
                case 'Experian':
                    $csvArray[$header] = $experian;
                    break;
                case 'Equifax':
                    $csvArray[$header] = $equifax;
                    break;
                case 'Transunion':
                    $csvArray[$header] = $transunion;
                    break;
                case 'Mid Fico Score':
                    $csvArray[$header] = $midficoscore;
                    break;
                case 'No of properties completed in last 36 months':
                    $csvArray[$header] = exportClientFiles::$fileHMLOExperienceInfoArray[$LMRId]['borNoOfREPropertiesCompleted'] ?? '';
                    break;
                case 'No of rehab/Construction done':
                    $csvArray[$header] = exportClientFiles::$fileHMLOExperienceInfoArray[$LMRId]['borRehabPropCompleted'] ?? '';
                    break;
                case 'Years of rehab/construction Exp':
                    $csvArray[$header] = exportClientFiles::$fileHMLOExperienceInfoArray[$LMRId]['borNoOfYearRehabExperience'] ?? '';
                    break;
                case 'No of investment properties':
                    $csvArray[$header] = exportClientFiles::$fileHMLOExperienceInfoArray[$LMRId]['borNoOfOwnProp'] ?? '';
                    break;
                case 'US Citizen':
                    $csvArray[$header] = exportClientFiles::$fileHMLOBackGroundInfoArray[$LMRId]['isBorUSCitizen'] ?? '';
                    break;
                case 'Total Cash In Bank Accounts':
                    $csvArray[$header] = $totalcashinbankaccounts;
                    break;
                case 'Co-borrower First Name':
                    $csvArray[$header] = $coborrowerfirstname;
                    break;
                case 'Co-borrower Last Name':
                    $csvArray[$header] = $coborrowerlastname;
                    break;
                case 'Co-borrower Phone Number':
                    $csvArray[$header] = $coborrowerphonenumber;
                    break;
                case 'Co-borrower Cell Number':
                    $csvArray[$header] = $coborrowercellnumber;
                    break;
                case 'Co-borrower Fax Number':
                    $csvArray[$header] = $coborrowerfaxnumber;
                    break;
                case 'Co-borrower SMS Service Provider':
                    $csvArray[$header] = $SMSServiceProviderArray[trim($myFileInfo['coBServiceProvider'])] ?? '';
                    break;
                case 'Co-borrower Email':
                    $csvArray[$header] = $coborroweremail;
                    break;
                case 'Co-Borrower SSN Last 4':
                    $csvArray[$header] = $coborrowerssnlast4;
                    break;
                case 'Co-Borrower Full SSN':
                    $csvArray[$header] = $coborrowerfullssn;
                    break;
                case 'Co-Borrower DOB':
                    $csvArray[$header] = $coborrowerdob;
                    break;
                case 'Borrower Address':
                    $csvArray[$header] = $file2Info[$LMRId]['presentAddress'] ?? '';
                    break;
                case 'Borrower City':
                    $csvArray[$header] = $file2Info[$LMRId]['presentCity'] ?? '';
                    break;
                case 'Borrower State':
                    $csvArray[$header] = $file2Info[$LMRId]['presentState'] ?? '';
                    break;
                case 'Borrower Zip':
                    $csvArray[$header] = $file2Info[$LMRId]['presentZip'] ?? '';
                    break;
                case 'Mailing Address':
                    $csvArray[$header] = $mailingaddress;
                    break;
                case 'Mailing City':
                    $csvArray[$header] = $mailingcity;
                    break;
                case 'Mailing State':
                    $csvArray[$header] = $mailingstate;
                    break;
                case 'Mailing Zip':
                    $csvArray[$header] = $mailingzip;
                    break;
                case 'Borrower Furnish this information':
                    $csvArray[$header] = $borrowerfurnishthisinformation;
                    break;
                case 'Borrower Ethnicity':
                    $csvArray[$header] = $borrowerethnicity;
                    break;
                case 'Borrower Ethnicity Sub':
                    $csvArray[$header] = $borrowerethnicitysub;
                    break;
                case 'Borrower Race':
                    $csvArray[$header] = $borrowerrace;
                    break;
                case 'Borrower Race Sub':
                    $csvArray[$header] = $borrowerracesub;
                    break;
                case 'Borrower Ethnicity Print Origin':
                    $csvArray[$header] = $QAInfo['bFiEthnicitySubOther'];
                    break;
                case 'Borrower Asian Print Race':
                    $csvArray[$header] = $QAInfo['bFiRaceAsianOther'];
                    break;
                case 'Borrower Pacific Print Race':
                    $csvArray[$header] = $QAInfo['bFiRacePacificOther'];
                    break;
                case 'Co-Borrower Ethnicity Print Origin':
                    $csvArray[$header] = $QAInfo['CBEthnicitySubOther'];
                    break;
                case 'Co-Borrower Asian Print Race':
                    $csvArray[$header] = $QAInfo['CBRaceAsianOther'];
                    break;
                case 'Co-Borrower Pacific Print Race':
                    $csvArray[$header] = $QAInfo['CBRacePacificOther'];
                    break;
                case 'Borrower Sex':
                    $csvArray[$header] = $borrowersex;
                    break;
                case 'Borrower Veteran':
                    $csvArray[$header] = $borrowerveteran;
                    break;
                case 'Co-Borrower Furnish this information':
                    $csvArray[$header] = $coborrowerfurnishthisinformation;
                    break;
                case 'Co-Borrower Ethnicity':
                    $csvArray[$header] = $coborrowerethnicity;
                    break;
                case 'Co-Borrower Ethnicity Sub':
                    $csvArray[$header] = $coborrowerethnicitysub;
                    break;
                case 'Co-Borrower Race':
                    $csvArray[$header] = $coborrowerrace;
                    break;
                case 'Co-Borrower Race Sub':
                    $csvArray[$header] = $coborrowerracesub;
                    break;
                case 'Co-Borrower Sex':
                    $csvArray[$header] = $coborrowersex;
                    break;
                case 'Co-Borrower Veteran':
                    $csvArray[$header] = $coborrowerveteran;
                    break;
                case 'Entity Name':
                    $csvArray[$header] = $fileHMLOEntityInfo['entityName'] ?? '';
                    break;
                case 'DBA Name':
                    $csvArray[$header] = $fileHMLOEntityInfo['tradeName'] ?? '';
                    break;
                case 'EIN':
                    $csvArray[$header] = $fileHMLOEntityInfo['ENINo'] ?? '';
                    break;
                case 'Entity Type':
                    $csvArray[$header] = $fileHMLOEntityInfo['entityType'] ?? '';
                    break;
                case 'Entity Address':
                    $csvArray[$header] = $fileHMLOEntityInfo['entityAddress'] ?? '';
                    break;
                case 'Entity City':
                    $csvArray[$header] = $fileHMLOEntityInfo['entityCity'] ?? '';
                    break;
                case 'Entity State':
                    $csvArray[$header] = $fileHMLOEntityInfo['entityState'] ?? '';
                    break;
                case 'Entity Zip':
                    $csvArray[$header] = $fileHMLOEntityInfo['entityZip'] ?? '';
                    break;
                case 'Of Employees':
                    $csvArray[$header] = $fileHMLOEntityInfo['noOfEmployees'] ?? '';
                    break;
                case 'Property Ownership':
                    $csvArray[$header] = $fileHMLOEntityInfo['entityPropertyOwnerShip'] ?? '';
                    break;
                case 'Value Of Property':
                    $csvArray[$header] = $fileHMLOEntityInfo['valueOfProperty'] ?? '';
                    break;
                case 'Naics Code':
                    $csvArray[$header] = $fileHMLOEntityInfo['naicsCode'] ?? '';
                    break;
                case 'Originator':
                    $csvArray[$header] = $fileHMLONewLoanInfo['HMLOLender'] ?? '';
                    break;
                case 'Loan Terms Expire Date':
                    $csvArray[$header] = Dates::formatDateWithRE($fileHMLONewLoanInfo['loanTermExpireDate'] ?? '', 'YMD', 'm/d/Y');
                    break;
                case 'Actual Closing Date':
                    $csvArray[$header] = $closingdate;
                    break;
                case 'Target Closing Date':
                    $csvArray[$header] = Dates::formatDateWithRE($myFileInfoObject->fileHMLOInfo()->targetClosingDate, 'YMD', 'm/d/Y');
                    break;
                case 'Loan Program':
                    $csvArray[$header] = $services;
                    break;
                case 'Internal Loan Program':
                    $csvArray[$header] = self::getInternalLoanProgram($fileInternalLP);
                    break;
                case 'Transaction Type':
                    $csvArray[$header] = $HMLOPropInfo['typeOfHMLOLoanRequesting'] ?? '';
                    break;
                case 'Borrower Occupancy':
                    $csvArray[$header] = $propertyInfo['isHouseProperty'] ?? '';
                    break;
                case 'Mortgage payment':
                    $csvArray[$header] = $myFileInfo['lien1Payment'] ?? '';
                    break;
                case 'Escrowed Taxes':
                    $csvArray[$header] = $incomeInfo['taxes1'] ?? '';
                    break;
                case 'Escrowed Insurance':
                    $csvArray[$header] = $HMLOPropInfo['annualPremium'] ?? '';
                    break;
                case 'Monthly Payment':
                    $csvArray[$header] = $myFileInfo['lien1Payment'] ?? '';
                    break;
                case 'No of properties collateralized':
                    $csvArray[$header] = $fileHMLONewLoanInfo['noOfPropertiesAcquiring'] ?? '';
                    break;
                case 'Acquisition Price Financed':
                    $csvArray[$header] = HMLOLoanTermsCalculation::$acquisitionPriceFinanced;
                    break;
                case 'Acquisition Down Payment':
                    $csvArray[$header] = $HMLOPropInfo['maxAmtToPutDown'] ?? '';
                    break;
                case 'Rehab/Construction Cost':
                    $csvArray[$header] = $fileHMLOInfo['rehabCost'] ?? '';
                    break;
                case 'Rehab Cost Financed':
                    $rehabCostFinanced = floatval($fileHMLONewLoanInfo['rehabCostFinanced'] ?? 0);
                    if (!$rehabCostFinanced) {
                        $rehabCostPercentageFinanced = floatval($fileHMLONewLoanInfo['rehabCostPercentageFinanced'] ?? 0);
                        $rehabCost = $fileHMLONewLoanInfo['rehabCost'];
                        $rehabCostFinanced = $rehabCostPercentageFinanced / 100.0 * $rehabCost;
                    }

                    $csvArray[$header] = $rehabCostFinanced;
                    break;
                case 'Initial Draw Amount':
                    $csvArray[$header] = $fileHMLONewLoanInfo['initialAdvance'] ?? '';
                    break;
                case 'As is Value':
                    $csvArray[$header] = $asisvalue;
                    break;
                case 'After Repair Value':
                    $csvArray[$header] = $afterrepairvalue;
                    break;
                case 'Closing Cost Financed':
                    $csvArray[$header] = $closingCostFinanced;
                    break;
                case 'Total Project Cost':
                    $csvArray[$header] = HMLOLoanTermsCalculation::$NewTotalProjectCost ?? '';
                    break;
                case 'Simple ARV %':
                    $csvArray[$header] = HMLOLoanTermsCalculation::$simpleARV ?? '';
                    break;
                case 'Full ARV %':
                    $csvArray[$header] = HMLOLoanTermsCalculation::$ARV ?? '';
                    break;
                case 'Acquisition LTV':
                    $csvArray[$header] = HMLOLoanTermsCalculation::$acquisitionLTV ?? '';
                    break;
                case 'Market LTV':
                    $csvArray[$header] = HMLOLoanTermsCalculation::$marketLTV ?? '';
                    break;
                case 'Loan To Cost':
                    $csvArray[$header] = HMLOLoanTermsCalculation::$NewLoanToCost;
                    break;
                case '% of Rehab Cost Financed':
                    $csvArray[$header] = HMLOLoanTermsCalculation::$rehabCostPercentageFinanced;
                    break;
                case 'Cash to Close':
                    $csvArray[$header] = HMLOLoanTermsCalculation::$totalCashToClose;
                    break;
                case 'Required Reserves':
                    $csvArray[$header] = HMLOLoanTermsCalculation::$totalRequiredReserves;
                    break;
                case 'Estate held in':
                    $csvArray[$header] = $estateheldin;
                    break;
                case 'Lien Position':
                    $csvArray[$header] = $glHMLOLienPosition[$HMLOPropInfo['lienPosition']] ?? '';
                    break;
                case 'Rehab Required':
                    $csvArray[$header] = $rehabrequired;
                    break;
                case 'Total Rehab Cost':
                    $csvArray[$header] = $totalestimatedrehabcost;
                    break;
                case 'Property Construction Level':
                    $csvArray[$header] = $HMLOPropInfo['propertyConstructionLevel'] ?? '';
                    break;
                case 'Exit Strategy':
                    $csvArray[$header] = $HMLOPropInfo['exitStrategy'] ?? '';
                    break;
                case 'Exit Strategy Explanation':
                    $csvArray[$header] = exportClientFiles::$LOExplanationInfo[$LMRId]['borComment'] ?? '';
                    break;
                case 'Initial Loan Amount':
                    $csvArray[$header] = HMLOLoanTermsCalculation::$initialLoanAmount;
                    break;
                case 'Total Loan Amount':
                    $csvArray[$header] = HMLOLoanTermsCalculation::$totalLoanAmount;
                    break;
                case 'Interest Rate':
                    $csvArray[$header] = $interestrate;
                    break;
                case 'Cost of Capital':
                    $csvArray[$header] = HMLOLoanTermsCalculation::$costOfCapital ?? '';
                    break;
                case 'Yield Spread':
                    $csvArray[$header] = HMLOLoanTermsCalculation::$yieldSpread ?? '';
                    break;
                case 'Purchase Price':
                    $csvArray[$header] = $HMLOlistingRealtorInfo['costBasis'];
                    break;
                case 'Loan Term':
                    $csvArray[$header] = $HMLOPropInfo['loanTerm'] ?? '';
                    break;
                case 'Loan Term (Numerical value only)':
                    $loanTermArray = explode(' ', $HMLOPropInfo['loanTerm'], 2);
                    $csvArray[$header] = $loanTermArray[0] ?? '';
                    break;
                case 'Loan Term (Text value only)':
                    $loanTermArray = explode(' ', $HMLOPropInfo['loanTerm'], 2);
                    $csvArray[$header] = $loanTermArray[1] ?? '';
                    break;
                case 'Current Loan Balance':
                    $csvArray[$header] = HMLOLoanTermsCalculation::$currentLoanBalance;
                    break;
                case 'Current Escrow Balance':
                    $csvArray[$header] = HMLOLoanTermsCalculation::$availableBudget;
                    break;
                case 'Pre-paid Interest Reserve':
                    $csvArray[$header] = HMLOLoanTermsCalculation::$prepaidInterestReserve ?? '';
                    break;
                case 'Total Draws Funded':
                    $csvArray[$header] = HMLOLoanTermsCalculation::$totalDrawsFunded;
                    break;
                case 'Draw Amount':
                    // TODO: this doesn't seem to exist
                    $csvArray[$header] = '';
                    break;
                case 'Draw Date':
                    // TODO: this doesn't seem to exist
                    $csvArray[$header] = '';
                    break;
                case 'Amortization':
                    $csvArray[$header] = $amortization;
                    break;
                case 'Amortization Type':
                    $csvArray[$header] = $amortizationtype;
                    break;
                case 'Pre-Payment Penalty Options':
                    $csvArray[$header] = $prepaymentpenaltyoptions;
                    break;
                case 'Servicing Number':
                    $csvArray[$header] = $servicingnumber;
                    break;
                case 'Desired Closing Date':
                    $csvArray[$header] = $desiredclosingdate;
                    break;
                case 'Days Until Closing':
                    $csvArray[$header] = $daysuntilclosing ?? '';
                    break;
                case 'Exit Fee Points':
                    $csvArray[$header] = $fileHMLONewLoanInfo['exitFeePoints'] ?? '';
                    break;
                case 'Exit Fee Amount':
                    $csvArray[$header] = $fileHMLONewLoanInfo['exitFeeAmount'] ?? '';
                    break;
                case 'Projected Foreclosure Date':
                    $csvArray[$header] = !Dates::IsEmpty($HMLOPropInfo['foreclosureDate'] ?? '') ? Dates::Datestamp($HMLOPropInfo['foreclosureDate'], '') : '';
                    break;
                case 'Pay Off Amount':
                    $csvArray[$header] = $payoffamount ?? '';
                    break;
                case 'Foreclosure Date':
                    $csvArray[$header] = $foreclosuredate;
                    break;
                case 'Accrual Type':
                    $csvArray[$header] = $accrualtype;
                    break;
                case 'Payment Based':
                    $csvArray[$header] = $paymentbased;
                    break;
                case 'Payment Frequency':
                    $csvArray[$header] = $paymentFrequency;
                    break;
                case 'Aggregate DSCR':
                    $csvArray[$header] = $fileHMLONewLoanInfo['aggregateDSCR'] ?? '';
                    break;
                case 'Total Cash Out':
                    $csvArray[$header] = HMLOLoanTermsCalculation::$totalCashOutAmt;
                    break;
                case 'Extension Option':
                    $csvArray[$header] = $fileHMLONewLoanInfo['extensionOption'] ?? '';
                    break;
                case 'Extension Option Points':
                    $csvArray[$header] = $fileHMLONewLoanInfo['extensionOptionPercentage'] ?? '';
                    break;
                case 'Extension Option Term':
                    $csvArray[$header] = $glHMLOExtensionOption[$fileHMLONewLoanInfo['extensionOption']] ?? '';
                    break;
                case 'Extension Option Rate':
                    $csvArray[$header] = $fileHMLONewLoanInfo['extensionRatePercentage'] ?? '';
                    break;
                case 'Rehab Budget':
                    $csvArray[$header] = $listingRealtorInfo2['intAssRehabBudget'] ?? '';
                    break;
                case 'Property Address':
                    $csvArray[$header] = (exportClientFiles::$primaryPropertyInfo->propertyAddress) ?? '';
                    break;
                case 'Property City':
                    $csvArray[$header] = (exportClientFiles::$primaryPropertyInfo->propertyCity) ?? '';
                    break;
                case 'Property State':
                    $csvArray[$header] = (exportClientFiles::$primaryPropertyInfo->propertyState) ?? '';
                    break;
                case 'Property Zip':
                    $csvArray[$header] = (exportClientFiles::$primaryPropertyInfo->propertyZipCode) ?? '';
                    break;
                case 'Property County':
                    $csvArray[$header] = (exportClientFiles::$primaryPropertyInfo->propertyCounty) ?? '';
                    break;
                case 'Present Occupancy':
                    $csvArray[$header] = (exportClientFiles::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId()->propertyPresentOccupancy) ?? '';
                    break;
                case 'Property Condition':
                    $csvArray[$header] = (exportClientFiles::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId()->propertyCondition) ?? '';
                    break;
                case 'Property Type':
                    $csvArray[$header] = (exportClientFiles::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyType) ? (GpropertyTypeNumbArray::$GpropertyTypeNumbArray[exportClientFiles::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyType]) : '';
                    break;
                case 'Occupancy Notes':
                    $csvArray[$header] = (exportClientFiles::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId()->propertyOccupancyNotes) ?? '';
                    break;
                case 'Condition Notes':
                    $csvArray[$header] = (exportClientFiles::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId()->propertyConditionNotes) ?? '';
                    break;
                case 'Features':
                    $csvArray[$header] = (exportClientFiles::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyFeatures) ?? '';
                    break;
                case 'Year Built':
                    $csvArray[$header] = (exportClientFiles::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyYearBuilt) ?? '';
                    break;
                case 'Lot Size':
                    $csvArray[$header] = (exportClientFiles::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyAcres) ?? '';
                    break;
                case 'Total Sq Ft':
                    $csvArray[$header] = (exportClientFiles::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertySqFt) ?? '';
                    break;
                case 'Bedrooms':
                    $csvArray[$header] = (exportClientFiles::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyNumberOfBedRooms) ?? '';
                    break;
                case 'Bathrooms':
                    $csvArray[$header] = (exportClientFiles::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyNumberOfBathRooms) ?? '';
                    break;
                case 'Tax Year':
                    $csvArray[$header] = (exportClientFiles::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId()->propertyTaxYear) ?? '';
                    break;
                case 'Legal Description':
                    $csvArray[$header] = (exportClientFiles::$primaryPropertyInfo->propertyLegalDescription) ?? '';
                    break;
                case 'URL link to property':
                    $csvArray[$header] = self::getPropertyLink(
                        (exportClientFiles::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId()->propertyURLLink1) ?? '',
                        (exportClientFiles::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId()->propertyURLLink2) ?? ''
                    );
                    break;
                case 'Property Access Contact Name':
                    $csvArray[$header] = (exportClientFiles::$primaryPropertyInfo->getTblPropertiesAccess_by_propertyId()->propertyAccessName) ?? '';
                    break;
                case 'Property Access Contact Phone':
                    $csvArray[$header] = (exportClientFiles::$primaryPropertyInfo->getTblPropertiesAccess_by_propertyId()->propertyAccessPhone) ? Strings::formatPhoneNumber(exportClientFiles::$primaryPropertyInfo->getTblPropertiesAccess_by_propertyId()->propertyAccessPhone) : '';
                    break;
                case 'Lock Box Info':
                    $csvArray[$header] = (exportClientFiles::$primaryPropertyInfo->getTblPropertiesAccess_by_propertyId()->propertyAccessLockBoxInfo) ?? '';
                    break;
                case 'Annual Property Tax':
                    $csvArray[$header] = exportClientFiles::$incomeInfoArray[$LMRId]['taxes1'] ?? '';
                    break;
                case 'Parcel No':
                    $csvArray[$header] = (exportClientFiles::$primaryPropertyInfo->propertyParcelNumber) ?? '';
                    break;
                case '# of Units':
                    $csvArray[$header] = (exportClientFiles::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyNumberOfUnits) ?? '';
                    break;
                case '# Half Baths':
                    $csvArray[$header] = (exportClientFiles::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyNumberOfHalfBathRooms) ?? '';
                    break;
                case 'Cash-Flow HOA Fees':
                    $csvArray[$header] = Currency::formatDollarAmountWithDecimal(trim($fileHMLONewLoanInfo['spcf_hoafees']));
                    break;
                case 'Gross Potential Income':
                    $csvArray[$header] = Currency::formatDollarAmountWithDecimal(trim($fileHMLONewLoanInfo['actualRentsInPlace']));
                    break;
                case 'Debt Service Ratio':
                    $csvArray[$header] = $debtserviceratio;
                    break;
                case 'Debt Service Ratio PITIA':
                    $csvArray[$header] = $debtserviceratiopitia;
                    break;
                case 'HOA Monthly Fees':
                    $csvArray[$header] = $incomeInfo['HOAFees1'] ?? '';
                    break;
                case 'Municipality':
                    $csvArray[$header] = (exportClientFiles::$primaryPropertyInfo->propertyMunicipality) ?? '';
                    break;
                case 'Is this loan being cross collateralized?':
                    $csvArray[$header] = $HMLOPropInfo['isBlanketLoan'] ?? '';
                    break;
                case 'Property 1 Estimates value':
                    $csvArray[$header] = (exportClientFiles::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId()->propertyEstimatedValue) ?? '';
                    break;
                case 'Property 1 Zillow value':
                    $csvArray[$header] = (exportClientFiles::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId()->propertyZillowValue);
                    break;
                case 'zillow Value':
                    $csvArray[$header] = $HMLOlistingRealtorInfo['zillowValue'] ?? '';
                    break;
                case 'zillow Rent Value':
                    $csvArray[$header] = Currency::formatDollarAmountWithDecimal($shortSaleInfo['zillowRentValue'] ?? '');
                    break;
                case 'Appraiser Name':
                    $csvArray[$header] = '';
                    break;
                case 'Appraiser Phone Number':
                    $csvArray[$header] = Strings::formatPhoneNumber($listingRealtorInfo2['intAssPhone'] ?? '');
                    break;
                case 'Appraiser Email':
                    $csvArray[$header] = $listingRealtorInfo2['intAssEmail'] ?? '';
                    break;
                case 'Appraiser Company':
                    $csvArray[$header] = $appraiserInfo['companyName'] ?? '';
                    break;
                case 'Appraised Value':
                    // TODO: this doesn't seem to exist
                    $csvArray[$header] = '';
                    break;
                case 'Appraiser Rehabbed Value':
                    // TODO: this doesn't seem to exist
                    $csvArray[$header] = '';
                    break;
                case 'Appraiser Monthly Rent':
                    // TODO: this doesn't seem to exist
                    $csvArray[$header] = '';
                    break;
                case 'Appraisal Date':
                    // TODO: this doesn't seem to exist
                    $csvArray[$header] = '';
                    break;
                case 'Appraiser Order Date':
                    // TODO: this doesn't seem to exist
                    $csvArray[$header] = '';
                    break;
                case 'AVM As Is Value':
                    // TODO: this doesn't seem to exist
                    $csvArray[$header] = '';
                    break;
                case 'AVM Monthly Rent':
                    // TODO: this doesn't seem to exist
                    $csvArray[$header] = '';
                    break;
                case 'BPO Value':
                    // TODO: this doesn't seem to exist
                    $csvArray[$header] = '';
                    break;
                case 'BPO Rehabbed Value':
                    // TODO: this doesn't seem to exist
                    $csvArray[$header] = '';
                    break;
                case 'BPO Monthly Rent':
                    // TODO: this doesn't seem to exist
                    $csvArray[$header] = '';
                    break;
                case 'BPO Date Obtained':
                    // TODO: this doesn't seem to exist
                    $csvArray[$header] = '';
                    break;
                case 'Property Value As Is':
                    $csvArray[$header] = $myFileInfo['homeValue'];
                    break;
                case 'Assessed Monthly Rent':
                    $csvArray[$header] = $listingRealtorInfo2['assessedRentValue'] ?? '';
                    break;
                case 'Valuation Notes':
                    $csvArray[$header] = $HMLOlistingRealtorInfo['appraisalNotes'] ?? '';
                    break;
                case 'Required Valuation Methods':
                    $csvArray[$header] = self::requiredValuation($HMLOPropInfo);
                    break;
                case 'Title Ordered Date':
                    $csvArray[$header] = $titleOrderedDate ?? '';
                    break;
                case 'Name on Title':
                    $csvArray[$header] = $propertyInfo['titleSeller'] ?? '';
                    break;
                case 'Title Rep First Name':
                    $csvArray[$header] = $titleInfo['contactName'] ?? '';
                    break;
                case 'Title Rep Last Name':
                    $csvArray[$header] = $titleInfo['contactLName'] ?? '';
                    break;
                case 'Title Company':
                    $csvArray[$header] = $titleInfo['companyName'] ?? '';
                    break;
                case 'Title Email':
                    $csvArray[$header] = $titleInfo['email'] ?? '';
                    break;
                case 'Title Phone':
                    $csvArray[$header] = Strings::formatPhoneNumber($titleInfo['phone'] ?? '');
                    break;
                case 'Attorney Name':
                    $csvArray[$header] = $attorneyInfo['contactName'] ?? '';
                    break;
                case 'Attorney Firm Name':
                    $csvArray[$header] = $attorneyInfo['companyName'] ?? '';
                    break;
                case 'Attorney Email':
                    $csvArray[$header] = $attorneyInfo['email'] ?? '';
                    break;
                case 'Attorney Phone':
                    $csvArray[$header] = Strings::formatPhoneNumber($attorneyInfo['phone'] ?? '');
                    break;
                case 'Name of Company':
                    $csvArray[$header] = $insuranceInfo['companyName'] ?? '';
                    break;
                case 'Insurance Rep First Name':
                    $csvArray[$header] = $insuranceInfo['contactName'] ?? '';
                    break;
                case 'Insurance Rep Last Name':
                    $csvArray[$header] = $insuranceInfo['contactLName'] ?? '';
                    break;
                case 'Insurance Rep Email':
                    $csvArray[$header] = $insuranceInfo['email'] ?? '';
                    break;
                case 'Insurance Rep Phone':
                    $csvArray[$header] = Strings::formatPhoneNumber($insuranceInfo['phone'] ?? '');
                    break;
                case 'Insurance Rep Cell':
                    $csvArray[$header] = Strings::formatPhoneNumber($insuranceInfo['cell'] ?? '');
                    break;
                case 'Types of Required Insurance':
                    // TODO: this doesn't seem to exist
                    $csvArray[$header] = '';
                    break;
                case 'Name of Carrier':
                    // TODO: this doesn't seem to exist
                    $csvArray[$header] = '';
                    break;
                case 'Policy Name':
                    // TODO: this doesn't seem to exist
                    $csvArray[$header] = '';
                    break;
                case 'Insurance Policy Number':
                    $csvArray[$header] = $HMLOPropInfo['proInsPolicyNo'] ?? '';
                    break;
                case 'Annual Premium':
                    $csvArray[$header] = $HMLOPropInfo['annualPremium'] ?? '';
                    break;
                case 'Policy Expiration Date':
                    $csvArray[$header] = Dates::formatDateWithRE($HMLOPropInfo['proInsPolicyExpDate'], 'YMD', 'm/d/Y');
                    break;
                case 'Borrower Occupation':
                    $csvArray[$header] = $incomeInfo['occupation1'] ?? '';
                    break;
                case 'Borrower Employment Type':
                    $csvArray[$header] = $incomeInfo['employedInfo1'] ?? '';
                    break;
                case 'Borrower Employer Name':
                    $csvArray[$header] = $incomeInfo['employer1'] ?? '';
                    break;
                case 'Borrower Base Employment Income':
                    $csvArray[$header] = $incomeInfo['grossIncome1'];
                    break;
                case 'Borrower Net Income':
                    $csvArray[$header] = Strings::replaceCommaValues($borrowergrossemployeeincome)
                        + Strings::replaceCommaValues($netRental1)
                        + Strings::replaceCommaValues($otherHouseHold1)
                        + Strings::replaceCommaValues($netEarnedInterest1);
                    break;
                case 'Borrower This Loan':
                    $csvArray[$header] = $myFileInfo['lien1Payment'] ?? '';
                    break;
                case 'Borrower Total Expenses':
                    $csvArray[$header] = $borrowertotalexpenses ?? '';
                    break;
                case 'Co-Borrower Occupation':
                    $csvArray[$header] = $incomeInfo['occupation2'] ?? '';
                    break;
                case 'Co-Borrower Employment Type':
                    $csvArray[$header] = $incomeInfo['employedInfo2'] ?? '';
                    break;
                case 'Co-Borrower Employer Name':
                    $csvArray[$header] = $incomeInfo['employer2'] ?? '';
                    break;
                case 'Co-Borrower Base Employment Income':
                    $csvArray[$header] = $incomeInfo['grossIncome2'] ?? '';
                    break;
                case 'Co-Borrower Net Income':
                    $csvArray[$header] = $coborrowernetincome ?? '';
                    break;
                case 'Co-Borrower Total Expenses':
                    $csvArray[$header] = $coborrowertotalexpenses ?? '';
                    break;
                case 'Branch Name':
                    $csvArray[$header] = $branchInfo['LMRExecutive'] ?? '';
                    break;
                case 'Branch Company':
                    $csvArray[$header] = $branchInfo['company'] ?? '';
                    break;
                case 'Branch Phone':
                    $csvArray[$header] = Strings::formatPhoneNumber(trim($branchInfo['tollFree'] ?? ''));
                    break;
                case 'Branch Cell':
                    $csvArray[$header] = Strings::formatPhoneNumber(trim($branchInfo['cellNumber'] ?? ''));
                    break;
                case 'Branch Fax':
                    $csvArray[$header] = Strings::formatPhoneNumber(trim($branchInfo['fax'] ?? ''));
                    break;
                case 'Branch Email':
                    $csvArray[$header] = $branchInfo['executiveEmail'] ?? '';
                    break;
                case 'Employee Name':
                    $csvArray[$header] = $employeename;
                    break;
                case 'Employee Phone':
                    $csvArray[$header] = $employeephone;
                    break;
                case 'Employee Cell':
                    $csvArray[$header] = $employeecell;
                    break;
                case 'Employee Fax':
                    $csvArray[$header] = $employeefax;
                    break;
                case 'Employee Email':
                    $csvArray[$header] = $employeeemail;
                    break;
                case 'Agent First Name':
                    $csvArray[$header] = $agentInfo['brokerFName'] ?? '';
                    break;
                case 'Agent Last Name':
                    $csvArray[$header] = $agentInfo['brokerLName'] ?? '';
                    break;
                case 'Agent Company':
                    $csvArray[$header] = $agentInfo['company'] ?? '';
                    break;
                case 'Agent Phone':
                    $csvArray[$header] = Strings::formatPhoneNumber(trim($agentInfo['phoneNumber']));
                    break;
                case 'Agent Cell':
                    $csvArray[$header] = Strings::formatPhoneNumber(trim($agentInfo['cellNumber']));
                    break;
                case 'Agent Fax':
                    $csvArray[$header] = Strings::formatPhoneNumber(trim($agentInfo['fax']));
                    break;
                case 'Agent Email':
                    $csvArray[$header] = $agentInfo['email'] ?? '';
                    break;
                case 'LO First Name':
                    $csvArray[$header] = $loInfo['FName'] ?? '';
                    break;
                case 'LO Last Name':
                    $csvArray[$header] = $loInfo['LName'] ?? '';
                    break;
                case 'LO Company':
                    $csvArray[$header] = $loInfo['company'] ?? '';
                    break;
                case 'LO Phone':
                    $csvArray[$header] = Strings::formatPhoneNumber(trim($loInfo['phoneNumber'] ?? ''));
                    break;
                case 'LO Cell':
                    $csvArray[$header] = Strings::formatPhoneNumber(trim($loInfo['cellNumber'] ?? ''));
                    break;
                case 'LO Fax':
                    $csvArray[$header] = Strings::formatPhoneNumber(trim($loInfo['fax'] ?? ''));
                    break;
                case 'LO Email':
                    $csvArray[$header] = $loInfo['email'] ?? '';
                    break;
                case 'File Created Date':
                    $csvArray[$header] = $filecreateddate;
                    break;
                case 'Last Updated date':
                    if ($myFileInfo['lastUpdatedDate']) {
                        $csvArray[$header] = date("Y-m-d", strtotime($myFileInfo['lastUpdatedDate'])) ?? '';
                    } else {
                        $csvArray[$header] = '';
                    }
                    break;
                case 'Last Updated Date/Time':
                    $csvArray[$header] = $myFileInfo['lastUpdatedDate'] ?? '';
                    break;
                case 'Received Date':
                    $csvArray[$header] = Dates::formatDateWithRE($myFileInfo['receivedDate'], 'YMD', 'Y-m-d');
                    break;
                case 'Status':
                    $csvArray[$header] = exportClientFiles::$statusInfoArray[$primeStatusId]['primaryStatus'] ?? '';
                    break;
                case 'Sub Status':
                    $csvArray[$header] = self::getSubStatus($LMRId);
                    break;
                case 'Days In Current Status':
                    $csvArray[$header] = self::getDaysInCurrentStatus($LMRId) ?? '';
                    break;
                case 'Days In Previous Status':
                    // TODO: this doesn't seem to exist
                    $csvArray[$header] = '';
                    break;
                case 'Loan Number':
                    $csvArray[$header] = $myFileInfo['loanNumber'] ?? '';
                    break;
                case 'Closing Date':
                    $csvArray[$header] = Dates::formatDateWithRE(trim($QAInfo['closingDate'] ?? ''), 'YMD', 'm/d/Y');
                    break;
                case 'Date of First payment due':
                    $csvArray[$header] = Dates::formatDateWithRE(trim($myFileInfo['trialPaymentDate1'] ?? ''), 'YMD', 'm/d/Y');
                    break;
                case 'Type of Purchase':
                    $csvArray[$header] = $HMLOPropInfo['typeOfSale'] ?? '';
                    break;
                case 'Rehabbed value':
                    $csvArray[$header] = Currency::formatDollarAmountWithDecimal($listingRealtorInfo2['intAssRehabbedValue']);
                    break;
                case 'Recommended Offer':
                    $csvArray[$header] = Currency::formatDollarAmountWithDecimal($listingRealtorInfo2['intAssRecommendedOffer']);
                    break;
                case 'Maturity Date':
                    $csvArray[$header] = Dates::formatDateWithRE($HMLOPropInfo['maturityDate'], 'YMD', 'm/d/Y');
                    break;
                case 'Pay Off Date':
                    $csvArray[$header] = !Dates::IsEmpty($HMLOPropInfo['payOffDate'] ?? '') ? Dates::Datestamp($HMLOPropInfo['payOffDate']) : '';
                    break;
                case 'Lead Source':
                    $csvArray[$header] = $myFileInfo['leadSource'] ?? '';
                    break;
                case 'Referring Party':
                    $csvArray[$header] = $HMLOPropInfo['referringParty'] ?? '';
                    break;
                case 'Date Of Loan Sale Agreement':
                    $csvArray[$header] = Dates::formatDateWithRE($HMLOPropInfo['loanSaleDate'], 'YMD', 'm/d/Y');
                    break;
                case 'Funding Date':
                    $csvArray[$header] = Dates::formatDateWithRE($HMLOPropInfo['fundingDate'], 'YMD', 'm/d/Y');
                    break;
                case 'Servicing Status':
                    $csvArray[$header] = $glServicingSubStatus[$HMLOPropInfo['servicingSubStatus']] ?? '';
                    break;
                case 'Workflow':
                    // this is handled in self::getWorkflow with columns added for each step
                    break;
                case 'Will borrower personally guarantee this loan?':
                    $csvArray[$header] = exportClientFiles::$fileHMLOBackGroundInfoArray[$LMRId]['isBorPersonallyGuaranteeLoan'] ?? '';
                    break;
                case 'Sale date':
                    $csvArray[$header] = !Dates::IsEmpty($myFileInfo['salesDate'] ?? '') ? $myFileInfo['salesDate'] : '';
                    break;
                case 'Origination Points Rate':
                    $csvArray[$header] = Currency::formatDollarAmountWithDecimal(trim($fileHMLONewLoanInfo['originationPointsRate']));
                    break;
                case 'Origination Points Value':
                    $csvArray[$header] = $fileHMLONewLoanInfo['originationPointsValue'];
                    break;
                case 'Broker Points Rate':
                    $csvArray[$header] = Currency::formatDollarAmountWithDecimal(trim($fileHMLONewLoanInfo['brokerPointsRate']));
                    break;
                case 'Broker Points Value':
                    $csvArray[$header] = $fileHMLONewLoanInfo['brokerPointsValue'] ?? '';
                    break;
                case 'Closing Cost Financing fee':
                    $csvArray[$header] = $fileHMLONewLoanInfo['closingCostFinancingFee'] ?? '';
                    break;
                case 'Attorney fee':
                    $csvArray[$header] = $fileHMLONewLoanInfo['attorneyFee'] ?? '';
                    break;
                case 'Application Fee':
                    $csvArray[$header] = $fileHMLONewLoanInfo['applicationFee'] ?? '';
                    break;
                case 'Appraisal Fee':
                    $csvArray[$header] = $fileHMLONewLoanInfo['appraisalFee'] ?? '';
                    break;
                case 'Estimated Title Insurance Fees':
                    // TODO: this doesn't seem to exist
                    $csvArray[$header] = '';
                    break;
                case 'Processing Fees':
                    $csvArray[$header] = $fileHMLONewLoanInfo['processingFee'];
                    break;
                case 'Draws Set Up Fee':
                    $csvArray[$header] = $fileHMLONewLoanInfo['drawsSetUpFee'] ?? '';
                    break;
                case 'Draws Fee':
                    $csvArray[$header] = $fileHMLONewLoanInfo['drawsFee'] ?? '';
                    break;
                case 'Valuation - BPO':
                    $csvArray[$header] = $fileHMLONewLoanInfo['valuationBPOFee'] ?? '';
                    break;
                case 'Valuation - AVM':
                    $csvArray[$header] = $fileHMLONewLoanInfo['valuationAVMFee'] ?? '';
                    break;
                case 'Credit Report':
                    $csvArray[$header] = $fileHMLONewLoanInfo['creditReportFee'] ?? '';
                    break;
                case 'Background Check':
                    $csvArray[$header] = $fileHMLONewLoanInfo['backgroundCheckFee'] ?? '';
                    break;
                case 'Tax Service':
                    $csvArray[$header] = $fileHMLONewLoanInfo['taxServiceFee'] ?? '';
                    break;
                case 'Document Preparation':
                    $csvArray[$header] = $fileHMLONewLoanInfo['documentPreparationFee'] ?? '';
                    break;
                case 'Wire Fee':
                    $csvArray[$header] = $fileHMLONewLoanInfo['wireFee'] ?? '';
                    break;
                case 'Servicing Set Up Fee':
                    $csvArray[$header] = $fileHMLONewLoanInfo['servicingSetUpFee'] ?? '';
                    break;
                case 'Flood Certificate':
                    $csvArray[$header] = $fileHMLONewLoanInfo['floodCertificateFee'] ?? '';
                    break;
                case 'Flood Service':
                    $csvArray[$header] = $fileHMLONewLoanInfo['floodServiceFee'] ?? '';
                    break;
                case 'Inspection Fees':
                    $csvArray[$header] = $fileHMLONewLoanInfo['inspectionFees'] ?? '';
                    break;
                case 'Project Feasibility':
                    $csvArray[$header] = $fileHMLONewLoanInfo['projectFeasibility'] ?? '';
                    break;
                case 'Due Diligence':
                    $csvArray[$header] = $fileHMLONewLoanInfo['dueDiligence'] ?? '';
                    break;
                case 'Ucc/Lien Search':
                    $csvArray[$header] = $fileHMLONewLoanInfo['UccLienSearch'] ?? '';
                    break;
                case 'Lender Credit to Offset 3rd Party Fees':
                    $csvArray[$header] = $fileHMLONewLoanInfo['thirdPartyFees'] ?? '';
                    break;
                case 'Other':
                    $csvArray[$header] = $fileHMLONewLoanInfo['otherFee'] ?? '';
                    break;
                case 'Escrow Fees':
                    $csvArray[$header] = $fileHMLONewLoanInfo['escrowFees'] ?? '';
                    break;
                case 'Recording Fee':
                    $csvArray[$header] = $fileHMLONewLoanInfo['recordingFee'] ?? '';
                    break;
                case 'Underwriting Fees':
                    $csvArray[$header] = $fileHMLONewLoanInfo['underwritingFees'] ?? '';
                    break;
                case 'Property tax':
                    $csvArray[$header] = $fileHMLONewLoanInfo['propertyTax'] ?? '';
                    break;
                case 'Discount Fee':
                    $csvArray[$header] = $fileHMLONewLoanInfo['bufferAndMessengerFee'] ?? '';
                    break;
                case 'Travel Notary Fee':
                    $csvArray[$header] = $fileHMLONewLoanInfo['travelNotaryFee'] ?? '';
                    break;
                case 'Pre paid Interest':
                    $csvArray[$header] = $fileHMLONewLoanInfo['prePaidInterest'] ?? '';
                    break;
                case 'Real Estate Taxes':
                    $csvArray[$header] = $fileHMLONewLoanInfo['realEstateTaxes'] ?? '';
                    break;
                case 'Insurance Premium':
                    $csvArray[$header] = $fileHMLONewLoanInfo['insurancePremium'] ?? '';
                    break;
                case 'Pay Off Liens/Creditors':
                    $csvArray[$header] = $fileHMLONewLoanInfo['payOffLiensCreditors'] ?? '';
                    break;
                case 'Wire Transfer Fee to Title':
                    $csvArray[$header] = $fileHMLONewLoanInfo['wireTransferFeeToTitle'] ?? '';
                    break;
                case 'Wire Transfer Fee to Escrow':
                    $csvArray[$header] = $fileHMLONewLoanInfo['wireTransferFeeToEscrow'] ?? '';
                    break;
                case 'Past Due Property Taxes':
                    $csvArray[$header] = $fileHMLONewLoanInfo['pastDuePropertyTaxes'] ?? '';
                    break;
                case 'Tax impounds':
                    $csvArray[$header] = $fileHMLONewLoanInfo['taxImpoundsFee'] ?? '';
                    break;
                case 'Ins impounds':
                    $csvArray[$header] = $fileHMLONewLoanInfo['insImpoundsFee'] ?? '';
                    break;
                case 'Earnest Deposit':
                    $csvArray[$header] = Currency::formatDollarAmountWithDecimal(trim($fileHMLONewLoanInfo['earnestDeposit']));
                    break;
                case 'Current Lender Info':
                    $csvArray[$header] = $fileHMLONewLoanInfo['refinanceCurrentLender'] ?? '';
                    break;
                case 'Broker Processing Fee':
                    $csvArray[$header] = $fileHMLONewLoanInfo['brokerProcessingFee'] ?? '';
                    break;
                case 'Disclosure Sent Date':
                    $csvArray[$header] = $fileAdminInfo ? Dates::formatDateWithRE($fileAdminInfo->disclosureSentDate, 'YMD', 'm/d/Y') : '';
                    break;
                case 'Total Paid Payables':
                    $total = 0;
                    foreach ($commissionInfo ?? [] as $item) {
                        $total += Strings::replaceCommaValues($item['totalPaid']);
                    }
                    $csvArray[$header] = Currency::formatDollarAmountWithDecimal($total);
                    break;
                case 'Backoffice File Link':
                    $csvArray[$header] = CONST_SITE_URL . 'backoffice/LMRequest.php?eId=' . cypher::myEncryption($branchID) . '&lId=' . cypher::myEncryption($LMRId) . '&rId=' . cypher::myEncryption($LMRResponseId) . '&op=a72f9e967052513d';
                    break;
                case 'Full App for Borrower':
                    $csvArray[$header] = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRId) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('FA') . '&UType=' . cypher::myEncryption('Borrower');
                    break;
                case 'Quick App for Borrower':
                    $csvArray[$header] = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRId) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('QA') . '&UType=' . cypher::myEncryption('Borrower');
                    break;
                case 'Full App for Broker':
                    $csvArray[$header] = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&aRc=' . $agentReferralCode . '&fOpt=' . cypher::myEncryption('agent') . '&lid=' . cypher::myEncryption($LMRId) . '&ft=' . $fileModuleCode . '&opt=' . cypher::myEncryption('Email') . '&op=' . cypher::myEncryption('FA') . '&UType=' . cypher::myEncryption('Agent/Broker');
                    break;
                case 'Quick App for Broker':
                    $csvArray[$header] = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&aRc=' . $agentReferralCode . '&fOpt=' . cypher::myEncryption('agent') . '&lid=' . cypher::myEncryption($LMRId) . '&ft=' . $fileModuleCode . '&opt=' . cypher::myEncryption('Email') . '&op=' . cypher::myEncryption('QA') . '&UType=' . cypher::myEncryption('Agent/Broker');
                    break;
                case 'Upload Portal for Borrower':
                    $csvArray[$header] = CONST_SITE_URL . 'backoffice/uploadLMRDocs.php?lid=' . cypher::myEncryption($LMRId) . '&ft=' . $fileModuleCode . '&UType=' . cypher::myEncryption('Borrower') . '&UName=' . cypher::myEncryption($clientfirstname) . '&email=' . cypher::myEncryption($clientemail);
                    break;
                case 'Upload Portal for Broker':
                    $csvArray[$header] = CONST_SITE_URL . 'backoffice/uploadLMRDocs.php?lid=' . cypher::myEncryption($LMRId) . '&ft=' . $fileModuleCode . '&UType=' . cypher::myEncryption('Agent/Broker') . '&UName=' . cypher::myEncryption($agentfirstname) . '&email=' . cypher::myEncryption($agentemail);
                    break;
                case 'QA Borrower Info Redacted':
                    $csvArray[$header] = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRId) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('QA') . '&sl=' . cypher::myEncryption('shareLink') . '&bir=' . cypher::myEncryption('yes');
                    break;
                case 'QA Borrower Info Redacted and Offer Sub':
                    $csvArray[$header] = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRId) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('QA') . '&sl=' . cypher::myEncryption('shareLink') . '&bir=' . cypher::myEncryption('yes') . '&so=' . cypher::myEncryption('yes');
                    break;
                case 'QA Read Only':
                    $csvArray[$header] = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRId) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('QA') . '&sl=' . cypher::myEncryption('shareLink');
                    break;
                case 'QA Read Only and Offer Sub':
                    $csvArray[$header] = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRId) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('QA') . '&sl=' . cypher::myEncryption('shareLink') . '&so=' . cypher::myEncryption('yes');
                    break;
                case 'QA Read Only and Upload Portal':
                    $csvArray[$header] = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRId) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('QA') . '&sl=' . cypher::myEncryption('shareLink') . '&aud=' . cypher::myEncryption('yes');
                    break;
                case 'QA Read Only Upload Portal and Offer Sub':
                    $csvArray[$header] = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRId) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('QA') . '&sl=' . cypher::myEncryption('shareLink') . '&aud=' . cypher::myEncryption('yes') . '&so=' . cypher::myEncryption('yes');
                    break;
                case 'FA Borrower Info Redacted':
                    $csvArray[$header] = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRId) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink') . '&bir=' . cypher::myEncryption('yes');
                    break;
                case 'FA Borrower Info Redacted and Offer Sub':
                    $csvArray[$header] = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRId) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink') . '&bir=' . cypher::myEncryption('yes') . '&so=' . cypher::myEncryption('yes');
                    break;
                case 'FA Read Only':
                    $csvArray[$header] = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRId) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink');
                    break;
                case 'FA Read Only and Offer Sub':
                    $csvArray[$header] = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRId) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink') . '&so=' . cypher::myEncryption('yes');
                    break;
                case 'FA Read Only and Upload Portal':
                    $csvArray[$header] = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRId) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink') . '&aud=' . cypher::myEncryption('yes');
                    break;
                case 'FA Read Only Upload Portal and Offer Sub':
                    $csvArray[$header] = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRId) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink') . '&aud=' . cypher::myEncryption('yes') . '&so=' . cypher::myEncryption('yes');
                    break;
                case 'Client First Name':
                    $csvArray[$header] = $clientfirstname;
                    break;
                case 'Client Last Name':
                    $csvArray[$header] = $clientlastname;
                    break;
                case 'Client Phone Number':
                    $csvArray[$header] = $clientphonenumber;
                    break;
                case 'Client Alt Phone Number':
                    $csvArray[$header] = $clientaltphonenumber;
                    break;
                case 'Client Cell Phone Number':
                    $csvArray[$header] = $clientcellphonenumber;
                    break;
                case 'Client Work Phone Number':
                    $csvArray[$header] = $clientworkphonenumber;
                    break;
                case 'Client Fax Number':
                    $csvArray[$header] = $clientfaxnumber;
                    break;
                case 'Client Email':
                    $csvArray[$header] = $clientemail;
                    break;
                case 'Client Time Zone':
                    $csvArray[$header] = $clienttimezone;
                    break;
                case 'Pre qualifier Name':
                    $csvArray[$header] = $prequalifiername;
                    break;
                case 'Client DOB':
                    $csvArray[$header] = $clientdob;
                    break;
                case 'Client SMS Number':
                    $csvArray[$header] = $clientsmsnumber;
                    break;
                case 'Co-borrower Alt Phone Number':
                    $csvArray[$header] = $coborroweraltphonenumber;
                    break;
                case 'Co-borrower Work Number':
                    $csvArray[$header] = $coborrowerworknumber;
                    break;
                case 'Co-borrower Time Zone':
                    $csvArray[$header] = $coborrowertimezone;
                    break;
                case 'Co-borrower SMS Number':
                    $csvArray[$header] = $coborrowersmsnumber;
                    break;
                case 'Occupancy':
                    $csvArray[$header] = $occupancy;
                    break;
                case 'No of bed room':
                    $csvArray[$header] = $noofbedroom;
                    break;
                case 'No of baths':
                    $csvArray[$header] = $noofbaths;
                    break;
                case 'Sq Ft':
                    $csvArray[$header] = $sqft;
                    break;
                case 'Home Value':
                    $csvArray[$header] = $myFileInfo['homeValue'] ?? '';
                    break;
                case 'Year purchased':
                    $csvArray[$header] = $yearpurchased;
                    break;
                case 'Cost Basis':
                    $csvArray[$header] = $costbasis;
                    break;
                case 'BPO1':
                    $csvArray[$header] = $bpo1;
                    break;
                case 'BPO2':
                    $csvArray[$header] = $bpo2;
                    break;
                case 'BPO3':
                    $csvArray[$header] = $bpo3;
                    break;
                case 'Appraiser 1':
                    $csvArray[$header] = $appraiser1;
                    break;
                case 'Appraiser 2':
                    $csvArray[$header] = $appraiser2;
                    break;
                case 'Original Lender':
                    $csvArray[$header] = $originallender;
                    break;
                case 'Current Lender':
                    $csvArray[$header] = $currentlender;
                    break;
                case 'Mortgage Investor 1':
                    $csvArray[$header] = $glMortgageInvestorOwnerArray[$myFileInfo['mortgageInvestor1']] ?? '';
                    break;
                case 'Loan Type lien 1':
                    $csvArray[$header] = $loantypelien1;
                    break;
                case 'Current Rate':
                    $csvArray[$header] = $currentrate;
                    break;
                case 'Lender Revenue':
                    $csvArray[$header] = $lenderrevenue;
                    break;
                case 'Unpaid Balance':
                    $csvArray[$header] = $unpaidbalance;
                    break;
                case 'Original Loan Amount':
                    $csvArray[$header] = $originalloanamount;
                    break;
                case 'Amount Past Due':
                    $csvArray[$header] = $amountpastdue;
                    break;
                case 'Months Past Due':
                    $csvArray[$header] = $monthspastdue;
                    break;
                case 'Mtg Type':
                    $csvArray[$header] = $mtgtype;
                    break;
                case 'Mtg PI Payment':
                    $csvArray[$header] = $mtgpipayment;
                    break;
                case 'Taxes':
                    $csvArray[$header] = $taxes;
                    break;
                case 'Insurance':
                    $csvArray[$header] = $insurance;
                    break;
                case 'Flood Insurance':
                    $csvArray[$header] = $floodinsurance;
                    break;
                case 'Mtg Insurance':
                    $csvArray[$header] = $mtginsurance;
                    break;
                case 'HOA Fees':
                    $csvArray[$header] = $incomeInfo['HOAFees1'] ?? '';
                    break;
                case 'Total PITIA':
                    $csvArray[$header] = $totalpitia;
                    break;
                case 'Notice Of Acceleration':
                    $csvArray[$header] = $noticeofacceleration;
                    break;
                case 'Loan Origination Date':
                    $csvArray[$header] = exportClientFiles::$loanOriginationInfo[$LMRId]['noteDate'] ?? '';
                    break;
                case 'Transfer Of Servicing Date':
                    $csvArray[$header] = $file2Info[$LMRId]['transferOfServicingDate'] ?? '';
                    break;
                case 'Original Lender 2':
                    $csvArray[$header] = $myFileInfo['originalLender2'] ?? '';
                    break;
                case 'Current Lender 2':
                    $csvArray[$header] = $myFileInfo['servicer2'] ?? '';
                    break;
                case 'Mortgage Investor 2':
                    $csvArray[$header] = $glMortgageInvestorOwnerArray[$myFileInfo['mortgageInvestor2']] ?? '';
                    break;
                case 'Loan Type lien 2':
                    $csvArray[$header] = $loantypelien2;
                    break;
                case 'Rate':
                    $csvArray[$header] = $rate;
                    break;
                case 'Unpaid Balance lien 2':
                    $csvArray[$header] = $unpaidbalancelien2;
                    break;
                case 'Original Loan Amount lien 2':
                    $csvArray[$header] = $originalloanamountlien2;
                    break;
                case 'Payment Amount':
                    $csvArray[$header] = $paymentamount;
                    break;
                case 'Loan Number lien 2':
                    $csvArray[$header] = $loannumberlien2;
                    break;
                case 'Amount Past Due lien 2':
                    $csvArray[$header] = $amountpastduelien2;
                    break;
                case 'Months Past Due lien 2':
                    $csvArray[$header] = $monthspastduelien2;
                    break;
                case 'Current Lender Servicer ':
                    $csvArray[$header] = $currentlenderservicer;
                    break;
                case 'Mtg Notes':
                    $csvArray[$header] = $mtgnotes;
                    break;
                case 'Borrower Gross Social Security Income':
                    $csvArray[$header] = $borrowergrosssocialsecurityincome;
                    break;
                case 'Borrower Gross Employee  Income':
                    $csvArray[$header] = $borrowergrossemployeeincome;
                    break;
                case 'Borrower Net employee Income':
                    $csvArray[$header] = $borrowernetemployeeincome;
                    break;
                case 'Borrower Total Gross Income':
                    $csvArray[$header] = $borrowertotalgrossincome;
                    break;
                case 'Borrower Total Net Income':
                    $csvArray[$header] = $borrowertotalnetincome;
                    break;
                case 'Credit Cards':
                    $csvArray[$header] = $creditcards;
                    break;
                case 'Credit Card Balance':
                    $csvArray[$header] = $creditcardbalance;
                    break;
                case 'Borrower Student Loan Tuitions':
                    $csvArray[$header] = $borrowerstudentloantuitions;
                    break;
                case 'Borrower Total Student Loan Balance':
                    $csvArray[$header] = $borrowertotalstudentloanbalance;
                    break;
                case 'Co-borrower Employment Type':
                    $csvArray[$header] = $coborroweremploymenttype;
                    break;
                case 'Co-borrower Gross Social Security Income':
                    $csvArray[$header] = $coborrowergrosssocialsecurityincome;
                    break;
                case 'Co-borrower Gross Employee  Income':
                    $csvArray[$header] = $coborrowergrossemployeeincome;
                    break;
                case 'Co-borrower Net employee Income':
                    $csvArray[$header] = $coborrowernetemployeeincome;
                    break;
                case 'Co-borrower Total Gross Income':
                    $csvArray[$header] = $coborrowertotalgrossincome;
                    break;
                case 'Co-borrower Total Net Income':
                    $csvArray[$header] = $coborrowertotalnetincome;
                    break;
                case 'Total Gross Income':
                    $csvArray[$header] = $totalgrossincome;
                    break;
                case 'Total Net Income':
                    $csvArray[$header] = $totalnetincome;
                    break;
                case 'Disposable Income':
                    $csvArray[$header] = $disposableincome;
                    break;
                case 'Current DTI':
                    $csvArray[$header] = $currentdti;
                    break;
                case 'Co-borrower Student Loan Tuitions':
                    $csvArray[$header] = $coborrowerstudentloantuitions;
                    break;
                case 'Co-borrower Total Student Loan Balance':
                    $csvArray[$header] = $coborrowertotalstudentloanbalance;
                    break;
                case 'Trustee Name':
                    $csvArray[$header] = $trusteename;
                    break;
                case 'Trustee Company':
                    $csvArray[$header] = $trusteecompany;
                    break;
                case 'Trustee Phone':
                    $csvArray[$header] = $trusteephone;
                    break;
                case 'Trustee Cell':
                    $csvArray[$header] = $trusteecell;
                    break;
                case 'Trustee Fax':
                    $csvArray[$header] = $trusteefax;
                    break;
                case 'Trustee Email':
                    $csvArray[$header] = $trusteeemail;
                    break;
                case 'Sale Date':
                    $csvArray[$header] = $saledate;
                    break;
                case 'Priority Status':
                    $csvArray[$header] = $prioritystatus;
                    break;
                case 'No of Pending Tasks':
                    $csvArray[$header] = $noofpendingtasks;
                    break;
                case 'File Number':
                    $csvArray[$header] = $filenumber;
                    break;
                case 'Notes':
                    $csvArray[$header] = $notes;
                    break;
                case 'Services':
                    $csvArray[$header] = $services;
                    break;
                case 'Borrower Call Back':
                    $csvArray[$header] = $borrowercallback;
                    break;
                case 'HAFA DATE':
                    $csvArray[$header] = $hafadate;
                    break;
                case 'Welcome Call Date':
                    $csvArray[$header] = $welcomecalldate;
                    break;
                case 'Bank Call Completed':
                    $csvArray[$header] = $bankcallcompleted;
                    break;
                case 'Lender Call Back':
                    $csvArray[$header] = $lendercallback;
                    break;
                case 'File Received Date':
                    $csvArray[$header] = $filereceiveddate;
                    break;
                case 'Summon Date':
                    $csvArray[$header] = $summondate;
                    break;
                case 'Closed Date':
                    $csvArray[$header] = $closeddate;
                    break;
                case 'Date Mod Received':
                    $csvArray[$header] = $datemodreceived;
                    break;
                case 'Mod 1st Payment':
                    $csvArray[$header] = $mod1stpayment;
                    break;
                case 'Lender Submission Date':
                    $csvArray[$header] = $lendersubmissiondate;
                    break;
                case 'Resolution Type':
                    $csvArray[$header] = $resolutiontype;
                    break;
                case 'Closed Disposition':
                    $csvArray[$header] = $closeddisposition;
                    break;

                case 'Checklist Status':
                    // TODO: not defined
                    $csvArray[$header] = '';
                    break;
                case 'Checklist Status Count':
                    // TODO: not defined
                    $csvArray[$header] = '';
                    break;
                case 'Appeal Date':
                    $csvArray[$header] = $myFileInfo['appealDate'] ?? '';
                    break;
                case 'Attorney Review Date':
                    $csvArray[$header] = $myFileInfo['attorneyReviewDate'] ?? '';
                    break;
                case 'Date of 1st Trial Payment':
                    $csvArray[$header] = $myFileInfo['trialPaymentDate1'] ?? '';
                    break;
                case 'Date of 2nd Trial Payment':
                    $csvArray[$header] = $myFileInfo['trialPaymentDate2'] ?? '';
                    break;
                case 'Date of 3rd Trial Payment':
                    $csvArray[$header] = $myFileInfo['trialPaymentDate3'] ?? '';
                    break;
                case 'Amount of 1st Mod Payment':
                    $csvArray[$header] = $myFileInfo['firstModPaymentAmt'] ?? '';
                    break;
                case 'Retainer Date':
                    $csvArray[$header] = $myFileInfo['retainerDate'] ?? '';
                    break;
                case 'Escalation Date':
                    $csvArray[$header] = $myFileInfo['escalationDate'] ?? '';
                    break;
                case 'Denial Date':
                    $csvArray[$header] = $myFileInfo['denialDate'] ?? '';
                    break;
                case 'Listing Realtor':
                    $csvArray[$header] = $shortSaleInfo['realtor'] ?? '';
                    break;
                case 'Listing Price':
                    $csvArray[$header] = $shortSaleInfo['listingPrice'] ?? '';
                    break;
                case 'Listing Date':
                    $csvArray[$header] = $shortSaleInfo['listingDate'] ?? '';
                    break;
                case 'Listing Address':
                    $csvArray[$header] = $shortSaleInfo['realtorAddress'] ?? '';
                    break;
                case 'Listing Phone':
                    $csvArray[$header] = Strings::formatPhoneNumber($shortSaleInfo['realtorPhoneNumber']);
                    break;
                case 'Listing Cell':
                    $csvArray[$header] = Strings::formatPhoneNumber($shortSaleInfo['sales1CellNo']);
                    break;
                case 'Listing Agency':
                    $csvArray[$header] = $shortSaleInfo['agency'] ?? '';
                    break;
                case 'Listing Fax':
                    $csvArray[$header] = Strings::formatPhoneNumber($shortSaleInfo['sales1Fax']);
                    break;
                case 'Listing Email':
                    $csvArray[$header] = $shortSaleInfo['realtorEmail'] ?? '';
                    break;
                case 'Title Name':
                    $csvArray[$header] = $propertyInfo['titleName'] ?? '';
                    break;
                case 'Title Ordered':
                    $csvArray[$header] = $propertyInfo['titleOrderedOn'] ?? '';
                    break;
                case 'Title Updated':
                    $csvArray[$header] = $propertyInfo['titleUpdatedOn'] ?? '';
                    break;
                case 'Title Representative':
                    $csvArray[$header] = $shortSaleInfo['contact'] ?? '';
                    break;
                case 'Title Fax':
                    $csvArray[$header] = Strings::formatPhoneNumber($shortSaleInfo['sales2Fax']);
                    break;
                case 'MLS Number':
                    // TODO: where is this coming from
                    $csvArray[$header] = '';
                    break;
                case 'Listing History Date':
                    // TODO: where is this coming from
                    $csvArray[$header] = '';
                    break;
                case 'Listing History Price':
                    // TODO: where is this coming from
                    $csvArray[$header] = '';
                    break;
                case 'Listing History Notes':
                    // TODO: where is this coming from
                    $csvArray[$header] = '';
                    break;
                case 'Buyer Deal 1':
                    $csvArray[$header] = $shortSaleInfo['buyer1Deal'] ?? '';
                    break;
                case 'First Buyer Name':
                    $csvArray[$header] = $shortSaleInfo['buyerName1'] ?? '';
                    break;
                case 'First Co Buyer Name':
                    $csvArray[$header] = $shortSaleInfo['coBuyerName1'] ?? '';
                    break;
                case 'First Buyer Phone':
                    $csvArray[$header] = Strings::formatPhoneNumber($shortSaleInfo['firstBuyerPhone'] ?? '');
                    break;
                case 'First Buyer Email':
                    $csvArray[$header] = $shortSaleInfo['firstBuyerEmail'] ?? '';
                    break;
                case 'Offer 1':
                    $csvArray[$header] = $shortSaleInfo['offer1'] ?? '';
                    break;
                case 'Sq Ft 1':
                    $csvArray[$header] = $shortSaleInfo['sqft1'] ?? '';
                    break;
                case 'Contract Date 1':
                    $csvArray[$header] = $shortSaleInfo['contractDate1'] ?? '';
                    break;
                case 'Closing Date 1':
                    $csvArray[$header] = $shortSaleInfo['closingDate1'] ?? '';
                    break;
                case 'Buyer Agent Name 1':
                    $csvArray[$header] = $shortSaleInfo['buyer1AgentName'] ?? '';
                    break;
                case 'First Buyer Agency':
                    $csvArray[$header] = $shortSaleInfo['buyer1AgencyName'] ?? '';
                    break;
                case 'Buyer Phone 1':
                    $csvArray[$header] = Strings::formatPhoneNumber($shortSaleInfo['buyer1Phone']) ?? '';
                    break;
                case 'Buyer Cell 1':
                    $csvArray[$header] = Strings::formatPhoneNumber($shortSaleInfo['buyer1Cell']) ?? '';
                    break;
                case 'Buyer Fax 1':
                    $csvArray[$header] = Strings::formatPhoneNumber($shortSaleInfo['buyer1Fax']) ?? '';
                    break;
                case 'Buyer Email 1':
                    $csvArray[$header] = $shortSaleInfo['buyer1Email'] ?? '';
                    break;
                case 'First Buyers Loan Officer Name':
                    $csvArray[$header] = $shortSaleInfo['buyer1LOName'] ?? '';
                    break;
                case 'First Buyers Loan Officer Company':
                    $csvArray[$header] = $shortSaleInfo['buyer1LOCompany'] ?? '';
                    break;
                case 'First Buyers LO Phone':
                    $csvArray[$header] = Strings::formatPhoneNumber($shortSaleInfo['buyer1LOPhone']) ?? '';
                    break;
                case 'First Buyers LO Cell':
                    $csvArray[$header] = Strings::formatPhoneNumber($shortSaleInfo['buyer1LOCell']) ?? '';
                    break;
                case 'First Buyers LO Fax':
                    $csvArray[$header] = Strings::formatPhoneNumber($shortSaleInfo['buyer1LOFax']) ?? '';
                    break;
                case 'First Buyers LO Email':
                    $csvArray[$header] = $shortSaleInfo['buyer1LOEmail'] ?? '';
                    break;
                case 'Relationship to Seller 1':
                    $csvArray[$header] = $shortSaleInfo['buyer1RelToSeller'] ?? '';
                    break;
                case 'Offer Notes 1':
                    $csvArray[$header] = $shortSaleInfo['buyer1Notes'] ?? '';
                    break;
                case 'Buyer Deal 2':
                    $csvArray[$header] = $shortSaleInfo['buyer2Deal'] ?? '';
                    break;
                case 'Second Buyer Name':
                    $csvArray[$header] = $shortSaleInfo['buyerName2'] ?? '';
                    break;
                case 'Second Co Buyer Name':
                    $csvArray[$header] = $shortSaleInfo['coBuyerName2'] ?? '';
                    break;
                case 'Second Buyer Phone':
                    $csvArray[$header] = Strings::formatPhoneNumber($shortSaleInfo['secondBuyerPhone'] ?? '');
                    break;
                case 'Second Buyer Email':
                    $csvArray[$header] = $shortSaleInfo['secondBuyerEmail'] ?? '';
                    break;
                case 'Offer 2':
                    $csvArray[$header] = $shortSaleInfo['offer2'] ?? '';
                    break;
                case 'Sq Ft 2':
                    $csvArray[$header] = $shortSaleInfo['sqft2'] ?? '';
                    break;
                case 'Contract Date 2':
                    $csvArray[$header] = $shortSaleInfo['contractDate2'] ?? '';
                    break;
                case 'Closing Date 2':
                    $csvArray[$header] = $shortSaleInfo['closingDate2'] ?? '';
                    break;
                case 'Buyer Agent Name 2':
                    $csvArray[$header] = $shortSaleInfo['buyer2AgentName'] ?? '';
                    break;
                case 'Second Buyer Agency':
                    $csvArray[$header] = $shortSaleInfo['buyer2AgencyName'] ?? '';
                    break;
                case 'Buyer Phone 2':
                    $csvArray[$header] = Strings::formatPhoneNumber($shortSaleInfo['buyer2Phone'] ?? '');
                    break;
                case 'Buyer Cell 2':
                    $csvArray[$header] = Strings::formatPhoneNumber($shortSaleInfo['buyer2Cell'] ?? '');
                    break;
                case 'Buyer Fax 2':
                    $csvArray[$header] = Strings::formatPhoneNumber($shortSaleInfo['buyer2Fax'] ?? '');
                    break;
                case 'Buyer Email 2':
                    $csvArray[$header] = $shortSaleInfo['buyer2Email'] ?? '';
                    break;
                case 'Second Buyers Loan Officer Name':
                    $csvArray[$header] = $shortSaleInfo['buyer2LOName'] ?? '';
                    break;
                case 'Second Buyers Loan Officer Company':
                    $csvArray[$header] = $shortSaleInfo['buyer2LOCompany'] ?? '';
                    break;
                case 'Second Buyers LO Phone':
                    $csvArray[$header] = Strings::formatPhoneNumber($shortSaleInfo['buyer2LOPhone'] ?? '');
                    break;
                case 'Second Buyers LO Cell':
                    $csvArray[$header] = Strings::formatPhoneNumber($shortSaleInfo['buyer2LOCell'] ?? '');
                    break;
                case 'Second Buyers LO Fax':
                    $csvArray[$header] = Strings::formatPhoneNumber($shortSaleInfo['buyer2LOFax'] ?? '');
                    break;
                case 'Second Buyers LO Email':
                    $csvArray[$header] = $shortSaleInfo['buyer2LOEmail'] ?? '';
                    break;
                case 'Relationship to Seller 2':
                    $csvArray[$header] = $shortSaleInfo['buyer2RelToSeller'] ?? '';
                    break;
                case 'Offer Notes 2':
                    $csvArray[$header] = $shortSaleInfo['buyer2Notes'] ?? '';
                    break;
                case 'Buyer Deal 3':
                    $csvArray[$header] = $shortSaleInfo['buyer3Deal'] ?? '';
                    break;
                case 'Third Buyer Name':
                    $csvArray[$header] = $shortSaleInfo['buyerName3'] ?? '';
                    break;
                case 'Third Co Buyer Name':
                    $csvArray[$header] = $shortSaleInfo['coBuyerName3'] ?? '';
                    break;
                case 'Third Buyer Phone':
                    $csvArray[$header] = Strings::formatPhoneNumber($shortSaleInfo['thirdBuyerPhone'] ?? '');
                    break;
                case 'Third Buyer Email':
                    $csvArray[$header] = $shortSaleInfo['thirdBuyerEmail'] ?? '';
                    break;
                case 'Offer 3':
                    $csvArray[$header] = $shortSaleInfo['offer3'] ?? '';
                    break;
                case 'Sq Ft 3':
                    $csvArray[$header] = $shortSaleInfo['sqft3'] ?? '';
                    break;
                case 'Contract Date 3':
                    $csvArray[$header] = $shortSaleInfo['contractDate3'] ?? '';
                    break;
                case 'Closing Date 3':
                    $csvArray[$header] = $shortSaleInfo['closingDate3'] ?? '';
                    break;
                case 'Buyer Agent Name 3':
                    $csvArray[$header] = $shortSaleInfo['buyer3AgentName'] ?? '';
                    break;
                case 'Third Buyer Agency':
                    $csvArray[$header] = $shortSaleInfo['buyer3AgencyName'] ?? '';
                    break;
                case 'Buyer Phone 3':
                    $csvArray[$header] = Strings::formatPhoneNumber($shortSaleInfo['buyer3Phone'] ?? '');
                    break;
                case 'Buyer Cell 3':
                    $csvArray[$header] = Strings::formatPhoneNumber($shortSaleInfo['buyer3Cell'] ?? '');
                    break;
                case 'Buyer Fax 3':
                    $csvArray[$header] = Strings::formatPhoneNumber($shortSaleInfo['buyer3Fax'] ?? '');
                    break;
                case 'Buyer Email 3':
                    $csvArray[$header] = $shortSaleInfo['buyer3Email'] ?? '';
                    break;
                case 'Third Buyers Loan Officer Name':
                    $csvArray[$header] = $shortSaleInfo['buyer3LOName'] ?? '';
                    break;
                case 'Third Buyers Loan Officer Company':
                    $csvArray[$header] = $shortSaleInfo['buyer3LOCompany'] ?? '';
                    break;
                case 'Third Buyers LO Phone':
                    $csvArray[$header] = Strings::formatPhoneNumber($shortSaleInfo['buyer3LOPhone'] ?? '');
                    break;
                case 'Third Buyers LO Cell':
                    $csvArray[$header] = Strings::formatPhoneNumber($shortSaleInfo['buyer3LOCell'] ?? '');
                    break;
                case 'Third Buyers LO Fax':
                    $csvArray[$header] = Strings::formatPhoneNumber($shortSaleInfo['buyer3LOFax'] ?? '');
                    break;
                case 'Third Buyers LO Email':
                    $csvArray[$header] = $shortSaleInfo['buyer3LOEmail'] ?? '';
                    break;
                case 'Relationship to Seller 3':
                    $csvArray[$header] = $shortSaleInfo['buyer3RelToSeller'] ?? '';
                    break;
                case 'Offer Notes 3':
                    $csvArray[$header] = $shortSaleInfo['buyer3Notes'] ?? '';
                    break;
                case 'Estimated Value':
                    $csvArray[$header] = $estimatedValue;
                    break;
                case 'High Price':
                    $csvArray[$header] = $highPrice;
                    break;
                case 'Suggested List Price':
                    $csvArray[$header] = $suggestedListPrice;
                    break;
                case 'Quick Resale Price':
                    $csvArray[$header] = $quickResalePrice;
                    break;
                case 'Sale Price':
                    $csvArray[$header] = $salePrice;
                    break;
                case 'Months on the MLS':
                    $csvArray[$header] = $monthsontheMLS;
                    break;
                case 'Average Listing Price':
                    $csvArray[$header] = $averageListingPrice;
                    break;
                case 'CMA Description':
                    $csvArray[$header] = $cmaDescription;
                    break;
                case 'Max LTV%':
                    $csvArray[$header] = Currency::formatDollarAmountWithDecimal(exportClientFiles::$loanSetupInfoArray[$LMRId]['maxLTVPercent'] ?? 0);
                    break;
                case 'Minimum Maintained DSCR Ratio':
                    $csvArray[$header] = Currency::formatDollarAmountWithDecimal(exportClientFiles::$loanSetupInfoArray[$LMRId]['minDSCRRatio'] ?? 0);
                    break;
                case 'Minimum Account Balance with Lender':
                    $csvArray[$header] = Currency::formatDollarAmountWithDecimal(exportClientFiles::$loanSetupInfoArray[$LMRId]['minActBal'] ?? 0);
                    break;
                case 'Floor Rate':
                    $csvArray[$header] = Currency::formatDollarAmountWithDecimal(exportClientFiles::$loanSetupInfoArray[$LMRId]['floorRate'] ?? 0);
                    break;
                case 'Prepayment Penalty Lockout Period in Months':
                    $csvArray[$header] = exportClientFiles::$loanSetupInfoArray[$LMRId]['prepayLockOut'];
                    break;
                case 'Is this loan an adjustable or a fixed rate mortgage?':
                    $csvArray[$header] = exportClientFiles::$loanSetupInfoArray[$LMRId]['isAdjustable'];
                    break;

                case 'Will there be a Pre-Stabilized and Post-Stabilized Period for this Loan?':
                    $csvArray[$header] = exportClientFiles::$loanSetupInfoArray[$LMRId]['isPreStabilized'];
                    break;
                case 'Initial Term in Years':
                    $csvArray[$header] = $initialLoanTerms->TermYears;
                    break;
                case 'Initial Interest Rate Margin':
                    $csvArray[$header] = Currency::formatDollarAmountWithDecimalZerosLimit($initialLoanTerms->RateMargin, 8);
                    break;
                case 'Initial Interest Rate Index':
                    $csvArray[$header] = $initialLoanTerms->RateIndex;
                    break;
                /*            case 'Initial Interest Rate Floor':
                                //$csvArray[$header] = Currency::formatDollarAmountWithDecimalZerosLimit($initialLoanTerms->RateFloor,8);
                                break;*/
                case 'Initial Amortization':
                    $csvArray[$header] = $initialLoanTerms->RateAmortization;
                    break;
                case 'Initial Term Adjustment Period':
                    $csvArray[$header] = glInitialTerms::$glInitialTerms[$initialLoanTerms->TermAdjust];
                    break;
                case 'Indicated Rate As Of':
                    $csvArray[$header] = Dates::formatDateWithRE(exportClientFiles::$loanSetupInfoArray[$LMRId]['indicatedRateDate'], 'YMD', 'm/d/Y');
                    break;
                case 'Indicated Rate%':
                    $csvArray[$header] = Currency::formatDollarAmountWithDecimal(exportClientFiles::$loanSetupInfoArray[$LMRId]['indicatedRatePercent']);
                    break;

                case 'Post Stabilized Rate Margin':
                    $csvArray[$header] = Currency::formatDollarAmountWithDecimal(exportClientFiles::$loanSetupInfoArray[$LMRId]['stabilizedrateMargin']);
                    break;
                case 'Post Stabilized Rate Index':
                    $csvArray[$header] = trim(exportClientFiles::$loanSetupInfoArray[$LMRId]['stabilizedRateIndex'], ',');
                    break;
                case 'Post Stabilized Rate Floor':
                    $csvArray[$header] = Currency::formatDollarAmountWithDecimal(exportClientFiles::$loanSetupInfoArray[$LMRId]['stabilizedRateFloor']);
                    break;
                case 'Post Stabilized Amoritization':
                    $csvArray[$header] = exportClientFiles::$loanSetupInfoArray[$LMRId]['stabilizedAmor'];
                    break;

                case 'Will there be a 2nd Term to this loan?':
                    $csvArray[$header] = exportClientFiles::$loanSetupInfoArray[$LMRId]['isSecondTerm'];
                    break;
                case '2nd Term in Years':
                    $csvArray[$header] = $secondaryLoanTerms->TermYears;
                    break;
                case '2nd Rate Margin':
                    $csvArray[$header] = Currency::formatDollarAmountWithDecimalZerosLimit($secondaryLoanTerms->RateMargin, 8);
                    break;
                case '2nd Rate Index':
                    $csvArray[$header] = $secondaryLoanTerms->RateIndex;
                    break;
                /*          case '2nd Rate Floor':
                              // $csvArray[$header] = Currency::formatDollarAmountWithDecimalZerosLimit($secondaryLoanTerms->RateFloor,8);
                              break;*/
                case '2nd Rate Amortization':
                    $csvArray[$header] = $secondaryLoanTerms->RateAmortization;
                    break;
                case '2nd Term Adjustment Period':
                    $csvArray[$header] = glInitialTerms::$glInitialTerms[$secondaryLoanTerms->TermAdjust];
                    break;
                case 'Name and Version of Credit Scoring Model':
                    $csvArray[$header] = $borrowerCreditScoringModelOfApplicant;
                    break;
                case 'Name and Version of Credit Scoring Model: Conditional Free Form Text Field':
                    $csvArray[$header] = $QAInfo['borrowerCreditScoringModelConditionalFreeOfApplicant'] ?? '';
                    break;
                case 'Was the ethnicity of the Borrower collected on the basis of visual observation or surname':
                    $csvArray[$header] = $QAInfo['bFiEthnicity'] ?? '';
                    break;
                case 'Was the sex of the Borrower collected on the basis of visual observation or surname':
                    $csvArray[$header] = $QAInfo['bFiSex'] ?? '';
                    break;
                case 'Was the race of the Borrower collected on the basis of visual observation or surname':
                    $csvArray[$header] = $QAInfo['bFiRace'] ?? '';
                    break;
                case 'The Demographic Information was provided through':
                    $csvArray[$header] = $bDemoInfo;
                    break;
                case 'Co-Borrower Name and Version of Credit Scoring Model':
                    $csvArray[$header] = $coBorrowerCreditScoringModelOfApplicant;
                    break;
                case 'Co-Borrower Name and Version of Credit Scoring Model: Conditional Free Form Text Field':
                    $csvArray[$header] = $QAInfo['coBorrowerCreditScoringModelConditionalFreeOfApplicant'] ?? '';
                    break;
                case 'Was the ethnicity of the Co-Borrower collected on the basis of visual observation or surname':
                    $csvArray[$header] = $QAInfo['CBFiEthnicity'] ?? '';
                    break;
                case 'Was the sex of the Co-Borrower collected on the basis of visual observation or surname':
                    $csvArray[$header] = $QAInfo['CBFiGender'] ?? '';
                    break;
                case 'Was the race of the Co-Borrower collected on the basis of visual observation or surname':
                    $csvArray[$header] = $QAInfo['CBFiRace'] ?? '';
                    break;
                case 'Co-Borrower Demographic Information was provided through':
                    $csvArray[$header] = $CBDemoInfo;
                    break;
                case 'Legal Entity Identifier (LEI)':
                    $csvArray[$header] = $QAInfo['legalEntityIdentifier'] ?? '';
                    break;
                case 'Introductory Rate Period':
                    $csvArray[$header] = $QAInfo['introductoryRatePeriod'] ?? '';
                    break;
                case 'Universal Loan Identifier (ULI)':
                    $csvArray[$header] = $QAInfo['universalLoanIdentifier'] ?? '';
                    break;
                case 'Interest-Only Payments':
                    $csvArray[$header] = $QAInfo['interestOnlyPayment'] ?? '';
                    break;
                case 'Action Taken':
                    $csvArray[$header] = $actionTaken;
                    break;
                case 'Type Of Purchaser':
                    $csvArray[$header] = $typeOfPurchaser;
                    break;
                case 'Total Units':
                    $csvArray[$header] = $QAInfo['totalUnits'] ?? '';
                    break;
                case 'Reason For Denial':
                    $csvArray[$header] = $reasonForDenial;
                    break;
                case 'Submission Of Application From':
                    $csvArray[$header] = $QAInfo['submissionOfApplicationFrom'] ?? '';
                    break;
                case 'Combined Loan-To-Value Ratio':
                    $csvArray[$header] = $QAInfo['combinedLoanToValueRatio'] ?? '';
                    break;
                case 'Census Tract':
                    $csvArray[$header] = $QAInfo['censusTract'] ?? '';
                    break;
                default:
                    if (isset(exportClientFiles::$selectedFieldsValues[$header])) {
                        $csvArray[$header] = exportClientFiles::$selectedFieldsValues[$header];
                        break;
                    }
            }
        }
        $csvArrayDummy = [];
        foreach ($csvArray as $headerKey => $columnValue) {
            if (strpos($headerKey, '##') === false) {
                $csvArrayDummy[self::getColumnOrder($headerKey) . $headerKey] = $columnValue;
            } else {
                $csvArrayDummy[$headerKey] = $columnValue;
            }
        }

        if (in_array('Checklist Status', exportClientFiles::$selectedFieldsArray)) {
            $csvArrayDummy[self::getColumnOrder('Checklist Status Count', 1) . 'Resend - Not Legible'] = $notEligibleStatus;
            $csvArrayDummy[self::getColumnOrder('Checklist Status Count', 2) . 'Pending Review'] = $pendingReviewStatus;
            $csvArrayDummy[self::getColumnOrder('Checklist Status Count', 3) . 'OK / Complete'] = $completeStatus;
            $csvArrayDummy[self::getColumnOrder('Checklist Status Count', 4) . 'Not Received'] = $missingdocsnotcheckedoff;
        }
        if (in_array('Checklist Status Count', exportClientFiles::$selectedFieldsArray)) {
            $csvArrayDummy[self::getColumnOrder('Checklist Status Count', 1) . 'No of Resend - Not Legible'] = $tt;
            $csvArrayDummy[self::getColumnOrder('Checklist Status Count', 2) . 'No of Pending Review'] = $pp;
            $csvArrayDummy[self::getColumnOrder('Checklist Status Count', 3) . 'No of OK / Complete'] = $cc;
            $csvArrayDummy[self::getColumnOrder('Checklist Status Count', 4) . 'No of Not Received'] = $noofmissingdocsnotcheckedoff;
        }


        if (in_array('Days In Previous Status', exportClientFiles::$selectedFieldsArray)) {
            $lastPrimaryStatus = getFileDaysInPreviousStatus::getReport(exportClientFiles::$PCID, $LMRId);
            $previousState = $lastPrimaryStatus['primaryStatus'] ?? '';
            $nDays = $lastPrimaryStatus['nDays'] ?? 0;
            //calculate No of Days
            if ($previousState == '' && $nDays == 0) {
                $previousState = '';
                $nDays = '';
            } elseif ($previousState && $nDays == 0) {
                $newRecordDate = $lastPrimaryStatus['recordDate'];
                $newRecordDate = Dates::formatDateWithRE(date('Y-m-d', strtotime($newRecordDate)), 'YMD', 'm/d/Y');
                if (trim($newRecordDate)) {
                    $nDays = floor((strtotime(date('Y-m-d')) - strtotime($recordDate)) / (60 * 60 * 24));
                }
            }
            $csvArrayDummy[self::getColumnOrder('Days In Previous Status') . 'Previous Status'] = $previousState;
            $csvArrayDummy[self::getColumnOrder('Days In Previous Status') . 'No Of Days'] = $nDays;
        }
        ksort($csvArrayDummy);

        return $csvArrayDummy;
    }

    public static function getColumnOrder(string $headerKey, int $isMultiColumnsHeader = 0): string
    {
        if (in_array($headerKey, exportClientFiles::$exportColumnByOrder)) {
            $columnOrder = array_search($headerKey, exportClientFiles::$exportColumnByOrder);
        } else if ($headerKey == 'File ID') {
            $columnOrder = 0;
        } else {
            exportClientFiles::$columnOrderDummy++;
            $columnOrder = exportClientFiles::$columnOrderDummy;
        }
        return sprintf("%03d", $columnOrder) . ($isMultiColumnsHeader ? '_' . $isMultiColumnsHeader . '_' : '') . '##';
    }

    public static function setHeaderByOrder()
    {
        $columnOrder = 0;
        $exportColumnByOrder = [];
        foreach (exportClientFiles::$selectedFieldsArray as $headerColumn) {
            if (strpos($headerColumn, '##') === false) {
                $exportColumnByOrder[] = self::getColumnOrder($headerColumn) . $headerColumn;
            } else {
                $exportColumnByOrder[] = $headerColumn;
            }
        }
        exportClientFiles::$selectedFieldsArray = $exportColumnByOrder;
    }

    public static function getVarName(string $selectedField)
    {
        $selectedField = strtolower(str_replace(' ', '', $selectedField));
        $selectedField = str_replace('/', '', str_replace('-', '', $selectedField));
        $selectedField = str_replace('%', '', $selectedField);
        $selectedField = str_replace('#', '', $selectedField);
        $selectedField = str_replace('?', '', $selectedField);
        $selectedField = str_replace('(', '', $selectedField);
        return str_replace(')', '', $selectedField);
    }

    public static function getInternalLoanProgram(?array $fileInternalLP)
    {
        $lpname = '';
        foreach ($fileInternalLP ?? [] as $ilp => $item) {
            if ($ilp == 0) {
                $lpname = $item['serviceType'];
            } else {
                $lpname = $lpname . ' , ' . $item['serviceType'];
            }
        }
        return $lpname;
    }

    /**
     * @param string $propertyURLLink1
     * @param string $propertyURLLink2
     * @return string
     */
    public static function getPropertyLink(string $propertyURLLink1, string $propertyURLLink2): string
    {
        $appandComma = '';
        $urllinktoproperty = '';
        if ($propertyURLLink1) {
            $urllinktoproperty = $propertyURLLink1;
            $appandComma = ', ';
        }
        if ($propertyURLLink2) {
            $urllinktoproperty .= $appandComma . $propertyURLLink2;
        }
        return $urllinktoproperty;
    }

    public static function addHeader(string $header)
    {
        if (!in_array($header, exportClientFiles::$selectedFieldsArray)) {
            exportClientFiles::$selectedFieldsArray[] = $header;
        }
    }

    public static function unsetHeader(string $header)
    {
        if (in_array($header, exportClientFiles::$selectedFieldsArray)) {
            unset(exportClientFiles::$selectedFieldsArray[array_search($header, exportClientFiles::$selectedFieldsArray)]);
            exportClientFiles::$selectedFieldsArray = array_values(exportClientFiles::$selectedFieldsArray);
        }
    }

    public static function manyToOneOptions()
    {
        foreach (self::$manyToOneHeaders as $header) {
            self::unsetHeader($header);
        }
    }

    public static function unsupportedOptions()
    {
        if (in_array('Checklist Status', exportClientFiles::$selectedFieldsArray)) {
            unset(exportClientFiles::$selectedFieldsArray[array_search('Checklist Status', exportClientFiles::$selectedFieldsArray)]);
            exportClientFiles::$selectedFieldsArray = array_values(exportClientFiles::$selectedFieldsArray);
        }
        if (in_array('Checklist Status Count', exportClientFiles::$selectedFieldsArray)) {
            unset(exportClientFiles::$selectedFieldsArray[array_search('Checklist Status Count', exportClientFiles::$selectedFieldsArray)]);
            exportClientFiles::$selectedFieldsArray = array_values(exportClientFiles::$selectedFieldsArray);
        }

        if (in_array('Days in Status', exportClientFiles::$selectedFieldsArray)) {
            unset(exportClientFiles::$selectedFieldsArray[array_search('Days in Status', exportClientFiles::$selectedFieldsArray)]);
            exportClientFiles::$selectedFieldsArray = array_values(exportClientFiles::$selectedFieldsArray);
        }

        if (in_array('Types of Required Insurance', exportClientFiles::$selectedFieldsArray)) {
            unset(exportClientFiles::$selectedFieldsArray[array_search('Types of Required Insurance', @exportClientFiles::$selectedFieldsArray)]);
        }
        if (in_array('Name of Carrier', exportClientFiles::$selectedFieldsArray)) {
            unset(exportClientFiles::$selectedFieldsArray[array_search('Name of Carrier', @exportClientFiles::$selectedFieldsArray)]);
        }
        if (in_array('Policy Name', exportClientFiles::$selectedFieldsArray)) {
            unset(exportClientFiles::$selectedFieldsArray[array_search('Policy Name', @exportClientFiles::$selectedFieldsArray)]);
        }
        if (in_array('Insurance Policy Number', exportClientFiles::$selectedFieldsArray)) {
            unset(exportClientFiles::$selectedFieldsArray[array_search('Insurance Policy Number', @exportClientFiles::$selectedFieldsArray)]);
        }
        if (in_array('Annual Premium', exportClientFiles::$selectedFieldsArray)) {
            unset(exportClientFiles::$selectedFieldsArray[array_search('Annual Premium', @exportClientFiles::$selectedFieldsArray)]);
        }
        if (in_array('Policy Expiration Date', exportClientFiles::$selectedFieldsArray)) {
            unset(exportClientFiles::$selectedFieldsArray[array_search('Policy Expiration Date', @exportClientFiles::$selectedFieldsArray)]);
        }
        if (in_array('Appraised Value', exportClientFiles::$selectedFieldsArray)) {
            unset(exportClientFiles::$selectedFieldsArray[array_search('Appraised Value', @exportClientFiles::$selectedFieldsArray)]);
            exportClientFiles::$selectedFieldsArray = array_values(exportClientFiles::$selectedFieldsArray);
        }
        if (in_array('Appraiser Rehabbed Value', exportClientFiles::$selectedFieldsArray)) {
            unset(exportClientFiles::$selectedFieldsArray[array_search('Appraiser Rehabbed Value', @exportClientFiles::$selectedFieldsArray)]);
            exportClientFiles::$selectedFieldsArray = array_values(exportClientFiles::$selectedFieldsArray);
        }
        if (in_array('Appraiser Monthly Rent', exportClientFiles::$selectedFieldsArray)) {
            unset(exportClientFiles::$selectedFieldsArray[array_search('Appraiser Monthly Rent', @exportClientFiles::$selectedFieldsArray)]);
            exportClientFiles::$selectedFieldsArray = array_values(exportClientFiles::$selectedFieldsArray);
        }
        if (in_array('Appraisal Date', exportClientFiles::$selectedFieldsArray)) {
            unset(exportClientFiles::$selectedFieldsArray[array_search('Appraisal Date', @exportClientFiles::$selectedFieldsArray)]);
            exportClientFiles::$selectedFieldsArray = array_values(exportClientFiles::$selectedFieldsArray);
        }
        if (in_array('AVM As Is Value', exportClientFiles::$selectedFieldsArray)) {
            unset(exportClientFiles::$selectedFieldsArray[array_search('AVM As Is Value', @exportClientFiles::$selectedFieldsArray)]);
            exportClientFiles::$selectedFieldsArray = array_values(exportClientFiles::$selectedFieldsArray);
        }
        if (in_array('BPO Value', exportClientFiles::$selectedFieldsArray)) {
            unset(exportClientFiles::$selectedFieldsArray[array_search('BPO Value', @exportClientFiles::$selectedFieldsArray)]);
            exportClientFiles::$selectedFieldsArray = array_values(exportClientFiles::$selectedFieldsArray);
        }
        if (in_array('BPO Rehabbed Value', exportClientFiles::$selectedFieldsArray)) {
            unset(exportClientFiles::$selectedFieldsArray[array_search('BPO Rehabbed Value', @exportClientFiles::$selectedFieldsArray)]);
            exportClientFiles::$selectedFieldsArray = array_values(exportClientFiles::$selectedFieldsArray);
        }
        if (in_array('BPO Monthly Rent', exportClientFiles::$selectedFieldsArray)) {
            unset(exportClientFiles::$selectedFieldsArray[array_search('BPO Monthly Rent', @exportClientFiles::$selectedFieldsArray)]);
            exportClientFiles::$selectedFieldsArray = array_values(exportClientFiles::$selectedFieldsArray);
        }
        if (in_array('BPO Date Obtained', exportClientFiles::$selectedFieldsArray)) {
            unset(exportClientFiles::$selectedFieldsArray[array_search('BPO Date Obtained', @exportClientFiles::$selectedFieldsArray)]);
            exportClientFiles::$selectedFieldsArray = array_values(exportClientFiles::$selectedFieldsArray);
        }
        if (in_array('AVM Monthly Rent', exportClientFiles::$selectedFieldsArray)) {
            unset(exportClientFiles::$selectedFieldsArray[array_search('AVM Monthly Rent', @exportClientFiles::$selectedFieldsArray)]);
        }
        if (in_array('Appraiser Order Date', exportClientFiles::$selectedFieldsArray)) {
            unset(exportClientFiles::$selectedFieldsArray[array_search('Appraiser Order Date', @exportClientFiles::$selectedFieldsArray)]);
            exportClientFiles::$selectedFieldsArray = array_values(exportClientFiles::$selectedFieldsArray);
        }
        if (in_array('Draw Date', exportClientFiles::$selectedFieldsArray)) {
            unset(exportClientFiles::$selectedFieldsArray[array_search('Draw Date', @exportClientFiles::$selectedFieldsArray)]);
            exportClientFiles::$selectedFieldsArray = array_values(exportClientFiles::$selectedFieldsArray);
        }
        if (in_array('Appraiser Company', exportClientFiles::$selectedFieldsArray)) {
            unset(exportClientFiles::$selectedFieldsArray[array_search('Appraiser Company', @exportClientFiles::$selectedFieldsArray)]);
            exportClientFiles::$selectedFieldsArray = array_values(exportClientFiles::$selectedFieldsArray);
            self::addHeader('Appraiser Company 1');
        }
        if (in_array('Checklist Status', exportClientFiles::$selectedFieldsArray)) {
            unset(exportClientFiles::$selectedFieldsArray[array_search('Checklist Status', exportClientFiles::$selectedFieldsArray)]);
            exportClientFiles::$selectedFieldsArray = array_values(exportClientFiles::$selectedFieldsArray);
        }
        if (in_array('Checklist Status Count', exportClientFiles::$selectedFieldsArray)) {
            unset(exportClientFiles::$selectedFieldsArray[array_search('Checklist Status Count', exportClientFiles::$selectedFieldsArray)]);
            exportClientFiles::$selectedFieldsArray = array_values(exportClientFiles::$selectedFieldsArray);
        }
        if (in_array('Days in Status', exportClientFiles::$selectedFieldsArray)) {
            unset(exportClientFiles::$selectedFieldsArray[array_search('Days in Status', exportClientFiles::$selectedFieldsArray)]);
            exportClientFiles::$selectedFieldsArray = array_values(exportClientFiles::$selectedFieldsArray);
        }
    }

    public static function requiredValuation(?array $HMLOPropInfo): string
    {
        if (!isset($HMLOPropInfo['reqValuationMethod'])) {
            return '';
        }

        $reqValuationMethodArray = explode(',', ($HMLOPropInfo['reqValuationMethod']));
        if (count($reqValuationMethodArray) > 0) {
            $getValuationMethodsGlobal = getValuationMethodsGlobal::getReport('ALL');
            $reqValuationMethodTextArray = [];
            foreach ($reqValuationMethodArray as $eachRequestValuationMethodId) {
                if (!($getValuationMethodsGlobal[$eachRequestValuationMethodId] ?? null)) {
                    continue;
                }
                $reqValuationMethodTextArray[] = $getValuationMethodsGlobal[$eachRequestValuationMethodId];
            }
            return implode(',', $reqValuationMethodTextArray);
        }
        return '';
    }

    /**
     * @param $LMRId
     * @return string
     */
    public static function getSubStatus($LMRId): string
    {
        $subStatusInfo = null;
        $substatus = '';

        if (array_key_exists($LMRId, exportClientFiles::$fileSubstatusInfo)) {
            $subStatusInfo = exportClientFiles::$fileSubstatusInfo[$LMRId];
        }
        if ($subStatusInfo) {
            $substatus = Arrays::implode2dArray(', ', $subStatusInfo, 'substatus');
        }
        return $substatus;

    }

    public static function getWorkflow(int $LMRId, array $tArray, array $secondaryStatusInfo)
    {
        if (!exportClientFiles::$pcWorkFlowList) {
            return;
        }

        if (!(exportClientFiles::$fileWorkflowSteps[$LMRId] ?? null)) {
            return;
        }

        foreach (exportClientFiles::$pcWorkFlowList as $workFlowInfo) {
            $workFlowHeaderTxt = 'WorkFlow: ' . $workFlowInfo['WFName'] . ' - ' . $workFlowInfo['steps'];

            self::addHeader(self::getColumnOrder('Workflow') . $workFlowHeaderTxt);
            exportClientFiles::$selectedFieldsValues[self::getColumnOrder('Workflow') . $workFlowHeaderTxt] = exportClientFiles::$fileWorkflowSteps[$LMRId][$workFlowInfo['WFSID']] ? 'true' : 'false';
        }

        if (count(exportClientFiles::$tArray) > 0) {
            foreach ($secondaryStatusInfo as $secondary) {
                $noOfDays = '';
                $WFName = trim($secondary['WFName']);
                $WFID = trim($secondary['WFID']);
                $WFSID = trim($secondary['WFSID']);

                $steps = trim($secondary['steps']);
                self::addHeader($WFName);
                exportClientFiles::$selectedFieldsValues[$WFName] = $steps;
                if (array_key_exists($LMRId, exportClientFiles::$noofDaysArray)) {
                    if (array_key_exists($WFID, exportClientFiles::$noofDaysArray[$LMRId])) {
                        if (array_key_exists($WFSID, exportClientFiles::$noofDaysArray[$LMRId][$WFID])) {
                            $noOfDays = exportClientFiles::$noofDaysArray[$LMRId][$WFID][$WFSID];
                            if ($noOfDays) {
                                $steps .= ', # Days : ' . $noOfDays;
                            }
                        }
                    }
                }
                self::addHeader($WFName . ' Days in Status');
                exportClientFiles::$selectedFieldsValues[$WFName . ' Days in Status'] = $noOfDays;
            }
        }
    }

    public static function getDaysInCurrentStatus(int $LMRId)
    {
        $daysincurrentstatus = null;
        if (array_key_exists($LMRId, exportClientFiles::$noOfDaysInCurrentStatus)) {
            if (array_key_exists('noOfDays', exportClientFiles::$noOfDaysInCurrentStatus[$LMRId])) {
                $daysincurrentstatus = exportClientFiles::$noOfDaysInCurrentStatus[$LMRId]['noOfDays'];
            }
        }
        return $daysincurrentstatus;
    }
}
