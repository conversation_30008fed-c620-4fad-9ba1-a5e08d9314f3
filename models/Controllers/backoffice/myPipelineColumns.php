<?php

namespace models\Controllers\backoffice;

use models\composite\oPC\PCInfo;
use models\composite\oUser\getUserPreferences;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glFileType;
use models\constants\gl\glUserRole;
use models\constants\pipelineUserPreferencesArrayForBF;
use models\constants\pipelineUserPreferencesColumnsForHMLO;
use models\constants\pipelineUserPreferencesArrayForMF;
use models\constants\pipelineUserPreferencesArrayLM;
use models\lendingwise\tblFieldsQuickApp;
use models\lendingwise\tblFormFieldsMaster;
use models\lendingwise\tblSavedView;
use models\cypher;
use models\types\strongType;

class myPipelineColumns extends strongType
{
    public static ?int $userNumber = null;
    public static ?int $PCID = null;
    public static ?string $userGroup = null;

    public static ?string $enc_userNumber = null;
    public static ?string $enc_PCID = null;
    public static ?string $enc_userGroup = null;

    public static ?array $PCModuleInfo = null;
    public static ?string $fileType = null;
    public static ?string $userRole = null;
    public static ?int $isPCHMLO = null;
    public static ?int $loggedInUserPCID = null;
    public static ?int $allowToCFPBSubmitForPC = null;
    public static ?array $PCStatusInfoArray = null;

    public static ?array $pipelineUserPreferencesArray = null;
    public static ?array $selectedColumns = null;
    public static ?array $selectedColumnsOrder = null;
    public static ?array $selectedColumnsAll = null;
    public static ?array $pipelineHeaderKeyArray = null;
    public static ?array $pipelineHeader = null;
    public static array $columnOrder = [];

    public static ?int $allowEmailCampaignPC = null;
    public static ?array $fieldLabels = null;

    public static ?array $mapFieldLabelsCategoryList = [
        'Appraisal info',
        'Loan Info',
    ];

    public static function Init(
        int    $userNumber,
        ?int   $PCID,
        string $userGroup,
        string $userRole,
        string $fileType,
        array  $PCModuleInfo,
        ?int   $loggedInUserPCID,
        ?int   $isPCHMLO,
        ?int   $allowToCFPBSubmitForPC
    )
    {
        self::$userNumber = $userNumber;
        self::$PCID = $PCID;
        self::$userGroup = $userGroup;

        self::$enc_userNumber = cypher::myEncryption($userNumber);
        self::$enc_PCID = cypher::myEncryption($PCID);
        self::$enc_userGroup = cypher::myEncryption($userGroup);

        // column settings
        self::$userRole = $userRole;
        self::$fileType = $fileType;
        self::$PCModuleInfo = $PCModuleInfo;

        self::$loggedInUserPCID = $loggedInUserPCID;
        self::$isPCHMLO = $isPCHMLO;
        self::$allowToCFPBSubmitForPC = $allowToCFPBSubmitForPC;
        $PCInfo = PCInfo::getReport(['PCID' => $PCID]);
        self::$allowEmailCampaignPC = $PCInfo['allowEmailCampaign'] ?? 0;

        if (self::$PCID) {
            $fields = tblFieldsQuickApp::GetCachedForPCID(self::$PCID);
        } else {
            $fields = tblFormFieldsMaster::GetAll();
        }

        foreach ($fields as $fieldInfo) {
            self::$fieldLabels[$fieldInfo->fieldName] = $fieldInfo->fieldLabel;
        }

        // Ensure labels for new points columns are available even if they are
        // not returned from the database.
        self::$fieldLabels['brokerPointsRate'] ??= 'Broker Points %';
        self::$fieldLabels['brokerPointsValue'] ??= 'Broker Points Value';
        self::$fieldLabels['originationPointsRate'] ??= 'Origination Points %';
        self::$fieldLabels['originationPointsValue'] ??= 'Origination Points Value';
    }

    public static function InitColumnSettings()
    {

        $pipelineUserPreferencesOptions = [
            'LM'   => pipelineUserPreferencesArrayLM::$pipelineUserPreferencesArray,
            'SS'   => null, // no special handling
            'HMLO' => pipelineUserPreferencesColumnsForHMLO::$columns,
            'loc'  => pipelineUserPreferencesArrayForBF::$pipelineUserPreferencesArrayForBF,
            'MF'   => pipelineUserPreferencesArrayForMF::$pipelineUserPreferencesArrayForMF,
        ];

        $pipelineHeaderValueArray = [];

        if (self::$userRole == glUserRole::USER_ROLE_REST
            || self::$fileType == glFileType::FILE_TYPE_2
            || self::$fileType == glFileType::FILE_TYPE_4
            || self::$fileType == glFileType::FILE_TYPE_LA) {
            return;
        }

        /**
         * Description : hide the specific fields according the file module types
         * Date        : Mar 17, 2017
         * Author      : Viji
         **/
        $PCModuleInfoKeys = array_keys(self::$PCModuleInfo);

        $pipelineUserPreferencesArray = [];

        foreach ($PCModuleInfoKeys as $key) {
            if (!isset($pipelineUserPreferencesOptions[$key])) {
                continue;
            }
            if (isset($pipelineUserPreferencesOptions[$key]['Loan Info V2']) && !glCustomJobForProcessingCompany::showLoanInfoV2(myPipelineColumns::$PCID)) {
                unset($pipelineUserPreferencesOptions[$key]['Loan Info V2']);
            }

            foreach ($pipelineUserPreferencesOptions[$key] as $header => $columns) {
                foreach ($columns as $column => $display) {
                    $column = $column . '';
                    if (in_array($header, self::$mapFieldLabelsCategoryList)) {
                        if (self::$fieldLabels[$column]) {
                            $pipelineUserPreferencesArray[$header][$column] = self::$fieldLabels[$column];
                        } else {
                            $pipelineUserPreferencesArray[$header][$column] = $display;
                        }
                    } else {
                        $pipelineUserPreferencesArray[$header][$column] = $display;
                    }
                }
            }
        }

        $pipelineUserPreferencesKeyArray = array_keys($pipelineUserPreferencesArray);
        $pipelineHeaderKeyArray = getUserPreferences::getReport([
            'UID'      => self::$userNumber,
            'UType'    => self::$userGroup,
            'PCID'     => self::$loggedInUserPCID,
            'isPCHMLO' => self::$isPCHMLO,
        ]);

        $selectedColumns = [];
        $selectedColumnsOrder = [];
        if (sizeof($pipelineHeaderKeyArray) > 0) {
            foreach ($pipelineHeaderKeyArray as $pipelineHeaderKeys) {
                $column = $pipelineHeaderKeys['COLUMNS'];
                // if we've posted a selection of columns and a saved column isn't in it, don't try to mark it selected
                if (!isset($_REQUEST['headerColumn']) || in_array($column, $_REQUEST['headerColumn'])) {
                    $selectedColumns[] = $column;
                    $selectedColumnsOrder[$column] = intval($pipelineHeaderKeys['order']);
                }
            }
        }

        if (isset($_REQUEST['headerColumn'])) {

            $headerColumns = $_REQUEST['headerColumn'];
            $filterColumnOrder = $_POST['filterColumnOrder'];

            $sortingReturn = self::sortingLogic($headerColumns, $filterColumnOrder);
            $selectedColumns = $sortingReturn['selectedColumns'];
            $selectedColumnsOrder = ($filterColumnOrder) ? $sortingReturn['selectedColumnsOrder'] : $selectedColumnsOrder;

        } elseif ($_REQUEST['savedView'] ?? null) {

            $view = tblSavedView::getView(
                self::$userNumber,
                self::$userGroup,
                'LMRReport',
                $_REQUEST['savedView']
            );
            $vals = json_decode($view->postVars, true);
            if ($vals) {
                ksort($vals);
            }
            $headerColumns = $vals['headerColumn'];
            $filterColumnOrder = $vals['filterColumnOrder'];
            $sortingReturn = self::sortingLogic($headerColumns, $filterColumnOrder);
            $selectedColumns = $sortingReturn['selectedColumns'];
            $selectedColumnsOrder = $sortingReturn['selectedColumnsOrder'];
        }

        $selectedColumnsAll = [];
        foreach ($pipelineUserPreferencesKeyArray as $key) {
            $pipelineHeaderValue = trim($key);
            $pipelineHeaderValueArray = $pipelineUserPreferencesArray[$pipelineHeaderValue];
            if (self::$allowToCFPBSubmitForPC == 0) {
                unset($pipelineHeaderValueArray['CFPBSubmittedDate']);
            }
        }
        foreach ($pipelineUserPreferencesArray as $columns) {
            foreach ($columns as $column => $display) {
                if (in_array($column, $selectedColumns)) {
                    $selectedColumnsAll[$column] = $display;
                }
            }
        }

        if (isset($pipelineHeaderValueArray['LMRInternalLoanProgram'])) {
            if (self::$userGroup != 'Employee') {
                unset($pipelineHeaderValueArray['LMRInternalLoanProgram']);
            }
        }

        $pipelineHeaderValueKeyArray = array_keys($pipelineHeaderValueArray);
        foreach ($pipelineHeaderValueKeyArray as $pipelineHeader) {
            if ($pipelineHeaderValueArray[trim($pipelineHeader)] == 'Agent') {
                $columnLabel = 'Broker';
            } else {
                $columnLabel = $pipelineHeaderValueArray[trim($pipelineHeader)];
            }
            if (in_array($pipelineHeader, $selectedColumns)) {
                $selectedColumnsAll[$pipelineHeader] = $columnLabel;
            }
        }

        self::$selectedColumns = $selectedColumns;
        self::$pipelineUserPreferencesArray = $pipelineUserPreferencesArray;
        self::$selectedColumnsOrder = $selectedColumnsOrder;
        self::$selectedColumnsAll = $selectedColumnsAll;
        self::$pipelineHeaderKeyArray = $pipelineHeaderKeyArray;
    }

    public static function sortingLogic($headerColumn, $filterColumnOrder): ?array
    {

        //  pr(func_get_args());
        $selectedColumns = [];
        $selectedColumnsOrder = [];
        foreach ($headerColumn as $column) {
            $selectedColumns[] = $column;
        }

        if ($filterColumnOrder ?? null) {
            $filterColumnOrder = $filterColumnOrder ?? null;
            $givenOrder = explode(',', $filterColumnOrder);
            foreach ($givenOrder as $order => $column) {
                if (in_array($column, $selectedColumns)) {
                    $selectedColumnsOrder[$column] = $order + 1;
                }
            }

            $order = sizeof($selectedColumnsOrder) ? max($selectedColumnsOrder) + 1 : 1;
            foreach ($headerColumn as $column) {
                if (!array_key_exists($column, $selectedColumnsOrder)) {
                    $selectedColumnsOrder[$column] = $order;
                    $order++;
                }
            }
            asort($selectedColumnsOrder);
        }

        return [
            'selectedColumns'      => $selectedColumns,
            'selectedColumnsOrder' => $selectedColumnsOrder,
        ];

    }

    public static function setPCStatusInfoArray()
    {
        self::$PCStatusInfoArray = [];
        foreach (myPipeline::$PCStatusInfoForTab ?? [] as $pcStatus) {
            self::$PCStatusInfoArray[$pcStatus['PSID']] = $pcStatus['primaryStatus'];
        }
    }

    public static function initColumns(
        string $fileType,
        string $allowCFPBAuditing,
        array  $CFPBAuditingUsersHeader
    )
    {
        $pipelineHeader = [];
        for ($i = 0; $i < count(self::$pipelineHeaderKeyArray ?? []); $i++) {
            $pipelineHeader[trim(self::$pipelineHeaderKeyArray[$i]['COLUMNS'])] = trim(self::$pipelineHeaderKeyArray[$i]['COLUMNS']);
        }
        if ($fileType == glFileType::FILE_TYPE_CFPB && $allowCFPBAuditing == 1) {
            $pipelineHeader = [];
            for ($i = 0; $i < count($CFPBAuditingUsersHeader); $i++) {
                $pipelineHeader[trim($CFPBAuditingUsersHeader[$i])] = trim($CFPBAuditingUsersHeader[$i]);
            }
        }
        self::$pipelineHeader = $pipelineHeader;
    }

    public static function isColumnActive(?string $column): ?bool
    {
        if (!self::$selectedColumns) {
            return false;
        }

        return in_array($column, self::$selectedColumns, true);
    }


    public static ?array $dataCheck = [];
    public static ?array $dataCheckColumn = [];

    public static function dataCheck(): array
    {
        $missing = [];
        foreach (self::$dataCheckColumn as $column) {
            if (!isset(self::$dataCheck[$column])) {
                $missing[] = $column;
            }
        }
        return $missing;
    }

    public static function showColumnValue($column, $value, $serialNo, $title = '', $textColour = ''): string
    {
        if (!in_array($column, self::$dataCheckColumn)) {
            self::$dataCheckColumn[] = $column;
        }
        if ($value) {
            self::$dataCheck[$column] = $value;
        }

        $column = htmlentities($column);
        return '
        <td ' . $textColour . ' data-column="' . $column . '"
            title="' . htmlentities($title) . '"
            class="pipeline_column column_' . $column . '"
            id="' . $column . '_' . ($serialNo + 1) . '">
            ' . $value . '
        </td>        
        ';
    }

    /**
     * @param string $column
     * @param string $title
     * @return string
     */
    public static function showNonSortableColumn(
        string $column,
        string $title,
        string $tooltip = ''
    ): string
    {
        return '<th title="' . htmlentities($tooltip) . '" class="text-nowrap" data-column="' . $column . '">
            ' . $title . '
        </th>';
    }

    /**
     * @param string $sortOpt
     * @param string $selectedSortOpt
     * @param string $orderDir
     * @param string|null $title
     * @param bool $th
     * @param int $PCID
     * @return string
     */
    public static function showSortableColumn(
        string  $sortOpt,
        string  $selectedSortOpt,
        string  $orderDir,
        ?string $title = '',
        bool    $th = false,
        int     $PCID = 0
    ): string
    {
        $sortOpt = trim($sortOpt);
        $orderDir = trim($orderDir);
        $selectedSortOpt = trim($selectedSortOpt);
        $cellTextColor = '';
        if (glCustomJobForProcessingCompany::colorSalesdate($PCID) && $sortOpt == 'salesDate') {
            $cellTextColor = " style='color: red;'";
        }

        return ($th ? '<th title="' . htmlentities($title) . '" ' . $cellTextColor . ' data-column="' . $sortOpt . '" class="text-nowrap ">' : '') . '
    <span data-sort_opt="' . $sortOpt . '"
       data-sort_dir="asc"
       title="Sort By Ascending"
       class="tooltipClass"
       onclick="myPipelineFunctions.showSortableList(this);">  
         <i class="fas fa-arrow-up icon-nm ' . ($sortOpt == $selectedSortOpt && $orderDir == 'asc' ? ' text-primary' : 'icon-inactive') . '"></i>
    </span>
        ' . ($title ? '<span class="mx-2">' . $title . '</span>' : '') . '
    <span data-sort_opt="' . $sortOpt . '"
       data-sort_dir="desc"
       title="Sort By Descending"
              class="tooltipClass"
       onclick="myPipelineFunctions.showSortableList(this);">
       <i class="fas fa-arrow-down icon-nm ' . ($sortOpt == $selectedSortOpt && $orderDir == 'desc' ? ' text-primary' : 'icon-inactive') . '"></i>
    </span>
    ' . ($th ? '</th>' : '');
    }

    public static function getFieldLabel(string $string)
    {
        return self::$fieldLabels[$string] ?? $string;
    }

}
