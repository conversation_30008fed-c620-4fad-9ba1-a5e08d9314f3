<?php

namespace models\Controllers\backoffice;

use models\composite\oBroker\getPCAgents;
use models\constants\gl\glBranchId;
use models\constants\gl\glCustomJobForBranch;
use models\cypher;
use models\lendingwise\tblCompanyLicenseDetails;
use models\lendingwise\tblFile;
use models\lendingwise\tblFileModules;
use models\lendingwise\tblFileResponse;
use models\lendingwise\tblFileUpdatedDate;
use models\lendingwise\tblLMRClientType;
use models\lendingwise\tblLMRequestPDF;
use models\lendingwise\tblPersonalLicenseDetails;
use models\lendingwise\tblRecordFileStatus;
use models\myFileInfo;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;
use models\types\strongType;
use models\UploadServer;
use Pdfcrowd\Error;
use Pdfcrowd\HtmlToPdfClient;

class LMRequest extends strongType
{
    public static ?float $initialAdvance = null;
    public static ?float $currentLoanBalance = null;
    public static ?float $totalLoanAmount = null;
    public static ?float $totalProjectCost = null;
    public static ?string $moduleCode = null;

    public static ?array $customLoanGuidelinesStateKeys = null;
    public static ?array $propertyTypeKeys = null;

    public static ?int $hasChangeLog = null;

    public static ?int $LMRId = null;
    public static ?string $loanProgram = null;
    private static ?tblFile $File = null;
    private static ?myFileInfo $myFileInfo = null;
    private static ?string $PDFDocName;
    public static ?array $agentList = null;

    public static ?string $termsConditionsText = null;

    public static ?array $pdfStatus = [
        1 => 'Generating PDF Document',
        2 => 'Generated PDF Document',
        3 => 'Error In Creation',
    ];

    public static ?string $activeTab = null;

    public static ?int $allowToEdit = 1;

    public static ?int $PCID = null;
    public static array $fileCustomLoanGuideline = [];
    public static array $fileCustomLoanGuidelinePropertyTypes = [];
    public static array $fileCustomLoanGuidelineFuturePropertyTypes = [];
    public static array $fileCustomLoanGuidelinePropertyStates = [];

    public static ?tblFileUpdatedDate $lastUpdate = null;

    public static function setLMRId(?int $LMRId, bool $fast = false)
    {
        if (self::$LMRId != $LMRId) {
            self::$LMRId = $LMRId;
            self::$File = null;
            self::$myFileInfo = null;
        }

        if($fast) {
            return;
        }

        if (self::$LMRId) {
            // values that are required to work properly in loan origination side which may not be set through servicing origination
            self::$lastUpdate = tblFileUpdatedDate::Get(['fileID' => self::$LMRId]);
            if (!self::$lastUpdate) {
                self::$lastUpdate = new tblFileUpdatedDate();
                self::$lastUpdate->fileID = self::$LMRId;
                self::$lastUpdate->lastUpdatedDate = Dates::Timestamp();
                self::$lastUpdate->Save();
            }

            if(!self::File()->getTblLMRClientType_by_LMRID()) {
                $tblLMRClientType = new tblLMRClientType();
                $tblLMRClientType->LMRID = self::$LMRId;
                $tblLMRClientType->ClientType = 'TBD';
                $tblLMRClientType->Save();
            }

            if(!self::File()->getTblFileResponse_by_LMRId()) {
                $tblFileResponse = new tblFileResponse();
                $tblFileResponse->LMRId = self::File()->LMRId;
                $tblFileResponse->Save();
            }

            if (self::File()->getTblFileResponse_by_LMRId()->activeStatus != self::File()->activeStatus) {
                self::File()->getTblFileResponse_by_LMRId()->activeStatus = self::File()->activeStatus;
                self::File()->getTblFileResponse_by_LMRId()->Save();
            }

            if(!self::File()->getTblFileModules_by_fileID()) {
                $mod = new tblFileModules();
                $mod->fileID = self::$LMRId;
                $mod->recordDate = Dates::Timestamp();
                $mod->moduleCode = 'HMLO';
                $mod->activeStatus = 1;
                $mod->Save();
            }

// null until an actual change
//            if(!self::File()->lastUpdatedDate) {
//                self::File()->lastUpdatedDate = Dates::Timestamp();
//                self::File()->Save();
//            }

            if(!self::File()->recordDate) {
                self::File()->recordDate = Dates::Timestamp();
                self::File()->Save();
            }

            if((self::File()->borrowerName && !self::File()->enc_borrowerName)
                || (self::File()->borrowerLName && !self::File()->enc_borrowerLName)
                || (self::File()->borrowerEmail && !self::File()->enc_borrowerEmail)
            ) {
                self::File()->enc_borrowerName = cypher::myEncryption(self::File()->borrowerName);
                self::File()->enc_borrowerLName = cypher::myEncryption(self::File()->borrowerLName);
                self::File()->enc_borrowerEmail = cypher::myEncryption(self::File()->borrowerEmail);
                self::File()->Save();
            }
        }
    }

    public static function File(): ?tblFile
    {
        if (is_null(self::$File)) {
            if (!self::$LMRId) {
                self::$File = new tblFile();
            } else {
                self::$File = tblFile::Get(['LMRId' => self::$LMRId]);
            }
        }
        return self::$File;
    }

    /* @var getPCAgents[] $_AgentList */
    private static ?array $_AgentList = null;

    public static function getAgentList(?int $PCID): ?array
    {
        if (is_null(self::$_AgentList)) {
            self::$_AgentList = getPCAgents::getReportObjects([
                'PCID' => $PCID,
            ]);
        }
        return self::$_AgentList;
    }

    public static function myFileInfo(): ?myFileInfo
    {
        if (is_null(self::$myFileInfo)) {
            self::$myFileInfo = new myFileInfo();
            //if (self::$LMRId) {
            self::$myFileInfo->LMRId = self::$LMRId;
            //}
        }
        return self::$myFileInfo;
    }


    public static function LMRequestPDFDoc($tabName,
                                           $FileInfo
    ): ?int
    {

        LMRequest::$PDFDocName = $tabName . '_' . $FileInfo->LMRId . '_' . Dates::DateToInt(Dates::Timestamp()) . '.pdf';
        $tblLMRequestPDF = new tblLMRequestPDF();

        $tblLMRequestPDF->docName = LMRequest::$PDFDocName;
        $tblLMRequestPDF->status = 1;
        $tblLMRequestPDF->tabName = $tabName;
        $tblLMRequestPDF->LMRId = $FileInfo->LMRId;
        $tblLMRequestPDF->createdBy = Strings::GetSess('userNumber') ? intval(Strings::GetSess('userNumber')) : null;
        $tblLMRequestPDF->createdGroup = Strings::GetSess('userGroup') ?? null;
        $tblLMRequestPDF->createdDate = Dates::Timestamp();
        $tblLMRequestPDF->save();

        return $tblLMRequestPDF->id;

    }

    public static function getFullUrl($relativeUrl): string
    {
        // Determine protocol
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";

        // Get host
        $host = $_SERVER['HTTP_HOST'];

        // If the URL is already full (contains "http" or "https"), return it as is
        if (preg_match('/^https?:\/\//', $relativeUrl)) {
            return $relativeUrl;
        }

        // Combine protocol, host, and relative URL to form a full URL
        return $protocol . $host . $relativeUrl;
    }

    public static function sanitizeSourceCode(?string $htmlCode): string
    {
        // If the input is null, return an empty string
        if ($htmlCode === null) {
            return '';
        }

        // Find all href or src attributes ending with .css or .js
        preg_match_all('/(href|src)="([^"]*\.(css|js)[^"]*)"/i', $htmlCode, $matches);

        // Loop through the matches and replace relative URLs with full URLs
        foreach ($matches[2] as $relativeUrl) {
            if ($relativeUrl) {
                // Replace relative URL with full URL using getFullUrl()
                $htmlCode = str_replace($relativeUrl, self::getFullUrl($relativeUrl), $htmlCode);
            }
        }

        // Replace the class aside-minimize-hoverable with aside-minimize-hoverable aside-minimize
        return str_replace('aside-minimize-hoverable', 'aside-minimize-hoverable aside-minimize', $htmlCode);
    }

    public static function generatePDF(string $htmlCode,
                                       string $tabName,
                                       int    $LMRId
    ): ?int
    {

        LMRequest::setLMRId($LMRId);
        $FileInfo = LMRequest::myFileInfo();

        $docId = self::LMRequestPDFDoc($tabName, $FileInfo);

        $tblLMRequestPDF = tblLMRequestPDF::Get(['id' => $docId]);
        $tblLMRequestPDF->docName = null;
        try {
            $htmlCode = str_replace('aside-minimize-hoverable', ' aside-minimize-hoverable aside-minimize ', $htmlCode);
            //$htmlCode = str_replace('http://local.lendingwise.com/', 'http://app.lendingwise.com/', $htmlCode);

            $client = new HtmlToPdfClient(PDFCROWD_HTML_PDF_USERNAME, PDFCROWD_HTML_PDF_KEY);
            $client->setCustomJavascript("$('.PDFDocumentCard').hide();");
            $client->setNoMargins(true);
            $client->setElementToConvert("#FileFormBody");
            $client->setRenderingMode('viewport');
            if ($tabName == 'DS') {
                $client->setSmartScalingMode('single-page-fit');
            } else {
                $client->setSmartScalingMode('viewport-fit');
            }

            $data = $client->convertString($htmlCode);
            $size = $client->getOutputSize();

            if ($size) {
                UploadServer::upload([
                    'LMRID' => $FileInfo->LMRId,
                    'encLMRID' => cypher::myEncryption($FileInfo->LMRId),
                    'recordDate' => $FileInfo->LMRInfo()->recordDate,
                    'oldFPCID' => $FileInfo->LMRInfo()->FPCID,
                    'fileDocName' => LMRequest::$PDFDocName,
                    'tmpFileContent' => base64_encode($data),
                ]);
                $tblLMRequestPDF->docName = LMRequest::$PDFDocName;
                $tblLMRequestPDF->status = 2;
            } else {
                $tblLMRequestPDF->status = 3;
                $tblLMRequestPDF->error = 'Error : Zero Size';
            }
        } catch (Error $e) {
            $tblLMRequestPDF->status = 3;
            $tblLMRequestPDF->error = 'Error : ' . $e->getMessage();
        }
        $tblLMRequestPDF->tabName = $tabName;
        $tblLMRequestPDF->LMRId = $FileInfo->LMRId;
        $tblLMRequestPDF->updatedBy = Strings::GetSess('userNumber') ? intval(Strings::GetSess('userNumber')) : null;
        $tblLMRequestPDF->updatedGroup = Strings::GetSess('userGroup') ?? null;
        $tblLMRequestPDF->updatedDate = Dates::Timestamp();
        $tblLMRequestPDF->save();

        return $tblLMRequestPDF->id;
    }

    /**
     * @param int $LMRId
     * @return void
     */
    public static function updateFileLastUpdatedDate(int $LMRId)
    {
        $lastUpdatedDate = Dates::Timestamp();
        $tblFile = tblFile::Get(['LMRId' => $LMRId]);
        if ($tblFile) {
            $tblFile->lastUpdatedDate = $lastUpdatedDate;
            $tblFile->Save();
        }

        //
        $tblFileUpdatedDate = tblFileUpdatedDate::Get(['fileID' => $LMRId]);
        if ($tblFileUpdatedDate) {
            $tblFileUpdatedDate->lastUpdatedDate = $lastUpdatedDate;
            $tblFileUpdatedDate->Save();
        }
    }

    public static function borrowerProfileSync(): bool
    {
        $ageOfLoanFile = self::calculateAgeOfLoanFile();
        if ($ageOfLoanFile <= 30) {
            return true;
        }
        return false;
    }

    public static function calculateAgeOfLoanFile(): int
    {
        $loanFile = self::$File;
        $loanFileDate = $loanFile->recordDate ?: Dates::Timestamp();
        return Dates::DaysDiff($loanFileDate, Dates::Timestamp());
    }

    public static function getAchWithdrawalAmount(): string
    {
        $ACHWithdrawalAmount = 0;
        $loanProgram = LMRequest::File()->getTblLMRClientType_by_LMRID()->ClientType; //Loan Program
        $loanTerm = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->loanTerm; //Loan Term

        if ($loanProgram == 'Bri996') { //Bridge Loan - Renovation
            //propertyInterestOnlyPayment (From Property Info tab > Property Analysis > Interest-only Payment)
            $ACHWithdrawalAmount = LMRequest::myFileInfo()->getPrimaryProperty()->getTblPropertiesAnalysis_by_propertyId()->propertyInterrestOnlyPayment;
        } elseif ($loanProgram == 'BRL') { // Bridge Loan
            //totalLoanInterestPayment (From Loan Info V2 tab > Total Loan Interest Payment)
            $ACHWithdrawalAmount = LMRequest::myFileInfo()->getLoanPropertySummary()->totalLoanInterestPayment;
        } elseif ($loanProgram == 'Bri879861') { //Bridge Loan - Portfolio
            //then totalLoanInterestPayment (From Loan Info V2 tab > Total Loan Interest Payment)
            $ACHWithdrawalAmount = LMRequest::myFileInfo()->getLoanPropertySummary()->totalLoanInterestPayment;
        } elseif ($loanProgram == 'BRLGUC') { // Ground Up Construction
            //then totalLoanInterestPayment (From Loan Info V2 tab > Total Loan Interest Payment)
            $ACHWithdrawalAmount = LMRequest::myFileInfo()->getLoanPropertySummary()->totalLoanInterestPayment;
        } elseif ($loanProgram == 'Ren496' && $loanTerm == '360 Months- 30 Year Fixed - Fully Amortizing') { // Rental Loan
            //then = (sum) monthly30YrPIPayment
            //     + Total Monthly Insurance from Loan Info V2 > Loan Details - Rental
            //     + Total Monthly Taxes from Loan Info V2 > Loan Details - Rental
            $ACHWithdrawalAmount = LMRequest::myFileInfo()->getLoanPropertySummary()->monthly30YrPIPayment
                + LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesMonthlyInsurance
                + LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesMonthlyTaxes;
        } elseif ($loanProgram == 'Ren496' && $loanTerm == '360 Months- 5/6 ARM- 10 Year Interest Only') {
            //then = (sum) totalLoanInterestPayment
            //     +  Total Monthly Insurance from Loan Info V2 > Loan Details - Rental
            //     + Total Monthly Taxes from Loan Info V2 > Loan Details - Rental
            $ACHWithdrawalAmount = LMRequest::myFileInfo()->getLoanPropertySummary()->totalLoanInterestPayment
                + LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesMonthlyInsurance
                + LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesMonthlyTaxes;
        } elseif ($loanProgram == 'Ren949' && $loanTerm == '360 Months- 30 Year Fixed - Fully Amortizing') { // Rental Loan - Portfolio
            //then = (sum) monthly30YrPIPayment
            //     + Total Monthly Insurance from Loan Info V2 > Loan Details - Rental
            //     + Total Monthly Taxes from Loan Info V2 > Loan Details - Rental
            $ACHWithdrawalAmount = LMRequest::myFileInfo()->getLoanPropertySummary()->monthly30YrPIPayment
                + LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesMonthlyInsurance
                + LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesMonthlyTaxes;
        } elseif ($loanProgram == 'Ren949' && $loanTerm == '360 Months- 5/6 ARM- 10 Year Interest Only') { // Rental Loan - Portfolio
            //then = (sum) totalLoanInterestPayment
            //     +  Total Monthly Insurance from Loan Info V2 > Loan Details - Rental
            //     + Total Monthly Taxes from Loan Info V2 > Loan Details - Rental
            $ACHWithdrawalAmount = LMRequest::myFileInfo()->getLoanPropertySummary()->totalLoanInterestPayment
                + LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesMonthlyInsurance
                + LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesMonthlyTaxes;
        }
        return Currency::formatDollarAmountWithDecimalZeros($ACHWithdrawalAmount);
    }

    /**
     * @return float|null
     */
    public static function calOriginationPoints(): ?float
    {
        $cv3OriginationPoint = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->cv3OriginationPoint;
        $originationPointsRate = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->originationPointsRate;
        if (glCustomJobForBranch::cv3BranchIsRetail(LMRequest::File()->FBRID)) { // Retail
            $cv3OriginationPoint = $originationPointsRate - LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->cv3ReferralPoint;
        }
        if (glCustomJobForBranch::cv3BranchIsWholesale(LMRequest::File()->FBRID)) { // Wholesale
            $cv3OriginationPoint = $originationPointsRate;
        }
        return $cv3OriginationPoint;
    }

    /**
     * @return string|null
     */
    public static function getAchBorrowerName(): ?string
    {
        $borrowerType = LMRequest::File()->getTblFileHMLOBusinessEntity_by_fileID()->borrowerType ?? '';
        switch ($borrowerType) {
            case 'Entity':
                $achBorrowerName = LMRequest::File()->getTblFileHMLOBusinessEntity_by_fileID()->entityName; //entityName
                break;
            case 'Individual':
                $achBorrowerName = LMRequest::File()->borrowerName . ' ' . LMRequest::File()->borrowerMName . ' ' . LMRequest::File()->borrowerLName;
                if (LMRequest::File()->isCoBorrower) {
                    $achBorrowerName .= ' & ' . LMRequest::File()->coBorrowerFName . ' ' . LMRequest::File()->coBorrowerLName;
                }
                break;
            case 'Trust':
            case 'Retirement Entity':
                $achBorrowerName = LMRequest::File()->getTblFilePropertyInfo_by_LMRId()->titleName; //titleName
                break;
            default:
                return '';
        }

        return $achBorrowerName;

    }

    /**
     * @return false|string|null
     */
    public static function getInitialUWStatusDate()
    {
        $initialUWStatusDate = null;
        $tblRecordFileStatus = tblRecordFileStatus::GetAll([
                'LMRID'      => self::$LMRId,
                'statusID'   => 107394, //Initial UW Status
                'statusType' => 'Pri',
            ]
            , ['recordDate' => 'DESC'], 1);
        if ($tblRecordFileStatus) {
            $initialUWStatusDate = $tblRecordFileStatus[0]->recordDate
                ? Dates::formatDateWithRE($tblRecordFileStatus[0]->recordDate, 'YMD', 'm/d/Y')
                : null;
        }

        return $initialUWStatusDate;
    }

    /**
     * @return false|string|null
     */
    public static function getApprovalDate()
    {
        $approvalDate = null;
        $tblRecordFileStatus = tblRecordFileStatus::GetAll([
            'LMRID'      => self::$LMRId,
            'statusID'   => 107395, //Collecting Conditions
            'statusType' => 'Pri',
        ],
            ['recordDate' => 'DESC'], 1);
        if ($tblRecordFileStatus) {
            $approvalDate = $tblRecordFileStatus[0]->recordDate
                ? Dates::formatDateWithRE($tblRecordFileStatus[0]->recordDate, 'YMD', 'm/d/Y')
                : null;
        }

        return $approvalDate;
    }

    public static function getLenderOriginatorNMLSID(): ?string
    {
        $loanOfficerPersonalNMLSLicense = 'None';
        $file = self::$File;
        $branchId = $file->FBRID ?? null;
        if ($branchId == glBranchId::CV3_RETAIL_BRANCH_ID) {
            $loanOfficerPersonalNMLSLicense = $file->getTblFileAgent_by_secondaryBrokerNumber()->personalNMLSLicense;
        }
        if (!$loanOfficerPersonalNMLSLicense) {
            return 'None';
        }
        return $loanOfficerPersonalNMLSLicense;
    }

    public static function getLoanOfficerCompanyStateLicense(): ?string
    {
        $loanOfficerCompanyStateLicense = 'None';
        $file = self::$File;
        $branchId = $file->FBRID ?? null;
        if ($branchId == glBranchId::CV3_RETAIL_BRANCH_ID) {
            $propertyState = LMRequest::myFileInfo()->getPrimaryProperty()->propertyState;
            $loanOfficerCompanyStateLicense = tblCompanyLicenseDetails::Get([
                'AID'   => $file->secondaryBrokerNumber,
                'state' => $propertyState,
            ])->licenseNumber;
        }
        if (!$loanOfficerCompanyStateLicense) {
            return 'None';
        }
        return $loanOfficerCompanyStateLicense;
    }

    public static function getLoanOfficerPersonalStateLicense(): ?string
    {
        $loanOfficerPersonalStateLicense = 'None';
        $file = self::$File;
        $branchId = $file->FBRID ?? null;
        if ($branchId == glBranchId::CV3_RETAIL_BRANCH_ID) {
            $propertyState = LMRequest::myFileInfo()->getPrimaryProperty()->propertyState;
            $loanOfficerPersonalStateLicense = tblPersonalLicenseDetails::Get([
                'AID'   => $file->secondaryBrokerNumber,
                'state' => $propertyState,
            ])->licenseNumber;
        }
        if (!$loanOfficerPersonalStateLicense) {
            return 'None';
        }
        return $loanOfficerPersonalStateLicense;
    }

    public static function getBrokerOriginatorNMLSID(): ?string
    {
        $brokerOriginatorNMLSID = 'None';
        $file = self::$File;
        $branchId = $file->FBRID ?? null;
        if ($branchId == glBranchId::CV3_BRANCH_ID_WHOLESALE) {
            $brokerOriginatorNMLSID = LMRequest::myFileInfo()->BrokerInfo()->personalNMLSLicense;
        }
        if (!$brokerOriginatorNMLSID) {
            return 'None';
        }
        return $brokerOriginatorNMLSID;
    }

    public static function getBrokerCompanyStateLicense(): ?string
    {
        $brokerCompanyStateLicense = 'None';
        $file = self::$File;
        $branchId = $file->FBRID ?? null;
        if ($branchId == glBranchId::CV3_BRANCH_ID_WHOLESALE) {
            $propertyState = LMRequest::myFileInfo()->getPrimaryProperty()->propertyState;
            $brokerCompanyStateLicense = tblCompanyLicenseDetails::Get([
                'AID'   => $file->brokerNumber,
                'state' => $propertyState,
            ])->licenseNumber;
        }
        if (!$brokerCompanyStateLicense) {
            return 'None';
        }
        return $brokerCompanyStateLicense;
    }

    public static function getBrokerPersonalStateLicense(): ?string
    {
        $brokerPersonalStateLicense = 'None';
        $file = self::$File;
        $branchId = $file->FBRID ?? null;
        if ($branchId == glBranchId::CV3_BRANCH_ID_WHOLESALE) {
            $propertyState = LMRequest::myFileInfo()->getPrimaryProperty()->propertyState;
            $brokerPersonalStateLicense = tblPersonalLicenseDetails::Get([
                'AID'   => $file->brokerNumber,
                'state' => $propertyState,
            ])->licenseNumber;
        }
        if (!$brokerPersonalStateLicense) {
            return 'None';
        }
        return $brokerPersonalStateLicense;
    }


}
