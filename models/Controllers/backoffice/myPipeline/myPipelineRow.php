<?php

namespace models\Controllers\backoffice\myPipeline;

use models\composite\proposalFormula;
use models\constants\gl\glAdverseAction;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\Controllers\LMRequest\HUDFundingClosingInfo;
use models\Controllers\LMRequest\Property;
use models\Database2;
use models\composite\oBranch\getBranchesForAgent;
use models\composite\oWorkflow\getWFStepsOptionalConditions;
use models\Controllers\backoffice\myPipeline;
use models\HMLOLoanTermsCalculation;
use models\HMLOLoanTermsCalculation\LTC2Variables;
use models\cypher;
use models\lendingwise\tblProcessingCompany;
use models\myFileInfo;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;
use models\types\strongType;

class myPipelineRow extends strongType
{
    public static ?int $serialNo = 0;
    public static ?array $fileHMLOPropertyInfo = null;
    public static ?array $fileHMLONewLoanInfo = null;

    public ?string $recordDate = null;
    public ?string $borPhone = null; // borPhone;
    public ?string $SubmissionDate = null; // SubmissionDate;
    public ?string $lastUpdatedDate = null; // lastUpdatedDate;
    public ?string $entityName = null; // entityName;
    public ?string $borrowercellNumber = null; // borrowercellNumber;
    public ?string $workNumber = null; // workNumber;
    public ?string $borrowerEmail = null; // borrowerEmail;
    public ?string $presentCity = null; // presentCity;
    public ?string $presentState = null; // presentState;
    public ?string $assetTotalCashBankAcc = null; // assetTotalCashBankAcc;
    public ?string $statusLink = null; // statusLink;
    public ?string $filePrimaryStatus = null; // filePrimaryStatus;
    public ?string $subStatus = null; // subStatus;
    public ?string $LMRInternalLoanProgram = null; // LMRInternalLoanProgram;
    public ?string $serviceReq = null; // serviceReq;
    public ?string $propertyType = null; // propertyType;
    public ?string $propertyCity = null; // propertyCity;
    public ?string $propertyState = null; // propertyState;
    public ?string $desiredClosingDate = null; // desiredClosingDate;
    public ?string $hearingDate = null; // hearingDate;
    public ?string $CFPBSubmittedDate = null; // CFPBSubmittedDate;
    public ?string $salesDate = null; // salesDate;
    public ?string $receivedDate = null; // receivedDate;
    public ?string $lenderSubmissionDate = null; // lenderSubmissionDate;
    public ?string $closedDate = null; // closedDate;
    public ?string $closingDate = null; // closingDate;
    public ?string $targetClosingDate = null; // closingDate;
    public ?string $borrowerCallBack = null; // borrowerCallBack;
    public ?string $lenderCallBack = null; // lenderCallBack;
    public ?string $HAFADate = null; // HAFADate;
    public ?string $orderStatus = null; // orderStatus;
    public ?string $paymentStatus = null; // paymentStatus;
    public ?array $tempRespArray = null; // tempRespArray;
    public ?string $fileVelocity = null; // fileVelocity;
    public ?array $thisFileStatusHistory = null; // thisFileStatusHistory;
    public ?string $executiveId = null;
    public ?string $totalLoanAmount = null; // totalLoanAmount;
    public ?string $initialLoanAmount = null; // initialLoanAmount;
    public ?string $yieldSpread = null; // yieldSpread;
    public ?string $costOfCapital = null; // costOfCapital;
    public ?string $payrollLoanAmount = null; // payrollLoanAmount;
    public ?string $loanAuditProduct = null; // loanAuditProduct;
    public ?string $fraudLevel = null; // fraudLevel;
    public ?string $CFPBAuditSubmitterInfo = null; // CFPBAuditSubmitterInfo;
    public ?string $CFPBAuditSubmittedBy = null; // CFPBAuditSubmittedBy;
    public ?string $entityInfo = null; // entityInfo;
    public ?string $entityType = null; // entityType;
    public ?string $entityStateOfFormation = null; // entityStateOfFormation;
    public ?string $entityState = null; // entityState;
    public ?string $entityCity = null; // entityCity;
    public ?string $entityPropertyOwnerShip = null; // entityPropertyOwnerShip;
    public ?string $noOfEmployees = null; // noOfEmployees;
    public ?string $avgMonthlyCreditcardSale = null; // avgMonthlyCreditcardSale;
    public ?string $avgTotalMonthlySale = null; // avgTotalMonthlySale;
    public ?string $annualGrossProfit = null; // annualGrossProfit;
    public ?string $ordinaryBusinessIncome = null; // ordinaryBusinessIncome;
    public ?string $grossSocialSecurity = null; // grossSocialSecurity;
    public ?string $totalGrossMonthlyHouseHoldIncome = null; // totalGrossMonthlyHouseHoldIncome;
    public ?string $totalHouseHoldIncome = null; // totalHouseHoldIncome;
    public ?string $totalHouseHoldExpenses = null; // totalHouseHoldExpenses;
    public ?string $disposableIncome = null; // disposableIncome;
    public ?string $lien1DTI = null; // lien1DTI;
    public ?string $totalAssets = null; // totalAssets;
    public ?string $lender1Info = null; // lender1Info;
    public ?string $lender1Name = null; // lender1Name;
    public ?string $lender2Name = null; // lender2Name;
    public ?string $mortgageOwner1 = null; // mortgageOwner1;
    public ?string $mortgageOwner2 = null; // mortgageOwner2;
    public ?string $loanType = null; // loanType;
    public ?string $loanType2 = null; // loanType2;
    public ?string $lien1Amount = null; // lien1Amount;
    public ?string $lien2Amount = null; // lien2Amount;
    public ?string $loanNumber = null; // loanNumber;
    public ?string $loanNumber2 = null; // loanNumber2;
    public ?string $lien1Rate = null; // lien1Rate;
    public ?string $lien2Rate = null; // lien2Rate;
    public ?string $lien1Payment = null; // lien1Payment;
    public ?string $lien2Payment = null; // lien2Payment;
    public ?string $tempLien1PITIA = null; // tempLien1PITIA;
    public ?string $lien1LPMade = null; // lien1LPMade;
    public ?string $lien2LPMade = null; // lien2LPMade;
    public ?string $noOfMonthsBehind1 = null; // noOfMonthsBehind1;
    public ?string $noOfMonthsBehind2 = null; // noOfMonthsBehind2;
    public ?string $lien1BalanceDue = null; // lien1BalanceDue;
    public ?string $loanOriginationDate = null; // loanOriginationDate;
    public ?string $lien2BalanceDue = null; // lien2BalanceDue;
    public ?string $occupancy = null; // occupancy;
    public ?string $homeValue = null; // homeValue;
    public ?string $costBasis = null; // costBasis;
    public ?string $appraiser1 = null; // appraiser1;
    public ?string $BPO1 = null; // BPO1;
    public ?string $rehabValue = null; // rehabValue;
    public ?string $bedrooms = null; // bedrooms;
    public ?string $bathrooms = null; // bathrooms;
    public ?string $yearBuilt = null; // yearBuilt;
    public ?string $fileNumber = null; // fileNumber;
    public ?string $assignedEmpInfo = null; // assignedEmpInfo;
    public ?string $PCName = null; // PCName;
    public ?string $branchName = null; // branchName;
    public ?string $assignedCFPBAuditors = null; // assignedCFPBAuditors;
    public ?string $leadSource = null; // leadSource;
    public ?string $insuranceCompName = null; // insuranceCompName;
    public ?string $listingAgentName = null; // listingAgentName;
    public ?string $priorityLevel = null; // priorityLevel;
    public ?string $typeOfHMLOLoanRequesting = null; // typeOfHMLOLoanRequesting;
    public ?string $trialPaymentDate1 = null; // trialPaymentDate1;
    public ?string $proInsPolicyExpDate = null; // proInsPolicyExpDate;
    public ?string $networthOfBusinessOwned = null; // networthOfBusinessOwned;
    public ?string $borCreditScoreRange = null; // borCreditScoreRange;
    public ?string $midFico = null; // midFico;
    public ?string $borExperianScore = null; // borExperianScore;
    public ?string $borEquifaxScore = null; // borEquifaxScore;
    public ?string $borTransunionScore = null; // borTransunionScore;
    public ?string $borNoOfREPropertiesCompleted = null; // borNoOfREPropertiesCompleted;
    public ?string $borRehabPropCompleted = null; // borRehabPropCompleted;
    public ?string $borNoOfOwnProp = null; // borNoOfOwnProp;
    public ?string $isBorUSCitizen = null; // isBorUSCitizen;
    public ?string $HMLOLender = null; // HMLOLender;
    public ?string $totalProjectCost = null; // totalProjectCost;
    public ?string $rehabCostFinanced = null; // rehabCostFinanced;
    public ?string $LTC = null; // LTC;
    public ?string $ARV = null; // ARV;
    public ?string $rehabConstructionCost = null; // rehabConstructionCost;
    public ?string $propertyNeedRehab = null; // propertyNeedRehab;
    public ?string $propertyConstructionLevel = null; // propertyConstructionLevel;
    public ?string $acquisitionLTV = null; // acquisitionLTV;
    public ?string $marketLTV = null; // marketLTV;
    public ?string $perRehabCostFinanced = null; // perRehabCostFinanced;
    public ?string $exitStrategy = null; // exitStrategy;
    public ?string $isHouseProperty = null; // isHouseProperty;
    public ?string $lienPosition = null; // lienPosition;
    public ?string $propertyCounty = null; // propertyCounty;
    public ?string $propertySqFt = null; // propertySqFt;
    public ?string $propertyURLLink = null; // propertyURLLink;
    public ?string $taxes1 = null; // taxes1;
    public ?string $workflowEvents = null; // workflowEvents;
    public ?string $HMLOLoanTerm = null; // HMLOLoanTerm;
    public ?string $presentOccupancyStatus = null; // presentOccupancyStatus;
    public ?string $propertyCondition = null; // propertyCondition;
    public ?string $appraiser1Value = null; // appraiser1Value;
    public ?string $AVM1 = null; // AVM1;
    public ?string $dateObtained = null; // dateObtained;
    public ?string $appraisal1OrderDate = null; // appraisal1OrderDate;
    public ?string $totalRehabCost = null; // totalRehabCost;
    public ?string $assessedValue = null; // assessedValue;
    public ?string $titleContacts = null; // titleContacts;
    public ?string $typeOfSale = null; // typeOfSale;
    public ?string $projectName = null; // projectName;
    public ?string $MFLoanTermsInfoCnt = null; // MFLoanTermsInfoCnt;
    public ?string $noOfApprovedStatusCnt = null; // noOfApprovedStatusCnt;
    public ?string $totalApprovedLoanAmt = null; // totalApprovedLoanAmt;
    public ?string $bankNumber = null; // bankNumber;
    public ?string $lienAmount = null; // lienAmount;
    public ?string $referringParty = null; // referringParty;
    public ?string $availableBudget1 = null; // availableBudget1;
    public ?string $maturityDate = null; // maturityDate;
    public ?string $servicingStatus = null; // servicingStatus;
    public ?string $payOffDate = null; // payOffDate;
    public ?string $loanSaleDate = null; // loanSaleDate;
    public ?string $PCLink = null; // PCLink;
    public ?string $filePCID = null; // filePCID;
    public ?string $oldFPCID = null; // oldFPCID;
    public ?string $oldPCName = null; // oldPCName;
    public ?string $BranchLink = null; // BranchLink;
    public ?string $brokerName = null; // brokerName;
    public ?string $lenderName = null; // lenderName;
    public ?string $servicerName = null; // servicerName;
    public ?string $trusteeName = null; // trusteeName;
    public ?string $investorContactDetails = null; // investorContactDetails;
    public ?string $secondaryBrokerName = null; // secondaryBrokerName;
    public ?string $price = null; // price;
    public ?string $LMRResponseId = null; // LMRResponseId;
    public ?array $fileInfoArray = null;
    public ?string $borrowerLink = null;
    public ?string $entityLink = null;
    public ?array $fileModuleTypeArray = null;
    public ?int $isFileSS = null;
    public ?int $isFileLSR = null;
    public ?string $executiveIdlender1Info = null;

    public ?int $sendMarketingEmail = null;
    public ?int $activeFile = null;
    public ?string $fileType = null;
    public ?int $allowAgentEditFile = null;
    public ?int $allowToUpdateFileAdminSection = null;
    public ?string $LMRClientId = null;
    public ?int $allowCFPBAuditing = null;
    public ?int $private = null;
    public ?string $LMRId = null;
    public ?array $taskEmpInfo = null;
    public ?array $taskBranchInfo = null;
    public ?array $taskAgentInfo = null;
    public ?array $notesInfoArray = null;
    public ?string $fileActiveStatus = null;
    public ?array $RESTReportArray = null;
    public ?array $assignedCFPBAuditorInfo = null;
    public ?string $CFPBAuditFileStatus = null;
    public ?string $LMRExecutiveId = null;
    public ?string $borrowerFName = null;
    public ?string $borrowerLName = null;
    public ?string $brokerNumber = null;
    public ?string $secondaryBrokerNumber = null;
    public ?string $viewUrl = null;
    public ?int $synergyBillPaidStatus = null;
    public ?array $lockCFPBFileInfoArray = null;
    public ?string $borrowerName = null;
    public ?string $fileUrl = null;
    public ?int $loanAuditFileStatus = null;
    public ?string $currentLoanBalance = null;
    public ?string $brokerProcessingFee = null;
    public ?string $disclosureSentDate = null;
    public ?string $brokerPointsRate = null;
    public ?string $brokerPointsValue = null;
    public ?string $originationPointsRate = null;
    public ?string $originationPointsValue = null;

    public ?string $propertyAppraisalAsIsValue = null;
    public ?string $propertyAppraisalRehabbedValue = null;
    public ?string $propertyAppraisalMonthlyRent = null;
    public ?string $propertyAppraisalJobTypes = null;
    public ?string $propertyAppraisalDateObtained = null;
    public ?string $propertyAppraisalRequestedReturnDate = null;
    public ?string $propertyAppraisalIsRushOrder = null;
    public ?string $propertyAppraisalOrderDate = null;
    public ?string $propertyAppraisalComments = null;
    public ?string $propertyAppraisalEffectiveDate = null;
    public ?string $propertyAppraisalInspectionDate = null;
    public ?string $propertyAppraisalExpectedDeliveryDate = null;
    public ?int $propertyAppraisalExpectedDeliveryDelay = null;

    public ?string $coBorrowerName = null;
    public ?string $fundingDate = null;
    public ?string $clearToCloseBy = null;
    public ?string $propertyFloodZone = null;
    public ?string $requiredInsurance = null;
    public ?string $HMDAActionTaken = null;
    public ?string $noOfPropertiesAcquiring = null;
    public ?string $titleOrderDate = null;
    public ?string $lenderInternalNotes = null;
    public ?string $warehouseInvestor = null;
    public ?string $wireAmountSent = null;
    public ?string $referralPoints = null;
    public ?string $eCoaWaiverStatus = null;

    public ?string $primaryAppraisalEcoaDeliveryDate = null;

    public ?string $propertyAppraisalSupplementalProductFormType1 = null;
    public ?string $propertyAppraisalSupplementalProductFormType2 = null;
    public ?string $propertyAppraisalSupplementalProductFormType3 = null;
    public ?string $propertyAppraisalSupplementalProductEffectiveDate1 = null;
    public ?string $propertyAppraisalSupplementalProductEffectiveDate2 = null;
    public ?string $propertyAppraisalSupplementalProductEffectiveDate3 = null;
    public ?string $propertyAppraisalSupplementalProductEcoaADeliveryDate1 = null;
    public ?string $propertyAppraisalSupplementalProductEcoaADeliveryDate2 = null;
    public ?string $propertyAppraisalSupplementalProductEcoaADeliveryDate3 = null;

    public ?string $rateLockPeriod = null;
    public ?string $rateLockDate = null;
    public ?string $rateLockExpirationDate = null;
    public ?string $rateLockExtension = null;
    public ?string $rateLockNotes = null;

    public ?string $brokerPartnerType = null;
    public ?string $totalPropertiesLoanAmount = null;
    public ?string $totalPropertiesPITIA = null;
    public ?string $createdDateWithTimestamp = null;
    public ?string $showSysGenNote = null;
    public ?string $netMonthlyPayment = null;

    public ?string $MERSID = null;
    public ?string $PSAClosingDate = null;
    public ?string $buildingAnalysisOutstanding = null;
    public ?string $buildingAnalysisNeed = null;
    public ?string $buildingAnalysisDueDate = null;
    public ?string $targetSubmissionDate = null;
    public ?string $authorizationStatus = null;
    public ?string $VOMStatus = null;
    public ?string $payoffStatus = null;
    public ?string $trackRecord = null;
    public ?string $welcomeCallStatus = null;
    public ?string $propertyAppraisalStatus = null;
    public ?string $LOISentDate = null;

    public function Init(
        ?int    $r,
        ?int    $PCID,
        ?string $executiveId,
        ?int    $userNumber,
        ?int    $private,
        ?int    $allowCFPBAuditing,
        ?int    $activeFile,
        ?int    $allowToEditMyFile,
        ?int    $externalBroker,
        ?int    $allowToUpdateFileAdminSection,
        ?string $userRole,
        ?string $fileType,
        ?string $userTimeZone,
        ?string $userGroup,
        ?string $UGroup,
        ?array  $LMRInternalLoanProgramInfoArray,
        ?array  $insExpiryDatesArray,
        ?array  $LMRDataKeyArray,
        ?array  $LMRDataArray,
        ?array  $file2Info,
        ?array  $fileLOAssetsInfo,
        ?array  $fileHMLOInfo,
        ?array  $fileHMLOExperienceInfo,
        ?array  $clientInfo,
        ?array  $fileHMLOBackGroundInfo,
        ?array  $fileHMLOEntityInfo,
        ?array  $fileModuleTypeArray,
        ?array  $fileClientTypeArray,
        ?array  $PCClientTypeInfoArray,
        ?array  $glLMRClientTypeArray,
        ?array  $substatusInfoArray,
        ?array  $glMortgageOwnerArray,
        ?array  $GpropertyTypeNumbArray,
        ?array  $listingRealtorInfo,
        ?array  $fileVelocityInfo,
        ?array  $assignedBoStaff,
        ?array  $assignedCFPBAuditorInfo,
        ?array  $filePropInfo,
        ?array  $fileBranchInfo,
        ?array  $fileAgentInfo,
        ?array  $lenderFileContactInfo,
        ?array  $trusteeFileContactInfo,
        ?array  $servicerFileContactInfo,
        ?array  $investorFileContactInfo,
        ?array  $fileLoanOfficerInfo,
        ?array  $LenderInfo1Array,
        ?array  $proposalInfo,
        ?array  $PCInfo,
        ?array  $HRHistoryInfoArray,
        ?array  $fileStatusInfo,
        ?array  $agentUpdateStatusException,
        ?array  $loanAuditInfoArray,
        ?array  $loanAuditProductInfo,
        ?array  $CFPBAuditInfoArray,
        ?array  $empCFPBInfo,
        ?array  $branchCFPBInfo,
        ?array  $agentCFPBInfo,
        ?array  $clientCFPBInfo,
        ?array  $ShowSaleDate,
        ?array  $CaseFileIdInfoArray,
        ?array  $incomeInfoArray,
        ?array  $AssetsInfo,
        ?array  $QAInfo,
        ?array  $fileContactsInfo,
        ?array  $loanOriginationInfo,
        ?array  $titleContactInfo,
        ?array  $insuranceCompContactInfo,
        ?array  $fileHMLONewLoanInfo,
        ?array  $LMRWFArray,
        ?array  $WFListArray,
        ?array  $PCWFArray,
        ?array  $fileHMLOListOfRepairs,
        ?array  $listingRealtorInfo2,
        ?array  $fileMFLoanTerms,
        ?array  $fileInfoArray,
        ?array  $lockCFPBFileInfoArray,
        ?array  $taskEmpInfo,
        ?array  $taskBranchInfo,
        ?array  $taskAgentInfo,
        ?array  $notesInfoArray,
        ?array  $RESTReportArray,
        ?array  $fileHMLOPropertyInfo,
        ?array  $tempBudgetAndDrawsInfo,
        ?array  $LMRadditionalLoanprogramsInfoArray,
        ?array  $fileStatusHistory
    )
    {
        $orderStatus = '';
        $paymentStatus = '';
        $statusLink = '';
        $filePrimaryStatus = '';
        $brokerFName = '';
        $brokerLName = '';
        $branchName = '';
        $lender1Info = '';
        $PCName = '';
        $RESTFee = '';
        $price = '';
        $SubmissionDate = '';
        $allowUserToUpdatePCStatus = 0;
        $houseNo = '';
        $fraudLevel = 0;
        $loanAuditFileStatus = 1;
        $CFPBSubmittedDate = '';
        $CFPBAuditFileStatus = 1;
        $synergyBillPaidStatus = 1;
        $costBasis = '';
        $appraiser1 = '';
        $BPO1 = '';
        $rehabValue = '';
        $assessedValue = '';
        $yearBuilt = '';
        $subStatus = '';
        $brokerPhone = '';
        $brokerEmail = '';
        $tempLenderInfo1Array = [];
        $proposalInfoArray = [];
        $loanAuditProduct = '';
        $HRFee = '';
        $sendMarketingEmail = 0;
        $networthOfBusinessOwned = '';
        $fileVelocity = '';
        $insuranceCompName = '';
        $listingAgentName = '';
        $CFPBAuditSubmittedBy = '';
        $allowOthersToUpdate = 0;
        $CFPBAuditSubmitterInfo = '';
        $allowAgentToEditFile = 0;
        $allowClientToEditFile = 0;
        $allowBOToEditFile = 0;
        $borCreditScoreRange = '';
        $targetClosingDate = '';
        $midFico = '';
        $borExperianScore = '';
        $borEquifaxScore = '';
        $borTransunionScore = '';
        $borNoOfREPropertiesCompleted = '';
        $borRehabPropCompleted = '';
        $borNoOfOwnProp = '';
        $isBorUSCitizen = '';
        $appraisal1OrderDate = '';
        $requiredAdditionalCond = [];

        $totalDrawsFunded = $availableBudget1 = 0;
        $allowAgentEditFile = 0;

        $LMRId = trim($LMRDataKeyArray[$r]);
        $tempRespArray = $LMRDataArray[$LMRId];

        Property::init($LMRId);

        $LMRClientId = trim($tempRespArray['clientId']);
        $LMRResponseId = trim($tempRespArray['LMRResponseId']);
        $LMRExecutiveId = trim($tempRespArray['FBRID']);
        $brokerNumber = trim($tempRespArray['brokerNumber']);

        $secondaryBrokerNumber = trim($tempRespArray['secondaryBrokerNumber']);
        $fileActiveStatus = trim($tempRespArray['fileActiveStatus']);
        $projectName = trim($tempRespArray['projectName']);

        $trialPaymentDate1 = Dates::formatDateWithRE($tempRespArray['trialPaymentDate1'] ?? null, 'YMD', 'm/d/Y');
        $recordDate = trim($tempRespArray['recordDate'] ?? null);
        $dateObtained = trim($tempRespArray['dateObtained'] ?? null);
        $salesDate = trim($tempRespArray['salesDate'] ?? null);
        $borrowerFName = stripslashes(trim($tempRespArray['borrowerName'] ?? null));
        $borrowerLName = stripslashes(trim($tempRespArray['borrowerLName'] ?? null));
        $borrowercellNumber = Strings::formatPhoneNumber(Strings::cleanPhoneNo(trim($tempRespArray['cellNumber'] ?? null)));
        $borPhone = Strings::formatPhoneNumber(Strings::cleanPhoneNo(trim($tempRespArray['phoneNumber'] ?? null)));
        $lender1Name = trim($tempRespArray['servicer1'] ?? null);
        $lender2Name = trim($tempRespArray['servicer2'] ?? null);
        $loanNumber = trim($tempRespArray['loanNumber'] ?? null);
        $loanNumber2 = trim($tempRespArray['loanNumber2'] ?? null);
        $borrowerEmail = trim($tempRespArray['borrowerEmail'] ?? null);
        $coBorrowerName = stripslashes(trim($tempRespArray['coBorrowerFName'] . ' ' . trim($tempRespArray['coBorrowerLName']) ?? null));
        $isCoBorrower = trim($tempRespArray['isCoBorrower'] ?? null);
        $requiredAdditionalCond['isCoBorrower'] = $isCoBorrower;
        $propertyAddress = trim($tempRespArray['propertyAddress'] ?? null);

        $primaryPropertyInfo = Property::$primaryPropertyInfo;

        $propertyCity = $primaryPropertyInfo ? $primaryPropertyInfo->propertyCity : null;
        $propertyState = $primaryPropertyInfo ? $primaryPropertyInfo->propertyState : null;
        $requiredAdditionalCond['propertyState'] = $propertyState;

        $propertyZip = $primaryPropertyInfo ? $primaryPropertyInfo->propertyZipCode : null;
        $propertyCounty = $primaryPropertyInfo ? $primaryPropertyInfo->propertyCounty : null;

        $propertyCharacteristics = $primaryPropertyInfo ? $primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId() : null;

        $propertyType = $propertyCharacteristics ? $propertyCharacteristics->propertyType : null;
        $propertySqFt = $propertyCharacteristics ? $propertyCharacteristics->propertySqFt : null;
        $yearBuilt = $propertyCharacteristics ? $propertyCharacteristics->propertyYearBuilt : null;
        $bedrooms = $propertyCharacteristics ? $propertyCharacteristics->propertyNumberOfBedRooms : null;
        $bathrooms = $propertyCharacteristics ? $propertyCharacteristics->propertyNumberOfBathRooms : null;

        $propertyDetails = $primaryPropertyInfo ? $primaryPropertyInfo->getTblPropertiesDetails_by_propertyId() : null;

        $propertyURLLink1 = $propertyDetails ? $propertyDetails->propertyURLLink1 : null;
        $propertyURLLink2 = $propertyDetails ? $propertyDetails->propertyURLLink2 : null;

        $propertyURLLink = Strings::arrayToString([$propertyURLLink1, $propertyURLLink2]);
        $propertyCondition = $propertyDetails ? $propertyDetails->propertyCondition : null;

        $propertyAppraisalDetails = $primaryPropertyInfo && $primaryPropertyInfo->getTblPropertiesAppraiserDetails_by_propertyId() ? $primaryPropertyInfo->getTblPropertiesAppraiserDetails_by_propertyId()[0] : null;
        $propertyFloodCertificatesDetails = $primaryPropertyInfo && $primaryPropertyInfo->getTblPropertiesFloodCertificates_by_propertyId() ? $primaryPropertyInfo->getTblPropertiesFloodCertificates_by_propertyId()[0] : null;
        if ($propertyFloodCertificatesDetails) {

            $this->propertyFloodZone = Property::$propertyFloodZoneList[$propertyFloodCertificatesDetails->propertyFloodZone] ?? '';
        }
        if ($propertyAppraisalDetails) {
            $this->propertyAppraisalAsIsValue = $propertyAppraisalDetails->propertyAppraisalAsIsValue;
            $this->propertyAppraisalRehabbedValue = $propertyAppraisalDetails->propertyAppraisalRehabbedValue;
            $this->propertyAppraisalMonthlyRent = $propertyAppraisalDetails->propertyAppraisalMonthlyRent;
            $this->propertyAppraisalJobTypes = $propertyAppraisalDetails->propertyAppraisalJobTypes;
            $this->propertyAppraisalDateObtained = Dates::formatDateWithRE($propertyAppraisalDetails->propertyAppraisalDateObtained, 'YMD', 'm/d/Y');
            $this->propertyAppraisalRequestedReturnDate = Dates::formatDateWithRE($propertyAppraisalDetails->propertyAppraisalRequestedReturnDate, 'YMD', 'm/d/Y');
            $this->propertyAppraisalIsRushOrder = (!is_null($propertyAppraisalDetails->propertyAppraisalIsRushOrder) ? Strings::booleanTextVal($propertyAppraisalDetails->propertyAppraisalIsRushOrder) : null);
            $this->propertyAppraisalOrderDate = Dates::formatDateWithRE($propertyAppraisalDetails->propertyAppraisalOrderDate, 'YMD', 'm/d/Y');
            $this->propertyAppraisalComments = $propertyAppraisalDetails->propertyAppraisalComments;
            $this->propertyAppraisalEffectiveDate = Dates::formatDateWithRE($propertyAppraisalDetails->propertyAppraisalEffectiveDate, 'YMD', 'm/d/Y');
            $this->primaryAppraisalEcoaDeliveryDate = Dates::formatDateWithRE($propertyAppraisalDetails->primaryAppraisalEcoaDeliveryDate, 'YMD', 'm/d/Y');
            $this->propertyAppraisalInspectionDate = Dates::formatDateWithRE($propertyAppraisalDetails->propertyAppraisalInspectionDate, 'YMD', 'm/d/Y');
            $this->propertyAppraisalExpectedDeliveryDate = Dates::formatDateWithRE($propertyAppraisalDetails->propertyAppraisalExpectedDeliveryDate, 'YMD', 'm/d/Y');
            $this->propertyAppraisalExpectedDeliveryDelay = $propertyAppraisalDetails->propertyAppraisalExpectedDeliveryDelay;

            $this->propertyAppraisalSupplementalProductFormType1 = $propertyAppraisalDetails->propertyAppraisalSupplementalProductFormType1;
            $this->propertyAppraisalSupplementalProductFormType2 = $propertyAppraisalDetails->propertyAppraisalSupplementalProductFormType2;
            $this->propertyAppraisalSupplementalProductFormType3 = $propertyAppraisalDetails->propertyAppraisalSupplementalProductFormType3;
            $this->propertyAppraisalSupplementalProductEffectiveDate1 = Dates::formatDateWithRE($propertyAppraisalDetails->propertyAppraisalSupplementalProductEffectiveDate1, 'YMD', 'm/d/Y');
            $this->propertyAppraisalSupplementalProductEffectiveDate2 = Dates::formatDateWithRE($propertyAppraisalDetails->propertyAppraisalSupplementalProductEffectiveDate2, 'YMD', 'm/d/Y');
            $this->propertyAppraisalSupplementalProductEffectiveDate3 = Dates::formatDateWithRE($propertyAppraisalDetails->propertyAppraisalSupplementalProductEffectiveDate3, 'YMD', 'm/d/Y');
            $this->propertyAppraisalSupplementalProductEcoaADeliveryDate1 = Dates::formatDateWithRE($propertyAppraisalDetails->propertyAppraisalSupplementalProductEcoaADeliveryDate1, 'YMD', 'm/d/Y');
            $this->propertyAppraisalSupplementalProductEcoaADeliveryDate2 = Dates::formatDateWithRE($propertyAppraisalDetails->propertyAppraisalSupplementalProductEcoaADeliveryDate2, 'YMD', 'm/d/Y');
            $this->propertyAppraisalSupplementalProductEcoaADeliveryDate3 = Dates::formatDateWithRE($propertyAppraisalDetails->propertyAppraisalSupplementalProductEcoaADeliveryDate3, 'YMD', 'm/d/Y');
        }

        $filePCID = trim($tempRespArray['FPCID']);

        $workNumber = Strings::formatPhoneNumber(trim($tempRespArray['workNumber']));
        $lien2Payment = trim($tempRespArray['lien2Payment']);
        $lien2Amount = trim($tempRespArray['lien2Amount']);
        $lien1Amount = trim($tempRespArray['lien1Amount']);
        $lien1Payment = trim($tempRespArray['lien1Payment']);
        $lien1Rate = trim($tempRespArray['lien1Rate']);
        $lien1Terms = trim($tempRespArray['lien1Terms']);
        $lien2Rate = trim($tempRespArray['lien2Rate']);
        $noOfMonthsBehind1 = trim($tempRespArray['noOfMonthsBehind1']);
        $noOfMonthsBehind2 = trim($tempRespArray['noOfMonthsBehind2']);
        $fileNumber = trim($tempRespArray['fileNumber']);
        $priorityLevel = Strings::processString(trim($tempRespArray['priorityLevel']));
        $lastUpdatedDate = trim($tempRespArray['lastUpdatedDate']);
        $lenderSubmissionDate = trim($tempRespArray['lenderSubmissionDate']);
        $receivedDate = trim($tempRespArray['receivedDate']);
        $closedDate = trim($tempRespArray['closedDate']);
        $borrowerCallBack = trim($tempRespArray['borrowerCallBack']);
        $lenderCallBack = trim($tempRespArray['lenderCallBack']);
        $mortgageOwner1 = trim($tempRespArray['mortgageOwner1']);
        $mortgageOwner2 = trim($tempRespArray['mortgageOwner2']);
        $loanType = trim($tempRespArray['loanType']);
        $loanType2 = trim($tempRespArray['loanType2']);
        $lien1LPMade = trim($tempRespArray['lien1LPMade']);
        $lien2LPMade = trim($tempRespArray['lien2LPMade']);
        $occupancy = trim($tempRespArray['occupancy']);
        $requiredAdditionalCond['propertyType'] = $propertyType;
        $homeValue = trim($tempRespArray['homeValue']);
        $lien1BalanceDue = trim($tempRespArray['lien1BalanceDue']);
        $lien2BalanceDue = trim($tempRespArray['lien2BalanceDue']);
        $HAFADate = trim($tempRespArray['HAFADate']);
        $oldFPCID = trim($tempRespArray['oldFPCID']);
        $leadSource = trim($tempRespArray['leadSource']);

        $entityName = '';
        $entityType = '';
        $entityCity = '';
        $entityState = '';
        $entityStateOfFormation = $entityPropertyOwnerShip = $avgMonthlyCreditcardSale = $avgTotalMonthlySale =
        $annualGrossProfit = $ordinaryBusinessIncome = $noOfEmployees = '';
        $entityInfo = '';
        $presentCity = $presentState = '';

        if (count($file2Info) > 0) {
            if (array_key_exists($LMRId, $file2Info)) { /* Paid Status - Synergy Attorney Services - Sep 6, 2016 */
                $synergyBillPaidStatus = trim($file2Info[$LMRId]['paidStatus']);
                $presentCity = trim($file2Info[$LMRId]['presentCity']);
                $presentState = trim($file2Info[$LMRId]['presentStateName']);
            }
        }

        if (array_key_exists($LMRClientId, $clientInfo)) {
            $sendMarketingEmail = $clientInfo[$LMRClientId]['sendMarketingEmail'];                                                          // | Get Send Marketing Email(Yes / No).
        }

        if (array_key_exists($LMRId, $fileLOAssetsInfo)) {
            $networthOfBusinessOwned = $fileLOAssetsInfo[$LMRId]['networthOfBusinessOwned'];                                                // | Get Net Worth.
        }

        $rehabCost = 0;
        if (array_key_exists($LMRId, $fileHMLOInfo)) {
            $borCreditScoreRange = $fileHMLOInfo[$LMRId]['borCreditScoreRange'];                                                   // | Get Borrower Credit Score.
            $midFico = $fileHMLOInfo[$LMRId]['midFicoScore'];
            $borExperianScore = $fileHMLOInfo[$LMRId]['borExperianScore'];                                                      // | Get Borrower Experian Score.
            $borTransunionScore = $fileHMLOInfo[$LMRId]['borTransunionScore'];                                                    // | Get Borrower Transunion Score.
            $borEquifaxScore = $fileHMLOInfo[$LMRId]['borEquifaxScore'];                                                       // | Get Borrower Equifax Score.
            $rehabCost = $fileHMLOInfo[$LMRId]['rehabCost'];
            $requiredAdditionalCond['borrowerCreditScore'] = $fileHMLOInfo[$LMRId]['borCreditScoreRange'];
            $targetClosingDate = Dates::formatDateWithRE($fileHMLOInfo[$LMRId]['targetClosingDate'], 'YMD', 'm/d/Y');
        }
        if (array_key_exists($LMRId, $fileHMLOExperienceInfo)) {
            $borNoOfREPropertiesCompleted = $fileHMLOExperienceInfo[$LMRId]['borNoOfREPropertiesCompleted'];                                // | # of prop completed for Investment.
            $borRehabPropCompleted = $fileHMLOExperienceInfo[$LMRId]['borRehabPropCompleted'];                                       // | # of prop completed for Construction.
            $borNoOfOwnProp = $fileHMLOExperienceInfo[$LMRId]['borNoOfOwnProp'];                                              // | # of prop for investment.
        }
        if (array_key_exists($LMRId, $fileHMLOBackGroundInfo)) {
            $isBorUSCitizen = $fileHMLOBackGroundInfo[$LMRId]['isBorUSCitizen'];                                              // | Check US Citizen
            $requiredAdditionalCond['isBorUSCitizen'] = $isBorUSCitizen;
        }
        /**
         * Description : Get the Entity Info
         * Date        : Mar 15, 2018
         * Author      : Venky
         **/

        if (array_key_exists($LMRId, $fileHMLOEntityInfo)) {
            $entityName = $fileHMLOEntityInfo[$LMRId]['entityName'];
            $entityType = $fileHMLOEntityInfo[$LMRId]['entityType'];
            $requiredAdditionalCond['entityType'] = $entityType;

            $entityCity = $fileHMLOEntityInfo[$LMRId]['entityCity'];
            $entityState = $fileHMLOEntityInfo[$LMRId]['entityState'];
            $requiredAdditionalCond['entityState'] = $entityState;
            $entityStateOfFormation = $fileHMLOEntityInfo[$LMRId]['entityStateOfFormation'];
            $entityPropertyOwnerShip = $fileHMLOEntityInfo[$LMRId]['entityPropertyOwnerShip'];
            $avgMonthlyCreditcardSale = $fileHMLOEntityInfo[$LMRId]['avgMonthlyCreditcardSale'];
            $avgTotalMonthlySale = $fileHMLOEntityInfo[$LMRId]['avgTotalMonthlySale'];
            $annualGrossProfit = $fileHMLOEntityInfo[$LMRId]['annualGrossProfit'];
            $ordinaryBusinessIncome = $fileHMLOEntityInfo[$LMRId]['ordinaryBusinessIncome'];
            $noOfEmployees = $fileHMLOEntityInfo[$LMRId]['noOfEmployees'];
        }


        $fileModuleInforray = [];
        if (array_key_exists($LMRId, $fileModuleTypeArray)) $fileModuleInforray = $fileModuleTypeArray[$LMRId];

        $fileSelectedModuleCode = [];
        if (count($fileModuleInforray) > 0) {
            $fileSelectedModuleCode = explode(',', Arrays::implode2dArray(', ', $fileModuleInforray, 'moduleCode'));
        }

        //important for Automation
        $fileTypesTxt = '';
        if (count($fileSelectedModuleCode) > 0) {
            $fileTypesTxt = implode(',', $fileSelectedModuleCode);
        }

        if ($PCID == 2 || $PCID == 1495 || $PCID == 820) {
            $workNumber = Strings::formatPhoneNumberWithoutSpace(trim($tempRespArray['workNumber']));
            $borrowercellNumber = Strings::formatPhoneNumberWithoutSpace(Strings::cleanPhoneNo(trim($tempRespArray['cellNumber'])));
            $borPhone = Strings::formatPhoneNumberWithoutSpace(Strings::cleanPhoneNo(trim($tempRespArray['phoneNumber'])));
        }

        $tempFileClientTypeArray = [];
        if (count($fileClientTypeArray) > 0) {
            if (array_key_exists($LMRId, $fileClientTypeArray)) {
                $tempFileClientTypeArray = $fileClientTypeArray[$LMRId];
            }
        }
        $fileLoanProgramsSTCode = [];
        $clArray = [];
        $serviceReq = '';
        $tempServiceReqTxt = [];
        if (count($tempFileClientTypeArray) > 0) {
            $clArray = explode(',', Arrays::implode2dArray(',', $tempFileClientTypeArray, 'ClientType'));

            foreach ($tempFileClientTypeArray as $item) {
                $fileLoanProgramsSTCode[] = $item['ClientType'];
            }
        }

        if ($userRole == 'Super' && $PCID == 0) {
            for ($cl = 0; $cl < count($clArray); $cl++) {
                if (array_key_exists(trim($clArray[$cl]), $glLMRClientTypeArray)) {
                    $tempServiceReqTxt[$glLMRClientTypeArray[trim($clArray[$cl])]] = trim($glLMRClientTypeArray[trim($clArray[$cl])]);
                }
            }
        } else {
            for ($j = 0; $j < count($PCClientTypeInfoArray); $j++) {
                for ($cl = 0; $cl < count($clArray); $cl++) {
                    if (trim($clArray[$cl]) == trim($PCClientTypeInfoArray[$j]['LMRClientType'])) {
                        $tempServiceReqTxt[trim($PCClientTypeInfoArray[$j]['serviceType'])] = trim($PCClientTypeInfoArray[$j]['STCode']);
                    }
                }
            }
        }

        /** If only selected the file Type = Short sale open the "Dashboard" tab on Sep 19, 2016 Start **/
        /** If only selected the file Type = Loan Servicing open the "Summary" tab on Sep 28, 2016 Start **/
        /** If only selected the file Type = Hard Money LOS open the "LI" tab on Nov 21, 2016 Start **/

        $isFileSS = 0;
        $isFileLSR = 0;
        $isFileLI = 0;
        $thisFileStatusHistory = [];
        if (count($clArray) > 0) {
            if (in_array('SS', $clArray)) {
                $isFileSS = 1;
            }
            if (in_array('LSR', $clArray)) {
                $isFileLSR = 1;
            }
            if (in_array('LI', $clArray)) {
                $isFileLI = 1;
            }
        }

        if (count($tempServiceReqTxt) > 0) {
            $serviceReq = implode(', ', array_keys($tempServiceReqTxt));
        }

        $tempSubStatusArray = [];
        if (count($substatusInfoArray) > 0) {
            if (array_key_exists($LMRId, $substatusInfoArray)) {
                $tempSubStatusArray = $substatusInfoArray[$LMRId];
            }
            for ($s = 0; $s < count($tempSubStatusArray); $s++) {
                if ($s > 0) {
                    $subStatus .= ', ';
                }
                $subStatus .= trim($tempSubStatusArray[$s]['substatus']);
            }
        }

        $LMRInternalLoanProgram = '';
        $LMRInternalLoanProgramArray = [];
        $LMRInternalLoanProgramSTCode = [];
        if (isset($LMRInternalLoanProgramInfoArray[$LMRId])) {
            foreach ($LMRInternalLoanProgramInfoArray[$LMRId] as $eachInternalLoanName) {
                $LMRInternalLoanProgramArray[] = $eachInternalLoanName['serviceType'];
                $LMRInternalLoanProgramSTCode[] = $eachInternalLoanName['STCode'];
            }
            if (count($LMRInternalLoanProgramArray) > 0) {
                $LMRInternalLoanProgram = implode(',<br>', $LMRInternalLoanProgramArray);
            }
        }
        $AdditionalLoanProgramSTCode = [];

        if (isset($LMRadditionalLoanprogramsInfoArray[$LMRId])) {
            foreach ($LMRadditionalLoanprogramsInfoArray[$LMRId] as $eachAdditionalLoanProgram) {
                $AdditionalLoanProgramSTCode[] = $eachAdditionalLoanProgram['STCode'];
            }
        }
        $requiredAdditionalCond['WfStepServices'] = (array_merge($fileLoanProgramsSTCode, $LMRInternalLoanProgramSTCode, $AdditionalLoanProgramSTCode));


        if (array_key_exists($mortgageOwner1, $glMortgageOwnerArray)) {
            $mortgageOwner1 = $glMortgageOwnerArray[$mortgageOwner1];
        } else {
            $mortgageOwner1 = '';
        }
        if (array_key_exists($mortgageOwner2, $glMortgageOwnerArray)) {
            $mortgageOwner2 = $glMortgageOwnerArray[$mortgageOwner2];
        } else {
            $mortgageOwner2 = '';
        }

        $AVM1 = '';
        $titlePhoneNumber = '';

        if (array_key_exists($propertyType, $GpropertyTypeNumbArray)) {
            $propertyType = $GpropertyTypeNumbArray[$propertyType];
        } else {
            $propertyType = '';
        }

        $appraiser1Value = '';
        if (array_key_exists($LMRId, $listingRealtorInfo)) {
            $costBasis = trim($listingRealtorInfo[$LMRId]['costBasis']);
            $appraiser1 = trim($listingRealtorInfo[$LMRId]['appraiser1']);
            $appraiser1Value = trim($listingRealtorInfo[$LMRId]['appraiser1Value']);
            $BPO1 = trim($listingRealtorInfo[$LMRId]['BPO1Value']);
            $rehabValue = trim($listingRealtorInfo[$LMRId]['rehabValue']);
            $assessedValue = trim($listingRealtorInfo[$LMRId]['assessedValue']);
            $AVM1 = trim($listingRealtorInfo[$LMRId]['AVM1Value']);
            $titlePhoneNumber = Strings::formatPhoneNumber(trim($listingRealtorInfo[$LMRId]['titleCompanyPhoneNumber']));
            $dateObtained = Dates::formatDateWithRE(trim($listingRealtorInfo[$LMRId]['dateObtained']), 'YMD', 'm/d/Y');
            $appraisal1OrderDate = Dates::formatDateWithRE(trim($listingRealtorInfo[$LMRId]['appraisal1OrderDate']), 'YMD', 'm/d/Y');
        }

        if (array_key_exists($LMRId, $fileVelocityInfo)) {
            $fileVelocity = $fileVelocityInfo[$LMRId]['noOfDays'];
        }
        if (isset($fileStatusHistory[$LMRId])) {
            $thisFileStatusHistory = $fileStatusHistory[$LMRId];
        }

        $tempAssignedBoStaff = [];
        if (array_key_exists($LMRId, $assignedBoStaff)) {
            $tempAssignedBoStaff = $assignedBoStaff[$LMRId];
        }

        $assignedEmpInfo = '';
        foreach ($tempAssignedBoStaff as $item) {
            $assignedEmpName = trim($item['processorName']);
            $assignedEmpRole = trim($item['role']);
            $tollFree = Strings::formatPhoneNumber(trim($item['tollFree']));
            $cellNumber = Strings::formatPhoneNumber(trim($item['cellNumber']));
            $fax = Strings::formatPhoneNumber(trim($item['fax']));
            $email = trim($item['email']);

            $appStr = '';
            $tempEmpInfo = '';

            if (trim($email)) {
                $tempEmpInfo = '<b>Email</b>: ' . $email;
                $appStr = '<br>';
            }
            if (trim($tollFree)) {
                $tempEmpInfo .= $appStr . '<b>Phone</b>: ' . $tollFree;
                $appStr = '<br>';
            }
            if (trim($cellNumber) && !glCustomJobForProcessingCompany::hideAssignedEmployeeCellNumber($PCID) ) {
                $tempEmpInfo .= $appStr . '<b>Cell</b>: ' . $cellNumber;
                $appStr = '<br>';
            }

            if (trim($fax)) {
                $tempEmpInfo .= $appStr . '<b>Fax</b>: ' . $fax;
            }

            if ($assignedEmpName) {
                $assignedEmpInfo .= '<tr>';
                $assignedEmpInfo .= "<td class=\"px-2\"><b> - " . $assignedEmpName . ' (' . $assignedEmpRole . ')</b><br>' . $tempEmpInfo . '</td>';
                $assignedEmpInfo .= '</tr>';
            }
        }

        $assignedEmpInfo = base64_encode($assignedEmpInfo);
        $assignedCFPBAuditors = '';

        if (($fileType == 'CFPB' && $allowCFPBAuditing == 1) || $userRole == 'Auditor Manager' || ($fileType == 'CFPB' && $userRole == 'Super')) {
            if (count($assignedCFPBAuditorInfo) > 0) {
                $tempAssignedCFPBAuditorInfo = [];
                if (array_key_exists($LMRId, $assignedCFPBAuditorInfo)) $tempAssignedCFPBAuditorInfo = $assignedCFPBAuditorInfo[$LMRId];

                foreach ($tempAssignedCFPBAuditorInfo as $assigned) {

                    $assignedEmpName = trim($assigned['processorName']);
                    $assignedEmpRole = trim($assigned['role']);
                    $tollFree = Strings::formatPhoneNumber(trim($assigned['tollFree']));
                    $cellNumber = Strings::formatPhoneNumber(trim($assigned['cellNumber']));
                    $fax = Strings::formatPhoneNumber(trim($assigned['fax']));
                    $email = trim($assigned['email']);

                    $appStr = '';
                    $tempEmpInfo = '';
                    if (trim($tollFree)) {
                        $tempEmpInfo = '<b>Email</b>: ' . $email;
                        $appStr = '<br>';
                    }
                    if (trim($tollFree)) {
                        $tempEmpInfo .= $appStr . '<b>Phone</b>: ' . $tollFree;
                        $appStr = '<br>';
                    }
                    if (trim($cellNumber)) {
                        $tempEmpInfo .= $appStr . '<b>Cell</b>: ' . $cellNumber;
                        $appStr = '<br>';
                    }

                    if (trim($fax)) {
                        $tempEmpInfo .= $appStr . '<b>Fax</b>: ' . $fax;
                    }

                    if ($assignedEmpName) {
                        $assignedCFPBAuditors .= "<tr class=\"bbg\">";
                        if (trim($tempEmpInfo)) {
                            $assignedCFPBAuditors .= "<td style='width: 5%'><div class=\"pad2 with-children-tip\"><a class=\"fa fa-info-circle fa-2x \" style=\"text-decoration:none;\" title=\"" . $tempEmpInfo . "\">&nbsp;</a></div></td>";
                        } else {
                            $assignedCFPBAuditors .= "<td style='width: 5%'>&nbsp;</td>";
                        }
                        $assignedCFPBAuditors .= "<td class=\"force-wrap w150\">" . $assignedEmpName . ' (<b>' . $assignedEmpRole . '</b>)</td>';
                        $assignedCFPBAuditors .= '</tr>';
                    }
                }
            }
        }

        $presentOccupancyStatus = '';
        if (array_key_exists($LMRId, $filePropInfo)) {
            $presentOccupancyStatus = trim($filePropInfo[$LMRId]['isHouseProperty']);
        }

        $isHouseProperty = $presentOccupancyStatus;
        $requiredAdditionalCond['isHouseProperty'] = $isHouseProperty;


        $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
        $ipArray['outputZone'] = $userTimeZone;
        $ipArray['inputTime'] = $lastUpdatedDate;
        $lastUpdatedDate = Dates::timeZoneConversion($ipArray);

        $recordDate = Dates::formatDateWithRE($recordDate, 'YMD', 'm/d/Y');
        $lastUpdatedDate = Dates::formatDateWithRE($lastUpdatedDate, 'YMD_HMS', 'm/d/Y h:i A');
        $salesDate = Dates::formatDateWithRE($salesDate, 'YMD', 'm/d/Y');
        $lenderSubmissionDate = Dates::formatDateWithRE($lenderSubmissionDate, 'YMD', 'm/d/Y');
        $receivedDate = Dates::formatDateWithRE($receivedDate, 'YMD', 'm/d/Y');
        $closedDate = Dates::formatDateWithRE($closedDate, 'YMD', 'm/d/Y');
        $borrowerCallBack = Dates::formatDateWithRE($borrowerCallBack, 'YMD', 'm/d/Y');
        $lenderCallBack = Dates::formatDateWithRE($lenderCallBack, 'YMD', 'm/d/Y');
        $lien1LPMade = Dates::formatDateWithRE($lien1LPMade, 'YMD', 'm/d/Y');
        $lien2LPMade = Dates::formatDateWithRE($lien2LPMade, 'YMD', 'm/d/Y');
        $HAFADate = Dates::formatDateWithRE($HAFADate, 'YMD', 'm/d/Y');


        if ($lastUpdatedDate) {
            $lastUpdatedDate .= ' - ' . $userTimeZone;
        }

        $allowAgentToSeeFile = 0;
        if (count($fileBranchInfo) > 0) {
            if (array_key_exists($LMRExecutiveId, $fileBranchInfo)) {
                $RESTFee = trim($fileBranchInfo[$LMRExecutiveId]['RESTFee']);
                $branchName = trim($fileBranchInfo[$LMRExecutiveId]['LMRExecutive']);
                $allowAgentToSeeFile = trim($fileBranchInfo[$LMRExecutiveId]['agentFileAccess']);
            }
        }
        if (count($fileAgentInfo) > 0) {
            if (array_key_exists($brokerNumber, $fileAgentInfo)) {
                $brokerFName = trim($fileAgentInfo[$brokerNumber]['brokerFName']);
                $brokerLName = trim($fileAgentInfo[$brokerNumber]['brokerLName']);
                $brokerPhone = Strings::formatPhoneNumber(trim($fileAgentInfo[$brokerNumber]['phoneNumber']));
                $brokerEmail = trim($fileAgentInfo[$brokerNumber]['email']);
            }
        }

        $lenderName = '';
        if (count($lenderFileContactInfo) > 0) {
            if (array_key_exists($LMRId, $lenderFileContactInfo)) {
                $lenderCompanyName = trim($lenderFileContactInfo[$LMRId]['companyName']);
                $lenderName = ucwords(trim($lenderFileContactInfo[$LMRId]['contactName']) . ' ' . trim($lenderFileContactInfo[$LMRId]['contactLName']));
                if ($lenderCompanyName) {
                    $lenderName .= '<br> <b>Company Name :</b> ' . $lenderCompanyName;
                }
            }

        }

        $trusteeName = '';
        if (count($trusteeFileContactInfo) > 0) {
            if (array_key_exists($LMRId, $trusteeFileContactInfo)) {
                $trusteeCompanyName = trim($trusteeFileContactInfo[$LMRId]['companyName']);
                $trusteeName = ucwords(trim($trusteeFileContactInfo[$LMRId]['contactName']) . ' ' . trim($trusteeFileContactInfo[$LMRId]['contactLName']));
                if ($trusteeCompanyName) {
                    $trusteeName .= '<br> <b>Company Name :</b> ' . $trusteeCompanyName;
                }
            }

        }

        $servicerName = '';
        if (count($servicerFileContactInfo) > 0) {
            if (array_key_exists($LMRId, $servicerFileContactInfo)) {
                $servicerCompanyName = trim($servicerFileContactInfo[$LMRId]['companyName']);
                $servicerName = ucwords(trim($servicerFileContactInfo[$LMRId]['contactName']) . ' ' . trim($servicerFileContactInfo[$LMRId]['contactLName']));
                if ($servicerCompanyName) {
                    $servicerName .= '<br> <b>Company Name :</b> ' . $servicerCompanyName;
                }
            }

        }

        $investorContactDetails = '';
        if (count($investorFileContactInfo) > 0) {
            if (array_key_exists($LMRId, $investorFileContactInfo)) {
                foreach ($investorFileContactInfo[$LMRId] as $inv => $investor) {
                    if ($inv > 0) {
                        $investorContactDetails .= '<br>';
                    }
                    $investorContactDetails .= '<b>Investor ' . ($inv + 1) . ' Details :</b><br>';
                    $investorCompanyName = trim($investor['companyName']);
                    $investorName = ucwords(trim($investor['contactName']) . ' ' . trim($investor['contactLName']));
                    $investorContactDetails .= $investorName;
                    if ($investorCompanyName) {
                        $investorContactDetails .= '<br>Company Name :' . $investorCompanyName;
                    }
                }

            }

        }


        $secondaryBrokerFName = '';
        $secondaryBrokerLName = '';
        $secondaryBrokerPhone = '';
        $secondaryBrokerEmail = '';

        if (count($fileLoanOfficerInfo) > 0) {
            if (array_key_exists($secondaryBrokerNumber, $fileLoanOfficerInfo)) {
                $secondaryBrokerFName = trim($fileLoanOfficerInfo[$secondaryBrokerNumber]['brokerFName']);
                $secondaryBrokerLName = trim($fileLoanOfficerInfo[$secondaryBrokerNumber]['brokerLName']);
                $secondaryBrokerPhone = Strings::formatPhoneNumber(trim($fileLoanOfficerInfo[$secondaryBrokerNumber]['phoneNumber']));
                $secondaryBrokerEmail = trim($fileLoanOfficerInfo[$secondaryBrokerNumber]['email']);
            }
        }

        $borrowerName = ucwords($borrowerLName . ' ' . $borrowerFName);
        $brokerName = ucwords($brokerFName . ' ' . $brokerLName);
        $secondaryBrokerName = ucwords($secondaryBrokerFName . ' ' . $secondaryBrokerLName);
        $branchName = ucwords($branchName);
        if ($brokerPhone) {
            $brokerName .= '<br> <b>Ph #:</b> ' . $brokerPhone;
        }
        if ($brokerEmail) {
            $brokerName .= "<br> <b>Email:</b> <span class=\"force-wrap\"><a href=\"mailto:" . $brokerEmail . "\">" . $brokerEmail . '</a></span>';
        }
        if (trim($brokerEmail) == $PCID . '@dummyAgentemail.com') {
            $brokerName = '';
        }

        if ($secondaryBrokerPhone) {
            $secondaryBrokerName .= '<br> <b>Ph #:</b> ' . $secondaryBrokerPhone;
        }
        if ($secondaryBrokerEmail) {
            $secondaryBrokerName .= "<br> <b>Email:</b> <span class=\"force-wrap\"><a href=\"mailto:" . $secondaryBrokerEmail . "\">" . $secondaryBrokerEmail . '</a></span>';
        }

        if (array_key_exists($LMRId, $LenderInfo1Array)) $tempLenderInfo1Array = $LenderInfo1Array[$LMRId];
        if (array_key_exists($LMRId, $proposalInfo)) $proposalInfoArray = $proposalInfo[$LMRId];
        if (count($tempLenderInfo1Array) > 0) {

            $lender1PhoneNo = Strings::formatPhoneNumber(trim($tempLenderInfo1Array['phoneNumber']));
            $lender1Fax = Strings::formatPhoneNumber(trim($tempLenderInfo1Array['fax']));
            $lender1Addr = trim($tempLenderInfo1Array['address1']);
            $lender1City = trim($tempLenderInfo1Array['city']);
            $lender1State = trim($tempLenderInfo1Array['state']);
            $lender1Zip = trim($tempLenderInfo1Array['zipCode']);

            $appStr = '';
            $lender1Address = '';
            if (trim($lender1Addr)) {
                $lender1Address = $lender1Addr;
                $appStr = ', ';
            }
            if (trim($lender1City)) {
                $lender1Address .= $appStr . $lender1City;
                $appStr = ', ';
            }
            if (trim($lender1State)) {
                $lender1Address .= $appStr . $lender1State;
                $appStr = ', ';
            }
            if (trim($lender1Zip)) {
                $lender1Address .= $appStr . $lender1Zip;
            }

            $appStr = '';
            $tempLender1Info = '';
            if (trim($lender1PhoneNo)) {
                $tempLender1Info = '<b>Phone</b>: ' . $lender1PhoneNo;
                $appStr = '<br>';
            }
            if (trim($lender1Fax)) {
                $tempLender1Info .= $appStr . '<b>Fax</b>: ' . $lender1Fax;
                $appStr = '<br>';
            }
            if (trim($lender1Address)) {
                $tempLender1Info .= $appStr . '<b>Address</b>: ' . $lender1Address;
            }
            if (trim($tempLender1Info)) $lender1Info = '<b>Contact info defaulted by The Loan Post</b><br>' . $tempLender1Info;

        }
        if (count($proposalInfoArray) > 0) {

            $lien1Bank1RepName = trim($proposalInfoArray['lien1Bank1RepName']);
            $lien1Bank1RepDept = trim($proposalInfoArray['lien1Bank1RepDept']);
            $lien1Bank1RepPhoneNo = Strings::formatPhoneNumber(trim($proposalInfoArray['lien1Bank1RepPhoneNo']));
            $lien1Bank1RepFax = Strings::formatPhoneNumber(trim($proposalInfoArray['lien1Bank1RepFax']));
            $lien1Bank1RepEmail = trim($proposalInfoArray['lien1Bank1RepEmail']);

            $appStr = '';
            $tempRepInfo = '';
            if (trim($lien1Bank1RepName)) {
                $tempRepInfo = '<b>Rep Name</b>: ' . $lien1Bank1RepName;
                $appStr = '<br>';
            }
            if (trim($lien1Bank1RepDept)) {
                $tempRepInfo .= $appStr . '<b>Dept</b>: ' . $lien1Bank1RepDept;
                $appStr = '<br>';
            }
            if (trim($lien1Bank1RepPhoneNo)) {
                $tempRepInfo .= $appStr . '<b>Direct Phone</b>: ' . $lien1Bank1RepPhoneNo;
                $appStr = '<br>';
            }
            if (trim($lien1Bank1RepFax)) {
                $tempRepInfo .= $appStr . '<b>Fax</b>: ' . $lien1Bank1RepFax;
                $appStr = '<br>';
            }
            if (trim($lien1Bank1RepEmail)) {
                $tempRepInfo .= $appStr . '<b>Email</b>: ' . $lien1Bank1RepEmail;
            }

            if (trim($tempRepInfo)) {
                if (trim($lender1Info)) {
                    $lender1Info .= '<br>';
                }
                $lender1Info .= '<b>Contact info entered by your staff inside client file</b><br>' . $tempRepInfo;
            }
        }

        if ($userRole == 'REST' || $fileType == 2 || $fileType == 4) {
            $orderStatus = trim($tempRespArray['orderStatus']);
            $paymentStatus = trim($tempRespArray['paymentStatus']);
            $price = trim($tempRespArray['price']);
            if ($userRole == 'REST' || $fileType == 2) {
                $SubmissionDate = trim($tempRespArray['SubmissionDate']);
                if (Dates::IsEmpty($SubmissionDate)) {
                    $SubmissionDate = '';
                } else {
                    $SubmissionDate = Dates::formatDateWithRE(date('Y-m-d', strtotime($SubmissionDate)), 'YMD', 'm/d/Y');
                }
            }
        }

        if ($userRole == 'REST' || $fileType == 2 || $fileType == 4 || $userRole == 'Super' || $userRole == 'Manager' || $userGroup == 'Employee' || $userRole == 'Agent' || $userRole == 'Branch' || $userRole == 'Auditor') {
            if (count($PCInfo) > 0) {
                if (array_key_exists($filePCID, $PCInfo)) {
                    $PCName = trim($PCInfo[$filePCID]['processingCompanyName']);
                    $HRFee = trim($PCInfo[$filePCID]['HRFee']);
                }
            }
        }
        if ($userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager') {
            if (count($PCInfo) > 0) {
                if (array_key_exists($filePCID, $PCInfo)) {
                    $PCName = trim($PCInfo[$filePCID]['processingCompanyName']);
                }
            }
        }
        $oldPCName = '';
        if ($fileType == 4) {
            if (array_key_exists($oldFPCID, $PCInfo)) {
                $oldPCName = trim($PCInfo[$oldFPCID]['processingCompanyName']);
            }
        }

        if ($fileType == 4) {
            if (count($HRHistoryInfoArray) > 0) {
                if (array_key_exists($LMRId, $HRHistoryInfoArray)) {
                    $SubmissionDate = trim($HRHistoryInfoArray[$LMRId]['submissionDate']);
                }
            }

            if (Dates::IsEmpty($SubmissionDate)) {
                $SubmissionDate = '';
            } else {
                $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
                $ipArray['outputZone'] = $userTimeZone;
                $ipArray['inputTime'] = $SubmissionDate;
                $SubmissionDate = Dates::timeZoneConversion($ipArray);

                $SubmissionDate = Dates::formatDateWithRE($SubmissionDate, 'YMD_HMS', 'm/d/Y');

            }
            if (Strings::replaceCommaValues($price) == 0) {
                $price = $HRFee;
            }
        }

        if ($userRole == 'REST' || $fileType == 2) {
            if (Strings::replaceCommaValues($price) == 0) {
                $price = $RESTFee;
            }
        }
        $primeStatusId = trim($tempRespArray['primeStatusId']);

        if (array_key_exists($primeStatusId, $fileStatusInfo)) {
            $filePrimaryStatus = trim($fileStatusInfo[$primeStatusId]['primaryStatus']);
            $allowAgentToEditFile = trim($fileStatusInfo[$primeStatusId]['allowAgentToEditFile']);
            $allowClientToEditFile = trim($fileStatusInfo[$primeStatusId]['allowClientToEditFile']);
            $allowBOToEditFile = trim($fileStatusInfo[$primeStatusId]['allowBOToEditFile']);
            $allowOthersToUpdate = trim($fileStatusInfo[$primeStatusId]['allowOthersToUpdate']);
        }

        if (($userGroup == 'Employee' || $userGroup == 'CFPB Auditor' || $userGroup == 'Auditor Manager') && ($allowBOToEditFile == 1)) {
            $allowUserToUpdatePCStatus = 1;
        } else if (($userGroup == 'Branch') && ($allowOthersToUpdate == 1)) {
            $allowUserToUpdatePCStatus = 1;
        } else if (($userGroup == 'Agent') && ($allowAgentToEditFile == 1)) {
            $allowUserToUpdatePCStatus = 1;
        } else if (($userGroup == 'Client') && ($allowClientToEditFile == 1)) {
            $allowUserToUpdatePCStatus = 1;
        }
        $appStr = '';
        $propInfo = '';
        if (trim($houseNo)) {
            $propInfo = $houseNo;
            $appStr = ' ';
        }
        if (trim($propertyAddress)) {
            $propInfo .= $appStr . $propertyAddress;
            $appStr = ', ';
        }
        if (trim($propertyCity)) {
            $propInfo .= $appStr . $propertyCity;
            $appStr = ', ';
        }
        if (trim($propertyState)) {
            $propInfo .= $appStr . $propertyState;
            $appStr = ' ';
        }
        if (trim($propertyZip)) {
            $propInfo .= $appStr . $propertyZip;
        }

        /* Allow USA Private Money, LLC Agent's to update Admin section under Lead Status - Apr 07, 2017 - Removed on Apr 08, 2017  */
        /* Allow Agent's to update Admin section under Lead Status / their assigned files as per the branch settings logic (Lead Pool mode) - Apr 07, 2017  */
        if ($userRole == 'Agent' && ($allowAgentToSeeFile == 2 || ($allowAgentToSeeFile == 3 && strtolower($filePrimaryStatus) == 'lead'))) {
            $allowToUpdateFileAdminSection = 1;
        }

        if ($activeFile > 0) {
            if ($userRole == 'Agent' && array_key_exists($PCID, $agentUpdateStatusException)) {
                if (trim($primeStatusId) == trim($agentUpdateStatusException[$PCID])) {
                    $statusLink = "<span data-toggle='modal' 
                         data-wsize='modal-lg'
                        data-target='#exampleModal1'
                        class='btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 ' 
                        rel=\"nofollow\"
                        style='cursor:pointer;'  
                        data-name=\"File: " . htmlentities($borrowerFName . ' ' . $borrowerLName) . "  > Update File Status\"
                        data-href=\"" . CONST_URL_POPS . "updateFileStatus.php\" 
                        data-id=\"LID=" . cypher::myEncryption($LMRId) . '&RID=' . cypher::myEncryption($LMRResponseId) . '&exID=' . cypher::myEncryption($LMRExecutiveId) . '&PCID=' . cypher::myEncryption($PCID) . '&fileTypesTxt=' . cypher::myEncryption($fileTypesTxt) . '&URole=' . cypher::myEncryption($userRole) . "\" 
                        title=\"Status: " . $filePrimaryStatus . '<br> Sub-Status: ' . $subStatus . "\" ><i class='flaticon2-reload text-success'></i></span>";
                }
            } else if (($allowToUpdateFileAdminSection == 1) ||
                ($userRole == 'Agent' && $userNumber == $brokerNumber && $allowToUpdateFileAdminSection == 1)

            ) {
                $statusLink = "<span 
                data-toggle='modal'
                 data-target='#exampleModal1'  
                  data-wsize='modal-lg' class='btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 '
                   rel=\"nofollow\" 
                        style='cursor:pointer;'  
                   data-name=\"File: " . htmlentities($borrowerFName . ' ' . $borrowerLName) . "  > Update File Status\" 
                   data-href=\"" . CONST_URL_POPS . "updateFileStatus.php\" data-id=\"LID=" . cypher::myEncryption($LMRId) . '&RID=' . cypher::myEncryption($LMRResponseId) . '&exID=' . cypher::myEncryption($LMRExecutiveId) . '&PCID=' . cypher::myEncryption($PCID) . '&fileTypesTxt=' . cypher::myEncryption($fileTypesTxt) . '&URole=' . cypher::myEncryption($userRole) . "\" 
                title=\"Status: " . trim(Strings::processString($filePrimaryStatus)) . '<br>Sub-Status: ' . Strings::processString($subStatus) . "\" 
                ><i 
                data-html='true' 
                class='tooltipClass flaticon2-reload text-success ' 
                title=\"Status: " . trim(Strings::processString($filePrimaryStatus)) . '<br>Sub-Status: ' . Strings::processString($subStatus) . "\" ></i></span>";
            }
        }
        $freePreScreen = 0;

        if ($userRole == 'Auditor' || $fileType == 'LA') {
            if (array_key_exists($LMRId, $loanAuditInfoArray)) {
                $loanAuditInfo = $loanAuditInfoArray[$LMRId];
                if (count($loanAuditInfo) > 0) {
                    $filePrimaryStatus = trim($loanAuditInfo['loanAuditStatus']);
                    $fraudLevel = trim($loanAuditInfo['fraudLevel']);
                    $loanAuditFileStatus = trim($loanAuditInfo['activeStatus']);
                    $freePreScreen = trim($loanAuditInfo['freePreScreen']);
                    $SubmissionDate = trim($loanAuditInfo['recordDate']);
                    $paymentStatus = trim($loanAuditInfo['paymentStatus']);
                    $price = trim($loanAuditInfo['price']);

                    if (Dates::IsEmpty($SubmissionDate)) {
                        $SubmissionDate = '';
                    } else {
                        $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
                        $ipArray['outputZone'] = $userTimeZone;
                        $ipArray['inputTime'] = $SubmissionDate;
                        $SubmissionDate = Dates::timeZoneConversion($ipArray);

                        $SubmissionDate = Dates::formatDateWithRE($SubmissionDate, 'YMD_HMS', 'm/d/Y');
                    }

                    $statusLink = "<span 
                    data-toggle='modal' 
                    data-target='#exampleModal1' 
                    data-wsize='modal-lg' 
                    style='cursor:pointer;'
                    rel=\"nofollow\"  
                    data-href=\"" . CONST_URL_BOSSL . 'LMRequest.php?eId=' . cypher::myEncryption($LMRExecutiveId) . '&amp;lId=' . cypher::myEncryption($LMRId) . '&amp;rId=' . cypher::myEncryption($LMRResponseId) . "&amp;tabOpt=LA\" 
                    class=\"btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 \" 
                    data-id=''
                    title=\"Change status\"><i class='flaticon2-reload text-success'></i> </span>
                    ";
                }
            }

            $append = '';
            if ($freePreScreen > 0) {
                $loanAuditProduct = 'Request a Free Pre-Screen Review';
                $append = ',<br> ';
            }
            $loanAuditProductArray = [];
            if (array_key_exists($LMRId, $loanAuditProductInfo)) {
                if ($userRole == 'Auditor' && $userNumber == CONST_AUDITOR_2_ID) {
                    $loanAuditProductArray = ['MFA' => 'Mortgage Fraud Analysis'];
                }
                $productArray = $loanAuditProductInfo[$LMRId];
                for ($p = 0; $p < count($productArray); $p++) {
                    $loanAuditProduct .= $append . $loanAuditProductArray[trim($productArray[$p]['PID'])];
                    $append = ',<br> ';
                }
            }
        }

        if (array_key_exists($LMRId, $CFPBAuditInfoArray)) {
            $CFPBAuditInfo = $CFPBAuditInfoArray[$LMRId];
            if (count($CFPBAuditInfo) > 0) {
                $CFPBSubmittedDate = trim($CFPBAuditInfo['submittedDate']);
                if (Dates::IsEmpty($CFPBSubmittedDate)) {
                    $CFPBSubmittedDate = '';
                } else {
                    $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
                    $ipArray['outputZone'] = $userTimeZone;
                    $ipArray['inputTime'] = $CFPBSubmittedDate;
                    $CFPBSubmittedDate = Dates::timeZoneConversion($ipArray);

                    $CFPBSubmittedDate = Dates::formatDateWithRE($CFPBSubmittedDate, 'YMD_HMS', 'm/d/Y h:i A');
                    if ($CFPBSubmittedDate) {
                        $CFPBSubmittedDate .= ' - ' . $userTimeZone;
                    }
                }
            }
        }

        if ($userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager' || $fileType == 'CFPB') {
            if (array_key_exists($LMRId, $CFPBAuditInfoArray)) {
                $CFPBAuditInfo = $CFPBAuditInfoArray[$LMRId];
                if (count($CFPBAuditInfo) > 0) {
                    $filePrimaryStatus = trim($CFPBAuditInfo['CFPBAuditStatus']);
                    $SubmissionDate = trim($CFPBAuditInfo['recordDate']);
                    $submitterUID = trim($CFPBAuditInfo['UID']);
                    $submitterUType = trim($CFPBAuditInfo['UType']);
                    $CFPBAuditFileStatus = trim($CFPBAuditInfo['activeStatus']);
                    $tempAppStr = '';
                    $CFPBAuditSubmittedBy = $submitterUType;

                    if ($submitterUType == 'Employee' || $submitterUType == 'CFPB Auditor' || $submitterUType == 'Auditor Manager' || $submitterUType == 'Super') {
                        if (array_key_exists($submitterUID, $empCFPBInfo)) {
                            $CFPBAuditSubmittedBy = trim($empCFPBInfo[$submitterUID]['processorName']);
                            $CFPBAuditSubmittedBy .= ' (' . $submitterUType . ')';
                            if (trim($empCFPBInfo[$submitterUID]['email'])) $CFPBAuditSubmittedBy .= "<br><b>Email:</b> <span class=\"force-wrap\"><a href=\"mailto:" . $empCFPBInfo[$submitterUID]['email'] . "\">" . $empCFPBInfo[$submitterUID]['email'] . '</a></span> ';
                            if (trim($empCFPBInfo[$submitterUID]['phoneNumber'])) {
                                $CFPBAuditSubmitterInfo .= '<b>Ph #:</b> ' . $empCFPBInfo[$submitterUID]['phoneNumber'];
                                $tempAppStr = '<br>';
                            }
                            if (trim($empCFPBInfo[$submitterUID]['cellNumber'])) {
                                $CFPBAuditSubmitterInfo .= $tempAppStr . '<b>Cell:</b> ' . $empCFPBInfo[$submitterUID]['cellNumber'];
                                $tempAppStr = '<br>';
                            }
                            if (trim($empCFPBInfo[$submitterUID]['attorneyTelephone'])) {
                                $CFPBAuditSubmitterInfo .= $tempAppStr . '<b>Companhy Ph #:</b> ' . $empCFPBInfo[$submitterUID]['attorneyTelephone'];
                            }
                        }
                    } else if ($submitterUType == 'Branch') {
                        if (array_key_exists($submitterUID, $branchCFPBInfo)) {
                            $CFPBAuditSubmittedBy = trim($branchCFPBInfo[$submitterUID]['LMRExecutive']);
                            $CFPBAuditSubmittedBy .= ' (' . $submitterUType . ')';
                            if (trim($branchCFPBInfo[$submitterUID]['email'])) $CFPBAuditSubmittedBy .= "<br><b>Email:</b> <span class=\"force-wrap\"><a href=\"mailto:" . $branchCFPBInfo[$submitterUID]['email'] . "\">" . $branchCFPBInfo[$submitterUID]['email'] . '</a></span> ';
                            if (trim($branchCFPBInfo[$submitterUID]['phoneNumber'])) {
                                $CFPBAuditSubmitterInfo .= $tempAppStr . '<b>Ph #:</b> ' . $branchCFPBInfo[$submitterUID]['phoneNumber'];
                                $tempAppStr = '<br>';
                            }
                            if (trim($branchCFPBInfo[$submitterUID]['cellNumber'])) {
                                $CFPBAuditSubmitterInfo .= $tempAppStr . '<b>Cell:</b> ' . $branchCFPBInfo[$submitterUID]['cellNumber'];
                                $tempAppStr = '<br>';
                            }
                            if (trim($branchCFPBInfo[$submitterUID]['attorneyTelephone'])) {
                                $CFPBAuditSubmitterInfo .= $tempAppStr . '<b>Companhy Ph #:</b> ' . $branchCFPBInfo[$submitterUID]['attorneyTelephone'];
                            }
                        }
                    } else if ($submitterUType == 'Agent') {
                        if (array_key_exists($submitterUID, $agentCFPBInfo)) {
                            $CFPBAuditSubmittedBy = trim($agentCFPBInfo[$submitterUID]['agentName']);
                            $CFPBAuditSubmittedBy .= ' (' . $submitterUType . ')';
                            if (trim($agentCFPBInfo[$submitterUID]['email'])) $CFPBAuditSubmittedBy .= "<br><b>Email:</b> <span class=\"force-wrap\"><a href=\"mailto:" . $agentCFPBInfo[$submitterUID]['email'] . "\">" . $agentCFPBInfo[$submitterUID]['email'] . '</a></span> ';
                            if (trim($agentCFPBInfo[$submitterUID]['phoneNumber'])) {
                                $CFPBAuditSubmitterInfo .= $tempAppStr . '<b>Ph #:</b> ' . $agentCFPBInfo[$submitterUID]['phoneNumber'];
                                $tempAppStr = '<br>';
                            }
                            if (trim($agentCFPBInfo[$submitterUID]['cellNumber'])) {
                                $CFPBAuditSubmitterInfo .= $tempAppStr . '<b>Cell:</b> ' . $agentCFPBInfo[$submitterUID]['cellNumber'];
                                $tempAppStr = '<br>';
                            }
                            if (trim($agentCFPBInfo[$submitterUID]['attorneyTelephone'])) {
                                $CFPBAuditSubmitterInfo .= $tempAppStr . '<b>Companhy Ph #:</b> ' . $agentCFPBInfo[$submitterUID]['attorneyTelephone'];
                            }
                        }
                    } else if ($submitterUType == 'Client') {
                        if (array_key_exists($submitterUID, $clientCFPBInfo)) {
                            $CFPBAuditSubmittedBy = trim($clientCFPBInfo[$submitterUID]['clientName']);
                            $CFPBAuditSubmittedBy .= ' (' . $submitterUType . ')';
                            if (trim($clientCFPBInfo[$submitterUID]['email'])) $CFPBAuditSubmittedBy .= "<br><b>Email:</b> <span class=\"force-wrap\"><a href=\"mailto:" . $clientCFPBInfo[$submitterUID]['email'] . "\">" . $clientCFPBInfo[$submitterUID]['email'] . '</a></span> ';
                            if (trim($clientCFPBInfo[$submitterUID]['phoneNumber'])) {
                                $CFPBAuditSubmitterInfo .= $tempAppStr . '<b>Ph #:</b> ' . $clientCFPBInfo[$submitterUID]['phoneNumber'];
                                $tempAppStr = '<br>';
                            }
                            if (trim($clientCFPBInfo[$submitterUID]['cellNumber'])) {
                                $CFPBAuditSubmitterInfo .= $tempAppStr . '<b>Cell:</b> ' . $clientCFPBInfo[$submitterUID]['cellNumber'];
                                $tempAppStr = '<br>';
                            }
                            if (trim($clientCFPBInfo[$submitterUID]['attorneyTelephone'])) {
                                $CFPBAuditSubmitterInfo .= $tempAppStr . '<b>Companhy Ph #:</b> ' . $clientCFPBInfo[$submitterUID]['attorneyTelephone'];
                            }
                        }
                    }

                    if (Dates::IsEmpty($SubmissionDate)) {
                        $SubmissionDate = '';
                    } else {
                        $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
                        $ipArray['outputZone'] = $userTimeZone;
                        $ipArray['inputTime'] = $SubmissionDate;
                        $SubmissionDate = Dates::timeZoneConversion($ipArray);
                        $SubmissionDate = Dates::formatDateWithRE($SubmissionDate, 'YMD_HMS', 'm/d/Y');
                    }
                    if ($userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager' || ($fileType == 'CFPB' && $allowCFPBAuditing == 1)) {
                        $statusLink = "<a 
                        data-toggle='modal' 
                        data-target='#exampleModal1'
data-wsize='modal-lg'
 target=\"_blank\" 
 rel=\"nofollow\" 
 data-name=\"File: " . htmlentities($borrowerFName) . ' ' . htmlentities($borrowerLName) . "  > Update File CFPB Audit Status\" 
 href='#' 
 data-href=\"" . CONST_URL_POPS . "updateCFPBStatusInfo.php\"
  class=\"btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 \" 
  title=\"Change status\" 
  data-id=\"LID=" . cypher::myEncryption($LMRId) . '&RID=' . cypher::myEncryption($LMRResponseId) . '&exID=' . cypher::myEncryption($LMRExecutiveId) . '&PCID=' . cypher::myEncryption($PCID) . '&URole=' . cypher::myEncryption($userRole) . "\"><i class='flaticon2-reload text-success'></i> </a>";
                    } else {
                        $statusLink = 'nope';
                    }
                }
            }
        }

        $PCLink = '';
        $BranchLink = '';

        if ($userRole == 'REST' || ($fileType == 2 && ($userRole == 'Super' || $userRole == 'Sales')) || (($fileType == 4 || $fileType == 'LA') && ($userRole == 'Super' || $userRole == 'Sales'))) {
            $PCLink = wordwrap($PCName, 15, '<br>', true);
            $BranchLink = wordwrap($branchName, 15, '<br>', true);
        }

        if ($userRole == 'Branch') {
            $fileUrl = CONST_URL_BRSSL . 'LMRequest.php?eId=' . cypher::myEncryption($LMRExecutiveId) . '&amp;lId=' . cypher::myEncryption($LMRId) . '&amp;rId=' . cypher::myEncryption($LMRResponseId);
        } else if ($userRole == 'Agent') {
            $fileUrl = CONST_URL_AG_SSL . 'LMRequest.php?eId=' . cypher::myEncryption($LMRExecutiveId) . '&amp;lId=' . cypher::myEncryption($LMRId) . '&amp;rId=' . cypher::myEncryption($LMRResponseId) . '&amp;eOpt=1';
        } else {
            $fileUrl = CONST_URL_BOSSL . 'LMRequest.php?eId=' . cypher::myEncryption($LMRExecutiveId) . '&amp;lId=' . cypher::myEncryption($LMRId) . '&amp;rId=' . cypher::myEncryption($LMRResponseId);
        }

        $viewUrl = $fileUrl . '&op=' . cypher::myEncryption('view');
        if ($fileActiveStatus == 0) {
            $fileUrl .= '&op=' . cypher::myEncryption('view');
        } else {
            $fileUrl .= '&op=' . cypher::myEncryption('edit');
        }

        if ($isFileSS == 1) {
            $fileUrl .= '&tabOpt=DASH';
            /** If only selected the file Type = Short sale open the "Dashboard" tab on Sep 19, 2016 Start **/
        }

        if ($isFileLI == 1) $fileUrl .= '&tabOpt=LI';

        /** If only selected the file Type = Hard Money LOS open the "Loan Info" tab on Nov 21, 2016 Start **/
        if ($allowToEditMyFile == 1 || ($allowToEditMyFile == 2 && $allowUserToUpdatePCStatus == 1)) {
            doNothing();
        } else if ($allowToUpdateFileAdminSection == 1) $fileUrl .= '&tabOpt=ADMIN';

        if ($userGroup == 'REST' || ($fileType == 2 && ($userGroup == 'Super' || $userGroup == 'Sales')) || (($fileType == 4 || $fileType == 'LA') && ($userGroup == 'Super' || $userGroup == 'Sales'))) {
            if ($fileType == 4) {
                $fileUrl .= '&tabOpt=HR';
            } else if ($fileType == 'LA') {
                $fileUrl .= '&tabOpt=LA';
            } else {
                $fileUrl .= '&tabOpt=REST';
            }

            $borrowerLink = '<a ';
            if ($private != 1) {
                $borrowerLink .= " target=\"_blank\"";
            }
            $borrowerLink .= " class=\"purple-text\" href=\"$fileUrl\" style=\"text-decoration:none\" alt=\"Open file\" title=\"Open file\"><b>" . $borrowerName . '</b></a>';

        } else {
            $agentBranchesArr = [];
            if ($userGroup == 'Agent' && ($allowToEditMyFile == 1 || ($allowToEditMyFile == 2 && $allowUserToUpdatePCStatus == 1))) {
                if ($allowAgentToSeeFile == 3 || $allowAgentToSeeFile == 2) {
                    $agentBranchesArr = getBranchesForAgent::getReport(['brokerNumber' => $userNumber]);
                }
                if ($allowAgentToSeeFile == 3 && $externalBroker == 1) {
                    $assignedBranchesArr = array_column($agentBranchesArr, 'executiveId');
                    if (in_array($LMRExecutiveId, $assignedBranchesArr)) {
                        $allowAgentEditFile = 1;
                    }
                } else if ($allowAgentToSeeFile == 2 && $externalBroker == 1) {
                    $statusFAArr = array_filter(array_column($agentBranchesArr, 'defaultPrimaryStatusForFA'));
                    $statusQAArr = array_filter(array_column($agentBranchesArr, 'defaultPrimaryStatus'));
                    $finalStatusArray = array_merge($statusFAArr, $statusQAArr);
                    $qry = "call SP_GetPCStatusInfo_new( :PCID , '1', 'pipeline', '', :UGroup )";
                    $statusInfoRes = Database2::getInstance()->queryData($qry, [
                        'PCID' => $PCID,
                        'UGroup' => $UGroup,
                    ]);
                    for ($j = 0; $j < count($statusInfoRes); $j++) {
                        if (trim($statusInfoRes[$j]['primaryStatus']) == 'Lead') $finalStatusArray[] = trim($statusInfoRes[$j]['PSID']);
                    }
                    $finalStatusArray = array_unique($finalStatusArray);
                    if (in_array($primeStatusId, $finalStatusArray)) {
                        $allowAgentEditFile = 1;
                    }
                }
//         if ($allowToCreateFiles && $userNumber == $brokerNumber) { /** Allow files belongs to that agent only **/
                if ((($userNumber == $brokerNumber) || ($userNumber == $secondaryBrokerNumber)) || $allowAgentEditFile == 1) {
                    /** Allow files belongs to that agent only **/
                    $borrowerLink = '<a ';
                    if ($private != 1) {
                        $borrowerLink .= "target=\"_blank\" ";
                    }
                    $borrowerLink .= " class=\"purple-text\" href=\"" . $fileUrl . "\" alt=\"Open file\" title=\"Open file\" style=\"text-decoration:none\"><b>" . $borrowerName . '</b></a>';
                } else {
                    $borrowerLink = '<a ';
                    if ($private != 1) {
                        $borrowerLink .= " target=\"_blank\" ";
                    }
                    $borrowerLink .= " class=\"purple-text\" href=\"" . $viewUrl . "\" style=\"text-decoration:none\" alt=\"View file\" title=\"View file\"><b>" . $borrowerName . '</b></a>';
                }
            } else if (($userGroup == 'Agent' && $externalBroker == 0) && $userNumber != $brokerNumber) {
                $borrowerLink = "<span class=\"purple-text\"><b>" . $borrowerName . '</b></span>';
            } else if (($userGroup == 'Agent' && $externalBroker == 1) && $userNumber != $secondaryBrokerNumber) {
                $borrowerLink = "<span class=\"purple-text\"><b>" . $borrowerName . '</b></span>';
            } else if ($allowToEditMyFile == 1 || ($allowToEditMyFile == 2 && $allowUserToUpdatePCStatus == 1)) {
                if ($userRole == 'Auditor') {

                    $borrowerLink = '<a ';
                    if ($private != 1) {
                        $borrowerLink .= " target=\"_blank\" ";
                    }
                    $borrowerLink .= " class=\"purple-text\" href=\"" . $fileUrl . "&tabOpt=LA\" alt=\"Open file\" title=\"Open file\" style=\"text-decoration:none\"><b>" . $borrowerName . '</b></a>';

                } else if ($userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager' || $fileType == 'CFPB') {

                    $borrowerLink = '<a ';
                    if (!$private) {
                        $borrowerLink .= " target=\"_blank\" ";
                    }
                    $borrowerLink .= " class=\"purple-text\" href=\"" . $fileUrl . "&tabOpt=CFPB\" alt=\"Open file\" title=\"Open file\" style=\"text-decoration:none\"><b>" . $borrowerName . '</b></a>';

                } else {

                    $borrowerLink = '<a ';
                    if (!$private) {
                        $borrowerLink .= " target=\"_blank\" ";
                    }
                    $borrowerLink .= " class=\"purple-text\" href=\"" . $fileUrl . "\" alt=\"Open file\" style=\"text-decoration:none\" title=\"Open file\"><b>" . $borrowerName . '</b></a>';

                }
            } else {

                $borrowerLink = '<a ';
                if ($private == 1) {
                    $borrowerLink .= " target=\"_blank\" ";
                }
                $borrowerLink .= " class=\"purple-text\" href=\"" . $fileUrl . "\" style=\"text-decoration:none\" alt=\"Open file\" title=\"Open file\"><b>" . $borrowerName . '</b></a>';

            }
        }

        $borrowerLink .= '<br>' . $propInfo;

        if ($fileActiveStatus == 0) {
            $borrowerLink .= '</span>';
        }

        $entityLink = '<a target="_blank" class="purple-text" href="' . $fileUrl . '" style="text-decoration:none" alt=\"Open file\" title=\"Open file\"><b>' . $entityName . '</b></a>';

        if ($entityName) {
            $entityInfo = '<b>Name : </b><a target="_blank" class="purple-text" href="' . $fileUrl . '" style="text-decoration:none" alt=\"Open file\" title=\"Open file\"><b>' . $entityName . '</b></a><br>';
        }
        if ($entityType) {
            $entityInfo .= '<b>Type : </b>' . $entityType . '<br>';
        }

        if ($entityStateOfFormation) {
            $entityInfo .= '<b>State Of Formation : </b>' . $entityStateOfFormation . '<br>';
        }
        /* 1492 = Customization for PC = J Freeman, 820 = Dave PC, 2 = AWATA PC  on Dec 07, 2015 */

        $incomeArray = [];
        if (array_key_exists($LMRId, $incomeInfoArray)) {
            $incomeArray = $incomeInfoArray[$LMRId];
        }
        $taxes1 = 0;
        $insurance1 = 0;
        $HOAFees1 = 0;
        $floodInsurance1 = 0;
        $grossIncome1 = 0;
        $grossIncome2 = 0;
        $overtime1 = 0;
        $overtime2 = 0;
        $commissionOrBonus1 = 0;
        $commissionOrBonus2 = 0;
        $tipsMiscIncome1 = 0;
        $tipsMiscIncome2 = 0;
        $socialSecurity1 = 0;
        $socialSecurity2 = 0;
        $pensionOrRetirement1 = 0;
        $pensionOrRetirement2 = 0;
        $disability1 = 0;
        $disability2 = 0;
        $childSupportOrAlimony1 = 0;
        $childSupportOrAlimony2 = 0;
        $rental1 = 0;
        $rental2 = 0;
        $earnedInterest1 = 0;
        $earnedInterest2 = 0;
        $sonOrDaughter1 = 0;
        $sonOrDaughter2 = 0;
        $parents1 = 0;
        $parents2 = 0;
        $unemployment1 = 0;
        $unemployment2 = 0;
        $otherHouseHold1 = 0;
        $otherHouseHold2 = 0;
        $roomRental1 = 0;
        $roomRental2 = 0;
        $secondJobIncome1 = 0;
        $secondJobIncome2 = 0;
        $foodStampWelfare1 = 0;
        $foodStampWelfare2 = 0;
        $netMonthlyIncome1 = 0;
        $netSocialSecurity1 = 0;
        $netPensionOrRetirement1 = 0;
        $netDisability1 = 0;
        $netRental1 = 0;
        $netUnemployment1 = 0;
        $netEarnedInterest1 = 0;
        $netRoomRental1 = 0;
        $netSecondJobIncome1 = 0;

        $netMonthlyIncome2 = 0;
        $netSocialSecurity2 = 0;
        $netPensionOrRetirement2 = 0;
        $netDisability2 = 0;
        $netRental2 = 0;
        $netUnemployment2 = 0;
        $netEarnedInterest2 = 0;
        $netRoomRental2 = 0;
        $netSecondJobIncome2 = 0;
        $coTotalNetHouseHoldIncome = 0;
        $mortgageInsurance1 = 0;
        $grossSocialSecurity = '';
        $addGrossedUp1 = 0;
        $addGrossedUp2 = 0;
        $capitalGains1 = 0;
        $partnership1 = 0;
        $militaryIncome1 = 0;
        $capitalGains2 = 0;
        $partnership2 = 0;
        $fundingDate = null;

        if (count($incomeArray) > 0) {
            $taxes1 = trim($incomeArray['taxes1']);
            $insurance1 = trim($incomeArray['insurance1']);
            $HOAFees1 = trim($incomeArray['HOAFees1']);
            $floodInsurance1 = trim($incomeArray['floodInsurance1']);
            $mortgageInsurance1 = trim($incomeArray['mortgageInsurance1']);

            $grossIncome1 = trim($incomeArray['grossIncome1']);
            $grossIncome2 = trim($incomeArray['grossIncome2']);
            $commissionOrBonus1 = trim($incomeArray['commissionOrBonus1']);
            $commissionOrBonus2 = trim($incomeArray['commissionOrBonus2']);
            $overtime1 = trim($incomeArray['overtime1']);
            $overtime2 = trim($incomeArray['overtime2']);
            $tipsMiscIncome1 = trim($incomeArray['tipsMiscIncome1']);
            $tipsMiscIncome2 = trim($incomeArray['tipsMiscIncome2']);
            $socialSecurity1 = trim($incomeArray['socialSecurity1']);
            $socialSecurity2 = trim($incomeArray['socialSecurity2']);
            $pensionOrRetirement1 = trim($incomeArray['pensionOrRetirement1']);
            $pensionOrRetirement2 = trim($incomeArray['pensionOrRetirement2']);
            $disability1 = trim($incomeArray['disability1']);
            $disability2 = trim($incomeArray['disability2']);
            $childSupportOrAlimony1 = trim($incomeArray['childSupportOrAlimony1']);
            $childSupportOrAlimony2 = trim($incomeArray['childSupportOrAlimony2']);
            $rental1 = trim($incomeArray['rental1']);
            $rental2 = trim($incomeArray['rental2']);
            $earnedInterest1 = trim($incomeArray['earnedInterest1']);
            $earnedInterest2 = trim($incomeArray['earnedInterest2']);
            $sonOrDaughter1 = trim($incomeArray['sonOrDaughter1']);
            $sonOrDaughter2 = trim($incomeArray['sonOrDaughter2']);
            $parents1 = trim($incomeArray['parents1']);
            $parents2 = trim($incomeArray['parents2']);
            $unemployment1 = trim($incomeArray['unemployment1']);
            $unemployment2 = trim($incomeArray['unemployment2']);
            $otherHouseHold1 = trim($incomeArray['otherHouseHold1']);
            $otherHouseHold2 = trim($incomeArray['otherHouseHold2']);
            $roomRental1 = trim($incomeArray['roomRental1']);
            $roomRental2 = trim($incomeArray['roomRental2']);
            $secondJobIncome1 = trim($incomeArray['secondJobIncome1']);
            $secondJobIncome2 = trim($incomeArray['secondJobIncome2']);
            $foodStampWelfare1 = trim($incomeArray['foodStampWelfare1']);
            $foodStampWelfare2 = trim($incomeArray['foodStampWelfare2']);
            $capitalGains2 = trim($incomeArray['capitalGains2']);
            $partnership2 = trim($incomeArray['partnership2']);

            $netMonthlyIncome1 = trim($incomeArray['netMonthlyIncome1']);
            $netSocialSecurity1 = trim($incomeArray['netSocialSecurity1']);
            $netPensionOrRetirement1 = trim($incomeArray['netPensionOrRetirement1']);
            $netDisability1 = trim($incomeArray['netDisability1']);
            $netRental1 = trim($incomeArray['netRental1']);
            $netUnemployment1 = trim($incomeArray['netUnemployment1']);
            $netEarnedInterest1 = trim($incomeArray['netEarnedInterest1']);
            $netRoomRental1 = trim($incomeArray['netRoomRental1']);
            $netSecondJobIncome1 = trim($incomeArray['netSecondJobIncome1']);

            $netMonthlyIncome2 = trim($incomeArray['netMonthlyIncome2']);
            $netSocialSecurity2 = trim($incomeArray['netSocialSecurity2']);
            $netPensionOrRetirement2 = trim($incomeArray['netPensionOrRetirement2']);
            $netDisability2 = trim($incomeArray['netDisability2']);
            $netRental2 = trim($incomeArray['netRental2']);
            $netUnemployment2 = trim($incomeArray['netUnemployment2']);
            $netEarnedInterest2 = trim($incomeArray['netEarnedInterest2']);
            $netRoomRental2 = trim($incomeArray['netRoomRental2']);
            $netSecondJobIncome2 = trim($incomeArray['netSecondJobIncome2']);

            $addGrossedUp1 = trim($incomeArray['addGrossedUp1']);
            $addGrossedUp2 = trim($incomeArray['addGrossedUp2']);
            $capitalGains1 = trim($incomeArray['capitalGains1']);
            $militaryIncome1 = trim($incomeArray['militaryIncome1']);
            $partnership1 = trim($incomeArray['partnership1']);
        }

        if ($addGrossedUp1 > 0) {
            $socialSecurity = Strings::replaceCommaValues($socialSecurity1 * 1.25);
        } else {
            $socialSecurity = Strings::replaceCommaValues($socialSecurity1);
        }
        $pensionOrRetirement = Strings::replaceCommaValues($pensionOrRetirement1);
        $disability = Strings::replaceCommaValues($disability1);
        $childSupportOrAlimony = Strings::replaceCommaValues($childSupportOrAlimony1);
        $rental = Strings::replaceCommaValues($rental1);
        $earnedInterest = Strings::replaceCommaValues($earnedInterest1);
        $sonOrDaughter = Strings::replaceCommaValues($sonOrDaughter1);
        $parents = Strings::replaceCommaValues($parents1);
        $unemployment = Strings::replaceCommaValues($unemployment1);
        $otherHouseHold = Strings::replaceCommaValues($otherHouseHold1);
        $roomRental = Strings::replaceCommaValues($roomRental1);
        $secondJobIncome = Strings::replaceCommaValues($secondJobIncome1);
        $foodStampWelfare = Strings::replaceCommaValues($foodStampWelfare1);

        $primTotalGrossIncome = Strings::replaceCommaValues($grossIncome1)
            + Strings::replaceCommaValues($commissionOrBonus1)
            + Strings::replaceCommaValues($overtime1)
            + Strings::replaceCommaValues($tipsMiscIncome1);

        $coTotalGrossIncome = Strings::replaceCommaValues($grossIncome2)
            + Strings::replaceCommaValues($commissionOrBonus2)
            + Strings::replaceCommaValues($overtime2)
            + Strings::replaceCommaValues($tipsMiscIncome2);

        $HMLORealEstateTaxes = 0;
        $totalInsurance = 0;

        if (in_array('HMLO', $fileSelectedModuleCode)) {

            $annualPremium = 0;
            if (isset($fileHMLOPropertyInfo[$LMRId])) $annualPremium = $fileHMLOPropertyInfo[$LMRId]['annualPremium'];
            $HMLORealEstateTaxes = proposalFormula::calculateHMLORealEstateTaxes($taxes1);
            $totalInsurance = proposalFormula::calculateTotalInsurance($floodInsurance1, $annualPremium);

            $primTotalNetHouseHoldIncome = Strings::replaceCommaValues($grossIncome1)
                + Strings::replaceCommaValues($commissionOrBonus1) + Strings::replaceCommaValues($overtime1)
                + Strings::replaceCommaValues($netRental1) + Strings::replaceCommaValues($netEarnedInterest1)
                + Strings::replaceCommaValues($otherHouseHold1) + Strings::replaceCommaValues($capitalGains1)
                + Strings::replaceCommaValues($partnership1) + Strings::replaceCommaValues($militaryIncome1);

            if ($isCoBorrower == 1) {

                $coTotalNetHouseHoldIncome = Strings::replaceCommaValues($grossIncome2)
                    + Strings::replaceCommaValues($commissionOrBonus2) + Strings::replaceCommaValues($overtime2)
                    + Strings::replaceCommaValues($netRental2) + Strings::replaceCommaValues($netEarnedInterest2)
                    + Strings::replaceCommaValues($otherHouseHold2) + Strings::replaceCommaValues($capitalGains2)
                    + Strings::replaceCommaValues($partnership2);
            }

        } else {

            $primTotalNetHouseHoldIncome = Strings::replaceCommaValues($netMonthlyIncome1) + Strings::replaceCommaValues($netSocialSecurity1)
                + Strings::replaceCommaValues($netPensionOrRetirement1) + Strings::replaceCommaValues($netDisability1)
                + Strings::replaceCommaValues($netRental1) + Strings::replaceCommaValues($netEarnedInterest1)
                + Strings::replaceCommaValues($netUnemployment1) + Strings::replaceCommaValues($netRoomRental1)
                + Strings::replaceCommaValues($netSecondJobIncome1) + Strings::replaceCommaValues($sonOrDaughter1)
                + Strings::replaceCommaValues($parents1) + Strings::replaceCommaValues($childSupportOrAlimony1)
                + Strings::replaceCommaValues($otherHouseHold1) + Strings::replaceCommaValues($foodStampWelfare1)
                + Strings::replaceCommaValues($capitalGains1) + Strings::replaceCommaValues($partnership1);

            if ($isCoBorrower == 1) {

                if ($addGrossedUp2 > 0) {
                    $socialSecurity += Strings::replaceCommaValues($socialSecurity2 * 1.25);
                } else {
                    $socialSecurity += Strings::replaceCommaValues($socialSecurity2);
                }
                $pensionOrRetirement += Strings::replaceCommaValues($pensionOrRetirement2);
                $disability += Strings::replaceCommaValues($disability2);
                $childSupportOrAlimony += Strings::replaceCommaValues($childSupportOrAlimony2);
                $rental += Strings::replaceCommaValues($rental2);
                $earnedInterest += Strings::replaceCommaValues($earnedInterest2);
                $sonOrDaughter += Strings::replaceCommaValues($sonOrDaughter2);
                $parents += Strings::replaceCommaValues($parents2);
                $unemployment += Strings::replaceCommaValues($unemployment2);
                $otherHouseHold += Strings::replaceCommaValues($otherHouseHold2);
                $roomRental += Strings::replaceCommaValues($roomRental2);
                $secondJobIncome += Strings::replaceCommaValues($secondJobIncome2);
                $foodStampWelfare += Strings::replaceCommaValues($foodStampWelfare2);

                $coTotalNetHouseHoldIncome = Strings::replaceCommaValues($netMonthlyIncome2) + Strings::replaceCommaValues($netSocialSecurity2)
                    + Strings::replaceCommaValues($netPensionOrRetirement2) + Strings::replaceCommaValues($netDisability2)
                    + Strings::replaceCommaValues($netRental2) + Strings::replaceCommaValues($netEarnedInterest2)
                    + Strings::replaceCommaValues($netUnemployment2) + Strings::replaceCommaValues($netRoomRental2)
                    + Strings::replaceCommaValues($netSecondJobIncome2) + Strings::replaceCommaValues($sonOrDaughter2)
                    + Strings::replaceCommaValues($parents2) + Strings::replaceCommaValues($childSupportOrAlimony2)
                    + Strings::replaceCommaValues($otherHouseHold2) + Strings::replaceCommaValues($foodStampWelfare2)
                    + Strings::replaceCommaValues($capitalGains2) + Strings::replaceCommaValues($partnership2);

            }
            $grossSocialSecurity = $socialSecurity;
        }

        /*** Calculate New gross monthly household income - You can use this variable in income info tab, LM Proposal tab ***/
        $totalHouseHoldIncome = $primTotalNetHouseHoldIncome + $coTotalNetHouseHoldIncome;

        $totalGrossMonthlyHouseHoldIncome = $primTotalGrossIncome
            + $coTotalGrossIncome + $socialSecurity
            + $pensionOrRetirement + $disability
            + $childSupportOrAlimony + $rental + $earnedInterest
            + $sonOrDaughter + $parents + $unemployment
            + $otherHouseHold + $roomRental + $secondJobIncome
            + $foodStampWelfare;
        /*** Remove  the mortgage insurance from calculating DTI - on Sep 03, 2015 ***/
        if ($mortgageOwner1 == 'FHA') {
            /*** Include the mortgage insurance while calculating DTI if the mortgage type/owner is FHA - on May 04, 2016 ***/
            $tempLien1PITIA = proposalFormula::calculatePaymentPI($lien1Payment, $taxes1, $insurance1, $HOAFees1, $mortgageInsurance1, $floodInsurance1);
        } else {
            $tempLien1PITIA = proposalFormula::calculatePaymentPI($lien1Payment, $taxes1, $insurance1, $HOAFees1, 0, $floodInsurance1);
        }

        $lien1DTI = proposalFormula::calculateDTI($tempLien1PITIA, 0, $totalGrossMonthlyHouseHoldIncome);

        $lien1DTI = Currency::formatDollarAmountWithDecimal(Strings::replaceCommaValues($lien1DTI));

        $lien1Amount = Currency::formatDollarAmountWithDecimal($lien1Amount);
        $lien2Amount = Currency::formatDollarAmountWithDecimal($lien2Amount);
        $lien2Rate = Currency::formatDollarAmountWithDecimal($lien2Rate);
        $lien1BalanceDue = Currency::formatDollarAmountWithDecimal($lien1BalanceDue);
        $lien2BalanceDue = Currency::formatDollarAmountWithDecimal($lien2BalanceDue);
        $lien1Payment = Currency::formatDollarAmountWithDecimal($lien1Payment);
        $lien2Payment = Currency::formatDollarAmountWithDecimal($lien2Payment);
        $taxes1 = Currency::formatDollarAmountWithDecimal($taxes1);
        $insurance1 = Currency::formatDollarAmountWithDecimal($insurance1);
        $HOAFees1 = Currency::formatDollarAmountWithDecimal($HOAFees1);
        $grossSocialSecurity = Currency::formatDollarAmountWithDecimal($grossSocialSecurity);

        $coTotalHouseHoldExpenses = 0;

        $primaryMortgage1 = Strings::replaceCommaValues($lien1Payment) + Strings::replaceCommaValues($lien2Payment);
        $primTotalHouseHoldExpenses = Strings::replaceCommaValues($primaryMortgage1)
            + Strings::replaceCommaValues($HOAFees1);

        if (in_array('HMLO', $fileSelectedModuleCode)) {
            $primTotalHouseHoldExpenses += Strings::replaceCommaValues($HMLORealEstateTaxes) + Strings::replaceCommaValues($totalInsurance);
        } else {
            $primTotalHouseHoldExpenses += Strings::replaceCommaValues($taxes1)
                + Strings::replaceCommaValues($insurance1)
                + Strings::replaceCommaValues($floodInsurance1);
        }

        if (count($incomeArray) > 0) {
            $primTotalHouseHoldExpenses += Strings::replaceCommaValues(trim($incomeArray['otherMortgage1']))
                + Strings::replaceCommaValues(trim($incomeArray['creditCards1']))
                + Strings::replaceCommaValues(trim($incomeArray['autoLoan1']))
                + Strings::replaceCommaValues(trim($incomeArray['childSupportOrAlimonyMonthly1']))
                + Strings::replaceCommaValues(trim($incomeArray['unsecuredLoans1']))
                + Strings::replaceCommaValues(trim($incomeArray['studentLoans1']))
                + Strings::replaceCommaValues(trim($incomeArray['careAmt1']))
                + Strings::replaceCommaValues(trim($incomeArray['allInsurance1']))
                + Strings::replaceCommaValues(trim($incomeArray['groceries1']))
                + Strings::replaceCommaValues(trim($incomeArray['carExpenses1']))
                + Strings::replaceCommaValues(trim($incomeArray['medicalBill1']))
                + Strings::replaceCommaValues(trim($incomeArray['entertainment1']))
                + Strings::replaceCommaValues(trim($incomeArray['other1']))
                + Strings::replaceCommaValues(trim($incomeArray['cable1']))
                + Strings::replaceCommaValues(trim($incomeArray['natural1']))
                + Strings::replaceCommaValues(trim($incomeArray['water1']))
                + Strings::replaceCommaValues(trim($incomeArray['internet1']))
                + Strings::replaceCommaValues(trim($incomeArray['utilityOther1']))
                + Strings::replaceCommaValues(trim($incomeArray['electricity1']))
                + Strings::replaceCommaValues(trim($incomeArray['primaryBorrowerPhone']))
                + Strings::replaceCommaValues(trim($incomeArray['mortgageInsurance1']))
                + Strings::replaceCommaValues(trim($incomeArray['donation1']))
                + Strings::replaceCommaValues(trim($incomeArray['pets1']))
                + Strings::replaceCommaValues(trim($incomeArray['monthlyParking1']))
                + Strings::replaceCommaValues(trim($incomeArray['unionDues1']))
                + Strings::replaceCommaValues(trim($incomeArray['personalLoan1']))
                + Strings::replaceCommaValues(trim($incomeArray['dryCleaning1']))
                + Strings::replaceCommaValues(trim($incomeArray['lunchPurchased1']))
                + Strings::replaceCommaValues(trim($incomeArray['rentalExp1']));
            if ($isCoBorrower == 1) {
                $coTotalHouseHoldExpenses = Strings::replaceCommaValues(trim($incomeArray['otherMortgage2']))
                    + Strings::replaceCommaValues(trim($incomeArray['creditCards2']))
                    + Strings::replaceCommaValues(trim($incomeArray['autoLoan2']))
                    + Strings::replaceCommaValues(trim($incomeArray['childSupportOrAlimonyMonthly2']))
                    + Strings::replaceCommaValues(trim($incomeArray['unsecuredLoans2']))
                    + Strings::replaceCommaValues(trim($incomeArray['studentLoans2']))
                    + Strings::replaceCommaValues(trim($incomeArray['careAmt2']))
                    + Strings::replaceCommaValues(trim($incomeArray['allInsurance2']))
                    + Strings::replaceCommaValues(trim($incomeArray['groceries2']))
                    + Strings::replaceCommaValues(trim($incomeArray['carExpenses2']))
                    + Strings::replaceCommaValues(trim($incomeArray['medicalBill2']))
                    + Strings::replaceCommaValues(trim($incomeArray['entertainment2']))
                    + Strings::replaceCommaValues(trim($incomeArray['other2']))
                    + Strings::replaceCommaValues(trim($incomeArray['cable2']))
                    + Strings::replaceCommaValues(trim($incomeArray['natural2']))
                    + Strings::replaceCommaValues(trim($incomeArray['water2']))
                    + Strings::replaceCommaValues(trim($incomeArray['internet2']))
                    + Strings::replaceCommaValues(trim($incomeArray['utilityOther2']))
                    + Strings::replaceCommaValues(trim($incomeArray['electricity2']))
                    + Strings::replaceCommaValues(trim($incomeArray['coBorrowerPhone']))
                    + Strings::replaceCommaValues(trim($incomeArray['donation2']))
                    + Strings::replaceCommaValues(trim($incomeArray['pets2']))
                    + Strings::replaceCommaValues(trim($incomeArray['monthlyParking2']))
                    + Strings::replaceCommaValues(trim($incomeArray['unionDues2']))
                    + Strings::replaceCommaValues(trim($incomeArray['personalLoan2']))
                    + Strings::replaceCommaValues(trim($incomeArray['dryCleaning2']))
                    + Strings::replaceCommaValues(trim($incomeArray['lunchPurchased2']))
                    + Strings::replaceCommaValues(trim($incomeArray['rentalExp2']));
            }
        }
        $totalHouseHoldExpenses = $primTotalHouseHoldExpenses + $coTotalHouseHoldExpenses;
        $disposableIncome = $totalHouseHoldIncome - $totalHouseHoldExpenses;

        $totalHouseHoldExpenses = round(Strings::replaceCommaValues($totalHouseHoldExpenses), 2);
        $disposableIncome = round(Strings::replaceCommaValues($disposableIncome), 2);
        $totalHouseHoldIncome = round(Strings::replaceCommaValues($totalHouseHoldIncome), 2);

        $assetsInfoArray = [];
        if (array_key_exists($LMRId, $AssetsInfo)) $assetsInfoArray = $AssetsInfo[$LMRId];
        $totalAssets = 0;
        $assetTotalCashBankAcc = 0;
        if (count($assetsInfoArray) > 0) {
            $totalAssets = Strings::replaceCommaValues(trim($assetsInfoArray['assetCheckingAccounts'])) + Strings::replaceCommaValues(trim($assetsInfoArray['assetSavingMoneyMarket']));
            $totalAssets += Strings::replaceCommaValues(trim($assetsInfoArray['assetStocks'])) + Strings::replaceCommaValues(trim($assetsInfoArray['assetIRAAccounts']));
            $totalAssets += Strings::replaceCommaValues(trim($assetsInfoArray['assetESPOAccounts'])) + Strings::replaceCommaValues(trim($assetsInfoArray['assetHome']));
            $totalAssets += Strings::replaceCommaValues(trim($assetsInfoArray['assetORE'])) + Strings::replaceCommaValues(trim($assetsInfoArray['assetCars']));
            $totalAssets += Strings::replaceCommaValues(trim($assetsInfoArray['assetLifeInsurance'])) + Strings::replaceCommaValues(trim($assetsInfoArray['assetOther']));
            $totalAssets += Strings::replaceCommaValues(trim($assetsInfoArray['assetCash']));
            $assetTotalCashBankAcc = trim($assetsInfoArray['assetTotalCashBankAcc']);
        }

        $totalAssets = round($totalAssets, 2);

        $closingDate = '';
        if (array_key_exists($LMRId, $QAInfo)) {
            $insuranceCompName = trim($QAInfo[$LMRId]['insuranceCompName']);
            $closingDate = trim($QAInfo[$LMRId]['closingDate']);
        }
        $closingDate = Dates::formatDateWithRE($closingDate, 'YMD', 'm/d/Y');

        $desiredClosingDate = '';
        if (array_key_exists($LMRId, $QAInfo)) {
            $desiredClosingDate = trim($QAInfo[$LMRId]['desiredClosingDate']);
        }
        $desiredClosingDate = Dates::formatDateWithRE($desiredClosingDate, 'YMD', 'm/d/Y');

        $hearingDate = '';
        if (array_key_exists($LMRId, $QAInfo)) {
            $hearingDate = trim($QAInfo[$LMRId]['hearingDate']);
        }
        $hearingDate = Dates::formatDateWithRE($hearingDate, 'YMD', 'm/d/Y');

        if (count($fileContactsInfo) > 0) {
            if (array_key_exists($LMRId, $fileContactsInfo)) {
                $listingAgentName = trim($fileContactsInfo[$LMRId]['contactName']);
            }
        }
        $loanOriginationDate = '';
        if (array_key_exists($LMRId, $loanOriginationInfo)) {
            $loanOriginationDate = trim($loanOriginationInfo[$LMRId]['noteDate']);
            if (Dates::IsEmpty($loanOriginationDate)) {
                $loanOriginationDate = '';
            } else {
                $loanOriginationDate = Dates::formatDateWithRE($loanOriginationDate, 'YMD', 'm/d/Y');

            }
        }

        /* HMLO */
        $titleContacts = '';
        $apStr = '';
        if (count($titleContactInfo) > 0) {
            if (array_key_exists($LMRId, $titleContactInfo)) {
                $titleContactFName = trim($titleContactInfo[$LMRId]['contactName']);
                $titleCompanyName = trim($titleContactInfo[$LMRId]['companyName']);
                $titleContactLName = trim($titleContactInfo[$LMRId]['contactLName']);

                if (trim($titleCompanyName)) {
                    $titleContacts = '<b>Company:</b> ' . $titleCompanyName;
                    $apStr = '<br>';
                }
                if (trim($titleContactFName)) {
                    $titleContacts .= $apStr . '<b>Rep Name:</b> ' . $titleContactFName . ' ' . $titleContactLName;
                    $apStr = '<br>';
                }
                if (trim($titlePhoneNumber)) {
                    $titleContacts .= $apStr . '<b>Phone:</b> ' . $titlePhoneNumber;
                }
            }
        }
        if (count($insuranceCompContactInfo) > 0) {
            if (array_key_exists($LMRId, $insuranceCompContactInfo)) {
                $nameOfCarrier = trim($insuranceCompContactInfo[$LMRId]['companyName']);
                if (in_array('HMLO', $fileSelectedModuleCode)) {
                    $insuranceCompName = $nameOfCarrier;
                }
            }
        }

        $payrollLoanAmount = 0;
        $maxAmtToPutDown = 0;
        $rehabCostFinanced = 0;
        $closingCostFinanced = 0;
        $_typeOfHMLOLoanRequesting = '';
        $typeOfSale = '';
        $exitStrategy = '';
        $propertyNeedRehab = '';
        $propertyConstructionLevel = '';
        $lienPosition = '';
        $proInsPolicyExpDate = '';
        $isBorBorrowedDownPayment = $haveBorSquareFootage = $additionalPropertyRestrictions = $acceptedPurchase = '';
        $isBlanketLoan = '';
        $payOffOtherOutstandingAmounts = 0;
        $payOffMortgage1 = 0;
        $payOffMortgage2 = 0;
        $payOffOutstandingTaxes = 0;
        $cashOutAmt = 0;
        $HMLOLender = '';
        $rehabCostPercentageFinanced = 0;
        $interestChargedFromDate = $interestChargedEndDate = '';
        $taxImpoundsMonth = 0;
        $taxImpoundsMonthAmt = $insImpoundsMonthAmt = $insImpoundsFee = $paymentReserves = $requiredConstruction = $contingencyReserve = 0;
        $rehabConstructionCost = '';
        $referringParty = $maturityDate = $payOffDate = $loanSaleDate = '';
        $servicingStatus = '';
        $percentageTotalLoan = null;
        $noOfPropertiesAcquiring = '';
        $MERSID = null;
        $referralPoints = null;

        $requiredAdditionalCond['typeOfHMLOLoanRequesting'] = $fileHMLOPropertyInfo[$LMRId]['typeOfHMLOLoanRequesting'] ?? null;




        if (in_array('HMLO', $fileSelectedModuleCode)
            || in_array('EF', $fileSelectedModuleCode)
            || in_array('loc', $fileSelectedModuleCode)) {
            // Total Loan Amount Not Updating in real time (https://www.pivotaltracker.com/story/show/*********/comments/*********)
            if (array_key_exists($LMRId, $fileHMLOPropertyInfo ?? [])) {
                // use alternate name for this because the calculation include seems to be wrong TODO
                $_typeOfHMLOLoanRequesting = $fileHMLOPropertyInfo[$LMRId]['typeOfHMLOLoanRequesting'];
                $maxAmtToPutDown = $fileHMLOPropertyInfo[$LMRId]['maxAmtToPutDown'];
                $typeOfSale = $fileHMLOPropertyInfo[$LMRId]['typeOfSale'];
                $exitStrategy = $fileHMLOPropertyInfo[$LMRId]['exitStrategy'];
                $propertyNeedRehab = $fileHMLOPropertyInfo[$LMRId]['propertyNeedRehab'];
                $propertyConstructionLevel = $fileHMLOPropertyInfo[$LMRId]['propertyConstructionLevel'];
                $requiredAdditionalCond['propertyNeedRehab'] = $propertyNeedRehab;
                $lienPosition = $fileHMLOPropertyInfo[$LMRId]['lienPosition'];
                $proInsPolicyExpDate = $fileHMLOPropertyInfo[$LMRId]['proInsPolicyExpDate'];
                $paymentReserves = $fileHMLOPropertyInfo[$LMRId]['paymentReserves'];
                $requiredConstruction = $fileHMLOPropertyInfo[$LMRId]['requiredConstruction'];
                $contingencyReserve = $fileHMLOPropertyInfo[$LMRId]['contingencyReserve'];
                $percentageTotalLoan = $fileHMLOPropertyInfo[$LMRId]['percentageTotalLoan'];
                $referringParty = $fileHMLOPropertyInfo[$LMRId]['referringParty'];
                $maturityDate = Dates::formatDateWithRE($fileHMLOPropertyInfo[$LMRId]['maturityDate'], 'YMD', 'm/d/Y');
                $fundingDate = Dates::formatDateWithRE($fileHMLOPropertyInfo[$LMRId]['fundingDate'], 'YMD', 'm/d/Y');
                $servicingStatus = $fileHMLOPropertyInfo[$LMRId]['servicingSubStatus'];
                $payOffDate = Dates::formatDateWithRE($fileHMLOPropertyInfo[$LMRId]['payOffDate'], 'YMD', 'm/d/Y');
                $loanSaleDate = Dates::formatDateWithRE($fileHMLOPropertyInfo[$LMRId]['loanSaleDate'], 'YMD', 'm/d/Y');
                $proInsPolicyExpDate = Dates::formatDateWithRE($proInsPolicyExpDate, 'YMD', 'm/d/Y');

                $insExpiryDatesArrays = explode(',', $insExpiryDatesArray[$LMRId]['ExpiryDatesString'] ?? '');

                $isBlanketLoan = $fileHMLOPropertyInfo[$LMRId]['isBlanketLoan'];
                $requiredAdditionalCond['isBlanketLoan'] = $isBlanketLoan;

                if (($insExpiryDatesArrays[0])) {
                    $proInsPolicyExpDate = '';
                    foreach ($insExpiryDatesArrays as $insExpDate) {
                        if (!Dates::IsEmpty($insExpDate)) {
                            $proInsPolicyExpDate .= Dates::formatDateWithRE($insExpDate, 'YMD', 'm/d/Y') . ',<br>';//Dates::formatDateWithRE($insExpDate, 'YMD', 'm/d/Y');
                        }
                    }
                }
            }

            HMLOLoanTermsCalculation::Clear();

            if (count($fileHMLONewLoanInfo) > 0) {
                if (array_key_exists($LMRId, $fileHMLONewLoanInfo)) {

                    $rehabConstructionCost = $fileHMLONewLoanInfo[$LMRId]['rehabCost'];
                    $closingCostFinanced = $fileHMLONewLoanInfo[$LMRId]['closingCostFinanced'];
                    $payOffMortgage1 = $fileHMLONewLoanInfo[$LMRId]['payOffMortgage1'];
                    $payOffMortgage2 = $fileHMLONewLoanInfo[$LMRId]['payOffMortgage2'];
                    $payOffOutstandingTaxes = $fileHMLONewLoanInfo[$LMRId]['payOffOutstandingTaxes'];
                    $payOffOtherOutstandingAmounts = $fileHMLONewLoanInfo[$LMRId]['payOffOtherOutstandingAmounts'];
                    $cashOutAmt = $fileHMLONewLoanInfo[$LMRId]['cashOutAmt'];
                    $HMLOLender = $fileHMLONewLoanInfo[$LMRId]['HMLOLender'];
                    $rehabCostPercentageFinanced = $fileHMLONewLoanInfo[$LMRId]['rehabCostPercentageFinanced'];
                    $interestChargedFromDate = $fileHMLONewLoanInfo[$LMRId]['interestChargedFromDate'];
                    $interestChargedEndDate = $fileHMLONewLoanInfo[$LMRId]['interestChargedEndDate'];
                    $taxImpoundsMonth = $fileHMLONewLoanInfo[$LMRId]['taxImpoundsMonth'];
                    $taxImpoundsMonthAmt = $fileHMLONewLoanInfo[$LMRId]['taxImpoundsMonthAmt'];
                    $insImpoundsMonthAmt = $fileHMLONewLoanInfo[$LMRId]['insImpoundsMonthAmt'];
                    $insImpoundsFee = $fileHMLONewLoanInfo[$LMRId]['insImpoundsFee'];
                    $rehabCost = $fileHMLONewLoanInfo[$LMRId]['rehabCost'];
                    $payrollLoanAmount = $fileHMLONewLoanInfo[$LMRId]['monthlyLoanAmount'];
                    $noOfPropertiesAcquiring = $fileHMLONewLoanInfo[$LMRId]['noOfPropertiesAcquiring'];
                    $referralPoints = $fileHMLONewLoanInfo[$LMRId]['cv3ReferralPoint'];
                    $MERSID = $fileHMLONewLoanInfo[$LMRId]['MERSID'];

                }
            }

            $rehabCostFinanced = proposalFormula::calculateRehabCostFinancedByPercentage($rehabCost, $rehabCostPercentageFinanced);
            for ($aGur = 0; $aGur < count($tempBudgetAndDrawsInfo[$LMRId] ?? []); $aGur++) {
                $totalDrawsFunded += Strings::replaceCommaValues($tempBudgetAndDrawsInfo[$LMRId][$aGur]['amountAddedToTotalDrawsFunded']);
            }
            $availableBudget1 = Strings::replaceCommaValues($rehabCostFinanced) - Strings::replaceCommaValues($totalDrawsFunded);

            if ($HMLOLender == 'Array' || $HMLOLender == '') $HMLOLender = $PCName;
            /*********** - Clear Lender Name given as Array**/


            // require CONST_BO_PATH . '/HMLOLoanTermsCalculation.php';

            $insImpoundsMonth = null;
            $taxImpoundsFee = null;
            $haveCurrentLoanBal = null;
            $doYouHaveInvoiceToFactor = null;
            $filepaydownInfo = null;
            $purchaseCloseDate = null;
            $isThisGroundUpConstruction = null;
            $activeTabPipeLine = null;

            HMLOLoanTermsCalculation::Init(
                isset($URLPOSTING),
                $fileInfoArray,
                Strings::replaceCommaValues($insImpoundsMonth),
                Strings::replaceCommaValues($taxImpoundsFee),
                $isBorBorrowedDownPayment,
                $propertyNeedRehab,
                $haveBorSquareFootage,
                $additionalPropertyRestrictions,
                $exitStrategy,
                $acceptedPurchase,
                $haveCurrentLoanBal,
                $doYouHaveInvoiceToFactor,
                Strings::Numeric($maxAmtToPutDown),
                Strings::replaceCommaValues($closingCostFinanced),
                Strings::replaceCommaValues($payOffMortgage1),
                Strings::replaceCommaValues($payOffMortgage2),
                Strings::replaceCommaValues($payOffOutstandingTaxes),
                Strings::replaceCommaValues($payOffOtherOutstandingAmounts),
                Strings::replaceCommaValues($cashOutAmt),
                $filepaydownInfo,
                $lien1Terms,
                $purchaseCloseDate,
                Strings::replaceCommaValues($taxes1),
                $isBlanketLoan,
                $isThisGroundUpConstruction,
                $interestChargedFromDate,
                $interestChargedEndDate,
                Strings::replaceCommaValues($taxImpoundsMonth),
                Strings::replaceCommaValues($taxImpoundsMonthAmt),
                Strings::replaceCommaValues($insImpoundsMonthAmt),
                Strings::replaceCommaValues($insImpoundsFee),
                Strings::replaceCommaValues($paymentReserves),
                Strings::replaceCommaValues($requiredConstruction),
                Strings::replaceCommaValues($contingencyReserve),
                $fileHMLOPropertyInfo[$LMRId] ?? null,
                $fileHMLONewLoanInfo[$LMRId] ?? null,
                $LMRId,
                $activeTabPipeLine,
                $percentageTotalLoan,
                false,
                true
            );
        }

        $workflowEvents = '';
        if (count($LMRWFArray) > 0) {
            if (array_key_exists($LMRId, $LMRWFArray)) {
                $appComma = '';
                $workflowForFileArray = [];
                $workflowForFileCntArray = [];
                if (count($WFListArray) > 0) {
                    if (array_key_exists($LMRId, $WFListArray)) {
                        foreach ($WFListArray[$LMRId] as $item) {
                            $workflowForFileCntArray[$item['WFID']][] = $item;                                         // | Workflow Steps - workflow Key For count.
                            $workflowForFileArray[$item['WFSID']] = $item;                                         // | Workflow Steps - Steps Key For steps name.
                        }
                    }
                }

                foreach ($LMRWFArray[$LMRId] as $item) {
                    $PCWorkflowArrayN = [];
                    $FileWorkflowArrayN = [];
                    $fileWFSteps_tmp = $WFName = '';
                    $workflowSTCnt = 0;
                    $WfStepIds = [];

                    $lmWFID = $item['WFID'];
                    if ($lmWFID) {
                        if (array_key_exists($lmWFID, $PCWFArray)) {
                            $PCWorkflowArrayN = array_values($PCWFArray[$lmWFID]);
                        }
                        if (array_key_exists($lmWFID, myPipeline::$FileWorkFlowStepsArray[$LMRId] ?? [])) {
                            $FileWorkflowArrayN = array_values(myPipeline::$FileWorkFlowStepsArray[$LMRId][$lmWFID]);
                        }
                    }
                    $PCFileWorkflowArrayN = array_merge($PCWorkflowArrayN, $FileWorkflowArrayN);

                    foreach ($PCFileWorkflowArrayN as $pcWFs) {
                        $WfStepIds[] = $pcWFs['WFSID'];
                    }
                    $WorkflowOptionalConditions = getWFStepsOptionalConditions::getReport(['WFSId' => implode(',', $WfStepIds)]);

                    $numberOfWFStepsExcluded = 0;

                    foreach ($PCFileWorkflowArrayN as $pcWorkFlow) {
                        $WFSID = $pcWorkFlow['WFSID'];
                        $WFName = $pcWorkFlow['WFName'];
                        $WFSteps = htmlentities($pcWorkFlow['steps']);
                        if (isset($PCWFArray[$lmWFID][$WFSID])) {
                            $WorkflowOptionalConditions[$WFSID]['coBorrowerRelatedWfstep'] = $PCWFArray[$lmWFID][$WFSID]['coBorrowerRelatedWfstep'];
                            $WorkflowOptionalConditions[$WFSID]['noCrossCollRelatedWfstep'] = $PCWFArray[$lmWFID][$WFSID]['noCrossCollRelatedWfstep'];
                            $WorkflowOptionalConditions[$WFSID]['rehabRelatedWfstep'] = $PCWFArray[$lmWFID][$WFSID]['rehabRelatedWfstep'];
                            $WorkflowOptionalConditions[$WFSID]['usCitizenRelatedWfstep'] = $PCWFArray[$lmWFID][$WFSID]['usCitizenRelatedWfstep'];
                        }
                        $optionalLogic = BaseHTML::workFlowAdditionalLogic($WorkflowOptionalConditions[$WFSID], $requiredAdditionalCond);

                        if ($optionalLogic == '') {
                            $fileWFSteps_tmp .= "<div style=\"float:left;padding-right:2px;\" class=\"icons10 mr-1\">";
                            if (array_key_exists($WFSID, $workflowForFileArray)) {
                                $workflowSTCnt++;
                                $appComma = '<br>';
                                $fileWFSteps_tmp .= "<a class=\"step_complete tooltipClass\" title=\"" . ($WFSteps) . "\"></a>";
                            } else {
                                $fileWFSteps_tmp .= "<a class=\"no_step tooltipClass\" title=\"" . ($WFSteps) . "\">&nbsp;</a>";
                            }
                            $fileWFSteps_tmp .= '</div>';
                        } else {
                            $numberOfWFStepsExcluded++;
                        }
                    }
                    if (count($PCFileWorkflowArrayN) > 0) {   //quick hack to hide workflows that exist but are not assigned/applied to this loan... they have 0 steps so easy check
                        $workflowEvents .= $appComma . "<span style=\"color:#FF1493\"><b>" . $WFName . '</b></span>';
                        $workflowEvents .= '<br># step(s) applied: <b>' . $workflowSTCnt . ' of ' . (count($PCFileWorkflowArrayN) - $numberOfWFStepsExcluded) . ' </b><br>' . $fileWFSteps_tmp;
                        $workflowEvents .= '<br>';
                    }
                }
            }
        }

        $totalRehabCost = 0;
        if (count($fileHMLOListOfRepairs) > 0) {
            if (array_key_exists($LMRId, $fileHMLOListOfRepairs)) {
                $architectFees = trim($fileHMLOListOfRepairs[$LMRId]['architectFees']);
                $permitsFees = trim($fileHMLOListOfRepairs[$LMRId]['permitsFees']);
                $demolitionTrashDumpsters = trim($fileHMLOListOfRepairs[$LMRId]['demolitionTrashDumpsters']);
                $exteriorRepairs = trim($fileHMLOListOfRepairs[$LMRId]['exteriorRepairs']);
                $termiteInspectionTreatment = trim($fileHMLOListOfRepairs[$LMRId]['termiteInspectionTreatment']);
                $foundationStructuralReport = trim($fileHMLOListOfRepairs[$LMRId]['foundationStructuralReport']);
                $roofing = trim($fileHMLOListOfRepairs[$LMRId]['roofing']);
                $windows = trim($fileHMLOListOfRepairs[$LMRId]['windows']);
                $doors = trim($fileHMLOListOfRepairs[$LMRId]['doors']);
                $siding = trim($fileHMLOListOfRepairs[$LMRId]['siding']);
                $carpentry = trim($fileHMLOListOfRepairs[$LMRId]['carpentry']);
                $deckPorch = trim($fileHMLOListOfRepairs[$LMRId]['deckPorch']);
                $drivewayWalkwayPatio = trim($fileHMLOListOfRepairs[$LMRId]['drivewayWalkwayPatio']);
                $landscaping = trim($fileHMLOListOfRepairs[$LMRId]['landscaping']);
                $exteriorRepairsOther = trim($fileHMLOListOfRepairs[$LMRId]['exteriorRepairsOther']);
                $HVACRough = trim($fileHMLOListOfRepairs[$LMRId]['HVACRough']);
                $HVACFinish = trim($fileHMLOListOfRepairs[$LMRId]['HVACFinish']);
                $plumbingRough = trim($fileHMLOListOfRepairs[$LMRId]['plumbingRough']);
                $plumbingFixtures = trim($fileHMLOListOfRepairs[$LMRId]['plumbingFixtures']);
                $plumbingFinish = trim($fileHMLOListOfRepairs[$LMRId]['plumbingFinish']);
                $electricalRough = trim($fileHMLOListOfRepairs[$LMRId]['electricalRough']);
                $electricalFixtures = trim($fileHMLOListOfRepairs[$LMRId]['electricalFixtures']);
                $electricalFinish = trim($fileHMLOListOfRepairs[$LMRId]['electricalFinish']);
                $sheetRock = trim($fileHMLOListOfRepairs[$LMRId]['sheetRock']);
                $interiorRepairsDoors = trim($fileHMLOListOfRepairs[$LMRId]['interiorRepairsDoors']);
                $interiorRepairsCarpentry = trim($fileHMLOListOfRepairs[$LMRId]['interiorRepairsCarpentry']);
                $interiorRepairsOther1 = trim($fileHMLOListOfRepairs[$LMRId]['interiorRepairsOther1']);
                $interiorRepairsOther2 = trim($fileHMLOListOfRepairs[$LMRId]['interiorRepairsOther2']);
                $interiorRepairsOther3 = trim($fileHMLOListOfRepairs[$LMRId]['interiorRepairsOther3']);
                $kitchenCabinets = trim($fileHMLOListOfRepairs[$LMRId]['kitchenCabinets']);
                $kitchenCountertops = trim($fileHMLOListOfRepairs[$LMRId]['kitchenCountertops']);
                $kitchenAppliances = trim($fileHMLOListOfRepairs[$LMRId]['kitchenAppliances']);
                $bath1 = trim($fileHMLOListOfRepairs[$LMRId]['bath1']);
                $bath2 = trim($fileHMLOListOfRepairs[$LMRId]['bath2']);
                $bath3 = trim($fileHMLOListOfRepairs[$LMRId]['bath3']);
                $interiorPainting = trim($fileHMLOListOfRepairs[$LMRId]['interiorPainting']);
                $exteriorPainting = trim($fileHMLOListOfRepairs[$LMRId]['exteriorPainting']);
                $flooringCarpetVinyl = trim($fileHMLOListOfRepairs[$LMRId]['flooringCarpetVinyl']);
                $flooringTile = trim($fileHMLOListOfRepairs[$LMRId]['flooringTile']);
                $flooringHardwood = trim($fileHMLOListOfRepairs[$LMRId]['flooringHardwood']);
                $finalCleanupOther1 = trim($fileHMLOListOfRepairs[$LMRId]['finalCleanupOther1']);
                $finalCleanupOther2 = trim($fileHMLOListOfRepairs[$LMRId]['finalCleanupOther2']);
                $finalCleanupOther3 = trim($fileHMLOListOfRepairs[$LMRId]['finalCleanupOther3']);
                $finalCleanupOther4 = trim($fileHMLOListOfRepairs[$LMRId]['finalCleanupOther4']);
                $totalRehabCost = Strings::replaceCommaValues($architectFees)
                    + Strings::replaceCommaValues($permitsFees)
                    + Strings::replaceCommaValues($demolitionTrashDumpsters)
                    + Strings::replaceCommaValues($exteriorRepairs)
                    + Strings::replaceCommaValues($termiteInspectionTreatment)
                    + Strings::replaceCommaValues($foundationStructuralReport)
                    + Strings::replaceCommaValues($roofing)
                    + Strings::replaceCommaValues($windows)
                    + Strings::replaceCommaValues($doors)
                    + Strings::replaceCommaValues($siding)
                    + Strings::replaceCommaValues($carpentry)
                    + Strings::replaceCommaValues($deckPorch)
                    + Strings::replaceCommaValues($drivewayWalkwayPatio)
                    + Strings::replaceCommaValues($landscaping)
                    + Strings::replaceCommaValues($exteriorRepairsOther)
                    + Strings::replaceCommaValues($HVACRough)
                    + Strings::replaceCommaValues($HVACFinish)
                    + Strings::replaceCommaValues($plumbingRough)
                    + Strings::replaceCommaValues($plumbingFixtures)
                    + Strings::replaceCommaValues($plumbingFinish)
                    + Strings::replaceCommaValues($electricalRough)
                    + Strings::replaceCommaValues($electricalFixtures)
                    + Strings::replaceCommaValues($electricalFinish)
                    + Strings::replaceCommaValues($sheetRock)
                    + Strings::replaceCommaValues($interiorRepairsDoors)
                    + Strings::replaceCommaValues($interiorRepairsCarpentry)
                    + Strings::replaceCommaValues($interiorRepairsOther1)
                    + Strings::replaceCommaValues($interiorRepairsOther2)
                    + Strings::replaceCommaValues($interiorRepairsOther3)
                    + Strings::replaceCommaValues($kitchenCabinets)
                    + Strings::replaceCommaValues($kitchenCountertops)
                    + Strings::replaceCommaValues($kitchenAppliances)
                    + Strings::replaceCommaValues($bath1)
                    + Strings::replaceCommaValues($bath2)
                    + Strings::replaceCommaValues($bath3)
                    + Strings::replaceCommaValues($interiorPainting)
                    + Strings::replaceCommaValues($exteriorPainting)
                    + Strings::replaceCommaValues($flooringCarpetVinyl)
                    + Strings::replaceCommaValues($flooringTile)
                    + Strings::replaceCommaValues($flooringHardwood)
                    + Strings::replaceCommaValues($finalCleanupOther1)
                    + Strings::replaceCommaValues($finalCleanupOther2)
                    + Strings::replaceCommaValues($finalCleanupOther3)
                    + Strings::replaceCommaValues($finalCleanupOther4);

                $totalRehabCost = round(Strings::replaceCommaValues($totalRehabCost), 2);
            }
        }

        $bankNumber = '';
        $lienAmount = 0;
        if (count($listingRealtorInfo2) > 0) {
            if (array_key_exists($LMRId, $listingRealtorInfo2)) {
                $bankNumber = $listingRealtorInfo2[$LMRId]['bankNumber'];
                $lienAmount = $listingRealtorInfo2[$LMRId]['lienAmount'];
//                $BPOExpirationDate = Dates::formatDateWithRE($listingRealtorInfo2[$LMRId]['BPOExpirationDate'], 'YMD', 'm/d/Y');
            }
        }

        /**
         *
         * Description : Get the Merchant Funding Modules pipeline Loan Info section in show and Hide columns
         * Developer : Viji & Venky
         * Date    : Aug 31, 2017
         **/
        $tempFileMFLoanTerms = [];
        if (count($fileMFLoanTerms) > 0) {
            if (array_key_exists($LMRId, $fileMFLoanTerms)) {
                $tempFileMFLoanTerms = $fileMFLoanTerms[$LMRId];
            }
        }

        $MFLoanTermsInfoCnt = count($tempFileMFLoanTerms);
        $noOfApprovedStatusCnt = 0;
        $totalApprovedLoanAmt = 0;
        foreach ($tempFileMFLoanTerms as $terms) {

            $approvedLoanAmt = $terms['approvedLoanAmt'];
            $applnStatus = $terms['applnStatus'];

            if ($applnStatus == 'Approved') {
                $totalApprovedLoanAmt += $approvedLoanAmt;
                $noOfApprovedStatusCnt++;
            }
        }

        // from HMLOLoanTermsCalculation.php

        $totalApprovedLoanAmt = Currency::formatDollarAmountWithDecimal($totalApprovedLoanAmt);

        $myFileInfoObject = new myFileInfo();
        $myFileInfoObject->LMRId = $LMRId;

        $fileAdminInfo = $myFileInfoObject->getFileAdminInfo();
        $adverseActionInfo = $myFileInfoObject->getAdverseAction();
        $filePropertyInfo = $myFileInfoObject->FileProInfo();
        $fileResponseInfo = $myFileInfoObject->ResponseInfo();
        $fileHMLONewLoanInfo = $myFileInfoObject->getFileHMLONewLoanInfo();
        $fileHMLOInfoObj = $myFileInfoObject->fileHMLOInfo();
        $refinanceMortgageInfoObj = $myFileInfoObject->refinanceMortgageInfo();
        $refinanceMortgageInfoPrimaryObj =  $refinanceMortgageInfoObj[0];
        $fileBrokerInfo = $myFileInfoObject->BrokerInfo();
        $titleOrderDate = Dates::formatDateWithRE($filePropertyInfo->titleOrderedDate, 'YMD', 'm/d/Y');
        HUDFundingClosingInfo::init($LMRId);
        $fundingWire = HUDFundingClosingInfo::$tblFundingClosing;

        $glBrokerPartnerTypeArray = glCustomJobForProcessingCompany::getBrokerPartnerType($PCID, 0);
        $brokerPartnerType = $glBrokerPartnerTypeArray[$fileBrokerInfo->brokerPartnerType] ?? '';

        $createdDateWithTimestamp = $myFileInfoObject->getCreatedDateWithTimestamp() ?? '';
        $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
        $ipArray['outputZone'] = $userTimeZone;
        $ipArray['inputTime'] = $createdDateWithTimestamp;
        $createdDateWithTimestamp = Dates::timeZoneConversion($ipArray);
        $createdDateWithTimestamp = Dates::formatDateWithRE($createdDateWithTimestamp, 'YMD_HMS', 'm/d/Y h:i A');

        if ($createdDateWithTimestamp) {
            $createdDateWithTimestamp .= ' - ' . $userTimeZone;
        }

        $this->SubmissionDate = $SubmissionDate;
        $this->recordDate = $recordDate;
        $this->lastUpdatedDate = $lastUpdatedDate;
        $this->entityName = $entityName;
        $this->borPhone = $borPhone;
        $this->borrowercellNumber = $borrowercellNumber;
        $this->workNumber = $workNumber;
        $this->borrowerEmail = $borrowerEmail;
        $this->presentCity = $presentCity;
        $this->presentState = $presentState;
        $this->assetTotalCashBankAcc = $assetTotalCashBankAcc;
        $this->statusLink = $statusLink;
        $this->filePrimaryStatus = $filePrimaryStatus;
        $this->subStatus = $subStatus;
        $this->LMRInternalLoanProgram = $LMRInternalLoanProgram;
        $this->serviceReq = $serviceReq;
        $this->propertyType = $propertyType;
        $this->propertyCity = Property::$primaryPropertyInfo->propertyCity;
        $this->propertyState = Property::$primaryPropertyInfo->propertyState;


        $this->desiredClosingDate = $desiredClosingDate;
        $this->hearingDate = $hearingDate;
        $this->CFPBSubmittedDate = $CFPBSubmittedDate;
        $this->salesDate = $salesDate;
        $this->receivedDate = $receivedDate;
        $this->lenderSubmissionDate = $lenderSubmissionDate;
        $this->closedDate = $closedDate;
        $this->closingDate = $closingDate;
        $this->borrowerCallBack = $borrowerCallBack;
        $this->lenderCallBack = $lenderCallBack;
        $this->HAFADate = $HAFADate;
        $this->tempRespArray = $tempRespArray;
        $this->thisFileStatusHistory = $thisFileStatusHistory;
        $this->fileVelocity = $fileVelocity;
        $this->totalLoanAmount = HMLOLoanTermsCalculation::$totalLoanAmount;
        $this->initialLoanAmount = HMLOLoanTermsCalculation::$initialLoanAmount;
        $this->yieldSpread = HMLOLoanTermsCalculation::$yieldSpread;
        $this->costOfCapital = HMLOLoanTermsCalculation::$costOfCapital;
        $this->netMonthlyPayment = HMLOLoanTermsCalculation::$netMonthlyPayment;
        $this->payrollLoanAmount = $payrollLoanAmount;
        $this->loanAuditProduct = $loanAuditProduct;
        $this->fraudLevel = $fraudLevel;
        $this->CFPBAuditSubmitterInfo = $CFPBAuditSubmitterInfo;
        $this->CFPBAuditSubmittedBy = $CFPBAuditSubmittedBy;
        $this->entityInfo = $entityInfo;
        $this->entityType = $entityType;
        $this->entityStateOfFormation = $entityStateOfFormation;
        $this->entityState = $entityState;
        $this->entityCity = $entityCity;
        $this->entityPropertyOwnerShip = $entityPropertyOwnerShip;
        $this->noOfEmployees = $noOfEmployees;
        $this->avgMonthlyCreditcardSale = $avgMonthlyCreditcardSale;
        $this->avgTotalMonthlySale = $avgTotalMonthlySale;
        $this->annualGrossProfit = $annualGrossProfit;
        $this->ordinaryBusinessIncome = $ordinaryBusinessIncome;
        $this->grossSocialSecurity = $grossSocialSecurity;
        $this->totalGrossMonthlyHouseHoldIncome = $totalGrossMonthlyHouseHoldIncome;
        if ($totalHouseHoldIncome) {
            $this->totalHouseHoldIncome = '$ ' . Currency::formatDollarAmountWithDecimal($totalHouseHoldIncome);
        } else {
            $this->totalHouseHoldIncome = null;
        }
        $this->totalHouseHoldExpenses = $totalHouseHoldExpenses;
        $this->disposableIncome = $disposableIncome;
        $this->lien1DTI = $lien1DTI;
        $this->totalAssets = $totalAssets;
        $this->lender1Info = $lender1Info;
        $this->lender1Name = $lender1Name;
        $this->lender2Name = $lender2Name;
        $this->mortgageOwner1 = $mortgageOwner1;
        $this->mortgageOwner2 = $mortgageOwner2;
        $this->loanType = $loanType;
        $this->loanType2 = $loanType2;
        $this->lien1Amount = $lien1Amount;
        $this->lien2Amount = $lien2Amount;
        $this->loanNumber = $loanNumber;
        $this->loanNumber2 = $loanNumber2;
        $this->lien1Rate = $lien1Rate;
        $this->lien2Rate = $lien2Rate;
        $this->lien2Payment = $lien2Payment;
        $this->tempLien1PITIA = $tempLien1PITIA;
        $this->lien1LPMade = $lien1LPMade;
        $this->lien2LPMade = $lien2LPMade;
        $this->noOfMonthsBehind1 = $noOfMonthsBehind1;
        $this->noOfMonthsBehind2 = $noOfMonthsBehind2;
        $this->lien1BalanceDue = $lien1BalanceDue;
        $this->loanOriginationDate = $loanOriginationDate;
        $this->lien2BalanceDue = $lien2BalanceDue;
        $this->occupancy = $occupancy;
        $this->appraiser1 = $appraiser1;
        $this->BPO1 = $BPO1;
        $this->rehabValue = $rehabValue;
        $this->bedrooms = $bedrooms;
        $this->bathrooms = $bathrooms;
        $this->yearBuilt = $yearBuilt;
        $this->fileNumber = $fileNumber;
        $this->assignedEmpInfo = $assignedEmpInfo;
        $this->PCName = $PCName;
        $this->assignedCFPBAuditors = $assignedCFPBAuditors;
        $this->leadSource = $leadSource;
        $this->insuranceCompName = $insuranceCompName;
        $this->listingAgentName = $listingAgentName;
        $this->priorityLevel = $priorityLevel;
        $this->typeOfHMLOLoanRequesting = $_typeOfHMLOLoanRequesting;
        $this->trialPaymentDate1 = $trialPaymentDate1;
        $this->proInsPolicyExpDate = $proInsPolicyExpDate;
        $this->networthOfBusinessOwned = $networthOfBusinessOwned;
        $this->borCreditScoreRange = $borCreditScoreRange;
        $this->midFico = $midFico;
        $this->borExperianScore = $borExperianScore;
        $this->borEquifaxScore = $borEquifaxScore;
        $this->borTransunionScore = $borTransunionScore;
        $this->borNoOfREPropertiesCompleted = $borNoOfREPropertiesCompleted;
        $this->borRehabPropCompleted = $borRehabPropCompleted;
        $this->borNoOfOwnProp = $borNoOfOwnProp;
        $this->isBorUSCitizen = $isBorUSCitizen;
        $this->HMLOLender = $HMLOLender;
        $this->totalProjectCost = HMLOLoanTermsCalculation::$NewTotalProjectCost;
        $this->rehabCostFinanced = $rehabCostFinanced > 0 ? '$ ' . Currency::formatDollarAmountWithDecimal($rehabCostFinanced) : '';
        $this->LTC = HMLOLoanTermsCalculation::$LTC;
        $this->ARV = HMLOLoanTermsCalculation::$ARV;
        $this->rehabConstructionCost = $rehabConstructionCost;
        $this->acquisitionLTV = HMLOLoanTermsCalculation::$acquisitionLTV;
        $this->marketLTV = HMLOLoanTermsCalculation::$marketLTV;
        $this->perRehabCostFinanced = HMLOLoanTermsCalculation::$perRehabCostFinanced;
        $this->exitStrategy = HMLOLoanTermsCalculation::$exitStrategy;
        $this->isHouseProperty = $isHouseProperty;
        $this->propertyNeedRehab = $propertyNeedRehab;
        $this->propertyConstructionLevel = $propertyConstructionLevel;
        $this->lienPosition = $lienPosition;
        $this->propertyCounty = $propertyCounty;
        $this->propertySqFt = $propertySqFt;
        $this->propertyURLLink = $propertyURLLink;
        $this->taxes1 = $taxes1;
        $this->workflowEvents = $workflowEvents;
        $this->HMLOLoanTerm = HMLOLoanTermsCalculation::$loanTerm;
        $this->costBasis = $costBasis;
        $this->lien1Payment = $lien1Payment;
        $this->homeValue = $homeValue;
        $this->presentOccupancyStatus = $presentOccupancyStatus;
        $this->propertyCondition = $propertyCondition;
        $this->appraiser1Value = $appraiser1Value;
        $this->AVM1 = $AVM1;
        $this->dateObtained = $dateObtained;
        $this->appraisal1OrderDate = $appraisal1OrderDate;
        $this->totalRehabCost = $totalRehabCost;
        $this->assessedValue = $assessedValue;
        $this->titleContacts = $titleContacts;
        $this->typeOfSale = $typeOfSale;
        $this->projectName = $projectName;
        $this->MFLoanTermsInfoCnt = $MFLoanTermsInfoCnt;
        $this->noOfApprovedStatusCnt = $noOfApprovedStatusCnt;
        $this->totalApprovedLoanAmt = $totalApprovedLoanAmt;
        $this->bankNumber = $bankNumber;
        $this->lienAmount = $lienAmount;
        $this->referringParty = $referringParty;
        $this->availableBudget1 = $availableBudget1 ? '$ ' . Currency::formatDollarAmountWithDecimal($availableBudget1) : '';
        $this->currentLoanBalance = HMLOLoanTermsCalculation::$currentLoanBalance;
        $this->maturityDate = $maturityDate;
        $this->servicingStatus = $servicingStatus;
        $this->payOffDate = $payOffDate;
        $this->loanSaleDate = $loanSaleDate;
        $this->PCLink = $PCLink;
        $this->filePCID = $filePCID;
        $this->oldFPCID = $oldFPCID;
        $this->oldPCName = $oldPCName;
        $this->BranchLink = $BranchLink;
        $this->brokerName = $brokerName;
        $this->lenderName = $lenderName;
        $this->servicerName = $servicerName;
        $this->trusteeName = $trusteeName;
        $this->investorContactDetails = $investorContactDetails;
        $this->secondaryBrokerName = $secondaryBrokerName;
        $this->branchName = $branchName;
        $this->orderStatus = $orderStatus;
        $this->price = $price;
        $this->paymentStatus = $paymentStatus;
        $this->LMRResponseId = $LMRResponseId;

        $this->fileInfoArray = $fileInfoArray;
        $this->borrowerLink = $borrowerLink;
        $this->entityLink = $entityLink;
        $this->sendMarketingEmail = $sendMarketingEmail;
        $this->activeFile = $activeFile;
        $this->fileModuleTypeArray = $fileModuleTypeArray;
        $this->isFileSS = $isFileSS;
        $this->isFileLSR = $isFileLSR;

        $this->fileType = $fileType;
        $this->allowAgentEditFile = $allowAgentEditFile;

        $this->allowToUpdateFileAdminSection = $allowToUpdateFileAdminSection;
        $this->LMRClientId = $LMRClientId;
        $this->allowCFPBAuditing = $allowCFPBAuditing;
        $this->executiveId = $executiveId;
        $this->private = $private;
        $this->LMRId = $LMRId;

        $this->lockCFPBFileInfoArray = $lockCFPBFileInfoArray;
        $this->taskEmpInfo = $taskEmpInfo;
        $this->taskBranchInfo = $taskBranchInfo;
        $this->taskAgentInfo = $taskAgentInfo;
        $this->notesInfoArray = $notesInfoArray;
        $this->fileActiveStatus = $fileActiveStatus;
        $this->RESTReportArray = $RESTReportArray;
        $this->assignedCFPBAuditorInfo = $assignedCFPBAuditorInfo;
        $this->CFPBAuditFileStatus = $CFPBAuditFileStatus;
        $this->LMRExecutiveId = $LMRExecutiveId;

        $this->borrowerFName = $borrowerFName;
        $this->borrowerLName = $borrowerLName;
        $this->brokerNumber = $brokerNumber;
        $this->secondaryBrokerNumber = $secondaryBrokerNumber;
        $this->viewUrl = $viewUrl;
        $this->synergyBillPaidStatus = $synergyBillPaidStatus;
        $this->borrowerName = $borrowerName;
        $this->fileUrl = $fileUrl;
        $this->loanAuditFileStatus = $loanAuditFileStatus;
        $this->brokerProcessingFee = HMLOLoanTermsCalculation::$brokerProcessingFee;
        $this->disclosureSentDate = $fileAdminInfo ? Dates::formatDateWithRE($fileAdminInfo->disclosureSentDate, 'YMD', 'm/d/Y') : null;
        // Broker points may be calculated during loan terms generation. If the
        // calculation has not populated the rate/value, fall back to the raw
        // value loaded with the file info so the pipeline always displays the
        // broker points entered on the loan.
        $this->brokerPointsRate = $fileHMLONewLoanInfo->brokerPointsRate
            ?? LTC2Variables::$brokerPointsRate;
        $this->brokerPointsValue = $fileHMLONewLoanInfo->brokerPointsValue
            ?? HMLOLoanTermsCalculation::$brokerPointsValue;
        $this->originationPointsRate = $fileHMLONewLoanInfo->originationPointsRate
            ?? LTC2Variables::$originationPointsRate;
        $this->originationPointsValue = $fileHMLONewLoanInfo->originationPointsValue
            ?? HMLOLoanTermsCalculation::$originationPointsValue;

        $this->coBorrowerName = $coBorrowerName;
        $this->fundingDate = $fundingDate;
        $this->noOfPropertiesAcquiring = $noOfPropertiesAcquiring;
        $this->requiredInsurance = $myFileInfoObject->requiredInsuranceInfo($fileHMLOPropertyInfo[$LMRId]['proInsType']) ?? null;
        $this->clearToCloseBy = $myFileInfoObject->creditDecisionClearToCloseBy() ?? null;
        $this->HMDAActionTaken = glAdverseAction::$glAAActionTaken[$adverseActionInfo->HMDAActionTaken] ?? null;
        $this->titleOrderDate = $titleOrderDate;
        $this->lenderInternalNotes = $fileResponseInfo->lenderInternalNotes;
        $this->wireAmountSent = Currency::formatDollarAmountWithDecimal($fundingWire->wireAmountSent);
        $this->warehouseInvestor = $fundingWire->warehouseInvestor;
        $this->referralPoints = number_format($referralPoints, 2, '.', '');
        $this->rateLockPeriod = $fileHMLONewLoanInfo->rateLockPeriod ?? null;
        $this->rateLockDate = $fileHMLONewLoanInfo ? Dates::formatDateWithRE($fileHMLONewLoanInfo->rateLockDate, 'YMD', 'm/d/Y') : null;
        $this->LOISentDate = $fileHMLONewLoanInfo ? Dates::formatDateWithRE($fileHMLONewLoanInfo->LOISentDate, 'YMD', 'm/d/Y') : null;
        $this->rateLockExpirationDate = $fileHMLONewLoanInfo ? Dates::formatDateWithRE($fileHMLONewLoanInfo->rateLockExpirationDate, 'YMD', 'm/d/Y') : null;
        $this->rateLockExtension = $fileHMLONewLoanInfo->rateLockExtension;
        $this->rateLockNotes = $fileHMLONewLoanInfo->rateLockNotes;
        $this->eCoaWaiverStatus = $fileResponseInfo->eCoaWaiverStatus;
        $this->brokerPartnerType = $brokerPartnerType;
        $this->totalPropertiesLoanAmount = Currency::formatDollarAmountWithDecimal($myFileInfoObject->getLoanPropertySummary()->totalPropertiesLoanAmount);
        $this->totalPropertiesPITIA = Currency::formatDollarAmountWithDecimal($myFileInfoObject->getLoanPropertySummary()->totalPropertiesPITIA);
        $this->createdDateWithTimestamp = $createdDateWithTimestamp;
        $tblProcessingCompany = tblProcessingCompany::get(['PCID' => $filePCID]);
        $this->showSysGenNote =  $tblProcessingCompany->showSysGenNote;
        $this->MERSID = $MERSID;
        $this->PSAClosingDate = $fileHMLONewLoanInfo ? Dates::formatDateWithRE($fileHMLONewLoanInfo->PSAClosingDate, 'YMD', 'm/d/Y') : null;
        $this->buildingAnalysisOutstanding = $fileHMLONewLoanInfo ? $fileHMLONewLoanInfo->getTblBuildingAnalysisOutstanding_by_id()->buildingAnalysisOutstandingName : null;
        $this->buildingAnalysisNeed = $fileHMLONewLoanInfo ? $fileHMLONewLoanInfo->getTblBuildingAnalysisNeed_by_id()->buildingAnalysisNeedName : null;
        $this->buildingAnalysisDueDate = $fileAdminInfo ? Dates::formatDateWithRE($fileHMLONewLoanInfo->buildingAnalysisDueDate, 'YMD', 'm/d/Y') : null;
        $this->targetSubmissionDate = $fileAdminInfo ? Dates::formatDateWithRE($fileAdminInfo->targetSubmissionDate, 'YMD', 'm/d/Y') : null;
        $this->authorizationStatus = $fileHMLOInfoObj ? $fileHMLOInfoObj->getTblBorrowerAuthorizationStatus_by_authorizationStatus()->status_description : null;
        $this->VOMStatus = (isset($refinanceMortgageInfoPrimaryObj)
            && $refinanceMortgageInfoPrimaryObj->getTblVOMPayoffStatus_by_VOMStatus())
            ? $refinanceMortgageInfoPrimaryObj->getTblVOMPayoffStatus_by_VOMStatus()->VOMPayoffStatusDescription
            : null;

        $this->payoffStatus = (isset($refinanceMortgageInfoPrimaryObj)
            && $refinanceMortgageInfoPrimaryObj->getTblVOMPayoffStatus_by_payoffStatus())
            ? $refinanceMortgageInfoPrimaryObj->getTblVOMPayoffStatus_by_payoffStatus()->VOMPayoffStatusDescription
            : null;

        $this->trackRecord = (isset($myFileInfoObject)
            && $myFileInfoObject->fileHMLOExperienceInfo()
            && $myFileInfoObject->fileHMLOExperienceInfo()->getTblBorrowerExperienceTrackRecord_by_trackRecord())
            ? $myFileInfoObject->fileHMLOExperienceInfo()->getTblBorrowerExperienceTrackRecord_by_trackRecord()->track_record_description
            : null;

        $this->welcomeCallStatus = (isset($fileAdminInfo)
            && $fileAdminInfo->getTblWelcomeCallStatus_by_welcomeCallStatus())
            ? $fileAdminInfo->getTblWelcomeCallStatus_by_welcomeCallStatus()->callStatus
            : null;


        $this->propertyAppraisalStatus = (isset($propertyAppraisalDetails)
            && $propertyAppraisalDetails->getTblPropertyAppraisalStatuses_by_id())
            ? $propertyAppraisalDetails->getTblPropertyAppraisalStatuses_by_id()->appraisalStatusesName
            : null;

        $this->targetClosingDate = $targetClosingDate;
    }
}
