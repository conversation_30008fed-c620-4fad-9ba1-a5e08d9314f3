<?php

namespace models\Controllers\LMRequest;

use models\composite\oClient\updateFileInfo;
use models\composite\oContacts\saveContacts;
use models\composite\oContacts\saveFileContacts;
use models\composite\oFileDoc\HtmlPdfLMRFileDoc;
use models\composite\oHMLOInfo\saveHMLOBorrowerInfo;
use models\composite\oIncExp\creditorsLiabilitiesSave;
use models\composite\oIncExp\saveAssetsInfo;
use models\composite\oIncExp\saveIncomeInfo;
use models\composite\oLoanOrigination\saveFinanceAndSecurities;
use models\composite\oLoanOrigination\scheduleRealEstateSave;
use models\lendingwise\tblFileWebFormEsignDetails;
use models\cypher;
use models\Resque\AutomatedHTMLPdfTask;
use models\standard\Dates;
use models\standard\Strings;
use models\types\strongType;

class PFSWebForm extends strongType
{
    public static ?int $LMRId = null;
    public static ?int $PCID = null;
    public static ?int $clientId = null;
    public static ?int $isSysNotesPrivate = null;
    public static ?int $LMRResponseId = null;
    public static ?string $borrowerName = null;
    public static ?string $FileCreatedDate = null;
    public static ?array $contactInfo = [];
    public static ?array $CIDArray = [];
    public static ?array $CIDArray1 = [];
    public static ?array $CIDnewArray = [];
    public static ?array $WebFormEsignInfo = null;
    public static ?bool $checkPdfDocStatus = null;
    public static ?string $uploadDocUrl = null;
    public static ?string $folderName = null;

    public static ?string $t = null;
    public static ?bool $isPDFFinished = null;
    public static ?string $jobId = null;
    public static ?bool $isPFSWebForm = false;

    /**
     * @param $LMRId
     * @return void
     */
    public static function init($LMRId)
    {
        self::$WebFormEsignInfo = tblFileWebFormEsignDetails::getData($LMRId, 'PFS');
        if (PFSWebForm::$t && HTML_PDFCROWD) {
            self::getPDF();
        }
    }

    /**
     * @return void
     */
    public static function Post()
    {
        self::$LMRId = cypher::myDecryption($_REQUEST['LMRId']) ?: null;
        self::$FileCreatedDate = cypher::myDecryption($_REQUEST['recordDate']) ?: null;
        self::$PCID = cypher::myDecryption($_REQUEST['encPCID']) ?: null;
        self::$clientId = cypher::myDecryption($_REQUEST['clientId']) ?: null;
        self::$borrowerName = cypher::myDecryption($_REQUEST['borrowerName']) ?: null;
        self::$isSysNotesPrivate = cypher::myDecryption($_REQUEST['isSysNotesPrivate']) ?: null;
        self::$LMRResponseId = cypher::myDecryption($_REQUEST['LMRResponseId']) ?: null;

        accountantInfo::save(self::$LMRId);
        if (accountantInfo::$ip) {
            self::$contactInfo = array_merge(self::$contactInfo, accountantInfo::$ip);
        }
        if (accountantInfo::$CIDArray) {
            self::$CIDArray = array_merge(self::$CIDArray, accountantInfo::$CIDArray);
        }

        attorneyInfo::save(self::$LMRId);
        if (attorneyInfo::$ip) {
            self::$contactInfo = array_merge(self::$contactInfo, attorneyInfo::$ip);
        }
        if (attorneyInfo::$CIDArray) {
            self::$CIDArray = array_merge(self::$CIDArray, attorneyInfo::$CIDArray);
        }

        $borrowerInfo = [];

        if (isset($_POST['borrowerFName'])) {
            $borrowerInfo['borrowerName'] = $_POST['borrowerFName'];
            $borrowerInfo['enc_borrowerName'] = cypher::myEncryption(($_POST['borrowerFName']));
        }
        if (isset($_POST['borrowerLName'])) {
            $borrowerInfo['borrowerLName'] = $_POST['borrowerLName'];
            $borrowerInfo['enc_borrowerLName'] = cypher::myEncryption(($_POST['borrowerLName']));
        }
        $borrowerInfo['LMRId'] = self::$LMRId;
        updateFileInfo::getReport($borrowerInfo);


        //Financial AdvisorInfo
        financialAdvisorInfo::save(self::$LMRId);
        if (financialAdvisorInfo::$ip) {
            self::$contactInfo = array_merge(self::$contactInfo, financialAdvisorInfo::$ip);
        }
        if (financialAdvisorInfo::$CIDArray) {
            self::$CIDArray = array_merge(self::$CIDArray, financialAdvisorInfo::$CIDArray);
        }
        // End of Financial AdvisorInfo


        // Insurance Agent Information
        insuranceAgentInfo::save(self::$LMRId);
        // End of Insurance Agent Information


        if (count(self::$contactInfo) > 0) {
            $inArray['PCID'] = self::$PCID;
            $inArray['contacts'] = self::$contactInfo;
            self::$CIDArray1 = saveContacts::getReport($inArray);
        }
        self:: $CIDArray = array_unique(array_merge(self::$CIDArray, self::$CIDArray1), SORT_REGULAR);

        foreach (self::$CIDArray as $CIDkey => $CIDData) {
            self::$CIDnewArray[$CIDkey] = $CIDData;
        }
        self::$CIDnewArray = array_values(self::$CIDnewArray);

        if (count(self::$CIDnewArray) > 0) {
            saveFileContacts::getReport([
                'LMRId' => self::$LMRId,
                'CID' => self::$CIDnewArray,
            ]);
        }


        saveIncomeInfo::getReport([
            'p' => $_REQUEST,
            'LMRId' => self::$LMRId,
        ]);
        saveFinanceAndSecurities::getReport([
            'p' => $_REQUEST,
            'LMRId' => self::$LMRId,
        ]);

        $scheduleestate = [
            'LMRId' => self::$LMRId,
            'schedulePropAddr' => $_POST['schedulePropAddr'],
            'schedulePropCity' => $_POST['schedulePropCity'],
            'schedulePropState' => $_POST['schedulePropState'],
            'schedulePropZip' => $_POST['schedulePropZip'],
            'scheduleStatus' => $_POST['scheduleStatus'],
            'propType' => $_POST['propType'],
            'presentMarketValue' => $_POST['presentMarketValue'],
            'amountOfMortgages' => $_POST['amountOfMortgages'],
            'grossRentalIncome' => $_POST['grossRentalIncome'],
            'mortgagePayments' => $_POST['mortgagePayments'],
            'insMaintTaxMisc' => $_POST['insMaintTaxMisc'],
            'netRentalIncome' => $_POST['netRentalIncome'],
            'scheduleID' => $_POST['scheduleID'],
            'titledUnder' => $_POST['titledUnder'],
            'datePurchased' => $_POST['datePurchased'],
            'purchasePrice' => $_POST['purchasePrice'],
            'valueofImprovementsMade' => $_POST['valueofImprovementsMade'],
            'intendedOccupancy' => $_POST['intendedOccupancy'],
            'p' => $_POST,
        ];
        scheduleRealEstateSave::getReport($scheduleestate);
        partnerShips::saveData($_REQUEST['partnerShipFields'], self::$LMRId);

        saveAssetsInfo::getReport([
            'LMRId' => self::$LMRId,
            'p' => $_POST,
            'PCID' => self::$PCID,
        ]);

        creditorsLiabilitiesSave::getReport(['LMRId' => self::$LMRId]);
        saveHMLOBorrowerInfo::getReport([
            'LMRId' => self::$LMRId,
            //'saveTab' => 'LR',
            'p' => $_POST,
            'PCID' => self::$PCID,
        ]);

        signatureForm::$LMRId = self::$LMRId;
        signatureForm::$formType = 'PFS';
        signatureForm::signatureSave();
        if (HTML_PDFCROWD) {
            self::generatePDF();
        }
        Strings::SetSess('msg', 'Your submission was successful');

    }

    /**
     * @return void
     */


    public static function generatePDF()
    {
        $uniqueId = Dates::Timestamp(null, null, 'YmdHis');
        $docName = 'PFS Webform_' . self::$borrowerName . '_' . date('M/d/Y') . '_' . date('H:i') . '.pdf';
        self::$t = AutomatedHTMLPdfTask::createTask([
            'LMRId' => self::$LMRId,
            'PCID' => self::$PCID,
            'fileCreatedDate' => self::$FileCreatedDate,
            'oldFPCID' => self::$PCID,
            'userGroup' => 'Client',
            'userNumber' => self::$clientId,
            'borrowerLName' => self::$borrowerName,
            'userName' => 'Client',
            'uploadedBy' => self::$clientId,
            'docName' => cypher::myEncryption($docName),
            'docCategory' => 'WebForm PDF',
            'isSysNotesPrivate' => self::$isSysNotesPrivate,
            'recordDate' => self::$FileCreatedDate,
            'LMRResponseId' => self::$LMRResponseId,
            'webFormURL' => $_SERVER['HTTP_REFERER'],
            'ipAddress' => $_SERVER['REMOTE_ADDR'],
            'uniqueJobId' => $uniqueId,
        ]);
    }

    public static function getPDF()
    {
        self::$jobId = self::$t;

        $job = AutomatedHTMLPdfTask::getTask(self::$t);

        if (is_object($job)) {
            self::$checkPdfDocStatus = true;
            $ResqueData = json_decode($job->params, true);
            self::$isPDFFinished = (bool)$job->completed_at;
            $uniqueJobId = $ResqueData['uniqueJobId'];
            $htmlPDFDocInfo = HtmlPdfLMRFileDoc::getReport(['uniqueJobId' => $uniqueJobId]);
            if (sizeof($htmlPDFDocInfo) > 0) {
                HtmlPdfLMRFileDoc::update([
                    'uniqueJobId' => $uniqueJobId,
                    'tokenId' => self::$jobId,
                ]);
            }
        } else {
            self::$checkPdfDocStatus = false;
            $htmlPDFDocInfo = HtmlPdfLMRFileDoc::getReport(['tokenId' => self::$jobId]);
        }
        self::$folderName = self::$PCID . '/'
            . date('Y', strtotime(self::$FileCreatedDate)) . '/'
            . date('m', strtotime(self::$FileCreatedDate)) . '/'
            . date('d', strtotime(self::$FileCreatedDate));

        self::$folderName .= '/' . self::$LMRId . '/upload/';

        self::$uploadDocUrl = CONST_URL_BOSSL . 'viewDocuments.php?fn='
            . cypher::myEncryption($htmlPDFDocInfo['docName'])
            . '&fd=' . cypher::myEncryption(CONST_PATH_LMR_FILE_DOCS . self::$folderName)
            . '&opt=enc&dn=' . cypher::myEncryption(str_replace(' ', '_', $htmlPDFDocInfo['docName']));

    }

}
