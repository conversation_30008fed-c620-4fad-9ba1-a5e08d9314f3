<?php

namespace models\lendingwise;

use models\lendingwise\db\tblPCChecklistCategory_db;

/**
 *
 */
class tblPCChecklistCategory extends tblPCChecklistCategory_db
{

    public static function getPCCategories(?int $PCID, ?int $activeStatus = null): array
    {

        $sql = 'select * from tblPCChecklistCategory where PCID IS NULL or PCID = :PCID';
        $params = [
            'PCID' => $PCID,
        ];
        if ($activeStatus) {
            $sql .= ' and activeStatus = :activeStatus';
            $params['activeStatus'] = $activeStatus;
        }

        $sql .= ' order by `order`';
        return self::queryData($sql, $params, function ($row) {
            return new self($row);
        });
    }
}
