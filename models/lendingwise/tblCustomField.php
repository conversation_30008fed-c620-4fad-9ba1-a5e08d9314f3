<?php

namespace models\lendingwise;

use models\Controllers\loanForm;
use models\CustomField;
use models\lendingwise\db\tblCustomField_db;
use models\lendingwise\db\tblCustomFieldType_db;
use models\PageVariables;
use models\standard\Dates;
use models\standard\Strings;

/**
 *
 */
class tblCustomField extends tblCustomField_db
{
    /**
     * @var self[]
     */
    private static ?array $_customField = null;
    private ?array $_fileTypes = null;
    private ?array $_loanPrograms = null;

    private ?array $_additionalSettings = null;
    private ?string $_fieldId = null;

    public function dataAddress()
    {
        if (is_null($this->_additionalSettings)) {
            $this->_additionalSettings = json_decode($this->additionalSettings, true);
        }

        return $this->_additionalSettings['address'] ?? [];
    }

    // convert user input to array and JSON string
    public static function getOptions($options)
    {
        if ($options) {
            $list = explode(';', $options);
            foreach ($list as $i => $v) {
                $list[$i] = trim($v);
            }
            $options = json_encode($list, JSON_PRETTY_PRINT);
        }

        return $options;
    }

    public function Save(): array
    {
        if (PageVariables::$userNumber) {
            $this->createdBy = PageVariables::$userNumber;
            $this->createdByRole = PageVariables::$userRole;
            $this->createdByGroup = PageVariables::$userGroup;
        }
        $this->createdAt = Dates::Timestamp();
        if ($this->Width < 0 || $this->Width > 12) {
            $this->Width = 0;
        }

        return parent::Save();
    }

    public function _loanPrograms()
    {
        if (is_null($this->_loanPrograms)) {
            $this->_loanPrograms = json_decode($this->loanProgram, true);
        }
        return $this->_loanPrograms ?? [];
    }

    public function _fileTypes(): array
    {
        if (is_null($this->_fileTypes)) {
            $this->_fileTypes = json_decode($this->fileType, true);
        }
        return $this->_fileTypes ?? [];
    }

    public function saveEntry(
        string  $objectType,
        string  $primaryKey,
                $value,
        ?int    $createdBy,
        ?string $createdByRole,
        ?string $createdByGroup
    )
    {
        $check = tblCustomFieldValue::Get([
            'tblCustomFieldId' => $this->id,
            'objectType'       => $objectType,
            'primaryKey'       => $primaryKey,
        ]);

        if (!$check) {
            $check = new tblCustomFieldValue();
        }

        $check->objectType = str_replace('\\', '\\\\', $objectType);
        $check->primaryKey = $primaryKey;
        $check->tblCustomFieldId = $this->id;
        $check->userEntered = json_encode($value, JSON_PRETTY_PRINT);
        $check->createdBy = $createdBy;
        $check->createdByRole = $createdByRole;
        $check->createdByGroup = $createdByGroup;
        $check->setByType($this->tblCustomFieldTypeId);

        switch ($this->tblCustomFieldTypeId) {
            case tblCustomFieldType::Text:
            case tblCustomFieldType::Paragraph:
            case tblCustomFieldType::Email:
            case tblCustomFieldType::State:
            case tblCustomFieldType::Radio:
            case tblCustomFieldType::Select:
            case tblCustomFieldType::Address:
                $check->text = !is_string($value) ? '' : $value;
                break;
            case tblCustomFieldType::Number:
                $check->text = !is_string($value) ? '' : intval($value);
                break;
            case tblCustomFieldType::Currency:
                $check->currency = !is_string($value) ? null : round(Strings::replaceCommaValues($value), 2);
                break;
            case tblCustomFieldType::Percent:
                $check->percent = !is_string($value) ? null : round(floatval($value), 3);
                break;
            case tblCustomFieldType::Date:
                $check->date = !is_string($value) ? null : Dates::Datestamp($value, '');
                break;
            case tblCustomFieldType::Phone:
            case tblCustomFieldType::ZipCode:
            case tblCustomFieldType::SSN:
                $check->number = !is_string($value) ? null : (Strings::NumbersOnly($value) ?: null);
                break;
            case tblCustomFieldType::File:
                $check->fileStorageId = $value ?? null;
                break;

            case tblCustomFieldType::CheckBox: // JSON encoded array in userEntered
            case tblCustomFieldType::SelectMulti:
                break;
        }
        $check->created_at = Dates::Timestamp();
        $check->Save();
    }


    private function renderInput(
        $value = null, // can be string or array
        ?bool $mandatory = false,
        ?array $fileTypes = null,
        ?array $loanPrograms = null
    ): string
    {

        loanForm::$fileLoanPrograms = $this->_loanPrograms();
        loanForm::$fileTypes = $this->_fileTypes();
        loanForm::$sectionId = $this->sectionID;
        loanForm::$customFieldId = $this->id;
        $fieldNameForm = 'CustomField[' . $this->id . ']';
        $fieldIdForm = 'CustomField_' . $this->id;

        $this->_fieldId = $fieldIdForm;

        $html = '';
        switch ($this->tblCustomFieldTypeId) {
            case tblCustomFieldType::Header:
                $html .= loanForm::simpleHeader();
                break;

            case tblCustomFieldType::CheckBox:
                $options = json_decode($this->Options, true);
                foreach ($options as $option) {
                    $option = trim($option);
                    $html .= loanForm::simpleCheckbox(
                        $fieldNameForm . '[]',
                        $mandatory ? 'mandatory' : '',
                        '',
                        '',
                        $value && ((is_string($value) && $option == $value) || in_array($option, $value)),
                        $option,
                        $option,
                        $fieldIdForm
                    );
                }
                break;
            case tblCustomFieldType::Currency:
                $html .= loanForm::simpleCurrency(
                    $fieldIdForm,
                    $fieldNameForm,
                    $value,
                    $mandatory ? 'mandatory' : '',
                    '',
                    ''
                );
                break;
            case tblCustomFieldType::Date:
                $html .= loanForm::simpleDate(
                    $fieldIdForm,
                    $fieldNameForm,
                    $value,
                    $mandatory ? 'mandatory' : ''
                );
                break;
            case tblCustomFieldType::Email:
                $html .= loanForm::simpleEmail(
                    $fieldIdForm,
                    $fieldNameForm,
                    $value,
                    $mandatory ? 'mandatory' : ''

                );
                break;
            case tblCustomFieldType::Number:
                $html .= loanForm::simpleNumber(
                    $fieldNameForm,
                    true,
                    0,
                    $value,
                    '',
                    $mandatory ? 'mandatory' : '',
                    $fieldIdForm
                );
                break;
            case tblCustomFieldType::Paragraph:
                $html .= loanForm::simpleTextArea(
                    $fieldIdForm,
                    $fieldNameForm,
                    $value,
                    $mandatory ? 'mandatory' : ''
                );
                break;
            case tblCustomFieldType::Percent:
                $html .= loanForm::simplePercentage(
                    $fieldIdForm,
                    $fieldNameForm,
                    $value,
                    $mandatory ? 'mandatory' : '',
                    'customFieldsCheckDecimal(this)'
                );
                break;
            case tblCustomFieldType::Phone:
                $html .= loanForm::simplePhone(
                    $fieldIdForm,
                    $fieldNameForm,
                    $value,
                    $mandatory ? 'mandatory' : ''
                );
                break;
            case tblCustomFieldType::State:
                $html .= loanForm::simpleState(
                    $fieldNameForm,
                    $fieldIdForm,
                    $value,
                    $mandatory ? 'mandatory' : ''
                );
                break;

            case tblCustomFieldType::Country:
                $html .= loanForm::simpleCountry(
                    $fieldNameForm,
                    $fieldIdForm,
                    $value,
                    $mandatory ? 'mandatory' : ''
                );
                break;

            case tblCustomFieldType::File:
                $html .= loanForm::simpleFile(
                    $fieldIdForm,
                    $fieldNameForm,
                    $value,
                    ($mandatory ? 'mandatory' : '') . ' '
                );
                break;

            case tblCustomFieldType::Address:
                $html .= '
                <script>
                    $(document).ready(function() {
                        $("#CustomField_' . $this->id . '").on("input", function() {
                            address_lookup.InitLegacy($(this));
                        });
                    });
                </script>
            ';


                $html .= loanForm::simpleText(
                    $fieldIdForm,
                    $fieldNameForm,
                    $value,
                    $mandatory ? 'mandatory' : '',
                    null,
                    $this->dataAddress()
                );
                break;

            case tblCustomFieldType::SSN:
                $html .= loanForm::simpleText(
                    $fieldIdForm,
                    $fieldNameForm,
                    $value,
                    'mask_ssn' . ($mandatory ? ' mandatory' : ''),
                    null,
                    null,
                    '___ - __ - ____'
                );
                break;

            case tblCustomFieldType::Radio:
                $options = json_decode($this->Options, true);
                foreach ($options as $option) {
                    $option = trim($option);
                    $html .= loanForm::simpleRadio(
                        $fieldNameForm,
                        $mandatory ? 'mandatory' : '',
                        '',
                        '',
                        $option == $value,
                        $option,
                        $option,
                        $fieldIdForm,

                    );
                }
                break;
            case tblCustomFieldType::Select:
                $options = json_decode($this->Options, true);
                $list = [];
                foreach ($options as $option) {
                    $option = trim($option);
                    $list[$option] = $option;
                }
                $html .= loanForm::simpleSelect(
                    $fieldNameForm,
                    true,
                    0,
                    $value,
                    $list,
                    '',
                    $mandatory ? 'mandatory' : '',
                    'Select One..',
                    '',
                    $fieldIdForm
                );
                break;
            case tblCustomFieldType::SelectMulti:
                $options = json_decode($this->Options, true);
                $list = [];
                foreach ($options as $option) {
                    $option = trim($option);
                    $list[$option] = $option;
                }
                $html .= loanForm::simpleSelect(
                    'CustomField[' . $this->id . '][]',
                    true,
                    0,
                    $value,
                    $list,
                    '',
                    $mandatory ? 'mandatory' : '',
                    'Select One..',
                    '',
                    $fieldIdForm,
                    true
                );
                break;
            case tblCustomFieldType::Text:
                $html .= loanForm::simpleText(
                    $fieldIdForm,
                    $fieldNameForm,
                    $value,
                    $mandatory ? 'mandatory' : ''
                );
                break;
            case tblCustomFieldType::ZipCode:
                $html .= loanForm::simpleZipCode(
                    $fieldIdForm,
                    $fieldNameForm,
                    $value,
                    $mandatory ? 'mandatory' : ''
                );
                break;
        }

        loanForm::$fileLoanPrograms = $loanPrograms;
        loanForm::$fileTypes = $fileTypes;

        return $html;
    }

    public function render(
        $value = null, // can be string or array
        ?bool $mandatory = false,
        ?bool $edit = false,
        ?array $fileTypes = null,
        ?array $loanPrograms = null,
        ?bool $isFieldAllowToDisplay = true
    ): string
    {
        $isFieldAllowToDisplayStyle = !$isFieldAllowToDisplay ? 'display:none;' : '';
        $class_d_flex = $isFieldAllowToDisplay ? ' d-flex ' : '';
        $inputHTML = $this->renderInput($value, $mandatory,$fileTypes,$loanPrograms);
        $toolTipHTML = '';
        if ($this->ToolTip) {
            $toolTipHTML = '&nbsp;<i class="popoverClass fas fa-info-circle text-primary"
                           data-html="true"
                           data-content="' . htmlentities($this->ToolTip) . '"></i>';
        }

        if ($this->LabelWidth) {
            return '
            <div 
            class="form-group form-field-header col-' . $this->LabelWidth . '"
            id="' . $this->_fieldId . '_label"
            style="'.$isFieldAllowToDisplayStyle.'"
            >
                <label class="' . $this->_fieldId . '" for="' . $this->_fieldId . '">
                    ' . $this->Label . $toolTipHTML . '
                </label>
            </div>
            <div 
            class="form-group form-field-options col-' . $this->Width . ' align-items-center"
            id="' . $this->_fieldId . '_input"
            style="'.$isFieldAllowToDisplayStyle.'"
            >' . $inputHTML . '</div>
            ';
        }

        return '
            <div 
            class="form-group form-field-header col-' . ($this->Width ?: 3) . '"
            id="' . $this->_fieldId . '_container"
            style="'.$isFieldAllowToDisplayStyle.'"
            >
                <label class="' . $this->_fieldId . '" for="' . $this->_fieldId . '">
                    ' . $this->Label . $toolTipHTML . '
                    ' . ($edit ? '<a href="/backoffice/settings/custom_fields/loan_tab?sectionID=' . $this->sectionID . '&id=' . $this->id . '"><i class="fa fa-edit"></i></a>' : '') . '
                </label>
            </div>
            <div 
            class="form-group form-field-options col-' . $this->Width . ' align-items-center"
            id="' . $this->_fieldId . '_input"
            style="'.$isFieldAllowToDisplayStyle.'"
            >' . $inputHTML . '</div>
        ';
    }

    public static function getCached(int $id)
    {
        if (!isset(self::$_customField[$id])) {
            self::$_customField[$id] = self::Get(['id' => $id]);
        }
        return self::$_customField[$id];
    }

    private ?tblCustomFieldType $_TblCustomFieldType_by_id = null;

    public function getTblCustomFieldType_by_id(): ?tblCustomFieldType
    { // do not make a foreign key in the database for this column
        if (is_null($this->_TblCustomFieldType_by_id)) {
            $this->_TblCustomFieldType_by_id = tblCustomFieldType::Get([
                tblCustomFieldType_db::COLUMN_ID => $this->tblCustomFieldTypeId,
            ]);
        }
        return $this->_TblCustomFieldType_by_id;
    }
}
