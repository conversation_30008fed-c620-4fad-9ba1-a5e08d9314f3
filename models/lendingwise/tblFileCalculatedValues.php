<?php

namespace models\lendingwise;

use Exception;
use models\composite\LoanAmortization;
use models\composite\LoanPaymentDue;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\loanPropertySummary;
use models\Controllers\LMRequest\PropertyAnalysis;
use models\HMLOLoanTermsCalculation;
use models\HMLOLoanTermsCalculation\LTC2Variables;
use models\lendingwise\db\tblFileCalculatedValues_db;
use models\myFileInfo\fileHMLONewLoanInfo;
use models\Request;
use models\standard\Dates;
use models\standard\Strings;

/**
 *
 */
class tblFileCalculatedValues extends tblFileCalculatedValues_db
{
    protected static bool $_has_change_log = true; // maybe - verify it doesn't blow up the change log 11/20

    public static function GetCalculatedValuesForLoan(
        int $LMRId
    ): tblFileCalculatedValues
    {
        LMRequest::$LMRId = null;
        LMRequest::setLMRId($LMRId);
        PropertyAnalysis::updatePropertiesAnalysisValues($LMRId);
        loanPropertySummary::updateLoanInfoV2Values($LMRId);
        fileHMLONewLoanInfo::calculateYieldSpread($LMRId);
        HMLOLoanTermsCalculation::InitForLMRId($LMRId);

        $calc = tblFileCalculatedValues::Get(['LMRId' => $LMRId]);
        if (!$calc) {
            $calc = new tblFileCalculatedValues();
        }
        $calc->LMRId = $LMRId;


//        public ?float $TotalAssets = null; //decimal(18,2)
//        public ?float $TotalOwed = null; //decimal(18,2)
//        public ?float $TotalNetWorth = null; //decimal(18,2)
//        public ?float $NetMonthlyPaymentPITI = null; //decimal(18,2)
//        public ?float $TotalProjectCost = null; //decimal(18,2)
//        public ?float $SimpleARV = null; //decimal(18,2)
//        public ?float $FullARV = null; //decimal(18,2)
//        public ?float $AcquisitionLTV = null; //decimal(18,2)
//        public ?float $MarketLTV = null; //decimal(18,2)
//        public ?float $LoanToCost = null; //decimal(18,2)
//        public ?float $GrossProfit = null; //decimal(18,2)
//        public ?float $GrossProfitMargin = null; //decimal(18,2)
//        public ?float $PerDiemDays = null; //decimal(18,2)
//        public ?float $PerDiemInterestAmount = null; //decimal(18,2)
//        public ?float $PerDiemInterestPercent = null; //decimal(18,2)
//        public ?float $TotalPerDiemInterestAmount = null; //decimal(18,2)
//        public ?float $TotalFeesAndCosts = null; //decimal(18,2)
//        public ?float $NetLenderFundsToTitle = null; //decimal(18,2)
//        public ?float $TotalCashToClose = null; //decimal(18,2)
//        public ?float $CurrentEscrowBalance = null; //decimal(18,2)

        $calc->TotalAssets = HMLOLoanTermsCalculation::$TotalAssets;

        $calc->TotalOwed = HMLOLoanTermsCalculation::$TotalOwed;
        $calc->TotalAutoMobiles = HMLOLoanTermsCalculation::$TotalAutoMobiles;
        $calc->TotalRehabCost = HMLOLoanTermsCalculation::$TotalRehabCost;

        $calc->ApplicationsSubmitted = HMLOLoanTermsCalculation::$ApplicationsSubmitted;
        $calc->ApplicationsApproved = HMLOLoanTermsCalculation::$ApplicationsApproved;
        $calc->TotalAmountApproved = HMLOLoanTermsCalculation::$TotalAmountApproved;

        $calc->AvailableBudget = HMLOLoanTermsCalculation::$availableBudget;

        $calc->TotalDrawsFunded = HMLOLoanTermsCalculation::$totalDrawsFunded;

        $calc->NetOperatingIncome = HMLOLoanTermsCalculation::$netOperatingIncome;

        $calc->TotalNetWorth = HMLOLoanTermsCalculation::$TotalNetWorth;

        $calc->TotalLoanAmount = HMLOLoanTermsCalculation::$totalLoanAmount;
        $calc->CurrentLoanBalance = HMLOLoanTermsCalculation::$currentLoanBalance;

        $calc->NetLenderFundsToTitle = Strings::replaceCommaValues(HMLOLoanTermsCalculation::$netLenderFundsToBorrower);
        $calc->CurrentEscrowBalance = HMLOLoanTermsCalculation::$availableBudget;
        $calc->NetMonthlyPaymentPITI = Strings::replaceCommaValues(HMLOLoanTermsCalculation::$netMonthlyPayment);
        $calc->TotalProjectCost = Strings::replaceCommaValues(HMLOLoanTermsCalculation::$totalProjectCost);
        $calc->SimpleARV = HMLOLoanTermsCalculation::$simpleARV;
        $calc->FullARV = HMLOLoanTermsCalculation::$ARV;
        $calc->AcquisitionLTV = HMLOLoanTermsCalculation::$acquisitionLTV;
        $calc->MarketLTV = HMLOLoanTermsCalculation::$marketLTV;
        $calc->LoanToCost = HMLOLoanTermsCalculation::$LTC;
        $calc->GrossProfit = HMLOLoanTermsCalculation::$grossProfit;
        $calc->GrossProfitMargin = HMLOLoanTermsCalculation::$grossProfitMargin;
        $calc->PerDiemDays = HMLOLoanTermsCalculation::$dailyEstPerDiemArray['diemDays'];
        $calc->PerDiemInterestAmount = HMLOLoanTermsCalculation::$dailyEstPerDiemArray['totalDailyInterestCharge'];
        $calc->PerDiemInterestPercent = HMLOLoanTermsCalculation::$lien1Rate;
        $calc->TotalPerDiemInterestAmount = HMLOLoanTermsCalculation::$totalEstPerDiem;
        $calc->TotalFeesAndCosts = Strings::replaceCommaValues(HMLOLoanTermsCalculation::$totalFeesAndCost);
        $calc->TotalCashToClose = Strings::replaceCommaValues(HMLOLoanTermsCalculation::$totalCashToClose);
        $calc->LTC_MarketValue = HMLOLoanTermsCalculation::$LTCMarketValue;
        $calc->LTC_InitialLoanAmount = HMLOLoanTermsCalculation::$LTCInitialLoanAmount;
        $calc->LTC_SoftHardCost = HMLOLoanTermsCalculation::$NewLoanToCost;
        $calc->InitialLoanAmount = HMLOLoanTermsCalculation::$initialLoanAmount;
        $calc->doRecalculate = 0;

        $calc->CurrentDTI = HMLOLoanTermsCalculation::$currentDTI;
        $calc->debtServiceRatio = HMLOLoanTermsCalculation::$debtServiceRatio;
        $calc->debtServiceRatioPITIA = HMLOLoanTermsCalculation::$debtServiceRatioPITIA;
        $calc->totalRequiredReserves = HMLOLoanTermsCalculation::$totalRequiredReserves;
        $calc->totalCashOutAmt = HMLOLoanTermsCalculation::$totalCashOutAmt;
        $calc->newTotalProjectCost = HMLOLoanTermsCalculation::$NewTotalProjectCost;
        $calc->CurrentLoanBalance = HMLOLoanTermsCalculation::$currentLoanBalance;
        $calc->paymentReservesAmt = HMLOLoanTermsCalculation::$paymentReservesAmt;

        $calc->PayOffAmount = HMLOLoanTermsCalculation::$payOffAmount;
        $calc->totalFeesAndCost = HMLOLoanTermsCalculation::$totalFeesAndCost;

        $loanServicing = LMRequest::myFileInfo()->tblFile()->getLoanServicing();
        $loanServicing->checkValid();

        $calc->isValidForServicing = $loanServicing->isInvalid ? 0 : 1;
        $calc->PaymentFrequency = LMRequest::myFileInfo()->tblFile()->getTblFileHMLONewLoanInfo_by_fileID()->paymentFrequency;

        $calc->LTC2_additionalReserveInterest = LTC2Variables::$LTC2_additionalReserveInterest;
        $calc->LTC2_additionalOriginationInterest = LTC2Variables::$LTC2_additionalOriginationInterest;

        if (!$loanServicing->isInvalid) {
            try {
                LoanAmortization::ConvertToServicing($loanServicing);
            } catch (Exception $e) {
                Debug($e);
            }

            $PaymentDueDate = Dates::Datestamp(LoanPaymentDue::GetPaymentDueDate($loanServicing), '');
            $PaymentDate = $PaymentDueDate;

            $paymentDue = LoanPaymentDue::GetPaymentDue(
                $loanServicing,
                $PaymentDueDate,
                $PaymentDate
            );
            $calc->PaymentNextDueDate = $paymentDue->DueDate;
            $calc->PaymentLastPaymentDate = $paymentDue->LastPaymentDate;
            $calc->PaymentTotal = $paymentDue->Amount;
            $calc->PaymentPrincipal = $paymentDue->Principal;
            $calc->PaymentInterest = $paymentDue->Interest;
        } else {
            $calc->PaymentNextDueDate = null;
            $calc->PaymentLastPaymentDate = null;
            $calc->PaymentTotal = null;
            $calc->PaymentPrincipal = null;
            $calc->PaymentInterest = null;
        }

        $calc->Save();

        PropertyAnalysis::updatePropertiesAnalysisValues($LMRId);
        loanPropertySummary::updateLoanInfoV2Values($LMRId);


        return $calc;
    }
}
