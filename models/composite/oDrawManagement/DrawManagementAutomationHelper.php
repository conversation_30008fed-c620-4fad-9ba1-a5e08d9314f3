<?php

namespace models\composite\oDrawManagement;

use models\types\strongType;
use models\Controllers\backoffice\LMRequest;
use models\lendingwise\tblFileDrawRequests;
use models\constants\automationConstants;
use models\standard\Strings;
use models\composite\oUserAccess\getAutomationControlAccess;

/**
 * Helper class to provide draw management data for automation rules
 */
class DrawManagementAutomationHelper extends strongType
{
    /**
     * Get draw management status for automation rule evaluation
     *
     * @param int $LMRId The loan file ID
     * @return array Array containing draw management status information
     */
    public static function getDrawManagementStatus(int $LMRId): array
    {
        $result = [
            'hasDrawRequest' => false,
            'initialSOWStatus' => null,
            'drawRequestStatus' => null,
            'revisionStatus' => null,
            'currentStatus' => null,
            'isInitialSOW' => false,
            'isDrawRequest' => false,
            'isRevision' => false
        ];

        try {
            $drawManager = new DrawRequestManager($LMRId);
            $drawRequest = $drawManager->getDrawRequest();

            if (!$drawRequest) {
                return $result;
            }

            $result['hasDrawRequest'] = true;
            $result['currentStatus'] = $drawRequest->status;

            $isInitialSOW = $drawManager->isInitialScopeOfWork();
            $isDrawRequest = $drawRequest->isDrawRequest;

            if ($isInitialSOW) {
                $result['isInitialSOW'] = true;
                $result['initialSOWStatus'] = $drawRequest->status;
            } elseif ($isDrawRequest) {
                $result['isDrawRequest'] = true;
                $result['drawRequestStatus'] = $drawRequest->status;
            } else {
                $result['isRevision'] = true;
                $result['revisionStatus'] = $drawRequest->status;
            }

        } catch (\Exception $e) {
            error_log("DrawManagementAutomationHelper error: " . $e->getMessage());
        }

        return $result;
    }

    /**
     * Trigger automation rules for draw request status changes.
     * This method determines the PCID and triggers automation rules.
     *
     * @param tblFileDrawRequests $drawRequest The draw request object to trigger automation rules for.
     */
    public static function triggerAutomationRules(tblFileDrawRequests $drawRequest): void
    {
        try {
            global $LMRId, $PCID, $triggerRule, $userTimeZone;

            $LMRId = $drawRequest->LMRId;
            LMRequest::setLMRId($LMRId);
            $PCID = LMRequest::File()->FPCID;
            $triggerRule = 'Yes';
            $userTimeZone = date_default_timezone_get();
            $fileType = LMRequest::myFileInfo()->fileModuleInfo()[0]->moduleCode;
            $ruleType = self::getDrawManagementRuleType($drawRequest);

            if (!$ruleType) {
                return;
            }

            // Get the current user's automation control access setting
            $userNumber = Strings::GetSess('userNumber');
            $userGroup = Strings::GetSess('userGroup');
            $userAutomationControlAccess = 0;

            if ($userNumber && $userGroup === 'Employee') {
                $userAutomationControlAccess = getAutomationControlAccess::getReport($userNumber);
            }

            $_POST['LMRId'] = $LMRId;
            $_POST['PCID'] = $PCID;
            $_POST['triggerRule'] = 'Yes';
            $_POST['lastUpdatedParam'] = $ruleType;
            $_POST['fileRow'] = 'Update';
            $_POST['dataChanged'] = 'Yes';
            $_POST['userAutomationControlAccess'] = $userAutomationControlAccess ?? 0;
            $_REQUEST['fileTypesTxt'] = $fileType;

            require_once __DIR__ . '/../../../public/backoffice/automatedRulesActionController.php';
        } catch (\Exception $e) {
            error_log('Error triggering automation rules: ' . $e->getMessage());

        }
    }

    /**
     * Determine the draw management rule type for automation
     *
     * @return string|null The rule type constant or null if not applicable
     */
    public static function getDrawManagementRuleType(tblFileDrawRequests $drawRequest): ?string
    {
        if (!$drawRequest) {
            return null;
        }

        if ($drawRequest->isDrawRequest == 1) {
            return automationConstants::$automation_DrawRequest;
        } elseif ($drawRequest->isDrawRequest == 0) {
            $approvedSOWCount = \models\lendingwise\tblDrawRequests_h::GetCount([
                'drawId' => $drawRequest->id,
                'status' => 'approved',
                'isDrawRequest' => 0
            ]);

            if ($approvedSOWCount == 0) {
                return automationConstants::$automation_InitialSOW;
            } else {
                return automationConstants::$automation_Revision;
            }
        }

        return null;
    }

}
