<?php
namespace models\composite\oDrawManagement\traits;

use models\composite\oDrawManagement\SowCategory;
use models\composite\oDrawManagement\SowLineItem;
use models\Database2;

/**
 * Trait DefaultTemplateCreator
 *
 * Provides functionality to create default template categories and line items
 * from JSON data when a new template is initialized for a PCID.
 */
trait DefaultTemplateCreator
{
    /**
     * Creates default template categories and line items from JSON data.
     * This method is called when a new template is created for a PCID.
     *
     * @return bool True on success, false on failure.
     */
    private function createDefaultTemplate(): bool
    {
        if (!$this->templateId) {
            return false;
        }

        $defaultData = $this->loadDefaultTemplateData();
        if (empty($defaultData)) {
            return false;
        }

        $db = Database2::getInstance();
        $db->beginTransaction();

        try {
            foreach ($defaultData as $categoryData) {
                // Create category
                $categoryObject = new SowCategory();
                $categoryArray = [
                    'id' => null,
                    'templateId' => $this->templateId,
                    'categoryName' => $categoryData['categoryName'],
                    'description' => $categoryData['description'] ?? '',
                    'order' => $categoryData['order']
                ];

                $categoryObject->save($categoryArray);
                $categoryId = $categoryObject->id;

                // Create line items for this category
                if (isset($categoryData['lineItems']) && is_array($categoryData['lineItems'])) {
                    foreach ($categoryData['lineItems'] as $lineItemData) {
                        $lineItemObject = new SowLineItem();
                        $lineItemArray = [
                            'id' => null,
                            'templateId' => $this->templateId,
                            'categoryId' => $categoryId,
                            'name' => $lineItemData['name'],
                            'description' => $lineItemData['description'] ?? '',
                            'order' => $lineItemData['order']
                        ];

                        $lineItemObject->save($lineItemArray);
                    }
                }
            }

            $db->commit();

            return true;

        } catch (\Exception $e) {
            $db->rollBack();
            error_log("Failed to create default template: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Loads the default template data from JSON file.
     * @return array The default template data array.
     */
    private function loadDefaultTemplateData(): array
    {
        try {
            $jsonFilePath = __DIR__ . '/defaultTemplateData.json';
            $jsonContent = file_get_contents($jsonFilePath);
            $data = json_decode($jsonContent, true);
            return $data ?: [];
        } catch (\Exception $e) {
            error_log("Failed to load default template data: " . $e->getMessage());
            return [];
        }
    }
}
