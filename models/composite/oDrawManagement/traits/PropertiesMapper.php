<?php
namespace models\composite\oDrawManagement\traits;

use models\composite\oDrawManagement\dto\base\BaseDTO;

/**
 * Enhanced Trait PropertiesMapper
 *
 * Provides automated bidirectional property mapping between database objects,
 * class properties, and DTOs. Reduces boilerplate code and ensures consistency.
 */
trait PropertiesMapper
{
    /**
     * Automatically map properties from a database object to class properties.
     * This method uses reflection to map properties with the same names.
     *
     * @param object $dbObject The database object to map from.
     * @param array $propertyMap Optional custom property mapping ['classProperty' => 'dbProperty'].
     * @param string|null $dbObjectProperty Optional property name to store the database object.
     * @return void
     */
    protected function mapPropertiesFromDbObject(
        object $dbObject,
        array $propertyMap = [],
        ?string $dbObjectProperty = null
    ): void {
        $reflection = new \ReflectionClass($this);
        $classProperties = $reflection->getProperties();
        $typeDefaults = ['int' => 0, 'float' => 0.00, 'string' => '', 'bool' => false];
        foreach ($classProperties as $property) {
            $propertyName = $property->getName();
            $type = $property->getType();
            $typeName = $type ? $type->getName() : null;

            if ($dbObjectProperty && $propertyName === $dbObjectProperty) {
                continue;
            }

            if ($property->getDeclaringClass()->getName() !== $reflection->getName()) {
                continue;
            }

            $sourceProperty = $propertyMap[$propertyName] ?? $propertyName;

            if (property_exists($dbObject, $sourceProperty)) {
                $value = $dbObject->$sourceProperty;

                if ($value !== null) {
                    $this->$propertyName = $value;
                }
            }
        }

        if ($dbObjectProperty && property_exists($this, $dbObjectProperty)) {
            $this->$dbObjectProperty = $dbObject;
        }
    }

    /**
     * Generic setProperties method that can be used by most composite models.
     *
     * @param object $dbObject The database object to map from.
     * @param array $customMapping Optional custom property mapping.
     * @param array $defaultValues Optional default values.
     * @return void
     */
    protected function setProperties(object $dbObject, array $customMapping = [], array $defaultValues = []): void
    {
        $className = (new \ReflectionClass($this))->getShortName();
        $dbObjectProperty = $this->getImplementingClassPropertyName($className);

        $dbObjClass = get_class($dbObject);
        $this->mapPropertiesFromDbObject($dbObject, $customMapping, $dbObjectProperty);
    }

    /**
     * Determine the database object property name based on class naming conventions.
     *
     * @param string $className The class name.
     * @return string The expected database object property name.
     */
    private function getImplementingClassPropertyName(string $className): string
    {
        $classToPropertyMap = [
            'DrawRequest' => 'drawRequest',
            'BorrowerDrawCategory' => 'category',
            'BorrowerDrawLineItem' => 'lineItem',
            'DrawRequestsHistory' => 'drawRequestHistory',
            'SowCategory' => 'category',
            'SowLineItem' => 'lineItem',
            'SowTemplate' => 'template',
            'DrawSummaryCalculatedValues' => 'drawSummaryCalculatedValues',
        ];

        return $classToPropertyMap[$className] ?? strtolower($className);
    }

    /**
     * Map properties from class to database object (reverse mapping)
     *
     * @param object $dbObject The database object to map to
     * @param array $propertyMap Optional custom property mapping ['dbProperty' => 'classProperty']
     * @param array $excludeProperties Properties to exclude from mapping
     * @return void
     */
    protected function mapPropertiesToDbObject(
        object $dbObject,
        array $propertyMap = [],
        array $excludeProperties = []
    ): void {
        $classReflection = new \ReflectionClass($this);
        $classProperties = $classReflection->getProperties(\ReflectionProperty::IS_PUBLIC);

        foreach ($classProperties as $classProperty) {
            $classPropertyName = $classProperty->getName();

            if (in_array($classPropertyName, $excludeProperties)) {
                continue;
            }

            $dbPropertyName = $propertyMap[$classPropertyName] ?? $classPropertyName;

            if (property_exists($dbObject, $dbPropertyName)) {
                $value = $classProperty->getValue($this);

                $convertedValue = $this->convertPropertyValueToDb($value, $dbPropertyName);
                $dbObject->$dbPropertyName = $convertedValue;
            }
        }
    }

    /**
     * Create DTO from current object
     *
     * @param string $dtoClass The DTO class name
     * @param array $propertyMap Optional custom property mapping
     * @return BaseDTO
     */
    protected function toDTO(string $dtoClass, array $propertyMap = []): BaseDTO
    {
        $data = $this->toArray();

        if (!empty($propertyMap)) {
            $mappedData = [];
            foreach ($data as $key => $value) {
                $mappedKey = $propertyMap[$key] ?? $key;
                $mappedData[$mappedKey] = $value;
            }
            $data = $mappedData;
        }

        return $dtoClass::fromArray($data);
    }

    /**
     * Update object from DTO
     *
     * @param BaseDTO $dto The DTO to update from
     * @param array $propertyMap Optional custom property mapping
     * @param array $excludeProperties Properties to exclude from update
     * @return void
     */
    protected function fromDTO(BaseDTO $dto, array $propertyMap = [], array $excludeProperties = []): void
    {
        $data = $dto->toArray();

        foreach ($data as $key => $value) {
            $propertyName = $propertyMap[$key] ?? $key;

            if (in_array($propertyName, $excludeProperties)) {
                continue;
            }

            if (property_exists($this, $propertyName)) {
                $convertedValue = $this->convertPropertyValue($value, $propertyName);
                $this->$propertyName = $convertedValue;
            }
        }
    }

    /**
     * Convert property value to appropriate type.
     * Enhanced version with common type conversions.
     *
     * @param mixed $value The value to convert.
     * @param string $propertyName The property name for context.
     * @return mixed The converted value.
     */
    protected function convertPropertyValue($value, string $propertyName)
    {
        if ($value === null) {
            return null;
        }

        if (str_contains($propertyName, 'At') || str_contains($propertyName, 'Date')) {
            return $this->convertDateValue($value);
        }

        if (str_contains($propertyName, 'Amount') || str_contains($propertyName, 'Cost') ||
            str_contains($propertyName, 'Percent')) {
            return $this->convertNumericValue($value);
        }

        if (str_contains($propertyName, 'Id') || str_contains($propertyName, 'ID')) {
            return $this->convertIdValue($value);
        }

        return $value;
    }

    /**
     * Convert property value for database storage (reverse conversion)
     *
     * @param mixed $value The value to convert
     * @param string $propertyName The property name for context
     * @return mixed The converted value
     */
    protected function convertPropertyValueToDb($value, string $propertyName)
    {
        if ($value === null) {
            return null;
        }

        if (str_contains($propertyName, 'At') || str_contains($propertyName, 'Date')) {
            return $this->convertDateValueToDb($value);
        }

        return $value;
    }

    /**
     * Convert date value from various formats
     *
     * @param mixed $value Date value
     * @return string|null
     */
    protected function convertDateValue($value): ?string
    {
        if ($value === null || $value === '') {
            return null;
        }

        if (is_string($value)) {
            return $value;
        }

        if ($value instanceof \DateTime) {
            return $value->format('Y-m-d H:i:s');
        }

        return (string) $value;
    }

    /**
     * Convert date value for database storage
     *
     * @param mixed $value Date value
     * @return string|null
     */
    protected function convertDateValueToDb($value): ?string
    {
        return $this->convertDateValue($value);
    }

    /**
     * Convert numeric value
     *
     * @param mixed $value Numeric value
     * @return float|int|null
     */
    protected function convertNumericValue($value)
    {
        if ($value === null || $value === '') {
            return null;
        }

        if (is_numeric($value)) {
            return strpos($value, '.') !== false ? (float) $value : (int) $value;
        }

        return 0;
    }

    /**
     * Convert ID value
     *
     * @param mixed $value ID value
     * @return int|null
     */
    protected function convertIdValue($value): ?int
    {
        if ($value === null || $value === '') {
            return null;
        }

        return (int) $value;
    }
}
