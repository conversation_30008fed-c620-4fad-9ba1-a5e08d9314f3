<?php
namespace models\composite\oDrawManagement;

use models\types\strongType;
use models\composite\oDrawManagement\traits\PropertiesMapper;
use models\lendingwise\tblDrawRequestLineItems_h;

class BorrowerDrawLineItemHistory extends strongType
{
    use PropertiesMapper;

    public ?int $id = null;
    public ?int $recordId = null;
    public ?int $lineItemId = null;
    public ?float $cost = 0.00;
    public ?float $completedAmount = 0.00;
    public ?float $completedPercent = 0.00;
    public ?float $requestedAmount = 0.00;
    public ?float $disbursedAmount = 0.00;
    public ?string $notes = null;
    public ?string $lenderNotes = null;
    public ?string $createdAt = null;
    public ?tblDrawRequestLineItems_h $lineItemHistory = null;

    /**
     * BorrowerDrawLineItemHistory constructor.
     * @param tblDrawRequestLineItems_h|null $lineItemHistory The database line item history object to initialize from.
     */
    public function __construct(?tblDrawRequestLineItems_h $lineItemHistory = null) {
        if ($lineItemHistory == null) $lineItemHistory = new tblDrawRequestLineItems_h();
        $this->lineItemHistory = $lineItemHistory;
        $this->setProperties($lineItemHistory);
    }

    /**
     * Saves the current line item history object to the database.
     * @return array The result of the save operation.
     */
    public function save(array $lineItemData): array {
        $this->setFromArray($lineItemData);
        $saved = $this->lineItemHistory->save();
        $this->id = $this->lineItemHistory->id;
        return $saved;
    }

    /**
     * Sets properties from an array.
     * @param array $data The data array.
     * @return void
     */
    private function setFromArray(array $data): void {
        if (isset($data['recordId'])) $this->lineItemHistory->recordId = $data['recordId'];
        if (isset($data['lineItemId'])) $this->lineItemHistory->lineItemId = $data['lineItemId'];
        if (isset($data['cost'])) $this->lineItemHistory->cost = $data['cost'];
        if (isset($data['completedAmount'])) $this->lineItemHistory->completedAmount = $data['completedAmount'];
        if (isset($data['completedPercent'])) $this->lineItemHistory->completedPercent = $data['completedPercent'];
        if (isset($data['requestedAmount'])) $this->lineItemHistory->requestedAmount = $data['requestedAmount'];
        if (isset($data['disbursedAmount'])) $this->lineItemHistory->disbursedAmount = $data['disbursedAmount'];
        if (isset($data['notes'])) $this->lineItemHistory->notes = $data['notes'];
        if (isset($data['lenderNotes'])) $this->lineItemHistory->lenderNotes = $data['lenderNotes'];
    }

    /**
     * Gets the database object.
     * @return tblDrawRequestLineItems_h The database object.
     */
    public function getDbObject(): tblDrawRequestLineItems_h {
        return $this->lineItemHistory;
    }

    /**
     * Converts the line item history object to an associative array.
     * @return array An associative array representation of the line item history.
     */
    public function toArray(): array {
        return [
            "id" => $this->id,
            "recordId" => $this->recordId,
            "lineItemId" => $this->lineItemId,
            "cost" => $this->cost,
            "completedAmount" => $this->completedAmount,
            "completedPercent" => $this->completedPercent,
            "requestedAmount" => $this->requestedAmount,
            "disbursedAmount" => $this->disbursedAmount,
            "notes" => $this->notes,
            "lenderNotes" => $this->lenderNotes,
            "createdAt" => $this->createdAt
        ];
    }
}
