<?php

namespace models\composite\oFile;

use models\Controllers\backoffice\employeeReport;
use models\Database2;
use models\constants\gl\glCustomSubstatusFilterForPC;
use models\constants\gl\glUserGroup;
use models\constants\gl\glUserRole;
use models\cypher;
use models\standard\Arrays;
use models\standard\Dates;
use models\standard\Strings;
use models\types\strongType;

/**
 *
 */
class getFileIDs_new extends strongType
{
    private static ?string $tempQry = null;
    private static ?string $massTableQuery = null;

    private static array $params = [];
    private static ?string $accountQry = null;

    private static function filterQry(
        $orderBy,
        $clientID,
        $userRole,
        $restFile,
        $activeStatus,
        $userGroup,
        $salesRep,
        $salesPCID,
        $PCID,
        $assignedBranchId,
        $assignedCFPBFileID,
        $BRID,
        $assignedBRID,
        $leadsBRID,
        $externalBroker,
        $AID,
        $leadStatusID,
        $branchId,
        $brokerNumber,
        $secondaryBrokerNumber,
        $staleDay,
        $propertyState,
        $priorityLevel,
        $startDate,
        $endDate,
        $modifiedStartDate,
        $modifiedEndDate,
        $closingStartDate,
        $closingEndDate,
        $maturityStartDate,
        $maturityEndDate,
        $lenderName,
        $leadSource,
        $LMRClientType,
        $LMRInternalClientType,
        $statusOpt,
        $searchTerm,
        $searchField,
        $billingArray,
        $employeeId,
        $UID,
        $myFileIDsArray,
        $notesType,
        $RESTProcId,
        $paymentStatus,
        $subStDate,
        $subEndDate,
        $clientEmail,
        $WFID,
        $WFSID,
        $WFSNotCompletedId,
        $WFStatus,
        $WFStepIDs,
        $billingDueDate,
        $multipleStatus,
        $multipleModuleCode,
        $loanAuditStatus,
        $massTableQuery1,
        $massTableQueryClosing,
        $massTableSubQuery1,
        $massTableSubQuery2,
        $massTableQuery2,
        $CFPBAuditStatus,
        $primeStatusId,
        $openHouseDate,
        $sortOpt,
        $genCSV,
        $pageNumber,
        $noOfRecords,
        $servicingStatusCode,
        $servicingStatusCodeArray,
        $appraisalStartDate,
        $appraisalEndDate,
        $receivedStartDate,
        $receivedEndDate,
        $disclosureSentDateStart,
        $disclosureSentDateEnd,
        $paymentBased,
        $targetClosingStartDate,
        $targetClosingEndDate,
        $contactsSearch
    ): string
    {
        $searchTerm = stripslashes($searchTerm);
        $searchTerm = trim(str_replace('"', '', $searchTerm));

        if(strlen($searchTerm) > 255) {
            $searchTerm = substr($searchTerm, 0, 255);
        }
        $glCustomSubstatusFilterForPC = glCustomSubstatusFilterForPC::$glCustomSubstatusFilterForPC;


        $filterQry = '';
        $qryOr = '';
        if ($clientID) {
            $filterQry .= ' AND tl.clientId = :clientId ';
            self::$params['clientId'] = $clientID;
        }
        if ($userRole == 'Auditor'
            || $userRole == 'CFPB Auditor'
            || $userRole == 'Auditor Manager'
            || $restFile == 'CFPB'
        ) {
            $filterQry .= ' AND tl.activeStatus = 1 ';
        } else {
            $filterQry .= ' AND tl.activeStatus = :activeStatus ';
            self::$params['activeStatus'] = $activeStatus;
        }

        if ($userGroup == 'Sales' || trim($salesRep)) {
            $filterQry .= ' AND tl.FPCID IN (' . $salesPCID . ') ';
        }
        if ($PCID) {
            $filterQry .= ' AND tl.FPCID = :PCID ';
            self::$params['PCID'] = $PCID;
        }
        if ($assignedBranchId) {
            $filterQry .= ' AND tl.FBRID IN (' . $assignedBranchId . ') ';
        }
        if ($userGroup == 'CFPB Auditor') {
            $filterQry .= ' AND tl.LMRId IN (' . $assignedCFPBFileID . ') ';
        }
        if($servicingStatusCode != ''){
            $filterQry .= ' AND tpi.servicingSubStatus IN ('."'" . implode ( "', '", $servicingStatusCodeArray ) . "'".')';
        }

        if ($BRID || $assignedBRID || $leadsBRID) {
            $filterQry .= '
            -- BRID assignedBRID leadsBRID 
            AND (';
            if ($BRID) {
                $filterQry .= 'tl.FBRID IN(' . $BRID . ') OR ';

                if ($externalBroker == '1') {
                    $filterQry .= 'tl.secondaryBrokerNumber in(' . $AID . ')';
                } else {
                    $filterQry .= 'tl.brokerNumber in(' . $AID . ')';
                }
                $qryOr = ' OR ';
            }
            if ($assignedBRID) {

                if ($externalBroker == '1') {
                    $filterQry .= $qryOr . '(tl.FBRID IN(' . $assignedBRID . ') AND tl.secondaryBrokerNumber in(' . $AID . '))';
                } else {
                    $filterQry .= $qryOr . '(tl.FBRID IN(' . $assignedBRID . ') AND tl.brokerNumber in(' . $AID . '))';
                }

                $qryOr = ' OR ';
            }
            if ($leadsBRID) {

                if ($externalBroker == '1') {
                    $filterQry .= $qryOr . '((tl.FBRID IN(' . $leadsBRID . ') AND tlr.primeStatusId  in ( ' . $leadStatusID . ' ) ) OR tl.secondaryBrokerNumber in(' . $AID . '))';
                } else {
                    $filterQry .= $qryOr . '((tl.FBRID IN(' . $leadsBRID . ') AND tlr.primeStatusId  IN ( ' . $leadStatusID . ') ) OR tl.brokerNumber in(' . $AID . '))';
                }

            }
            $filterQry .= ') ';
            if (sizeof($brokerNumber)) {
                $filterQry .= ' AND tl.brokerNumber in (' . Database2::GetPlaceholders(sizeof($brokerNumber), ':brokerNumber', true) . ')';
                foreach ($brokerNumber as $j => $paVal) {
                    self::$params['brokerNumber' . $j] = trim($paVal);
                }
            }
        }

        if (sizeof($branchId)) {
            $filterQry .= ' AND tl.FBRID in(' . Database2::GetPlaceholders(sizeof($branchId), ':branchId', true)  . ') ';
            foreach ($branchId as $j => $paVal) {
                self::$params['branchId' . $j] = trim($paVal);
            }
        }
        if (sizeof($brokerNumber) && $BRID == '' && $assignedBRID == '' && $leadsBRID == '') {
            $filterQry .= ' AND tl.brokerNumber in(' . Database2::GetPlaceholders(sizeof($brokerNumber), ':brokerNumber', true) . ') ';
            foreach ($brokerNumber as $j => $paVal) {
                self::$params['brokerNumber' . $j] = trim($paVal);
            }
        }
        if(!is_array($secondaryBrokerNumber)) {
            $secondaryBrokerNumber = [$secondaryBrokerNumber];
        }
        if (sizeof($secondaryBrokerNumber ?? []) && $BRID == '' && $assignedBRID == '' && $leadsBRID == '') {
            $filterQry .= ' AND tl.secondaryBrokerNumber in ('.Database2::GetPlaceholders(sizeof($secondaryBrokerNumber), ':secondaryBrokerNumber', true).' )';
            foreach ($secondaryBrokerNumber as $j => $paVal) {
                self::$params['secondaryBrokerNumber'.$j] = trim($paVal);
            }
        }
        if (sizeof($contactsSearch ?? [])) {
            $filterQry .= ' AND tfc.CID in ('.Database2::GetPlaceholders(sizeof($contactsSearch), ':searchCID', true).' )';
            foreach ($contactsSearch as $j => $paVal) {
                self::$params['searchCID'.$j] = trim($paVal);
            }
        }
        if ($staleDay) {
            $filterQry .= ' AND tfu.lastUpdatedDate < DATE(DATE_SUB(NOW(), INTERVAL ' . intval($staleDay) . ' DAY)) ';
        }
        if ($propertyState) {
            $filterQry .= ' AND tp.propertyState IN (' . $propertyState . ') ';
        }
        if ($priorityLevel) {
            $filterQry .= " AND tlr.priorityLevel = :priorityLevel ";
            self::$params['priorityLevel'] = $priorityLevel;
        }

        self::$params['startDate'] = $startDate;
        self::$params['endDate'] = $endDate;

        if ($startDate == '' || $endDate == '') {
            if ($startDate) {
                $filterQry .= " AND tl.recordDate >= :startDate ";
            } elseif ($endDate) {
                $filterQry .= " AND tl.recordDate <= :endDate ";
            }
        } else {
            $filterQry .= " AND tl.recordDate BETWEEN :startDate AND :endDate ";
        }


        self::$params['receivedStartDate'] = $receivedStartDate;
        self::$params['receivedEndDate'] = $receivedEndDate;

        if ($receivedStartDate == '' || $receivedEndDate == '') {
            if ($receivedStartDate) {
                $filterQry .= " AND tl.receivedDate >= :receivedStartDate ";
            } elseif ($receivedEndDate) {
                $filterQry .= " AND tl.receivedDate <= :receivedEndDate ";
            }
        } else {
            $filterQry .= " AND tl.receivedDate BETWEEN :receivedStartDate AND :receivedEndDate ";
        }

        self::$params['modifiedStartDate'] = $modifiedStartDate;
        self::$params['modifiedEndDate'] = $modifiedEndDate;

        if ($modifiedStartDate == '' || $modifiedEndDate == '') {
            if ($modifiedStartDate) {
                $filterQry .= " AND date(tfu.lastUpdatedDate) >= :modifiedStartDate ";
            } elseif ($modifiedEndDate) {
                $filterQry .= " AND date(tfu.lastUpdatedDate) <= :modifiedEndDate ";
            }
        } else {
            $filterQry .= " AND date(tfu.lastUpdatedDate) BETWEEN :modifiedStartDate AND :modifiedEndDate ";
        }

        self::$params['closingStartDate'] = $closingStartDate;
        self::$params['closingEndDate'] = $closingEndDate;

        if ($closingStartDate == '' || $closingEndDate == '') {
            if ($closingStartDate) {
                $filterQry .= " AND date(tqa.closingDate) >= :closingStartDate ";
            } elseif ($closingEndDate) {
                $filterQry .= " AND date(tqa.closingDate) <= :closingEndDate ";
            }
        } else {
            $filterQry .= " AND date(tqa.closingDate) BETWEEN :closingStartDate AND :closingEndDate ";
        }

        self::$params['maturityStartDate'] = $maturityStartDate;
        self::$params['maturityEndDate'] = $maturityEndDate;

        if ($maturityStartDate == '' || $maturityEndDate == '') {
            if ($maturityStartDate) {
                $filterQry .= " AND date(tpi.maturityDate) >= :maturityStartDate ";
            } elseif ($maturityEndDate) {
                $filterQry .= " AND date(tpi.maturityDate) <= :maturityEndDate ";
            }
        } else {
            $filterQry .= " AND date(tpi.maturityDate) BETWEEN :maturityStartDate AND :maturityEndDate ";
        }

        self::$params['targetClosingStartDate'] = $targetClosingStartDate;
        self::$params['targetClosingEndDate'] = $targetClosingEndDate;

        if ($targetClosingStartDate == '' || $targetClosingEndDate == '') {
            if ($targetClosingStartDate) {
                $filterQry .= " AND date(tfh.targetClosingDate) >= :targetClosingStartDate ";
            } elseif ($targetClosingEndDate) {
                $filterQry .= " AND date(tfh.targetClosingDate) <= :targetClosingEndDate ";
            }
        } else {
            $filterQry .= " AND date(tfh.targetClosingDate) BETWEEN :targetClosingStartDate AND :targetClosingEndDate ";
        }

        self::$params['appraisalStartDate'] = $appraisalStartDate;
        self::$params['appraisalEndDate'] = $appraisalEndDate;

        if ($appraisalStartDate == '' || $appraisalEndDate == '') {
            if ($appraisalStartDate) {
                $filterQry .= " AND ( date(tss.appraisal1OrderDate) >= :appraisalStartDate OR date(tss.appraisal2OrderDate) >= :appraisalStartDate )  ";
            } elseif ($appraisalEndDate) {
                $filterQry .= " AND ( date(tss.appraisal1OrderDate) <= :appraisalEndDate OR date(tss.appraisal2OrderDate) <= :appraisalEndDate ) ";
            }
        } else {
            $filterQry .= " AND ( date(tss.appraisal1OrderDate) BETWEEN :appraisalStartDate AND :appraisalEndDate  OR date(tss.appraisal2OrderDate) BETWEEN :appraisalStartDate AND :appraisalEndDate  )";
        }

        self::$params['disclosureSentDateStart'] = $disclosureSentDateStart;
        self::$params['disclosureSentDateEnd'] = $disclosureSentDateEnd;

        if ($disclosureSentDateStart == '' || $disclosureSentDateEnd == '') {
            if ($disclosureSentDateStart) {
                $filterQry .= " AND ( date(tfa.disclosureSentDate) >= :disclosureSentDateStart )  ";
            } elseif ($disclosureSentDateEnd) {
                $filterQry .= " AND ( date(tfa.disclosureSentDate) <= :disclosureSentDateEnd ) ";
            }
        } else {
            $filterQry .= " AND ( date(tfa.disclosureSentDate) BETWEEN :disclosureSentDateStart AND :disclosureSentDateEnd )";
        }

        if($paymentBased){
            self::$params['paymentBased'] = $paymentBased;
            $filterQry .= " AND ( tnl.isLoanPaymentAmt = :paymentBased ) ";
        }


        if ($lenderName) {
            $filterQry .= ' AND ( tl.servicer1 in(' . $lenderName . ') OR tl.servicer2 in(' . $lenderName . ')) ';
        }
        if ($leadSource) {
            $filterQry .= " AND tlr.leadSource = :leadSource ";
            self::$params['leadSource'] = $leadSource;
        }
        if ($LMRClientType) {
            if (!is_array($LMRClientType)) {
                $LMRClientType = explode(',', $LMRClientType);
            }
            $list = [];
            foreach ($LMRClientType as $i => $type) {
                $type = trim($type);
                if ($type[0] == '\'') {
                    $type = substr($type, 1);
                }
                $m = strlen($type) - 1;
                if ($type[$m] == '\'') {
                    $type = substr($type, 0, -1);
                }
                self::$params['LMRClientType' . $i] = $type;
                $list[] = ':LMRClientType' . $i;
            }
            $_list = implode(', ', $list);
            if($_list) {
                $filterQry .= ' AND tct.ClientType IN (' . $_list . ') ';
            }
        }
        if (count($LMRInternalClientType)) {
            $LMRInternalClientTypeTxt = "'" . implode("','", ($LMRInternalClientType)) . "'";
            $filterQry .= ' AND tFIL.internalLoanProgram IN (' . $LMRInternalClientTypeTxt . ') ';
        }
        if ($statusOpt) {
            $_statusOpt = is_array($statusOpt) ? implode(',', $statusOpt) : $statusOpt;
            if($_statusOpt) {
                $filterQry .= " AND COALESCE(dateUnchecked, '') = '' AND ts.substatusId in (" . $_statusOpt . ')';
            }
        }
        $phoneArray = [
            'tl.cellNumber',
            'tl.phoneNumber',
            'tl.coBPhoneNumber',
            'tl.coBCellNumber',
            'tl.workNumber',
            'tl.fax',
            'tl.coBFax',
            'tl.altPhoneNumber',
            'tl.coBAltPhoneNumber'
        ];
        $PhoneSearchTerm = Strings::cleanPhoneNo($searchTerm);
        $PhoneSearchTermCnt = strlen($PhoneSearchTerm);

        $SSNArray = [
            'tl.ssnNumber',
            'tl.coBSsnNumber'
        ];

        $tempSearchField = [];
        if ($PhoneSearchTermCnt == 10) {
            foreach ($searchField as $i => $field) {
                if (in_array($field, $phoneArray)) {
                    $tempSearchField[] = $field;
                    if (!(($PCID == '3394' || $userGroup == glUserGroup::USER_GROUP_SUPER) && count($searchField))) {
                        unset($searchField[$i]);
                    }
                }
            }
            if (count($searchField)) {
                $searchField = array_values($searchField);
            }
        }

        if (($PCID == '3394' || $userGroup == 'Super') && count($searchField)) {
            $exactSearchArray = [
                'tl.borrowerName',
                'tl.borrowerLName',
                'tl.borrowerEmail',
                'tl.borrowerSecondaryEmail',
                'tl.coBorrowerFName',
                'tl.coBorrowerLName',
                'tl.coBorrowerEmail',
                'tl.LMRId',
                'tlr.fileNumber',
                'tp.propertyCity',
                'tl.loanNumber',
                'tl.loanNumber2',
            ];
            if (trim($searchTerm)) {
                self::$params['searchTerm'] = $searchTerm;
                self::$params['searchTermLike'] = $searchTerm . '%';
                self::$params['searchTermLikeEnc'] = cypher::myEncryption($searchTerm) . '%';
                self::$params['searchTermPhone'] = Strings::cleanPhoneNo($searchTerm);

                $filterQry = '';
                foreach ($searchField as $i => $field) {
                    if (in_array($field, $exactSearchArray)) {
                        if ($i) {
                            $filterQry .= 'OR ';
                        }
                        $filterQry .= $field . " = :searchTerm ";
                    } elseif (in_array($field, $phoneArray)) {
                        if (is_numeric(Strings::cleanPhoneNo($searchTerm))) {
                            if ($i) {
                                $filterQry .= 'OR ';
                            }
                            $filterQry .= $field . " = :searchTermPhone ";
                        }
                    } elseif (in_array(trim($field), $billingArray)) {
                        if ($i) {
                            $filterQry .= 'OR ';
                        }
                        $filterQry .= $field . " like :searchTermLikeEnc ";
                    } else {
                        if ($i) {
                            $filterQry .= 'OR ';
                        }
                        $filterQry .= $field . " like :searchTermLike ";
                    }
                }
                if($filterQry) {
                    $filterQry = ' 
                -- searchField 1
                and (
                    ' . $filterQry . '
                )';
                }
            }
        } elseif (count($searchField) || ($PhoneSearchTermCnt == 10 && count($tempSearchField))) {

            self::$params['searchTermLikeB'] = '%' . $searchTerm . '%';

            $searchTermLikeBPhone = Strings::cleanPhoneNo($searchTerm);
            if($searchTermLikeBPhone) {
                self::$params['searchTermLikeBPhone'] = '%' . $searchTermLikeBPhone . '%';
            }

            $searchTermLikeBEnc = cypher::myEncryption($searchTerm);
            if($searchTermLikeBEnc) {
                self::$params['searchTermLikeBEnc'] = '%' . $searchTermLikeBEnc . '%';
            }

            $searchTermLikeBSSN = Strings::getNumberValue($searchTerm);
            if($searchTermLikeBSSN) {
                self::$params['searchTermLikeBSSN'] = '%' . $searchTermLikeBSSN . '%';
            }


            $filterQry .= ' 
                -- searchField 2
                and ( 
            ';

            $subQuery = [];
            foreach ($searchField as $field) {

                if (in_array($field, $phoneArray)) {
                    if($searchTermLikeBPhone) {
                        $subQuery [] = $field . " like :searchTermLikeBPhone  ";
                    }
                } elseif ($searchTermLikeBEnc && in_array(trim($field), $billingArray)) {
                    if($searchTermLikeBEnc) {
                        $subQuery [] = $field . " like :searchTermLikeBEnc ";
                    }
                } elseif ($searchTermLikeBSSN && in_array($field, $SSNArray)) {
                    if($searchTermLikeBSSN) {
                        $subQuery [] = $field . " like :searchTermLikeBSSN ";
                    }
                } else {
                    $subQuery []= $field . " like :searchTermLikeB ";
                }
            }

            if ($PhoneSearchTermCnt == 10) {
                foreach ($tempSearchField as $tField) {
                    $subQuery []= $tField . " like :searchTermLikeB ";
                }
            }

            if(sizeof($subQuery)) {
                $filterQry .= implode(' OR ', $subQuery) . ' )';
            }
        }


        if (sizeof($employeeId)) {
            if (!in_array($UID,$employeeId)
                && $userGroup == 'Employee'
                && $userRole != 'Manager') {
                if (count($myFileIDsArray)) {
                    $filterQry .= ' AND te.fileID IN (' . implode(', ', $myFileIDsArray) . ')';
                }
            }

                $filterQry .= ' AND te.UID IN ( ' . Database2::GetPlaceholders(sizeof($employeeId), ':employeeID', true) . ' ) AND te.URole = "Employee" AND COALESCE(te.dateRemoved, "") = "" ';
                foreach ($employeeId as $j => $paVal) {
                    self::$params['employeeID' . $j] = trim($paVal);
                }


        }

        if ($PCID) {
            $filterQry .= ' AND tl.FPCID = :PCID ';
            self::$params['PCID'] = $PCID;
        }


        if ($notesType) {
            $filterQry .= " AND tpc.deleted = 0 AND tpc.notesType = :notesType ";
            self::$params['notesType'] = $notesType;
        }

        if ($userRole == 'REST' || $restFile == 2) {
            $filterQry .= ' AND tre.caseFileId ';
            if (trim($RESTProcId)) {
                $filterQry .= ' AND tl.FPCID = :RESTProcId ';
                self::$params['RESTProcId'] = $RESTProcId;
            }
            if ($paymentStatus) {
                $filterQry .= " AND  tre.paymentStatus = :paymentStatus ";
                self::$params['paymentStatus'] = $paymentStatus;
            }

            self::$params['subStDate'] = $subStDate;
            self::$params['subEndDate'] = $subEndDate;

            if ($subStDate == '' || $subEndDate == '') {
                if ($subStDate) {
                    $filterQry .= " AND date(tre.SubmissionDate) >= :subStDate ";
                } elseif ($subEndDate) {
                    $filterQry .= " AND date(tre.SubmissionDate) <= :subEndDate ";
                }
            } else {
                $filterQry .= " AND date(tre.SubmissionDate) BETWEEN :subStDate AND :subEndDate ";
            }
        }
        if ($restFile == 4 || $restFile == 'LA') {
            self::$params['paymentStatus'] = $paymentStatus;

            if ($paymentStatus) {
                if ($restFile == 'LA') {
                    $filterQry .= " AND tfl.paymentStatus = '" . $paymentStatus . "' ";
                } else {
                    $filterQry .= " AND thr.paymentStatus = '" . $paymentStatus . "' ";
                }
            }

            self::$params['subStDate'] = $subStDate;
            self::$params['subEndDate'] = $subEndDate;

            if ($subStDate == '' || $subEndDate == '') {
                if ($subStDate) {
                    $filterQry .= " AND date(thrh.SubmissionDate) >= :subStDate ";
                } elseif ($subEndDate) {
                    $filterQry .= " AND date(thrh.SubmissionDate) <= :subEndDate ";
                }
            } else {
                $filterQry .= " AND date(thrh.SubmissionDate) BETWEEN :subStDate AND :subEndDate ";
            }
        }
        if (trim($clientEmail)) {
            $filterQry .= " AND tl.borrowerEmail like('" . $clientEmail . "') ";
        }
        if (sizeof($WFID)) {
            $filterQry .= ' AND tfw.WFID in( ' . Database2::GetPlaceholders(sizeof($WFID), ':workflowId', true) . ' ) ';
            foreach ($WFID as $j => $paVal) {
                self::$params['workflowId' . $j] = trim($paVal);
            }
        }
        if (sizeof($WFSID)) {
            $filterQry .= ' AND tfs.WFSID in( ' . Database2::GetPlaceholders(sizeof($WFSID), ':workflowStepId', true) . ' ) ';
            foreach ($WFSID as $j => $paVal) {
                self::$params['workflowStepId' . $j] = trim($paVal);
            }
        }
        if (sizeof($WFSNotCompletedId)) {
            $filterQry .= 'AND   tl.LMRId  NOT IN
                        (SELECT LMRID FROM tblLMRWorkflowSteps WHERE WFSID IN (' . Database2::GetPlaceholders(sizeof($WFSNotCompletedId), ':workflowStepNotCompletedId', true)  . ')  AND dstatus=1
                        )   ';
            foreach ($WFSNotCompletedId as $j => $paVal) {
                self::$params['workflowStepNotCompletedId' . $j] = trim($paVal);
            }
        }

//        if ($WFSID) {
//            if ($workFlowStepStatus == 1) {
//                $filterQry .= ' AND tfs.WFSID = ' . $WFSID;
//            } else {
//                $filterQry .= ' AND tfs.WFSID is null ';
//            }
//        }
        if ($WFStatus > 1) {
            self::$params['WFStatus'] = $WFStatus;
            $filterQry .= ' AND tfs.docStatus = :WFStatus ';
        }

        if ($WFStepIDs) {
            $filterQry .= ' AND tfsw.WFSID in(' . $WFStepIDs . ') ';
        }

        if ($billingDueDate) {
            $curDate = Dates::Datestamp();

            $firstDay = getdate(mktime(0, 0, 0, date('m'), date('d'), date('Y')));
            $curDay = $firstDay['wday'];
            $wkStDate = date('Y-m-d', mktime(0, 0, 0, date('m'), date('d') - $curDay, date('Y')));
            $wkStDateArray = explode('-', $wkStDate);
            $wkStDay = $wkStDateArray[2];
            $wkStMn = $wkStDateArray[1];
            $wkStYr = $wkStDateArray[0];

            if ($billingDueDate == 'pastDue') {
                $filterQry .= " and bill.dateOwed < :curDate ";
                self::$params['curDate'] = $curDate;

            } elseif ($billingDueDate == 'thisWeek') {
                $wkEndDate = date('Y-m-d', mktime(0, 0, 0, $wkStMn, $wkStDay + 6, $wkStYr));
                $filterQry .= " and (bill.dateOwed between :wkStDate and :wkEndDate )";
                self::$params['wkStDate'] = $wkStDate;
                self::$params['wkEndDate'] = $wkEndDate;
            } elseif ($billingDueDate == 'twoWeek') {
                $wkEndDate = date('Y-m-d', mktime(0, 0, 0, $wkStMn, $wkStDay + 13, $wkStYr));
                $filterQry .= " and (bill.dateOwed between :wkStDate and :wkEndDate )";
                self::$params['wkStDate'] = $wkStDate;
                self::$params['wkEndDate'] = $wkEndDate;
            } elseif ($billingDueDate == 'threeWeek') {
                $wkEndDate = date('Y-m-d', mktime(0, 0, 0, $wkStMn, $wkStDay + 20, $wkStYr));
                $filterQry .= " and (bill.dateOwed between :wkStDate and :wkEndDate )";
                self::$params['wkStDate'] = $wkStDate;
                self::$params['wkEndDate'] = $wkEndDate;
            }
        }

        if ($multipleStatus) {
            if(is_string($multipleStatus)) {
                $multipleStatus = explode(',', $multipleStatus);
            }
            $_multipleStatus = implode('', $multipleStatus);
            if ($PCID) {
                if (preg_match('/^[a-zA-Z]+$/', $_multipleStatus)) { /* Multi select and check the string has aplahbets - Dec 8, 2015 */
                    $filterQry .= " AND tps.primaryStatus in('" . implode('\',\'', $multipleStatus) . "') ";
                } else {
                    $filterQry .= ' AND tlr.primeStatusId in(' . implode(',', $multipleStatus) . ') ';
                }
            } else {
                $multipleStatusArray = explode(',', $multipleStatus);
                if (count($multipleStatusArray)) $multipleStatus = "'" . implode("','", $multipleStatusArray) . "'";
                $filterQry .= ' AND tps.primaryStatus in(' . $multipleStatus . ') ';
            }
        }
        if ($multipleModuleCode) {
            $multipleModuleCodeArray = preg_split('/[, ]+/', $multipleModuleCode);
            if(is_array($multipleModuleCodeArray)){
                $filterQry .= ' AND  tpsm.moduleCode IN ( ' . Database2::GetPlaceholders(sizeof($multipleModuleCodeArray), ':moduleCode', true) . ' )';
                foreach ($multipleModuleCodeArray as $j => $paVal) {
                    self::$params['moduleCode' . $j] = trim($paVal);
                }
               // $temp = "'" . implode("','", $multipleModuleCodeArray) . "'";
                //$filterQry .= ' AND tpsm.moduleCode in(' . $temp . ') ';
            }
        }


        if (trim($loanAuditStatus) != 'All' && $userRole == 'Auditor') {
            $filterQry .= " AND tfl.loanAuditStatus = :loanAuditStatus ";
            self::$params['loanAuditStatus'] = $loanAuditStatus;
        }

        self::$tempQry = $massTableQuery1 . $massTableQueryClosing . self::$accountQry . ' where  tl.LMRId = tlr.LMRId ' . $filterQry;
        /** To Get count for all Status - Please don't add any here after if u want to add any condition make use of line before tempQry **/

        self::$massTableQuery = $massTableSubQuery1 . $massTableQuery1 . $massTableSubQuery2 . $massTableQuery2; // With Total Loan Amount Queries

        if (trim($CFPBAuditStatus) != 'All' && ($userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager' || $restFile == 'CFPB')) {
            $filterQry .= " AND tcfpb.CFPBAuditStatus = :CFPBAuditStatus ";
            self::$params['CFPBAuditStatus'] = $CFPBAuditStatus;
        }

        if ($primeStatusId != 'All'
            && $userRole != glUserRole::USER_ROLE_CFPB_AUDITOR
            && $userRole != glUserRole::USER_ROLE_AUDITOR_MANAGER
        ) {
            if ($primeStatusId && $restFile == 'LA') {
                $filterQry .= " AND tfl.loanAuditStatus = :primeStatusId ";
                self::$params['primeStatusId'] = $primeStatusId;
            } elseif ($primeStatusId && $restFile == 'CFPB') {
                $filterQry .= " AND tcfpb.CFPBAuditStatus = :primeStatusId ";
                self::$params['primeStatusId'] = $primeStatusId;
            } else if ($primeStatusId) {
                $filterQry .= ' AND tlr.primeStatusId = :primeStatusId ';
                self::$params['primeStatusId'] = $primeStatusId;
            }
        }
        if (($LMRClientType) || ($statusOpt && $statusOpt != 'stale') || ($notesType)) {
            $filterQry .= ' GROUP BY tlr.LMRResponseId ';
        }
        /**
         * Description : Searching multiple Substatus's do an AND type search (customization for Emerald City Realty PC).
         * Date        : Jan 08, 2018
         * Developer   : Venkatesh
         * task Id        : #153998068
         **/
        if ($statusOpt && in_array($PCID, $glCustomSubstatusFilterForPC)) {
            $subStatusCntArray = preg_split('/[, ]+/', $statusOpt, -1, PREG_SPLIT_NO_EMPTY);
            if (count($subStatusCntArray)) {
                $filterQry .= ' HAVING COUNT(DISTINCT ts.substatusId ) >= :count_subStatusCntArray ';
                self::$params['count_subStatusCntArray'] = count($subStatusCntArray);
            }
        }
        if ($openHouseDate == 'No') {
            $filterQry .= 'AND tss2.openHouseDate IS NULL ';
        }

        if ($billingDueDate) {
            if (($LMRClientType) || ($statusOpt && $statusOpt != 'stale') || ($notesType)) {
                $filterQry .= ', ';
            } else {
                $filterQry .= ' GROUP BY ';
            }
            $filterQry .= ' bill.LMRId HAVING balanceDue ';
        }
        if ($userRole == 'Auditor' || $restFile == 'LA') {
            $filterQry .= ' GROUP BY tfl.fileID HAVING MAX(tfl.activeStatus) = :activeStatus ';
            self::$params['activeStatus'] = $activeStatus;
        }
        if ($userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager' || $restFile == 'CFPB') {
            $filterQry .= ' GROUP BY tcfpb.LMRID HAVING MAX(tcfpb.activeStatus) = :activeStatus ';
            self::$params['activeStatus'] = $activeStatus;
        }
        if ($restFile == 4) {
            $filterQry .= ' GROUP BY thr.fileID ';
        }

        if ($sortOpt == 'HMLOTotalLoanAmt'
            || $sortOpt == 'monthlyLoanAmount'
            || $sortOpt == 'totalRehabCost'
            || $sortOpt == 'acquisitionLTV'
            || $sortOpt == 'marketLTV'
            || $sortOpt == 'totalProjectCost'
            || $sortOpt == 'LTC'
            || $sortOpt == 'ARV'
        ) {
            $filterQry .= ' GROUP BY tl.LMRId ';
        }

        $filterQry .= ' ORDER BY ';

        if ($sortOpt == 'date') {
            if (trim($orderBy)) {
                $filterQry .= ' tl.recordDate ' . $orderBy . ', ';
            } else {
                $filterQry .= ' tl.recordDate DESC, ';
            }
        } elseif ($sortOpt == 'name') {
            $filterQry .= ' lastName, firstName ' . $orderBy . ', ';
        } elseif ($sortOpt == 'loanOfficerName') {
            $filterQry .= ' lastName, firstName ' . $orderBy . ', ';
        } elseif ($sortOpt == 'branchName') {
            $filterQry .= ' tle.LMRExecutive ' . $orderBy . ', ';
        } elseif ($sortOpt == 'borFN') {
            $filterQry .= ' tl.borrowerLName, tl.borrowerName ' . $orderBy . ', ';
        } elseif ($sortOpt == 'salesDate') {
            if (trim($orderBy)) {
                $filterQry .= ' salesDate ' . $orderBy . ', ';
            } else {
                $filterQry .= ' salesDate DESC, ';
            }
        } elseif ($sortOpt == 'servicer') {
            $filterQry .= ' tl.servicer1 ' . $orderBy . ', ';
        } elseif ($sortOpt == 'servicer2') {
            $filterQry .= ' tl.servicer2 ' . $orderBy . ', ';
        } elseif ($sortOpt == 'orderStatus') {
            $filterQry .= ' tre.orderStatus ' . $orderBy . ', ';
        } elseif ($sortOpt == 'paymentStatus') {
            $filterQry .= ' tre.paymentStatus ' . $orderBy . ', ';
        } elseif ($sortOpt == 'restDate') {
            $filterQry .= ' tre.SubmissionDate ' . $orderBy . ', ';
        } elseif ($sortOpt == 'LASubmissionDate') {
            $filterQry .= ' tfl.recordDate ' . $orderBy . ', ';
        } elseif ($sortOpt == 'CaseFile') {
            $filterQry .= ' tre.CaseFileId ' . $orderBy . ', ';
        } elseif ($sortOpt == 'borrowerCallBack') {
            $filterQry .= ' tlr.borrowerCallBack ' . $orderBy . ', ';
        } elseif ($sortOpt == 'lenderCallBack') {
            $filterQry .= ' tlr.lenderCallBack ' . $orderBy . ', ';
        } elseif ($sortOpt == 'receivedDate') {
            $filterQry .= ' tl.receivedDate ' . $orderBy . ', ';
        } elseif ($sortOpt == 'lenderSubmission') {
            $filterQry .= ' tlr.lenderSubmissionDate ' . $orderBy . ', ';
        } elseif ($sortOpt == 'HAFADate') {
            $filterQry .= ' tlr.HAFADate ' . $orderBy . ', ';
        } elseif ($sortOpt == 'closedDate') {
            $filterQry .= ' tl.closedDate ' . $orderBy . ', ';
        } elseif ($sortOpt == 'loanType') {
            $filterQry .= ' tl.loanType ' . $orderBy . ', ';
        } elseif ($sortOpt == 'loanType2') {
            $filterQry .= ' tl.loanType2 ' . $orderBy . ', ';
        } elseif ($sortOpt == 'currentBalance') {
            $filterQry .= ' cast(functionToRemoveSpecialChar(tl.lien1Amount) AS DECIMAL(20,3)) ' . $orderBy . ', ';
        } elseif ($sortOpt == 'currentBalance2') {
            $filterQry .= ' cast(functionToRemoveSpecialChar(tl.lien2Amount) AS DECIMAL(20,3)) ' . $orderBy . ', ';
        } elseif ($sortOpt == 'loanNumber') {
            $filterQry .= ' CAST(functionToRemoveSpecialChar(tl.loanNumber) AS DECIMAL(20,3)) ' . $orderBy . ', ';
        } elseif ($sortOpt == 'loanNumber2') {
            $filterQry .= ' CAST(functionToRemoveSpecialChar(tl.loanNumber2) AS DECIMAL(20,3)) ' . $orderBy . ', ';
        } elseif ($sortOpt == 'lien1Payment') {
            $filterQry .= ' CAST(functionToRemoveSpecialChar(tl.lien1Payment) AS DECIMAL(20,3)) ' . $orderBy . ', ';
        } elseif ($sortOpt == 'lien2Payment') {
            $filterQry .= ' CAST(functionToRemoveSpecialChar(tl.lien2Payment) AS DECIMAL(20,3)) ' . $orderBy . ', ';
        } elseif ($sortOpt == 'lien1Rate') {
            $filterQry .= ' CAST(functionToRemoveSpecialChar(tl.lien1Rate) AS DECIMAL(20,3)) ' . $orderBy . ', ';
        } elseif ($sortOpt == 'lien2Rate') {
            $filterQry .= ' CAST(functionToRemoveSpecialChar(tl.lien2Rate) AS DECIMAL(20,3)) ' . $orderBy . ', ';
        } elseif ($sortOpt == 'lien1LPMade') {
            $filterQry .= ' tl.lien1LPMade ' . $orderBy . ', ';
        } elseif ($sortOpt == 'lien2LPMade') {
            $filterQry .= ' tl.lien2LPMade ' . $orderBy . ', ';
        } elseif ($sortOpt == 'noOfMonthsBehind1') {
            $filterQry .= " LPAD(tl.noOfMonthsBehind1, 25, '0') " . $orderBy . ', ';
        } elseif ($sortOpt == 'noOfMonthsBehind2') {
            $filterQry .= " LPAD(tl.noOfMonthsBehind2, 25, '0') " . $orderBy . ', ';
        } elseif ($sortOpt == 'lien1BalanceDue') {
            $filterQry .= ' CAST(functionToRemoveSpecialChar(tl.lien1BalanceDue) AS DECIMAL(20,3)) ' . $orderBy . ', ';
        } elseif ($sortOpt == 'lien2BalanceDue') {
            $filterQry .= ' CAST(functionToRemoveSpecialChar(tl.lien2BalanceDue) AS DECIMAL(20,3)) ' . $orderBy . ', ';
        } elseif ($sortOpt == 'occupancy') {
            $filterQry .= ' tl.occupancy ' . $orderBy . ', ';
        } elseif ($sortOpt == 'homeValue') {
            $filterQry .= ' CAST(functionToRemoveSpecialChar(tl.homeValue) AS DECIMAL(20,3)) ' . $orderBy . ', ';
        } elseif ($sortOpt == 'fileNumber') {
            $filterQry .= ' tlr.fileNumber ' . $orderBy . ', ';
        } elseif ($sortOpt == 'priorityLevel') {
            $filterQry .= " FIELD(tlr.priorityLevel,'','low','medium','high') " . $orderBy . ', ';
        } elseif ($sortOpt == 'bathrooms') {
            $filterQry .= ' CAST(functionToRemoveSpecialChar(tPCH.propertyNumberOfBathRooms) AS DECIMAL(20,0)) ' . $orderBy . ', ';
        } elseif ($sortOpt == 'bedrooms') {
            $filterQry .= ' CAST(functionToRemoveSpecialChar(tPCH.propertyNumberOfBedRooms) AS DECIMAL(20,0)) ' . $orderBy . ', ';
        } elseif ($userRole == 'REST' || ($restFile == '2' && $userRole == 'Super')) {
            $filterQry .= ' tre.SubmissionDate DESC, ';
        } elseif ($sortOpt == 'FileID') {
            $filterQry .= ' tl.LMRId ' . $orderBy . ', ';
        } elseif ($sortOpt == 'daysInStatus') {
            $filterQry .= ' (DATEDIFF(CURDATE(), DATE(trf.recordDate))) ' . $orderBy . ', ';
        } elseif ($restFile == '4') {
            $filterQry .= ' thrh.SubmissionDate DESC, ';
        } elseif ($sortOpt == 'CFPBSubmittedDate' && ($userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager' || $restFile == 'CFPB')) {
            $filterQry .= ' tcfpb.submittedDate ' . $orderBy . ', ';
        } elseif ($sortOpt == 'closingDate' || $sortOpt == 'targetClosingDate') {
            $filterQry .= ' tqa.closingDate ' . $orderBy . ', ';
        } elseif ($sortOpt == 'adminTargetClosingDate') {
            $filterQry .= ' tfh.targetClosingDate ' . $orderBy . ', ';
        } elseif ($sortOpt == 'desiredClosingDate') {
            $filterQry .= ' tqa.desiredClosingDate ' . $orderBy . ', ';
        } elseif ($sortOpt == 'repairValue') { // After Repair Value & Purchase Price Sorting added Pivotal # 153672200
            $filterQry .= ' tss.rehabValByBor ' . $orderBy . ', ';
        } elseif ($sortOpt == 'purchasePrice') {
            $filterQry .= ' tss.costBasis ' . $orderBy . ', ';
        } elseif ($sortOpt == 'seviceTypes') {
            $filterQry .= ' tct.ClientType ' . $orderBy . ', ';
        } elseif ($sortOpt == 'HMLOTotalLoanAmt') {  // Total Loan Amount Sorting Added - Jan 12, 2018 - Pivotal # 153672200
            $filterQry .= ' totalAmt ' . $orderBy . ', ';
        } elseif ($sortOpt == 'monthlyLoanAmount') {  // Total payroll Amount Sorting Added - Jan 12, 2018 - Pivotal # 153672200
            $filterQry .= ' tsba.monthlyLoanAmount ' . $orderBy . ', ';
        } elseif ($sortOpt == 'totalRehabCost') {  // Total Rehab Cost Sorting Added - Jan 12, 2018 - Pivotal # 153672200
            $filterQry .= ' totalRehabCost ' . $orderBy . ', ';
        } elseif ($sortOpt == 'trialPaymentDate1') {  // First Payment Due Sorting Added - Feb 12, 2018 - Pivotal # 153672200
            $filterQry .= ' tlr.trialPaymentDate1 ' . $orderBy . ', ';
        } elseif ($sortOpt == 'networthOfBusinessOwned') {  // Net Worth Sorting Added - Feb 12, 2018 - Pivotal # 153672200
            $filterQry .= ' tflo.networthOfBusinessOwned ' . $orderBy . ', ';
        } elseif ($sortOpt == 'borNoOfREPropertiesCompleted') {
            $filterQry .= ' tfei.borNoOfREPropertiesCompleted ' . $orderBy . ', ';
        } elseif ($sortOpt == 'borRehabPropCompleted') {
            $filterQry .= ' tfei.borRehabPropCompleted ' . $orderBy . ', ';
        } elseif ($sortOpt == 'borNoOfOwnProp') {
            $filterQry .= ' tfei.borNoOfOwnProp ' . $orderBy . ', ';
        } elseif ($sortOpt == 'isBorUSCitizen') {
            $filterQry .= ' tfhbg.isBorUSCitizen ' . $orderBy . ', ';
        } elseif ($sortOpt == 'HMLOLender') {
            $filterQry .= ' tnl.HMLOLender ' . $orderBy . ', ';
        } elseif ($sortOpt == 'ASISValue') {
            $filterQry .= ' tss.assessedValue ' . $orderBy . ', ';
        } elseif ($sortOpt == 'acquisitionLTV') {
            $filterQry .= ' acquisitionLTV ' . $orderBy . ', ';
        } elseif ($sortOpt == 'marketLTV') {
            $filterQry .= ' marketLTV ' . $orderBy . ', ';
        } elseif ($sortOpt == 'propertyCity') {
            $filterQry .= ' tp.propertyCity ' . $orderBy . ', ';
        } elseif ($sortOpt == 'propertyState') {
            $filterQry .= ' tp.propertyState ' . $orderBy . ', ';
        } elseif ($sortOpt == 'propertyCounty') {
            $filterQry .= ' tp.propertyCounty ' . $orderBy . ', ';
        } elseif ($sortOpt == 'totalProjectCost') {
            $filterQry .= ' totalProjectCost ' . $orderBy . ', ';
        } elseif ($sortOpt == 'LTC') {
            $filterQry .= ' LTC ' . $orderBy . ', ';
        } elseif ($sortOpt == 'ARV') {
            $filterQry .= ' ARV ' . $orderBy . ', ';
        } elseif ($sortOpt == 'leadSource') {
            $filterQry .= ' tlr.leadSource ' . $orderBy . ', ';
        } elseif ($sortOpt == 'proInsPolicyExpDate') {
            //  $filterQry .= " tmp1.proInsPolicyExpDate ".$orderBy.", ";
            $filterQry .= ' tmp1.policyExpDate ' . $orderBy . ', ';
        } elseif ($openHouseDate == 'Yes') {
            $filterQry .= ' tss2.openHouseDate DESC, ';
        } elseif ($sortOpt == 'primeStatus') {
            $filterQry .= ' tps.primaryStatus ' . $orderBy . ', ';
        } elseif ($sortOpt == 'HMLOLoanType') {
            $filterQry .= ' tnl.typeOfHMLOLoanRequesting ' . $orderBy . ', ';
        } elseif ($sortOpt == 'rehabConstructionCost') {
            $filterQry .= ' tnl.rehabCost ' . $orderBy . ', ';
        } elseif ($sortOpt == 'entity') {
            $filterQry .= ' tent.entityName ' . $orderBy . ', ';
        } elseif ($sortOpt == 'maturityDate') {
            $filterQry .= ' tpi.maturityDate ' . $orderBy . ', ';
        } elseif ($sortOpt == 'payOffDate') {
            $filterQry .= ' tpi.payOffDate ' . $orderBy . ', ';
        } elseif ($sortOpt == 'loanSaleDate') {
            $filterQry .= ' tpi.loanSaleDate ' . $orderBy . ', ';
        } elseif ($sortOpt == 'referringParty') {
            $filterQry .= ' tpi.referringParty ' . $orderBy . ', ';
        } elseif ($sortOpt == 'appraisal1OrderDate') {
            $filterQry .= ' tss.appraisal1OrderDate ' . $orderBy . ', ';
        }  elseif ($sortOpt == 'dateObtained') {
            $filterQry .= ' tss.dateObtained ' . $orderBy . ', ';
        } elseif ($sortOpt == 'avgMonthlyCreditCardSales') {
            $filterQry .= ' ent.avgMonthlyCreditcardSale ' . $orderBy . ', ';
        } elseif ($sortOpt == 'avgTotalMonthlySales') {
            $filterQry .= ' ent.avgTotalMonthlySale ' . $orderBy . ', ';
        } elseif ($sortOpt == 'annualGrossProfit') {
            $filterQry .= ' ent.annualGrossProfit ' . $orderBy . ', ';
        } elseif ($sortOpt == 'netBusinessIncome') {
            $filterQry .= ' ent.ordinaryBusinessIncome ' . $orderBy . ', ';
        } elseif ($sortOpt == 'entityName') {
            $filterQry .= ' ent.entityName ' . $orderBy . ', ';
        } elseif ($sortOpt == 'entityType') {
            $filterQry .= ' ent.entityType ' . $orderBy . ', ';
        } elseif ($sortOpt == 'stateOfFormation') {
            $filterQry .= ' ent.entityStateOfFormation ' . $orderBy . ', ';
        } elseif ($sortOpt == 'entityCity') {
            $filterQry .= ' ent.entityCity ' . $orderBy . ', ';
        } elseif ($sortOpt == 'entityState') {
            $filterQry .= ' ent.entityState ' . $orderBy . ', ';
        } elseif ($sortOpt == 'propertyOwnership') {
            $filterQry .= ' ent.entityPropertyOwnerShip ' . $orderBy . ', ';
        } elseif ($sortOpt == 'ofEmployees') {
            $filterQry .= ' ent.noOfEmployees ' . $orderBy . ', ';
        } elseif ($sortOpt == 'disclosureSentDate') {
            $filterQry .= ' tfa.disclosureSentDate ' . $orderBy . ', ';
        } elseif ($sortOpt == 'eCoaWaiverStatus') {
            $filterQry .= ' tlr.eCoaWaiverStatus ' . $orderBy . ', ';
        } else if($sortOpt == 'LMRInternalLoanProgram') {
            $filterQry .= ' tLST2.serviceType ' . $orderBy . ', ';
        } else if($sortOpt == 'PSAClosingDate') {
            $filterQry .= ' tnl.PSAClosingDate ' . $orderBy . ', ';
        } else if($sortOpt == 'targetSubmissionDate') {
            $filterQry .= ' tfa.targetSubmissionDate ' . $orderBy . ', ';
        } else if($sortOpt == 'buildingAnalysisOutstanding') {
            $filterQry .= ' tBAO.buildingAnalysisOutstandingName ' . $orderBy . ', ';
        } else if($sortOpt == 'buildingAnalysisNeed') {
            $filterQry .= ' tBAN.buildingAnalysisNeedName ' . $orderBy . ', ';
        } else if($sortOpt == 'buildingAnalysisDueDate') {
            $filterQry .= ' tnl.buildingAnalysisDueDate ' . $orderBy . ', ';
        }  else if($sortOpt == 'authorizationStatus') {
            $filterQry .= ' tBAS.status_description ' . $orderBy . ', ';
        } else if($sortOpt == 'VOMStatus') {
            $filterQry .= ' tVPS_VOMStatus.VOMPayoffStatusDescription ' . $orderBy . ', ';
        }  else if($sortOpt == 'payoffStatus') {
            $filterQry .= ' tVPS_payoffStatus.VOMPayoffStatusDescription ' . $orderBy . ', ';
        } else if($sortOpt == 'trackRecord') {
            $filterQry .= ' tBETR.track_record_description ' . $orderBy . ', ';
        } else if($sortOpt == 'welcomeCallStatus') {
            $filterQry .= ' tWCS.callStatus ' . $orderBy . ', ';
        }

        if ($notesType) {
            $filterQry .= ' MAX(tpc.notesDate) DESC, ';
        }

        if ($genCSV == 'Yes') {
            $filterQry .= ' tfu.lastUpdatedDate desc, tl.LMRId';
        } else {
            $filterQry .= ' tfu.lastUpdatedDate ' . $orderBy . ', tl.LMRId  LIMIT ' . intval($pageNumber) . ', ' . intval($noOfRecords);
        }

        return $filterQry;
    }

    private static function massTableQuery2(
        $sortOpt,
        $userGroup,
        $salesRep,
        $salesPCID,
        $assignedBranchId,
        $assignedCFPBFileID,
        $searchField,
        $billingArray,
        $PCID
    ): string
    {
        $massTableQuery2 = '';

        if ($sortOpt == 'daysInStatus') {
            $massTableQuery2 .= ' JOIN tblRecordFileStatus trf ON trf.LMRId = tl.LMRId 
            JOIN  ( SELECT MAX(rID) AS latestRID FROM tblRecordFileStatus t1, tblFile t2 
            WHERE t1.LMRID=t2.LMRID ';

            if ($userGroup == 'Sales' || trim($salesRep)) {
                $massTableQuery2 .= ' AND t2.FPCID IN (' . $salesPCID . ') ';
            }
            if ($PCID) {
                $massTableQuery2 .= ' AND t2.FPCID = :PCID ';
                self::$params['PCID'] = $PCID;
            }
            if ($userGroup == 'CFPB Auditor') {
                $massTableQuery2 .= ' AND t2.FBRID IN (' . $assignedCFPBFileID . ') ';
            }
            if ($assignedBranchId) {
                $massTableQuery2 .= ' AND t2.FBRID IN (' . $assignedBranchId . ') ';
            }

            $massTableQuery2 .= ' GROUP BY t1.LMRID ) AS trf2 ON trf.rID = trf2.latestRID 
                               ';
        }

        for ($i = 0; $i < count($searchField); $i++) {
            if (in_array(trim($searchField[$i]), $billingArray) && ($searchField[$i]) == 'tcc.CCNumber') {
                $massTableQuery2 .= ' JOIN tblLMRCCInfo tcc ON tl.LMRId = tcc.LMRID  ';
                self::$accountQry .= ' JOIN tblLMRCCInfo tcc ON tl.LMRId = tcc.LMRID  ';
            }
            if (in_array(trim($searchField[$i]), $billingArray) && trim($searchField[$i]) == 'tca.accountNo') {
                $massTableQuery2 .= ' JOIN tblACHInfo tca ON tl.LMRId = tca.LMRID ';
                self::$accountQry .= ' JOIN tblACHInfo tca ON tl.LMRId = tca.LMRID ';
            }
        }

        return $massTableQuery2;
    }

    private static function massTableQueryClosing(
        $closingStartDate,
        $closingEndDate,
        $paymentBased
    ): string
    {
        $massTableQueryClosing = '';
        if ($closingStartDate || $closingEndDate) {
            $massTableQueryClosing .= ' JOIN tblQAInfo tqa ON tl.LMRId = tqa.LMRId ';
        }
        if($paymentBased){
            $massTableQueryClosing .= ' LEFT JOIN tblFileHMLONewLoanInfo tnl ON tl.LMRId = tnl.fileID ';
        }


        return $massTableQueryClosing;
    }

    private static function massTableQuery1(
        $userRole,
        $restFile,
        $UID,
        $employeeId,
        $LMRClientType,
        $sortOpt,
        $WFID,
        $LMRInternalClientType,
        $statusOpt,
        $notesType,
        $billingDueDate,
        $WFSID,
        $WFSNotCompletedId,
        $WFStepIDs,
        $multipleStatus,
        $multipleModuleCode,
        $searchField,
        $contactId,
        $openHouseDate,
        $servicingStatusCode,
        $referringParty,
        $maturityStartDate,
        $maturityEndDate,
        $appraisalStartDate,
        $appraisalEndDate,
        $receivedStartDate,
        $receivedEndDate,
        $disclosureSentDateStart,
        $disclosureSentDateEnd,
        $targetClosingStartDate,
        $targetClosingEndDate,
        $contactsSearch
    ): string
    {
        $massTableQuery1 = ' 
        FROM tblFile tl 
        JOIN tblFileResponse tlr ON tlr.LMRId = tl.LMRId AND tlr.activeStatus = tl.activeStatus 
        JOIN tblFileUpdatedDate tfu ON tl.LMRId = tfu.fileID 
        left join tblProperties tp on tl.LMRId = tp.LMRId
        left join tblPropertiesCharacteristics tPCH on tp.propertyId = tPCH.propertyId
        left join tblPropertiesDetails tPD on tp.propertyId = tPD.propertyId

        ';
        if ($userRole == 'Auditor' || $restFile == 'LA') {
            $massTableQuery1 .= ' JOIN tblFileLoanAudit tfl ON tl.LMRId = tfl.fileID JOIN tblPCAuditor t5 ON t5.PCID = tl.FPCID AND t5.auditorID>0 JOIN tblAdminUsers tu ON tu.AID = t5.auditorID AND tu.activeStatus = 1 ';
            if ($userRole == 'Auditor') {
                $massTableQuery1 .= ' AND t5.auditorID = :UID ';
                self::$params['UID'] = $UID;
            }
        }
        if ($userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager' || $restFile == 'CFPB') {
            $massTableQuery1 .= " JOIN tblFileCFPB tcfpb ON tl.LMRId = tcfpb.LMRID AND COALESCE(tcfpb.submittedDate, '') AND tcfpb.activeStatus = 1 ";
        }

        if (sizeof($employeeId)) {
            $massTableQuery1 .= ' JOIN tblFileUsers te ON tl.LMRId = te.fileID';
        }
        if(!is_array($WFID)) {
            $WFID = [$WFID];
        }
        if ($LMRClientType || $sortOpt == 'seviceTypes' || sizeof($WFID)) {
            $massTableQuery1 .= ' JOIN tblLMRClientType tct ON tl.LMRId = tct.LMRId';
        }
        if(count($LMRInternalClientType) || $sortOpt == 'LMRInternalLoanProgram') {
            if($sortOpt == 'LMRInternalLoanProgram') {
                $massTableQuery1 .= ' LEFT ';
            }
            $massTableQuery1 .= ' JOIN tblFileInternalLoanPrograms tFIL ON tl.LMRId = tFIL.LMRID ';
            if($sortOpt == 'LMRInternalLoanProgram') {
                $massTableQuery1 .= ' LEFT JOIN tblLibServiceTypes tLST2 ON tFIL.internalLoanProgram = tLST2.STCode and tLST2.activeStatus = 1 ';
            }
        }
        if ($statusOpt) {
            $massTableQuery1 .= ' JOIN tblFileSubstatus ts ON tl.LMRId = ts.fileID';
        }
        if ($notesType) {
            $massTableQuery1 .= ' JOIN tblLMRProcessorComments tpc ON tl.LMRId = tpc.fileID';
        }
        if ($billingDueDate) {
            $massTableQuery1 .= ' JOIN tblLMRBilling bill ON tl.LMRId = bill.LMRId AND bill.rStatus = 1 JOIN tblLMRBillingPayment pay ON tl.LMRId = pay.LMRId AND pay.rStatus = 1 AND bill.LMRId = pay.LMRId AND bill.phase=pay.phase ';
        }

        // restFile = 2 means allow users to see REST File pipeline
        if ($userRole == 'REST' || $restFile == 2) {
            $massTableQuery1 .= ' JOIN tblRestInfo tre ON tl.LMRId = tre.LMRId ';
        }

        if (in_array($sortOpt , ['disclosureSentDate','targetSubmissionDate','welcomeCallStatus'])
            || ($disclosureSentDateStart || $disclosureSentDateEnd)) {
            $massTableQuery1 .= ' LEFT JOIN tblFileAdminInfo tfa ON tl.LMRId = tfa.LMRId ';
        }
        if ($sortOpt == 'welcomeCallStatus') {
            $massTableQuery1 .= ' LEFT JOIN tblWelcomeCallStatus tWCS ON tfa.welcomeCallStatus = tWCS.id ';
        }
// HOMEReportFile = 4 means allow users to see HOME Report File pipeline
        if ($restFile == 4) {
            $massTableQuery1 .= ' JOIN tblHomeReport thr ON tl.LMRId = thr.fileID 
                                   JOIN (SELECT MIN(th1.SubmissionDate) AS SubmissionDate, th1.fileID FROM tblHomeReportHistory th1 GROUP BY th1.fileID) AS thrh ON thr.fileID = thrh.fileID ';
        }

        /** To include ONLY sorting by agent name **/
        if ($sortOpt == 'name') {
            $massTableQuery1 .= ' JOIN tblAgent tb ON tl.brokerNumber = tb.userNumber and tb.status = 1 ';
        }

        if ($sortOpt == 'loanOfficerName') {
            $massTableQuery1 .= ' JOIN tblAgent tb ON tl.secondaryBrokerNumber = tb.userNumber and tb.status = 1 ';
        }

        /** To include ONLY sorting by branch name **/
        if ($sortOpt == 'branchName') {
            $massTableQuery1 .= ' JOIN tblBranch tle ON tl.FBRID = tle.executiveId AND tle.activeStatus = 1 ';
        }
        if (sizeof($WFID)) {
            $massTableQuery1 .= ' JOIN tblLMRWorkflow tfw ON tl.LMRId = tfw.LMRID AND tfw.dStatus = 1
                                  JOIN tblPCWFServiceType tPCWS ON tfw.WFID = tPCWS.WFID AND tct.ClientType = tPCWS.WFServiceType ';
        }
        if (sizeof($WFSID)) {
            $massTableQuery1 .= ' LEFT  JOIN tblLMRWorkflowSteps tfs ON tl.LMRId = tfs.LMRID AND tfw.dStatus = 1 ';
        }


//        if (($WFSID || $WFStatus > 1)) {
//            if ($workFlowStepStatus == 0) {
//                $massTableQuery1 .= ' LEFT ';
//            }
//            $massTableQuery1 .= '  JOIN tblLMRWorkflowSteps tfs ON tl.LMRId = tfs.LMRID AND tfw.dStatus = 1 ';
//        }
        if ($WFStepIDs) {
            $massTableQuery1 .= ' JOIN tblFileSecondaryWFStatus tfsw ON tl.LMRId = tfsw.fileID ';
        }
        if ($multipleStatus || $multipleModuleCode || $sortOpt == 'primeStatus') {
            $massTableQuery1 .= ' JOIN tblPCPrimeStatus tps ON tlr.primeStatusId = tps.PSID  ';
        }
        if ($multipleModuleCode) {
            $massTableQuery1 .= ' JOIN tblPCPrimeStatusModules tpsm ON tps.PSID = tpsm.primeStatusId
            AND tlr.primeStatusId = tpsm.primeStatusId 
            JOIN tblFileModules tfm ON tl.LMRId = tfm.fileID AND tpsm.moduleCode = tfm.moduleCode ';
        }

        if ((in_array('ent.entityName', $searchField))
            || ($sortOpt == 'avgMonthlyCreditCardSales'
                || $sortOpt == 'avgTotalMonthlySales'
                || $sortOpt == 'annualGrossProfit'
                || $sortOpt == 'netBusinessIncome'
                || $sortOpt == 'entityName'
                || $sortOpt == 'entityType'
                || $sortOpt == 'stateOfFormation'
                || $sortOpt == 'entityCity'
                || $sortOpt == 'entityState'
                || $sortOpt == 'propertyOwnership'
                || $sortOpt == 'ofEmployees')
        ) {
            $massTableQuery1 .= ' LEFT JOIN tblFileHMLOBusinessEntity ent ON tl.LMRId = ent.fileID ';
        }

        if ($referringParty ||
            $maturityStartDate ||
            $maturityEndDate ||
            $servicingStatusCode
        ) {
            $massTableQuery1 .= " JOIN tblFileHMLOPropInfo tpi ON tl.LMRId = tpi.fileID AND tlr.LMRId = tpi.fileID ";

            if ($referringParty) { //  && $servicingStatusCode != '' ???
                $massTableQuery1 .= "  AND tpi.referringParty LIKE :referringParty ";
                self::$params['referringParty'] = '%' . $referringParty . '%';
            }
        }

        if ($targetClosingStartDate || $targetClosingEndDate || $sortOpt == 'authorizationStatus') {
            $massTableQuery1 .= 'LEFT JOIN tblFileHMLO tfh ON tl.LMRId = tfh.fileID AND tlr.LMRId = tfh.fileID ';
        }
        if($sortOpt == 'authorizationStatus') {
            $massTableQuery1 .= 'LEFT  JOIN tblBorrowerAuthorizationStatus tBAS ON tfh.authorizationStatus = tBAS.id ';
        }
       if(in_array($sortOpt, ['VOMStatus','payoffStatus'])) {
            $massTableQuery1 .= 'LEFT JOIN (
                    SELECT MIN(id) as RefinanceMortgageId,LMRId,VOMStatus,payoffStatus FROM tblRefinanceMortgage t1000 group by LMRId
               ) as RefinanceMortgage on RefinanceMortgage.LMRId = tlr.LMRId
               LEFT  JOIN tblVOMPayoffStatus tVPS_VOMStatus ON tVPS_VOMStatus.id = RefinanceMortgage.VOMStatus 
               LEFT  JOIN tblVOMPayoffStatus tVPS_payoffStatus ON tVPS_VOMStatus.id = RefinanceMortgage.payoffStatus 
               ';
        }

        if($appraisalStartDate || $appraisalEndDate){
            $massTableQuery1 .= ' LEFT JOIN tblShortSale tss ON tl.LMRId = tss.LMRId AND tlr.LMRId = tss.LMRId ';
        }

        /**
         * Description : Filter the contact list
         * Developer   : Venkatesh R
         * Date        : Jan 11, 2018
         **/
        if ($contactId) {
            $massTableQuery1 .= ' JOIN tblFileContacts tfc ON tl.LMRId = tfc.fileID AND tfc.CID = :contactId ';
            self::$params['contactId'] = $contactId;
        }
        if(sizeof($contactsSearch ?? [])){
            $massTableQuery1 .= ' JOIN tblFileContacts tfc ON tl.LMRId = tfc.fileID ';
        }

        /**
         * Description : Filter the date of open house
         * Developer   : Berin Y
         * Date        : May 22, 2018.
         **/
        if ($openHouseDate == 'Yes') {
            $massTableQuery1 .= " JOIN tblShortSale2 tss2
                ON tl.LMRId = tss2.LMRId AND DATE(STR_TO_DATE(tss2.openHouseDate, '%Y-%m-%d')) >= CURDATE() ";
        }

        return $massTableQuery1;
    }

    private static function massTableSubQuery2(
        $openHouseDate,
        $sortOpt,
        $closingStartDate,
        $closingEndDate,
        $massTableQuery1,
        $referringParty,
        $paymentBased
    ): string
    {
        $massTableSubQuery2 = '';

        if ($openHouseDate == 'No') {
            $massTableSubQuery2 .= ' LEFT JOIN tblShortSale2 tss2 ON tl.LMRId = tss2.LMRId ';
        }

        /** To include ONLY sorting by branch name - Dec 20, 2017 by Suresh K **/

        if ($sortOpt == 'closingDate') {
            $massTableSubQuery2 .= " JOIN tblQAInfo tqa ON tl.LMRId = tqa.LMRId AND DATE(STR_TO_DATE(tqa.closingDate, '%Y-%m-%d')) >= CURDATE()";
        }
        if ($sortOpt == 'targetClosingDate' || $sortOpt == 'desiredClosingDate' || $closingStartDate || $closingEndDate) {
            $massTableSubQuery2 .= ' LEFT JOIN tblQAInfo tqa ON tl.LMRId = tqa.LMRId ';
        }
        if ($sortOpt == 'adminTargetClosingDate') {
            $massTableSubQuery2 .= ' JOIN tblFileHMLO tfh ON tl.LMRId = tfh.fileID AND tlr.LMRId = tfh.fileID ';
        }
        if ($sortOpt == 'repairValue' || $sortOpt == 'purchasePrice' || $sortOpt == 'ASISValue') { // After Repair Value & Purchase Price Sorting added Pivotal # 153672200
            $massTableSubQuery2 .= ' LEFT JOIN tblShortSale tss ON tl.LMRId = tss.LMRId ';
        }
        if ($sortOpt == 'networthOfBusinessOwned') { // After Repair Value & Purchase Price Sorting added Pivotal # 153672200
            $massTableSubQuery2 .= ' LEFT JOIN tblFileLOAssetsInfo tflo ON tl.LMRId = tflo.fileID ';
        }
        if ($sortOpt == 'borNoOfREPropertiesCompleted'
            || $sortOpt == 'borRehabPropCompleted'
            || $sortOpt == 'borNoOfOwnProp'
            || $sortOpt == 'trackRecord') {
            $massTableSubQuery2 .= ' LEFT JOIN tblFileHMLOExperience tfei ON tl.LMRId = tfei.fileID ';
        }
        if ( $sortOpt == 'trackRecord') {
            $massTableSubQuery2 .= ' LEFT JOIN tblBorrowerExperienceTrackRecord tBETR ON tfei.trackRecord = tBETR.id ';
        }
        if ($sortOpt == 'isBorUSCitizen') {
            $massTableSubQuery2 .= ' LEFT JOIN tblFileHMLOBackGround tfhbg ON tl.LMRId = tfhbg.fileID ';
        }
        if ($sortOpt == 'HMLOLender' || $paymentBased) {
            $massTableSubQuery2 .= ' LEFT JOIN tblFileHMLONewLoanInfo tnl ON tl.LMRId = tnl.fileID ';
        }
        if ($sortOpt == 'entity') {
            $massTableSubQuery2 .= ' LEFT JOIN tblFileHMLOBusinessEntity tent ON tl.LMRId = tent.fileID ';
        }


        /** Total Loan Amount Sorting Added - Jan 12, 2018 - Pivotal # 153672200 Start**/
        if ($sortOpt == 'HMLOLoanType'
            || $sortOpt == 'HMLOTotalLoanAmt'
            || $sortOpt == 'marketLTV'
            || $sortOpt == 'totalProjectCost'
            || $sortOpt == 'LTC'
            || $sortOpt == 'ARV'
            || $sortOpt == 'availableBudget'
            || $sortOpt == 'rehabConstructionCost'
            || $sortOpt == 'monthlyLoanAmount'
            || $sortOpt == 'PSAClosingDate'
            || $sortOpt == 'buildingAnalysisOutstanding'
            || $sortOpt == 'buildingAnalysisNeed'
            || $sortOpt == 'buildingAnalysisDueDate'
        ) {
            $massTableSubQuery2 .= ' 
            LEFT JOIN tblShortSale tss ON tl.LMRId = tss.LMRId AND tlr.LMRId = tss.LMRId
            LEFT JOIN tblFileHMLONewLoanInfo tnl ON tl.LMRId = tnl.fileID 
            ';
        }

        if($sortOpt == 'buildingAnalysisOutstanding') {
            $massTableSubQuery2 .= ' 
                 LEFT JOIN tblBuildingAnalysisOutstanding tBAO ON tnl.buildingAnalysisOutstanding = tBAO.id 
            ';
        }
        if($sortOpt == 'buildingAnalysisNeed') {
            $massTableSubQuery2 .= ' 
                 LEFT JOIN tblBuildingAnalysisNeed tBAN ON tnl.buildingAnalysisNeed = tBAN.id 
            ';
        }
        if ($sortOpt == 'monthlyLoanAmount') {
            $massTableSubQuery2 .= ' LEFT JOIN  tblFileSBAQuestions tsba ON tsba.fileID = tnl.fileID ';
        }
        /** Total Loan Amount Sorting Added - Jan 12, 2018 - Pivotal # 153672200 End **/

        if ($sortOpt == 'acquisitionLTV'
            || $sortOpt == 'maturityDate'
            || $sortOpt == 'payOffDate'
            || $sortOpt == 'loanSaleDate'
            || $sortOpt == 'referringParty'
            || $sortOpt == 'appraisal1OrderDate'
            || $sortOpt == 'dateObtained'
        ) {

            $massTableSubQuery2 .= ' LEFT JOIN tblShortSale tss ON tl.LMRId = tss.LMRId AND tlr.LMRId = tss.LMRId ';

            if ($referringParty == '' && strpos($massTableQuery1, ' tblFileHMLOPropInfo ') === false) {
                $massTableSubQuery2 .= ' LEFT JOIN tblFileHMLOPropInfo tpi ON tl.LMRId = tpi.fileID AND tlr.LMRId = tpi.fileID ';
            }
        }
        /** Total Rehab Cost Sorting Added - Jan 12, 2018 - Pivotal # 153672200 Start**/
        if ($sortOpt == 'totalRehabCost') {
            $massTableSubQuery2 .= ' LEFT JOIN tblFileHMLOListOfRepairs tflr ON tl.LMRId = tflr.fileID ';
        }

        if ($sortOpt == 'proInsPolicyExpDate') {
            $massTableSubQuery2 .= ' LEFT  JOIN tblInsuranceDetails tmp1 ON tl.LMRId = tmp1.fileID AND tmp1.activeStatus = 1 ';
        }

        return $massTableSubQuery2;
    }

    private static function massTableSubQuery1($sortOpt): string
    {
        $massTableSubQuery1 = '';

        /** Total Loan Amount Sorting Added - Jan 12, 2018 - Pivotal # 153672200 Start**/
        if ($sortOpt == 'HMLOTotalLoanAmt') {
            $massTableSubQuery1 = ', SUM(tlr.totalLoanAmount) AS totalAmt ';
        }
        /** Total Loan Amount Sorting Added - Jan 12, 2018 - Pivotal # 153672200 End **/

        if ($sortOpt == 'monthlyLoanAmount') {
            $massTableSubQuery1 = ', tsba.monthlyLoanAmount ';
        }

        /** Market LTV Sorting Start **/
        if ($sortOpt == 'marketLTV') {
            $massTableSubQuery1 = ", 
            SUM(
            CASE WHEN tnl.typeOfHMLOLoanRequesting = 'Cash-Out / Refinance' OR tnl.typeOfHMLOLoanRequesting = 'Rate & Term Refinance'
                OR tnl.typeOfHMLOLoanRequesting = 'Commercial Rate / Term Refinance' OR tnl.typeOfHMLOLoanRequesting = 'Commercial Cash Out Refinance'
                OR tnl.typeOfHMLOLoanRequesting = 'Line of Credit' OR tnl.typeOfHMLOLoanRequesting = 'Refinance'
                THEN
                    CASE WHEN COALESCE (functionToRemoveSpecialChar(tl.homeValue), 0)
                        THEN ((COALESCE (functionToRemoveSpecialChar(tlr.totalLoanAmount), 0) / COALESCE (functionToRemoveSpecialChar(tl.homeValue), 0)) * 100)
                    END
            ELSE
                    CASE WHEN COALESCE (functionToRemoveSpecialChar(tl.homeValue), 0)
                        THEN (((COALESCE (functionToRemoveSpecialChar(tnl.costBasis), 0) - COALESCE (functionToRemoveSpecialChar(tnl.maxAmtToPutDown), 0) ) / COALESCE (functionToRemoveSpecialChar(tl.homeValue), 0) ) * 100)
                    END
            END) AS marketLTV ";
        }
        /** Market LTV Sorting End **/

        /** Total Project Cost Sorting Start **/
        if ($sortOpt == 'totalProjectCost') {
            $massTableSubQuery1 = ", ( CASE
                               WHEN typeOfHMLOLoanRequesting = 'Cash-Out / Refinance' OR typeOfHMLOLoanRequesting = 'Commercial Rate / Term Refinance' OR typeOfHMLOLoanRequesting = 'Commercial Cash Out Refinance' OR typeOfHMLOLoanRequesting = 'Refinance'

                    THEN (COALESCE (functionToRemoveSpecialChar(tss.assessedValue), 0) + COALESCE (functionToRemoveSpecialChar(tnl.rehabCost), 0))
                WHEN typeOfHMLOLoanRequesting = 'Line of Credit'
                    THEN ( COALESCE (functionToRemoveSpecialChar(tnl.rehabCost), 0) + COALESCE (functionToRemoveSpecialChar(tnl.closingCostFinanced), 0) )
                ELSE ( COALESCE (functionToRemoveSpecialChar(tnl.costBasis), 0) + COALESCE (functionToRemoveSpecialChar(tnl.rehabCost), 0) + COALESCE (functionToRemoveSpecialChar(tnl.closingCostFinanced), 0) )
                END
                ) AS totalProjectCost ";
        }
        /** Total Project Cost Sorting End **/

        /** Total Project Cost Sorting Start **/
        if ($sortOpt == 'LTC') {
            $massTableSubQuery1 = ", ( CASE
                WHEN ( CASE
                    WHEN typeOfHMLOLoanRequesting = 'Cash-Out / Refinance' OR typeOfHMLOLoanRequesting = 'Commercial Rate / Term Refinance' OR typeOfHMLOLoanRequesting = 'Commercial Cash Out Refinance' OR typeOfHMLOLoanRequesting = 'Refinance'
                        THEN (COALESCE (functionToRemoveSpecialChar(tss.assessedValue), 0) + COALESCE (functionToRemoveSpecialChar(tfhm.rehabCost), 0))
                    WHEN typeOfHMLOLoanRequesting = 'Line of Credit'
                        THEN ( COALESCE (functionToRemoveSpecialChar(tfhm.rehabCost), 0) + COALESCE (functionToRemoveSpecialChar(tnl.closingCostFinanced), 0) )
                    ELSE ( COALESCE (functionToRemoveSpecialChar(tss.costBasis), 0) + COALESCE (functionToRemoveSpecialChar(tfhm.rehabCost), 0) + COALESCE (functionToRemoveSpecialChar(tnl.closingCostFinanced), 0) )
                    END )
                    THEN (
                        (SUM(
                            CASE WHEN typeOfHMLOLoanRequesting = 'Line of Credit'
                                THEN tnl.LOCTotalLoanAmt
                            WHEN typeOfHMLOLoanRequesting = 'Commercial Cash Out Refinance' OR typeOfHMLOLoanRequesting = 'Cash-Out / Refinance'  OR typeOfHMLOLoanRequesting = 'Refinance'
                                THEN tnl.CORTotalLoanAmt
                            ELSE
                                CASE WHEN typeOfHMLOLoanRequesting = 'Rate & Term Refinance' OR typeOfHMLOLoanRequesting = 'Commercial Rate / Term Refinance'  
                                    THEN (
                                        COALESCE (functionToRemoveSpecialChar(tnl.payOffMortgage1), 0)
                                        + COALESCE (functionToRemoveSpecialChar(tnl.payOffMortgage2), 0)
                                        + COALESCE (functionToRemoveSpecialChar(tnl.payOffOutstandingTaxes), 0)
                                        + COALESCE (functionToRemoveSpecialChar(tnl.cashOutAmt), 0)
                                        + COALESCE (functionToRemoveSpecialChar(tnl.closingCostFinanced), 0)
                                        + COALESCE (functionToRemoveSpecialChar(( COALESCE(functionToRemoveSpecialChar(tnl.rehabCost), 0) *
                                        ( COALESCE(functionToRemoveSpecialChar(tnl.rehabCostPercentageFinanced), 0) /100) )), 0)
                                    )
                                ELSE (
                                    (COALESCE (functionToRemoveSpecialChar(tss.costBasis), 0) - COALESCE (functionToRemoveSpecialChar(tpi.maxAmtToPutDown), 0))
                                    + COALESCE (functionToRemoveSpecialChar(( COALESCE(functionToRemoveSpecialChar(tnl.rehabCost), 0) *
                                    ( COALESCE(functionToRemoveSpecialChar(tnl.rehabCostPercentageFinanced), 0) /100) )), 0)
                                    + COALESCE (functionToRemoveSpecialChar(tnl.closingCostFinanced), 0)
                                    ) 
                                END
                            END )) / (( CASE
                                WHEN typeOfHMLOLoanRequesting = 'Cash-Out / Refinance' OR typeOfHMLOLoanRequesting = 'Commercial Rate / Term Refinance' OR typeOfHMLOLoanRequesting = 'Commercial Cash Out Refinance'  OR typeOfHMLOLoanRequesting = 'Refinance'
                                    THEN (COALESCE (functionToRemoveSpecialChar(tss.assessedValue), 0) + COALESCE (functionToRemoveSpecialChar(tfhm.rehabCost), 0))
                                WHEN typeOfHMLOLoanRequesting = 'Line of Credit'
                                    THEN ( COALESCE (functionToRemoveSpecialChar(tfhm.rehabCost), 0) + COALESCE (functionToRemoveSpecialChar(tnl.closingCostFinanced), 0) )
                                ELSE ( COALESCE (functionToRemoveSpecialChar(tss.costBasis), 0) + COALESCE (functionToRemoveSpecialChar(tfhm.rehabCost), 0) + COALESCE (functionToRemoveSpecialChar(tnl.closingCostFinanced), 0) )
                            END
                        ))
                    ) * 100
                END ) AS LTC ";
        }
        /** Total Project Cost Sorting End **/

        /** AVR Sorting Start **/
        if ($sortOpt == 'ARV') {
            $massTableSubQuery1 = ", CASE
            WHEN tl.homeValue
                THEN (SUM(
                    CASE WHEN typeOfHMLOLoanRequesting = 'Line of Credit'
                        THEN tnl.LOCTotalLoanAmt
                    WHEN typeOfHMLOLoanRequesting = 'Commercial Cash Out Refinance' OR typeOfHMLOLoanRequesting = 'Cash-Out / Refinance'  OR typeOfHMLOLoanRequesting = 'Refinance'
                        THEN tnl.CORTotalLoanAmt
                    ELSE
                        CASE WHEN typeOfHMLOLoanRequesting = 'Rate & Term Refinance' OR typeOfHMLOLoanRequesting = 'Commercial Rate / Term Refinance'  
                            THEN (
                                COALESCE (functionToRemoveSpecialChar(tnl.payOffMortgage1), 0)
                                + COALESCE (functionToRemoveSpecialChar(tnl.payOffMortgage2), 0)
                                + COALESCE (functionToRemoveSpecialChar(tnl.payOffOutstandingTaxes), 0)
                                + COALESCE (functionToRemoveSpecialChar(tnl.cashOutAmt), 0)
                                + COALESCE (functionToRemoveSpecialChar(tnl.closingCostFinanced), 0)
                                + COALESCE (functionToRemoveSpecialChar(( COALESCE(functionToRemoveSpecialChar(tnl.rehabCost), 0) *
                                ( COALESCE(functionToRemoveSpecialChar(tnl.rehabCostPercentageFinanced), 0) /100) )), 0)
                            )
                        ELSE (
                            (COALESCE (functionToRemoveSpecialChar(tss.costBasis), 0)
                            - COALESCE (functionToRemoveSpecialChar(tpi.maxAmtToPutDown), 0))
                            + COALESCE (functionToRemoveSpecialChar(( COALESCE(functionToRemoveSpecialChar(tnl.rehabCost), 0) *
                            ( COALESCE(functionToRemoveSpecialChar(tnl.rehabCostPercentageFinanced), 0) /100) )), 0)
                            + COALESCE (functionToRemoveSpecialChar(tnl.closingCostFinanced), 0)
                        )
                        END
                    END
                ) / COALESCE (functionToRemoveSpecialChar(tl.homeValue), 0) * 100 )
            END AS ARV ";
        }
        /** AVR Sorting End **/

        /** Total Rehab Cost Sorting Added - Jan 12, 2018 - Pivotal # 153672200 Start**/
        if ($sortOpt == 'totalRehabCost') {

            $massTableSubQuery1 = ', SUM(
              COALESCE (functionToRemoveSpecialChar(tflr.architectFees), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.permitsFees), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.demolitionTrashDumpsters), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.exteriorRepairs), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.termiteInspectionTreatment), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.foundationStructuralReport), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.roofing), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.windows), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.doors), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.siding), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.carpentry), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.deckPorch), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.drivewayWalkwayPatio), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.landscaping), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.exteriorRepairsOther), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.HVACRough), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.HVACFinish), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.plumbingRough), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.plumbingFixtures), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.plumbingFinish), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.electricalRough), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.electricalFixtures), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.electricalFinish), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.sheetRock), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.interiorRepairsDoors), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.interiorRepairsCarpentry), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.interiorRepairsOther1), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.interiorRepairsOther2), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.interiorRepairsOther3), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.kitchenCabinets), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.kitchenCountertops), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.kitchenAppliances), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.bath1), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.bath2), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.bath3), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.interiorPainting), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.exteriorPainting), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.flooringCarpetVinyl), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.flooringTile), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.flooringHardwood), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.finalCleanupOther1), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.finalCleanupOther2), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.finalCleanupOther3), 0)
            + COALESCE (functionToRemoveSpecialChar(tflr.finalCleanupOther4), 0)
            ) AS totalRehabCost ';

        }
        /** Total Rehab Cost Sorting Added - Jan 12, 2018 - Pivotal # 153672200 End **/

        if ($sortOpt == 'acquisitionLTV') {
            $massTableSubQuery1 = ", CASE WHEN (tss.costBasis - tpi.maxAmtToPutDown) AND tpi.propertyNeedRehab = 'Yes'
                THEN ( ( (tss.costBasis - tpi.maxAmtToPutDown) / tss.costBasis ) * 100 ) END AS acquisitionLTV ";
        }

        if ($sortOpt == 'availableBudget') {
            $massTableSubQuery1 = ' (COALESCE(
                functionToRemoveSpecialChar(
                    SELECT SUM(drawFunded) FROM tblBudgetAndDraws tbgd WHERE tbgd.LMRId = tl.LMRId
                ),
            0) - (COALESCE(functionToRemoveSpecialChar(tnl.rehabCost), 0) *
            (COALESCE(functionToRemoveSpecialChar(tnl.rehabCostPercentageFinanced), 0) /100))) as availableBudget ';
        }

        return $massTableSubQuery1;
    }

    private static function LMRClientType(array $LMRClientType): string
    {

        $tempClientType = '"' . implode('", "', $LMRClientType) . '"';
        $LMRClientType = '';

        $sql = 'CALL SP_GetServiceTypesForAllPC (
                    :serviceType,
                    :opt
            );';

        $params = [
            'serviceType' => $tempClientType,
            'opt' => 'SBYT',
        ];

        $allPCServiceInfoArray = Database2::getInstance()->multiQueryData($sql, 'myFld', $params);

        if (count($allPCServiceInfoArray)) {
            $LMRClientType = "'" . implode("','", array_keys($allPCServiceInfoArray)) . "'";
        }
        return $LMRClientType;
    }

    private static function assignedCFPBFileIDArray($UID): array
    {
        $assignedCFPBFileIDArray = [];

        $qry = "
SELECT 
    fileID 
FROM tblFileAssignedCFPBAuditors 
WHERE UID = :UID 
  AND URole = :URole 
  AND COALESCE(dateRemoved, '') = :dateRemoved 
  ";
        $sqlParams = [
            'UID' => $UID,
            'URole' => 'Employee',
            'dateRemoved' => '',
        ];
        $assignCFPBFileIDArray = Database2::getInstance()->queryData($qry, $sqlParams);
        for ($i = 0; $i < count($assignCFPBFileIDArray); $i++) {
            $assignedCFPBFileIDArray[] = $assignCFPBFileIDArray[$i]['fileID'];
        }
        return $assignedCFPBFileIDArray;
    }

    private static function PCArray($salesRep): array
    {
        $PCArray = [];
        $qry = '
SELECT 
    PCID 
FROM tblProcessingCompany
WHERE salesRep = :salesRep 
  AND activeStatus  = :activeStatus 
  AND dStatus = :dStatus
';
        $sqlParams = [
            'salesRep' => $salesRep,
            'activeStatus' => 1,
            'dStatus' => 0,
        ];
        $salesPCArray = Database2::getInstance()->queryData($qry, $sqlParams);
        for ($i = 0; $i < count($salesPCArray); $i++) {
            $PCArray[] = $salesPCArray[$i]['PCID'];
        }
        return $PCArray;
    }

    private static function myFileIDsArray($UID): array
    {
        $myFileIDsArray = [];
        $qry = "
SELECT 
    fileID 
FROM tblFileUsers
WHERE UID IN ( :UID ) 
  AND URole = :URole
  AND COALESCE(dateRemoved, '') = :dateRemoved ;
";
        $myFileIDArray = Database2::getInstance()->queryData($qry, [
            'UID' => $UID,
            'URole' => 'Employee',
            'dateRemoved' => '',
        ]);
        for ($i = 0; $i < count($myFileIDArray); $i++) {
            $myFileIDsArray[] = $myFileIDArray[$i]['fileID'];
        }
        return $myFileIDsArray;
    }

    private static function allowToViewAllFiles($UID)
    {
        $allowToViewAllFiles = 0;
        $qry = 'SELECT allowToViewAllFiles FROM tblAdminUsers WHERE AID = :AID AND activeStatus = :activeStatus ';
        $tempViewFileArray = Database2::getInstance()->queryData($qry, [
            'AID' => $UID,
            'activeStatus' => 1,
        ], null, true);


        if (count($tempViewFileArray)) {
            $allowToViewAllFiles = $tempViewFileArray['allowToViewAllFiles'];
        }
        return $allowToViewAllFiles;
    }

    private static function assignedBranchArray($PCID, $UID): array
    {
        $assignedBranchArray = [];
        $sqlParams = [];

        $qry = ' SELECT tl.LMRAEID FROM tblBranchEmployees tl, tblAdminUsers ta  WHERE
            tl.EID=ta.AID AND 1  ';

        $PCIDList = explode(',', $PCID);
        if (sizeof($PCIDList)) {
            $qry .= ' AND ta.processingCompanyId IN ( ' . Database2::GetPlaceholders(sizeof($PCIDList), ':processingCompanyId', true) . ' )';
            foreach ($PCIDList as $i => $paVal) {
                $sqlParams['processingCompanyId' . $i] = trim($paVal);
            }
        }

        $UIDList = explode(',', $UID);
        if (sizeof($UIDList)) {
            $qry .= ' AND EID IN ( ' . Database2::GetPlaceholders(sizeof($UIDList), ':EID', true) . ' )';
            foreach ($UIDList as $i => $paVal) {
                $sqlParams['EID' . $i] = trim($paVal);
            }
        }

        $assignBrArray = Database2::getInstance()->queryData($qry, $sqlParams);
        for ($i = 0; $i < count($assignBrArray); $i++) {
            $assignedBranchArray[] = $assignBrArray[$i]['LMRAEID'];
        }
        return $assignedBranchArray;
    }

    /**
     * @param $ip
     * @return array
     */
    public static function getReport($ip): array
    {
        $LMRIDArray = [];
        $resultArray = [];
        $assignedBranchId = '';
        $salesPCID = '';
        $assignedCFPBFileID = 0;
        $RESTProcId = 0;
        $myFileIDsArray = [];

        $billingArray = ['tcc.CCNumber', 'tca.accountNo'];


        $externalBroker = $ip['externalBroker'] ?? 0;
        $UID = trim($ip['UID'] ?? 0);
        $userGroup = trim($ip['userGroup'] ?? '');
        $userRole = trim($ip['userRole'] ?? '');
        $PCID = trim($ip['PCID'] ?? 0);
        $employeeId = $ip['employeeId'] ?? [];
        $branchId = $ip['branchId'] ?? [];
        $brokerNumber = $ip['brokerNumber'] ?? [];
        $secondaryBrokerNumber = $ip['secondaryBrokerNumber'] ?? [];
        $statusOpt = $ip['statusOpt'] ?? [];
        $staleDay = trim($ip['staleDay'] ?? '');
        $propertyState = trim($ip['propertyState'] ?? '');
        $priorityLevel = trim($ip['priorityLevel'] ?? '');
        $startDate = trim($ip['startDate'] ?? '');
        $endDate = trim($ip['endDate'] ?? '');
        $lenderName = trim($ip['lenderName'] ?? '');
        $leadSource = trim($ip['leadSource'] ?? '');
        $activeStatus = trim($ip['activeStatus'] ?? 1);
        $LMRClientType = $ip['LMRClientType'] ?? [];
        $LMRInternalClientType = ($ip['LMRInternalClientType'] ?? []);
        $searchTerm = trim($ip['searchTerm'] ?? '');
        $searchField = $ip['searchField'] ?? [];
        $pageNumber = trim($ip['pageNumber'] ?? 0);
        $noOfRecords = trim($ip['noOfRecordsPerPage'] ?? 0);
        $notesType = trim($ip['notesType'] ?? '');
        $sortOpt = trim($ip['sortOpt'] ?? '');
        $orderBy = trim($ip['orderBy'] ?? 'DESC');
        $primeStatusId = $ip['primeStatusId'] ?? [];
        $restFile = trim($ip['restFile'] ?? '');
        $billingDueDate = trim($ip['billingDueDate'] ?? '');
        $subStDate = trim($ip['subStDate'] ?? '');
        $subEndDate = trim($ip['subEndDate'] ?? '');
        $genCSV = trim($ip['genCSV'] ?? '');
        $clientEmail = trim($ip['clientEmail'] ?? '');
        $paymentStatus = trim($ip['paymentStatus'] ?? '');
        $loanAuditStatus = trim($ip['loanAuditStatus'] ?? 'All');
        $CFPBAuditStatus = trim($ip['CFPBAuditStatus'] ?? 'All');
        $salesRep = trim($ip['salesRep'] ?? '');
        $modifiedStartDate = trim($ip['modifiedStartDate'] ?? '');
        $modifiedEndDate = trim($ip['modifiedEndDate'] ?? '');
        $closingStartDate = trim($ip['closingStartDate'] ?? '');
        $closingEndDate = trim($ip['closingEndDate'] ?? '');
        $maturityStartDate = trim($ip['maturityStartDate'] ?? '');
        $maturityEndDate = trim($ip['maturityEndDate'] ?? '');
        $targetClosingStartDate = trim($ip['targetClosingStartDate'] ?? '');
        $targetClosingEndDate = trim($ip['targetClosingEndDate'] ?? '');
        $multipleModuleCode = $ip['multipleModuleCode'] ?? [];
        $clientID = trim($ip['clientID'] ?? '');
        $BRID = trim($ip['BRID'] ?? '');
        $assignedBRID = trim($ip['assignedBRID'] ?? '');
        $leadsBRID = trim($ip['leadsBRID'] ?? '');
        $AID = trim($ip['AID'] ?? 0);
        $leadStatusID = trim($ip['leadStatusID'] ?? 0);
        $WFID = $ip['WFID'] ?? [];
        $WFSID = $ip['WFSID'] ?? [];
        $referringParty = $ip['referringParty'] ?? '';
        $WFSNotCompletedId = $ip['WFSNotCompletedId'] ?? [];
        $servicingStatusCode = $ip['servicingStatusCode'] ?? '';
        $servicingStatusCodeArray = [];
        if($servicingStatusCode){
            $servicingStatusCodeArray = explode(',',$servicingStatusCode);
        }
        $paymentBased = $ip['paymentBased'] ?? '';
        $appraisalStartDate = trim($ip['appraisalStartDate'] ?? '');
        $appraisalEndDate = trim($ip['appraisalEndDate'] ?? '');

        $receivedStartDate = trim($ip['receivedStartDate'] ?? '');
        $receivedEndDate = trim($ip['receivedEndDate'] ?? '');

        $disclosureSentDateStart = trim($ip['disclosureSentDateStart'] ?? '');
        $disclosureSentDateEnd = trim($ip['disclosureSentDateEnd'] ?? '');

        $multipleStatus = '';
        if(is_array($primeStatusId)) {
            if (sizeof($primeStatusId)) {
                $multipleStatus = implode(',', $primeStatusId);
            }
            if(stristr($multipleStatus, 'all') !== false) {
                $multipleStatus = null;
            }
            $primeStatusId = 'All';
        } else {
            $multipleStatus = $ip['multipleStatus'] ?? null;
            if($multipleStatus) {
                $multipleStatus = explode(', ', $multipleStatus);
                $multipleStatus = array_unique($multipleStatus);
            }
        }

        $WFStatus = trim($ip['WFStatus'] ?? '');
        $WFStepIDs = $ip['WFStepIDs'] ?? [];
        $contactId = trim($ip['contactId'] ?? 0);
        $openHouseDate = trim($ip['openHouseDate'] ?? '');
        $fileIDParams = [];

        if ($userGroup == 'Employee') {
            $assignedBranchArray = self::assignedBranchArray($PCID, $UID);

            if (count($assignedBranchArray)) {
                $assignedBranchId = implode(', ', array_unique($assignedBranchArray));
            }


            //$UID != $employeeId
            if (sizeof($employeeId) && !in_array($UID, $employeeId) && $userRole != 'Manager') {
                $allowToViewAllFiles = self::allowToViewAllFiles($UID);
                if ($allowToViewAllFiles == 0) {
                    $myFileIDsArray = self::myFileIDsArray($UID);
                }
            }
        }

        if ($userGroup == 'Sales' || trim($salesRep)) { // Select the PCs of the Sales person
            $PCArray = self::PCArray($salesRep);
            if (count($PCArray)) {
                $salesPCID = implode(', ', array_unique($PCArray));
            }
        }

        if ($userGroup == 'CFPB Auditor') {
            $assignedCFPBFileIDArray = self::assignedCFPBFileIDArray($UID);

            if (count($assignedCFPBFileIDArray)) {
                $assignedCFPBFileID = implode(', ', array_unique($assignedCFPBFileIDArray));
            }

        }

        if ($userGroup == 'Super' && $PCID == 0 && $LMRClientType) {
            if(!is_array($LMRClientType)) {
                $list = [];
                $temp = explode(',', $LMRClientType);
                foreach($temp as $t) {
                    $list[] = trim($t);
                }
                $LMRClientType = $list;
            }
            $LMRClientType = self::LMRClientType($LMRClientType);
        }

        if ($startDate) {
            $startDate = Dates::formatDateWithRE($startDate, 'MDY', 'Y-m-d');
        }
        if ($endDate) {
            $endDate = Dates::formatDateWithRE($endDate, 'MDY', 'Y-m-d');
        }
        if ($subStDate) {
            $subStDate = Dates::formatDateWithRE($subStDate, 'MDY', 'Y-m-d');
        }
        if ($subEndDate) {
            $subEndDate = Dates::formatDateWithRE($subEndDate, 'MDY', 'Y-m-d');
        }
        if ($modifiedStartDate) {
            $modifiedStartDate = Dates::formatDateWithRE($modifiedStartDate, 'MDY', 'Y-m-d');
        }
        if ($modifiedEndDate) {
            $modifiedEndDate = Dates::formatDateWithRE($modifiedEndDate, 'MDY', 'Y-m-d');
        }
        if ($closingStartDate) {
            $closingStartDate = Dates::formatDateWithRE($closingStartDate, 'MDY', 'Y-m-d');
        }
        if ($closingEndDate) {
            $closingEndDate = Dates::formatDateWithRE($closingEndDate, 'MDY', 'Y-m-d');
        }
        if ($maturityStartDate) {
            $maturityStartDate = Dates::formatDateWithRE($maturityStartDate, 'MDY', 'Y-m-d');
        }
        if ($maturityEndDate) {
            $maturityEndDate = Dates::formatDateWithRE($maturityEndDate, 'MDY', 'Y-m-d');
        }
        if ($targetClosingStartDate) {
            $targetClosingStartDate = Dates::formatDateWithRE($targetClosingStartDate, 'MDY', 'Y-m-d');
        }
        if ($targetClosingEndDate) {
            $targetClosingEndDate = Dates::formatDateWithRE($targetClosingEndDate, 'MDY', 'Y-m-d');
        }
        $appraisalStartDate = Dates::formatDateWithRE($appraisalStartDate, 'MDY', 'Y-m-d');
        $appraisalEndDate = Dates::formatDateWithRE($appraisalEndDate, 'MDY', 'Y-m-d');

        $receivedStartDate = Dates::formatDateWithRE($receivedStartDate, 'MDY', 'Y-m-d');
        $receivedEndDate = Dates::formatDateWithRE($receivedEndDate, 'MDY', 'Y-m-d');

        $disclosureSentDateStart = Dates::formatDateWithRE($disclosureSentDateStart, 'MDY', 'Y-m-d');
        $disclosureSentDateEnd = Dates::formatDateWithRE($disclosureSentDateEnd, 'MDY', 'Y-m-d');

        $billingDueDateQry = '';
        if ($billingDueDate) {
            $billingDueDateQry = ', ((SUM(functionToRemoveSpecialChar(bill.amount))-  (SUM(functionToRemoveSpecialChar(pay.totalPaid)) / COUNT(DISTINCT bill.feeCode) ))) AS balanceDue ';
        }

        $contactsSearch = $ip['contactsSearch'] ?? [];

        $massTableQuery1 = self::massTableQuery1(
            $userRole,
            $restFile,
            $UID,
            $employeeId,
            $LMRClientType,
            $sortOpt,
            $WFID,
            $LMRInternalClientType,
            $statusOpt,
            $notesType,
            $billingDueDate,
            $WFSID,
            $WFSNotCompletedId,
            $WFStepIDs,
            $multipleStatus,
            $multipleModuleCode,
            $searchField,
            $contactId,
            $openHouseDate,
            $servicingStatusCode,
            $referringParty,
            $maturityEndDate,
            $maturityStartDate,
            $appraisalStartDate,
            $appraisalEndDate,
            $receivedStartDate,
            $receivedEndDate,
            $disclosureSentDateStart,
            $disclosureSentDateEnd,
            $targetClosingEndDate,
            $targetClosingStartDate,
            $contactsSearch
        );
        $massTableSubQuery1 = self::massTableSubQuery1($sortOpt);
        $massTableSubQuery2 = self::massTableSubQuery2(
            $openHouseDate,
            $sortOpt,
            $closingStartDate,
            $closingEndDate,
            $massTableQuery1,
            $referringParty,
            $paymentBased
        );

        $massTableQueryClosing = self::massTableQueryClosing($closingStartDate, $closingEndDate, $paymentBased);
        $massTableQuery2 = self::massTableQuery2(
            $sortOpt,
            $userGroup,
            $salesRep,
            $salesPCID,
            $assignedBranchId,
            $assignedCFPBFileID,
            $searchField,
            $billingArray,
            $PCID
        );


        $filterQry = self::filterQry(
            $orderBy,
            $clientID,
            $userRole,
            $restFile,
            $activeStatus,
            $userGroup,
            $salesRep,
            $salesPCID,
            $PCID,
            $assignedBranchId,
            $assignedCFPBFileID,
            $BRID,
            $assignedBRID,
            $leadsBRID,
            $externalBroker,
            $AID,
            $leadStatusID,
            $branchId,
            $brokerNumber,
            $secondaryBrokerNumber,
            $staleDay,
            $propertyState,
            $priorityLevel,
            $startDate,
            $endDate,
            $modifiedStartDate,
            $modifiedEndDate,
            $closingStartDate,
            $closingEndDate,
            $maturityStartDate,
            $maturityEndDate,
            $lenderName,
            $leadSource,
            $LMRClientType,
            $LMRInternalClientType,
            $statusOpt,
            $searchTerm,
            $searchField,
            $billingArray,
            $employeeId,
            $UID,
            $myFileIDsArray,
            $notesType,
            $RESTProcId,
            $paymentStatus,
            $subStDate,
            $subEndDate,
            $clientEmail,
            $WFID,
            $WFSID,
            $WFSNotCompletedId,
            $WFStatus,
            $WFStepIDs,
            $billingDueDate,
            $multipleStatus,
            $multipleModuleCode,
            $loanAuditStatus,
            $massTableQuery1,
            $massTableQueryClosing,
            $massTableSubQuery1,
            $massTableSubQuery2,
            $massTableQuery2,
            $CFPBAuditStatus,
            $primeStatusId,
            $openHouseDate,
            $sortOpt,
            $genCSV,
            $pageNumber,
            $noOfRecords,
            $servicingStatusCode,
            $servicingStatusCodeArray,
            $appraisalStartDate,
            $appraisalEndDate,
            $receivedStartDate,
            $receivedEndDate,
            $disclosureSentDateStart,
            $disclosureSentDateEnd,
            $paymentBased,
            $targetClosingStartDate,
            $targetClosingEndDate,
            $contactsSearch
        );

        if (trim($PCID) == '') {
            $PCID = 0;
        }

        $rsArray = [];
        $sql = 'CALL SP_getFileIDsForPipeline_new (
                :PCID,
                :UID,
                :userGroup,
                :filterQry,
                :billingDueDateQry,
                :tablesToBeUpdated,
                :fAppendQry
              
        );   ';
        //check if the request is from Reports - Employee Performance
        if (isset($ip['employeeReport']) && ($ip['employeeReport'] == 'filesUniqueUpdated' || $ip['employeeReport'] == 'filesUniqueGENotes')) {
            $employeeReport = employeeReport::getUniqueUpdatedFilesQuery(
                $employeeId,
                $PCID,
                $ip['employeeReport'],
                $modifiedStartDate,
                $modifiedEndDate,
            );
            self::$massTableQuery = $employeeReport['massTableQuery'];
            if(sizeof($employeeReport['params'])){
                self::$params = array_merge(self::$params,$employeeReport['params']);
            }
            $filterQry = $employeeReport['filterQry'];
            //for file all counts / status counts
            self::$tempQry = self::$massTableQuery;
            self::$tempQry .= ' WHERE 1=1 ' . $filterQry;
        }

        $filterQry = Database2::getInstance()->safeQuery($filterQry, self::$params);
        self::$massTableQuery = Database2::getInstance()->safeQuery(self::$massTableQuery, self::$params);

        $fileIDParams['PCID'] = $PCID;
        $fileIDParams['UID'] = $UID;
        $fileIDParams['userGroup'] = $userGroup;
        $fileIDParams['filterQry'] = addslashes($filterQry);
        $fileIDParams['billingDueDateQry'] = $billingDueDateQry;
        $fileIDParams['tablesToBeUpdated'] = addslashes(self::$massTableQuery);
        $fileIDParams['fAppendQry'] = null;

        // Debug($sql, $fileIDParams);

        $rs = Database2::getInstance()->multiQueryData($sql, 'myOpt', $fileIDParams);

        if (isset($rs['fileIDs'])) {
            $rsArray = $rs['fileIDs'];
        }
        $allFileResponseIDs = '';
        if (array_key_exists('allFileResponseIDs', $rs)) {
            $allFileResponseIDs = $rs['allFileResponseIDs'][0]['lmrResponseGroup'];
        }

        foreach ($rsArray as $item) {
            $LMRIDArray[] = $item['LMRId'];
        }

        self::$tempQry = Database2::getInstance()->safeQuery(self::$tempQry, self::$params);
        $ip['qry'] = self::$tempQry;
        $ip['PCID'] = $PCID;

        $cntInfoArray = getFileDataCnt::getReport($ip, self::$params);

        $resultArray['cntInfo'] = $cntInfoArray;
        $resultArray['fileID'] = $LMRIDArray;
        $resultArray['qry'] = self::$tempQry;
        $resultArray['allFileResponseIDs'] = $allFileResponseIDs;

        return $resultArray;
    }
}
