<?php

namespace models\composite\oClient;

use models\Database2;
use models\lendingwise\tblClient;
use models\Request;
use models\standard\Strings;
use models\types\strongType;

/**
 *
 */
class updateClientRecord extends strongType
{
    /**
     * @param $infoArray
     * @return array|int
     */
    public static function getReport($infoArray)
    {
        $appComma = '';
        $internalInfoTags = '';


        if (array_key_exists('internalInfoTags', $infoArray)) {
            if (is_array($infoArray['internalInfoTags'])) {
                if (count($infoArray['internalInfoTags']) > 0) {
                    $internalInfoTags = implode(',', $infoArray['internalInfoTags']);
                }
            } elseif (strlen($infoArray['internalInfoTags']) > 0) {
                $internalInfoTags = $infoArray['internalInfoTags'];
            }
        }

        $qryUp = ' update tblClient set ';
        if (array_key_exists('borrowerName', $infoArray)) {
            $qryUp .= " clientFName = '" . $infoArray['borrowerName'] . "' ";
            $appComma = ', ';
        }
        if (array_key_exists('borrowerLName', $infoArray)) {
            $qryUp .= $appComma . " clientLName = '" . $infoArray['borrowerLName'] . "' ";
            $appComma = ', ';
        }
        if (array_key_exists('phoneNumber', $infoArray)) {
            $qryUp .= $appComma . " clientPhone = '" . $infoArray['phoneNumber'] . "' ";
            $appComma = ', ';
        }
        if (array_key_exists('cellNumber', $infoArray)) {
            $qryUp .= $appComma . " clientCell = '" . $infoArray['cellNumber'] . "' ";
            $appComma = ', ';
        }
        if (array_key_exists('serviceProvider', $infoArray)) {
            $qryUp .= " serviceProvider = '" . $infoArray['serviceProvider'] . "' ";
            $appComma = ', ';
        }
        if (array_key_exists('borrowerEmail', $infoArray)) {
            $qryUp .= $appComma . " clientEmail = '" . $infoArray['borrowerEmail'] . "' ";
            $appComma = ', ';
        }
        if (array_key_exists('mailingAddress', $infoArray)) {
            $qryUp .= $appComma . " clientAddress = '" . $infoArray['mailingAddress'] . "' ";
            $appComma = ', ';
        }
        if (array_key_exists('mailingCity', $infoArray)) {
            $qryUp .= $appComma . " clientCity = '" . $infoArray['mailingCity'] . "' ";
            $appComma = ', ';
        }
        if (array_key_exists('mailingState', $infoArray)) {
            $qryUp .= $appComma . " clientState = '" . $infoArray['mailingState'] . "' ";
            $appComma = ', ';
        }
        if (array_key_exists('mailingZip', $infoArray)) {
            $qryUp .= $appComma . " clientZip = '" . $infoArray['mailingZip'] . "' ";
            $appComma = ', ';
        }
        if (array_key_exists('internalInfoStatus', $infoArray)) {
            $qryUp .= $appComma . " internalInfoStatus = '" . $infoArray['internalInfoStatus'] . "' ";
            $appComma = ', ';
        }
        if (array_key_exists('internalInfoTags', $infoArray)) {
            $qryUp .= $appComma . " internalInfoTags = '" . trim($internalInfoTags, ',') . "' ";
            $appComma = ', ';
        }
        /*
* Task : https://trello.com/c/QpIAPbjQ
* Credit Line filed Value Truncation issue on February 13, 2019
*/
        if (array_key_exists('internalInfoCreditLine', $infoArray)) {
            $qryUp .= $appComma . " internalInfoCreditLine = '" . Strings::replaceCommaValues($infoArray['internalInfoCreditLine']) . "' ";
        }
        if (array_key_exists('clientMaxAcqusitionLTV', $infoArray)) {
            $qryUp .= $appComma . " clientMaxAcqusitionLTV = '" . $infoArray['clientMaxAcqusitionLTV'] . "' ";
        }
        if (array_key_exists('clientMaxRehabBudget', $infoArray)) {
            $qryUp .= $appComma . " clientMaxRehabBudget = '" . $infoArray['clientMaxRehabBudget'] . "' ";
        }
        if (array_key_exists('clientMaxAllowedARV', $infoArray)) {
            $qryUp .= $appComma . " clientMaxAllowedARV = '" . $infoArray['clientMaxAllowedARV'] . "' ";
        }
        if (array_key_exists('clientInternalInfoLoanTerms', $infoArray)) {
            $qryUp .= $appComma . " clientInternalInfoLoanTerms = '" . $infoArray['clientInternalInfoLoanTerms'] . "' ";
        }
        if (array_key_exists('clientInternalInfoAddTerms', $infoArray)) {
            $qryUp .= $appComma . " clientInternalInfoAddTerms = '" . $infoArray['clientInternalInfoAddTerms'] . "' ";
        }
        $qryUp .= " where CID = '" . $infoArray['clientId'] . "' ";
        if ($infoArray['clientId'] > 0) {
            return Database2::getInstance()->update($qryUp);
        }
        return null;
    }

    /**
     * @param $params
     * @return void
     */
    public static function borrowerProfileSyncData($params)
    {
        $tblClient = tblClient::Get(['CID' => $params['CID']]);
        if (!$tblClient) {
            return;
        }
        if (array_key_exists('borrowerLName', $params)) {
            $tblClient->clientLName = $params['borrowerLName'];
        }
        if (array_key_exists('borrowerDOB', $params)) {
            $tblClient->borrowerDOB = $params['borrowerDOB'];
        }
        if (array_key_exists('borrowerPOB', $params)) {
            $tblClient->borrowerPOB = $params['borrowerPOB'];
        }
        if (array_key_exists('ssn', $params)) {
            $tblClient->ssnNumber = $params['ssn'];
        }
        if (array_key_exists('phoneNumber', $params)) {
            $tblClient->clientPhone = $params['phoneNumber'];
        }
        if (array_key_exists('cellNumber', $params)) {
            $tblClient->clientCell = $params['cellNumber'];
        }
        if (array_key_exists('serviceProvider', $params)) {
            $tblClient->serviceProvider = $params['serviceProvider'];
        }
        if (array_key_exists('borrowerSecondaryEmail', $params)) {
            $tblClient->clientSecondaryEmail = $params['borrowerSecondaryEmail'];
        }
        if (array_key_exists('presentAddress', $params)) {
            $tblClient->clientAddress = $params['presentAddress'];
        }
        if (array_key_exists('presentCity', $params)) {
            $tblClient->clientCity = $params['presentCity'];
        }
        if (array_key_exists('presentState', $params)) {
            $tblClient->clientState = $params['presentState'];
        }
        if (array_key_exists('presentZip', $params)) {
            $tblClient->clientZip = $params['presentZip'];
        }
        if (array_key_exists('methodOfContact', $params)) {
            $tblClient->methodOfContact = $params['methodOfContact'];
        }
        if (array_key_exists('driverLicenseState', $params)) {
            $tblClient->driverLicenseState = $params['driverLicenseState'];
        }
        if (array_key_exists('driverLicenseNumber', $params)) {
            $tblClient->driverLicenseNumber = $params['driverLicenseNumber'];
        }

        //HMDA Info Sync
        if (array_key_exists('PublishBInfo', $params)) {
            $tblClient->publishBInfo = $params['PublishBInfo'];
        }
        if (array_key_exists('BEthnicity', $params)) {
            $tblClient->ethnicity = $params['BEthnicity'];
        }
        if (array_key_exists('BRace', $params)) {
            $tblClient->race = $params['BRace'];
        }
        if (array_key_exists('BGender', $params)) {
            $tblClient->gender = $params['BGender'];
        }
        if (array_key_exists('BVeteran', $params)) {
            $tblClient->veteran = $params['BVeteran'];
        }
        if (array_key_exists('bFiEthnicity', $params)) {
            $tblClient->FIEthnicity = $params['bFiEthnicity'];
        }
        if (array_key_exists('bFiEthnicitySub', $params)) {
            $tblClient->FIEthnicitySub = $params['bFiEthnicitySub'];
        }
        if (array_key_exists('bFiEthnicitySubOther', $params)) {
            $tblClient->FIEthnicitySubOther = $params['bFiEthnicitySubOther'];
        }
        if (array_key_exists('bFiSex', $params)) {
            $tblClient->FISex = $params['bFiSex'];
        }
        if (array_key_exists('bFiRace', $params)) {
            $tblClient->FIRace = $params['bFiRace'];
        }
        if (array_key_exists('bFiRaceSub', $params)) {
            $tblClient->FIRaceSub = $params['bFiRaceSub'];
        }
        if (array_key_exists('bFiRaceAsianOther', $params)) {
            $tblClient->FIRaceAsianOther = $params['bFiRaceAsianOther'];
        }
        if (array_key_exists('bFiRacePacificOther', $params)) {
            $tblClient->FIRacePacificOther = $params['bFiRacePacificOther'];
        }
        if (array_key_exists('bDemoInfo', $params)) {
            $tblClient->DemoInfo = $params['bDemoInfo'];
        }


        $tblClient->Save();
    }
}