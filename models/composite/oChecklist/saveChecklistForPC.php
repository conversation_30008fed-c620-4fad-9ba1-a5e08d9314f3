<?php

namespace models\composite\oChecklist;

use models\Database2;
use models\lendingwise\tblPCChecklistModules;
use models\lendingwise\tblPCChecklistNotification;
use models\lendingwise\tblPCChecklistNotificationFileStatus;
use models\standard\Arrays;
use models\standard\Dates;
use models\standard\Strings;
use models\types\strongType;

/**
 *
 */
class saveChecklistForPC extends strongType
{
    /**
     * @param $inArray
     * @return string
     */
    public static function getReport($inArray): string
    {
        $qryComma = '';
        $lastInsID = '';
        $crntDate = Dates::Timestamp();

        $PCID = trim($inArray['PCID']);
        $checklistId = trim($inArray['checklistId'] ?? 0);
        $checklistItem = trim($inArray['checklistItem'] ?? '');
        $dispOrder = $inArray['dispOrder'] ?? '';
        $checklistServiceTypeArray = $inArray['checklistServiceType'] ?? [];
        $checklistDesc = $inArray['checklistDesc'] ?? '';

        $transactionType = $inArray['transactionType'] ?? [];
        $propertyType = $inArray['propertyType'] ?? [];
        $borrowerOccupancy = $inArray['borrowerOccupancy'] ?? [];
        $propertyState = $inArray['propertyState'] ?? [];
        $entityState = $inArray['entityState'] ?? [];
        $entityType = $inArray['entityType'] ?? [];
        $borrowerCreditScoreRange = $inArray['borrowerCreditScoreRange'] ?? [];
        $coBorrowerRelatedReqdoc = $inArray['coBorrowerRelatedReqdoc'] ?? 0;
        $rehabRelatedReqdoc = $inArray['rehabRelatedReqdoc'] ?? 0;
        $noCrossCollRelatedReqdoc = $inArray['noCrossCollRelatedReqdoc'] ?? 0;
        $usCitizenRelatedReqdoc = $inArray['usCitizenRelatedReqdoc'] ?? [];
        $refDocName = $inArray['refDocName'] ?? '';
        $refDocUrl = $inArray['refDocUrl'] ?? '';
        $branchList = $inArray['branchList'] ?? [];
        $borrowerType = $inArray['borrowerType'] ?? [];
        $notifyUsersOnUpload = ($_POST['notifyUsersOnUpload'] ?? null);
        $notifyEmail = ($_POST['notifyEmail'] ?? null);
        $notifyWithAttachment = ($_POST['notifyWithAttachment'] ?? null);
        $categoryId = $inArray['categoryId'] ?? null;

        $tblPCChecklistNotification = tblPCChecklistNotification::get([
            'PCID' => $PCID,
            'docName' => $checklistItem,
        ]) ?? new tblPCChecklistNotification();
        $tblPCChecklistNotification->PCID = $PCID;
        $tblPCChecklistNotification->docName = $checklistItem;
        $tblPCChecklistNotification->notifyUsersOnUpload = $notifyUsersOnUpload;
        $tblPCChecklistNotification->notifyEmail = $notifyEmail;
        $tblPCChecklistNotification->notifyWithAttachment = $notifyWithAttachment;
        if (!$tblPCChecklistNotification->id) {
            $tblPCChecklistNotification->createdBy = Strings::GetSess('userNumber') ? intval(Strings::GetSess('userNumber')) : null;
            $tblPCChecklistNotification->createdGroup = Strings::GetSess('userGroup') ?? null;
            $tblPCChecklistNotification->createdDate = Dates::Timestamp() ?? null;
        } else {
            $tblPCChecklistNotification->updatedBy = Strings::GetSess('userNumber') ? intval(Strings::GetSess('userNumber')) : null;
            $tblPCChecklistNotification->updatedGroup = Strings::GetSess('userGroup') ?? null;
            $tblPCChecklistNotification->updatedDate = Dates::Timestamp() ?? null;
        }
        $tblPCChecklistNotification->Save();

        if ($tblPCChecklistNotification->id) {
            $tblPCChecklistNotificationFileStatus = tblPCChecklistNotificationFileStatus::GetAll(['checklistNotificationId' => $tblPCChecklistNotification->id]);
            foreach ($tblPCChecklistNotificationFileStatus as $eachNotificationPrimaryStatus) {
                $eachNotificationPrimaryStatus->Delete();
            }

            foreach ($_POST['notifyWhenFileStatusContains'] as $eachPrimaryStatusId) {
                $tblPCChecklistNotificationFileStatus = new tblPCChecklistNotificationFileStatus();
                $tblPCChecklistNotificationFileStatus->checklistNotificationId = $tblPCChecklistNotification->id;
                $tblPCChecklistNotificationFileStatus->primaryStatusId = $eachPrimaryStatusId;
                $tblPCChecklistNotificationFileStatus->save();
            }
        }


        if ($checklistId) {
            $PCMID_LIST = Arrays::explodeIntVals($checklistId);
            foreach ($PCMID_LIST as $eachPCMID) {
                $tblPCChecklistModules = tblPCChecklistModules::get(['PCMID' => $eachPCMID]);
                if ($tblPCChecklistModules) {
                    $tblPCChecklistModules->refDocName = $refDocName;
                    $tblPCChecklistModules->refDocUrl = $refDocUrl;
                    $tblPCChecklistModules->docName = $checklistItem;
                    $tblPCChecklistModules->categoryId = $categoryId;
                    $tblPCChecklistModules->displayOrder = intval($dispOrder);
                    $tblPCChecklistModules->description = $checklistDesc;
                    $tblPCChecklistModules->coBorrowerRelatedReqdoc = $coBorrowerRelatedReqdoc;
                    $tblPCChecklistModules->rehabRelatedReqdoc = $rehabRelatedReqdoc;
                    $tblPCChecklistModules->noCrossCollRelatedReqdoc = $noCrossCollRelatedReqdoc;
                    $tblPCChecklistModules->usCitizenRelatedReqdoc = $usCitizenRelatedReqdoc;
                    $tblPCChecklistModules->dStatus = 1;
                    $tblPCChecklistModules->Save();
                }
            }
            /*   $qry = '
               update
                   tblPCChecklistModules
               set
                   refDocName= :refDocName
                   ,                refDocUrl= :refDocUrl
                   ,                docName = :docName
                   ,                displayOrder = :displayOrder
                   ,                dStatus = 1
                   ,                description = :description
                   ,                coBorrowerRelatedReqdoc = :coBorrowerRelatedReqdoc
                   ,                rehabRelatedReqdoc = :rehabRelatedReqdoc
                   ,                noCrossCollRelatedReqdoc = :noCrossCollRelatedReqdoc
                   ,                usCitizenRelatedReqdoc = :usCitizenRelatedReqdoc
                   ,                notifyUsersOnUpload = :notifyUsersOnUpload
                   ,                notifyEmail = :notifyEmail
               where
                   PCMID in(' . $checklistId . ')
               ';
               $cnt = Database2::getInstance()->update($qry, [
                   'refDocName' => $refDocName,
                   'refDocUrl' => $refDocUrl,
                   'docName' => $checklistItem,
                   'displayOrder' => $dispOrder,
                   'description' => $checklistDesc,
                   'coBorrowerRelatedReqdoc' => $coBorrowerRelatedReqdoc,
                   'rehabRelatedReqdoc' => $rehabRelatedReqdoc,
                   'noCrossCollRelatedReqdoc' => $noCrossCollRelatedReqdoc,
                   'usCitizenRelatedReqdoc' => $usCitizenRelatedReqdoc,
                   'notifyUsersOnUpload' => $notifyUsersOnUpload,
                   'notifyEmail' => $notifyEmail,
               ]);*/
            if (sizeof($PCMID_LIST)) {
                $inArray['PCMID'] = $checklistId;
                saveChecklistForRequiredBy::getReport($inArray);

                $inArray['paramArray']['transactionType'] = $transactionType ?? [];
                $inArray['paramArray']['propertyType'] = $propertyType ?? [];
                $inArray['paramArray']['borrowerOccupancy'] = $borrowerOccupancy ?? [];
                $inArray['paramArray']['propertyState'] = $propertyState ?? [];
                $inArray['paramArray']['entityType'] = $entityType ?? [];
                $inArray['paramArray']['entityState'] = $entityState ?? [];
                $inArray['paramArray']['borrowerCreditScoreRange'] = $borrowerCreditScoreRange ?? [];
                $inArray['paramArray']['branchList'] = $branchList ?? [];
                $inArray['paramArray']['borrowerType'] = $borrowerType ?? [];

                saveChecklistAdditionalConditions::getReport($inArray);
            }
        } else {
            if (count($checklistServiceTypeArray) > 0) {

                for ($vv = 0; $vv < count($checklistServiceTypeArray); $vv++) {

                    $tempChecklistServiceType = $checklistServiceTypeArray[$vv]['serviceType'];

                    for ($j = 0; $j < count($tempChecklistServiceType); $j++) {
                        $checklistServiceType = trim($tempChecklistServiceType[$j]);
                        $serviceTypeIfExistPCMID = doesServiceTypeExists::getReport([
                            'PCID' => $PCID,
                            'docName' => $checklistItem,
                            'serviceType' => $checklistServiceType,
                            'moduleType' => $checklistServiceTypeArray[$vv]['moduleType'],
                            'categoryId' => $categoryId,
                        ]);

                        if ($serviceTypeIfExistPCMID > 0) {
                            $lastInsID .= $qryComma . $serviceTypeIfExistPCMID;
                        } else {
                            $insSql = '
INSERT INTO tblPCChecklistModules (
                                   PCID, docName,categoryId, serviceType, moduleType
, createdDate, displayOrder, description, coBorrowerRelatedReqdoc
, rehabRelatedReqdoc, noCrossCollRelatedReqdoc, usCitizenRelatedReqdoc
, refDocName, refDocUrl
) 
                            VALUES ( 
                                    :PCID
                                     , :docName
                                      , :categoryId
                                      , :serviceType
                                       , :moduleType
                                        , :createdDate
                                         , :displayOrder 
                                         , :description 
                                         , :coBorrowerRelatedReqdoc , :rehabRelatedReqdoc 
                                         , :noCrossCollRelatedReqdoc , :usCitizenRelatedReqdoc
                                          , :refDocName , :refDocUrl
                                           );';
                            $insSqlParams = [
                                'PCID' => $PCID,
                                'docName' => $checklistItem,
                                'categoryId' => $categoryId,
                                'serviceType' => $checklistServiceType,
                                'moduleType' => $checklistServiceTypeArray[$vv]['moduleType'],
                                'createdDate' => $crntDate,
                                'displayOrder' => $dispOrder,
                                'description' => $checklistDesc,
                                'coBorrowerRelatedReqdoc' => $coBorrowerRelatedReqdoc,
                                'rehabRelatedReqdoc' => $rehabRelatedReqdoc,
                                'noCrossCollRelatedReqdoc' => $noCrossCollRelatedReqdoc,
                                'usCitizenRelatedReqdoc' => $usCitizenRelatedReqdoc,
                                'refDocName' => $refDocName,
                                'refDocUrl' => $refDocUrl,
                            ];
                            $lastInsID .= $qryComma . Database2::getInstance()->insert($insSql, $insSqlParams);
                        }
                        $qryComma = ',';
                    }
                }
            }
            if ($lastInsID) {
                $inArray['PCMID'] = $lastInsID;
                saveChecklistForRequiredBy::getReport($inArray);

                $inArray['paramArray']['transactionType'] = $transactionType ?? [];
                $inArray['paramArray']['propertyType'] = $propertyType ?? [];
                $inArray['paramArray']['borrowerOccupancy'] = $borrowerOccupancy ?? [];
                $inArray['paramArray']['propertyState'] = $propertyState ?? [];
                $inArray['paramArray']['entityType'] = $entityType ?? [];
                $inArray['paramArray']['entityState'] = $entityState ?? [];
                $inArray['paramArray']['borrowerCreditScoreRange'] = $borrowerCreditScoreRange ?? [];
                $inArray['paramArray']['branchList'] = $branchList ?? [];
                $inArray['paramArray']['borrowerType'] = $borrowerType ?? [];

                $inArray['PCMID'] = $lastInsID;
                saveChecklistAdditionalConditions::getReport($inArray);
            }

        }
        return $lastInsID;
    }
}
