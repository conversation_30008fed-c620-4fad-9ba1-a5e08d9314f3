<?php

namespace models\composite\oCustomDocs;

use models\constants\gl\glContactTypeRoles;
use models\Controllers\backoffice\LMRequest;
use models\Database2;
use models\lendingwise\tblLoanSetting;
use models\lendingwise\tblLoanSettingTerms;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;
use models\types\strongType;

/**
 *
 */
class getMultipleFileInfo extends strongType
{
    /**
     * @param $ip
     * @return array|null
     */
    public static function getReport($ip): ?array
    {
        $HOA1Info = [];
        $HOA2Info = [];
        $brokerNumberArray = [];
        $loanOfficerNumberArray = [];
        $branchIDArray = [];
        $PCIDArray = [];
        $responseIDArray = [];
        $lenderName1 = '';
        $lenderName2 = '';
        $lenderArray = [];
        $EscrowInfo = [];
        $closerInfo = [];
        $transactionCoordinatorInfo = [];
        $borrowerMissingDoc = $branchMissingDoc = $brokerMissingDoc = $loanOfficerMissingDoc = $allMissingDocs = [];

        $LMRID = trim($ip['LMRID']);

        if (!$LMRID) {
            return null;
        }

        $LMRInfo = [];
        $qry = ' 
                SELECT 
                    tf.*
                    , tbc.clientPwd
                    , tbc.clientEmail
                    , tbc.internalInfoCreditLine
                    , tbc.clientMaxAcqusitionLTV
                    , tbc.clientMaxRehabBudget
                    , tbc.clientMaxAllowedARV
                FROM tblFile tf, tblClient tbc
                WHERE 
                    tf.clientId=tbc.CID 
                  and tf.LMRId in(' . $LMRID . ')
                ORDER BY tf.LMRId
            ';

        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $coBorMailingStateQry = 'SELECT stateName FROM tblStates WHERE stateCode = :stateCode ';
            $sqlParams = [
                'stateCode' => $row['coBorrowerMailingState'],
            ];
            $coBorMailingState = Database2::getInstance()->queryData($coBorMailingStateQry, $sqlParams);
            $row['coBorMailingStateLong'] = $coBorMailingState[0]['stateName'];
            $propertyStateQry = 'SELECT stateName FROM tblStates WHERE stateCode = :stateCode ';
            $sqlParams = [
                'stateCode' => $row['propertyState'],
            ];
            $propertyState = Database2::getInstance()->queryData($propertyStateQry, $sqlParams);
            $row['propertyStateLong'] = $propertyState[0]['stateName'];

            $LMRInfo[$row['LMRId']] = $row;
            $lenderName2 = trim($row['servicer2']);
            $lenderName1 = trim($row['servicer1']);

            $brokerNumberArray[] = $row['brokerNumber'];
            $loanOfficerNumberArray[] = $row['secondaryBrokerNumber'];
            $branchIDArray[] = $row['FBRID'];
            $PCIDArray[] = $row['FPCID'];
        }

        $brokerNumber = implode(',', array_filter(array_unique($brokerNumberArray)));
        $loanOfficerNumber = implode(',', array_filter(array_unique($loanOfficerNumberArray)));
        $branchID = implode(',', array_filter(array_unique($branchIDArray)));
        $PCID = implode(',', array_filter(array_unique($PCIDArray)));

        if (!$PCID) {
            return null;
        }

        $BrokerInfo = [];
        if ($brokerNumber) {
            $qry = ' 
            select userNumber
                 , firstName
                 , lastName
                 , email
                 , company
                 , addr
                 , city
                 , state
                 , zipCode
                 , phoneNumber
                 , cellNumber
                 , fax
                 , website
                 , NMLSLicense
                 , prefCommunication
                 , DRE
                 , logo
                  , avatar

            from tblAgent
            where 
                userNumber in(' . $brokerNumber . ') 
                ORDER BY userNumber
             ';

            $rs = Database2::getInstance()->queryData($qry);
            foreach ($rs as $row) {
                $BrokerInfo[$row['userNumber']] = $row;
            }
        }


        $LoanOfficerInfo = [];
        if ($loanOfficerNumber) {
            $qry = ' 
select 
    userNumber
     , firstName
     , lastName
     , email
     , company
     , addr
     , city
     , state
     , zipCode
     , phoneNumber
     , cellNumber
     , fax
     , website
     , NMLSLicense
     , DRE
     , prefCommunication
     , license
      , avatar
from tblAgent 
where userNumber in(' . $loanOfficerNumber . ') 
ORDER BY userNumber 
';

            if ($loanOfficerNumber > '') $rs = Database2::getInstance()->queryData($qry);
            foreach ($rs as $row) {
                $LoanOfficerInfo[$row['userNumber']] = $row;
            }
        }

        /** File 2 Info **/
        $file2Info = [];
        $qry = ' select f.*, bs.stateName as borrowerStateLong, cs.stateName as coBorrStateLong from  tblFile2 f left join tblStates bs on f.presentState = bs.stateCode left join tblStates cs on f.coBPresentState = cs.stateCode where LMRID in(' . $LMRID . ') ORDER BY LMRID ';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $file2Info[$row['LMRID']] = $row;
        }

        $fileLoanOriginationInfo = [];
        $qry = ' SELECT * FROM  tblFileLoanOrigination WHERE fileID IN(' . $LMRID . ') ';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $fileLoanOriginationInfo[$row['fileID']] = $row;
        }

        $RESTInfo = [];
        $qry = ' SELECT * FROM tblRestInfo WHERE LMRId in (' . $LMRID . ') ORDER BY LMRId ';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $RESTInfo[$row['LMRId']] = $row;
        }

        $fileHMLOListOfRepairsInfoArray = [];
        $qry = ' SELECT * FROM tblFileHMLOListOfRepairs WHERE fileID in (' . $LMRID . ') ORDER BY fileID ';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $fileHMLOListOfRepairsInfoArray[$row['fileID']] = $row;
        }

        $budgetAndDrawsInfo = [];
        $qry = ' SELECT * FROM tblBudgetAndDraws WHERE LMRId in (' . $LMRID . ') ORDER BY LMRId ';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $budgetAndDrawsInfo[$row['LMRId']][] = $row;
        }

        //pay down info
        $paydownInfo = [];
        $qry = ' select * from tblPrincipalPayDown where LMRID in (' . $LMRID . ')';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $paydownInfo[$row['LMRID']][] = $row;
        }

        /** Branch Info **/


        $BranchInfo = [];
        if ($branchID) {
            $qry = '
 select 
     executiveId
      , LMRExecutive
      , executiveEmail
      , company
      , tollFree
      , fax
      , cellNumber
      , directPhone
      , address
      , city
      , state
      , zipCode
      , website
      , logo
      , avatar
 from tblBranch where executiveId in(' . $branchID . ') ORDER by executiveId ';
            $rs = Database2::getInstance()->queryData($qry);
            foreach ($rs as $row) {
                $brabchStateNameQry = 'SELECT stateName FROM tblStates WHERE stateCode = :stateCode ';
                $sqlParams = [
                    'stateCode' => $row['state'],
                ];
                $brabchStateName = Database2::getInstance()->queryData($brabchStateNameQry, $sqlParams);
                $row['brStateLongName'] = $brabchStateName[0]['stateName'];
                $BranchInfo[$row['executiveId']] = $row;
            }
        }

        $PCInfo = [];
        if ($PCID) {
            $qry = ' select PCID, processingCompanyName, attorneyFName, attorneyMName, attorneyLName
      , attorneyEmail, attorneyAddress, attorneyCity, attorneyState, attorneyZipCode
      , attorneyTelephone, attorneyFascimile, processingCompanyWebsite, procCompLogo
      , attorneyCell, borrowerLoginURL, NMLSID, servicerName, servicerAddress, servicerPhone,
      servicerEmail, payoffPhoneNo, payOffRequestEmail, lenderPayableInfo, adminUserTitle, isPLO
        from tblProcessingCompany where PCID in(' . $PCID . ') ORDER BY PCID ';
            $rs = Database2::getInstance()->queryData($qry);
            foreach ($rs as $row) {
                $PCStateNameQry = 'SELECT stateName FROM tblStates WHERE stateCode = :stateCode ';
                $sqlParams = [
                    'stateCode' => $row['attorneyState'],
                ];
                $PCStateName = Database2::getInstance()->queryData($PCStateNameQry, $sqlParams);
                $row['PCStateName'] = $PCStateName[0]['stateName'];
                $PCInfo[$row['PCID']] = $row;
            }
        }

        $BorrowerExpInfo = [];
        $qry = ' select * from tblFileHMLOExperience where fileID in(' . $LMRID . ') ';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $BorrowerExpInfo[$row['fileID']] = $row;
        }

        $responseInfo = [];
        $qry = ' select * from tblFileResponse where LMRId in(' . $LMRID . ') ORDER BY LMRId ';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $responseInfo[$row['LMRId']] = $row;
            $responseIDArray[] = $row['LMRResponseId'];
        }

        $incomeInfo = [];
        $qry = ' select * from tblIncomeInfo where LMRId in(' . $LMRID . ') ORDER BY LMRId';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            if (trim($row['addGrossedUp1']) > 0) {
                $row['socialSecurity1'] = Strings::replaceCommaValues($row['socialSecurity1']) * 1.25;
            }
            if (trim($row['addGrossedUp2']) > 0) {
                $row['socialSecurity2'] = Strings::replaceCommaValues($row['socialSecurity2']) * 1.25;
            }

            $incomeInfo[$row['LMRId']] = $row;
        }


        $propertyInfo = [];
        $qry = ' select LMRId, titleName, titleSeller, titleEscrowNo, isHouseProperty, titleOrderNumber , titleReportDate , recordingNo , titleOrderedDate
                            from tblFilePropertyInfo where LMRId in(' . $LMRID . ') ORDER BY LMRId';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $propertyInfo[$row['LMRId']] = $row;
        }

        $clientTypeInfo = $ClientTypes = [];
        $tempLMRId = 0;
        $newInfoArray = [];
        $qry = ' select LMRID, ClientType from tblLMRClientType where LMRID in(' . $LMRID . ') ORDER BY LMRID';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            if ($row['LMRID'] != $tempLMRId) $newInfoArray = [];
            $newInfoArray[] = $row;
            $clientTypeInfo[$row['LMRID']] = $newInfoArray;
            $tempLMRId = $row['LMRID'];
            $ClientTypes[] = $row['ClientType'];
        }

        $QAInfo = [];

        $qry = ' select LMRId, attorneyFirmName, attorneyName, attorneyPhone, attorneyFax, attorneyEmail, attorneyAddress, attorneyCity, 
        attorneyState, attorneyZip, jurisDiction, attorneyNumber, summonDate, noticeReceivedDate, closingDate, desiredClosingDate, 
        hearingDate, PublishBInfo, BRace, BEthnicity, BGender, PublishCBInfo, CBRace, CBEthnicity, CBGender, HOAOrCOAFeeAmt,
        legalEntityIdentifier, universalLoanIdentifier, actionTaken, typeOfPurchaser, reasonForDenial, censusTract, BVeteran, 
        bFiEthnicity, bFiSex, bFiRace, bDemoInfo, CBVeteran, CBFiEthnicity, CBFiGender, CBFiRace, CBDDemoInfo from tblQAInfo where LMRId in(' . $LMRID . ')  ORDER BY LMRId';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $QAInfo[$row['LMRId']] = $row;
        }
        $listingPageArray = [];

        $qry = 'SELECT * FROM tblListingPageData WHERE LMRId = :LMRId ';
        $rs = Database2::getInstance()->queryData($qry, [
            'LMRId' => $LMRID,
        ]);
        foreach ($rs as $row) {
            $listingPageArray[$row['LMRId']] = $row;
        }
        $shortSaleInfo = [];
        $qry = ' select LMRId, realtor, buyer1RelToSeller, listingPrice, listingDate, realtorAddress, realtorPhoneNumber, sales1CellNo, sales1Fax, realtorEmail, titleCompany, contact, titleCompanyPhoneNumber, sales2Fax, titleCompanyEmail, buyerName1, coBuyerName1, firstBuyerPhone, firstBuyerEmail, offer1, sqft1, contractDate1, closingDate1, buyer1AgentName, buyer1AgencyName, buyer1Cell, buyer1Fax, buyer1Email, buyer1LOName, buyer1LOCompany, buyer1LOPhone, buyer1LOFax, buyer1LOCell, buyer1LOEmail, buyer1Deal,updateEmailStatus, offerAmount, currentLenderValue, valueExpires, nextStage, updateEmailComments, updateEmailBuyerName, contractDate, foreclosureDate, costBasis, assessedValue, appraisalNotes, appraiser1Value, rehabValue, dateObtained, appraiser2Value, rehabValue2, dateObtained2, AVM1Company, AVM1Value, AVM2Company, AVM2Value, AVM3Company, AVM3Value, BPO1Value, rehabValue3, twelveMonthRent, dateObtained3, BPO2Value, rehabValue4, twelveMonthRent1, dateObtained4, BPO3Value, rehabValue5, twelveMonthRent2, dateObtained5,zillowValue, costSpent from tblShortSale where LMRId in(' . $LMRID . ')  ORDER BY LMRId';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $shortSaleInfo[$row['LMRId']] = $row;
        }

        $LMRACHInfo = [];
        $qry = ' select LMRId, accountNo, accountName, bankAddr, acctHolderCity, acctHolderState, acctHolderZip, acctHolderAddr,bankName,depositDate,routingNo from tblACHInfo where LMRId in(' . $LMRID . ')  ORDER BY LMRId';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $LMRACHInfo[$row['LMRId']] = $row;
        }

        $LMRCCInfo = [];
        $qry = ' select LMRID, CCNumber, CCName, CCSecCode, CCAddress, CCCity, CCState, CCZip,CCType,CCExpiryMonth,CCExpiryYear from tblLMRCCInfo where LMRID in(' . $LMRID . ')  ORDER BY LMRId';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $LMRCCInfo[$row['LMRID']] = $row;
        }

        /** file Module Info  **/

        $fileModuleInfo = [];
        $newInfoArray = [];
        $fileSelectedModuleCode = [];

        $qry = ' select t1.*, t2.moduleName from tblFileModules t1, tblModules t2 where t1.moduleCode = t2.moduleCode AND t2.activeStatus = 1 AND t1.fileID in (' . $LMRID . ') group by fileID, t1.moduleCode order by fileID, t1.moduleCode';
        $rs = Database2::getInstance()->queryData($qry);
        $tempLMRId = '';
        foreach ($rs as $row) {
            $tempArray = [
                'fileID' => $row['fileID'],
                'moduleCode' => $row['moduleCode'],
                'moduleName' => $row['moduleName'],
            ];
            if ($row['fileID'] != $tempLMRId) {
                $newInfoArray = [];
            }
            $newInfoArray[] = $tempArray;
            $fileModuleInfo[$row['fileID']] = $newInfoArray;
            $tempLMRId = $row['fileID'];
            $fileSelectedModuleCode[] = $row['moduleCode'];
        }

        /** file Module Info **/

        $PCCheckList = [];
        $tempServiceType = '';
        $newArray = [];
        $qry = " SELECT
     moduleType, docName, PCMID, PCID, serviceType 
 FROM tblPCChecklistModules 
 WHERE
     moduleType IN ('" . implode("', '", array_unique($fileSelectedModuleCode)) . "') 
     AND PCID in(" . $PCID . ") 
     AND serviceType IN ('" . implode("', '", array_unique($ClientTypes)) . "')
      AND dStatus = '1' 
      ORDER BY PCID, moduleType, serviceType, displayOrder, docName ";
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            if ($row['moduleType'] != $tempServiceType) {
                $newArray = [];
            }
            $newArray[] = $row;
            $PCCheckList[$row['PCID']][$row['moduleType']] = $newArray;
            $tempServiceType = $row['moduleType'];
        }


        /** PC's File Statuses **/
        $PCStatusInfo = $PCSubStatusInfo = [];
        $qry = ' 
SELECT t1.PSID, t1.PCID, t1.primaryStatus, t1.activeStatus, t1.statusDesc, t2.displayOrder, t4.moduleName 
FROM tblPCPrimeStatus t1, tblPCPrimeStatusModules t2, tblPCModules t3, tblModules t4 
WHERE t1.PSID = t2.primeStatusId AND t2.moduleCode = t3.moduleCode
  AND t1.PCID = t3.PCID AND t4.moduleCode = t2.moduleCode 
  AND t1.PCID IN (' . $PCID . ") AND t2.moduleCode IN ('" . implode("', '", array_unique($fileSelectedModuleCode)) . "')
   AND t1.activeStatus = 1 AND t3.activeStatus = 1 AND t4.activeStatus = 1 AND t3.subscribedStatus = 1 
                ORDER BY t1.PCID, t2.displayOrder, t1.primaryStatus, t2.moduleCode ";
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $PCStatusInfo[] = $row;
        }

        $qry = "SELECT  t1.PFSID, t1.PCID, t1.substatus, t2.moduleCode, IFNULL (t3.category, 'Other' ) AS category, t4.moduleName FROM tblPCFileSubstatus t1 
                        JOIN tblPCFileSubstatusModules t2 ON t1.PFSID = t2.substatusId
                        LEFT JOIN tblPCFileSubstatusCategory t3 ON t1.categoryId = t3.PSCID 
                        JOIN tblPCModules t5 ON  t2.moduleCode = t5.moduleCode AND  t1.PCID = t5.PCID
                        JOIN tblModules t4 ON t4.moduleCode = t2.moduleCode
                        WHERE t1.PFSID = t2.substatusId 
                        AND t1.PCID in (" . $PCID . ") AND t1.activeStatus = 1 AND t4.activeStatus = 1 
                        AND t5.activeStatus = 1 AND t5.subscribedStatus = 1
                        AND t2.moduleCode IN ('" . implode("', '", array_unique($fileSelectedModuleCode)) . "') 
                        ORDER BY t2.moduleCode, t1.PCID, ISNULL(t3.category), t3.category, t1.dispOrder, t1.substatus
                       ";
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $PCSubStatusInfo[] = $row;
        }

        /** File Sub Status Info **/
        $fileSubstatusInfo = [];

        $qry = ' SELECT tp.substatus, tf.fileID, tf.substatusId, tf.dateChecked, tf.dateUnchecked, tf.UID, tf.URole, tf.notes
                            FROM tblPCFileSubstatus tp, tblFileSubstatus tf, tblPCFileSubstatusModules tm 
                            WHERE tf.substatusId = tp.PFSID AND tp.PFSID = tm.substatusId AND tf.substatusId = tm.substatusId 
                            AND fileID in (' . $LMRID . ") AND fStatus =1 AND activeStatus = 1
                            AND COALESCE(dateUnchecked, '') = '' AND tm.moduleCode IN ('" . implode("', '", array_unique($fileSelectedModuleCode)) . "') 
                            GROUP BY tf.fileID, tf.substatusId
                            ORDER BY tf.fileID, tp.dispOrder, tp.substatus;";
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $fileSubstatusInfo[] = $row;
        }


        /**
         *
         * Description    : Get the file Check list Items Info
         * Date        : May 01, 2017
         * Author        : Viji & Venkatesh
         **/
        $fileCheckList = [];
        $tempServiceType = '';
        $newArray = [];
        $qry = " SELECT t1.moduleType, t1.docName, t1.FMID, t1.serviceType, t2.FPCID as PCID 
                            FROM tblFileChecklistModules t1, tblFile t2
                            WHERE t1.fileID = t2.LMRId AND t2.activeStatus = 1 AND
                            t1.moduleType IN ('" . implode("', '", array_unique($fileSelectedModuleCode)) . "') 
                            AND t1.serviceType IN ('" . implode("', '", array_unique($ClientTypes)) . "') 
                            AND t1.dStatus = '1' AND t1.fileID in(" . $LMRID . ') 
                            ORDER BY t1.moduleType, t1.serviceType, t1.displayOrder, t1.docName 
                        ';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            if ($row['moduleType'] != $tempServiceType) {
                $newArray = [];
            }
            $newArray[] = $row;
            $fileCheckList[$row['PCID']][$row['moduleType']] = $newArray;
            $tempServiceType = $row['moduleType'];
        }

        $missingDocInfo = [];
        $qry = ' select docId, updatedOn, updatedBy, updatedUserType, fileID, CLType 
 from tblMissingDocuments where fileID in(' . $LMRID . ') order by fileID, CLType, docId ';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $missingDocInfo[$row['fileID']][$row['CLType']][$row['docId']] = $row;
        }
        $fileChecklistNotesInfo = [];

        $qry = ' SELECT fileID, CID, notes, SID, UID, UType, CLType, notesType 
 FROM tblChecklistFlatNotes where fileID in(' . $LMRID . ') order by fileID, CLType, CID ';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $row['notes'] = rawurldecode($row['notes']);
            if (!$fileChecklistNotesInfo[$row['fileID']][$row['CLType']][$row['CID']]) {
                $fileChecklistNotesInfo[$row['fileID']][$row['CLType']][$row['CID']] = [];
            }
            $fileChecklistNotesInfo[$row['fileID']][$row['CLType']][$row['CID']][] = $row;
        }


        $fileChecklistNotesInfoNew = [];
        $qry = ' (SELECT * FROM (SELECT tn.fileID,tn.CID,tn.notes,tn.SID,tn.UID,tn.UType,tn.CLType,tn.notesType, tm.docName,tn.recordDate AS recordDate  
        FROM tblChecklistFlatNotes tn  left join tblPCChecklistModules tm on tm.PCMID=tn.CID where 
        tn.fileID  in(' . $LMRID . ") AND CLType='PCL'  )  AS temG ORDER BY temG.recordDate ASC )
             UNION
             (  SELECT * FROM (SELECT tn.fileID,tn.CID,tn.notes,tn.SID,tn.UID,tn.UType,tn.CLType,tn.notesType,tm.docName,tn.recordDate AS recordDate 
  FROM tblChecklistFlatNotes tn 
 LEFT JOIN tblFileChecklistModules tm ON tm.FMID=tn.CID WHERE tn.fileID   in(" . $LMRID . ") AND CLType='FCL'   )   AS temG ORDER BY temG.recordDate ASC)";
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $row['notes'] = rawurldecode($row['notes']);
            $fileChecklistNotesInfoNew[$row['fileID']][$row['CLType']][$row['docName']][] = $row;
        }

        $proposalInfo = [];
        $qry = ' select LMRId, lien1ProposalBalance, lien2ProposalBalance
      , lien1ProposalTerms, lien2ProposalTerms, lien1ProposalRate, lien2ProposalRate
      , lien1ProposalPrincipalReductionAmt, lien2ProposalPrincipalReductionAmt
      , lien1ProposalLoanType, lien2ProposalLoanType, lien1EscrowShortage
      , lien1ProposalEscrowShortage, lien1FeesAdminCosts, lien1ProposalFeesAdminCosts
      , lien2FeesAdminCosts, lien2ProposalFeesAdminCosts, proposalGrossIncome
      , proposalNetMonthlyIncome, proposalNetMonthlyIncome, nonMtgProposalTotalHouseHoldExpenses
      , totalHousingTaxesInsurance, proposalTotalHousingTaxesInsurance, REOArrearsMonths
      , attorneyFees, securePropertyCost, maintenanceCost, lien1ProposalBalanceDue, lien2ProposalBalanceDue
      , realEstateCommissions, newHAMPT1DTI, lien1ProposalPayment, newPaymentInterest, principalForgiveness
      , requestedLoanWriteDown 
 from tblProposalInfo where LMRId in(' . $LMRID . ')  
 ORDER BY LMRId';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $proposalInfo[$row['LMRId']] = $row;
        }

        $listingHistoryInfo = [];
        $qry = ' select LMRId, mlsNo from tblListingHistoryInfo where LMRId in(' . $LMRID . ')  ORDER BY LMRId';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $listingHistoryInfo[$row['LMRId']] = $row;
        }

        $qry = " SELECT te.fileID, te.UID, ta.processorName, ta.processorLName, ta.role,
                ta.activeStatus, ta.email, ta.tollFree, ta.fax, ta.directPhone, ta.cellNumber
                FROM tblFileUsers te, tblAdminUsers ta
                WHERE te.UID = ta.AID AND URole = 'Employee'
                AND (te.dateRemoved IS NULL)
                AND te.fileID IN(" . $LMRID . ')
                ORDER BY te.fileID ';
        $assignedEmpInfo = Database2::getInstance()->fetchMultiResultSet([
            'qry' => $qry,
            'keyParam' => 'fileID',
            'useMaster' => 1,
        ]);

        /**
         * Description : Borrower Missing Document PDF and Document
         * Date        : May 03, 2017
         * Author      : Viji & Venkatesh, Suresh
         * Included    : LMRChecklistInfo and cklistNotRequiredInfo
         **/
        $LMRChecklistInfo = [];
        $qry = ' select * from tblMissingDocuments where fileID in (' . $LMRID . ') order by fileID, CLType, docId';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $LMRChecklistInfo[$row['docId']] = $row;
            $LMRChecklistInfo[$row['CLType']][$row['docId']] = $row;
        }


        $LMRChecklistInfoPCL = [];
        $qry = ' select * from tblMissingDocuments tm left join tblPCChecklistModules tpc on tpc.PCMID=tm.docId   where tm.fileID in (' . $LMRID . ") and 1 and tm.CLType ='PCL'  order by fileID, CLType, docId";
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $LMRChecklistInfoPCL[$row['CLType']][$row['docName']][$row['fileID']] = $row;
        }

        $LMRChecklistInfoFCL = [];
        $qry = ' 
select tm.*,tfc.docName,tfc.fileID as ignoreId  
 from tblMissingDocuments tm 
     left join tblFileChecklistModules tfc on tfc.FMID=tm.docId  
                                                 where tm.fileID in (' . $LMRID . ") 
                                                 and tm.CLType ='FCL'  
                                                 order by fileID, CLType, docId
                                                 ";
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $LMRChecklistInfoFCL[$row['CLType']][$row['docName']][$row['fileID']] = $row;
        }

        $cklistNotRequiredInfo = [];
        $qry = ' select * from tblLMRChecklistNotRequired where LMRID in (' . $LMRID . ') order by LMRID, CLType, CID';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            if ($row['CLType'] != '') {
                $cklistNotRequiredInfo[$row['CLType']][$row['CID']] = $row;
            } else {
                $cklistNotRequiredInfo['PCL'][$row['CID']] = $row;
            }
        }

        $cklistNotRequiredInfoName = [];
        $qry = ' select * from tblLMRChecklistNotRequired tr 
            inner join tblPCChecklistModules tcm on tcm.PCMID=tr.CID 
 where LMRID in (' . $LMRID . ") and (CLType ='PCL' || CLType ='') and dStatus = 1 and tcm.serviceType IN ('" . implode("', '", array_unique($ClientTypes)) . "')
 order by LMRID, CLType, CID";
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $cklistNotRequiredInfoName[$row['docName']] = $row;
        }

        $qry = ' select * from tblLMRChecklistNotRequired tr 
            inner join tblFileChecklistModules tcm on tcm.FMID=tr.CID
 where tcm.fileID in (' . $LMRID . ") and (CLType ='FCL') and dStatus = 1 ";

        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            if ($row['CLType'] != '') {
                $cklistNotRequiredInfoName[$row['docName']] = $row;
            }
        }

        /** Lender 1 Info **/
        $LenderInfo1 = [];
        if (trim($lenderName1) != '') {
            $qry = " select * from tblLenders where lenderType = 'ML' AND activeStatus = 1 and company  = '" . addslashes($lenderName1) . "'
             order by approvedStatus desc limit 1";
            $rs = Database2::getInstance()->queryData($qry);
            foreach ($rs as $row) {
                $LenderInfo1 = $row;
                $lenderArray[] = $row['LID'];
            }
        }

        /** Lender 2 Info **/
        $LenderInfo2 = [];
        if (trim($lenderName2) != '') {
            $qry = " select * from tblLenders where lenderType = 'ML' AND activeStatus = 1 and company  = '" . addslashes($lenderName2) . "'
             order by approvedStatus desc limit 1";
            $rs = Database2::getInstance()->queryData($qry);
            foreach ($rs as $row) {
                $LenderInfo2 = $row;
                $lenderArray[] = $row['LID'];
            }
        }

        $lenderNOE = [];
        if (count($lenderArray) > 0) {
            $LIDs = implode(', ', array_unique($lenderArray));
            $qry = " select te.LID, tn.NOE_phoneNumber, tn.NOE_fax, tn.NOE_city, tn.NOE_state, tn.NOE_zipCode, tn.NOE_address, tn.NOE_suite
                        from tblLenders te, tblLenderNOEInfo tn
                        where te.LID=tn.LID and te.lenderType = 'ML' AND te.activeStatus = 1
                        and te.LID in (" . $LIDs . ') order by te.LID
                      ';
            $rs = Database2::getInstance()->queryData($qry);
            foreach ($rs as $row) {
                $lenderNOE[$row['LID']] = $row;
            }
        }

        $contractorInfo = [];
        $qry = ' SELECT * FROM tblFileContacts t1 join tblContacts t2 WHERE t1.CID = t2.CID and t1.fileID IN(' . $LMRID . ') ORDER BY t1.fileID ';

        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            if (trim($row['cRole']) == 'Bank Attorney') {
                $QAInfo[$row['fileID']]['attorneyFirmName'] = $row['companyName'];
                $QAInfo[$row['fileID']]['attorneyName'] = $row['contactName'];
                $QAInfo[$row['fileID']]['attorneyEmail'] = $row['email'];
                $QAInfo[$row['fileID']]['attorneyPhone'] = $row['phone'];
                $QAInfo[$row['fileID']]['attorneyFax'] = $row['fax'];
                $QAInfo[$row['fileID']]['attorneyAddress'] = $row['address'];
                $QAInfo[$row['fileID']]['attorneyCity'] = $row['city'];
                $QAInfo[$row['fileID']]['attorneyState'] = $row['state'];
                $QAInfo[$row['fileID']]['attorneyZip'] = $row['zip'];
            }
            if (trim($row['cRole']) == glContactTypeRoles::CONTACT_TYPE_SELLER_ATTORNEY) {
                $shortSaleInfo[$row['fileID']]['sellerAttorneyFirstName'] = $row['contactName'];
                $shortSaleInfo[$row['fileID']]['sellerAttorneyLastName'] = $row['contactLName'];
                $shortSaleInfo[$row['fileID']]['sellerAttorneyFirmName'] = $row['companyName'];
                $shortSaleInfo[$row['fileID']]['sellerAttorneyPhone'] = $row['phone'];
                $shortSaleInfo[$row['fileID']]['sellerAttorneyEmail'] = $row['email'];
            }
            if (trim($row['cRole']) == 'Attorney') {
                $shortSaleInfo[$row['fileID']]['titleAttorneyName'] = $row['contactName'];
                $shortSaleInfo[$row['fileID']]['titleAttorneyFirmName'] = $row['companyName'];
                $shortSaleInfo[$row['fileID']]['titleAttorneyPhoneNumber'] = $row['phone'];
                $shortSaleInfo[$row['fileID']]['titleAttorneyEmail'] = $row['email'];
                $shortSaleInfo[$row['fileID']]['titleAttorneyAddress'] = $row['address'];
                $shortSaleInfo[$row['fileID']]['titleAttorneyCity'] = $row['city'];
                $shortSaleInfo[$row['fileID']]['titleAttorneyState'] = $row['state'];
                $shortSaleInfo[$row['fileID']]['titleAttorneyZip'] = $row['zip'];
                $shortSaleInfo[$row['fileID']]['titleAttorneyLName'] = $row['contactLName'];
                $shortSaleInfo[$row['fileID']]['titleAttorneyCellNumber'] = $row['cell'];
                $shortSaleInfo[$row['fileID']]['titleAttorneyFaxNumber'] = $row['fax'];
                $shortSaleInfo[$row['fileID']]['titleAttorneyTollFreeNo'] = $row['tollFree'];
                $shortSaleInfo[$row['fileID']]['titleAttorneyLicenseNo'] = $row['licenseNo'];
                $shortSaleInfo[$row['fileID']]['titleAttorneyWebsite'] = $row['website'];
                $shortSaleInfo[$row['fileID']]['titleAttorneyBarNo'] = $row['barNo'];
                $shortSaleInfo[$row['fileID']]['titleAttorneyStateOfFormation'] = $row['stateOfFormation'];
                $shortSaleInfo[$row['fileID']]['titleAttorneyEntityType'] = $row['entityType'];
                $shortSaleInfo[$row['fileID']]['titleAttorneyDescription'] = $row['description'];
                $atStQry = "select stateName from tblStates where stateCode='" . $row['state'] . "'";
                $atState = Database2::getInstance()->fetchRecords(['qry' => $atStQry]);
                $attorneyStateLong = $atState[0]['stateName'];
                $shortSaleInfo[$row['fileID']]['titleAttorneyStateLong'] = $attorneyStateLong;
            }
            if (trim($row['cRole']) == 'Realtor') {
                $shortSaleInfo[$row['fileID']]['realtor'] = $row['contactName'];
                $shortSaleInfo[$row['fileID']]['realtorAddress'] = $row['address'];
                $shortSaleInfo[$row['fileID']]['realtorPhoneNumber'] = $row['phone'];
                $shortSaleInfo[$row['fileID']]['sales1Fax'] = $row['fax'];
                $shortSaleInfo[$row['fileID']]['sales1CellNo'] = $row['cell'];
                $shortSaleInfo[$row['fileID']]['realtorEmail'] = $row['email'];
            }
            if (trim($row['cRole']) == 'Title Rep') {
                $shortSaleInfo[$row['fileID']]['titleCompany'] = $row['companyName'];
                $shortSaleInfo[$row['fileID']]['contact'] = $row['contactName'];
                $shortSaleInfo[$row['fileID']]['contactLastName'] = $row['contactLName'];
                $shortSaleInfo[$row['fileID']]['titleCompanyPhoneNumber'] = $row['phone'];
                $shortSaleInfo[$row['fileID']]['sales2Fax'] = $row['fax'];
                $shortSaleInfo[$row['fileID']]['titleCompanyEmail'] = $row['email'];
                $shortSaleInfo[$row['fileID']]['titleNotes'] = $row['description'];
                $shortSaleInfo[$row['fileID']]['titleAddress'] = $row['address'];
                $shortSaleInfo[$row['fileID']]['titleCity'] = $row['city'];
                $shortSaleInfo[$row['fileID']]['titleState'] = $row['state'];
                $shortSaleInfo[$row['fileID']]['titleZip'] = $row['zip'];
            }
            if (trim($row['cRole']) == 'B1 Agent') {
                $shortSaleInfo[$row['fileID']]['buyer1AgentName'] = $row['contactName'];
                $shortSaleInfo[$row['fileID']]['buyer1AgencyName'] = $row['companyName'];
                $shortSaleInfo[$row['fileID']]['buyer1Cell'] = $row['cell'];
                $shortSaleInfo[$row['fileID']]['buyer1Fax'] = $row['fax'];
                $shortSaleInfo[$row['fileID']]['buyer1Email'] = $row['email'];
            }

            if (trim($row['cRole']) == 'Appraiser 1') {
                $shortSaleInfo[$row['fileID']]['appraiser1Company'] = $row['companyName'];
                $shortSaleInfo[$row['fileID']]['appraiser1FirstName'] = $row['contactName'];
                $shortSaleInfo[$row['fileID']]['appraiser1LastName'] = $row['contactLName'];
                $shortSaleInfo[$row['fileID']]['appraiser1Phone'] = $row['phone'];
                $shortSaleInfo[$row['fileID']]['appraiser1Email'] = $row['email'];
            }
            if (trim($row['cRole']) == 'Appraiser 2') {
                $shortSaleInfo[$row['fileID']]['appraiser2Company'] = $row['companyName'];
                $shortSaleInfo[$row['fileID']]['appraiser2FirstName'] = $row['contactName'];
                $shortSaleInfo[$row['fileID']]['appraiser2LastName'] = $row['contactLName'];
                $shortSaleInfo[$row['fileID']]['appraiser2Phone'] = $row['phone'];
                $shortSaleInfo[$row['fileID']]['appraiser2Email'] = $row['email'];
            }

            if (trim($row['cRole']) == 'BPO 1') {
                $shortSaleInfo[$row['fileID']]['realtor1Company'] = $row['companyName'];
                $shortSaleInfo[$row['fileID']]['realtor1FirstName'] = $row['contactName'];
                $shortSaleInfo[$row['fileID']]['realtor1LastName'] = $row['contactLName'];
                $shortSaleInfo[$row['fileID']]['realtor1Phone'] = $row['phone'];
                $shortSaleInfo[$row['fileID']]['realtor1Email'] = $row['email'];
            }
            if (trim($row['cRole']) == 'BPO 2') {
                $shortSaleInfo[$row['fileID']]['realtor2Company'] = $row['companyName'];
                $shortSaleInfo[$row['fileID']]['realtor2FirstName'] = $row['contactName'];
                $shortSaleInfo[$row['fileID']]['realtor2LastName'] = $row['contactLName'];
                $shortSaleInfo[$row['fileID']]['realtor2Phone'] = $row['phone'];
                $shortSaleInfo[$row['fileID']]['realtor2Email'] = $row['email'];
            }
            if (trim($row['cRole']) == 'BPO 3') {
                $shortSaleInfo[$row['fileID']]['realtor3Company'] = $row['companyName'];
                $shortSaleInfo[$row['fileID']]['realtor3FirstName'] = $row['contactName'];
                $shortSaleInfo[$row['fileID']]['realtor3LastName'] = $row['contactLName'];
                $shortSaleInfo[$row['fileID']]['realtor3Phone'] = $row['phone'];
                $shortSaleInfo[$row['fileID']]['realtor3Email'] = $row['email'];
            }

            if (trim($row['cRole']) == 'Insurance Rep') {
                $shortSaleInfo[$row['fileID']]['insuranceRepFirstName'] = $row['contactName'];
                $shortSaleInfo[$row['fileID']]['insuranceRepLastName'] = $row['contactLName'];
                $shortSaleInfo[$row['fileID']]['nameOfCarrier'] = $row['companyName'];
                $shortSaleInfo[$row['fileID']]['insuranceRepOfficePhone'] = $row['phone'];
                $shortSaleInfo[$row['fileID']]['insuranceRepEmail'] = $row['email'];
                $shortSaleInfo[$row['fileID']]['insuranceRepCell'] = $row['cell'];
                $shortSaleInfo[$row['fileID']]['insuranceRepFax'] = $row['fax'];
                $shortSaleInfo[$row['fileID']]['insuranceRepTollFree'] = $row['tollFree'];
                $shortSaleInfo[$row['fileID']]['propertyInsuranceNotes'] = $row['description'];
                $shortSaleInfo[$row['fileID']]['insuranceRepWebsite'] = $row['website'];
                $shortSaleInfo[$row['fileID']]['insuranceRepAddress'] = $row['address'];
                $shortSaleInfo[$row['fileID']]['insuranceRepCity'] = $row['city'];
                $shortSaleInfo[$row['fileID']]['insuranceRepState'] = $row['state'];
                $shortSaleInfo[$row['fileID']]['insuranceRepZip'] = $row['zip'];
            }

            if (trim($row['cRole']) == 'Lender') {
                $shortSaleInfo[$row['fileID']]['serviceLenderName'] = $row['contactName'];
                $shortSaleInfo[$row['fileID']]['lenderLastName'] = $row['contactLName'];
                $shortSaleInfo[$row['fileID']]['lenderFirmName'] = $row['companyName'];
                $shortSaleInfo[$row['fileID']]['lenderPhone'] = $row['phone'];
                $shortSaleInfo[$row['fileID']]['lenderEmail'] = $row['email'];
                $shortSaleInfo[$row['fileID']]['lenderCell'] = $row['cell'];
                $shortSaleInfo[$row['fileID']]['lenderFax'] = $row['fax'];
                $shortSaleInfo[$row['fileID']]['serviceLenderAddress'] = $row['address'];
                $shortSaleInfo[$row['fileID']]['serviceLenderCity'] = $row['city'];
                $shortSaleInfo[$row['fileID']]['serviceLenderState'] = $row['state'];
                $shortSaleInfo[$row['fileID']]['serviceLenderZip'] = $row['zip'];
                $shortSaleInfo[$row['fileID']]['lendertollFree'] = $row['tollFree'];
                $shortSaleInfo[$row['fileID']]['serviceLenderEIN'] = $row['einNo'];
                $shortSaleInfo[$row['fileID']]['serviceLenderRepTitle'] = $row['repTitle'];
                $shortSaleInfo[$row['fileID']]['lenderstateOfFormation'] = $row['stateOfFormation'];
                $shortSaleInfo[$row['fileID']]['lenderentityType'] = $row['entityType'];
                //$stQry = "select stateName from tblStates where stateCode='" . $row['state'] . "'";
                //$stLong = Database2::getInstance()->fetchRecords(array('qry' => $stQry));
                $stQry = 'SELECT stateName FROM tblStates WHERE stateCode = :stateCode ';
                $sqlParams = [
                    'stateCode' => $row['state'],
                ];
                $stLong = Database2::getInstance()->queryData($stQry, $sqlParams);
                $StateLong = $stLong[0]['stateName'];

                //$forstQry = "select stateName from tblStates where stateCode='" . $row['stateOfFormation'] . "'";
                //$forStLong = Database2::getInstance()->fetchRecords(array('qry' => $forstQry));
                $forstQry = 'SELECT stateName FROM tblStates WHERE stateCode = :stateCode ';
                $sqlParams = [
                    'stateCode' => $row['stateOfFormation'],
                ];
                $forStLong = Database2::getInstance()->queryData($forstQry, $sqlParams);
                $forStateLong = $forStLong[0]['stateName'];
                $shortSaleInfo[$row['fileID']]['serviceLenderStateLong'] = $StateLong;
                $shortSaleInfo[$row['fileID']]['lenderstateOfFormationLong'] = $forStateLong;
            }


            if (trim($row['cRole']) == 'Servicer') {
                $shortSaleInfo[$row['fileID']]['servicerRepFirstName'] = $row['contactName'];
                $shortSaleInfo[$row['fileID']]['servicerRepLastName'] = $row['contactLName'];
                $shortSaleInfo[$row['fileID']]['servicerCompanyName'] = $row['companyName'];
                $shortSaleInfo[$row['fileID']]['servicerEmail'] = $row['email'];
                $shortSaleInfo[$row['fileID']]['servicerAddress'] = $row['address'];
                $shortSaleInfo[$row['fileID']]['servicerCity'] = $row['city'];
                $shortSaleInfo[$row['fileID']]['servicerState'] = $row['state'];
                $shortSaleInfo[$row['fileID']]['servicerZip'] = $row['zip'];
                $shortSaleInfo[$row['fileID']]['servicerPhone'] = $row['phone'];
                //$servicerStQry = "select stateName from tblStates where stateCode='" . $row['state'] . "'";
                //$servicerState = Database2::getInstance()->fetchRecords(array('qry' => $servicerStQry));
                $servicerStQry = 'SELECT stateName FROM tblStates WHERE stateCode = :stateCode ';
                $sqlParams = [
                    'stateCode' => $row['state'],
                ];
                $servicerState = Database2::getInstance()->queryData($servicerStQry, $sqlParams);
                $servicerStateLong = $servicerState[0]['stateName'];
                $shortSaleInfo[$row['fileID']]['servicerStateLong'] = $servicerStateLong;
            }

            if (trim($row['cRole']) == 'Servicer Rep') {
                $shortSaleInfo[$row['fileID']]['servicingServicerRepFirstName'] = $row['contactName'];
                $shortSaleInfo[$row['fileID']]['servicingServicerRepLastName'] = $row['contactLName'];
                $shortSaleInfo[$row['fileID']]['servicingServicerCompanyName'] = $row['companyName'];
                $shortSaleInfo[$row['fileID']]['servicingServicerEmail'] = $row['email'];
                $shortSaleInfo[$row['fileID']]['servicingServicerAddress'] = $row['address'];
                $shortSaleInfo[$row['fileID']]['servicingServicerCity'] = $row['city'];
                $shortSaleInfo[$row['fileID']]['servicingServicerState'] = $row['state'];
                $shortSaleInfo[$row['fileID']]['servicingServicerZip'] = $row['zip'];
                $shortSaleInfo[$row['fileID']]['servicingServicerPhone'] = $row['phone'];
            }


            if (trim($row['cRole']) == 'Trustee') {
                $shortSaleInfo[$row['fileID']]['trusteeName'] = $row['contactName'];
                $shortSaleInfo[$row['fileID']]['trusteeLName'] = $row['contactLName'];
                $shortSaleInfo[$row['fileID']]['trusteeFirmName'] = $row['companyName'];
                $shortSaleInfo[$row['fileID']]['trusteePhone'] = $row['phone'];
                $shortSaleInfo[$row['fileID']]['trusteeEmail'] = $row['email'];
                $shortSaleInfo[$row['fileID']]['trusteeCellNo'] = $row['cell'];
                $shortSaleInfo[$row['fileID']]['trusteeFax'] = $row['fax'];
                $shortSaleInfo[$row['fileID']]['trusteeAddress'] = $row['address'];
                $shortSaleInfo[$row['fileID']]['trusteeCity'] = $row['city'];
                $shortSaleInfo[$row['fileID']]['trusteeState'] = $row['state'];
                $shortSaleInfo[$row['fileID']]['trusteeZip'] = $row['zip'];
                $shortSaleInfo[$row['fileID']]['trusteetollFree'] = $row['tollFree'];
                $shortSaleInfo[$row['fileID']]['trusteeStateOfFormation'] = $row['stateOfFormation'];
                $shortSaleInfo[$row['fileID']]['trusteeEntityType'] = $row['entityType'];

                //$tStQry = "select stateName from tblStates where stateCode='" . $row['state'] . "'";
                //$tState = Database2::getInstance()->fetchRecords(array('qry' => $tStQry));
                $tStQry = 'SELECT stateName FROM tblStates WHERE stateCode = :stateCode ';
                $sqlParams = [
                    'stateCode' => $row['state'],
                ];
                $tState = Database2::getInstance()->queryData($tStQry, $sqlParams);
                $trusteeStateLong = $tState[0]['stateName'];
                $shortSaleInfo[$row['fileID']]['trusteeStateLong'] = $trusteeStateLong;

                //$tfStQry = "select stateName from tblStates where stateCode='" . $row['stateOfFormation'] . "'";
                //$tfState = Database2::getInstance()->fetchRecords(array('qry' => $tfStQry));
                $tfStQry = 'SELECT stateName FROM tblStates WHERE stateCode = :stateCode ';
                $sqlParams = [
                    'stateCode' => $row['stateOfFormation'],
                ];
                $tfState = Database2::getInstance()->queryData($tfStQry, $sqlParams);
                $trusteeForStateLong = $tfState[0]['stateName'];
                $shortSaleInfo[$row['fileID']]['trusteeStateOfFormationLong'] = $trusteeForStateLong;
            }

            if (trim($row['cRole']) == 'Escrow') {
                $EscrowInfo[$row['fileID']]['escrowRepFirstName'] = $row['contactName'];
                $EscrowInfo[$row['fileID']]['escrowRepLastName'] = $row['contactLName'];
                $EscrowInfo[$row['fileID']]['escrowCompany'] = $row['companyName'];
                $EscrowInfo[$row['fileID']]['escrowEmail'] = $row['email'];
                $EscrowInfo[$row['fileID']]['escrowPhone'] = $row['phone'];
                $EscrowInfo[$row['fileID']]['escrowTollFree'] = $row['tollFree'];
                $EscrowInfo[$row['fileID']]['escrowFax'] = $row['fax'];
                $EscrowInfo[$row['fileID']]['escrowCell'] = $row['cell'];
                $EscrowInfo[$row['fileID']]['escrowNum'] = $row['barNo'];
                $EscrowInfo[$row['fileID']]['escrowAddress'] = $row['address'];
                $EscrowInfo[$row['fileID']]['escrowCity'] = $row['city'];
                $EscrowInfo[$row['fileID']]['escrowState'] = $row['state'];
                $EscrowInfo[$row['fileID']]['escrowZip'] = $row['zip'];
                //$EscrowStQry = "select stateName from tblStates where stateCode='" . $row['state'] . "'";
                //$EscrowState = Database2::getInstance()->fetchRecords(array('qry' => $EscrowStQry));
                $EscrowStQry = 'SELECT stateName FROM tblStates WHERE stateCode = :stateCode ';
                $sqlParams = [
                    'stateCode' => $row['state'],
                ];
                $EscrowState = Database2::getInstance()->queryData($EscrowStQry, $sqlParams);
                $EscrowStateLong = $EscrowState[0]['stateName'];
                $EscrowInfo[$row['fileID']]['escrowStateLong'] = $EscrowStateLong;
            }

            if (trim($row['cRole']) == 'HOA1') {
                $HOA1Info[$row['fileID']]['companyName'] = $row['companyName'];
                $HOA1Info[$row['fileID']]['contactName'] = $row['contactName'];
            }
            if (trim($row['cRole']) == 'HOA2') {
                $HOA2Info[$row['fileID']]['companyName'] = $row['companyName'];
                $HOA2Info[$row['fileID']]['contactName'] = $row['contactName'];
            }

            // Contractor Info
            if(trim($row['cRole']) == 'General Contractor') {
                $contractorInfo[$row['fileID']]['companyName'] = $row['companyName'];
                $contractorInfo[$row['fileID']]['address'] = $row['address'];
                $contractorInfo[$row['fileID']]['city'] = $row['city'];
                $contractorInfo[$row['fileID']]['state'] = $row['state'];
                $contractorInfo[$row['fileID']]['zip'] = $row['zip'];
                $contractorInfo[$row['fileID']]['phone'] = $row['phone'];
            }
        }

        /**
         * Description : Borrower Missing Document PDF and Document
         * Date        : May 03, 2017
         * Author      : Viji & Venkatesh, Suresh
         **/
        $reqDocStatusForArray = ['Borrower', 'Branch', 'Broker', 'Loan Officer', 'All'];
        foreach ($reqDocStatusForArray as $recDocStatus) {
            $qry = "
                SELECT 
                    * 
                FROM 
                    (
                        (
                            SELECT 
                                t1.moduleType
                                , t1.serviceType
                                , '99' as displayOrder, t1.FMID AS CID
                                , t1.fileID
                                , '' AS PCID
                                ,  t1.docName
                                ,  t1.createdDate
                                , t3.displayDocName
                                , t3.uploadedBy
                                , t3.uploadedDate
                                , t3.uploadingUserType
                                , 'FCL' AS 'CLType'
                                , t3.fileID as 'docFileId'
                                , '' as PCMID
                                , '' as coBorrowerRelatedReqdoc
                                , '' as rehabRelatedReqdoc
                                , '' as noCrossCollRelatedReqdoc
                                , '' as usCitizenRelatedReqdoc
                            FROM 
                                tblFileChecklistModules t1 
                            JOIN tblFileChecklistRequiredBy t2 ON t1.FMID = t2.FMID
                                " . ($recDocStatus != 'All' ? "AND t2.requiredBy = '" . $recDocStatus . "'" : '') . '
                            LEFT JOIN (
                                SELECT 
                                    t3.PCMID
                                    , t3.fileID
                                    , t4.displayDocName
                                    , t4.uploadedBy
                                    , t4.uploadedDate
                                    , t4.uploadingUserType 
                                FROM
                                    tblPCChecklistUploadDocs t3 
                                JOIN 
                                    tblLMRFileDocs t4 ON t3.docID=t4.docID 
                                                            AND t3.fileID=t4.LMRID  
                                                            AND t4.activeStatus = 1  
                                                            AND t3.activeStatus = 1 
                                                            AND t4.LMRID IN (' . $LMRID . ") 
                                                            AND t3.CLType = 'FCL'
                            ) AS t3 ON t1.FMID=t3.PCMID AND t1.fileID=t3.fileID AND t2.FMID=t3.PCMID
                            WHERE 
                                t1.moduleType IN ('" . implode("', '", array_unique($fileSelectedModuleCode)) . "') 
                                 and t1.serviceType IN ('" . implode("', '", array_unique($ClientTypes)) . "')
                                AND t1.dStatus = 1 
                                AND t1.fileID IN(" . $LMRID . ")
                        )
                
                UNION
                
                (
                    SELECT 
                        t1.moduleType
                        , t1.serviceType
                        , t1.displayOrder
                        , t1.PCMID AS CID
                        , '' AS fileID
                        , t1.PCID
                        ,  t1.docName
                        ,  t1.createdDate
                        , t3.displayDocName
                        , t3.uploadedBy
                        , t3.uploadedDate
                        , t3.uploadingUserType
                        , 'PCL' AS 'CLType'
                        , t3.fileID as 'docFileId'
                        , t1.PCMID as PCMID
                        , t1.coBorrowerRelatedReqdoc
                        , t1.rehabRelatedReqdoc
                        , t1.noCrossCollRelatedReqdoc
                        , t1.usCitizenRelatedReqdoc
                    FROM 
                        tblPCChecklistModules t1 
                                        LEFT join tblPCChecklistBranch tPCB on t1.PCMID = tPCB.PCMID
                    JOIN tblPCChecklistRequiredBy t2 ON t1.PCMID = t2.PCMID 
                            " . ($recDocStatus != 'All' ? "AND t2.requiredBy = '" . $recDocStatus . "'" : '') . '
                    LEFT JOIN (
                        SELECT 
                            t3.PCMID
                            ,t5.docName
                            , t3.fileID
                            , t4.displayDocName
                            , t4.uploadedBy
                            , t4.uploadedDate
                            , t4.uploadingUserType
                        FROM
                            tblPCChecklistUploadDocs t3
                        LEFT JOIN 
                            tblPCChecklistModules t5 ON t5.PCMID = t3.PCMID
                        JOIN tblLMRFileDocs t4 ON t3.docID=t4.docID 
                                AND t3.fileID=t4.LMRID  
                                AND t4.activeStatus = 1
                                AND t3.activeStatus = 1 
                                AND t4.LMRID IN(' . $LMRID . ") 
                                AND t3.CLType = 'PCL'
                        ) AS t3 ON t1.docName=t3.docName
                        WHERE 
                            t1.moduleType IN ('" . implode("', '", array_unique($fileSelectedModuleCode)) . "') 
                            AND t1.dStatus = 1 
                                     AND (tPCB.branchId is null or tPCB.branchId = '" . $branchID . "' )
                            AND t1.serviceType IN ('" . implode("', '", array_unique($ClientTypes)) . "')  AND t1.PCID IN(" . $PCID . ')
                    )
                ) AS t 
                ORDER BY t.displayOrder asc, t.createdDate asc
                ';


            $rs = Database2::getInstance()->queryData($qry);
            foreach ($rs as $row) {
                if ($recDocStatus == 'Borrower') {
                    $borrowerMissingDoc[$row['moduleType']][] = $row;
                } elseif ($recDocStatus == 'Branch') {
                    $branchMissingDoc[$row['moduleType']][] = $row;
                } elseif ($recDocStatus == 'Broker') {
                    $brokerMissingDoc[$row['moduleType']][] = $row;
                } elseif ($recDocStatus == 'Loan Officer') {
                    $loanOfficerMissingDoc[$row['moduleType']][] = $row;
                } else {
                    $allMissingDocs[$row['moduleType']][] = $row;
                }
            }
        }
        $HMLOBusinessEntity = [];
        $qry = ' SELECT *  FROM tblFileHMLOBusinessEntity WHERE fileID IN(' . $LMRID . ')  ORDER BY fileID';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $HMLOBusinessEntity[$row['fileID']] = $row;
        }


        $HMLOPropInfoArray = [];

        $qry = ' SELECT fileID, typeOfHMLOLoanRequesting, exitStrategy, maxAmtToPutDown, lienPosition, HMLOEstateHeldIn, 
        paymentReserves, requiredConstruction, contingencyReserve, annualPremium, loanTerm, maturityDate, dateNoteSigned, 
        recordingDate, payOffDate, paymentDue, lenderLossPayableEndorsementInfo, servicingNumber, servicingSubStatus, 
        defaultInterestRate,  triggeredByDays, latePayemntAppliedOn, lateChargeAmt, minLateFeeAmt, lenderNotes, 
        loanSaleDate, masterLoanSaleDate,  expectForDueDiligence ,referringParty,typeOfSale,amountPastDueOrOwed,
        desiredFundingAmount,useOfFunds,haveCurrentLoanBal,balance,heldWith,haveInvoiceToFactor,amount,
        securityInstrument,propertyNeedRehab,isBlanketLoan,fundingDate,docType, proInsType,isThisGroundUpConstruction,
        spread, rateIndex, involvedPurchase, wholesaleFee
 FROM tblFileHMLOPropInfo WHERE fileID IN(' . $LMRID . ')  ORDER BY fileID';

        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $HMLOPropInfoArray[$row['fileID']] = $row;
        }

        $HMLONewLoanInfoArray = [];
        $qry = ' SELECT * FROM tblFileHMLONewLoanInfo WHERE fileID IN(' . $LMRID . ')  ORDER BY fileID';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $row['prePaymentSelectVal'] = trim($row['prePaymentSelectVal'], ',');
            $HMLONewLoanInfoArray[$row['fileID']] = $row;
        }

        $HMLOInfoArray = [];

        $qry = ' SELECT * FROM tblFileHMLO WHERE fileID IN(' . $LMRID . ')  ORDER BY fileID';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $HMLOInfoArray[$row['fileID']] = $row;
        }

        $LOExplanationArray = [];
        $qry = ' SELECT fileID, borComment FROM tblFileLOExplanation WHERE fileID IN(' . $LMRID . ')  ORDER BY fileID';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $LOExplanationArray[$row['fileID']] = $row;
        }

        $listingRealtorInfo2 = [];
        $qry = ' SELECT LMRId, totalJudgement, appraisalJobTypes, appraisal2JobTypes FROM tblShortSale2 WHERE LMRId IN(' . $LMRID . ')  ORDER BY LMRId';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $listingRealtorInfo2[$row['LMRId']] = $row;
        }


        $AssetsInfo = [];
        $qry = " select ta.*,tha.otherDesc from tblAssetsInfo ta left join tblFileHMLOAssetsInfo tha on ta.LMRID = tha.fileID where LMRID in ('" . $LMRID . "') ";
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $AssetsInfo[$row['LMRID']] = $row;
        }

        $fileHMLOBackGroundInfo = [];
        $qry = " SELECT * FROM  tblFileHMLOBackGround WHERE fileID IN('" . $LMRID . "') ";
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $fileHMLOBackGroundInfo[$row['fileID']] = $row;
        }

        $fileLOAssetsInfo = [];
        $qry = ' SELECT * FROM  tblFileLOAssetsInfo WHERE fileID IN(' . $LMRID . ') ';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $fileLOAssetsInfo = $row;
        }


        //new insurance Details;
        //(select group_concat(insuranceName) from tblInsurance_types where id =1 )
        $getInsuranceDtls = [];
        $qry = "select id as insDtlId,fileID,policyType,policyCarrier,policyName,policyNumber,policyExpDate,policyEffDate,policyAnnualPremium,policyNote,policyCoverage,insuranceDateReceived,insuranceDateOrdered
 from tblInsuranceDetails where activeStatus=1 and fileID='" . $LMRID . "'";
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $getInsuranceDtls[] = $row;
        }
        foreach ($getInsuranceDtls as $k => $v) {
            $policyIDs = explode(',', $v['policyType']);
            $getInsuranceDtls[$k]['ckecked'] = $policyIDs;

            if ($v['policyType'] != '') {
                $qry = 'select group_concat(insuranceName) as insuranceNames from tblInsurance_types where activeStatus=1 and id in (' . $v['policyType'] . ')';
                $rs = Database2::getInstance()->queryData($qry);
                foreach ($rs as $row) {
                    $getInsuranceDtls[$k]['insuranceTypes'] = $row['insuranceNames'];
                }
            } else {
                $getInsuranceDtls[$k]['insuranceTypes'] = '';
            }

            if (!Dates::IsEmpty($v['policyExpDate'])) {
                $getInsuranceDtls[$k]['policyExpDate'] = date('m/d/Y', strtotime($v['policyExpDate']));
            } else {
                $getInsuranceDtls[$k]['policyExpDate'] = '';
            }
            if (!Dates::IsEmpty($v['policyEffDate'])) {
                $getInsuranceDtls[$k]['policyEffDate'] = date('m/d/Y', strtotime($v['policyEffDate']));
            } else {
                $getInsuranceDtls[$k]['policyEffDate'] = '';
            }
            $getInsuranceDtls[$k]['insuranceDateReceived'] =  Dates::formatDateWithRE($v['insuranceDateReceived'], 'YMD', 'm/d/Y');
            $getInsuranceDtls[$k]['insuranceDateOrdered'] =  Dates::formatDateWithRE($v['insuranceDateOrdered'], 'YMD', 'm/d/Y');
        }

        $additionalGuarantorsInfo = [];
        $qry = ' SELECT g.*, s.stateName FROM  tblFileAdditionalGuarantors g left join tblStates s on g.guarantorState = s.stateCode WHERE g.LMRId IN(' . $LMRID . ') ';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $additionalGuarantorsInfo[] = $row;
        }

        $alternateNamesInfo = [];
        $qry = ' SELECT * FROM  tblBorrowerAlternateNames WHERE LMRId IN(' . $LMRID . ') ';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $alternateNamesInfo[] = $row;
        }

        /** Investor Info **/

        LMRequest::setLMRId($LMRID);

        $rs = LMRequest::File()->ActiveInvestors();

        foreach ($rs as $row) {
            $investorYield = Currency::formatDollarAmountWithDecimal($row->investorYield);
            $investedAmount = Currency::formatDollarAmountWithDecimal($row->investedAmount);
            $expectedMonthlyPayment = ((Strings::replaceCommaValues($investedAmount) * (Strings::replaceCommaValues($investorYield) / 100)) / 12);

            $row->expectedMonthlyPayment = $expectedMonthlyPayment;
            $investorInfo[] = $row->toArray();
        }


        $getSellerInfo = [];
        $qry = 'select * from tblPropSellerInfo where LMRId = :LMRId ';
        $rs = Database2::getInstance()->queryData($qry, [
            'LMRId' => $LMRID,
        ]);
        foreach ($rs as $row) {
            $getSellerInfo = $row;
        }

        $LMRInternalLoanprogramsFiles = [];

        $qry = "SELECT LMRID,GROUP_CONCAT(internalLoanProgram SEPARATOR ',' ) AS 'internalLoanPrograms'  FROM tblFileInternalLoanPrograms  WHERE activeStatus = :activeStatus AND LMRId IN ( " . $LMRID . ') GROUP BY LMRID';
        $sqlParams = [
            'activeStatus' => 1,
        ];
        //$LMRInternalLoanProgramArrays = Database2::getInstance()->fetchRecords(array('qry' => $qry));
        $LMRInternalLoanProgramArrays = Database2::getInstance()->queryData($qry, $sqlParams);
        foreach ($LMRInternalLoanProgramArrays as $lmrInterloanProgram) {
            $LMRInternalLoanprogramsFiles[$lmrInterloanProgram['LMRID']][] = $lmrInterloanProgram['internalLoanPrograms'];
        }

        $LMRInternalLoanprogramsNames = [];
        $qry = 'SELECT LMRID,internalLoanProgram ,serviceType  
                       FROM tblFileInternalLoanPrograms 
                       INNER JOIN tblLibServiceTypes ON tblLibServiceTypes.STCode = internalLoanProgram 
                       WHERE tblFileInternalLoanPrograms.activeStatus = :activeStatus AND LMRId IN ( ' . $LMRID . ');';
        $LMRInternalLoanProgramNamesArrays = Database2::getInstance()->queryData($qry, $sqlParams);
        foreach ($LMRInternalLoanProgramNamesArrays as $lmrInterloanProgramName) {
            $LMRInternalLoanprogramsNames[$lmrInterloanProgramName['LMRID']][] = $lmrInterloanProgramName;
        }


        $LMRadditionalLoanprogramsFiles = [];

        $qry = "SELECT LMRID,GROUP_CONCAT(additionalLoanProgram SEPARATOR ',' ) AS 'internalLoanPrograms'  FROM tblFileAdditionalLoanPrograms  WHERE activeStatus = :activeStatus and LMRId in ( " . $LMRID . ' ) GROUP BY LMRID';
        $LMRadditionalLoanProgramArrays = Database2::getInstance()->queryData($qry, $sqlParams);
        foreach ($LMRadditionalLoanProgramArrays as $lmrAdditionalloanProgram) {
            $LMRadditionalLoanprogramsFiles[$lmrAdditionalloanProgram['LMRID']][] = $lmrAdditionalloanProgram['internalLoanPrograms'];
        }

        $creditMemo = [];
        $selCMQry = "SELECT * FROM tblCreditMemo WHERE LMRId IN('" . $LMRID . "');";
        $rs = Database2::getInstance()->queryData($selCMQry);
        foreach ($rs as $row) {
            $creditMemo[$row['LMRId']][] = $row;
        }

        $sreoArrays = [];
        $sqlParams = [];
        $LMRIDArray = [];
        $LMRIDArray = explode(',', $LMRID);
        $srqry = 'SELECT * FROM  tblFileLOScheduleRealInfo WHERE fileID IN ( ' . Database2::GetPlaceholders(sizeof($LMRIDArray), ':fileID', true) . ' )';
        foreach ($LMRIDArray as $i => $paVal) {
            $sqlParams['fileID' . $i] = trim($paVal);
        }
        $sreoRes = Database2::getInstance()->queryData($srqry, $sqlParams);
        foreach ($sreoRes as $sreo) {
            $sreoArrays[$sreo['fileID']][] = $sreo;
        }

        $getCreditMemoDetails = [];
        $qry = 'SELECT * from tblCreditMemo WHERE LMRId = :fileID ';
        $sqlParams = [
            'fileID' => $LMRID,
        ];
        $row = Database2::getInstance()->queryData($qry, $sqlParams);
        $getCreditMemoDetails[$LMRID] = $row;

        $memberOfficerInfo = [];
        $memQry = ' SELECT * FROM  tblMemberOfficerInfo WHERE LMRID IN(' . Database2::GetPlaceholders(sizeof($LMRIDArray), ':LMRID', true) . ') ';
        foreach ($LMRIDArray as $i => $paVal) {
            $sqlParams['LMRID' . $i] = trim($paVal);
        }
        $rs = Database2::getInstance()->queryData($memQry, $sqlParams);
        foreach ($rs as $row) {
            $memberOfficerInfo[$row['LMRID']][] = $row;
        }

        $loanSettingsInfo = tblLoanSetting::getDataMultiple($LMRID);
        $loanSettingsTerms = tblLoanSettingTerms::getDataMultiple($LMRID);
        foreach ($loanSettingsTerms as $eachLoanTerm) {
            $loanSettingsInfo[$eachLoanTerm['LMRId']]['loanTerms'][] = new tblLoanSettingTerms($eachLoanTerm);
        }
        $noOfDaysInCurrentStatusInfo = [];
        $nsqry = " select tf.LMRID,
       IFNULL(t2.recordDate, tf.recordDate)                                                       as recordDate,
       IFNULL(t2.statusID, tFR.primeStatusID)                                                     as statusID,
       t2.recordDate,
       DATEDIFF(CURDATE(), DATE(t2.recordDate))                                                   as 'tblRecordFileDays',
       DATEDIFF(CURDATE(), DATE(tf.recordDate))                                                   as 'fileDays',
       IFNULL(DATEDIFF(CURDATE(), DATE(t2.recordDate)), DATEDIFF(CURDATE(), DATE(tf.recordDate))) as noOfDays,
       tf.LMRID                                                                                   AS myKey,
       'noOfDaysInCurrentStatus'                                                                  AS myOpt
from tblFile as tf
         left join (
         SELECT t1.LMRID,
                           t1.recordDate,
                           t1.statusID
                    FROM tblRecordFileStatus AS t1
                             JOIN (SELECT MAX(rID) AS latestRID
                                   FROM tblRecordFileStatus " .
            " WHERE LMRID IN (" . Database2::GetPlaceholders(sizeof($LMRIDArray), ":LMRID", true) . ")
GROUP BY LMRID) AS t2 ON t1.rID = latestRID
                    ORDER BY LMRID
                    ) AS t2
                   ON tf.LMRID = t2.LMRID
         left join tblFileResponse tFR ON tf.LMRId = tFR.LMRId 
         where tf.LMRID IN  (" . Database2::GetPlaceholders(sizeof($LMRIDArray), ':LMRID', true) . ")";
        foreach ($LMRIDArray as $i => $paVal) {
            $sqlParams['LMRID' . $i] = trim($paVal);
        }
        $rs = Database2::getInstance()->queryData($nsqry, $sqlParams);
        foreach ($rs as $row) {
            $noOfDaysInCurrentStatusInfo[$row['LMRID']] = $row;
        }

        $equipmentInfo = [];
        $eQry = ' SELECT * FROM  tblEquipmentInfo WHERE LMRId IN(' . Database2::GetPlaceholders(sizeof($LMRIDArray), ':LMRID', true) . ') ORDER BY EIID ';
        foreach ($LMRIDArray as $i => $paVal) {
            $sqlParams['LMRID' . $i] = trim($paVal);
        }
        $rs = Database2::getInstance()->queryData($eQry, $sqlParams);
        foreach ($rs as $row) {
            $equipmentInfo[$row['LMRId']][] = $row;
        }
        $HUDLenderToPayInfo = [];
        $eQry = ' SELECT * FROM  tblLMRHUDLenderToPay WHERE LMRId IN(' . Database2::GetPlaceholders(sizeof($LMRIDArray), ':LMRID', true) . ') 
        ORDER BY fieldID ';
        foreach ($LMRIDArray as $i => $paVal) {
            $sqlParams['LMRID' . $i] = trim($paVal);
        }
        $rs = Database2::getInstance()->queryData($eQry, $sqlParams);
        foreach ($rs as $row) {
            $HUDLenderToPayInfo[$row['LMRID']][$row['fieldID']] = $row;
        }

        $HUDReservesDepositInfo = [];
        $eQry = ' SELECT * FROM  tblLMRHUDReservesDeposit WHERE LMRId IN(' . Database2::GetPlaceholders(sizeof($LMRIDArray), ':LMRID', true) . ') 
        ORDER BY fieldID ';
        foreach ($LMRIDArray as $i => $paVal) {
            $sqlParams['LMRID' . $i] = trim($paVal);
        }
        $rs = Database2::getInstance()->queryData($eQry, $sqlParams);
        foreach ($rs as $row) {
            $HUDReservesDepositInfo[$row['LMRID']][$row['fieldID']] = $row;
        }

        $creditDecisionInfo = [];
        $eQry = ' SELECT * FROM  tblCreditDecision WHERE LMRId IN(' . Database2::GetPlaceholders(sizeof($LMRIDArray), ':LMRID', true) . ') 
        ORDER BY id limit 1';
        foreach ($LMRIDArray as $i => $paVal) {
            $sqlParams['LMRID' . $i] = trim($paVal);
        }
        $rs = Database2::getInstance()->queryData($eQry, $sqlParams);
        foreach ($rs as $row) {
            $creditDecisionInfo[$row['LMRId']][] = $row;
        }

        $HUDItemsPayableLoanInfo = [];
        $eQry = ' SELECT * FROM  tblLMRHUDItemsPayableLoan WHERE LMRId IN(' . Database2::GetPlaceholders(sizeof($LMRIDArray), ':LMRID', true) . ') 
        ORDER BY fieldID ';
        foreach ($LMRIDArray as $i => $paVal) {
            $sqlParams['LMRID' . $i] = trim($paVal);
        }
        $rs = Database2::getInstance()->queryData($eQry, $sqlParams);
        foreach ($rs as $row) {
            $HUDItemsPayableLoanInfo[$row['LMRID']][$row['fieldID']] = $row;
        }

        $extensionInfo = [];
        $eQry = ' SELECT * FROM  tblExtensions WHERE LMRId IN(' . Database2::GetPlaceholders(sizeof($LMRIDArray), ':LMRID', true) . ') 
        ORDER BY id';
        foreach ($LMRIDArray as $i => $paVal) {
            $sqlParams['LMRID' . $i] = trim($paVal);
        }
        $rs = Database2::getInstance()->queryData($eQry, $sqlParams);
        foreach ($rs as $row) {
            $extensionInfo[$row['LMRId']][] = $row;
        }
        $refinanceMortgageInfo = [];
        $eQry = ' SELECT * FROM  tblRefinanceMortgage WHERE LMRId IN(' . Database2::GetPlaceholders(sizeof($LMRIDArray), ':LMRID', true) . ') 
        ORDER BY id limit 1';
        foreach ($LMRIDArray as $i => $paVal) {
            $sqlParams['LMRID' . $i] = trim($paVal);
        }
        $rs = Database2::getInstance()->queryData($eQry, $sqlParams);
        foreach ($rs as $row) {
            $refinanceMortgageInfo[$row['LMRId']][] = $row;
        }


        $fileExtensionOptionsInfo = [];
        $eQry = ' SELECT * FROM  tblFileExtensionOptions WHERE LMRId IN(' . Database2::GetPlaceholders(sizeof($LMRIDArray), ':LMRID', true) . ') 
        ORDER BY id limit 1';
        foreach ($LMRIDArray as $i => $paVal) {
            $sqlParams['LMRID' . $i] = trim($paVal);
        }
        $rs = Database2::getInstance()->queryData($eQry, $sqlParams);
        foreach ($rs as $row) {
            $fileExtensionOptionsInfo[$row['LMRId']][] = $row;
        }


        $fileAuditInfo = [];
        $qry = 'select MIN(FUAID),fileID ,recordDate from tblFileUpdateAudit where fileID IN(' . Database2::GetPlaceholders(sizeof($LMRIDArray), ':LMRID', true) . ') 
        GROUP BY fileID';
        foreach ($LMRIDArray as $i => $paVal) {
            $sqlParams['LMRID' . $i] = trim($paVal);
        }
        $rs = Database2::getInstance()->queryData($qry, $sqlParams);
        foreach ($rs as $row) {
            $fileAuditInfo[$row['fileID']] = $row;
        }


        return [
            'LMRInfo' => $LMRInfo,
            'listingPageArray' => $listingPageArray,
            'BrokerInfo' => $BrokerInfo,
            'LoanOfficerInfo' => $LoanOfficerInfo,
            'BranchInfo' => $BranchInfo,
            'PCInfo' => $PCInfo,
            'incomeInfo' => $incomeInfo,
            'propertyInfo' => $propertyInfo,
            'clientTypeInfo' => $clientTypeInfo,
            'responseInfo' => $responseInfo,
            'QAInfo' => $QAInfo,
            'shortSaleInfo' => $shortSaleInfo,
            'LMRACHInfo' => $LMRACHInfo,
            'LMRCCInfo' => $LMRCCInfo,
            'PCCheckList' => $PCCheckList,
            'missingDocInfo' => $missingDocInfo,
            'proposalInfo' => $proposalInfo,
            'assignedEmpInfo' => $assignedEmpInfo,
            'listingHistoryInfo' => $listingHistoryInfo,
            'LenderInfo1' => $LenderInfo1,
            'LenderInfo2' => $LenderInfo2,
            'fileChecklistNotesInfo' => $fileChecklistNotesInfo,
            'fileChecklistNotesInfoNew' => $fileChecklistNotesInfoNew,
            'LMRChecklistInfoFCL' => $LMRChecklistInfoFCL,
            'LMRChecklistInfoPCL' => $LMRChecklistInfoPCL,

            'file2Info' => $file2Info,
            'lenderNOE' => $lenderNOE,
            'fileModuleInfo' => $fileModuleInfo,
            'RESTInfo' => $RESTInfo,
            'HMLOBusinessEntity' => $HMLOBusinessEntity,
            'HMLOPropInfoArray' => $HMLOPropInfoArray,
            'fileCheckList' => $fileCheckList,
            'borrowerMissingDoc' => $borrowerMissingDoc,
            'branchMissingDoc' => $branchMissingDoc,
            'brokerMissingDoc' => $brokerMissingDoc,
            'loanOfficerMissingDoc' => $loanOfficerMissingDoc,
            'allMissingDocs' => $allMissingDocs,
            'LMRChecklistInfo' => $LMRChecklistInfo,
            'cklistNotRequiredInfo' => $cklistNotRequiredInfo,
            'cklistNotRequiredInfoName' => $cklistNotRequiredInfoName,
            'HMLONewLoanInfo' => $HMLONewLoanInfoArray,
            'LOExplanation' => $LOExplanationArray,
            'HMLOInfo' => $HMLOInfoArray,
            'fileHMLOListOfRepairsInfo' => $fileHMLOListOfRepairsInfoArray,
            'listingRealtorInfo2' => $listingRealtorInfo2,
            'budgetAndDrawsInfo' => $budgetAndDrawsInfo,
            'assetInfo' => $AssetsInfo,
            'fileHMLOBackGroundInfo' => $fileHMLOBackGroundInfo,
            'EscrowInfo' => $EscrowInfo,
            'HOA1Info' => $HOA1Info,
            'HOA2Info' => $HOA2Info,
            'BorrowerExpInfo' => $BorrowerExpInfo,
            'PCStatusInfo' => $PCStatusInfo,
            'PCSubStatusInfo' => $PCSubStatusInfo, 'fileSubstatusInfo' => $fileSubstatusInfo,
            'fileLOAssetsInfo' => $fileLOAssetsInfo,
            'getInsuranceDtls' => $getInsuranceDtls,
            'paydownInfo' => $paydownInfo,
            'getSellerInfo' => $getSellerInfo,
            'additionalGuarantorsInfo' => $additionalGuarantorsInfo,
            'investorInfo' => $investorInfo,
            'LMRInternalLoanprogramsFiles' => $LMRInternalLoanprogramsFiles,
            'LMRInternalLoanProgramNames' => $LMRInternalLoanprogramsNames,
            'LMRadditionalLoanprogramsFiles' => $LMRadditionalLoanprogramsFiles,
            'creditMemo' => $creditMemo,
            'fileLoanOriginationInfo' => $fileLoanOriginationInfo,
            'alternateNamesInfo' => $alternateNamesInfo,
            'sreoInfo' => $sreoArrays,
            'getCreditMemoDetails' => $getCreditMemoDetails,
            'memberOfficerInfo' => $memberOfficerInfo,
            'loanSettingsInfo' => $loanSettingsInfo,
            'noOfDaysInCurrentStatusInfo' => $noOfDaysInCurrentStatusInfo,
            'equipmentInfo' => $equipmentInfo,
            'HUDLenderToPayInfo' => $HUDLenderToPayInfo,
            'HUDReservesDepositInfo' => $HUDReservesDepositInfo,
            'creditDecisionInfo' => $creditDecisionInfo,
            'HUDItemsPayableLoanInfo' => $HUDItemsPayableLoanInfo,
            'extensionInfo'=>$extensionInfo,
            'refinanceMortgageInfo'=>$refinanceMortgageInfo,
            'fileExtensionOptionsInfo'=>$fileExtensionOptionsInfo,
            'contractorInfo'=>$contractorInfo,
            'fileAuditInfo'=>$fileAuditInfo
        ];
    }
}
